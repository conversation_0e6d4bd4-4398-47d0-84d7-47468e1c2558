package org.gof.core.connsrv.netty;


import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.handler.codec.http.websocketx.BinaryWebSocketFrame;

public class ProtoDecoder extends ChannelInboundHandlerAdapter{

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) {
        if (msg instanceof BinaryWebSocketFrame) {
            BinaryWebSocketFrame frame = (BinaryWebSocketFrame)msg;
            try {
                ByteBuf in = frame.content();
				byte[] decoded = new byte[in.readableBytes()];
				in.readBytes(decoded);
				in.release();
				ctx.fireChannelRead(decoded);
                return;
            } catch (Exception e){
                frame.release();
            }
        }
        ctx.fireChannelRead(msg);
    }

}
