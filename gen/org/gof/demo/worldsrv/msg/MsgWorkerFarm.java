// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: msg.WorkerFarm.proto

// Protobuf Java Version: 3.25.3
package org.gof.demo.worldsrv.msg;

public final class MsgWorkerFarm {
  private MsgWorkerFarm() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface worker_farm_worker_setting_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.worker_farm_worker_setting_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_setting_info info = 1;</code>
     * @return Whether the info field is set.
     */
    boolean hasInfo();
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_setting_info info = 1;</code>
     * @return The info.
     */
    org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info getInfo();
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_setting_info info = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_infoOrBuilder getInfoOrBuilder();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.worker_farm_worker_setting_c2s}
   */
  public static final class worker_farm_worker_setting_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.worker_farm_worker_setting_c2s)
      worker_farm_worker_setting_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use worker_farm_worker_setting_c2s.newBuilder() to construct.
    private worker_farm_worker_setting_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private worker_farm_worker_setting_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new worker_farm_worker_setting_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgWorkerFarm.internal_static_org_gof_demo_worldsrv_msg_worker_farm_worker_setting_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgWorkerFarm.internal_static_org_gof_demo_worldsrv_msg_worker_farm_worker_setting_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_c2s.class, org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_c2s.Builder.class);
    }

    private int bitField0_;
    public static final int INFO_FIELD_NUMBER = 1;
    private org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info info_;
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_setting_info info = 1;</code>
     * @return Whether the info field is set.
     */
    @java.lang.Override
    public boolean hasInfo() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_setting_info info = 1;</code>
     * @return The info.
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info getInfo() {
      return info_ == null ? org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info.getDefaultInstance() : info_;
    }
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_setting_info info = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_infoOrBuilder getInfoOrBuilder() {
      return info_ == null ? org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info.getDefaultInstance() : info_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getInfo());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getInfo());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_c2s other = (org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_c2s) obj;

      if (hasInfo() != other.hasInfo()) return false;
      if (hasInfo()) {
        if (!getInfo()
            .equals(other.getInfo())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasInfo()) {
        hash = (37 * hash) + INFO_FIELD_NUMBER;
        hash = (53 * hash) + getInfo().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.worker_farm_worker_setting_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.worker_farm_worker_setting_c2s)
        org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgWorkerFarm.internal_static_org_gof_demo_worldsrv_msg_worker_farm_worker_setting_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgWorkerFarm.internal_static_org_gof_demo_worldsrv_msg_worker_farm_worker_setting_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_c2s.class, org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_c2s.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        info_ = null;
        if (infoBuilder_ != null) {
          infoBuilder_.dispose();
          infoBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgWorkerFarm.internal_static_org_gof_demo_worldsrv_msg_worker_farm_worker_setting_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_c2s build() {
        org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_c2s result = new org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_c2s result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.info_ = infoBuilder_ == null
              ? info_
              : infoBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_c2s.getDefaultInstance()) return this;
        if (other.hasInfo()) {
          mergeInfo(other.getInfo());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getInfoFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info info_;
      private com.google.protobuf.SingleFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info, org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info.Builder, org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_infoOrBuilder> infoBuilder_;
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_setting_info info = 1;</code>
       * @return Whether the info field is set.
       */
      public boolean hasInfo() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_setting_info info = 1;</code>
       * @return The info.
       */
      public org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info getInfo() {
        if (infoBuilder_ == null) {
          return info_ == null ? org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info.getDefaultInstance() : info_;
        } else {
          return infoBuilder_.getMessage();
        }
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_setting_info info = 1;</code>
       */
      public Builder setInfo(org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info value) {
        if (infoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          info_ = value;
        } else {
          infoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_setting_info info = 1;</code>
       */
      public Builder setInfo(
          org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info.Builder builderForValue) {
        if (infoBuilder_ == null) {
          info_ = builderForValue.build();
        } else {
          infoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_setting_info info = 1;</code>
       */
      public Builder mergeInfo(org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info value) {
        if (infoBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
            info_ != null &&
            info_ != org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info.getDefaultInstance()) {
            getInfoBuilder().mergeFrom(value);
          } else {
            info_ = value;
          }
        } else {
          infoBuilder_.mergeFrom(value);
        }
        if (info_ != null) {
          bitField0_ |= 0x00000001;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_setting_info info = 1;</code>
       */
      public Builder clearInfo() {
        bitField0_ = (bitField0_ & ~0x00000001);
        info_ = null;
        if (infoBuilder_ != null) {
          infoBuilder_.dispose();
          infoBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_setting_info info = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info.Builder getInfoBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getInfoFieldBuilder().getBuilder();
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_setting_info info = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_infoOrBuilder getInfoOrBuilder() {
        if (infoBuilder_ != null) {
          return infoBuilder_.getMessageOrBuilder();
        } else {
          return info_ == null ?
              org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info.getDefaultInstance() : info_;
        }
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_setting_info info = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info, org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info.Builder, org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_infoOrBuilder> 
          getInfoFieldBuilder() {
        if (infoBuilder_ == null) {
          infoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info, org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info.Builder, org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_infoOrBuilder>(
                  getInfo(),
                  getParentForChildren(),
                  isClean());
          info_ = null;
        }
        return infoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.worker_farm_worker_setting_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.worker_farm_worker_setting_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<worker_farm_worker_setting_c2s>
        PARSER = new com.google.protobuf.AbstractParser<worker_farm_worker_setting_c2s>() {
      @java.lang.Override
      public worker_farm_worker_setting_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<worker_farm_worker_setting_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<worker_farm_worker_setting_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface worker_farm_worker_setting_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.worker_farm_worker_setting_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 result = 1;</code>
     * @return The result.
     */
    int getResult();

    /**
     * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_setting_info info = 2;</code>
     * @return Whether the info field is set.
     */
    boolean hasInfo();
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_setting_info info = 2;</code>
     * @return The info.
     */
    org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info getInfo();
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_setting_info info = 2;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_infoOrBuilder getInfoOrBuilder();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.worker_farm_worker_setting_s2c}
   */
  public static final class worker_farm_worker_setting_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.worker_farm_worker_setting_s2c)
      worker_farm_worker_setting_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use worker_farm_worker_setting_s2c.newBuilder() to construct.
    private worker_farm_worker_setting_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private worker_farm_worker_setting_s2c() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new worker_farm_worker_setting_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgWorkerFarm.internal_static_org_gof_demo_worldsrv_msg_worker_farm_worker_setting_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgWorkerFarm.internal_static_org_gof_demo_worldsrv_msg_worker_farm_worker_setting_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_s2c.class, org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_s2c.Builder.class);
    }

    private int bitField0_;
    public static final int RESULT_FIELD_NUMBER = 1;
    private int result_ = 0;
    /**
     * <code>uint32 result = 1;</code>
     * @return The result.
     */
    @java.lang.Override
    public int getResult() {
      return result_;
    }

    public static final int INFO_FIELD_NUMBER = 2;
    private org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info info_;
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_setting_info info = 2;</code>
     * @return Whether the info field is set.
     */
    @java.lang.Override
    public boolean hasInfo() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_setting_info info = 2;</code>
     * @return The info.
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info getInfo() {
      return info_ == null ? org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info.getDefaultInstance() : info_;
    }
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_setting_info info = 2;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_infoOrBuilder getInfoOrBuilder() {
      return info_ == null ? org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info.getDefaultInstance() : info_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (result_ != 0) {
        output.writeUInt32(1, result_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(2, getInfo());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (result_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, result_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getInfo());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_s2c other = (org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_s2c) obj;

      if (getResult()
          != other.getResult()) return false;
      if (hasInfo() != other.hasInfo()) return false;
      if (hasInfo()) {
        if (!getInfo()
            .equals(other.getInfo())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + RESULT_FIELD_NUMBER;
      hash = (53 * hash) + getResult();
      if (hasInfo()) {
        hash = (37 * hash) + INFO_FIELD_NUMBER;
        hash = (53 * hash) + getInfo().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.worker_farm_worker_setting_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.worker_farm_worker_setting_s2c)
        org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgWorkerFarm.internal_static_org_gof_demo_worldsrv_msg_worker_farm_worker_setting_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgWorkerFarm.internal_static_org_gof_demo_worldsrv_msg_worker_farm_worker_setting_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_s2c.class, org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_s2c.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        result_ = 0;
        info_ = null;
        if (infoBuilder_ != null) {
          infoBuilder_.dispose();
          infoBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgWorkerFarm.internal_static_org_gof_demo_worldsrv_msg_worker_farm_worker_setting_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_s2c build() {
        org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_s2c result = new org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.result_ = result_;
        }
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.info_ = infoBuilder_ == null
              ? info_
              : infoBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_s2c.getDefaultInstance()) return this;
        if (other.getResult() != 0) {
          setResult(other.getResult());
        }
        if (other.hasInfo()) {
          mergeInfo(other.getInfo());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                result_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 18: {
                input.readMessage(
                    getInfoFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int result_ ;
      /**
       * <code>uint32 result = 1;</code>
       * @return The result.
       */
      @java.lang.Override
      public int getResult() {
        return result_;
      }
      /**
       * <code>uint32 result = 1;</code>
       * @param value The result to set.
       * @return This builder for chaining.
       */
      public Builder setResult(int value) {

        result_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 result = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearResult() {
        bitField0_ = (bitField0_ & ~0x00000001);
        result_ = 0;
        onChanged();
        return this;
      }

      private org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info info_;
      private com.google.protobuf.SingleFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info, org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info.Builder, org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_infoOrBuilder> infoBuilder_;
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_setting_info info = 2;</code>
       * @return Whether the info field is set.
       */
      public boolean hasInfo() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_setting_info info = 2;</code>
       * @return The info.
       */
      public org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info getInfo() {
        if (infoBuilder_ == null) {
          return info_ == null ? org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info.getDefaultInstance() : info_;
        } else {
          return infoBuilder_.getMessage();
        }
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_setting_info info = 2;</code>
       */
      public Builder setInfo(org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info value) {
        if (infoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          info_ = value;
        } else {
          infoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_setting_info info = 2;</code>
       */
      public Builder setInfo(
          org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info.Builder builderForValue) {
        if (infoBuilder_ == null) {
          info_ = builderForValue.build();
        } else {
          infoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_setting_info info = 2;</code>
       */
      public Builder mergeInfo(org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info value) {
        if (infoBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
            info_ != null &&
            info_ != org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info.getDefaultInstance()) {
            getInfoBuilder().mergeFrom(value);
          } else {
            info_ = value;
          }
        } else {
          infoBuilder_.mergeFrom(value);
        }
        if (info_ != null) {
          bitField0_ |= 0x00000002;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_setting_info info = 2;</code>
       */
      public Builder clearInfo() {
        bitField0_ = (bitField0_ & ~0x00000002);
        info_ = null;
        if (infoBuilder_ != null) {
          infoBuilder_.dispose();
          infoBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_setting_info info = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info.Builder getInfoBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getInfoFieldBuilder().getBuilder();
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_setting_info info = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_infoOrBuilder getInfoOrBuilder() {
        if (infoBuilder_ != null) {
          return infoBuilder_.getMessageOrBuilder();
        } else {
          return info_ == null ?
              org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info.getDefaultInstance() : info_;
        }
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_setting_info info = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info, org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info.Builder, org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_infoOrBuilder> 
          getInfoFieldBuilder() {
        if (infoBuilder_ == null) {
          infoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info, org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info.Builder, org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_infoOrBuilder>(
                  getInfo(),
                  getParentForChildren(),
                  isClean());
          info_ = null;
        }
        return infoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.worker_farm_worker_setting_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.worker_farm_worker_setting_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<worker_farm_worker_setting_s2c>
        PARSER = new com.google.protobuf.AbstractParser<worker_farm_worker_setting_s2c>() {
      @java.lang.Override
      public worker_farm_worker_setting_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<worker_farm_worker_setting_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<worker_farm_worker_setting_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface worker_farm_worker_setting_info_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.worker_farm_worker_setting_info_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 type = 1;</code>
     * @return The type.
     */
    int getType();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.worker_farm_worker_setting_info_c2s}
   */
  public static final class worker_farm_worker_setting_info_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.worker_farm_worker_setting_info_c2s)
      worker_farm_worker_setting_info_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use worker_farm_worker_setting_info_c2s.newBuilder() to construct.
    private worker_farm_worker_setting_info_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private worker_farm_worker_setting_info_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new worker_farm_worker_setting_info_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgWorkerFarm.internal_static_org_gof_demo_worldsrv_msg_worker_farm_worker_setting_info_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgWorkerFarm.internal_static_org_gof_demo_worldsrv_msg_worker_farm_worker_setting_info_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_c2s.class, org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_c2s.Builder.class);
    }

    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_ = 0;
    /**
     * <code>uint32 type = 1;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (type_ != 0) {
        output.writeUInt32(1, type_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, type_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_c2s other = (org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_c2s) obj;

      if (getType()
          != other.getType()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.worker_farm_worker_setting_info_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.worker_farm_worker_setting_info_c2s)
        org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgWorkerFarm.internal_static_org_gof_demo_worldsrv_msg_worker_farm_worker_setting_info_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgWorkerFarm.internal_static_org_gof_demo_worldsrv_msg_worker_farm_worker_setting_info_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_c2s.class, org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        type_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgWorkerFarm.internal_static_org_gof_demo_worldsrv_msg_worker_farm_worker_setting_info_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_c2s build() {
        org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_c2s result = new org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.type_ = type_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_c2s.getDefaultInstance()) return this;
        if (other.getType() != 0) {
          setType(other.getType());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                type_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int type_ ;
      /**
       * <code>uint32 type = 1;</code>
       * @return The type.
       */
      @java.lang.Override
      public int getType() {
        return type_;
      }
      /**
       * <code>uint32 type = 1;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(int value) {

        type_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 type = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        type_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.worker_farm_worker_setting_info_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.worker_farm_worker_setting_info_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<worker_farm_worker_setting_info_c2s>
        PARSER = new com.google.protobuf.AbstractParser<worker_farm_worker_setting_info_c2s>() {
      @java.lang.Override
      public worker_farm_worker_setting_info_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<worker_farm_worker_setting_info_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<worker_farm_worker_setting_info_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface worker_farm_worker_setting_info_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.worker_farm_worker_setting_info_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_setting_info setting_list = 1;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info> 
        getSettingListList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_setting_info setting_list = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info getSettingList(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_setting_info setting_list = 1;</code>
     */
    int getSettingListCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_setting_info setting_list = 1;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_infoOrBuilder> 
        getSettingListOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_setting_info setting_list = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_infoOrBuilder getSettingListOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.worker_farm_worker_setting_info_s2c}
   */
  public static final class worker_farm_worker_setting_info_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.worker_farm_worker_setting_info_s2c)
      worker_farm_worker_setting_info_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use worker_farm_worker_setting_info_s2c.newBuilder() to construct.
    private worker_farm_worker_setting_info_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private worker_farm_worker_setting_info_s2c() {
      settingList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new worker_farm_worker_setting_info_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgWorkerFarm.internal_static_org_gof_demo_worldsrv_msg_worker_farm_worker_setting_info_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgWorkerFarm.internal_static_org_gof_demo_worldsrv_msg_worker_farm_worker_setting_info_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_s2c.class, org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_s2c.Builder.class);
    }

    public static final int SETTING_LIST_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info> settingList_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_setting_info setting_list = 1;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info> getSettingListList() {
      return settingList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_setting_info setting_list = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_infoOrBuilder> 
        getSettingListOrBuilderList() {
      return settingList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_setting_info setting_list = 1;</code>
     */
    @java.lang.Override
    public int getSettingListCount() {
      return settingList_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_setting_info setting_list = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info getSettingList(int index) {
      return settingList_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_setting_info setting_list = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_infoOrBuilder getSettingListOrBuilder(
        int index) {
      return settingList_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < settingList_.size(); i++) {
        output.writeMessage(1, settingList_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < settingList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, settingList_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_s2c other = (org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_s2c) obj;

      if (!getSettingListList()
          .equals(other.getSettingListList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getSettingListCount() > 0) {
        hash = (37 * hash) + SETTING_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getSettingListList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.worker_farm_worker_setting_info_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.worker_farm_worker_setting_info_s2c)
        org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgWorkerFarm.internal_static_org_gof_demo_worldsrv_msg_worker_farm_worker_setting_info_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgWorkerFarm.internal_static_org_gof_demo_worldsrv_msg_worker_farm_worker_setting_info_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_s2c.class, org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        if (settingListBuilder_ == null) {
          settingList_ = java.util.Collections.emptyList();
        } else {
          settingList_ = null;
          settingListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgWorkerFarm.internal_static_org_gof_demo_worldsrv_msg_worker_farm_worker_setting_info_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_s2c build() {
        org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_s2c result = new org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_s2c(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_s2c result) {
        if (settingListBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            settingList_ = java.util.Collections.unmodifiableList(settingList_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.settingList_ = settingList_;
        } else {
          result.settingList_ = settingListBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_s2c result) {
        int from_bitField0_ = bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_s2c.getDefaultInstance()) return this;
        if (settingListBuilder_ == null) {
          if (!other.settingList_.isEmpty()) {
            if (settingList_.isEmpty()) {
              settingList_ = other.settingList_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureSettingListIsMutable();
              settingList_.addAll(other.settingList_);
            }
            onChanged();
          }
        } else {
          if (!other.settingList_.isEmpty()) {
            if (settingListBuilder_.isEmpty()) {
              settingListBuilder_.dispose();
              settingListBuilder_ = null;
              settingList_ = other.settingList_;
              bitField0_ = (bitField0_ & ~0x00000001);
              settingListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getSettingListFieldBuilder() : null;
            } else {
              settingListBuilder_.addAllMessages(other.settingList_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info.parser(),
                        extensionRegistry);
                if (settingListBuilder_ == null) {
                  ensureSettingListIsMutable();
                  settingList_.add(m);
                } else {
                  settingListBuilder_.addMessage(m);
                }
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info> settingList_ =
        java.util.Collections.emptyList();
      private void ensureSettingListIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          settingList_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info>(settingList_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info, org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info.Builder, org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_infoOrBuilder> settingListBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_setting_info setting_list = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info> getSettingListList() {
        if (settingListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(settingList_);
        } else {
          return settingListBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_setting_info setting_list = 1;</code>
       */
      public int getSettingListCount() {
        if (settingListBuilder_ == null) {
          return settingList_.size();
        } else {
          return settingListBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_setting_info setting_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info getSettingList(int index) {
        if (settingListBuilder_ == null) {
          return settingList_.get(index);
        } else {
          return settingListBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_setting_info setting_list = 1;</code>
       */
      public Builder setSettingList(
          int index, org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info value) {
        if (settingListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSettingListIsMutable();
          settingList_.set(index, value);
          onChanged();
        } else {
          settingListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_setting_info setting_list = 1;</code>
       */
      public Builder setSettingList(
          int index, org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info.Builder builderForValue) {
        if (settingListBuilder_ == null) {
          ensureSettingListIsMutable();
          settingList_.set(index, builderForValue.build());
          onChanged();
        } else {
          settingListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_setting_info setting_list = 1;</code>
       */
      public Builder addSettingList(org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info value) {
        if (settingListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSettingListIsMutable();
          settingList_.add(value);
          onChanged();
        } else {
          settingListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_setting_info setting_list = 1;</code>
       */
      public Builder addSettingList(
          int index, org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info value) {
        if (settingListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSettingListIsMutable();
          settingList_.add(index, value);
          onChanged();
        } else {
          settingListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_setting_info setting_list = 1;</code>
       */
      public Builder addSettingList(
          org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info.Builder builderForValue) {
        if (settingListBuilder_ == null) {
          ensureSettingListIsMutable();
          settingList_.add(builderForValue.build());
          onChanged();
        } else {
          settingListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_setting_info setting_list = 1;</code>
       */
      public Builder addSettingList(
          int index, org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info.Builder builderForValue) {
        if (settingListBuilder_ == null) {
          ensureSettingListIsMutable();
          settingList_.add(index, builderForValue.build());
          onChanged();
        } else {
          settingListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_setting_info setting_list = 1;</code>
       */
      public Builder addAllSettingList(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info> values) {
        if (settingListBuilder_ == null) {
          ensureSettingListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, settingList_);
          onChanged();
        } else {
          settingListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_setting_info setting_list = 1;</code>
       */
      public Builder clearSettingList() {
        if (settingListBuilder_ == null) {
          settingList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          settingListBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_setting_info setting_list = 1;</code>
       */
      public Builder removeSettingList(int index) {
        if (settingListBuilder_ == null) {
          ensureSettingListIsMutable();
          settingList_.remove(index);
          onChanged();
        } else {
          settingListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_setting_info setting_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info.Builder getSettingListBuilder(
          int index) {
        return getSettingListFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_setting_info setting_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_infoOrBuilder getSettingListOrBuilder(
          int index) {
        if (settingListBuilder_ == null) {
          return settingList_.get(index);  } else {
          return settingListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_setting_info setting_list = 1;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_infoOrBuilder> 
           getSettingListOrBuilderList() {
        if (settingListBuilder_ != null) {
          return settingListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(settingList_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_setting_info setting_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info.Builder addSettingListBuilder() {
        return getSettingListFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_setting_info setting_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info.Builder addSettingListBuilder(
          int index) {
        return getSettingListFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_setting_info setting_list = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info.Builder> 
           getSettingListBuilderList() {
        return getSettingListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info, org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info.Builder, org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_infoOrBuilder> 
          getSettingListFieldBuilder() {
        if (settingListBuilder_ == null) {
          settingListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info, org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info.Builder, org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_infoOrBuilder>(
                  settingList_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          settingList_ = null;
        }
        return settingListBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.worker_farm_worker_setting_info_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.worker_farm_worker_setting_info_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<worker_farm_worker_setting_info_s2c>
        PARSER = new com.google.protobuf.AbstractParser<worker_farm_worker_setting_info_s2c>() {
      @java.lang.Override
      public worker_farm_worker_setting_info_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<worker_farm_worker_setting_info_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<worker_farm_worker_setting_info_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_worker_setting_info_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface worker_farm_add_worker_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.worker_farm_add_worker_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_setting_info setting = 1;</code>
     * @return Whether the setting field is set.
     */
    boolean hasSetting();
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_setting_info setting = 1;</code>
     * @return The setting.
     */
    org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info getSetting();
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_setting_info setting = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_infoOrBuilder getSettingOrBuilder();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.worker_farm_add_worker_c2s}
   */
  public static final class worker_farm_add_worker_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.worker_farm_add_worker_c2s)
      worker_farm_add_worker_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use worker_farm_add_worker_c2s.newBuilder() to construct.
    private worker_farm_add_worker_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private worker_farm_add_worker_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new worker_farm_add_worker_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgWorkerFarm.internal_static_org_gof_demo_worldsrv_msg_worker_farm_add_worker_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgWorkerFarm.internal_static_org_gof_demo_worldsrv_msg_worker_farm_add_worker_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_c2s.class, org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_c2s.Builder.class);
    }

    private int bitField0_;
    public static final int SETTING_FIELD_NUMBER = 1;
    private org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info setting_;
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_setting_info setting = 1;</code>
     * @return Whether the setting field is set.
     */
    @java.lang.Override
    public boolean hasSetting() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_setting_info setting = 1;</code>
     * @return The setting.
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info getSetting() {
      return setting_ == null ? org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info.getDefaultInstance() : setting_;
    }
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_setting_info setting = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_infoOrBuilder getSettingOrBuilder() {
      return setting_ == null ? org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info.getDefaultInstance() : setting_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getSetting());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getSetting());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_c2s other = (org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_c2s) obj;

      if (hasSetting() != other.hasSetting()) return false;
      if (hasSetting()) {
        if (!getSetting()
            .equals(other.getSetting())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasSetting()) {
        hash = (37 * hash) + SETTING_FIELD_NUMBER;
        hash = (53 * hash) + getSetting().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.worker_farm_add_worker_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.worker_farm_add_worker_c2s)
        org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgWorkerFarm.internal_static_org_gof_demo_worldsrv_msg_worker_farm_add_worker_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgWorkerFarm.internal_static_org_gof_demo_worldsrv_msg_worker_farm_add_worker_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_c2s.class, org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_c2s.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getSettingFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        setting_ = null;
        if (settingBuilder_ != null) {
          settingBuilder_.dispose();
          settingBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgWorkerFarm.internal_static_org_gof_demo_worldsrv_msg_worker_farm_add_worker_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_c2s build() {
        org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_c2s result = new org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_c2s result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.setting_ = settingBuilder_ == null
              ? setting_
              : settingBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_c2s.getDefaultInstance()) return this;
        if (other.hasSetting()) {
          mergeSetting(other.getSetting());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getSettingFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info setting_;
      private com.google.protobuf.SingleFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info, org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info.Builder, org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_infoOrBuilder> settingBuilder_;
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_setting_info setting = 1;</code>
       * @return Whether the setting field is set.
       */
      public boolean hasSetting() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_setting_info setting = 1;</code>
       * @return The setting.
       */
      public org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info getSetting() {
        if (settingBuilder_ == null) {
          return setting_ == null ? org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info.getDefaultInstance() : setting_;
        } else {
          return settingBuilder_.getMessage();
        }
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_setting_info setting = 1;</code>
       */
      public Builder setSetting(org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info value) {
        if (settingBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          setting_ = value;
        } else {
          settingBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_setting_info setting = 1;</code>
       */
      public Builder setSetting(
          org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info.Builder builderForValue) {
        if (settingBuilder_ == null) {
          setting_ = builderForValue.build();
        } else {
          settingBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_setting_info setting = 1;</code>
       */
      public Builder mergeSetting(org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info value) {
        if (settingBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
            setting_ != null &&
            setting_ != org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info.getDefaultInstance()) {
            getSettingBuilder().mergeFrom(value);
          } else {
            setting_ = value;
          }
        } else {
          settingBuilder_.mergeFrom(value);
        }
        if (setting_ != null) {
          bitField0_ |= 0x00000001;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_setting_info setting = 1;</code>
       */
      public Builder clearSetting() {
        bitField0_ = (bitField0_ & ~0x00000001);
        setting_ = null;
        if (settingBuilder_ != null) {
          settingBuilder_.dispose();
          settingBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_setting_info setting = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info.Builder getSettingBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getSettingFieldBuilder().getBuilder();
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_setting_info setting = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_infoOrBuilder getSettingOrBuilder() {
        if (settingBuilder_ != null) {
          return settingBuilder_.getMessageOrBuilder();
        } else {
          return setting_ == null ?
              org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info.getDefaultInstance() : setting_;
        }
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_setting_info setting = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info, org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info.Builder, org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_infoOrBuilder> 
          getSettingFieldBuilder() {
        if (settingBuilder_ == null) {
          settingBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info, org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_info.Builder, org.gof.demo.worldsrv.msg.Define.p_worker_farm_setting_infoOrBuilder>(
                  getSetting(),
                  getParentForChildren(),
                  isClean());
          setting_ = null;
        }
        return settingBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.worker_farm_add_worker_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.worker_farm_add_worker_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<worker_farm_add_worker_c2s>
        PARSER = new com.google.protobuf.AbstractParser<worker_farm_add_worker_c2s>() {
      @java.lang.Override
      public worker_farm_add_worker_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<worker_farm_add_worker_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<worker_farm_add_worker_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface worker_farm_add_worker_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.worker_farm_add_worker_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 result = 1;</code>
     * @return The result.
     */
    int getResult();

    /**
     * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_info team_info = 2;</code>
     * @return Whether the teamInfo field is set.
     */
    boolean hasTeamInfo();
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_info team_info = 2;</code>
     * @return The teamInfo.
     */
    org.gof.demo.worldsrv.msg.Define.p_worker_farm_info getTeamInfo();
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_info team_info = 2;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_worker_farm_infoOrBuilder getTeamInfoOrBuilder();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.worker_farm_add_worker_s2c}
   */
  public static final class worker_farm_add_worker_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.worker_farm_add_worker_s2c)
      worker_farm_add_worker_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use worker_farm_add_worker_s2c.newBuilder() to construct.
    private worker_farm_add_worker_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private worker_farm_add_worker_s2c() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new worker_farm_add_worker_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgWorkerFarm.internal_static_org_gof_demo_worldsrv_msg_worker_farm_add_worker_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgWorkerFarm.internal_static_org_gof_demo_worldsrv_msg_worker_farm_add_worker_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_s2c.class, org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_s2c.Builder.class);
    }

    private int bitField0_;
    public static final int RESULT_FIELD_NUMBER = 1;
    private int result_ = 0;
    /**
     * <code>uint32 result = 1;</code>
     * @return The result.
     */
    @java.lang.Override
    public int getResult() {
      return result_;
    }

    public static final int TEAM_INFO_FIELD_NUMBER = 2;
    private org.gof.demo.worldsrv.msg.Define.p_worker_farm_info teamInfo_;
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_info team_info = 2;</code>
     * @return Whether the teamInfo field is set.
     */
    @java.lang.Override
    public boolean hasTeamInfo() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_info team_info = 2;</code>
     * @return The teamInfo.
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_worker_farm_info getTeamInfo() {
      return teamInfo_ == null ? org.gof.demo.worldsrv.msg.Define.p_worker_farm_info.getDefaultInstance() : teamInfo_;
    }
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_info team_info = 2;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_worker_farm_infoOrBuilder getTeamInfoOrBuilder() {
      return teamInfo_ == null ? org.gof.demo.worldsrv.msg.Define.p_worker_farm_info.getDefaultInstance() : teamInfo_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (result_ != 0) {
        output.writeUInt32(1, result_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(2, getTeamInfo());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (result_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, result_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getTeamInfo());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_s2c other = (org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_s2c) obj;

      if (getResult()
          != other.getResult()) return false;
      if (hasTeamInfo() != other.hasTeamInfo()) return false;
      if (hasTeamInfo()) {
        if (!getTeamInfo()
            .equals(other.getTeamInfo())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + RESULT_FIELD_NUMBER;
      hash = (53 * hash) + getResult();
      if (hasTeamInfo()) {
        hash = (37 * hash) + TEAM_INFO_FIELD_NUMBER;
        hash = (53 * hash) + getTeamInfo().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.worker_farm_add_worker_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.worker_farm_add_worker_s2c)
        org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgWorkerFarm.internal_static_org_gof_demo_worldsrv_msg_worker_farm_add_worker_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgWorkerFarm.internal_static_org_gof_demo_worldsrv_msg_worker_farm_add_worker_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_s2c.class, org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_s2c.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getTeamInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        result_ = 0;
        teamInfo_ = null;
        if (teamInfoBuilder_ != null) {
          teamInfoBuilder_.dispose();
          teamInfoBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgWorkerFarm.internal_static_org_gof_demo_worldsrv_msg_worker_farm_add_worker_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_s2c build() {
        org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_s2c result = new org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.result_ = result_;
        }
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.teamInfo_ = teamInfoBuilder_ == null
              ? teamInfo_
              : teamInfoBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_s2c.getDefaultInstance()) return this;
        if (other.getResult() != 0) {
          setResult(other.getResult());
        }
        if (other.hasTeamInfo()) {
          mergeTeamInfo(other.getTeamInfo());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                result_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 18: {
                input.readMessage(
                    getTeamInfoFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int result_ ;
      /**
       * <code>uint32 result = 1;</code>
       * @return The result.
       */
      @java.lang.Override
      public int getResult() {
        return result_;
      }
      /**
       * <code>uint32 result = 1;</code>
       * @param value The result to set.
       * @return This builder for chaining.
       */
      public Builder setResult(int value) {

        result_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 result = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearResult() {
        bitField0_ = (bitField0_ & ~0x00000001);
        result_ = 0;
        onChanged();
        return this;
      }

      private org.gof.demo.worldsrv.msg.Define.p_worker_farm_info teamInfo_;
      private com.google.protobuf.SingleFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_worker_farm_info, org.gof.demo.worldsrv.msg.Define.p_worker_farm_info.Builder, org.gof.demo.worldsrv.msg.Define.p_worker_farm_infoOrBuilder> teamInfoBuilder_;
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_info team_info = 2;</code>
       * @return Whether the teamInfo field is set.
       */
      public boolean hasTeamInfo() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_info team_info = 2;</code>
       * @return The teamInfo.
       */
      public org.gof.demo.worldsrv.msg.Define.p_worker_farm_info getTeamInfo() {
        if (teamInfoBuilder_ == null) {
          return teamInfo_ == null ? org.gof.demo.worldsrv.msg.Define.p_worker_farm_info.getDefaultInstance() : teamInfo_;
        } else {
          return teamInfoBuilder_.getMessage();
        }
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_info team_info = 2;</code>
       */
      public Builder setTeamInfo(org.gof.demo.worldsrv.msg.Define.p_worker_farm_info value) {
        if (teamInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          teamInfo_ = value;
        } else {
          teamInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_info team_info = 2;</code>
       */
      public Builder setTeamInfo(
          org.gof.demo.worldsrv.msg.Define.p_worker_farm_info.Builder builderForValue) {
        if (teamInfoBuilder_ == null) {
          teamInfo_ = builderForValue.build();
        } else {
          teamInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_info team_info = 2;</code>
       */
      public Builder mergeTeamInfo(org.gof.demo.worldsrv.msg.Define.p_worker_farm_info value) {
        if (teamInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
            teamInfo_ != null &&
            teamInfo_ != org.gof.demo.worldsrv.msg.Define.p_worker_farm_info.getDefaultInstance()) {
            getTeamInfoBuilder().mergeFrom(value);
          } else {
            teamInfo_ = value;
          }
        } else {
          teamInfoBuilder_.mergeFrom(value);
        }
        if (teamInfo_ != null) {
          bitField0_ |= 0x00000002;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_info team_info = 2;</code>
       */
      public Builder clearTeamInfo() {
        bitField0_ = (bitField0_ & ~0x00000002);
        teamInfo_ = null;
        if (teamInfoBuilder_ != null) {
          teamInfoBuilder_.dispose();
          teamInfoBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_info team_info = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_worker_farm_info.Builder getTeamInfoBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getTeamInfoFieldBuilder().getBuilder();
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_info team_info = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_worker_farm_infoOrBuilder getTeamInfoOrBuilder() {
        if (teamInfoBuilder_ != null) {
          return teamInfoBuilder_.getMessageOrBuilder();
        } else {
          return teamInfo_ == null ?
              org.gof.demo.worldsrv.msg.Define.p_worker_farm_info.getDefaultInstance() : teamInfo_;
        }
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_worker_farm_info team_info = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_worker_farm_info, org.gof.demo.worldsrv.msg.Define.p_worker_farm_info.Builder, org.gof.demo.worldsrv.msg.Define.p_worker_farm_infoOrBuilder> 
          getTeamInfoFieldBuilder() {
        if (teamInfoBuilder_ == null) {
          teamInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_worker_farm_info, org.gof.demo.worldsrv.msg.Define.p_worker_farm_info.Builder, org.gof.demo.worldsrv.msg.Define.p_worker_farm_infoOrBuilder>(
                  getTeamInfo(),
                  getParentForChildren(),
                  isClean());
          teamInfo_ = null;
        }
        return teamInfoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.worker_farm_add_worker_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.worker_farm_add_worker_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<worker_farm_add_worker_s2c>
        PARSER = new com.google.protobuf.AbstractParser<worker_farm_add_worker_s2c>() {
      @java.lang.Override
      public worker_farm_add_worker_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<worker_farm_add_worker_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<worker_farm_add_worker_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_add_worker_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface worker_farm_history_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.worker_farm_history_c2s)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.worker_farm_history_c2s}
   */
  public static final class worker_farm_history_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.worker_farm_history_c2s)
      worker_farm_history_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use worker_farm_history_c2s.newBuilder() to construct.
    private worker_farm_history_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private worker_farm_history_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new worker_farm_history_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgWorkerFarm.internal_static_org_gof_demo_worldsrv_msg_worker_farm_history_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgWorkerFarm.internal_static_org_gof_demo_worldsrv_msg_worker_farm_history_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_c2s.class, org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_c2s.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_c2s other = (org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_c2s) obj;

      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.worker_farm_history_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.worker_farm_history_c2s)
        org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgWorkerFarm.internal_static_org_gof_demo_worldsrv_msg_worker_farm_history_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgWorkerFarm.internal_static_org_gof_demo_worldsrv_msg_worker_farm_history_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_c2s.class, org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgWorkerFarm.internal_static_org_gof_demo_worldsrv_msg_worker_farm_history_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_c2s build() {
        org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_c2s result = new org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_c2s(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_c2s.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.worker_farm_history_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.worker_farm_history_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<worker_farm_history_c2s>
        PARSER = new com.google.protobuf.AbstractParser<worker_farm_history_c2s>() {
      @java.lang.Override
      public worker_farm_history_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<worker_farm_history_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<worker_farm_history_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface worker_farm_history_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.worker_farm_history_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_history_info team_info = 1;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_worker_farm_history_info> 
        getTeamInfoList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_history_info team_info = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_worker_farm_history_info getTeamInfo(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_history_info team_info = 1;</code>
     */
    int getTeamInfoCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_history_info team_info = 1;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_worker_farm_history_infoOrBuilder> 
        getTeamInfoOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_history_info team_info = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_worker_farm_history_infoOrBuilder getTeamInfoOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.worker_farm_history_s2c}
   */
  public static final class worker_farm_history_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.worker_farm_history_s2c)
      worker_farm_history_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use worker_farm_history_s2c.newBuilder() to construct.
    private worker_farm_history_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private worker_farm_history_s2c() {
      teamInfo_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new worker_farm_history_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgWorkerFarm.internal_static_org_gof_demo_worldsrv_msg_worker_farm_history_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgWorkerFarm.internal_static_org_gof_demo_worldsrv_msg_worker_farm_history_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_s2c.class, org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_s2c.Builder.class);
    }

    public static final int TEAM_INFO_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_worker_farm_history_info> teamInfo_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_history_info team_info = 1;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_worker_farm_history_info> getTeamInfoList() {
      return teamInfo_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_history_info team_info = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_worker_farm_history_infoOrBuilder> 
        getTeamInfoOrBuilderList() {
      return teamInfo_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_history_info team_info = 1;</code>
     */
    @java.lang.Override
    public int getTeamInfoCount() {
      return teamInfo_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_history_info team_info = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_worker_farm_history_info getTeamInfo(int index) {
      return teamInfo_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_history_info team_info = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_worker_farm_history_infoOrBuilder getTeamInfoOrBuilder(
        int index) {
      return teamInfo_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < teamInfo_.size(); i++) {
        output.writeMessage(1, teamInfo_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < teamInfo_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, teamInfo_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_s2c other = (org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_s2c) obj;

      if (!getTeamInfoList()
          .equals(other.getTeamInfoList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getTeamInfoCount() > 0) {
        hash = (37 * hash) + TEAM_INFO_FIELD_NUMBER;
        hash = (53 * hash) + getTeamInfoList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.worker_farm_history_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.worker_farm_history_s2c)
        org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgWorkerFarm.internal_static_org_gof_demo_worldsrv_msg_worker_farm_history_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgWorkerFarm.internal_static_org_gof_demo_worldsrv_msg_worker_farm_history_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_s2c.class, org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        if (teamInfoBuilder_ == null) {
          teamInfo_ = java.util.Collections.emptyList();
        } else {
          teamInfo_ = null;
          teamInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgWorkerFarm.internal_static_org_gof_demo_worldsrv_msg_worker_farm_history_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_s2c build() {
        org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_s2c result = new org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_s2c(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_s2c result) {
        if (teamInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            teamInfo_ = java.util.Collections.unmodifiableList(teamInfo_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.teamInfo_ = teamInfo_;
        } else {
          result.teamInfo_ = teamInfoBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_s2c result) {
        int from_bitField0_ = bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_s2c.getDefaultInstance()) return this;
        if (teamInfoBuilder_ == null) {
          if (!other.teamInfo_.isEmpty()) {
            if (teamInfo_.isEmpty()) {
              teamInfo_ = other.teamInfo_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureTeamInfoIsMutable();
              teamInfo_.addAll(other.teamInfo_);
            }
            onChanged();
          }
        } else {
          if (!other.teamInfo_.isEmpty()) {
            if (teamInfoBuilder_.isEmpty()) {
              teamInfoBuilder_.dispose();
              teamInfoBuilder_ = null;
              teamInfo_ = other.teamInfo_;
              bitField0_ = (bitField0_ & ~0x00000001);
              teamInfoBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getTeamInfoFieldBuilder() : null;
            } else {
              teamInfoBuilder_.addAllMessages(other.teamInfo_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                org.gof.demo.worldsrv.msg.Define.p_worker_farm_history_info m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_worker_farm_history_info.parser(),
                        extensionRegistry);
                if (teamInfoBuilder_ == null) {
                  ensureTeamInfoIsMutable();
                  teamInfo_.add(m);
                } else {
                  teamInfoBuilder_.addMessage(m);
                }
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_worker_farm_history_info> teamInfo_ =
        java.util.Collections.emptyList();
      private void ensureTeamInfoIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          teamInfo_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_worker_farm_history_info>(teamInfo_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_worker_farm_history_info, org.gof.demo.worldsrv.msg.Define.p_worker_farm_history_info.Builder, org.gof.demo.worldsrv.msg.Define.p_worker_farm_history_infoOrBuilder> teamInfoBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_history_info team_info = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_worker_farm_history_info> getTeamInfoList() {
        if (teamInfoBuilder_ == null) {
          return java.util.Collections.unmodifiableList(teamInfo_);
        } else {
          return teamInfoBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_history_info team_info = 1;</code>
       */
      public int getTeamInfoCount() {
        if (teamInfoBuilder_ == null) {
          return teamInfo_.size();
        } else {
          return teamInfoBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_history_info team_info = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_worker_farm_history_info getTeamInfo(int index) {
        if (teamInfoBuilder_ == null) {
          return teamInfo_.get(index);
        } else {
          return teamInfoBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_history_info team_info = 1;</code>
       */
      public Builder setTeamInfo(
          int index, org.gof.demo.worldsrv.msg.Define.p_worker_farm_history_info value) {
        if (teamInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTeamInfoIsMutable();
          teamInfo_.set(index, value);
          onChanged();
        } else {
          teamInfoBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_history_info team_info = 1;</code>
       */
      public Builder setTeamInfo(
          int index, org.gof.demo.worldsrv.msg.Define.p_worker_farm_history_info.Builder builderForValue) {
        if (teamInfoBuilder_ == null) {
          ensureTeamInfoIsMutable();
          teamInfo_.set(index, builderForValue.build());
          onChanged();
        } else {
          teamInfoBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_history_info team_info = 1;</code>
       */
      public Builder addTeamInfo(org.gof.demo.worldsrv.msg.Define.p_worker_farm_history_info value) {
        if (teamInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTeamInfoIsMutable();
          teamInfo_.add(value);
          onChanged();
        } else {
          teamInfoBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_history_info team_info = 1;</code>
       */
      public Builder addTeamInfo(
          int index, org.gof.demo.worldsrv.msg.Define.p_worker_farm_history_info value) {
        if (teamInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTeamInfoIsMutable();
          teamInfo_.add(index, value);
          onChanged();
        } else {
          teamInfoBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_history_info team_info = 1;</code>
       */
      public Builder addTeamInfo(
          org.gof.demo.worldsrv.msg.Define.p_worker_farm_history_info.Builder builderForValue) {
        if (teamInfoBuilder_ == null) {
          ensureTeamInfoIsMutable();
          teamInfo_.add(builderForValue.build());
          onChanged();
        } else {
          teamInfoBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_history_info team_info = 1;</code>
       */
      public Builder addTeamInfo(
          int index, org.gof.demo.worldsrv.msg.Define.p_worker_farm_history_info.Builder builderForValue) {
        if (teamInfoBuilder_ == null) {
          ensureTeamInfoIsMutable();
          teamInfo_.add(index, builderForValue.build());
          onChanged();
        } else {
          teamInfoBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_history_info team_info = 1;</code>
       */
      public Builder addAllTeamInfo(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_worker_farm_history_info> values) {
        if (teamInfoBuilder_ == null) {
          ensureTeamInfoIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, teamInfo_);
          onChanged();
        } else {
          teamInfoBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_history_info team_info = 1;</code>
       */
      public Builder clearTeamInfo() {
        if (teamInfoBuilder_ == null) {
          teamInfo_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          teamInfoBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_history_info team_info = 1;</code>
       */
      public Builder removeTeamInfo(int index) {
        if (teamInfoBuilder_ == null) {
          ensureTeamInfoIsMutable();
          teamInfo_.remove(index);
          onChanged();
        } else {
          teamInfoBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_history_info team_info = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_worker_farm_history_info.Builder getTeamInfoBuilder(
          int index) {
        return getTeamInfoFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_history_info team_info = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_worker_farm_history_infoOrBuilder getTeamInfoOrBuilder(
          int index) {
        if (teamInfoBuilder_ == null) {
          return teamInfo_.get(index);  } else {
          return teamInfoBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_history_info team_info = 1;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_worker_farm_history_infoOrBuilder> 
           getTeamInfoOrBuilderList() {
        if (teamInfoBuilder_ != null) {
          return teamInfoBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(teamInfo_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_history_info team_info = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_worker_farm_history_info.Builder addTeamInfoBuilder() {
        return getTeamInfoFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_worker_farm_history_info.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_history_info team_info = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_worker_farm_history_info.Builder addTeamInfoBuilder(
          int index) {
        return getTeamInfoFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_worker_farm_history_info.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_history_info team_info = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_worker_farm_history_info.Builder> 
           getTeamInfoBuilderList() {
        return getTeamInfoFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_worker_farm_history_info, org.gof.demo.worldsrv.msg.Define.p_worker_farm_history_info.Builder, org.gof.demo.worldsrv.msg.Define.p_worker_farm_history_infoOrBuilder> 
          getTeamInfoFieldBuilder() {
        if (teamInfoBuilder_ == null) {
          teamInfoBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_worker_farm_history_info, org.gof.demo.worldsrv.msg.Define.p_worker_farm_history_info.Builder, org.gof.demo.worldsrv.msg.Define.p_worker_farm_history_infoOrBuilder>(
                  teamInfo_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          teamInfo_ = null;
        }
        return teamInfoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.worker_farm_history_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.worker_farm_history_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<worker_farm_history_s2c>
        PARSER = new com.google.protobuf.AbstractParser<worker_farm_history_s2c>() {
      @java.lang.Override
      public worker_farm_history_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<worker_farm_history_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<worker_farm_history_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_history_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface worker_farm_team_update_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.worker_farm_team_update_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_info team_info = 1;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_worker_farm_info> 
        getTeamInfoList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_info team_info = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_worker_farm_info getTeamInfo(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_info team_info = 1;</code>
     */
    int getTeamInfoCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_info team_info = 1;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_worker_farm_infoOrBuilder> 
        getTeamInfoOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_info team_info = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_worker_farm_infoOrBuilder getTeamInfoOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.worker_farm_team_update_s2c}
   */
  public static final class worker_farm_team_update_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.worker_farm_team_update_s2c)
      worker_farm_team_update_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use worker_farm_team_update_s2c.newBuilder() to construct.
    private worker_farm_team_update_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private worker_farm_team_update_s2c() {
      teamInfo_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new worker_farm_team_update_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgWorkerFarm.internal_static_org_gof_demo_worldsrv_msg_worker_farm_team_update_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgWorkerFarm.internal_static_org_gof_demo_worldsrv_msg_worker_farm_team_update_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_team_update_s2c.class, org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_team_update_s2c.Builder.class);
    }

    public static final int TEAM_INFO_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_worker_farm_info> teamInfo_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_info team_info = 1;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_worker_farm_info> getTeamInfoList() {
      return teamInfo_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_info team_info = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_worker_farm_infoOrBuilder> 
        getTeamInfoOrBuilderList() {
      return teamInfo_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_info team_info = 1;</code>
     */
    @java.lang.Override
    public int getTeamInfoCount() {
      return teamInfo_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_info team_info = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_worker_farm_info getTeamInfo(int index) {
      return teamInfo_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_info team_info = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_worker_farm_infoOrBuilder getTeamInfoOrBuilder(
        int index) {
      return teamInfo_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < teamInfo_.size(); i++) {
        output.writeMessage(1, teamInfo_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < teamInfo_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, teamInfo_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_team_update_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_team_update_s2c other = (org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_team_update_s2c) obj;

      if (!getTeamInfoList()
          .equals(other.getTeamInfoList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getTeamInfoCount() > 0) {
        hash = (37 * hash) + TEAM_INFO_FIELD_NUMBER;
        hash = (53 * hash) + getTeamInfoList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_team_update_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_team_update_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_team_update_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_team_update_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_team_update_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_team_update_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_team_update_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_team_update_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_team_update_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_team_update_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_team_update_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_team_update_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_team_update_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.worker_farm_team_update_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.worker_farm_team_update_s2c)
        org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_team_update_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgWorkerFarm.internal_static_org_gof_demo_worldsrv_msg_worker_farm_team_update_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgWorkerFarm.internal_static_org_gof_demo_worldsrv_msg_worker_farm_team_update_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_team_update_s2c.class, org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_team_update_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_team_update_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        if (teamInfoBuilder_ == null) {
          teamInfo_ = java.util.Collections.emptyList();
        } else {
          teamInfo_ = null;
          teamInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgWorkerFarm.internal_static_org_gof_demo_worldsrv_msg_worker_farm_team_update_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_team_update_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_team_update_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_team_update_s2c build() {
        org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_team_update_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_team_update_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_team_update_s2c result = new org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_team_update_s2c(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_team_update_s2c result) {
        if (teamInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            teamInfo_ = java.util.Collections.unmodifiableList(teamInfo_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.teamInfo_ = teamInfo_;
        } else {
          result.teamInfo_ = teamInfoBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_team_update_s2c result) {
        int from_bitField0_ = bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_team_update_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_team_update_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_team_update_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_team_update_s2c.getDefaultInstance()) return this;
        if (teamInfoBuilder_ == null) {
          if (!other.teamInfo_.isEmpty()) {
            if (teamInfo_.isEmpty()) {
              teamInfo_ = other.teamInfo_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureTeamInfoIsMutable();
              teamInfo_.addAll(other.teamInfo_);
            }
            onChanged();
          }
        } else {
          if (!other.teamInfo_.isEmpty()) {
            if (teamInfoBuilder_.isEmpty()) {
              teamInfoBuilder_.dispose();
              teamInfoBuilder_ = null;
              teamInfo_ = other.teamInfo_;
              bitField0_ = (bitField0_ & ~0x00000001);
              teamInfoBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getTeamInfoFieldBuilder() : null;
            } else {
              teamInfoBuilder_.addAllMessages(other.teamInfo_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                org.gof.demo.worldsrv.msg.Define.p_worker_farm_info m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_worker_farm_info.parser(),
                        extensionRegistry);
                if (teamInfoBuilder_ == null) {
                  ensureTeamInfoIsMutable();
                  teamInfo_.add(m);
                } else {
                  teamInfoBuilder_.addMessage(m);
                }
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_worker_farm_info> teamInfo_ =
        java.util.Collections.emptyList();
      private void ensureTeamInfoIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          teamInfo_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_worker_farm_info>(teamInfo_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_worker_farm_info, org.gof.demo.worldsrv.msg.Define.p_worker_farm_info.Builder, org.gof.demo.worldsrv.msg.Define.p_worker_farm_infoOrBuilder> teamInfoBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_info team_info = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_worker_farm_info> getTeamInfoList() {
        if (teamInfoBuilder_ == null) {
          return java.util.Collections.unmodifiableList(teamInfo_);
        } else {
          return teamInfoBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_info team_info = 1;</code>
       */
      public int getTeamInfoCount() {
        if (teamInfoBuilder_ == null) {
          return teamInfo_.size();
        } else {
          return teamInfoBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_info team_info = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_worker_farm_info getTeamInfo(int index) {
        if (teamInfoBuilder_ == null) {
          return teamInfo_.get(index);
        } else {
          return teamInfoBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_info team_info = 1;</code>
       */
      public Builder setTeamInfo(
          int index, org.gof.demo.worldsrv.msg.Define.p_worker_farm_info value) {
        if (teamInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTeamInfoIsMutable();
          teamInfo_.set(index, value);
          onChanged();
        } else {
          teamInfoBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_info team_info = 1;</code>
       */
      public Builder setTeamInfo(
          int index, org.gof.demo.worldsrv.msg.Define.p_worker_farm_info.Builder builderForValue) {
        if (teamInfoBuilder_ == null) {
          ensureTeamInfoIsMutable();
          teamInfo_.set(index, builderForValue.build());
          onChanged();
        } else {
          teamInfoBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_info team_info = 1;</code>
       */
      public Builder addTeamInfo(org.gof.demo.worldsrv.msg.Define.p_worker_farm_info value) {
        if (teamInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTeamInfoIsMutable();
          teamInfo_.add(value);
          onChanged();
        } else {
          teamInfoBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_info team_info = 1;</code>
       */
      public Builder addTeamInfo(
          int index, org.gof.demo.worldsrv.msg.Define.p_worker_farm_info value) {
        if (teamInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTeamInfoIsMutable();
          teamInfo_.add(index, value);
          onChanged();
        } else {
          teamInfoBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_info team_info = 1;</code>
       */
      public Builder addTeamInfo(
          org.gof.demo.worldsrv.msg.Define.p_worker_farm_info.Builder builderForValue) {
        if (teamInfoBuilder_ == null) {
          ensureTeamInfoIsMutable();
          teamInfo_.add(builderForValue.build());
          onChanged();
        } else {
          teamInfoBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_info team_info = 1;</code>
       */
      public Builder addTeamInfo(
          int index, org.gof.demo.worldsrv.msg.Define.p_worker_farm_info.Builder builderForValue) {
        if (teamInfoBuilder_ == null) {
          ensureTeamInfoIsMutable();
          teamInfo_.add(index, builderForValue.build());
          onChanged();
        } else {
          teamInfoBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_info team_info = 1;</code>
       */
      public Builder addAllTeamInfo(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_worker_farm_info> values) {
        if (teamInfoBuilder_ == null) {
          ensureTeamInfoIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, teamInfo_);
          onChanged();
        } else {
          teamInfoBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_info team_info = 1;</code>
       */
      public Builder clearTeamInfo() {
        if (teamInfoBuilder_ == null) {
          teamInfo_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          teamInfoBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_info team_info = 1;</code>
       */
      public Builder removeTeamInfo(int index) {
        if (teamInfoBuilder_ == null) {
          ensureTeamInfoIsMutable();
          teamInfo_.remove(index);
          onChanged();
        } else {
          teamInfoBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_info team_info = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_worker_farm_info.Builder getTeamInfoBuilder(
          int index) {
        return getTeamInfoFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_info team_info = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_worker_farm_infoOrBuilder getTeamInfoOrBuilder(
          int index) {
        if (teamInfoBuilder_ == null) {
          return teamInfo_.get(index);  } else {
          return teamInfoBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_info team_info = 1;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_worker_farm_infoOrBuilder> 
           getTeamInfoOrBuilderList() {
        if (teamInfoBuilder_ != null) {
          return teamInfoBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(teamInfo_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_info team_info = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_worker_farm_info.Builder addTeamInfoBuilder() {
        return getTeamInfoFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_worker_farm_info.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_info team_info = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_worker_farm_info.Builder addTeamInfoBuilder(
          int index) {
        return getTeamInfoFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_worker_farm_info.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_worker_farm_info team_info = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_worker_farm_info.Builder> 
           getTeamInfoBuilderList() {
        return getTeamInfoFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_worker_farm_info, org.gof.demo.worldsrv.msg.Define.p_worker_farm_info.Builder, org.gof.demo.worldsrv.msg.Define.p_worker_farm_infoOrBuilder> 
          getTeamInfoFieldBuilder() {
        if (teamInfoBuilder_ == null) {
          teamInfoBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_worker_farm_info, org.gof.demo.worldsrv.msg.Define.p_worker_farm_info.Builder, org.gof.demo.worldsrv.msg.Define.p_worker_farm_infoOrBuilder>(
                  teamInfo_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          teamInfo_ = null;
        }
        return teamInfoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.worker_farm_team_update_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.worker_farm_team_update_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_team_update_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_team_update_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_team_update_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<worker_farm_team_update_s2c>
        PARSER = new com.google.protobuf.AbstractParser<worker_farm_team_update_s2c>() {
      @java.lang.Override
      public worker_farm_team_update_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<worker_farm_team_update_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<worker_farm_team_update_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgWorkerFarm.worker_farm_team_update_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_worker_farm_worker_setting_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_worker_farm_worker_setting_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_worker_farm_worker_setting_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_worker_farm_worker_setting_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_worker_farm_worker_setting_info_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_worker_farm_worker_setting_info_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_worker_farm_worker_setting_info_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_worker_farm_worker_setting_info_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_worker_farm_add_worker_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_worker_farm_add_worker_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_worker_farm_add_worker_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_worker_farm_add_worker_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_worker_farm_history_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_worker_farm_history_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_worker_farm_history_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_worker_farm_history_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_worker_farm_team_update_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_worker_farm_team_update_s2c_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\024msg.WorkerFarm.proto\022\031org.gof.demo.wor" +
      "ldsrv.msg\032\roptions.proto\032\014define.proto\"m" +
      "\n\036worker_farm_worker_setting_c2s\022C\n\004info" +
      "\030\001 \001(\01325.org.gof.demo.worldsrv.msg.p_wor" +
      "ker_farm_setting_info:\006\210\303\032\201\222\001\"}\n\036worker_" +
      "farm_worker_setting_s2c\022\016\n\006result\030\001 \001(\r\022" +
      "C\n\004info\030\002 \001(\01325.org.gof.demo.worldsrv.ms" +
      "g.p_worker_farm_setting_info:\006\210\303\032\201\222\001\";\n#" +
      "worker_farm_worker_setting_info_c2s\022\014\n\004t" +
      "ype\030\001 \001(\r:\006\210\303\032\202\222\001\"z\n#worker_farm_worker_" +
      "setting_info_s2c\022K\n\014setting_list\030\001 \003(\01325" +
      ".org.gof.demo.worldsrv.msg.p_worker_farm" +
      "_setting_info:\006\210\303\032\202\222\001\"l\n\032worker_farm_add" +
      "_worker_c2s\022F\n\007setting\030\001 \001(\01325.org.gof.d" +
      "emo.worldsrv.msg.p_worker_farm_setting_i" +
      "nfo:\006\210\303\032\203\222\001\"v\n\032worker_farm_add_worker_s2" +
      "c\022\016\n\006result\030\001 \001(\r\022@\n\tteam_info\030\002 \001(\0132-.o" +
      "rg.gof.demo.worldsrv.msg.p_worker_farm_i" +
      "nfo:\006\210\303\032\203\222\001\"!\n\027worker_farm_history_c2s:\006" +
      "\210\303\032\204\222\001\"k\n\027worker_farm_history_s2c\022H\n\ttea" +
      "m_info\030\001 \003(\01325.org.gof.demo.worldsrv.msg" +
      ".p_worker_farm_history_info:\006\210\303\032\204\222\001\"g\n\033w" +
      "orker_farm_team_update_s2c\022@\n\tteam_info\030" +
      "\001 \003(\0132-.org.gof.demo.worldsrv.msg.p_work" +
      "er_farm_info:\006\210\303\032\205\222\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          org.gof.demo.worldsrv.msg.Options.getDescriptor(),
          org.gof.demo.worldsrv.msg.Define.getDescriptor(),
        });
    internal_static_org_gof_demo_worldsrv_msg_worker_farm_worker_setting_c2s_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_org_gof_demo_worldsrv_msg_worker_farm_worker_setting_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_worker_farm_worker_setting_c2s_descriptor,
        new java.lang.String[] { "Info", });
    internal_static_org_gof_demo_worldsrv_msg_worker_farm_worker_setting_s2c_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_org_gof_demo_worldsrv_msg_worker_farm_worker_setting_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_worker_farm_worker_setting_s2c_descriptor,
        new java.lang.String[] { "Result", "Info", });
    internal_static_org_gof_demo_worldsrv_msg_worker_farm_worker_setting_info_c2s_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_org_gof_demo_worldsrv_msg_worker_farm_worker_setting_info_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_worker_farm_worker_setting_info_c2s_descriptor,
        new java.lang.String[] { "Type", });
    internal_static_org_gof_demo_worldsrv_msg_worker_farm_worker_setting_info_s2c_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_org_gof_demo_worldsrv_msg_worker_farm_worker_setting_info_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_worker_farm_worker_setting_info_s2c_descriptor,
        new java.lang.String[] { "SettingList", });
    internal_static_org_gof_demo_worldsrv_msg_worker_farm_add_worker_c2s_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_org_gof_demo_worldsrv_msg_worker_farm_add_worker_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_worker_farm_add_worker_c2s_descriptor,
        new java.lang.String[] { "Setting", });
    internal_static_org_gof_demo_worldsrv_msg_worker_farm_add_worker_s2c_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_org_gof_demo_worldsrv_msg_worker_farm_add_worker_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_worker_farm_add_worker_s2c_descriptor,
        new java.lang.String[] { "Result", "TeamInfo", });
    internal_static_org_gof_demo_worldsrv_msg_worker_farm_history_c2s_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_org_gof_demo_worldsrv_msg_worker_farm_history_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_worker_farm_history_c2s_descriptor,
        new java.lang.String[] { });
    internal_static_org_gof_demo_worldsrv_msg_worker_farm_history_s2c_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_org_gof_demo_worldsrv_msg_worker_farm_history_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_worker_farm_history_s2c_descriptor,
        new java.lang.String[] { "TeamInfo", });
    internal_static_org_gof_demo_worldsrv_msg_worker_farm_team_update_s2c_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_org_gof_demo_worldsrv_msg_worker_farm_team_update_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_worker_farm_team_update_s2c_descriptor,
        new java.lang.String[] { "TeamInfo", });
    com.google.protobuf.ExtensionRegistry registry =
        com.google.protobuf.ExtensionRegistry.newInstance();
    registry.add(org.gof.demo.worldsrv.msg.Options.msgid);
    com.google.protobuf.Descriptors.FileDescriptor
        .internalUpdateFileDescriptor(descriptor, registry);
    org.gof.demo.worldsrv.msg.Options.getDescriptor();
    org.gof.demo.worldsrv.msg.Define.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
