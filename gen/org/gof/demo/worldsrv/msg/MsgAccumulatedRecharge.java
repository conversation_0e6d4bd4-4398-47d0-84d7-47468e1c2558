// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: msg.accumulatedRecharge.proto

// Protobuf Java Version: 3.25.3
package org.gof.demo.worldsrv.msg;

public final class MsgAccumulatedRecharge {
  private MsgAccumulatedRecharge() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface accumulated_recharge_info_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.accumulated_recharge_info_c2s)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * <pre>
   * 累充信息总览
   * </pre>
   *
   * Protobuf type {@code org.gof.demo.worldsrv.msg.accumulated_recharge_info_c2s}
   */
  public static final class accumulated_recharge_info_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.accumulated_recharge_info_c2s)
      accumulated_recharge_info_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use accumulated_recharge_info_c2s.newBuilder() to construct.
    private accumulated_recharge_info_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private accumulated_recharge_info_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new accumulated_recharge_info_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.internal_static_org_gof_demo_worldsrv_msg_accumulated_recharge_info_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.internal_static_org_gof_demo_worldsrv_msg_accumulated_recharge_info_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_c2s.class, org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_c2s.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_c2s other = (org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_c2s) obj;

      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 累充信息总览
     * </pre>
     *
     * Protobuf type {@code org.gof.demo.worldsrv.msg.accumulated_recharge_info_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.accumulated_recharge_info_c2s)
        org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.internal_static_org_gof_demo_worldsrv_msg_accumulated_recharge_info_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.internal_static_org_gof_demo_worldsrv_msg_accumulated_recharge_info_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_c2s.class, org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.internal_static_org_gof_demo_worldsrv_msg_accumulated_recharge_info_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_c2s build() {
        org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_c2s result = new org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_c2s(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_c2s.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.accumulated_recharge_info_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.accumulated_recharge_info_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<accumulated_recharge_info_c2s>
        PARSER = new com.google.protobuf.AbstractParser<accumulated_recharge_info_c2s>() {
      @java.lang.Override
      public accumulated_recharge_info_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<accumulated_recharge_info_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<accumulated_recharge_info_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface accumulated_recharge_info_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.accumulated_recharge_info_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 第几轮累充活动
     * </pre>
     *
     * <code>uint32 round = 1;</code>
     * @return The round.
     */
    int getRound();

    /**
     * <pre>
     * 当前轮累计充值了几天
     * </pre>
     *
     * <code>uint32 recharge_day = 2;</code>
     * @return The rechargeDay.
     */
    int getRechargeDay();

    /**
     * <pre>
     * 免费礼包状态（2：可领取 3：已领取）
     * </pre>
     *
     * <code>uint32 free_state = 3;</code>
     * @return The freeState.
     */
    int getFreeState();

    /**
     * <pre>
     * 每日充值礼包状态（1：无法领取 2：可领取 3：已领取）
     * </pre>
     *
     * <code>uint32 daily_state = 4;</code>
     * @return The dailyState.
     */
    int getDailyState();

    /**
     * <pre>
     * 已领取的累充信息（[5,10,15]）
     * </pre>
     *
     * <code>repeated uint32 received_info = 5;</code>
     * @return A list containing the receivedInfo.
     */
    java.util.List<java.lang.Integer> getReceivedInfoList();
    /**
     * <pre>
     * 已领取的累充信息（[5,10,15]）
     * </pre>
     *
     * <code>repeated uint32 received_info = 5;</code>
     * @return The count of receivedInfo.
     */
    int getReceivedInfoCount();
    /**
     * <pre>
     * 已领取的累充信息（[5,10,15]）
     * </pre>
     *
     * <code>repeated uint32 received_info = 5;</code>
     * @param index The index of the element to return.
     * @return The receivedInfo at the given index.
     */
    int getReceivedInfo(int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.accumulated_recharge_info_s2c}
   */
  public static final class accumulated_recharge_info_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.accumulated_recharge_info_s2c)
      accumulated_recharge_info_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use accumulated_recharge_info_s2c.newBuilder() to construct.
    private accumulated_recharge_info_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private accumulated_recharge_info_s2c() {
      receivedInfo_ = emptyIntList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new accumulated_recharge_info_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.internal_static_org_gof_demo_worldsrv_msg_accumulated_recharge_info_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.internal_static_org_gof_demo_worldsrv_msg_accumulated_recharge_info_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_s2c.class, org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_s2c.Builder.class);
    }

    public static final int ROUND_FIELD_NUMBER = 1;
    private int round_ = 0;
    /**
     * <pre>
     * 第几轮累充活动
     * </pre>
     *
     * <code>uint32 round = 1;</code>
     * @return The round.
     */
    @java.lang.Override
    public int getRound() {
      return round_;
    }

    public static final int RECHARGE_DAY_FIELD_NUMBER = 2;
    private int rechargeDay_ = 0;
    /**
     * <pre>
     * 当前轮累计充值了几天
     * </pre>
     *
     * <code>uint32 recharge_day = 2;</code>
     * @return The rechargeDay.
     */
    @java.lang.Override
    public int getRechargeDay() {
      return rechargeDay_;
    }

    public static final int FREE_STATE_FIELD_NUMBER = 3;
    private int freeState_ = 0;
    /**
     * <pre>
     * 免费礼包状态（2：可领取 3：已领取）
     * </pre>
     *
     * <code>uint32 free_state = 3;</code>
     * @return The freeState.
     */
    @java.lang.Override
    public int getFreeState() {
      return freeState_;
    }

    public static final int DAILY_STATE_FIELD_NUMBER = 4;
    private int dailyState_ = 0;
    /**
     * <pre>
     * 每日充值礼包状态（1：无法领取 2：可领取 3：已领取）
     * </pre>
     *
     * <code>uint32 daily_state = 4;</code>
     * @return The dailyState.
     */
    @java.lang.Override
    public int getDailyState() {
      return dailyState_;
    }

    public static final int RECEIVED_INFO_FIELD_NUMBER = 5;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.IntList receivedInfo_ =
        emptyIntList();
    /**
     * <pre>
     * 已领取的累充信息（[5,10,15]）
     * </pre>
     *
     * <code>repeated uint32 received_info = 5;</code>
     * @return A list containing the receivedInfo.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getReceivedInfoList() {
      return receivedInfo_;
    }
    /**
     * <pre>
     * 已领取的累充信息（[5,10,15]）
     * </pre>
     *
     * <code>repeated uint32 received_info = 5;</code>
     * @return The count of receivedInfo.
     */
    public int getReceivedInfoCount() {
      return receivedInfo_.size();
    }
    /**
     * <pre>
     * 已领取的累充信息（[5,10,15]）
     * </pre>
     *
     * <code>repeated uint32 received_info = 5;</code>
     * @param index The index of the element to return.
     * @return The receivedInfo at the given index.
     */
    public int getReceivedInfo(int index) {
      return receivedInfo_.getInt(index);
    }
    private int receivedInfoMemoizedSerializedSize = -1;

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (round_ != 0) {
        output.writeUInt32(1, round_);
      }
      if (rechargeDay_ != 0) {
        output.writeUInt32(2, rechargeDay_);
      }
      if (freeState_ != 0) {
        output.writeUInt32(3, freeState_);
      }
      if (dailyState_ != 0) {
        output.writeUInt32(4, dailyState_);
      }
      if (getReceivedInfoList().size() > 0) {
        output.writeUInt32NoTag(42);
        output.writeUInt32NoTag(receivedInfoMemoizedSerializedSize);
      }
      for (int i = 0; i < receivedInfo_.size(); i++) {
        output.writeUInt32NoTag(receivedInfo_.getInt(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (round_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, round_);
      }
      if (rechargeDay_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, rechargeDay_);
      }
      if (freeState_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(3, freeState_);
      }
      if (dailyState_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(4, dailyState_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < receivedInfo_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeUInt32SizeNoTag(receivedInfo_.getInt(i));
        }
        size += dataSize;
        if (!getReceivedInfoList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        receivedInfoMemoizedSerializedSize = dataSize;
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_s2c other = (org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_s2c) obj;

      if (getRound()
          != other.getRound()) return false;
      if (getRechargeDay()
          != other.getRechargeDay()) return false;
      if (getFreeState()
          != other.getFreeState()) return false;
      if (getDailyState()
          != other.getDailyState()) return false;
      if (!getReceivedInfoList()
          .equals(other.getReceivedInfoList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ROUND_FIELD_NUMBER;
      hash = (53 * hash) + getRound();
      hash = (37 * hash) + RECHARGE_DAY_FIELD_NUMBER;
      hash = (53 * hash) + getRechargeDay();
      hash = (37 * hash) + FREE_STATE_FIELD_NUMBER;
      hash = (53 * hash) + getFreeState();
      hash = (37 * hash) + DAILY_STATE_FIELD_NUMBER;
      hash = (53 * hash) + getDailyState();
      if (getReceivedInfoCount() > 0) {
        hash = (37 * hash) + RECEIVED_INFO_FIELD_NUMBER;
        hash = (53 * hash) + getReceivedInfoList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.accumulated_recharge_info_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.accumulated_recharge_info_s2c)
        org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.internal_static_org_gof_demo_worldsrv_msg_accumulated_recharge_info_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.internal_static_org_gof_demo_worldsrv_msg_accumulated_recharge_info_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_s2c.class, org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        round_ = 0;
        rechargeDay_ = 0;
        freeState_ = 0;
        dailyState_ = 0;
        receivedInfo_ = emptyIntList();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.internal_static_org_gof_demo_worldsrv_msg_accumulated_recharge_info_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_s2c build() {
        org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_s2c result = new org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.round_ = round_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.rechargeDay_ = rechargeDay_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.freeState_ = freeState_;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.dailyState_ = dailyState_;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          receivedInfo_.makeImmutable();
          result.receivedInfo_ = receivedInfo_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_s2c.getDefaultInstance()) return this;
        if (other.getRound() != 0) {
          setRound(other.getRound());
        }
        if (other.getRechargeDay() != 0) {
          setRechargeDay(other.getRechargeDay());
        }
        if (other.getFreeState() != 0) {
          setFreeState(other.getFreeState());
        }
        if (other.getDailyState() != 0) {
          setDailyState(other.getDailyState());
        }
        if (!other.receivedInfo_.isEmpty()) {
          if (receivedInfo_.isEmpty()) {
            receivedInfo_ = other.receivedInfo_;
            receivedInfo_.makeImmutable();
            bitField0_ |= 0x00000010;
          } else {
            ensureReceivedInfoIsMutable();
            receivedInfo_.addAll(other.receivedInfo_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                round_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                rechargeDay_ = input.readUInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                freeState_ = input.readUInt32();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              case 32: {
                dailyState_ = input.readUInt32();
                bitField0_ |= 0x00000008;
                break;
              } // case 32
              case 40: {
                int v = input.readUInt32();
                ensureReceivedInfoIsMutable();
                receivedInfo_.addInt(v);
                break;
              } // case 40
              case 42: {
                int length = input.readRawVarint32();
                int limit = input.pushLimit(length);
                ensureReceivedInfoIsMutable();
                while (input.getBytesUntilLimit() > 0) {
                  receivedInfo_.addInt(input.readUInt32());
                }
                input.popLimit(limit);
                break;
              } // case 42
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int round_ ;
      /**
       * <pre>
       * 第几轮累充活动
       * </pre>
       *
       * <code>uint32 round = 1;</code>
       * @return The round.
       */
      @java.lang.Override
      public int getRound() {
        return round_;
      }
      /**
       * <pre>
       * 第几轮累充活动
       * </pre>
       *
       * <code>uint32 round = 1;</code>
       * @param value The round to set.
       * @return This builder for chaining.
       */
      public Builder setRound(int value) {

        round_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 第几轮累充活动
       * </pre>
       *
       * <code>uint32 round = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearRound() {
        bitField0_ = (bitField0_ & ~0x00000001);
        round_ = 0;
        onChanged();
        return this;
      }

      private int rechargeDay_ ;
      /**
       * <pre>
       * 当前轮累计充值了几天
       * </pre>
       *
       * <code>uint32 recharge_day = 2;</code>
       * @return The rechargeDay.
       */
      @java.lang.Override
      public int getRechargeDay() {
        return rechargeDay_;
      }
      /**
       * <pre>
       * 当前轮累计充值了几天
       * </pre>
       *
       * <code>uint32 recharge_day = 2;</code>
       * @param value The rechargeDay to set.
       * @return This builder for chaining.
       */
      public Builder setRechargeDay(int value) {

        rechargeDay_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 当前轮累计充值了几天
       * </pre>
       *
       * <code>uint32 recharge_day = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearRechargeDay() {
        bitField0_ = (bitField0_ & ~0x00000002);
        rechargeDay_ = 0;
        onChanged();
        return this;
      }

      private int freeState_ ;
      /**
       * <pre>
       * 免费礼包状态（2：可领取 3：已领取）
       * </pre>
       *
       * <code>uint32 free_state = 3;</code>
       * @return The freeState.
       */
      @java.lang.Override
      public int getFreeState() {
        return freeState_;
      }
      /**
       * <pre>
       * 免费礼包状态（2：可领取 3：已领取）
       * </pre>
       *
       * <code>uint32 free_state = 3;</code>
       * @param value The freeState to set.
       * @return This builder for chaining.
       */
      public Builder setFreeState(int value) {

        freeState_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 免费礼包状态（2：可领取 3：已领取）
       * </pre>
       *
       * <code>uint32 free_state = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearFreeState() {
        bitField0_ = (bitField0_ & ~0x00000004);
        freeState_ = 0;
        onChanged();
        return this;
      }

      private int dailyState_ ;
      /**
       * <pre>
       * 每日充值礼包状态（1：无法领取 2：可领取 3：已领取）
       * </pre>
       *
       * <code>uint32 daily_state = 4;</code>
       * @return The dailyState.
       */
      @java.lang.Override
      public int getDailyState() {
        return dailyState_;
      }
      /**
       * <pre>
       * 每日充值礼包状态（1：无法领取 2：可领取 3：已领取）
       * </pre>
       *
       * <code>uint32 daily_state = 4;</code>
       * @param value The dailyState to set.
       * @return This builder for chaining.
       */
      public Builder setDailyState(int value) {

        dailyState_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 每日充值礼包状态（1：无法领取 2：可领取 3：已领取）
       * </pre>
       *
       * <code>uint32 daily_state = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearDailyState() {
        bitField0_ = (bitField0_ & ~0x00000008);
        dailyState_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.IntList receivedInfo_ = emptyIntList();
      private void ensureReceivedInfoIsMutable() {
        if (!receivedInfo_.isModifiable()) {
          receivedInfo_ = makeMutableCopy(receivedInfo_);
        }
        bitField0_ |= 0x00000010;
      }
      /**
       * <pre>
       * 已领取的累充信息（[5,10,15]）
       * </pre>
       *
       * <code>repeated uint32 received_info = 5;</code>
       * @return A list containing the receivedInfo.
       */
      public java.util.List<java.lang.Integer>
          getReceivedInfoList() {
        receivedInfo_.makeImmutable();
        return receivedInfo_;
      }
      /**
       * <pre>
       * 已领取的累充信息（[5,10,15]）
       * </pre>
       *
       * <code>repeated uint32 received_info = 5;</code>
       * @return The count of receivedInfo.
       */
      public int getReceivedInfoCount() {
        return receivedInfo_.size();
      }
      /**
       * <pre>
       * 已领取的累充信息（[5,10,15]）
       * </pre>
       *
       * <code>repeated uint32 received_info = 5;</code>
       * @param index The index of the element to return.
       * @return The receivedInfo at the given index.
       */
      public int getReceivedInfo(int index) {
        return receivedInfo_.getInt(index);
      }
      /**
       * <pre>
       * 已领取的累充信息（[5,10,15]）
       * </pre>
       *
       * <code>repeated uint32 received_info = 5;</code>
       * @param index The index to set the value at.
       * @param value The receivedInfo to set.
       * @return This builder for chaining.
       */
      public Builder setReceivedInfo(
          int index, int value) {

        ensureReceivedInfoIsMutable();
        receivedInfo_.setInt(index, value);
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 已领取的累充信息（[5,10,15]）
       * </pre>
       *
       * <code>repeated uint32 received_info = 5;</code>
       * @param value The receivedInfo to add.
       * @return This builder for chaining.
       */
      public Builder addReceivedInfo(int value) {

        ensureReceivedInfoIsMutable();
        receivedInfo_.addInt(value);
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 已领取的累充信息（[5,10,15]）
       * </pre>
       *
       * <code>repeated uint32 received_info = 5;</code>
       * @param values The receivedInfo to add.
       * @return This builder for chaining.
       */
      public Builder addAllReceivedInfo(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureReceivedInfoIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, receivedInfo_);
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 已领取的累充信息（[5,10,15]）
       * </pre>
       *
       * <code>repeated uint32 received_info = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearReceivedInfo() {
        receivedInfo_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000010);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.accumulated_recharge_info_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.accumulated_recharge_info_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<accumulated_recharge_info_s2c>
        PARSER = new com.google.protobuf.AbstractParser<accumulated_recharge_info_s2c>() {
      @java.lang.Override
      public accumulated_recharge_info_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<accumulated_recharge_info_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<accumulated_recharge_info_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_info_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface accumulated_recharge_reward_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.accumulated_recharge_reward_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 领奖类型（1：免费礼包 2：每日充值礼包 3：累充礼包）
     * </pre>
     *
     * <code>uint32 type = 1;</code>
     * @return The type.
     */
    int getType();

    /**
     * <pre>
     * 领奖礼包对应的充值天数
     * </pre>
     *
     * <code>uint32 day = 2;</code>
     * @return The day.
     */
    int getDay();
  }
  /**
   * <pre>
   * 领取累充奖励
   * </pre>
   *
   * Protobuf type {@code org.gof.demo.worldsrv.msg.accumulated_recharge_reward_c2s}
   */
  public static final class accumulated_recharge_reward_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.accumulated_recharge_reward_c2s)
      accumulated_recharge_reward_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use accumulated_recharge_reward_c2s.newBuilder() to construct.
    private accumulated_recharge_reward_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private accumulated_recharge_reward_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new accumulated_recharge_reward_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.internal_static_org_gof_demo_worldsrv_msg_accumulated_recharge_reward_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.internal_static_org_gof_demo_worldsrv_msg_accumulated_recharge_reward_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_c2s.class, org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_c2s.Builder.class);
    }

    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_ = 0;
    /**
     * <pre>
     * 领奖类型（1：免费礼包 2：每日充值礼包 3：累充礼包）
     * </pre>
     *
     * <code>uint32 type = 1;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }

    public static final int DAY_FIELD_NUMBER = 2;
    private int day_ = 0;
    /**
     * <pre>
     * 领奖礼包对应的充值天数
     * </pre>
     *
     * <code>uint32 day = 2;</code>
     * @return The day.
     */
    @java.lang.Override
    public int getDay() {
      return day_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (type_ != 0) {
        output.writeUInt32(1, type_);
      }
      if (day_ != 0) {
        output.writeUInt32(2, day_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, type_);
      }
      if (day_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, day_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_c2s other = (org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_c2s) obj;

      if (getType()
          != other.getType()) return false;
      if (getDay()
          != other.getDay()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      hash = (37 * hash) + DAY_FIELD_NUMBER;
      hash = (53 * hash) + getDay();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 领取累充奖励
     * </pre>
     *
     * Protobuf type {@code org.gof.demo.worldsrv.msg.accumulated_recharge_reward_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.accumulated_recharge_reward_c2s)
        org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.internal_static_org_gof_demo_worldsrv_msg_accumulated_recharge_reward_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.internal_static_org_gof_demo_worldsrv_msg_accumulated_recharge_reward_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_c2s.class, org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        type_ = 0;
        day_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.internal_static_org_gof_demo_worldsrv_msg_accumulated_recharge_reward_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_c2s build() {
        org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_c2s result = new org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.type_ = type_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.day_ = day_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_c2s.getDefaultInstance()) return this;
        if (other.getType() != 0) {
          setType(other.getType());
        }
        if (other.getDay() != 0) {
          setDay(other.getDay());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                type_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                day_ = input.readUInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int type_ ;
      /**
       * <pre>
       * 领奖类型（1：免费礼包 2：每日充值礼包 3：累充礼包）
       * </pre>
       *
       * <code>uint32 type = 1;</code>
       * @return The type.
       */
      @java.lang.Override
      public int getType() {
        return type_;
      }
      /**
       * <pre>
       * 领奖类型（1：免费礼包 2：每日充值礼包 3：累充礼包）
       * </pre>
       *
       * <code>uint32 type = 1;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(int value) {

        type_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 领奖类型（1：免费礼包 2：每日充值礼包 3：累充礼包）
       * </pre>
       *
       * <code>uint32 type = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        type_ = 0;
        onChanged();
        return this;
      }

      private int day_ ;
      /**
       * <pre>
       * 领奖礼包对应的充值天数
       * </pre>
       *
       * <code>uint32 day = 2;</code>
       * @return The day.
       */
      @java.lang.Override
      public int getDay() {
        return day_;
      }
      /**
       * <pre>
       * 领奖礼包对应的充值天数
       * </pre>
       *
       * <code>uint32 day = 2;</code>
       * @param value The day to set.
       * @return This builder for chaining.
       */
      public Builder setDay(int value) {

        day_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 领奖礼包对应的充值天数
       * </pre>
       *
       * <code>uint32 day = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearDay() {
        bitField0_ = (bitField0_ & ~0x00000002);
        day_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.accumulated_recharge_reward_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.accumulated_recharge_reward_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<accumulated_recharge_reward_c2s>
        PARSER = new com.google.protobuf.AbstractParser<accumulated_recharge_reward_c2s>() {
      @java.lang.Override
      public accumulated_recharge_reward_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<accumulated_recharge_reward_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<accumulated_recharge_reward_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface accumulated_recharge_reward_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.accumulated_recharge_reward_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 领奖类型（1：免费礼包 2：每日充值礼包 3：累充礼包）
     * </pre>
     *
     * <code>uint32 type = 1;</code>
     * @return The type.
     */
    int getType();

    /**
     * <pre>
     * 已领取的累充信息（[5,10,15]）
     * </pre>
     *
     * <code>repeated uint32 received_info = 2;</code>
     * @return A list containing the receivedInfo.
     */
    java.util.List<java.lang.Integer> getReceivedInfoList();
    /**
     * <pre>
     * 已领取的累充信息（[5,10,15]）
     * </pre>
     *
     * <code>repeated uint32 received_info = 2;</code>
     * @return The count of receivedInfo.
     */
    int getReceivedInfoCount();
    /**
     * <pre>
     * 已领取的累充信息（[5,10,15]）
     * </pre>
     *
     * <code>repeated uint32 received_info = 2;</code>
     * @param index The index of the element to return.
     * @return The receivedInfo at the given index.
     */
    int getReceivedInfo(int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.accumulated_recharge_reward_s2c}
   */
  public static final class accumulated_recharge_reward_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.accumulated_recharge_reward_s2c)
      accumulated_recharge_reward_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use accumulated_recharge_reward_s2c.newBuilder() to construct.
    private accumulated_recharge_reward_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private accumulated_recharge_reward_s2c() {
      receivedInfo_ = emptyIntList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new accumulated_recharge_reward_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.internal_static_org_gof_demo_worldsrv_msg_accumulated_recharge_reward_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.internal_static_org_gof_demo_worldsrv_msg_accumulated_recharge_reward_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_s2c.class, org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_s2c.Builder.class);
    }

    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_ = 0;
    /**
     * <pre>
     * 领奖类型（1：免费礼包 2：每日充值礼包 3：累充礼包）
     * </pre>
     *
     * <code>uint32 type = 1;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }

    public static final int RECEIVED_INFO_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.IntList receivedInfo_ =
        emptyIntList();
    /**
     * <pre>
     * 已领取的累充信息（[5,10,15]）
     * </pre>
     *
     * <code>repeated uint32 received_info = 2;</code>
     * @return A list containing the receivedInfo.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getReceivedInfoList() {
      return receivedInfo_;
    }
    /**
     * <pre>
     * 已领取的累充信息（[5,10,15]）
     * </pre>
     *
     * <code>repeated uint32 received_info = 2;</code>
     * @return The count of receivedInfo.
     */
    public int getReceivedInfoCount() {
      return receivedInfo_.size();
    }
    /**
     * <pre>
     * 已领取的累充信息（[5,10,15]）
     * </pre>
     *
     * <code>repeated uint32 received_info = 2;</code>
     * @param index The index of the element to return.
     * @return The receivedInfo at the given index.
     */
    public int getReceivedInfo(int index) {
      return receivedInfo_.getInt(index);
    }
    private int receivedInfoMemoizedSerializedSize = -1;

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (type_ != 0) {
        output.writeUInt32(1, type_);
      }
      if (getReceivedInfoList().size() > 0) {
        output.writeUInt32NoTag(18);
        output.writeUInt32NoTag(receivedInfoMemoizedSerializedSize);
      }
      for (int i = 0; i < receivedInfo_.size(); i++) {
        output.writeUInt32NoTag(receivedInfo_.getInt(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, type_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < receivedInfo_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeUInt32SizeNoTag(receivedInfo_.getInt(i));
        }
        size += dataSize;
        if (!getReceivedInfoList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        receivedInfoMemoizedSerializedSize = dataSize;
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_s2c other = (org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_s2c) obj;

      if (getType()
          != other.getType()) return false;
      if (!getReceivedInfoList()
          .equals(other.getReceivedInfoList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      if (getReceivedInfoCount() > 0) {
        hash = (37 * hash) + RECEIVED_INFO_FIELD_NUMBER;
        hash = (53 * hash) + getReceivedInfoList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.accumulated_recharge_reward_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.accumulated_recharge_reward_s2c)
        org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.internal_static_org_gof_demo_worldsrv_msg_accumulated_recharge_reward_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.internal_static_org_gof_demo_worldsrv_msg_accumulated_recharge_reward_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_s2c.class, org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        type_ = 0;
        receivedInfo_ = emptyIntList();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.internal_static_org_gof_demo_worldsrv_msg_accumulated_recharge_reward_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_s2c build() {
        org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_s2c result = new org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.type_ = type_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          receivedInfo_.makeImmutable();
          result.receivedInfo_ = receivedInfo_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_s2c.getDefaultInstance()) return this;
        if (other.getType() != 0) {
          setType(other.getType());
        }
        if (!other.receivedInfo_.isEmpty()) {
          if (receivedInfo_.isEmpty()) {
            receivedInfo_ = other.receivedInfo_;
            receivedInfo_.makeImmutable();
            bitField0_ |= 0x00000002;
          } else {
            ensureReceivedInfoIsMutable();
            receivedInfo_.addAll(other.receivedInfo_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                type_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                int v = input.readUInt32();
                ensureReceivedInfoIsMutable();
                receivedInfo_.addInt(v);
                break;
              } // case 16
              case 18: {
                int length = input.readRawVarint32();
                int limit = input.pushLimit(length);
                ensureReceivedInfoIsMutable();
                while (input.getBytesUntilLimit() > 0) {
                  receivedInfo_.addInt(input.readUInt32());
                }
                input.popLimit(limit);
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int type_ ;
      /**
       * <pre>
       * 领奖类型（1：免费礼包 2：每日充值礼包 3：累充礼包）
       * </pre>
       *
       * <code>uint32 type = 1;</code>
       * @return The type.
       */
      @java.lang.Override
      public int getType() {
        return type_;
      }
      /**
       * <pre>
       * 领奖类型（1：免费礼包 2：每日充值礼包 3：累充礼包）
       * </pre>
       *
       * <code>uint32 type = 1;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(int value) {

        type_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 领奖类型（1：免费礼包 2：每日充值礼包 3：累充礼包）
       * </pre>
       *
       * <code>uint32 type = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        type_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.IntList receivedInfo_ = emptyIntList();
      private void ensureReceivedInfoIsMutable() {
        if (!receivedInfo_.isModifiable()) {
          receivedInfo_ = makeMutableCopy(receivedInfo_);
        }
        bitField0_ |= 0x00000002;
      }
      /**
       * <pre>
       * 已领取的累充信息（[5,10,15]）
       * </pre>
       *
       * <code>repeated uint32 received_info = 2;</code>
       * @return A list containing the receivedInfo.
       */
      public java.util.List<java.lang.Integer>
          getReceivedInfoList() {
        receivedInfo_.makeImmutable();
        return receivedInfo_;
      }
      /**
       * <pre>
       * 已领取的累充信息（[5,10,15]）
       * </pre>
       *
       * <code>repeated uint32 received_info = 2;</code>
       * @return The count of receivedInfo.
       */
      public int getReceivedInfoCount() {
        return receivedInfo_.size();
      }
      /**
       * <pre>
       * 已领取的累充信息（[5,10,15]）
       * </pre>
       *
       * <code>repeated uint32 received_info = 2;</code>
       * @param index The index of the element to return.
       * @return The receivedInfo at the given index.
       */
      public int getReceivedInfo(int index) {
        return receivedInfo_.getInt(index);
      }
      /**
       * <pre>
       * 已领取的累充信息（[5,10,15]）
       * </pre>
       *
       * <code>repeated uint32 received_info = 2;</code>
       * @param index The index to set the value at.
       * @param value The receivedInfo to set.
       * @return This builder for chaining.
       */
      public Builder setReceivedInfo(
          int index, int value) {

        ensureReceivedInfoIsMutable();
        receivedInfo_.setInt(index, value);
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 已领取的累充信息（[5,10,15]）
       * </pre>
       *
       * <code>repeated uint32 received_info = 2;</code>
       * @param value The receivedInfo to add.
       * @return This builder for chaining.
       */
      public Builder addReceivedInfo(int value) {

        ensureReceivedInfoIsMutable();
        receivedInfo_.addInt(value);
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 已领取的累充信息（[5,10,15]）
       * </pre>
       *
       * <code>repeated uint32 received_info = 2;</code>
       * @param values The receivedInfo to add.
       * @return This builder for chaining.
       */
      public Builder addAllReceivedInfo(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureReceivedInfoIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, receivedInfo_);
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 已领取的累充信息（[5,10,15]）
       * </pre>
       *
       * <code>repeated uint32 received_info = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearReceivedInfo() {
        receivedInfo_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.accumulated_recharge_reward_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.accumulated_recharge_reward_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<accumulated_recharge_reward_s2c>
        PARSER = new com.google.protobuf.AbstractParser<accumulated_recharge_reward_s2c>() {
      @java.lang.Override
      public accumulated_recharge_reward_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<accumulated_recharge_reward_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<accumulated_recharge_reward_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge.accumulated_recharge_reward_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_accumulated_recharge_info_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_accumulated_recharge_info_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_accumulated_recharge_info_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_accumulated_recharge_info_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_accumulated_recharge_reward_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_accumulated_recharge_reward_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_accumulated_recharge_reward_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_accumulated_recharge_reward_s2c_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\035msg.accumulatedRecharge.proto\022\031org.gof" +
      ".demo.worldsrv.msg\032\roptions.proto\032\014defin" +
      "e.proto\"&\n\035accumulated_recharge_info_c2s" +
      ":\005\210\303\032\221N\"\213\001\n\035accumulated_recharge_info_s2" +
      "c\022\r\n\005round\030\001 \001(\r\022\024\n\014recharge_day\030\002 \001(\r\022\022" +
      "\n\nfree_state\030\003 \001(\r\022\023\n\013daily_state\030\004 \001(\r\022" +
      "\025\n\rreceived_info\030\005 \003(\r:\005\210\303\032\221N\"C\n\037accumul" +
      "ated_recharge_reward_c2s\022\014\n\004type\030\001 \001(\r\022\013" +
      "\n\003day\030\002 \001(\r:\005\210\303\032\222N\"M\n\037accumulated_rechar" +
      "ge_reward_s2c\022\014\n\004type\030\001 \001(\r\022\025\n\rreceived_" +
      "info\030\002 \003(\r:\005\210\303\032\222Nb\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          org.gof.demo.worldsrv.msg.Options.getDescriptor(),
          org.gof.demo.worldsrv.msg.Define.getDescriptor(),
        });
    internal_static_org_gof_demo_worldsrv_msg_accumulated_recharge_info_c2s_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_org_gof_demo_worldsrv_msg_accumulated_recharge_info_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_accumulated_recharge_info_c2s_descriptor,
        new java.lang.String[] { });
    internal_static_org_gof_demo_worldsrv_msg_accumulated_recharge_info_s2c_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_org_gof_demo_worldsrv_msg_accumulated_recharge_info_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_accumulated_recharge_info_s2c_descriptor,
        new java.lang.String[] { "Round", "RechargeDay", "FreeState", "DailyState", "ReceivedInfo", });
    internal_static_org_gof_demo_worldsrv_msg_accumulated_recharge_reward_c2s_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_org_gof_demo_worldsrv_msg_accumulated_recharge_reward_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_accumulated_recharge_reward_c2s_descriptor,
        new java.lang.String[] { "Type", "Day", });
    internal_static_org_gof_demo_worldsrv_msg_accumulated_recharge_reward_s2c_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_org_gof_demo_worldsrv_msg_accumulated_recharge_reward_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_accumulated_recharge_reward_s2c_descriptor,
        new java.lang.String[] { "Type", "ReceivedInfo", });
    com.google.protobuf.ExtensionRegistry registry =
        com.google.protobuf.ExtensionRegistry.newInstance();
    registry.add(org.gof.demo.worldsrv.msg.Options.msgid);
    com.google.protobuf.Descriptors.FileDescriptor
        .internalUpdateFileDescriptor(descriptor, registry);
    org.gof.demo.worldsrv.msg.Options.getDescriptor();
    org.gof.demo.worldsrv.msg.Define.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
