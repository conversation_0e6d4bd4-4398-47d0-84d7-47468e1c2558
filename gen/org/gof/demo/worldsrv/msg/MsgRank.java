// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: msg.rank.proto

// Protobuf Java Version: 3.25.3
package org.gof.demo.worldsrv.msg;

public final class MsgRank {
  private MsgRank() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface rank_data_list_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.rank_data_list_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_request request = 1;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_rank_request> 
        getRequestList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_request request = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_rank_request getRequest(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_request request = 1;</code>
     */
    int getRequestCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_request request = 1;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_rank_requestOrBuilder> 
        getRequestOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_request request = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_rank_requestOrBuilder getRequestOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.rank_data_list_c2s}
   */
  public static final class rank_data_list_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.rank_data_list_c2s)
      rank_data_list_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use rank_data_list_c2s.newBuilder() to construct.
    private rank_data_list_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private rank_data_list_c2s() {
      request_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new rank_data_list_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgRank.internal_static_org_gof_demo_worldsrv_msg_rank_data_list_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgRank.internal_static_org_gof_demo_worldsrv_msg_rank_data_list_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_c2s.class, org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_c2s.Builder.class);
    }

    public static final int REQUEST_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_rank_request> request_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_request request = 1;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_rank_request> getRequestList() {
      return request_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_request request = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_rank_requestOrBuilder> 
        getRequestOrBuilderList() {
      return request_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_request request = 1;</code>
     */
    @java.lang.Override
    public int getRequestCount() {
      return request_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_request request = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_rank_request getRequest(int index) {
      return request_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_request request = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_rank_requestOrBuilder getRequestOrBuilder(
        int index) {
      return request_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < request_.size(); i++) {
        output.writeMessage(1, request_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < request_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, request_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_c2s other = (org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_c2s) obj;

      if (!getRequestList()
          .equals(other.getRequestList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getRequestCount() > 0) {
        hash = (37 * hash) + REQUEST_FIELD_NUMBER;
        hash = (53 * hash) + getRequestList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.rank_data_list_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.rank_data_list_c2s)
        org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgRank.internal_static_org_gof_demo_worldsrv_msg_rank_data_list_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgRank.internal_static_org_gof_demo_worldsrv_msg_rank_data_list_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_c2s.class, org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        if (requestBuilder_ == null) {
          request_ = java.util.Collections.emptyList();
        } else {
          request_ = null;
          requestBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgRank.internal_static_org_gof_demo_worldsrv_msg_rank_data_list_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_c2s build() {
        org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_c2s result = new org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_c2s(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_c2s result) {
        if (requestBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            request_ = java.util.Collections.unmodifiableList(request_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.request_ = request_;
        } else {
          result.request_ = requestBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_c2s result) {
        int from_bitField0_ = bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_c2s.getDefaultInstance()) return this;
        if (requestBuilder_ == null) {
          if (!other.request_.isEmpty()) {
            if (request_.isEmpty()) {
              request_ = other.request_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureRequestIsMutable();
              request_.addAll(other.request_);
            }
            onChanged();
          }
        } else {
          if (!other.request_.isEmpty()) {
            if (requestBuilder_.isEmpty()) {
              requestBuilder_.dispose();
              requestBuilder_ = null;
              request_ = other.request_;
              bitField0_ = (bitField0_ & ~0x00000001);
              requestBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getRequestFieldBuilder() : null;
            } else {
              requestBuilder_.addAllMessages(other.request_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                org.gof.demo.worldsrv.msg.Define.p_rank_request m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_rank_request.parser(),
                        extensionRegistry);
                if (requestBuilder_ == null) {
                  ensureRequestIsMutable();
                  request_.add(m);
                } else {
                  requestBuilder_.addMessage(m);
                }
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_rank_request> request_ =
        java.util.Collections.emptyList();
      private void ensureRequestIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          request_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_rank_request>(request_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_rank_request, org.gof.demo.worldsrv.msg.Define.p_rank_request.Builder, org.gof.demo.worldsrv.msg.Define.p_rank_requestOrBuilder> requestBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_request request = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_rank_request> getRequestList() {
        if (requestBuilder_ == null) {
          return java.util.Collections.unmodifiableList(request_);
        } else {
          return requestBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_request request = 1;</code>
       */
      public int getRequestCount() {
        if (requestBuilder_ == null) {
          return request_.size();
        } else {
          return requestBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_request request = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_rank_request getRequest(int index) {
        if (requestBuilder_ == null) {
          return request_.get(index);
        } else {
          return requestBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_request request = 1;</code>
       */
      public Builder setRequest(
          int index, org.gof.demo.worldsrv.msg.Define.p_rank_request value) {
        if (requestBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRequestIsMutable();
          request_.set(index, value);
          onChanged();
        } else {
          requestBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_request request = 1;</code>
       */
      public Builder setRequest(
          int index, org.gof.demo.worldsrv.msg.Define.p_rank_request.Builder builderForValue) {
        if (requestBuilder_ == null) {
          ensureRequestIsMutable();
          request_.set(index, builderForValue.build());
          onChanged();
        } else {
          requestBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_request request = 1;</code>
       */
      public Builder addRequest(org.gof.demo.worldsrv.msg.Define.p_rank_request value) {
        if (requestBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRequestIsMutable();
          request_.add(value);
          onChanged();
        } else {
          requestBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_request request = 1;</code>
       */
      public Builder addRequest(
          int index, org.gof.demo.worldsrv.msg.Define.p_rank_request value) {
        if (requestBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRequestIsMutable();
          request_.add(index, value);
          onChanged();
        } else {
          requestBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_request request = 1;</code>
       */
      public Builder addRequest(
          org.gof.demo.worldsrv.msg.Define.p_rank_request.Builder builderForValue) {
        if (requestBuilder_ == null) {
          ensureRequestIsMutable();
          request_.add(builderForValue.build());
          onChanged();
        } else {
          requestBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_request request = 1;</code>
       */
      public Builder addRequest(
          int index, org.gof.demo.worldsrv.msg.Define.p_rank_request.Builder builderForValue) {
        if (requestBuilder_ == null) {
          ensureRequestIsMutable();
          request_.add(index, builderForValue.build());
          onChanged();
        } else {
          requestBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_request request = 1;</code>
       */
      public Builder addAllRequest(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_rank_request> values) {
        if (requestBuilder_ == null) {
          ensureRequestIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, request_);
          onChanged();
        } else {
          requestBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_request request = 1;</code>
       */
      public Builder clearRequest() {
        if (requestBuilder_ == null) {
          request_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          requestBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_request request = 1;</code>
       */
      public Builder removeRequest(int index) {
        if (requestBuilder_ == null) {
          ensureRequestIsMutable();
          request_.remove(index);
          onChanged();
        } else {
          requestBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_request request = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_rank_request.Builder getRequestBuilder(
          int index) {
        return getRequestFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_request request = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_rank_requestOrBuilder getRequestOrBuilder(
          int index) {
        if (requestBuilder_ == null) {
          return request_.get(index);  } else {
          return requestBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_request request = 1;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_rank_requestOrBuilder> 
           getRequestOrBuilderList() {
        if (requestBuilder_ != null) {
          return requestBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(request_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_request request = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_rank_request.Builder addRequestBuilder() {
        return getRequestFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_rank_request.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_request request = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_rank_request.Builder addRequestBuilder(
          int index) {
        return getRequestFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_rank_request.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_request request = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_rank_request.Builder> 
           getRequestBuilderList() {
        return getRequestFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_rank_request, org.gof.demo.worldsrv.msg.Define.p_rank_request.Builder, org.gof.demo.worldsrv.msg.Define.p_rank_requestOrBuilder> 
          getRequestFieldBuilder() {
        if (requestBuilder_ == null) {
          requestBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_rank_request, org.gof.demo.worldsrv.msg.Define.p_rank_request.Builder, org.gof.demo.worldsrv.msg.Define.p_rank_requestOrBuilder>(
                  request_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          request_ = null;
        }
        return requestBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.rank_data_list_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.rank_data_list_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<rank_data_list_c2s>
        PARSER = new com.google.protobuf.AbstractParser<rank_data_list_c2s>() {
      @java.lang.Override
      public rank_data_list_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<rank_data_list_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<rank_data_list_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface rank_data_list_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.rank_data_list_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_data rank_data_list = 1;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_rank_data> 
        getRankDataListList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_data rank_data_list = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_rank_data getRankDataList(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_data rank_data_list = 1;</code>
     */
    int getRankDataListCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_data rank_data_list = 1;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_rank_dataOrBuilder> 
        getRankDataListOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_data rank_data_list = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_rank_dataOrBuilder getRankDataListOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.rank_data_list_s2c}
   */
  public static final class rank_data_list_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.rank_data_list_s2c)
      rank_data_list_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use rank_data_list_s2c.newBuilder() to construct.
    private rank_data_list_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private rank_data_list_s2c() {
      rankDataList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new rank_data_list_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgRank.internal_static_org_gof_demo_worldsrv_msg_rank_data_list_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgRank.internal_static_org_gof_demo_worldsrv_msg_rank_data_list_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_s2c.class, org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_s2c.Builder.class);
    }

    public static final int RANK_DATA_LIST_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_rank_data> rankDataList_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_data rank_data_list = 1;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_rank_data> getRankDataListList() {
      return rankDataList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_data rank_data_list = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_rank_dataOrBuilder> 
        getRankDataListOrBuilderList() {
      return rankDataList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_data rank_data_list = 1;</code>
     */
    @java.lang.Override
    public int getRankDataListCount() {
      return rankDataList_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_data rank_data_list = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_rank_data getRankDataList(int index) {
      return rankDataList_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_data rank_data_list = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_rank_dataOrBuilder getRankDataListOrBuilder(
        int index) {
      return rankDataList_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < rankDataList_.size(); i++) {
        output.writeMessage(1, rankDataList_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < rankDataList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, rankDataList_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_s2c other = (org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_s2c) obj;

      if (!getRankDataListList()
          .equals(other.getRankDataListList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getRankDataListCount() > 0) {
        hash = (37 * hash) + RANK_DATA_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getRankDataListList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.rank_data_list_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.rank_data_list_s2c)
        org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgRank.internal_static_org_gof_demo_worldsrv_msg_rank_data_list_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgRank.internal_static_org_gof_demo_worldsrv_msg_rank_data_list_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_s2c.class, org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        if (rankDataListBuilder_ == null) {
          rankDataList_ = java.util.Collections.emptyList();
        } else {
          rankDataList_ = null;
          rankDataListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgRank.internal_static_org_gof_demo_worldsrv_msg_rank_data_list_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_s2c build() {
        org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_s2c result = new org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_s2c(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_s2c result) {
        if (rankDataListBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            rankDataList_ = java.util.Collections.unmodifiableList(rankDataList_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.rankDataList_ = rankDataList_;
        } else {
          result.rankDataList_ = rankDataListBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_s2c result) {
        int from_bitField0_ = bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_s2c.getDefaultInstance()) return this;
        if (rankDataListBuilder_ == null) {
          if (!other.rankDataList_.isEmpty()) {
            if (rankDataList_.isEmpty()) {
              rankDataList_ = other.rankDataList_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureRankDataListIsMutable();
              rankDataList_.addAll(other.rankDataList_);
            }
            onChanged();
          }
        } else {
          if (!other.rankDataList_.isEmpty()) {
            if (rankDataListBuilder_.isEmpty()) {
              rankDataListBuilder_.dispose();
              rankDataListBuilder_ = null;
              rankDataList_ = other.rankDataList_;
              bitField0_ = (bitField0_ & ~0x00000001);
              rankDataListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getRankDataListFieldBuilder() : null;
            } else {
              rankDataListBuilder_.addAllMessages(other.rankDataList_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                org.gof.demo.worldsrv.msg.Define.p_rank_data m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_rank_data.parser(),
                        extensionRegistry);
                if (rankDataListBuilder_ == null) {
                  ensureRankDataListIsMutable();
                  rankDataList_.add(m);
                } else {
                  rankDataListBuilder_.addMessage(m);
                }
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_rank_data> rankDataList_ =
        java.util.Collections.emptyList();
      private void ensureRankDataListIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          rankDataList_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_rank_data>(rankDataList_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_rank_data, org.gof.demo.worldsrv.msg.Define.p_rank_data.Builder, org.gof.demo.worldsrv.msg.Define.p_rank_dataOrBuilder> rankDataListBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_data rank_data_list = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_rank_data> getRankDataListList() {
        if (rankDataListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(rankDataList_);
        } else {
          return rankDataListBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_data rank_data_list = 1;</code>
       */
      public int getRankDataListCount() {
        if (rankDataListBuilder_ == null) {
          return rankDataList_.size();
        } else {
          return rankDataListBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_data rank_data_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_rank_data getRankDataList(int index) {
        if (rankDataListBuilder_ == null) {
          return rankDataList_.get(index);
        } else {
          return rankDataListBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_data rank_data_list = 1;</code>
       */
      public Builder setRankDataList(
          int index, org.gof.demo.worldsrv.msg.Define.p_rank_data value) {
        if (rankDataListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRankDataListIsMutable();
          rankDataList_.set(index, value);
          onChanged();
        } else {
          rankDataListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_data rank_data_list = 1;</code>
       */
      public Builder setRankDataList(
          int index, org.gof.demo.worldsrv.msg.Define.p_rank_data.Builder builderForValue) {
        if (rankDataListBuilder_ == null) {
          ensureRankDataListIsMutable();
          rankDataList_.set(index, builderForValue.build());
          onChanged();
        } else {
          rankDataListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_data rank_data_list = 1;</code>
       */
      public Builder addRankDataList(org.gof.demo.worldsrv.msg.Define.p_rank_data value) {
        if (rankDataListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRankDataListIsMutable();
          rankDataList_.add(value);
          onChanged();
        } else {
          rankDataListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_data rank_data_list = 1;</code>
       */
      public Builder addRankDataList(
          int index, org.gof.demo.worldsrv.msg.Define.p_rank_data value) {
        if (rankDataListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRankDataListIsMutable();
          rankDataList_.add(index, value);
          onChanged();
        } else {
          rankDataListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_data rank_data_list = 1;</code>
       */
      public Builder addRankDataList(
          org.gof.demo.worldsrv.msg.Define.p_rank_data.Builder builderForValue) {
        if (rankDataListBuilder_ == null) {
          ensureRankDataListIsMutable();
          rankDataList_.add(builderForValue.build());
          onChanged();
        } else {
          rankDataListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_data rank_data_list = 1;</code>
       */
      public Builder addRankDataList(
          int index, org.gof.demo.worldsrv.msg.Define.p_rank_data.Builder builderForValue) {
        if (rankDataListBuilder_ == null) {
          ensureRankDataListIsMutable();
          rankDataList_.add(index, builderForValue.build());
          onChanged();
        } else {
          rankDataListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_data rank_data_list = 1;</code>
       */
      public Builder addAllRankDataList(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_rank_data> values) {
        if (rankDataListBuilder_ == null) {
          ensureRankDataListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, rankDataList_);
          onChanged();
        } else {
          rankDataListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_data rank_data_list = 1;</code>
       */
      public Builder clearRankDataList() {
        if (rankDataListBuilder_ == null) {
          rankDataList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          rankDataListBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_data rank_data_list = 1;</code>
       */
      public Builder removeRankDataList(int index) {
        if (rankDataListBuilder_ == null) {
          ensureRankDataListIsMutable();
          rankDataList_.remove(index);
          onChanged();
        } else {
          rankDataListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_data rank_data_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_rank_data.Builder getRankDataListBuilder(
          int index) {
        return getRankDataListFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_data rank_data_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_rank_dataOrBuilder getRankDataListOrBuilder(
          int index) {
        if (rankDataListBuilder_ == null) {
          return rankDataList_.get(index);  } else {
          return rankDataListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_data rank_data_list = 1;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_rank_dataOrBuilder> 
           getRankDataListOrBuilderList() {
        if (rankDataListBuilder_ != null) {
          return rankDataListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(rankDataList_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_data rank_data_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_rank_data.Builder addRankDataListBuilder() {
        return getRankDataListFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_rank_data.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_data rank_data_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_rank_data.Builder addRankDataListBuilder(
          int index) {
        return getRankDataListFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_rank_data.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_rank_data rank_data_list = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_rank_data.Builder> 
           getRankDataListBuilderList() {
        return getRankDataListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_rank_data, org.gof.demo.worldsrv.msg.Define.p_rank_data.Builder, org.gof.demo.worldsrv.msg.Define.p_rank_dataOrBuilder> 
          getRankDataListFieldBuilder() {
        if (rankDataListBuilder_ == null) {
          rankDataListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_rank_data, org.gof.demo.worldsrv.msg.Define.p_rank_data.Builder, org.gof.demo.worldsrv.msg.Define.p_rank_dataOrBuilder>(
                  rankDataList_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          rankDataList_ = null;
        }
        return rankDataListBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.rank_data_list_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.rank_data_list_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<rank_data_list_s2c>
        PARSER = new com.google.protobuf.AbstractParser<rank_data_list_s2c>() {
      @java.lang.Override
      public rank_data_list_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<rank_data_list_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<rank_data_list_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgRank.rank_data_list_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface rank_serv_list_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.rank_serv_list_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 type = 1;</code>
     * @return The type.
     */
    int getType();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.rank_serv_list_c2s}
   */
  public static final class rank_serv_list_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.rank_serv_list_c2s)
      rank_serv_list_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use rank_serv_list_c2s.newBuilder() to construct.
    private rank_serv_list_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private rank_serv_list_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new rank_serv_list_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgRank.internal_static_org_gof_demo_worldsrv_msg_rank_serv_list_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgRank.internal_static_org_gof_demo_worldsrv_msg_rank_serv_list_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_c2s.class, org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_c2s.Builder.class);
    }

    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_ = 0;
    /**
     * <code>int32 type = 1;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (type_ != 0) {
        output.writeInt32(1, type_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, type_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_c2s other = (org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_c2s) obj;

      if (getType()
          != other.getType()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.rank_serv_list_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.rank_serv_list_c2s)
        org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgRank.internal_static_org_gof_demo_worldsrv_msg_rank_serv_list_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgRank.internal_static_org_gof_demo_worldsrv_msg_rank_serv_list_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_c2s.class, org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        type_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgRank.internal_static_org_gof_demo_worldsrv_msg_rank_serv_list_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_c2s build() {
        org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_c2s result = new org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.type_ = type_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_c2s.getDefaultInstance()) return this;
        if (other.getType() != 0) {
          setType(other.getType());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                type_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int type_ ;
      /**
       * <code>int32 type = 1;</code>
       * @return The type.
       */
      @java.lang.Override
      public int getType() {
        return type_;
      }
      /**
       * <code>int32 type = 1;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(int value) {

        type_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>int32 type = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        type_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.rank_serv_list_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.rank_serv_list_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<rank_serv_list_c2s>
        PARSER = new com.google.protobuf.AbstractParser<rank_serv_list_c2s>() {
      @java.lang.Override
      public rank_serv_list_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<rank_serv_list_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<rank_serv_list_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface rank_serv_list_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.rank_serv_list_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated uint32 serv_list = 1 [packed = false];</code>
     * @return A list containing the servList.
     */
    java.util.List<java.lang.Integer> getServListList();
    /**
     * <code>repeated uint32 serv_list = 1 [packed = false];</code>
     * @return The count of servList.
     */
    int getServListCount();
    /**
     * <code>repeated uint32 serv_list = 1 [packed = false];</code>
     * @param index The index of the element to return.
     * @return The servList at the given index.
     */
    int getServList(int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.rank_serv_list_s2c}
   */
  public static final class rank_serv_list_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.rank_serv_list_s2c)
      rank_serv_list_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use rank_serv_list_s2c.newBuilder() to construct.
    private rank_serv_list_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private rank_serv_list_s2c() {
      servList_ = emptyIntList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new rank_serv_list_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgRank.internal_static_org_gof_demo_worldsrv_msg_rank_serv_list_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgRank.internal_static_org_gof_demo_worldsrv_msg_rank_serv_list_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_s2c.class, org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_s2c.Builder.class);
    }

    public static final int SERV_LIST_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.IntList servList_ =
        emptyIntList();
    /**
     * <code>repeated uint32 serv_list = 1 [packed = false];</code>
     * @return A list containing the servList.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getServListList() {
      return servList_;
    }
    /**
     * <code>repeated uint32 serv_list = 1 [packed = false];</code>
     * @return The count of servList.
     */
    public int getServListCount() {
      return servList_.size();
    }
    /**
     * <code>repeated uint32 serv_list = 1 [packed = false];</code>
     * @param index The index of the element to return.
     * @return The servList at the given index.
     */
    public int getServList(int index) {
      return servList_.getInt(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < servList_.size(); i++) {
        output.writeUInt32(1, servList_.getInt(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        for (int i = 0; i < servList_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeUInt32SizeNoTag(servList_.getInt(i));
        }
        size += dataSize;
        size += 1 * getServListList().size();
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_s2c other = (org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_s2c) obj;

      if (!getServListList()
          .equals(other.getServListList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getServListCount() > 0) {
        hash = (37 * hash) + SERV_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getServListList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.rank_serv_list_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.rank_serv_list_s2c)
        org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgRank.internal_static_org_gof_demo_worldsrv_msg_rank_serv_list_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgRank.internal_static_org_gof_demo_worldsrv_msg_rank_serv_list_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_s2c.class, org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        servList_ = emptyIntList();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgRank.internal_static_org_gof_demo_worldsrv_msg_rank_serv_list_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_s2c build() {
        org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_s2c result = new org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          servList_.makeImmutable();
          result.servList_ = servList_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_s2c.getDefaultInstance()) return this;
        if (!other.servList_.isEmpty()) {
          if (servList_.isEmpty()) {
            servList_ = other.servList_;
            servList_.makeImmutable();
            bitField0_ |= 0x00000001;
          } else {
            ensureServListIsMutable();
            servList_.addAll(other.servList_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                int v = input.readUInt32();
                ensureServListIsMutable();
                servList_.addInt(v);
                break;
              } // case 8
              case 10: {
                int length = input.readRawVarint32();
                int limit = input.pushLimit(length);
                ensureServListIsMutable();
                while (input.getBytesUntilLimit() > 0) {
                  servList_.addInt(input.readUInt32());
                }
                input.popLimit(limit);
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.Internal.IntList servList_ = emptyIntList();
      private void ensureServListIsMutable() {
        if (!servList_.isModifiable()) {
          servList_ = makeMutableCopy(servList_);
        }
        bitField0_ |= 0x00000001;
      }
      /**
       * <code>repeated uint32 serv_list = 1 [packed = false];</code>
       * @return A list containing the servList.
       */
      public java.util.List<java.lang.Integer>
          getServListList() {
        servList_.makeImmutable();
        return servList_;
      }
      /**
       * <code>repeated uint32 serv_list = 1 [packed = false];</code>
       * @return The count of servList.
       */
      public int getServListCount() {
        return servList_.size();
      }
      /**
       * <code>repeated uint32 serv_list = 1 [packed = false];</code>
       * @param index The index of the element to return.
       * @return The servList at the given index.
       */
      public int getServList(int index) {
        return servList_.getInt(index);
      }
      /**
       * <code>repeated uint32 serv_list = 1 [packed = false];</code>
       * @param index The index to set the value at.
       * @param value The servList to set.
       * @return This builder for chaining.
       */
      public Builder setServList(
          int index, int value) {

        ensureServListIsMutable();
        servList_.setInt(index, value);
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint32 serv_list = 1 [packed = false];</code>
       * @param value The servList to add.
       * @return This builder for chaining.
       */
      public Builder addServList(int value) {

        ensureServListIsMutable();
        servList_.addInt(value);
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint32 serv_list = 1 [packed = false];</code>
       * @param values The servList to add.
       * @return This builder for chaining.
       */
      public Builder addAllServList(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureServListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, servList_);
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint32 serv_list = 1 [packed = false];</code>
       * @return This builder for chaining.
       */
      public Builder clearServList() {
        servList_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.rank_serv_list_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.rank_serv_list_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<rank_serv_list_s2c>
        PARSER = new com.google.protobuf.AbstractParser<rank_serv_list_s2c>() {
      @java.lang.Override
      public rank_serv_list_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<rank_serv_list_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<rank_serv_list_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgRank.rank_serv_list_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface rank_cross_status_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.rank_cross_status_c2s)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.rank_cross_status_c2s}
   */
  public static final class rank_cross_status_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.rank_cross_status_c2s)
      rank_cross_status_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use rank_cross_status_c2s.newBuilder() to construct.
    private rank_cross_status_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private rank_cross_status_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new rank_cross_status_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgRank.internal_static_org_gof_demo_worldsrv_msg_rank_cross_status_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgRank.internal_static_org_gof_demo_worldsrv_msg_rank_cross_status_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_c2s.class, org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_c2s.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_c2s other = (org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_c2s) obj;

      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.rank_cross_status_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.rank_cross_status_c2s)
        org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgRank.internal_static_org_gof_demo_worldsrv_msg_rank_cross_status_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgRank.internal_static_org_gof_demo_worldsrv_msg_rank_cross_status_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_c2s.class, org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgRank.internal_static_org_gof_demo_worldsrv_msg_rank_cross_status_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_c2s build() {
        org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_c2s result = new org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_c2s(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_c2s.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.rank_cross_status_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.rank_cross_status_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<rank_cross_status_c2s>
        PARSER = new com.google.protobuf.AbstractParser<rank_cross_status_c2s>() {
      @java.lang.Override
      public rank_cross_status_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<rank_cross_status_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<rank_cross_status_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface rank_cross_status_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.rank_cross_status_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 status = 1;</code>
     * @return The status.
     */
    int getStatus();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.rank_cross_status_s2c}
   */
  public static final class rank_cross_status_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.rank_cross_status_s2c)
      rank_cross_status_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use rank_cross_status_s2c.newBuilder() to construct.
    private rank_cross_status_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private rank_cross_status_s2c() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new rank_cross_status_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgRank.internal_static_org_gof_demo_worldsrv_msg_rank_cross_status_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgRank.internal_static_org_gof_demo_worldsrv_msg_rank_cross_status_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_s2c.class, org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_s2c.Builder.class);
    }

    public static final int STATUS_FIELD_NUMBER = 1;
    private int status_ = 0;
    /**
     * <code>uint32 status = 1;</code>
     * @return The status.
     */
    @java.lang.Override
    public int getStatus() {
      return status_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (status_ != 0) {
        output.writeUInt32(1, status_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (status_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, status_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_s2c other = (org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_s2c) obj;

      if (getStatus()
          != other.getStatus()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + STATUS_FIELD_NUMBER;
      hash = (53 * hash) + getStatus();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.rank_cross_status_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.rank_cross_status_s2c)
        org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgRank.internal_static_org_gof_demo_worldsrv_msg_rank_cross_status_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgRank.internal_static_org_gof_demo_worldsrv_msg_rank_cross_status_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_s2c.class, org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        status_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgRank.internal_static_org_gof_demo_worldsrv_msg_rank_cross_status_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_s2c build() {
        org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_s2c result = new org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.status_ = status_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_s2c.getDefaultInstance()) return this;
        if (other.getStatus() != 0) {
          setStatus(other.getStatus());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                status_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int status_ ;
      /**
       * <code>uint32 status = 1;</code>
       * @return The status.
       */
      @java.lang.Override
      public int getStatus() {
        return status_;
      }
      /**
       * <code>uint32 status = 1;</code>
       * @param value The status to set.
       * @return This builder for chaining.
       */
      public Builder setStatus(int value) {

        status_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 status = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearStatus() {
        bitField0_ = (bitField0_ & ~0x00000001);
        status_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.rank_cross_status_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.rank_cross_status_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<rank_cross_status_s2c>
        PARSER = new com.google.protobuf.AbstractParser<rank_cross_status_s2c>() {
      @java.lang.Override
      public rank_cross_status_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<rank_cross_status_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<rank_cross_status_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgRank.rank_cross_status_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface rank_like_info_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.rank_like_info_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 排行榜sn
     * </pre>
     *
     * <code>uint32 rank_sn = 1;</code>
     * @return The rankSn.
     */
    int getRankSn();
  }
  /**
   * <pre>
   * 排行榜点赞信息
   * </pre>
   *
   * Protobuf type {@code org.gof.demo.worldsrv.msg.rank_like_info_c2s}
   */
  public static final class rank_like_info_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.rank_like_info_c2s)
      rank_like_info_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use rank_like_info_c2s.newBuilder() to construct.
    private rank_like_info_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private rank_like_info_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new rank_like_info_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgRank.internal_static_org_gof_demo_worldsrv_msg_rank_like_info_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgRank.internal_static_org_gof_demo_worldsrv_msg_rank_like_info_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_c2s.class, org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_c2s.Builder.class);
    }

    public static final int RANK_SN_FIELD_NUMBER = 1;
    private int rankSn_ = 0;
    /**
     * <pre>
     * 排行榜sn
     * </pre>
     *
     * <code>uint32 rank_sn = 1;</code>
     * @return The rankSn.
     */
    @java.lang.Override
    public int getRankSn() {
      return rankSn_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (rankSn_ != 0) {
        output.writeUInt32(1, rankSn_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (rankSn_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, rankSn_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_c2s other = (org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_c2s) obj;

      if (getRankSn()
          != other.getRankSn()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + RANK_SN_FIELD_NUMBER;
      hash = (53 * hash) + getRankSn();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 排行榜点赞信息
     * </pre>
     *
     * Protobuf type {@code org.gof.demo.worldsrv.msg.rank_like_info_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.rank_like_info_c2s)
        org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgRank.internal_static_org_gof_demo_worldsrv_msg_rank_like_info_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgRank.internal_static_org_gof_demo_worldsrv_msg_rank_like_info_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_c2s.class, org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        rankSn_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgRank.internal_static_org_gof_demo_worldsrv_msg_rank_like_info_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_c2s build() {
        org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_c2s result = new org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.rankSn_ = rankSn_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_c2s.getDefaultInstance()) return this;
        if (other.getRankSn() != 0) {
          setRankSn(other.getRankSn());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                rankSn_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int rankSn_ ;
      /**
       * <pre>
       * 排行榜sn
       * </pre>
       *
       * <code>uint32 rank_sn = 1;</code>
       * @return The rankSn.
       */
      @java.lang.Override
      public int getRankSn() {
        return rankSn_;
      }
      /**
       * <pre>
       * 排行榜sn
       * </pre>
       *
       * <code>uint32 rank_sn = 1;</code>
       * @param value The rankSn to set.
       * @return This builder for chaining.
       */
      public Builder setRankSn(int value) {

        rankSn_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 排行榜sn
       * </pre>
       *
       * <code>uint32 rank_sn = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearRankSn() {
        bitField0_ = (bitField0_ & ~0x00000001);
        rankSn_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.rank_like_info_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.rank_like_info_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<rank_like_info_c2s>
        PARSER = new com.google.protobuf.AbstractParser<rank_like_info_c2s>() {
      @java.lang.Override
      public rank_like_info_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<rank_like_info_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<rank_like_info_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface rank_like_info_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.rank_like_info_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 排行榜sn
     * </pre>
     *
     * <code>uint32 rank_sn = 1;</code>
     * @return The rankSn.
     */
    int getRankSn();

    /**
     * <pre>
     * 点赞状态列表（前N名的点赞状态，0为未点赞，1为已点赞）[0,0,0]
     * </pre>
     *
     * <code>repeated uint32 like_status_list = 2;</code>
     * @return A list containing the likeStatusList.
     */
    java.util.List<java.lang.Integer> getLikeStatusListList();
    /**
     * <pre>
     * 点赞状态列表（前N名的点赞状态，0为未点赞，1为已点赞）[0,0,0]
     * </pre>
     *
     * <code>repeated uint32 like_status_list = 2;</code>
     * @return The count of likeStatusList.
     */
    int getLikeStatusListCount();
    /**
     * <pre>
     * 点赞状态列表（前N名的点赞状态，0为未点赞，1为已点赞）[0,0,0]
     * </pre>
     *
     * <code>repeated uint32 like_status_list = 2;</code>
     * @param index The index of the element to return.
     * @return The likeStatusList at the given index.
     */
    int getLikeStatusList(int index);
  }
  /**
   * <pre>
   * 排行榜点赞信息
   * </pre>
   *
   * Protobuf type {@code org.gof.demo.worldsrv.msg.rank_like_info_s2c}
   */
  public static final class rank_like_info_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.rank_like_info_s2c)
      rank_like_info_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use rank_like_info_s2c.newBuilder() to construct.
    private rank_like_info_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private rank_like_info_s2c() {
      likeStatusList_ = emptyIntList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new rank_like_info_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgRank.internal_static_org_gof_demo_worldsrv_msg_rank_like_info_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgRank.internal_static_org_gof_demo_worldsrv_msg_rank_like_info_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_s2c.class, org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_s2c.Builder.class);
    }

    public static final int RANK_SN_FIELD_NUMBER = 1;
    private int rankSn_ = 0;
    /**
     * <pre>
     * 排行榜sn
     * </pre>
     *
     * <code>uint32 rank_sn = 1;</code>
     * @return The rankSn.
     */
    @java.lang.Override
    public int getRankSn() {
      return rankSn_;
    }

    public static final int LIKE_STATUS_LIST_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.IntList likeStatusList_ =
        emptyIntList();
    /**
     * <pre>
     * 点赞状态列表（前N名的点赞状态，0为未点赞，1为已点赞）[0,0,0]
     * </pre>
     *
     * <code>repeated uint32 like_status_list = 2;</code>
     * @return A list containing the likeStatusList.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getLikeStatusListList() {
      return likeStatusList_;
    }
    /**
     * <pre>
     * 点赞状态列表（前N名的点赞状态，0为未点赞，1为已点赞）[0,0,0]
     * </pre>
     *
     * <code>repeated uint32 like_status_list = 2;</code>
     * @return The count of likeStatusList.
     */
    public int getLikeStatusListCount() {
      return likeStatusList_.size();
    }
    /**
     * <pre>
     * 点赞状态列表（前N名的点赞状态，0为未点赞，1为已点赞）[0,0,0]
     * </pre>
     *
     * <code>repeated uint32 like_status_list = 2;</code>
     * @param index The index of the element to return.
     * @return The likeStatusList at the given index.
     */
    public int getLikeStatusList(int index) {
      return likeStatusList_.getInt(index);
    }
    private int likeStatusListMemoizedSerializedSize = -1;

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (rankSn_ != 0) {
        output.writeUInt32(1, rankSn_);
      }
      if (getLikeStatusListList().size() > 0) {
        output.writeUInt32NoTag(18);
        output.writeUInt32NoTag(likeStatusListMemoizedSerializedSize);
      }
      for (int i = 0; i < likeStatusList_.size(); i++) {
        output.writeUInt32NoTag(likeStatusList_.getInt(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (rankSn_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, rankSn_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < likeStatusList_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeUInt32SizeNoTag(likeStatusList_.getInt(i));
        }
        size += dataSize;
        if (!getLikeStatusListList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        likeStatusListMemoizedSerializedSize = dataSize;
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_s2c other = (org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_s2c) obj;

      if (getRankSn()
          != other.getRankSn()) return false;
      if (!getLikeStatusListList()
          .equals(other.getLikeStatusListList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + RANK_SN_FIELD_NUMBER;
      hash = (53 * hash) + getRankSn();
      if (getLikeStatusListCount() > 0) {
        hash = (37 * hash) + LIKE_STATUS_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getLikeStatusListList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 排行榜点赞信息
     * </pre>
     *
     * Protobuf type {@code org.gof.demo.worldsrv.msg.rank_like_info_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.rank_like_info_s2c)
        org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgRank.internal_static_org_gof_demo_worldsrv_msg_rank_like_info_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgRank.internal_static_org_gof_demo_worldsrv_msg_rank_like_info_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_s2c.class, org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        rankSn_ = 0;
        likeStatusList_ = emptyIntList();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgRank.internal_static_org_gof_demo_worldsrv_msg_rank_like_info_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_s2c build() {
        org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_s2c result = new org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.rankSn_ = rankSn_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          likeStatusList_.makeImmutable();
          result.likeStatusList_ = likeStatusList_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_s2c.getDefaultInstance()) return this;
        if (other.getRankSn() != 0) {
          setRankSn(other.getRankSn());
        }
        if (!other.likeStatusList_.isEmpty()) {
          if (likeStatusList_.isEmpty()) {
            likeStatusList_ = other.likeStatusList_;
            likeStatusList_.makeImmutable();
            bitField0_ |= 0x00000002;
          } else {
            ensureLikeStatusListIsMutable();
            likeStatusList_.addAll(other.likeStatusList_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                rankSn_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                int v = input.readUInt32();
                ensureLikeStatusListIsMutable();
                likeStatusList_.addInt(v);
                break;
              } // case 16
              case 18: {
                int length = input.readRawVarint32();
                int limit = input.pushLimit(length);
                ensureLikeStatusListIsMutable();
                while (input.getBytesUntilLimit() > 0) {
                  likeStatusList_.addInt(input.readUInt32());
                }
                input.popLimit(limit);
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int rankSn_ ;
      /**
       * <pre>
       * 排行榜sn
       * </pre>
       *
       * <code>uint32 rank_sn = 1;</code>
       * @return The rankSn.
       */
      @java.lang.Override
      public int getRankSn() {
        return rankSn_;
      }
      /**
       * <pre>
       * 排行榜sn
       * </pre>
       *
       * <code>uint32 rank_sn = 1;</code>
       * @param value The rankSn to set.
       * @return This builder for chaining.
       */
      public Builder setRankSn(int value) {

        rankSn_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 排行榜sn
       * </pre>
       *
       * <code>uint32 rank_sn = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearRankSn() {
        bitField0_ = (bitField0_ & ~0x00000001);
        rankSn_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.IntList likeStatusList_ = emptyIntList();
      private void ensureLikeStatusListIsMutable() {
        if (!likeStatusList_.isModifiable()) {
          likeStatusList_ = makeMutableCopy(likeStatusList_);
        }
        bitField0_ |= 0x00000002;
      }
      /**
       * <pre>
       * 点赞状态列表（前N名的点赞状态，0为未点赞，1为已点赞）[0,0,0]
       * </pre>
       *
       * <code>repeated uint32 like_status_list = 2;</code>
       * @return A list containing the likeStatusList.
       */
      public java.util.List<java.lang.Integer>
          getLikeStatusListList() {
        likeStatusList_.makeImmutable();
        return likeStatusList_;
      }
      /**
       * <pre>
       * 点赞状态列表（前N名的点赞状态，0为未点赞，1为已点赞）[0,0,0]
       * </pre>
       *
       * <code>repeated uint32 like_status_list = 2;</code>
       * @return The count of likeStatusList.
       */
      public int getLikeStatusListCount() {
        return likeStatusList_.size();
      }
      /**
       * <pre>
       * 点赞状态列表（前N名的点赞状态，0为未点赞，1为已点赞）[0,0,0]
       * </pre>
       *
       * <code>repeated uint32 like_status_list = 2;</code>
       * @param index The index of the element to return.
       * @return The likeStatusList at the given index.
       */
      public int getLikeStatusList(int index) {
        return likeStatusList_.getInt(index);
      }
      /**
       * <pre>
       * 点赞状态列表（前N名的点赞状态，0为未点赞，1为已点赞）[0,0,0]
       * </pre>
       *
       * <code>repeated uint32 like_status_list = 2;</code>
       * @param index The index to set the value at.
       * @param value The likeStatusList to set.
       * @return This builder for chaining.
       */
      public Builder setLikeStatusList(
          int index, int value) {

        ensureLikeStatusListIsMutable();
        likeStatusList_.setInt(index, value);
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 点赞状态列表（前N名的点赞状态，0为未点赞，1为已点赞）[0,0,0]
       * </pre>
       *
       * <code>repeated uint32 like_status_list = 2;</code>
       * @param value The likeStatusList to add.
       * @return This builder for chaining.
       */
      public Builder addLikeStatusList(int value) {

        ensureLikeStatusListIsMutable();
        likeStatusList_.addInt(value);
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 点赞状态列表（前N名的点赞状态，0为未点赞，1为已点赞）[0,0,0]
       * </pre>
       *
       * <code>repeated uint32 like_status_list = 2;</code>
       * @param values The likeStatusList to add.
       * @return This builder for chaining.
       */
      public Builder addAllLikeStatusList(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureLikeStatusListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, likeStatusList_);
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 点赞状态列表（前N名的点赞状态，0为未点赞，1为已点赞）[0,0,0]
       * </pre>
       *
       * <code>repeated uint32 like_status_list = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearLikeStatusList() {
        likeStatusList_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.rank_like_info_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.rank_like_info_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<rank_like_info_s2c>
        PARSER = new com.google.protobuf.AbstractParser<rank_like_info_s2c>() {
      @java.lang.Override
      public rank_like_info_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<rank_like_info_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<rank_like_info_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgRank.rank_like_info_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface rank_like_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.rank_like_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 排行榜sn
     * </pre>
     *
     * <code>uint32 rank_sn = 1;</code>
     * @return The rankSn.
     */
    int getRankSn();

    /**
     * <pre>
     * 点赞的名次，值为1,2,3
     * </pre>
     *
     * <code>uint32 like_rank = 2;</code>
     * @return The likeRank.
     */
    int getLikeRank();
  }
  /**
   * <pre>
   * 排行榜点赞
   * </pre>
   *
   * Protobuf type {@code org.gof.demo.worldsrv.msg.rank_like_c2s}
   */
  public static final class rank_like_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.rank_like_c2s)
      rank_like_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use rank_like_c2s.newBuilder() to construct.
    private rank_like_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private rank_like_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new rank_like_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgRank.internal_static_org_gof_demo_worldsrv_msg_rank_like_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgRank.internal_static_org_gof_demo_worldsrv_msg_rank_like_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgRank.rank_like_c2s.class, org.gof.demo.worldsrv.msg.MsgRank.rank_like_c2s.Builder.class);
    }

    public static final int RANK_SN_FIELD_NUMBER = 1;
    private int rankSn_ = 0;
    /**
     * <pre>
     * 排行榜sn
     * </pre>
     *
     * <code>uint32 rank_sn = 1;</code>
     * @return The rankSn.
     */
    @java.lang.Override
    public int getRankSn() {
      return rankSn_;
    }

    public static final int LIKE_RANK_FIELD_NUMBER = 2;
    private int likeRank_ = 0;
    /**
     * <pre>
     * 点赞的名次，值为1,2,3
     * </pre>
     *
     * <code>uint32 like_rank = 2;</code>
     * @return The likeRank.
     */
    @java.lang.Override
    public int getLikeRank() {
      return likeRank_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (rankSn_ != 0) {
        output.writeUInt32(1, rankSn_);
      }
      if (likeRank_ != 0) {
        output.writeUInt32(2, likeRank_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (rankSn_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, rankSn_);
      }
      if (likeRank_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, likeRank_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgRank.rank_like_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgRank.rank_like_c2s other = (org.gof.demo.worldsrv.msg.MsgRank.rank_like_c2s) obj;

      if (getRankSn()
          != other.getRankSn()) return false;
      if (getLikeRank()
          != other.getLikeRank()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + RANK_SN_FIELD_NUMBER;
      hash = (53 * hash) + getRankSn();
      hash = (37 * hash) + LIKE_RANK_FIELD_NUMBER;
      hash = (53 * hash) + getLikeRank();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgRank.rank_like_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_like_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_like_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_like_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_like_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_like_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_like_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_like_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgRank.rank_like_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgRank.rank_like_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_like_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRank.rank_like_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgRank.rank_like_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 排行榜点赞
     * </pre>
     *
     * Protobuf type {@code org.gof.demo.worldsrv.msg.rank_like_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.rank_like_c2s)
        org.gof.demo.worldsrv.msg.MsgRank.rank_like_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgRank.internal_static_org_gof_demo_worldsrv_msg_rank_like_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgRank.internal_static_org_gof_demo_worldsrv_msg_rank_like_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgRank.rank_like_c2s.class, org.gof.demo.worldsrv.msg.MsgRank.rank_like_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgRank.rank_like_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        rankSn_ = 0;
        likeRank_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgRank.internal_static_org_gof_demo_worldsrv_msg_rank_like_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRank.rank_like_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgRank.rank_like_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRank.rank_like_c2s build() {
        org.gof.demo.worldsrv.msg.MsgRank.rank_like_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRank.rank_like_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgRank.rank_like_c2s result = new org.gof.demo.worldsrv.msg.MsgRank.rank_like_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgRank.rank_like_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.rankSn_ = rankSn_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.likeRank_ = likeRank_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgRank.rank_like_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgRank.rank_like_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgRank.rank_like_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgRank.rank_like_c2s.getDefaultInstance()) return this;
        if (other.getRankSn() != 0) {
          setRankSn(other.getRankSn());
        }
        if (other.getLikeRank() != 0) {
          setLikeRank(other.getLikeRank());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                rankSn_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                likeRank_ = input.readUInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int rankSn_ ;
      /**
       * <pre>
       * 排行榜sn
       * </pre>
       *
       * <code>uint32 rank_sn = 1;</code>
       * @return The rankSn.
       */
      @java.lang.Override
      public int getRankSn() {
        return rankSn_;
      }
      /**
       * <pre>
       * 排行榜sn
       * </pre>
       *
       * <code>uint32 rank_sn = 1;</code>
       * @param value The rankSn to set.
       * @return This builder for chaining.
       */
      public Builder setRankSn(int value) {

        rankSn_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 排行榜sn
       * </pre>
       *
       * <code>uint32 rank_sn = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearRankSn() {
        bitField0_ = (bitField0_ & ~0x00000001);
        rankSn_ = 0;
        onChanged();
        return this;
      }

      private int likeRank_ ;
      /**
       * <pre>
       * 点赞的名次，值为1,2,3
       * </pre>
       *
       * <code>uint32 like_rank = 2;</code>
       * @return The likeRank.
       */
      @java.lang.Override
      public int getLikeRank() {
        return likeRank_;
      }
      /**
       * <pre>
       * 点赞的名次，值为1,2,3
       * </pre>
       *
       * <code>uint32 like_rank = 2;</code>
       * @param value The likeRank to set.
       * @return This builder for chaining.
       */
      public Builder setLikeRank(int value) {

        likeRank_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 点赞的名次，值为1,2,3
       * </pre>
       *
       * <code>uint32 like_rank = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearLikeRank() {
        bitField0_ = (bitField0_ & ~0x00000002);
        likeRank_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.rank_like_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.rank_like_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgRank.rank_like_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgRank.rank_like_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgRank.rank_like_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<rank_like_c2s>
        PARSER = new com.google.protobuf.AbstractParser<rank_like_c2s>() {
      @java.lang.Override
      public rank_like_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<rank_like_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<rank_like_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgRank.rank_like_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_rank_data_list_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_rank_data_list_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_rank_data_list_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_rank_data_list_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_rank_serv_list_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_rank_serv_list_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_rank_serv_list_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_rank_serv_list_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_rank_cross_status_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_rank_cross_status_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_rank_cross_status_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_rank_cross_status_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_rank_like_info_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_rank_like_info_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_rank_like_info_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_rank_like_info_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_rank_like_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_rank_like_c2s_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\016msg.rank.proto\022\031org.gof.demo.worldsrv." +
      "msg\032\roptions.proto\032\014define.proto\"W\n\022rank" +
      "_data_list_c2s\022:\n\007request\030\001 \003(\0132).org.go" +
      "f.demo.worldsrv.msg.p_rank_request:\005\210\303\032\201" +
      " \"[\n\022rank_data_list_s2c\022>\n\016rank_data_lis" +
      "t\030\001 \003(\0132&.org.gof.demo.worldsrv.msg.p_ra" +
      "nk_data:\005\210\303\032\201 \")\n\022rank_serv_list_c2s\022\014\n\004" +
      "type\030\001 \001(\005:\005\210\303\032\202 \"2\n\022rank_serv_list_s2c\022" +
      "\025\n\tserv_list\030\001 \003(\rB\002\020\000:\005\210\303\032\202 \"\036\n\025rank_cr" +
      "oss_status_c2s:\005\210\303\032\203 \".\n\025rank_cross_stat" +
      "us_s2c\022\016\n\006status\030\001 \001(\r:\005\210\303\032\203 \",\n\022rank_li" +
      "ke_info_c2s\022\017\n\007rank_sn\030\001 \001(\r:\005\210\303\032\204 \"F\n\022r" +
      "ank_like_info_s2c\022\017\n\007rank_sn\030\001 \001(\r\022\030\n\020li" +
      "ke_status_list\030\002 \003(\r:\005\210\303\032\204 \":\n\rrank_like" +
      "_c2s\022\017\n\007rank_sn\030\001 \001(\r\022\021\n\tlike_rank\030\002 \001(\r" +
      ":\005\210\303\032\205 b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          org.gof.demo.worldsrv.msg.Options.getDescriptor(),
          org.gof.demo.worldsrv.msg.Define.getDescriptor(),
        });
    internal_static_org_gof_demo_worldsrv_msg_rank_data_list_c2s_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_org_gof_demo_worldsrv_msg_rank_data_list_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_rank_data_list_c2s_descriptor,
        new java.lang.String[] { "Request", });
    internal_static_org_gof_demo_worldsrv_msg_rank_data_list_s2c_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_org_gof_demo_worldsrv_msg_rank_data_list_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_rank_data_list_s2c_descriptor,
        new java.lang.String[] { "RankDataList", });
    internal_static_org_gof_demo_worldsrv_msg_rank_serv_list_c2s_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_org_gof_demo_worldsrv_msg_rank_serv_list_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_rank_serv_list_c2s_descriptor,
        new java.lang.String[] { "Type", });
    internal_static_org_gof_demo_worldsrv_msg_rank_serv_list_s2c_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_org_gof_demo_worldsrv_msg_rank_serv_list_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_rank_serv_list_s2c_descriptor,
        new java.lang.String[] { "ServList", });
    internal_static_org_gof_demo_worldsrv_msg_rank_cross_status_c2s_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_org_gof_demo_worldsrv_msg_rank_cross_status_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_rank_cross_status_c2s_descriptor,
        new java.lang.String[] { });
    internal_static_org_gof_demo_worldsrv_msg_rank_cross_status_s2c_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_org_gof_demo_worldsrv_msg_rank_cross_status_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_rank_cross_status_s2c_descriptor,
        new java.lang.String[] { "Status", });
    internal_static_org_gof_demo_worldsrv_msg_rank_like_info_c2s_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_org_gof_demo_worldsrv_msg_rank_like_info_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_rank_like_info_c2s_descriptor,
        new java.lang.String[] { "RankSn", });
    internal_static_org_gof_demo_worldsrv_msg_rank_like_info_s2c_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_org_gof_demo_worldsrv_msg_rank_like_info_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_rank_like_info_s2c_descriptor,
        new java.lang.String[] { "RankSn", "LikeStatusList", });
    internal_static_org_gof_demo_worldsrv_msg_rank_like_c2s_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_org_gof_demo_worldsrv_msg_rank_like_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_rank_like_c2s_descriptor,
        new java.lang.String[] { "RankSn", "LikeRank", });
    com.google.protobuf.ExtensionRegistry registry =
        com.google.protobuf.ExtensionRegistry.newInstance();
    registry.add(org.gof.demo.worldsrv.msg.Options.msgid);
    com.google.protobuf.Descriptors.FileDescriptor
        .internalUpdateFileDescriptor(descriptor, registry);
    org.gof.demo.worldsrv.msg.Options.getDescriptor();
    org.gof.demo.worldsrv.msg.Define.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
