// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: msg.warToken.proto

// Protobuf Java Version: 3.25.3
package org.gof.demo.worldsrv.msg;

public final class MsgWarToken {
  private MsgWarToken() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface war_token_info_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.war_token_info_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 id = 1;</code>
     * @return The id.
     */
    int getId();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.war_token_info_c2s}
   */
  public static final class war_token_info_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.war_token_info_c2s)
      war_token_info_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use war_token_info_c2s.newBuilder() to construct.
    private war_token_info_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private war_token_info_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new war_token_info_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_info_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_info_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_c2s.class, org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_c2s.Builder.class);
    }

    public static final int ID_FIELD_NUMBER = 1;
    private int id_ = 0;
    /**
     * <code>uint32 id = 1;</code>
     * @return The id.
     */
    @java.lang.Override
    public int getId() {
      return id_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (id_ != 0) {
        output.writeUInt32(1, id_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (id_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, id_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_c2s other = (org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_c2s) obj;

      if (getId()
          != other.getId()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + getId();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.war_token_info_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.war_token_info_c2s)
        org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_info_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_info_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_c2s.class, org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        id_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_info_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_c2s build() {
        org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_c2s result = new org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.id_ = id_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_c2s.getDefaultInstance()) return this;
        if (other.getId() != 0) {
          setId(other.getId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                id_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int id_ ;
      /**
       * <code>uint32 id = 1;</code>
       * @return The id.
       */
      @java.lang.Override
      public int getId() {
        return id_;
      }
      /**
       * <code>uint32 id = 1;</code>
       * @param value The id to set.
       * @return This builder for chaining.
       */
      public Builder setId(int value) {

        id_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        id_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.war_token_info_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.war_token_info_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<war_token_info_c2s>
        PARSER = new com.google.protobuf.AbstractParser<war_token_info_c2s>() {
      @java.lang.Override
      public war_token_info_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<war_token_info_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<war_token_info_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface war_token_info_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.war_token_info_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 id = 1;</code>
     * @return The id.
     */
    int getId();

    /**
     * <code>uint32 if_buy = 2;</code>
     * @return The ifBuy.
     */
    int getIfBuy();

    /**
     * <code>uint32 end_time = 3;</code>
     * @return The endTime.
     */
    int getEndTime();

    /**
     * <code>uint32 lev = 4;</code>
     * @return The lev.
     */
    int getLev();

    /**
     * <code>uint32 max_lev = 5;</code>
     * @return The maxLev.
     */
    int getMaxLev();

    /**
     * <code>uint32 exp = 6;</code>
     * @return The exp.
     */
    int getExp();

    /**
     * <code>uint32 need_exp = 7;</code>
     * @return The needExp.
     */
    int getNeedExp();

    /**
     * <code>uint32 extra_times = 8;</code>
     * @return The extraTimes.
     */
    int getExtraTimes();

    /**
     * <code>uint32 extra_max_times = 9;</code>
     * @return The extraMaxTimes.
     */
    int getExtraMaxTimes();

    /**
     * <code>uint32 extra_need_exp = 10;</code>
     * @return The extraNeedExp.
     */
    int getExtraNeedExp();

    /**
     * <code>uint32 bundle_id = 11;</code>
     * @return The bundleId.
     */
    int getBundleId();

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward extra_reward = 12;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward> 
        getExtraRewardList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward extra_reward = 12;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_reward getExtraReward(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward extra_reward = 12;</code>
     */
    int getExtraRewardCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward extra_reward = 12;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder> 
        getExtraRewardOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward extra_reward = 12;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder getExtraRewardOrBuilder(
        int index);

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list free_reward = 13;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward_status_list> 
        getFreeRewardList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list free_reward = 13;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_reward_status_list getFreeReward(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list free_reward = 13;</code>
     */
    int getFreeRewardCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list free_reward = 13;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_reward_status_listOrBuilder> 
        getFreeRewardOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list free_reward = 13;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_reward_status_listOrBuilder getFreeRewardOrBuilder(
        int index);

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward = 14;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward_status_list> 
        getHighRewardList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward = 14;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_reward_status_list getHighReward(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward = 14;</code>
     */
    int getHighRewardCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward = 14;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_reward_status_listOrBuilder> 
        getHighRewardOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward = 14;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_reward_status_listOrBuilder getHighRewardOrBuilder(
        int index);

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 15;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_war_token_task> 
        getTaskListList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 15;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_war_token_task getTaskList(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 15;</code>
     */
    int getTaskListCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 15;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_war_token_taskOrBuilder> 
        getTaskListOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 15;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_war_token_taskOrBuilder getTaskListOrBuilder(
        int index);

    /**
     * <code>repeated uint32 special_reward_list = 16 [packed = false];</code>
     * @return A list containing the specialRewardList.
     */
    java.util.List<java.lang.Integer> getSpecialRewardListList();
    /**
     * <code>repeated uint32 special_reward_list = 16 [packed = false];</code>
     * @return The count of specialRewardList.
     */
    int getSpecialRewardListCount();
    /**
     * <code>repeated uint32 special_reward_list = 16 [packed = false];</code>
     * @param index The index of the element to return.
     * @return The specialRewardList at the given index.
     */
    int getSpecialRewardList(int index);

    /**
     * <code>uint32 total_exp = 17;</code>
     * @return The totalExp.
     */
    int getTotalExp();

    /**
     * <code>uint32 if_buy_2 = 18;</code>
     * @return The ifBuy2.
     */
    int getIfBuy2();

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward_2 = 19;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward_status_list> 
        getHighReward2List();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward_2 = 19;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_reward_status_list getHighReward2(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward_2 = 19;</code>
     */
    int getHighReward2Count();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward_2 = 19;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_reward_status_listOrBuilder> 
        getHighReward2OrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward_2 = 19;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_reward_status_listOrBuilder getHighReward2OrBuilder(
        int index);

    /**
     * <code>uint32 merge_flag = 20;</code>
     * @return The mergeFlag.
     */
    int getMergeFlag();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.war_token_info_s2c}
   */
  public static final class war_token_info_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.war_token_info_s2c)
      war_token_info_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use war_token_info_s2c.newBuilder() to construct.
    private war_token_info_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private war_token_info_s2c() {
      extraReward_ = java.util.Collections.emptyList();
      freeReward_ = java.util.Collections.emptyList();
      highReward_ = java.util.Collections.emptyList();
      taskList_ = java.util.Collections.emptyList();
      specialRewardList_ = emptyIntList();
      highReward2_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new war_token_info_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_info_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_info_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_s2c.class, org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_s2c.Builder.class);
    }

    public static final int ID_FIELD_NUMBER = 1;
    private int id_ = 0;
    /**
     * <code>uint32 id = 1;</code>
     * @return The id.
     */
    @java.lang.Override
    public int getId() {
      return id_;
    }

    public static final int IF_BUY_FIELD_NUMBER = 2;
    private int ifBuy_ = 0;
    /**
     * <code>uint32 if_buy = 2;</code>
     * @return The ifBuy.
     */
    @java.lang.Override
    public int getIfBuy() {
      return ifBuy_;
    }

    public static final int END_TIME_FIELD_NUMBER = 3;
    private int endTime_ = 0;
    /**
     * <code>uint32 end_time = 3;</code>
     * @return The endTime.
     */
    @java.lang.Override
    public int getEndTime() {
      return endTime_;
    }

    public static final int LEV_FIELD_NUMBER = 4;
    private int lev_ = 0;
    /**
     * <code>uint32 lev = 4;</code>
     * @return The lev.
     */
    @java.lang.Override
    public int getLev() {
      return lev_;
    }

    public static final int MAX_LEV_FIELD_NUMBER = 5;
    private int maxLev_ = 0;
    /**
     * <code>uint32 max_lev = 5;</code>
     * @return The maxLev.
     */
    @java.lang.Override
    public int getMaxLev() {
      return maxLev_;
    }

    public static final int EXP_FIELD_NUMBER = 6;
    private int exp_ = 0;
    /**
     * <code>uint32 exp = 6;</code>
     * @return The exp.
     */
    @java.lang.Override
    public int getExp() {
      return exp_;
    }

    public static final int NEED_EXP_FIELD_NUMBER = 7;
    private int needExp_ = 0;
    /**
     * <code>uint32 need_exp = 7;</code>
     * @return The needExp.
     */
    @java.lang.Override
    public int getNeedExp() {
      return needExp_;
    }

    public static final int EXTRA_TIMES_FIELD_NUMBER = 8;
    private int extraTimes_ = 0;
    /**
     * <code>uint32 extra_times = 8;</code>
     * @return The extraTimes.
     */
    @java.lang.Override
    public int getExtraTimes() {
      return extraTimes_;
    }

    public static final int EXTRA_MAX_TIMES_FIELD_NUMBER = 9;
    private int extraMaxTimes_ = 0;
    /**
     * <code>uint32 extra_max_times = 9;</code>
     * @return The extraMaxTimes.
     */
    @java.lang.Override
    public int getExtraMaxTimes() {
      return extraMaxTimes_;
    }

    public static final int EXTRA_NEED_EXP_FIELD_NUMBER = 10;
    private int extraNeedExp_ = 0;
    /**
     * <code>uint32 extra_need_exp = 10;</code>
     * @return The extraNeedExp.
     */
    @java.lang.Override
    public int getExtraNeedExp() {
      return extraNeedExp_;
    }

    public static final int BUNDLE_ID_FIELD_NUMBER = 11;
    private int bundleId_ = 0;
    /**
     * <code>uint32 bundle_id = 11;</code>
     * @return The bundleId.
     */
    @java.lang.Override
    public int getBundleId() {
      return bundleId_;
    }

    public static final int EXTRA_REWARD_FIELD_NUMBER = 12;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward> extraReward_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward extra_reward = 12;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward> getExtraRewardList() {
      return extraReward_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward extra_reward = 12;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder> 
        getExtraRewardOrBuilderList() {
      return extraReward_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward extra_reward = 12;</code>
     */
    @java.lang.Override
    public int getExtraRewardCount() {
      return extraReward_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward extra_reward = 12;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_reward getExtraReward(int index) {
      return extraReward_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward extra_reward = 12;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder getExtraRewardOrBuilder(
        int index) {
      return extraReward_.get(index);
    }

    public static final int FREE_REWARD_FIELD_NUMBER = 13;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward_status_list> freeReward_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list free_reward = 13;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward_status_list> getFreeRewardList() {
      return freeReward_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list free_reward = 13;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_reward_status_listOrBuilder> 
        getFreeRewardOrBuilderList() {
      return freeReward_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list free_reward = 13;</code>
     */
    @java.lang.Override
    public int getFreeRewardCount() {
      return freeReward_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list free_reward = 13;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_reward_status_list getFreeReward(int index) {
      return freeReward_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list free_reward = 13;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_reward_status_listOrBuilder getFreeRewardOrBuilder(
        int index) {
      return freeReward_.get(index);
    }

    public static final int HIGH_REWARD_FIELD_NUMBER = 14;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward_status_list> highReward_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward = 14;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward_status_list> getHighRewardList() {
      return highReward_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward = 14;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_reward_status_listOrBuilder> 
        getHighRewardOrBuilderList() {
      return highReward_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward = 14;</code>
     */
    @java.lang.Override
    public int getHighRewardCount() {
      return highReward_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward = 14;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_reward_status_list getHighReward(int index) {
      return highReward_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward = 14;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_reward_status_listOrBuilder getHighRewardOrBuilder(
        int index) {
      return highReward_.get(index);
    }

    public static final int TASK_LIST_FIELD_NUMBER = 15;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_war_token_task> taskList_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 15;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_war_token_task> getTaskListList() {
      return taskList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 15;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_war_token_taskOrBuilder> 
        getTaskListOrBuilderList() {
      return taskList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 15;</code>
     */
    @java.lang.Override
    public int getTaskListCount() {
      return taskList_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 15;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_war_token_task getTaskList(int index) {
      return taskList_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 15;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_war_token_taskOrBuilder getTaskListOrBuilder(
        int index) {
      return taskList_.get(index);
    }

    public static final int SPECIAL_REWARD_LIST_FIELD_NUMBER = 16;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.IntList specialRewardList_ =
        emptyIntList();
    /**
     * <code>repeated uint32 special_reward_list = 16 [packed = false];</code>
     * @return A list containing the specialRewardList.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getSpecialRewardListList() {
      return specialRewardList_;
    }
    /**
     * <code>repeated uint32 special_reward_list = 16 [packed = false];</code>
     * @return The count of specialRewardList.
     */
    public int getSpecialRewardListCount() {
      return specialRewardList_.size();
    }
    /**
     * <code>repeated uint32 special_reward_list = 16 [packed = false];</code>
     * @param index The index of the element to return.
     * @return The specialRewardList at the given index.
     */
    public int getSpecialRewardList(int index) {
      return specialRewardList_.getInt(index);
    }

    public static final int TOTAL_EXP_FIELD_NUMBER = 17;
    private int totalExp_ = 0;
    /**
     * <code>uint32 total_exp = 17;</code>
     * @return The totalExp.
     */
    @java.lang.Override
    public int getTotalExp() {
      return totalExp_;
    }

    public static final int IF_BUY_2_FIELD_NUMBER = 18;
    private int ifBuy2_ = 0;
    /**
     * <code>uint32 if_buy_2 = 18;</code>
     * @return The ifBuy2.
     */
    @java.lang.Override
    public int getIfBuy2() {
      return ifBuy2_;
    }

    public static final int HIGH_REWARD_2_FIELD_NUMBER = 19;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward_status_list> highReward2_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward_2 = 19;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward_status_list> getHighReward2List() {
      return highReward2_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward_2 = 19;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_reward_status_listOrBuilder> 
        getHighReward2OrBuilderList() {
      return highReward2_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward_2 = 19;</code>
     */
    @java.lang.Override
    public int getHighReward2Count() {
      return highReward2_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward_2 = 19;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_reward_status_list getHighReward2(int index) {
      return highReward2_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward_2 = 19;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_reward_status_listOrBuilder getHighReward2OrBuilder(
        int index) {
      return highReward2_.get(index);
    }

    public static final int MERGE_FLAG_FIELD_NUMBER = 20;
    private int mergeFlag_ = 0;
    /**
     * <code>uint32 merge_flag = 20;</code>
     * @return The mergeFlag.
     */
    @java.lang.Override
    public int getMergeFlag() {
      return mergeFlag_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (id_ != 0) {
        output.writeUInt32(1, id_);
      }
      if (ifBuy_ != 0) {
        output.writeUInt32(2, ifBuy_);
      }
      if (endTime_ != 0) {
        output.writeUInt32(3, endTime_);
      }
      if (lev_ != 0) {
        output.writeUInt32(4, lev_);
      }
      if (maxLev_ != 0) {
        output.writeUInt32(5, maxLev_);
      }
      if (exp_ != 0) {
        output.writeUInt32(6, exp_);
      }
      if (needExp_ != 0) {
        output.writeUInt32(7, needExp_);
      }
      if (extraTimes_ != 0) {
        output.writeUInt32(8, extraTimes_);
      }
      if (extraMaxTimes_ != 0) {
        output.writeUInt32(9, extraMaxTimes_);
      }
      if (extraNeedExp_ != 0) {
        output.writeUInt32(10, extraNeedExp_);
      }
      if (bundleId_ != 0) {
        output.writeUInt32(11, bundleId_);
      }
      for (int i = 0; i < extraReward_.size(); i++) {
        output.writeMessage(12, extraReward_.get(i));
      }
      for (int i = 0; i < freeReward_.size(); i++) {
        output.writeMessage(13, freeReward_.get(i));
      }
      for (int i = 0; i < highReward_.size(); i++) {
        output.writeMessage(14, highReward_.get(i));
      }
      for (int i = 0; i < taskList_.size(); i++) {
        output.writeMessage(15, taskList_.get(i));
      }
      for (int i = 0; i < specialRewardList_.size(); i++) {
        output.writeUInt32(16, specialRewardList_.getInt(i));
      }
      if (totalExp_ != 0) {
        output.writeUInt32(17, totalExp_);
      }
      if (ifBuy2_ != 0) {
        output.writeUInt32(18, ifBuy2_);
      }
      for (int i = 0; i < highReward2_.size(); i++) {
        output.writeMessage(19, highReward2_.get(i));
      }
      if (mergeFlag_ != 0) {
        output.writeUInt32(20, mergeFlag_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (id_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, id_);
      }
      if (ifBuy_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, ifBuy_);
      }
      if (endTime_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(3, endTime_);
      }
      if (lev_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(4, lev_);
      }
      if (maxLev_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(5, maxLev_);
      }
      if (exp_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(6, exp_);
      }
      if (needExp_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(7, needExp_);
      }
      if (extraTimes_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(8, extraTimes_);
      }
      if (extraMaxTimes_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(9, extraMaxTimes_);
      }
      if (extraNeedExp_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(10, extraNeedExp_);
      }
      if (bundleId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(11, bundleId_);
      }
      for (int i = 0; i < extraReward_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(12, extraReward_.get(i));
      }
      for (int i = 0; i < freeReward_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(13, freeReward_.get(i));
      }
      for (int i = 0; i < highReward_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(14, highReward_.get(i));
      }
      for (int i = 0; i < taskList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(15, taskList_.get(i));
      }
      {
        int dataSize = 0;
        for (int i = 0; i < specialRewardList_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeUInt32SizeNoTag(specialRewardList_.getInt(i));
        }
        size += dataSize;
        size += 2 * getSpecialRewardListList().size();
      }
      if (totalExp_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(17, totalExp_);
      }
      if (ifBuy2_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(18, ifBuy2_);
      }
      for (int i = 0; i < highReward2_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(19, highReward2_.get(i));
      }
      if (mergeFlag_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(20, mergeFlag_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_s2c other = (org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_s2c) obj;

      if (getId()
          != other.getId()) return false;
      if (getIfBuy()
          != other.getIfBuy()) return false;
      if (getEndTime()
          != other.getEndTime()) return false;
      if (getLev()
          != other.getLev()) return false;
      if (getMaxLev()
          != other.getMaxLev()) return false;
      if (getExp()
          != other.getExp()) return false;
      if (getNeedExp()
          != other.getNeedExp()) return false;
      if (getExtraTimes()
          != other.getExtraTimes()) return false;
      if (getExtraMaxTimes()
          != other.getExtraMaxTimes()) return false;
      if (getExtraNeedExp()
          != other.getExtraNeedExp()) return false;
      if (getBundleId()
          != other.getBundleId()) return false;
      if (!getExtraRewardList()
          .equals(other.getExtraRewardList())) return false;
      if (!getFreeRewardList()
          .equals(other.getFreeRewardList())) return false;
      if (!getHighRewardList()
          .equals(other.getHighRewardList())) return false;
      if (!getTaskListList()
          .equals(other.getTaskListList())) return false;
      if (!getSpecialRewardListList()
          .equals(other.getSpecialRewardListList())) return false;
      if (getTotalExp()
          != other.getTotalExp()) return false;
      if (getIfBuy2()
          != other.getIfBuy2()) return false;
      if (!getHighReward2List()
          .equals(other.getHighReward2List())) return false;
      if (getMergeFlag()
          != other.getMergeFlag()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + getId();
      hash = (37 * hash) + IF_BUY_FIELD_NUMBER;
      hash = (53 * hash) + getIfBuy();
      hash = (37 * hash) + END_TIME_FIELD_NUMBER;
      hash = (53 * hash) + getEndTime();
      hash = (37 * hash) + LEV_FIELD_NUMBER;
      hash = (53 * hash) + getLev();
      hash = (37 * hash) + MAX_LEV_FIELD_NUMBER;
      hash = (53 * hash) + getMaxLev();
      hash = (37 * hash) + EXP_FIELD_NUMBER;
      hash = (53 * hash) + getExp();
      hash = (37 * hash) + NEED_EXP_FIELD_NUMBER;
      hash = (53 * hash) + getNeedExp();
      hash = (37 * hash) + EXTRA_TIMES_FIELD_NUMBER;
      hash = (53 * hash) + getExtraTimes();
      hash = (37 * hash) + EXTRA_MAX_TIMES_FIELD_NUMBER;
      hash = (53 * hash) + getExtraMaxTimes();
      hash = (37 * hash) + EXTRA_NEED_EXP_FIELD_NUMBER;
      hash = (53 * hash) + getExtraNeedExp();
      hash = (37 * hash) + BUNDLE_ID_FIELD_NUMBER;
      hash = (53 * hash) + getBundleId();
      if (getExtraRewardCount() > 0) {
        hash = (37 * hash) + EXTRA_REWARD_FIELD_NUMBER;
        hash = (53 * hash) + getExtraRewardList().hashCode();
      }
      if (getFreeRewardCount() > 0) {
        hash = (37 * hash) + FREE_REWARD_FIELD_NUMBER;
        hash = (53 * hash) + getFreeRewardList().hashCode();
      }
      if (getHighRewardCount() > 0) {
        hash = (37 * hash) + HIGH_REWARD_FIELD_NUMBER;
        hash = (53 * hash) + getHighRewardList().hashCode();
      }
      if (getTaskListCount() > 0) {
        hash = (37 * hash) + TASK_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getTaskListList().hashCode();
      }
      if (getSpecialRewardListCount() > 0) {
        hash = (37 * hash) + SPECIAL_REWARD_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getSpecialRewardListList().hashCode();
      }
      hash = (37 * hash) + TOTAL_EXP_FIELD_NUMBER;
      hash = (53 * hash) + getTotalExp();
      hash = (37 * hash) + IF_BUY_2_FIELD_NUMBER;
      hash = (53 * hash) + getIfBuy2();
      if (getHighReward2Count() > 0) {
        hash = (37 * hash) + HIGH_REWARD_2_FIELD_NUMBER;
        hash = (53 * hash) + getHighReward2List().hashCode();
      }
      hash = (37 * hash) + MERGE_FLAG_FIELD_NUMBER;
      hash = (53 * hash) + getMergeFlag();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.war_token_info_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.war_token_info_s2c)
        org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_info_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_info_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_s2c.class, org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        id_ = 0;
        ifBuy_ = 0;
        endTime_ = 0;
        lev_ = 0;
        maxLev_ = 0;
        exp_ = 0;
        needExp_ = 0;
        extraTimes_ = 0;
        extraMaxTimes_ = 0;
        extraNeedExp_ = 0;
        bundleId_ = 0;
        if (extraRewardBuilder_ == null) {
          extraReward_ = java.util.Collections.emptyList();
        } else {
          extraReward_ = null;
          extraRewardBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000800);
        if (freeRewardBuilder_ == null) {
          freeReward_ = java.util.Collections.emptyList();
        } else {
          freeReward_ = null;
          freeRewardBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00001000);
        if (highRewardBuilder_ == null) {
          highReward_ = java.util.Collections.emptyList();
        } else {
          highReward_ = null;
          highRewardBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00002000);
        if (taskListBuilder_ == null) {
          taskList_ = java.util.Collections.emptyList();
        } else {
          taskList_ = null;
          taskListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00004000);
        specialRewardList_ = emptyIntList();
        totalExp_ = 0;
        ifBuy2_ = 0;
        if (highReward2Builder_ == null) {
          highReward2_ = java.util.Collections.emptyList();
        } else {
          highReward2_ = null;
          highReward2Builder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00040000);
        mergeFlag_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_info_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_s2c build() {
        org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_s2c result = new org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_s2c(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_s2c result) {
        if (extraRewardBuilder_ == null) {
          if (((bitField0_ & 0x00000800) != 0)) {
            extraReward_ = java.util.Collections.unmodifiableList(extraReward_);
            bitField0_ = (bitField0_ & ~0x00000800);
          }
          result.extraReward_ = extraReward_;
        } else {
          result.extraReward_ = extraRewardBuilder_.build();
        }
        if (freeRewardBuilder_ == null) {
          if (((bitField0_ & 0x00001000) != 0)) {
            freeReward_ = java.util.Collections.unmodifiableList(freeReward_);
            bitField0_ = (bitField0_ & ~0x00001000);
          }
          result.freeReward_ = freeReward_;
        } else {
          result.freeReward_ = freeRewardBuilder_.build();
        }
        if (highRewardBuilder_ == null) {
          if (((bitField0_ & 0x00002000) != 0)) {
            highReward_ = java.util.Collections.unmodifiableList(highReward_);
            bitField0_ = (bitField0_ & ~0x00002000);
          }
          result.highReward_ = highReward_;
        } else {
          result.highReward_ = highRewardBuilder_.build();
        }
        if (taskListBuilder_ == null) {
          if (((bitField0_ & 0x00004000) != 0)) {
            taskList_ = java.util.Collections.unmodifiableList(taskList_);
            bitField0_ = (bitField0_ & ~0x00004000);
          }
          result.taskList_ = taskList_;
        } else {
          result.taskList_ = taskListBuilder_.build();
        }
        if (highReward2Builder_ == null) {
          if (((bitField0_ & 0x00040000) != 0)) {
            highReward2_ = java.util.Collections.unmodifiableList(highReward2_);
            bitField0_ = (bitField0_ & ~0x00040000);
          }
          result.highReward2_ = highReward2_;
        } else {
          result.highReward2_ = highReward2Builder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.id_ = id_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.ifBuy_ = ifBuy_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.endTime_ = endTime_;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.lev_ = lev_;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.maxLev_ = maxLev_;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.exp_ = exp_;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.needExp_ = needExp_;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          result.extraTimes_ = extraTimes_;
        }
        if (((from_bitField0_ & 0x00000100) != 0)) {
          result.extraMaxTimes_ = extraMaxTimes_;
        }
        if (((from_bitField0_ & 0x00000200) != 0)) {
          result.extraNeedExp_ = extraNeedExp_;
        }
        if (((from_bitField0_ & 0x00000400) != 0)) {
          result.bundleId_ = bundleId_;
        }
        if (((from_bitField0_ & 0x00008000) != 0)) {
          specialRewardList_.makeImmutable();
          result.specialRewardList_ = specialRewardList_;
        }
        if (((from_bitField0_ & 0x00010000) != 0)) {
          result.totalExp_ = totalExp_;
        }
        if (((from_bitField0_ & 0x00020000) != 0)) {
          result.ifBuy2_ = ifBuy2_;
        }
        if (((from_bitField0_ & 0x00080000) != 0)) {
          result.mergeFlag_ = mergeFlag_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_s2c.getDefaultInstance()) return this;
        if (other.getId() != 0) {
          setId(other.getId());
        }
        if (other.getIfBuy() != 0) {
          setIfBuy(other.getIfBuy());
        }
        if (other.getEndTime() != 0) {
          setEndTime(other.getEndTime());
        }
        if (other.getLev() != 0) {
          setLev(other.getLev());
        }
        if (other.getMaxLev() != 0) {
          setMaxLev(other.getMaxLev());
        }
        if (other.getExp() != 0) {
          setExp(other.getExp());
        }
        if (other.getNeedExp() != 0) {
          setNeedExp(other.getNeedExp());
        }
        if (other.getExtraTimes() != 0) {
          setExtraTimes(other.getExtraTimes());
        }
        if (other.getExtraMaxTimes() != 0) {
          setExtraMaxTimes(other.getExtraMaxTimes());
        }
        if (other.getExtraNeedExp() != 0) {
          setExtraNeedExp(other.getExtraNeedExp());
        }
        if (other.getBundleId() != 0) {
          setBundleId(other.getBundleId());
        }
        if (extraRewardBuilder_ == null) {
          if (!other.extraReward_.isEmpty()) {
            if (extraReward_.isEmpty()) {
              extraReward_ = other.extraReward_;
              bitField0_ = (bitField0_ & ~0x00000800);
            } else {
              ensureExtraRewardIsMutable();
              extraReward_.addAll(other.extraReward_);
            }
            onChanged();
          }
        } else {
          if (!other.extraReward_.isEmpty()) {
            if (extraRewardBuilder_.isEmpty()) {
              extraRewardBuilder_.dispose();
              extraRewardBuilder_ = null;
              extraReward_ = other.extraReward_;
              bitField0_ = (bitField0_ & ~0x00000800);
              extraRewardBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getExtraRewardFieldBuilder() : null;
            } else {
              extraRewardBuilder_.addAllMessages(other.extraReward_);
            }
          }
        }
        if (freeRewardBuilder_ == null) {
          if (!other.freeReward_.isEmpty()) {
            if (freeReward_.isEmpty()) {
              freeReward_ = other.freeReward_;
              bitField0_ = (bitField0_ & ~0x00001000);
            } else {
              ensureFreeRewardIsMutable();
              freeReward_.addAll(other.freeReward_);
            }
            onChanged();
          }
        } else {
          if (!other.freeReward_.isEmpty()) {
            if (freeRewardBuilder_.isEmpty()) {
              freeRewardBuilder_.dispose();
              freeRewardBuilder_ = null;
              freeReward_ = other.freeReward_;
              bitField0_ = (bitField0_ & ~0x00001000);
              freeRewardBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getFreeRewardFieldBuilder() : null;
            } else {
              freeRewardBuilder_.addAllMessages(other.freeReward_);
            }
          }
        }
        if (highRewardBuilder_ == null) {
          if (!other.highReward_.isEmpty()) {
            if (highReward_.isEmpty()) {
              highReward_ = other.highReward_;
              bitField0_ = (bitField0_ & ~0x00002000);
            } else {
              ensureHighRewardIsMutable();
              highReward_.addAll(other.highReward_);
            }
            onChanged();
          }
        } else {
          if (!other.highReward_.isEmpty()) {
            if (highRewardBuilder_.isEmpty()) {
              highRewardBuilder_.dispose();
              highRewardBuilder_ = null;
              highReward_ = other.highReward_;
              bitField0_ = (bitField0_ & ~0x00002000);
              highRewardBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getHighRewardFieldBuilder() : null;
            } else {
              highRewardBuilder_.addAllMessages(other.highReward_);
            }
          }
        }
        if (taskListBuilder_ == null) {
          if (!other.taskList_.isEmpty()) {
            if (taskList_.isEmpty()) {
              taskList_ = other.taskList_;
              bitField0_ = (bitField0_ & ~0x00004000);
            } else {
              ensureTaskListIsMutable();
              taskList_.addAll(other.taskList_);
            }
            onChanged();
          }
        } else {
          if (!other.taskList_.isEmpty()) {
            if (taskListBuilder_.isEmpty()) {
              taskListBuilder_.dispose();
              taskListBuilder_ = null;
              taskList_ = other.taskList_;
              bitField0_ = (bitField0_ & ~0x00004000);
              taskListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getTaskListFieldBuilder() : null;
            } else {
              taskListBuilder_.addAllMessages(other.taskList_);
            }
          }
        }
        if (!other.specialRewardList_.isEmpty()) {
          if (specialRewardList_.isEmpty()) {
            specialRewardList_ = other.specialRewardList_;
            specialRewardList_.makeImmutable();
            bitField0_ |= 0x00008000;
          } else {
            ensureSpecialRewardListIsMutable();
            specialRewardList_.addAll(other.specialRewardList_);
          }
          onChanged();
        }
        if (other.getTotalExp() != 0) {
          setTotalExp(other.getTotalExp());
        }
        if (other.getIfBuy2() != 0) {
          setIfBuy2(other.getIfBuy2());
        }
        if (highReward2Builder_ == null) {
          if (!other.highReward2_.isEmpty()) {
            if (highReward2_.isEmpty()) {
              highReward2_ = other.highReward2_;
              bitField0_ = (bitField0_ & ~0x00040000);
            } else {
              ensureHighReward2IsMutable();
              highReward2_.addAll(other.highReward2_);
            }
            onChanged();
          }
        } else {
          if (!other.highReward2_.isEmpty()) {
            if (highReward2Builder_.isEmpty()) {
              highReward2Builder_.dispose();
              highReward2Builder_ = null;
              highReward2_ = other.highReward2_;
              bitField0_ = (bitField0_ & ~0x00040000);
              highReward2Builder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getHighReward2FieldBuilder() : null;
            } else {
              highReward2Builder_.addAllMessages(other.highReward2_);
            }
          }
        }
        if (other.getMergeFlag() != 0) {
          setMergeFlag(other.getMergeFlag());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                id_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                ifBuy_ = input.readUInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                endTime_ = input.readUInt32();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              case 32: {
                lev_ = input.readUInt32();
                bitField0_ |= 0x00000008;
                break;
              } // case 32
              case 40: {
                maxLev_ = input.readUInt32();
                bitField0_ |= 0x00000010;
                break;
              } // case 40
              case 48: {
                exp_ = input.readUInt32();
                bitField0_ |= 0x00000020;
                break;
              } // case 48
              case 56: {
                needExp_ = input.readUInt32();
                bitField0_ |= 0x00000040;
                break;
              } // case 56
              case 64: {
                extraTimes_ = input.readUInt32();
                bitField0_ |= 0x00000080;
                break;
              } // case 64
              case 72: {
                extraMaxTimes_ = input.readUInt32();
                bitField0_ |= 0x00000100;
                break;
              } // case 72
              case 80: {
                extraNeedExp_ = input.readUInt32();
                bitField0_ |= 0x00000200;
                break;
              } // case 80
              case 88: {
                bundleId_ = input.readUInt32();
                bitField0_ |= 0x00000400;
                break;
              } // case 88
              case 98: {
                org.gof.demo.worldsrv.msg.Define.p_reward m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_reward.parser(),
                        extensionRegistry);
                if (extraRewardBuilder_ == null) {
                  ensureExtraRewardIsMutable();
                  extraReward_.add(m);
                } else {
                  extraRewardBuilder_.addMessage(m);
                }
                break;
              } // case 98
              case 106: {
                org.gof.demo.worldsrv.msg.Define.p_reward_status_list m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_reward_status_list.parser(),
                        extensionRegistry);
                if (freeRewardBuilder_ == null) {
                  ensureFreeRewardIsMutable();
                  freeReward_.add(m);
                } else {
                  freeRewardBuilder_.addMessage(m);
                }
                break;
              } // case 106
              case 114: {
                org.gof.demo.worldsrv.msg.Define.p_reward_status_list m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_reward_status_list.parser(),
                        extensionRegistry);
                if (highRewardBuilder_ == null) {
                  ensureHighRewardIsMutable();
                  highReward_.add(m);
                } else {
                  highRewardBuilder_.addMessage(m);
                }
                break;
              } // case 114
              case 122: {
                org.gof.demo.worldsrv.msg.Define.p_war_token_task m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_war_token_task.parser(),
                        extensionRegistry);
                if (taskListBuilder_ == null) {
                  ensureTaskListIsMutable();
                  taskList_.add(m);
                } else {
                  taskListBuilder_.addMessage(m);
                }
                break;
              } // case 122
              case 128: {
                int v = input.readUInt32();
                ensureSpecialRewardListIsMutable();
                specialRewardList_.addInt(v);
                break;
              } // case 128
              case 130: {
                int length = input.readRawVarint32();
                int limit = input.pushLimit(length);
                ensureSpecialRewardListIsMutable();
                while (input.getBytesUntilLimit() > 0) {
                  specialRewardList_.addInt(input.readUInt32());
                }
                input.popLimit(limit);
                break;
              } // case 130
              case 136: {
                totalExp_ = input.readUInt32();
                bitField0_ |= 0x00010000;
                break;
              } // case 136
              case 144: {
                ifBuy2_ = input.readUInt32();
                bitField0_ |= 0x00020000;
                break;
              } // case 144
              case 154: {
                org.gof.demo.worldsrv.msg.Define.p_reward_status_list m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_reward_status_list.parser(),
                        extensionRegistry);
                if (highReward2Builder_ == null) {
                  ensureHighReward2IsMutable();
                  highReward2_.add(m);
                } else {
                  highReward2Builder_.addMessage(m);
                }
                break;
              } // case 154
              case 160: {
                mergeFlag_ = input.readUInt32();
                bitField0_ |= 0x00080000;
                break;
              } // case 160
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int id_ ;
      /**
       * <code>uint32 id = 1;</code>
       * @return The id.
       */
      @java.lang.Override
      public int getId() {
        return id_;
      }
      /**
       * <code>uint32 id = 1;</code>
       * @param value The id to set.
       * @return This builder for chaining.
       */
      public Builder setId(int value) {

        id_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        id_ = 0;
        onChanged();
        return this;
      }

      private int ifBuy_ ;
      /**
       * <code>uint32 if_buy = 2;</code>
       * @return The ifBuy.
       */
      @java.lang.Override
      public int getIfBuy() {
        return ifBuy_;
      }
      /**
       * <code>uint32 if_buy = 2;</code>
       * @param value The ifBuy to set.
       * @return This builder for chaining.
       */
      public Builder setIfBuy(int value) {

        ifBuy_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 if_buy = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearIfBuy() {
        bitField0_ = (bitField0_ & ~0x00000002);
        ifBuy_ = 0;
        onChanged();
        return this;
      }

      private int endTime_ ;
      /**
       * <code>uint32 end_time = 3;</code>
       * @return The endTime.
       */
      @java.lang.Override
      public int getEndTime() {
        return endTime_;
      }
      /**
       * <code>uint32 end_time = 3;</code>
       * @param value The endTime to set.
       * @return This builder for chaining.
       */
      public Builder setEndTime(int value) {

        endTime_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 end_time = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearEndTime() {
        bitField0_ = (bitField0_ & ~0x00000004);
        endTime_ = 0;
        onChanged();
        return this;
      }

      private int lev_ ;
      /**
       * <code>uint32 lev = 4;</code>
       * @return The lev.
       */
      @java.lang.Override
      public int getLev() {
        return lev_;
      }
      /**
       * <code>uint32 lev = 4;</code>
       * @param value The lev to set.
       * @return This builder for chaining.
       */
      public Builder setLev(int value) {

        lev_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 lev = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearLev() {
        bitField0_ = (bitField0_ & ~0x00000008);
        lev_ = 0;
        onChanged();
        return this;
      }

      private int maxLev_ ;
      /**
       * <code>uint32 max_lev = 5;</code>
       * @return The maxLev.
       */
      @java.lang.Override
      public int getMaxLev() {
        return maxLev_;
      }
      /**
       * <code>uint32 max_lev = 5;</code>
       * @param value The maxLev to set.
       * @return This builder for chaining.
       */
      public Builder setMaxLev(int value) {

        maxLev_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 max_lev = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearMaxLev() {
        bitField0_ = (bitField0_ & ~0x00000010);
        maxLev_ = 0;
        onChanged();
        return this;
      }

      private int exp_ ;
      /**
       * <code>uint32 exp = 6;</code>
       * @return The exp.
       */
      @java.lang.Override
      public int getExp() {
        return exp_;
      }
      /**
       * <code>uint32 exp = 6;</code>
       * @param value The exp to set.
       * @return This builder for chaining.
       */
      public Builder setExp(int value) {

        exp_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 exp = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearExp() {
        bitField0_ = (bitField0_ & ~0x00000020);
        exp_ = 0;
        onChanged();
        return this;
      }

      private int needExp_ ;
      /**
       * <code>uint32 need_exp = 7;</code>
       * @return The needExp.
       */
      @java.lang.Override
      public int getNeedExp() {
        return needExp_;
      }
      /**
       * <code>uint32 need_exp = 7;</code>
       * @param value The needExp to set.
       * @return This builder for chaining.
       */
      public Builder setNeedExp(int value) {

        needExp_ = value;
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 need_exp = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearNeedExp() {
        bitField0_ = (bitField0_ & ~0x00000040);
        needExp_ = 0;
        onChanged();
        return this;
      }

      private int extraTimes_ ;
      /**
       * <code>uint32 extra_times = 8;</code>
       * @return The extraTimes.
       */
      @java.lang.Override
      public int getExtraTimes() {
        return extraTimes_;
      }
      /**
       * <code>uint32 extra_times = 8;</code>
       * @param value The extraTimes to set.
       * @return This builder for chaining.
       */
      public Builder setExtraTimes(int value) {

        extraTimes_ = value;
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 extra_times = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearExtraTimes() {
        bitField0_ = (bitField0_ & ~0x00000080);
        extraTimes_ = 0;
        onChanged();
        return this;
      }

      private int extraMaxTimes_ ;
      /**
       * <code>uint32 extra_max_times = 9;</code>
       * @return The extraMaxTimes.
       */
      @java.lang.Override
      public int getExtraMaxTimes() {
        return extraMaxTimes_;
      }
      /**
       * <code>uint32 extra_max_times = 9;</code>
       * @param value The extraMaxTimes to set.
       * @return This builder for chaining.
       */
      public Builder setExtraMaxTimes(int value) {

        extraMaxTimes_ = value;
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 extra_max_times = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearExtraMaxTimes() {
        bitField0_ = (bitField0_ & ~0x00000100);
        extraMaxTimes_ = 0;
        onChanged();
        return this;
      }

      private int extraNeedExp_ ;
      /**
       * <code>uint32 extra_need_exp = 10;</code>
       * @return The extraNeedExp.
       */
      @java.lang.Override
      public int getExtraNeedExp() {
        return extraNeedExp_;
      }
      /**
       * <code>uint32 extra_need_exp = 10;</code>
       * @param value The extraNeedExp to set.
       * @return This builder for chaining.
       */
      public Builder setExtraNeedExp(int value) {

        extraNeedExp_ = value;
        bitField0_ |= 0x00000200;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 extra_need_exp = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearExtraNeedExp() {
        bitField0_ = (bitField0_ & ~0x00000200);
        extraNeedExp_ = 0;
        onChanged();
        return this;
      }

      private int bundleId_ ;
      /**
       * <code>uint32 bundle_id = 11;</code>
       * @return The bundleId.
       */
      @java.lang.Override
      public int getBundleId() {
        return bundleId_;
      }
      /**
       * <code>uint32 bundle_id = 11;</code>
       * @param value The bundleId to set.
       * @return This builder for chaining.
       */
      public Builder setBundleId(int value) {

        bundleId_ = value;
        bitField0_ |= 0x00000400;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 bundle_id = 11;</code>
       * @return This builder for chaining.
       */
      public Builder clearBundleId() {
        bitField0_ = (bitField0_ & ~0x00000400);
        bundleId_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward> extraReward_ =
        java.util.Collections.emptyList();
      private void ensureExtraRewardIsMutable() {
        if (!((bitField0_ & 0x00000800) != 0)) {
          extraReward_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_reward>(extraReward_);
          bitField0_ |= 0x00000800;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_reward, org.gof.demo.worldsrv.msg.Define.p_reward.Builder, org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder> extraRewardBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward extra_reward = 12;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward> getExtraRewardList() {
        if (extraRewardBuilder_ == null) {
          return java.util.Collections.unmodifiableList(extraReward_);
        } else {
          return extraRewardBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward extra_reward = 12;</code>
       */
      public int getExtraRewardCount() {
        if (extraRewardBuilder_ == null) {
          return extraReward_.size();
        } else {
          return extraRewardBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward extra_reward = 12;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_reward getExtraReward(int index) {
        if (extraRewardBuilder_ == null) {
          return extraReward_.get(index);
        } else {
          return extraRewardBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward extra_reward = 12;</code>
       */
      public Builder setExtraReward(
          int index, org.gof.demo.worldsrv.msg.Define.p_reward value) {
        if (extraRewardBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureExtraRewardIsMutable();
          extraReward_.set(index, value);
          onChanged();
        } else {
          extraRewardBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward extra_reward = 12;</code>
       */
      public Builder setExtraReward(
          int index, org.gof.demo.worldsrv.msg.Define.p_reward.Builder builderForValue) {
        if (extraRewardBuilder_ == null) {
          ensureExtraRewardIsMutable();
          extraReward_.set(index, builderForValue.build());
          onChanged();
        } else {
          extraRewardBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward extra_reward = 12;</code>
       */
      public Builder addExtraReward(org.gof.demo.worldsrv.msg.Define.p_reward value) {
        if (extraRewardBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureExtraRewardIsMutable();
          extraReward_.add(value);
          onChanged();
        } else {
          extraRewardBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward extra_reward = 12;</code>
       */
      public Builder addExtraReward(
          int index, org.gof.demo.worldsrv.msg.Define.p_reward value) {
        if (extraRewardBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureExtraRewardIsMutable();
          extraReward_.add(index, value);
          onChanged();
        } else {
          extraRewardBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward extra_reward = 12;</code>
       */
      public Builder addExtraReward(
          org.gof.demo.worldsrv.msg.Define.p_reward.Builder builderForValue) {
        if (extraRewardBuilder_ == null) {
          ensureExtraRewardIsMutable();
          extraReward_.add(builderForValue.build());
          onChanged();
        } else {
          extraRewardBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward extra_reward = 12;</code>
       */
      public Builder addExtraReward(
          int index, org.gof.demo.worldsrv.msg.Define.p_reward.Builder builderForValue) {
        if (extraRewardBuilder_ == null) {
          ensureExtraRewardIsMutable();
          extraReward_.add(index, builderForValue.build());
          onChanged();
        } else {
          extraRewardBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward extra_reward = 12;</code>
       */
      public Builder addAllExtraReward(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_reward> values) {
        if (extraRewardBuilder_ == null) {
          ensureExtraRewardIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, extraReward_);
          onChanged();
        } else {
          extraRewardBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward extra_reward = 12;</code>
       */
      public Builder clearExtraReward() {
        if (extraRewardBuilder_ == null) {
          extraReward_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000800);
          onChanged();
        } else {
          extraRewardBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward extra_reward = 12;</code>
       */
      public Builder removeExtraReward(int index) {
        if (extraRewardBuilder_ == null) {
          ensureExtraRewardIsMutable();
          extraReward_.remove(index);
          onChanged();
        } else {
          extraRewardBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward extra_reward = 12;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_reward.Builder getExtraRewardBuilder(
          int index) {
        return getExtraRewardFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward extra_reward = 12;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder getExtraRewardOrBuilder(
          int index) {
        if (extraRewardBuilder_ == null) {
          return extraReward_.get(index);  } else {
          return extraRewardBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward extra_reward = 12;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder> 
           getExtraRewardOrBuilderList() {
        if (extraRewardBuilder_ != null) {
          return extraRewardBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(extraReward_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward extra_reward = 12;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_reward.Builder addExtraRewardBuilder() {
        return getExtraRewardFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_reward.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward extra_reward = 12;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_reward.Builder addExtraRewardBuilder(
          int index) {
        return getExtraRewardFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_reward.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward extra_reward = 12;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward.Builder> 
           getExtraRewardBuilderList() {
        return getExtraRewardFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_reward, org.gof.demo.worldsrv.msg.Define.p_reward.Builder, org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder> 
          getExtraRewardFieldBuilder() {
        if (extraRewardBuilder_ == null) {
          extraRewardBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_reward, org.gof.demo.worldsrv.msg.Define.p_reward.Builder, org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder>(
                  extraReward_,
                  ((bitField0_ & 0x00000800) != 0),
                  getParentForChildren(),
                  isClean());
          extraReward_ = null;
        }
        return extraRewardBuilder_;
      }

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward_status_list> freeReward_ =
        java.util.Collections.emptyList();
      private void ensureFreeRewardIsMutable() {
        if (!((bitField0_ & 0x00001000) != 0)) {
          freeReward_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_reward_status_list>(freeReward_);
          bitField0_ |= 0x00001000;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_reward_status_list, org.gof.demo.worldsrv.msg.Define.p_reward_status_list.Builder, org.gof.demo.worldsrv.msg.Define.p_reward_status_listOrBuilder> freeRewardBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list free_reward = 13;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward_status_list> getFreeRewardList() {
        if (freeRewardBuilder_ == null) {
          return java.util.Collections.unmodifiableList(freeReward_);
        } else {
          return freeRewardBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list free_reward = 13;</code>
       */
      public int getFreeRewardCount() {
        if (freeRewardBuilder_ == null) {
          return freeReward_.size();
        } else {
          return freeRewardBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list free_reward = 13;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_reward_status_list getFreeReward(int index) {
        if (freeRewardBuilder_ == null) {
          return freeReward_.get(index);
        } else {
          return freeRewardBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list free_reward = 13;</code>
       */
      public Builder setFreeReward(
          int index, org.gof.demo.worldsrv.msg.Define.p_reward_status_list value) {
        if (freeRewardBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureFreeRewardIsMutable();
          freeReward_.set(index, value);
          onChanged();
        } else {
          freeRewardBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list free_reward = 13;</code>
       */
      public Builder setFreeReward(
          int index, org.gof.demo.worldsrv.msg.Define.p_reward_status_list.Builder builderForValue) {
        if (freeRewardBuilder_ == null) {
          ensureFreeRewardIsMutable();
          freeReward_.set(index, builderForValue.build());
          onChanged();
        } else {
          freeRewardBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list free_reward = 13;</code>
       */
      public Builder addFreeReward(org.gof.demo.worldsrv.msg.Define.p_reward_status_list value) {
        if (freeRewardBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureFreeRewardIsMutable();
          freeReward_.add(value);
          onChanged();
        } else {
          freeRewardBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list free_reward = 13;</code>
       */
      public Builder addFreeReward(
          int index, org.gof.demo.worldsrv.msg.Define.p_reward_status_list value) {
        if (freeRewardBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureFreeRewardIsMutable();
          freeReward_.add(index, value);
          onChanged();
        } else {
          freeRewardBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list free_reward = 13;</code>
       */
      public Builder addFreeReward(
          org.gof.demo.worldsrv.msg.Define.p_reward_status_list.Builder builderForValue) {
        if (freeRewardBuilder_ == null) {
          ensureFreeRewardIsMutable();
          freeReward_.add(builderForValue.build());
          onChanged();
        } else {
          freeRewardBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list free_reward = 13;</code>
       */
      public Builder addFreeReward(
          int index, org.gof.demo.worldsrv.msg.Define.p_reward_status_list.Builder builderForValue) {
        if (freeRewardBuilder_ == null) {
          ensureFreeRewardIsMutable();
          freeReward_.add(index, builderForValue.build());
          onChanged();
        } else {
          freeRewardBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list free_reward = 13;</code>
       */
      public Builder addAllFreeReward(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_reward_status_list> values) {
        if (freeRewardBuilder_ == null) {
          ensureFreeRewardIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, freeReward_);
          onChanged();
        } else {
          freeRewardBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list free_reward = 13;</code>
       */
      public Builder clearFreeReward() {
        if (freeRewardBuilder_ == null) {
          freeReward_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00001000);
          onChanged();
        } else {
          freeRewardBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list free_reward = 13;</code>
       */
      public Builder removeFreeReward(int index) {
        if (freeRewardBuilder_ == null) {
          ensureFreeRewardIsMutable();
          freeReward_.remove(index);
          onChanged();
        } else {
          freeRewardBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list free_reward = 13;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_reward_status_list.Builder getFreeRewardBuilder(
          int index) {
        return getFreeRewardFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list free_reward = 13;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_reward_status_listOrBuilder getFreeRewardOrBuilder(
          int index) {
        if (freeRewardBuilder_ == null) {
          return freeReward_.get(index);  } else {
          return freeRewardBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list free_reward = 13;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_reward_status_listOrBuilder> 
           getFreeRewardOrBuilderList() {
        if (freeRewardBuilder_ != null) {
          return freeRewardBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(freeReward_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list free_reward = 13;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_reward_status_list.Builder addFreeRewardBuilder() {
        return getFreeRewardFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_reward_status_list.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list free_reward = 13;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_reward_status_list.Builder addFreeRewardBuilder(
          int index) {
        return getFreeRewardFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_reward_status_list.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list free_reward = 13;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward_status_list.Builder> 
           getFreeRewardBuilderList() {
        return getFreeRewardFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_reward_status_list, org.gof.demo.worldsrv.msg.Define.p_reward_status_list.Builder, org.gof.demo.worldsrv.msg.Define.p_reward_status_listOrBuilder> 
          getFreeRewardFieldBuilder() {
        if (freeRewardBuilder_ == null) {
          freeRewardBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_reward_status_list, org.gof.demo.worldsrv.msg.Define.p_reward_status_list.Builder, org.gof.demo.worldsrv.msg.Define.p_reward_status_listOrBuilder>(
                  freeReward_,
                  ((bitField0_ & 0x00001000) != 0),
                  getParentForChildren(),
                  isClean());
          freeReward_ = null;
        }
        return freeRewardBuilder_;
      }

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward_status_list> highReward_ =
        java.util.Collections.emptyList();
      private void ensureHighRewardIsMutable() {
        if (!((bitField0_ & 0x00002000) != 0)) {
          highReward_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_reward_status_list>(highReward_);
          bitField0_ |= 0x00002000;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_reward_status_list, org.gof.demo.worldsrv.msg.Define.p_reward_status_list.Builder, org.gof.demo.worldsrv.msg.Define.p_reward_status_listOrBuilder> highRewardBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward = 14;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward_status_list> getHighRewardList() {
        if (highRewardBuilder_ == null) {
          return java.util.Collections.unmodifiableList(highReward_);
        } else {
          return highRewardBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward = 14;</code>
       */
      public int getHighRewardCount() {
        if (highRewardBuilder_ == null) {
          return highReward_.size();
        } else {
          return highRewardBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward = 14;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_reward_status_list getHighReward(int index) {
        if (highRewardBuilder_ == null) {
          return highReward_.get(index);
        } else {
          return highRewardBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward = 14;</code>
       */
      public Builder setHighReward(
          int index, org.gof.demo.worldsrv.msg.Define.p_reward_status_list value) {
        if (highRewardBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureHighRewardIsMutable();
          highReward_.set(index, value);
          onChanged();
        } else {
          highRewardBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward = 14;</code>
       */
      public Builder setHighReward(
          int index, org.gof.demo.worldsrv.msg.Define.p_reward_status_list.Builder builderForValue) {
        if (highRewardBuilder_ == null) {
          ensureHighRewardIsMutable();
          highReward_.set(index, builderForValue.build());
          onChanged();
        } else {
          highRewardBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward = 14;</code>
       */
      public Builder addHighReward(org.gof.demo.worldsrv.msg.Define.p_reward_status_list value) {
        if (highRewardBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureHighRewardIsMutable();
          highReward_.add(value);
          onChanged();
        } else {
          highRewardBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward = 14;</code>
       */
      public Builder addHighReward(
          int index, org.gof.demo.worldsrv.msg.Define.p_reward_status_list value) {
        if (highRewardBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureHighRewardIsMutable();
          highReward_.add(index, value);
          onChanged();
        } else {
          highRewardBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward = 14;</code>
       */
      public Builder addHighReward(
          org.gof.demo.worldsrv.msg.Define.p_reward_status_list.Builder builderForValue) {
        if (highRewardBuilder_ == null) {
          ensureHighRewardIsMutable();
          highReward_.add(builderForValue.build());
          onChanged();
        } else {
          highRewardBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward = 14;</code>
       */
      public Builder addHighReward(
          int index, org.gof.demo.worldsrv.msg.Define.p_reward_status_list.Builder builderForValue) {
        if (highRewardBuilder_ == null) {
          ensureHighRewardIsMutable();
          highReward_.add(index, builderForValue.build());
          onChanged();
        } else {
          highRewardBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward = 14;</code>
       */
      public Builder addAllHighReward(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_reward_status_list> values) {
        if (highRewardBuilder_ == null) {
          ensureHighRewardIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, highReward_);
          onChanged();
        } else {
          highRewardBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward = 14;</code>
       */
      public Builder clearHighReward() {
        if (highRewardBuilder_ == null) {
          highReward_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00002000);
          onChanged();
        } else {
          highRewardBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward = 14;</code>
       */
      public Builder removeHighReward(int index) {
        if (highRewardBuilder_ == null) {
          ensureHighRewardIsMutable();
          highReward_.remove(index);
          onChanged();
        } else {
          highRewardBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward = 14;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_reward_status_list.Builder getHighRewardBuilder(
          int index) {
        return getHighRewardFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward = 14;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_reward_status_listOrBuilder getHighRewardOrBuilder(
          int index) {
        if (highRewardBuilder_ == null) {
          return highReward_.get(index);  } else {
          return highRewardBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward = 14;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_reward_status_listOrBuilder> 
           getHighRewardOrBuilderList() {
        if (highRewardBuilder_ != null) {
          return highRewardBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(highReward_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward = 14;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_reward_status_list.Builder addHighRewardBuilder() {
        return getHighRewardFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_reward_status_list.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward = 14;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_reward_status_list.Builder addHighRewardBuilder(
          int index) {
        return getHighRewardFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_reward_status_list.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward = 14;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward_status_list.Builder> 
           getHighRewardBuilderList() {
        return getHighRewardFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_reward_status_list, org.gof.demo.worldsrv.msg.Define.p_reward_status_list.Builder, org.gof.demo.worldsrv.msg.Define.p_reward_status_listOrBuilder> 
          getHighRewardFieldBuilder() {
        if (highRewardBuilder_ == null) {
          highRewardBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_reward_status_list, org.gof.demo.worldsrv.msg.Define.p_reward_status_list.Builder, org.gof.demo.worldsrv.msg.Define.p_reward_status_listOrBuilder>(
                  highReward_,
                  ((bitField0_ & 0x00002000) != 0),
                  getParentForChildren(),
                  isClean());
          highReward_ = null;
        }
        return highRewardBuilder_;
      }

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_war_token_task> taskList_ =
        java.util.Collections.emptyList();
      private void ensureTaskListIsMutable() {
        if (!((bitField0_ & 0x00004000) != 0)) {
          taskList_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_war_token_task>(taskList_);
          bitField0_ |= 0x00004000;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_war_token_task, org.gof.demo.worldsrv.msg.Define.p_war_token_task.Builder, org.gof.demo.worldsrv.msg.Define.p_war_token_taskOrBuilder> taskListBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 15;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_war_token_task> getTaskListList() {
        if (taskListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(taskList_);
        } else {
          return taskListBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 15;</code>
       */
      public int getTaskListCount() {
        if (taskListBuilder_ == null) {
          return taskList_.size();
        } else {
          return taskListBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 15;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_war_token_task getTaskList(int index) {
        if (taskListBuilder_ == null) {
          return taskList_.get(index);
        } else {
          return taskListBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 15;</code>
       */
      public Builder setTaskList(
          int index, org.gof.demo.worldsrv.msg.Define.p_war_token_task value) {
        if (taskListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTaskListIsMutable();
          taskList_.set(index, value);
          onChanged();
        } else {
          taskListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 15;</code>
       */
      public Builder setTaskList(
          int index, org.gof.demo.worldsrv.msg.Define.p_war_token_task.Builder builderForValue) {
        if (taskListBuilder_ == null) {
          ensureTaskListIsMutable();
          taskList_.set(index, builderForValue.build());
          onChanged();
        } else {
          taskListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 15;</code>
       */
      public Builder addTaskList(org.gof.demo.worldsrv.msg.Define.p_war_token_task value) {
        if (taskListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTaskListIsMutable();
          taskList_.add(value);
          onChanged();
        } else {
          taskListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 15;</code>
       */
      public Builder addTaskList(
          int index, org.gof.demo.worldsrv.msg.Define.p_war_token_task value) {
        if (taskListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTaskListIsMutable();
          taskList_.add(index, value);
          onChanged();
        } else {
          taskListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 15;</code>
       */
      public Builder addTaskList(
          org.gof.demo.worldsrv.msg.Define.p_war_token_task.Builder builderForValue) {
        if (taskListBuilder_ == null) {
          ensureTaskListIsMutable();
          taskList_.add(builderForValue.build());
          onChanged();
        } else {
          taskListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 15;</code>
       */
      public Builder addTaskList(
          int index, org.gof.demo.worldsrv.msg.Define.p_war_token_task.Builder builderForValue) {
        if (taskListBuilder_ == null) {
          ensureTaskListIsMutable();
          taskList_.add(index, builderForValue.build());
          onChanged();
        } else {
          taskListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 15;</code>
       */
      public Builder addAllTaskList(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_war_token_task> values) {
        if (taskListBuilder_ == null) {
          ensureTaskListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, taskList_);
          onChanged();
        } else {
          taskListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 15;</code>
       */
      public Builder clearTaskList() {
        if (taskListBuilder_ == null) {
          taskList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00004000);
          onChanged();
        } else {
          taskListBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 15;</code>
       */
      public Builder removeTaskList(int index) {
        if (taskListBuilder_ == null) {
          ensureTaskListIsMutable();
          taskList_.remove(index);
          onChanged();
        } else {
          taskListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 15;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_war_token_task.Builder getTaskListBuilder(
          int index) {
        return getTaskListFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 15;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_war_token_taskOrBuilder getTaskListOrBuilder(
          int index) {
        if (taskListBuilder_ == null) {
          return taskList_.get(index);  } else {
          return taskListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 15;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_war_token_taskOrBuilder> 
           getTaskListOrBuilderList() {
        if (taskListBuilder_ != null) {
          return taskListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(taskList_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 15;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_war_token_task.Builder addTaskListBuilder() {
        return getTaskListFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_war_token_task.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 15;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_war_token_task.Builder addTaskListBuilder(
          int index) {
        return getTaskListFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_war_token_task.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 15;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_war_token_task.Builder> 
           getTaskListBuilderList() {
        return getTaskListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_war_token_task, org.gof.demo.worldsrv.msg.Define.p_war_token_task.Builder, org.gof.demo.worldsrv.msg.Define.p_war_token_taskOrBuilder> 
          getTaskListFieldBuilder() {
        if (taskListBuilder_ == null) {
          taskListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_war_token_task, org.gof.demo.worldsrv.msg.Define.p_war_token_task.Builder, org.gof.demo.worldsrv.msg.Define.p_war_token_taskOrBuilder>(
                  taskList_,
                  ((bitField0_ & 0x00004000) != 0),
                  getParentForChildren(),
                  isClean());
          taskList_ = null;
        }
        return taskListBuilder_;
      }

      private com.google.protobuf.Internal.IntList specialRewardList_ = emptyIntList();
      private void ensureSpecialRewardListIsMutable() {
        if (!specialRewardList_.isModifiable()) {
          specialRewardList_ = makeMutableCopy(specialRewardList_);
        }
        bitField0_ |= 0x00008000;
      }
      /**
       * <code>repeated uint32 special_reward_list = 16 [packed = false];</code>
       * @return A list containing the specialRewardList.
       */
      public java.util.List<java.lang.Integer>
          getSpecialRewardListList() {
        specialRewardList_.makeImmutable();
        return specialRewardList_;
      }
      /**
       * <code>repeated uint32 special_reward_list = 16 [packed = false];</code>
       * @return The count of specialRewardList.
       */
      public int getSpecialRewardListCount() {
        return specialRewardList_.size();
      }
      /**
       * <code>repeated uint32 special_reward_list = 16 [packed = false];</code>
       * @param index The index of the element to return.
       * @return The specialRewardList at the given index.
       */
      public int getSpecialRewardList(int index) {
        return specialRewardList_.getInt(index);
      }
      /**
       * <code>repeated uint32 special_reward_list = 16 [packed = false];</code>
       * @param index The index to set the value at.
       * @param value The specialRewardList to set.
       * @return This builder for chaining.
       */
      public Builder setSpecialRewardList(
          int index, int value) {

        ensureSpecialRewardListIsMutable();
        specialRewardList_.setInt(index, value);
        bitField0_ |= 0x00008000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint32 special_reward_list = 16 [packed = false];</code>
       * @param value The specialRewardList to add.
       * @return This builder for chaining.
       */
      public Builder addSpecialRewardList(int value) {

        ensureSpecialRewardListIsMutable();
        specialRewardList_.addInt(value);
        bitField0_ |= 0x00008000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint32 special_reward_list = 16 [packed = false];</code>
       * @param values The specialRewardList to add.
       * @return This builder for chaining.
       */
      public Builder addAllSpecialRewardList(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureSpecialRewardListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, specialRewardList_);
        bitField0_ |= 0x00008000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint32 special_reward_list = 16 [packed = false];</code>
       * @return This builder for chaining.
       */
      public Builder clearSpecialRewardList() {
        specialRewardList_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00008000);
        onChanged();
        return this;
      }

      private int totalExp_ ;
      /**
       * <code>uint32 total_exp = 17;</code>
       * @return The totalExp.
       */
      @java.lang.Override
      public int getTotalExp() {
        return totalExp_;
      }
      /**
       * <code>uint32 total_exp = 17;</code>
       * @param value The totalExp to set.
       * @return This builder for chaining.
       */
      public Builder setTotalExp(int value) {

        totalExp_ = value;
        bitField0_ |= 0x00010000;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 total_exp = 17;</code>
       * @return This builder for chaining.
       */
      public Builder clearTotalExp() {
        bitField0_ = (bitField0_ & ~0x00010000);
        totalExp_ = 0;
        onChanged();
        return this;
      }

      private int ifBuy2_ ;
      /**
       * <code>uint32 if_buy_2 = 18;</code>
       * @return The ifBuy2.
       */
      @java.lang.Override
      public int getIfBuy2() {
        return ifBuy2_;
      }
      /**
       * <code>uint32 if_buy_2 = 18;</code>
       * @param value The ifBuy2 to set.
       * @return This builder for chaining.
       */
      public Builder setIfBuy2(int value) {

        ifBuy2_ = value;
        bitField0_ |= 0x00020000;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 if_buy_2 = 18;</code>
       * @return This builder for chaining.
       */
      public Builder clearIfBuy2() {
        bitField0_ = (bitField0_ & ~0x00020000);
        ifBuy2_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward_status_list> highReward2_ =
        java.util.Collections.emptyList();
      private void ensureHighReward2IsMutable() {
        if (!((bitField0_ & 0x00040000) != 0)) {
          highReward2_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_reward_status_list>(highReward2_);
          bitField0_ |= 0x00040000;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_reward_status_list, org.gof.demo.worldsrv.msg.Define.p_reward_status_list.Builder, org.gof.demo.worldsrv.msg.Define.p_reward_status_listOrBuilder> highReward2Builder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward_2 = 19;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward_status_list> getHighReward2List() {
        if (highReward2Builder_ == null) {
          return java.util.Collections.unmodifiableList(highReward2_);
        } else {
          return highReward2Builder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward_2 = 19;</code>
       */
      public int getHighReward2Count() {
        if (highReward2Builder_ == null) {
          return highReward2_.size();
        } else {
          return highReward2Builder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward_2 = 19;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_reward_status_list getHighReward2(int index) {
        if (highReward2Builder_ == null) {
          return highReward2_.get(index);
        } else {
          return highReward2Builder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward_2 = 19;</code>
       */
      public Builder setHighReward2(
          int index, org.gof.demo.worldsrv.msg.Define.p_reward_status_list value) {
        if (highReward2Builder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureHighReward2IsMutable();
          highReward2_.set(index, value);
          onChanged();
        } else {
          highReward2Builder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward_2 = 19;</code>
       */
      public Builder setHighReward2(
          int index, org.gof.demo.worldsrv.msg.Define.p_reward_status_list.Builder builderForValue) {
        if (highReward2Builder_ == null) {
          ensureHighReward2IsMutable();
          highReward2_.set(index, builderForValue.build());
          onChanged();
        } else {
          highReward2Builder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward_2 = 19;</code>
       */
      public Builder addHighReward2(org.gof.demo.worldsrv.msg.Define.p_reward_status_list value) {
        if (highReward2Builder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureHighReward2IsMutable();
          highReward2_.add(value);
          onChanged();
        } else {
          highReward2Builder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward_2 = 19;</code>
       */
      public Builder addHighReward2(
          int index, org.gof.demo.worldsrv.msg.Define.p_reward_status_list value) {
        if (highReward2Builder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureHighReward2IsMutable();
          highReward2_.add(index, value);
          onChanged();
        } else {
          highReward2Builder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward_2 = 19;</code>
       */
      public Builder addHighReward2(
          org.gof.demo.worldsrv.msg.Define.p_reward_status_list.Builder builderForValue) {
        if (highReward2Builder_ == null) {
          ensureHighReward2IsMutable();
          highReward2_.add(builderForValue.build());
          onChanged();
        } else {
          highReward2Builder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward_2 = 19;</code>
       */
      public Builder addHighReward2(
          int index, org.gof.demo.worldsrv.msg.Define.p_reward_status_list.Builder builderForValue) {
        if (highReward2Builder_ == null) {
          ensureHighReward2IsMutable();
          highReward2_.add(index, builderForValue.build());
          onChanged();
        } else {
          highReward2Builder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward_2 = 19;</code>
       */
      public Builder addAllHighReward2(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_reward_status_list> values) {
        if (highReward2Builder_ == null) {
          ensureHighReward2IsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, highReward2_);
          onChanged();
        } else {
          highReward2Builder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward_2 = 19;</code>
       */
      public Builder clearHighReward2() {
        if (highReward2Builder_ == null) {
          highReward2_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00040000);
          onChanged();
        } else {
          highReward2Builder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward_2 = 19;</code>
       */
      public Builder removeHighReward2(int index) {
        if (highReward2Builder_ == null) {
          ensureHighReward2IsMutable();
          highReward2_.remove(index);
          onChanged();
        } else {
          highReward2Builder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward_2 = 19;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_reward_status_list.Builder getHighReward2Builder(
          int index) {
        return getHighReward2FieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward_2 = 19;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_reward_status_listOrBuilder getHighReward2OrBuilder(
          int index) {
        if (highReward2Builder_ == null) {
          return highReward2_.get(index);  } else {
          return highReward2Builder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward_2 = 19;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_reward_status_listOrBuilder> 
           getHighReward2OrBuilderList() {
        if (highReward2Builder_ != null) {
          return highReward2Builder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(highReward2_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward_2 = 19;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_reward_status_list.Builder addHighReward2Builder() {
        return getHighReward2FieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_reward_status_list.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward_2 = 19;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_reward_status_list.Builder addHighReward2Builder(
          int index) {
        return getHighReward2FieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_reward_status_list.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward_status_list high_reward_2 = 19;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward_status_list.Builder> 
           getHighReward2BuilderList() {
        return getHighReward2FieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_reward_status_list, org.gof.demo.worldsrv.msg.Define.p_reward_status_list.Builder, org.gof.demo.worldsrv.msg.Define.p_reward_status_listOrBuilder> 
          getHighReward2FieldBuilder() {
        if (highReward2Builder_ == null) {
          highReward2Builder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_reward_status_list, org.gof.demo.worldsrv.msg.Define.p_reward_status_list.Builder, org.gof.demo.worldsrv.msg.Define.p_reward_status_listOrBuilder>(
                  highReward2_,
                  ((bitField0_ & 0x00040000) != 0),
                  getParentForChildren(),
                  isClean());
          highReward2_ = null;
        }
        return highReward2Builder_;
      }

      private int mergeFlag_ ;
      /**
       * <code>uint32 merge_flag = 20;</code>
       * @return The mergeFlag.
       */
      @java.lang.Override
      public int getMergeFlag() {
        return mergeFlag_;
      }
      /**
       * <code>uint32 merge_flag = 20;</code>
       * @param value The mergeFlag to set.
       * @return This builder for chaining.
       */
      public Builder setMergeFlag(int value) {

        mergeFlag_ = value;
        bitField0_ |= 0x00080000;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 merge_flag = 20;</code>
       * @return This builder for chaining.
       */
      public Builder clearMergeFlag() {
        bitField0_ = (bitField0_ & ~0x00080000);
        mergeFlag_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.war_token_info_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.war_token_info_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<war_token_info_s2c>
        PARSER = new com.google.protobuf.AbstractParser<war_token_info_s2c>() {
      @java.lang.Override
      public war_token_info_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<war_token_info_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<war_token_info_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_info_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface war_token_close_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.war_token_close_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 id = 1;</code>
     * @return The id.
     */
    int getId();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.war_token_close_s2c}
   */
  public static final class war_token_close_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.war_token_close_s2c)
      war_token_close_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use war_token_close_s2c.newBuilder() to construct.
    private war_token_close_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private war_token_close_s2c() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new war_token_close_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_close_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_close_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgWarToken.war_token_close_s2c.class, org.gof.demo.worldsrv.msg.MsgWarToken.war_token_close_s2c.Builder.class);
    }

    public static final int ID_FIELD_NUMBER = 1;
    private int id_ = 0;
    /**
     * <code>uint32 id = 1;</code>
     * @return The id.
     */
    @java.lang.Override
    public int getId() {
      return id_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (id_ != 0) {
        output.writeUInt32(1, id_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (id_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, id_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgWarToken.war_token_close_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgWarToken.war_token_close_s2c other = (org.gof.demo.worldsrv.msg.MsgWarToken.war_token_close_s2c) obj;

      if (getId()
          != other.getId()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + getId();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_close_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_close_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_close_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_close_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_close_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_close_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_close_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_close_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_close_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_close_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_close_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_close_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgWarToken.war_token_close_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.war_token_close_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.war_token_close_s2c)
        org.gof.demo.worldsrv.msg.MsgWarToken.war_token_close_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_close_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_close_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgWarToken.war_token_close_s2c.class, org.gof.demo.worldsrv.msg.MsgWarToken.war_token_close_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgWarToken.war_token_close_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        id_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_close_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_close_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.war_token_close_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_close_s2c build() {
        org.gof.demo.worldsrv.msg.MsgWarToken.war_token_close_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_close_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgWarToken.war_token_close_s2c result = new org.gof.demo.worldsrv.msg.MsgWarToken.war_token_close_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgWarToken.war_token_close_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.id_ = id_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgWarToken.war_token_close_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgWarToken.war_token_close_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgWarToken.war_token_close_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgWarToken.war_token_close_s2c.getDefaultInstance()) return this;
        if (other.getId() != 0) {
          setId(other.getId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                id_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int id_ ;
      /**
       * <code>uint32 id = 1;</code>
       * @return The id.
       */
      @java.lang.Override
      public int getId() {
        return id_;
      }
      /**
       * <code>uint32 id = 1;</code>
       * @param value The id to set.
       * @return This builder for chaining.
       */
      public Builder setId(int value) {

        id_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        id_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.war_token_close_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.war_token_close_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgWarToken.war_token_close_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgWarToken.war_token_close_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_close_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<war_token_close_s2c>
        PARSER = new com.google.protobuf.AbstractParser<war_token_close_s2c>() {
      @java.lang.Override
      public war_token_close_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<war_token_close_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<war_token_close_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_close_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface war_token_update_task_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.war_token_update_task_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 id = 1;</code>
     * @return The id.
     */
    int getId();

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 2;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_war_token_task> 
        getTaskListList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 2;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_war_token_task getTaskList(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 2;</code>
     */
    int getTaskListCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 2;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_war_token_taskOrBuilder> 
        getTaskListOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 2;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_war_token_taskOrBuilder getTaskListOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.war_token_update_task_s2c}
   */
  public static final class war_token_update_task_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.war_token_update_task_s2c)
      war_token_update_task_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use war_token_update_task_s2c.newBuilder() to construct.
    private war_token_update_task_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private war_token_update_task_s2c() {
      taskList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new war_token_update_task_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_update_task_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_update_task_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_task_s2c.class, org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_task_s2c.Builder.class);
    }

    public static final int ID_FIELD_NUMBER = 1;
    private int id_ = 0;
    /**
     * <code>uint32 id = 1;</code>
     * @return The id.
     */
    @java.lang.Override
    public int getId() {
      return id_;
    }

    public static final int TASK_LIST_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_war_token_task> taskList_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 2;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_war_token_task> getTaskListList() {
      return taskList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_war_token_taskOrBuilder> 
        getTaskListOrBuilderList() {
      return taskList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 2;</code>
     */
    @java.lang.Override
    public int getTaskListCount() {
      return taskList_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 2;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_war_token_task getTaskList(int index) {
      return taskList_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 2;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_war_token_taskOrBuilder getTaskListOrBuilder(
        int index) {
      return taskList_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (id_ != 0) {
        output.writeUInt32(1, id_);
      }
      for (int i = 0; i < taskList_.size(); i++) {
        output.writeMessage(2, taskList_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (id_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, id_);
      }
      for (int i = 0; i < taskList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, taskList_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_task_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_task_s2c other = (org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_task_s2c) obj;

      if (getId()
          != other.getId()) return false;
      if (!getTaskListList()
          .equals(other.getTaskListList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + getId();
      if (getTaskListCount() > 0) {
        hash = (37 * hash) + TASK_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getTaskListList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_task_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_task_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_task_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_task_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_task_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_task_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_task_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_task_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_task_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_task_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_task_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_task_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_task_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.war_token_update_task_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.war_token_update_task_s2c)
        org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_task_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_update_task_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_update_task_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_task_s2c.class, org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_task_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_task_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        id_ = 0;
        if (taskListBuilder_ == null) {
          taskList_ = java.util.Collections.emptyList();
        } else {
          taskList_ = null;
          taskListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_update_task_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_task_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_task_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_task_s2c build() {
        org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_task_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_task_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_task_s2c result = new org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_task_s2c(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_task_s2c result) {
        if (taskListBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0)) {
            taskList_ = java.util.Collections.unmodifiableList(taskList_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.taskList_ = taskList_;
        } else {
          result.taskList_ = taskListBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_task_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.id_ = id_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_task_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_task_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_task_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_task_s2c.getDefaultInstance()) return this;
        if (other.getId() != 0) {
          setId(other.getId());
        }
        if (taskListBuilder_ == null) {
          if (!other.taskList_.isEmpty()) {
            if (taskList_.isEmpty()) {
              taskList_ = other.taskList_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensureTaskListIsMutable();
              taskList_.addAll(other.taskList_);
            }
            onChanged();
          }
        } else {
          if (!other.taskList_.isEmpty()) {
            if (taskListBuilder_.isEmpty()) {
              taskListBuilder_.dispose();
              taskListBuilder_ = null;
              taskList_ = other.taskList_;
              bitField0_ = (bitField0_ & ~0x00000002);
              taskListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getTaskListFieldBuilder() : null;
            } else {
              taskListBuilder_.addAllMessages(other.taskList_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                id_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 18: {
                org.gof.demo.worldsrv.msg.Define.p_war_token_task m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_war_token_task.parser(),
                        extensionRegistry);
                if (taskListBuilder_ == null) {
                  ensureTaskListIsMutable();
                  taskList_.add(m);
                } else {
                  taskListBuilder_.addMessage(m);
                }
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int id_ ;
      /**
       * <code>uint32 id = 1;</code>
       * @return The id.
       */
      @java.lang.Override
      public int getId() {
        return id_;
      }
      /**
       * <code>uint32 id = 1;</code>
       * @param value The id to set.
       * @return This builder for chaining.
       */
      public Builder setId(int value) {

        id_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        id_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_war_token_task> taskList_ =
        java.util.Collections.emptyList();
      private void ensureTaskListIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          taskList_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_war_token_task>(taskList_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_war_token_task, org.gof.demo.worldsrv.msg.Define.p_war_token_task.Builder, org.gof.demo.worldsrv.msg.Define.p_war_token_taskOrBuilder> taskListBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 2;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_war_token_task> getTaskListList() {
        if (taskListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(taskList_);
        } else {
          return taskListBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 2;</code>
       */
      public int getTaskListCount() {
        if (taskListBuilder_ == null) {
          return taskList_.size();
        } else {
          return taskListBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_war_token_task getTaskList(int index) {
        if (taskListBuilder_ == null) {
          return taskList_.get(index);
        } else {
          return taskListBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 2;</code>
       */
      public Builder setTaskList(
          int index, org.gof.demo.worldsrv.msg.Define.p_war_token_task value) {
        if (taskListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTaskListIsMutable();
          taskList_.set(index, value);
          onChanged();
        } else {
          taskListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 2;</code>
       */
      public Builder setTaskList(
          int index, org.gof.demo.worldsrv.msg.Define.p_war_token_task.Builder builderForValue) {
        if (taskListBuilder_ == null) {
          ensureTaskListIsMutable();
          taskList_.set(index, builderForValue.build());
          onChanged();
        } else {
          taskListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 2;</code>
       */
      public Builder addTaskList(org.gof.demo.worldsrv.msg.Define.p_war_token_task value) {
        if (taskListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTaskListIsMutable();
          taskList_.add(value);
          onChanged();
        } else {
          taskListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 2;</code>
       */
      public Builder addTaskList(
          int index, org.gof.demo.worldsrv.msg.Define.p_war_token_task value) {
        if (taskListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTaskListIsMutable();
          taskList_.add(index, value);
          onChanged();
        } else {
          taskListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 2;</code>
       */
      public Builder addTaskList(
          org.gof.demo.worldsrv.msg.Define.p_war_token_task.Builder builderForValue) {
        if (taskListBuilder_ == null) {
          ensureTaskListIsMutable();
          taskList_.add(builderForValue.build());
          onChanged();
        } else {
          taskListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 2;</code>
       */
      public Builder addTaskList(
          int index, org.gof.demo.worldsrv.msg.Define.p_war_token_task.Builder builderForValue) {
        if (taskListBuilder_ == null) {
          ensureTaskListIsMutable();
          taskList_.add(index, builderForValue.build());
          onChanged();
        } else {
          taskListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 2;</code>
       */
      public Builder addAllTaskList(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_war_token_task> values) {
        if (taskListBuilder_ == null) {
          ensureTaskListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, taskList_);
          onChanged();
        } else {
          taskListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 2;</code>
       */
      public Builder clearTaskList() {
        if (taskListBuilder_ == null) {
          taskList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          taskListBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 2;</code>
       */
      public Builder removeTaskList(int index) {
        if (taskListBuilder_ == null) {
          ensureTaskListIsMutable();
          taskList_.remove(index);
          onChanged();
        } else {
          taskListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_war_token_task.Builder getTaskListBuilder(
          int index) {
        return getTaskListFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_war_token_taskOrBuilder getTaskListOrBuilder(
          int index) {
        if (taskListBuilder_ == null) {
          return taskList_.get(index);  } else {
          return taskListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 2;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_war_token_taskOrBuilder> 
           getTaskListOrBuilderList() {
        if (taskListBuilder_ != null) {
          return taskListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(taskList_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_war_token_task.Builder addTaskListBuilder() {
        return getTaskListFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_war_token_task.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_war_token_task.Builder addTaskListBuilder(
          int index) {
        return getTaskListFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_war_token_task.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_war_token_task task_list = 2;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_war_token_task.Builder> 
           getTaskListBuilderList() {
        return getTaskListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_war_token_task, org.gof.demo.worldsrv.msg.Define.p_war_token_task.Builder, org.gof.demo.worldsrv.msg.Define.p_war_token_taskOrBuilder> 
          getTaskListFieldBuilder() {
        if (taskListBuilder_ == null) {
          taskListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_war_token_task, org.gof.demo.worldsrv.msg.Define.p_war_token_task.Builder, org.gof.demo.worldsrv.msg.Define.p_war_token_taskOrBuilder>(
                  taskList_,
                  ((bitField0_ & 0x00000002) != 0),
                  getParentForChildren(),
                  isClean());
          taskList_ = null;
        }
        return taskListBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.war_token_update_task_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.war_token_update_task_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_task_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_task_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_task_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<war_token_update_task_s2c>
        PARSER = new com.google.protobuf.AbstractParser<war_token_update_task_s2c>() {
      @java.lang.Override
      public war_token_update_task_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<war_token_update_task_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<war_token_update_task_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_task_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface war_token_task_reward_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.war_token_task_reward_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 id = 1;</code>
     * @return The id.
     */
    int getId();

    /**
     * <code>uint32 task_id = 2;</code>
     * @return The taskId.
     */
    int getTaskId();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.war_token_task_reward_c2s}
   */
  public static final class war_token_task_reward_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.war_token_task_reward_c2s)
      war_token_task_reward_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use war_token_task_reward_c2s.newBuilder() to construct.
    private war_token_task_reward_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private war_token_task_reward_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new war_token_task_reward_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_task_reward_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_task_reward_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_c2s.class, org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_c2s.Builder.class);
    }

    public static final int ID_FIELD_NUMBER = 1;
    private int id_ = 0;
    /**
     * <code>uint32 id = 1;</code>
     * @return The id.
     */
    @java.lang.Override
    public int getId() {
      return id_;
    }

    public static final int TASK_ID_FIELD_NUMBER = 2;
    private int taskId_ = 0;
    /**
     * <code>uint32 task_id = 2;</code>
     * @return The taskId.
     */
    @java.lang.Override
    public int getTaskId() {
      return taskId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (id_ != 0) {
        output.writeUInt32(1, id_);
      }
      if (taskId_ != 0) {
        output.writeUInt32(2, taskId_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (id_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, id_);
      }
      if (taskId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, taskId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_c2s other = (org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_c2s) obj;

      if (getId()
          != other.getId()) return false;
      if (getTaskId()
          != other.getTaskId()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + getId();
      hash = (37 * hash) + TASK_ID_FIELD_NUMBER;
      hash = (53 * hash) + getTaskId();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.war_token_task_reward_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.war_token_task_reward_c2s)
        org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_task_reward_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_task_reward_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_c2s.class, org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        id_ = 0;
        taskId_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_task_reward_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_c2s build() {
        org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_c2s result = new org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.id_ = id_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.taskId_ = taskId_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_c2s.getDefaultInstance()) return this;
        if (other.getId() != 0) {
          setId(other.getId());
        }
        if (other.getTaskId() != 0) {
          setTaskId(other.getTaskId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                id_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                taskId_ = input.readUInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int id_ ;
      /**
       * <code>uint32 id = 1;</code>
       * @return The id.
       */
      @java.lang.Override
      public int getId() {
        return id_;
      }
      /**
       * <code>uint32 id = 1;</code>
       * @param value The id to set.
       * @return This builder for chaining.
       */
      public Builder setId(int value) {

        id_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        id_ = 0;
        onChanged();
        return this;
      }

      private int taskId_ ;
      /**
       * <code>uint32 task_id = 2;</code>
       * @return The taskId.
       */
      @java.lang.Override
      public int getTaskId() {
        return taskId_;
      }
      /**
       * <code>uint32 task_id = 2;</code>
       * @param value The taskId to set.
       * @return This builder for chaining.
       */
      public Builder setTaskId(int value) {

        taskId_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 task_id = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearTaskId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        taskId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.war_token_task_reward_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.war_token_task_reward_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<war_token_task_reward_c2s>
        PARSER = new com.google.protobuf.AbstractParser<war_token_task_reward_c2s>() {
      @java.lang.Override
      public war_token_task_reward_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<war_token_task_reward_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<war_token_task_reward_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface war_token_task_reward_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.war_token_task_reward_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 id = 1;</code>
     * @return The id.
     */
    int getId();

    /**
     * <code>uint32 task_id = 2;</code>
     * @return The taskId.
     */
    int getTaskId();

    /**
     * <code>uint32 add_exp = 3;</code>
     * @return The addExp.
     */
    int getAddExp();

    /**
     * <code>uint32 lev = 4;</code>
     * @return The lev.
     */
    int getLev();

    /**
     * <code>uint32 exp = 5;</code>
     * @return The exp.
     */
    int getExp();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.war_token_task_reward_s2c}
   */
  public static final class war_token_task_reward_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.war_token_task_reward_s2c)
      war_token_task_reward_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use war_token_task_reward_s2c.newBuilder() to construct.
    private war_token_task_reward_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private war_token_task_reward_s2c() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new war_token_task_reward_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_task_reward_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_task_reward_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_s2c.class, org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_s2c.Builder.class);
    }

    public static final int ID_FIELD_NUMBER = 1;
    private int id_ = 0;
    /**
     * <code>uint32 id = 1;</code>
     * @return The id.
     */
    @java.lang.Override
    public int getId() {
      return id_;
    }

    public static final int TASK_ID_FIELD_NUMBER = 2;
    private int taskId_ = 0;
    /**
     * <code>uint32 task_id = 2;</code>
     * @return The taskId.
     */
    @java.lang.Override
    public int getTaskId() {
      return taskId_;
    }

    public static final int ADD_EXP_FIELD_NUMBER = 3;
    private int addExp_ = 0;
    /**
     * <code>uint32 add_exp = 3;</code>
     * @return The addExp.
     */
    @java.lang.Override
    public int getAddExp() {
      return addExp_;
    }

    public static final int LEV_FIELD_NUMBER = 4;
    private int lev_ = 0;
    /**
     * <code>uint32 lev = 4;</code>
     * @return The lev.
     */
    @java.lang.Override
    public int getLev() {
      return lev_;
    }

    public static final int EXP_FIELD_NUMBER = 5;
    private int exp_ = 0;
    /**
     * <code>uint32 exp = 5;</code>
     * @return The exp.
     */
    @java.lang.Override
    public int getExp() {
      return exp_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (id_ != 0) {
        output.writeUInt32(1, id_);
      }
      if (taskId_ != 0) {
        output.writeUInt32(2, taskId_);
      }
      if (addExp_ != 0) {
        output.writeUInt32(3, addExp_);
      }
      if (lev_ != 0) {
        output.writeUInt32(4, lev_);
      }
      if (exp_ != 0) {
        output.writeUInt32(5, exp_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (id_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, id_);
      }
      if (taskId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, taskId_);
      }
      if (addExp_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(3, addExp_);
      }
      if (lev_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(4, lev_);
      }
      if (exp_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(5, exp_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_s2c other = (org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_s2c) obj;

      if (getId()
          != other.getId()) return false;
      if (getTaskId()
          != other.getTaskId()) return false;
      if (getAddExp()
          != other.getAddExp()) return false;
      if (getLev()
          != other.getLev()) return false;
      if (getExp()
          != other.getExp()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + getId();
      hash = (37 * hash) + TASK_ID_FIELD_NUMBER;
      hash = (53 * hash) + getTaskId();
      hash = (37 * hash) + ADD_EXP_FIELD_NUMBER;
      hash = (53 * hash) + getAddExp();
      hash = (37 * hash) + LEV_FIELD_NUMBER;
      hash = (53 * hash) + getLev();
      hash = (37 * hash) + EXP_FIELD_NUMBER;
      hash = (53 * hash) + getExp();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.war_token_task_reward_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.war_token_task_reward_s2c)
        org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_task_reward_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_task_reward_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_s2c.class, org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        id_ = 0;
        taskId_ = 0;
        addExp_ = 0;
        lev_ = 0;
        exp_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_task_reward_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_s2c build() {
        org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_s2c result = new org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.id_ = id_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.taskId_ = taskId_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.addExp_ = addExp_;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.lev_ = lev_;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.exp_ = exp_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_s2c.getDefaultInstance()) return this;
        if (other.getId() != 0) {
          setId(other.getId());
        }
        if (other.getTaskId() != 0) {
          setTaskId(other.getTaskId());
        }
        if (other.getAddExp() != 0) {
          setAddExp(other.getAddExp());
        }
        if (other.getLev() != 0) {
          setLev(other.getLev());
        }
        if (other.getExp() != 0) {
          setExp(other.getExp());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                id_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                taskId_ = input.readUInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                addExp_ = input.readUInt32();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              case 32: {
                lev_ = input.readUInt32();
                bitField0_ |= 0x00000008;
                break;
              } // case 32
              case 40: {
                exp_ = input.readUInt32();
                bitField0_ |= 0x00000010;
                break;
              } // case 40
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int id_ ;
      /**
       * <code>uint32 id = 1;</code>
       * @return The id.
       */
      @java.lang.Override
      public int getId() {
        return id_;
      }
      /**
       * <code>uint32 id = 1;</code>
       * @param value The id to set.
       * @return This builder for chaining.
       */
      public Builder setId(int value) {

        id_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        id_ = 0;
        onChanged();
        return this;
      }

      private int taskId_ ;
      /**
       * <code>uint32 task_id = 2;</code>
       * @return The taskId.
       */
      @java.lang.Override
      public int getTaskId() {
        return taskId_;
      }
      /**
       * <code>uint32 task_id = 2;</code>
       * @param value The taskId to set.
       * @return This builder for chaining.
       */
      public Builder setTaskId(int value) {

        taskId_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 task_id = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearTaskId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        taskId_ = 0;
        onChanged();
        return this;
      }

      private int addExp_ ;
      /**
       * <code>uint32 add_exp = 3;</code>
       * @return The addExp.
       */
      @java.lang.Override
      public int getAddExp() {
        return addExp_;
      }
      /**
       * <code>uint32 add_exp = 3;</code>
       * @param value The addExp to set.
       * @return This builder for chaining.
       */
      public Builder setAddExp(int value) {

        addExp_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 add_exp = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearAddExp() {
        bitField0_ = (bitField0_ & ~0x00000004);
        addExp_ = 0;
        onChanged();
        return this;
      }

      private int lev_ ;
      /**
       * <code>uint32 lev = 4;</code>
       * @return The lev.
       */
      @java.lang.Override
      public int getLev() {
        return lev_;
      }
      /**
       * <code>uint32 lev = 4;</code>
       * @param value The lev to set.
       * @return This builder for chaining.
       */
      public Builder setLev(int value) {

        lev_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 lev = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearLev() {
        bitField0_ = (bitField0_ & ~0x00000008);
        lev_ = 0;
        onChanged();
        return this;
      }

      private int exp_ ;
      /**
       * <code>uint32 exp = 5;</code>
       * @return The exp.
       */
      @java.lang.Override
      public int getExp() {
        return exp_;
      }
      /**
       * <code>uint32 exp = 5;</code>
       * @param value The exp to set.
       * @return This builder for chaining.
       */
      public Builder setExp(int value) {

        exp_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 exp = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearExp() {
        bitField0_ = (bitField0_ & ~0x00000010);
        exp_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.war_token_task_reward_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.war_token_task_reward_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<war_token_task_reward_s2c>
        PARSER = new com.google.protobuf.AbstractParser<war_token_task_reward_s2c>() {
      @java.lang.Override
      public war_token_task_reward_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<war_token_task_reward_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<war_token_task_reward_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_task_reward_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface war_token_lev_reward_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.war_token_lev_reward_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 id = 1;</code>
     * @return The id.
     */
    int getId();

    /**
     * <code>uint32 take_lev = 2;</code>
     * @return The takeLev.
     */
    int getTakeLev();

    /**
     * <code>uint32 type = 3;</code>
     * @return The type.
     */
    int getType();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.war_token_lev_reward_c2s}
   */
  public static final class war_token_lev_reward_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.war_token_lev_reward_c2s)
      war_token_lev_reward_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use war_token_lev_reward_c2s.newBuilder() to construct.
    private war_token_lev_reward_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private war_token_lev_reward_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new war_token_lev_reward_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_lev_reward_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_lev_reward_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_c2s.class, org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_c2s.Builder.class);
    }

    public static final int ID_FIELD_NUMBER = 1;
    private int id_ = 0;
    /**
     * <code>uint32 id = 1;</code>
     * @return The id.
     */
    @java.lang.Override
    public int getId() {
      return id_;
    }

    public static final int TAKE_LEV_FIELD_NUMBER = 2;
    private int takeLev_ = 0;
    /**
     * <code>uint32 take_lev = 2;</code>
     * @return The takeLev.
     */
    @java.lang.Override
    public int getTakeLev() {
      return takeLev_;
    }

    public static final int TYPE_FIELD_NUMBER = 3;
    private int type_ = 0;
    /**
     * <code>uint32 type = 3;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (id_ != 0) {
        output.writeUInt32(1, id_);
      }
      if (takeLev_ != 0) {
        output.writeUInt32(2, takeLev_);
      }
      if (type_ != 0) {
        output.writeUInt32(3, type_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (id_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, id_);
      }
      if (takeLev_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, takeLev_);
      }
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(3, type_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_c2s other = (org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_c2s) obj;

      if (getId()
          != other.getId()) return false;
      if (getTakeLev()
          != other.getTakeLev()) return false;
      if (getType()
          != other.getType()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + getId();
      hash = (37 * hash) + TAKE_LEV_FIELD_NUMBER;
      hash = (53 * hash) + getTakeLev();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.war_token_lev_reward_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.war_token_lev_reward_c2s)
        org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_lev_reward_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_lev_reward_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_c2s.class, org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        id_ = 0;
        takeLev_ = 0;
        type_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_lev_reward_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_c2s build() {
        org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_c2s result = new org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.id_ = id_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.takeLev_ = takeLev_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.type_ = type_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_c2s.getDefaultInstance()) return this;
        if (other.getId() != 0) {
          setId(other.getId());
        }
        if (other.getTakeLev() != 0) {
          setTakeLev(other.getTakeLev());
        }
        if (other.getType() != 0) {
          setType(other.getType());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                id_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                takeLev_ = input.readUInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                type_ = input.readUInt32();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int id_ ;
      /**
       * <code>uint32 id = 1;</code>
       * @return The id.
       */
      @java.lang.Override
      public int getId() {
        return id_;
      }
      /**
       * <code>uint32 id = 1;</code>
       * @param value The id to set.
       * @return This builder for chaining.
       */
      public Builder setId(int value) {

        id_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        id_ = 0;
        onChanged();
        return this;
      }

      private int takeLev_ ;
      /**
       * <code>uint32 take_lev = 2;</code>
       * @return The takeLev.
       */
      @java.lang.Override
      public int getTakeLev() {
        return takeLev_;
      }
      /**
       * <code>uint32 take_lev = 2;</code>
       * @param value The takeLev to set.
       * @return This builder for chaining.
       */
      public Builder setTakeLev(int value) {

        takeLev_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 take_lev = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearTakeLev() {
        bitField0_ = (bitField0_ & ~0x00000002);
        takeLev_ = 0;
        onChanged();
        return this;
      }

      private int type_ ;
      /**
       * <code>uint32 type = 3;</code>
       * @return The type.
       */
      @java.lang.Override
      public int getType() {
        return type_;
      }
      /**
       * <code>uint32 type = 3;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(int value) {

        type_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 type = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000004);
        type_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.war_token_lev_reward_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.war_token_lev_reward_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<war_token_lev_reward_c2s>
        PARSER = new com.google.protobuf.AbstractParser<war_token_lev_reward_c2s>() {
      @java.lang.Override
      public war_token_lev_reward_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<war_token_lev_reward_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<war_token_lev_reward_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface war_token_lev_reward_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.war_token_lev_reward_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 id = 1;</code>
     * @return The id.
     */
    int getId();

    /**
     * <code>uint32 take_lev = 2;</code>
     * @return The takeLev.
     */
    int getTakeLev();

    /**
     * <code>uint32 type = 3;</code>
     * @return The type.
     */
    int getType();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.war_token_lev_reward_s2c}
   */
  public static final class war_token_lev_reward_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.war_token_lev_reward_s2c)
      war_token_lev_reward_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use war_token_lev_reward_s2c.newBuilder() to construct.
    private war_token_lev_reward_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private war_token_lev_reward_s2c() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new war_token_lev_reward_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_lev_reward_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_lev_reward_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_s2c.class, org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_s2c.Builder.class);
    }

    public static final int ID_FIELD_NUMBER = 1;
    private int id_ = 0;
    /**
     * <code>uint32 id = 1;</code>
     * @return The id.
     */
    @java.lang.Override
    public int getId() {
      return id_;
    }

    public static final int TAKE_LEV_FIELD_NUMBER = 2;
    private int takeLev_ = 0;
    /**
     * <code>uint32 take_lev = 2;</code>
     * @return The takeLev.
     */
    @java.lang.Override
    public int getTakeLev() {
      return takeLev_;
    }

    public static final int TYPE_FIELD_NUMBER = 3;
    private int type_ = 0;
    /**
     * <code>uint32 type = 3;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (id_ != 0) {
        output.writeUInt32(1, id_);
      }
      if (takeLev_ != 0) {
        output.writeUInt32(2, takeLev_);
      }
      if (type_ != 0) {
        output.writeUInt32(3, type_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (id_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, id_);
      }
      if (takeLev_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, takeLev_);
      }
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(3, type_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_s2c other = (org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_s2c) obj;

      if (getId()
          != other.getId()) return false;
      if (getTakeLev()
          != other.getTakeLev()) return false;
      if (getType()
          != other.getType()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + getId();
      hash = (37 * hash) + TAKE_LEV_FIELD_NUMBER;
      hash = (53 * hash) + getTakeLev();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.war_token_lev_reward_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.war_token_lev_reward_s2c)
        org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_lev_reward_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_lev_reward_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_s2c.class, org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        id_ = 0;
        takeLev_ = 0;
        type_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_lev_reward_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_s2c build() {
        org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_s2c result = new org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.id_ = id_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.takeLev_ = takeLev_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.type_ = type_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_s2c.getDefaultInstance()) return this;
        if (other.getId() != 0) {
          setId(other.getId());
        }
        if (other.getTakeLev() != 0) {
          setTakeLev(other.getTakeLev());
        }
        if (other.getType() != 0) {
          setType(other.getType());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                id_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                takeLev_ = input.readUInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                type_ = input.readUInt32();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int id_ ;
      /**
       * <code>uint32 id = 1;</code>
       * @return The id.
       */
      @java.lang.Override
      public int getId() {
        return id_;
      }
      /**
       * <code>uint32 id = 1;</code>
       * @param value The id to set.
       * @return This builder for chaining.
       */
      public Builder setId(int value) {

        id_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        id_ = 0;
        onChanged();
        return this;
      }

      private int takeLev_ ;
      /**
       * <code>uint32 take_lev = 2;</code>
       * @return The takeLev.
       */
      @java.lang.Override
      public int getTakeLev() {
        return takeLev_;
      }
      /**
       * <code>uint32 take_lev = 2;</code>
       * @param value The takeLev to set.
       * @return This builder for chaining.
       */
      public Builder setTakeLev(int value) {

        takeLev_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 take_lev = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearTakeLev() {
        bitField0_ = (bitField0_ & ~0x00000002);
        takeLev_ = 0;
        onChanged();
        return this;
      }

      private int type_ ;
      /**
       * <code>uint32 type = 3;</code>
       * @return The type.
       */
      @java.lang.Override
      public int getType() {
        return type_;
      }
      /**
       * <code>uint32 type = 3;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(int value) {

        type_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 type = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000004);
        type_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.war_token_lev_reward_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.war_token_lev_reward_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<war_token_lev_reward_s2c>
        PARSER = new com.google.protobuf.AbstractParser<war_token_lev_reward_s2c>() {
      @java.lang.Override
      public war_token_lev_reward_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<war_token_lev_reward_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<war_token_lev_reward_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_lev_reward_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface war_token_add_reward_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.war_token_add_reward_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 id = 1;</code>
     * @return The id.
     */
    int getId();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.war_token_add_reward_c2s}
   */
  public static final class war_token_add_reward_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.war_token_add_reward_c2s)
      war_token_add_reward_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use war_token_add_reward_c2s.newBuilder() to construct.
    private war_token_add_reward_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private war_token_add_reward_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new war_token_add_reward_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_add_reward_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_add_reward_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_c2s.class, org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_c2s.Builder.class);
    }

    public static final int ID_FIELD_NUMBER = 1;
    private int id_ = 0;
    /**
     * <code>uint32 id = 1;</code>
     * @return The id.
     */
    @java.lang.Override
    public int getId() {
      return id_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (id_ != 0) {
        output.writeUInt32(1, id_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (id_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, id_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_c2s other = (org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_c2s) obj;

      if (getId()
          != other.getId()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + getId();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.war_token_add_reward_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.war_token_add_reward_c2s)
        org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_add_reward_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_add_reward_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_c2s.class, org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        id_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_add_reward_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_c2s build() {
        org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_c2s result = new org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.id_ = id_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_c2s.getDefaultInstance()) return this;
        if (other.getId() != 0) {
          setId(other.getId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                id_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int id_ ;
      /**
       * <code>uint32 id = 1;</code>
       * @return The id.
       */
      @java.lang.Override
      public int getId() {
        return id_;
      }
      /**
       * <code>uint32 id = 1;</code>
       * @param value The id to set.
       * @return This builder for chaining.
       */
      public Builder setId(int value) {

        id_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        id_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.war_token_add_reward_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.war_token_add_reward_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<war_token_add_reward_c2s>
        PARSER = new com.google.protobuf.AbstractParser<war_token_add_reward_c2s>() {
      @java.lang.Override
      public war_token_add_reward_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<war_token_add_reward_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<war_token_add_reward_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface war_token_add_reward_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.war_token_add_reward_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 id = 1;</code>
     * @return The id.
     */
    int getId();

    /**
     * <code>uint32 exp = 2;</code>
     * @return The exp.
     */
    int getExp();

    /**
     * <code>uint32 extra_times = 3;</code>
     * @return The extraTimes.
     */
    int getExtraTimes();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.war_token_add_reward_s2c}
   */
  public static final class war_token_add_reward_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.war_token_add_reward_s2c)
      war_token_add_reward_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use war_token_add_reward_s2c.newBuilder() to construct.
    private war_token_add_reward_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private war_token_add_reward_s2c() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new war_token_add_reward_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_add_reward_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_add_reward_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_s2c.class, org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_s2c.Builder.class);
    }

    public static final int ID_FIELD_NUMBER = 1;
    private int id_ = 0;
    /**
     * <code>uint32 id = 1;</code>
     * @return The id.
     */
    @java.lang.Override
    public int getId() {
      return id_;
    }

    public static final int EXP_FIELD_NUMBER = 2;
    private int exp_ = 0;
    /**
     * <code>uint32 exp = 2;</code>
     * @return The exp.
     */
    @java.lang.Override
    public int getExp() {
      return exp_;
    }

    public static final int EXTRA_TIMES_FIELD_NUMBER = 3;
    private int extraTimes_ = 0;
    /**
     * <code>uint32 extra_times = 3;</code>
     * @return The extraTimes.
     */
    @java.lang.Override
    public int getExtraTimes() {
      return extraTimes_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (id_ != 0) {
        output.writeUInt32(1, id_);
      }
      if (exp_ != 0) {
        output.writeUInt32(2, exp_);
      }
      if (extraTimes_ != 0) {
        output.writeUInt32(3, extraTimes_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (id_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, id_);
      }
      if (exp_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, exp_);
      }
      if (extraTimes_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(3, extraTimes_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_s2c other = (org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_s2c) obj;

      if (getId()
          != other.getId()) return false;
      if (getExp()
          != other.getExp()) return false;
      if (getExtraTimes()
          != other.getExtraTimes()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + getId();
      hash = (37 * hash) + EXP_FIELD_NUMBER;
      hash = (53 * hash) + getExp();
      hash = (37 * hash) + EXTRA_TIMES_FIELD_NUMBER;
      hash = (53 * hash) + getExtraTimes();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.war_token_add_reward_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.war_token_add_reward_s2c)
        org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_add_reward_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_add_reward_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_s2c.class, org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        id_ = 0;
        exp_ = 0;
        extraTimes_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_add_reward_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_s2c build() {
        org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_s2c result = new org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.id_ = id_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.exp_ = exp_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.extraTimes_ = extraTimes_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_s2c.getDefaultInstance()) return this;
        if (other.getId() != 0) {
          setId(other.getId());
        }
        if (other.getExp() != 0) {
          setExp(other.getExp());
        }
        if (other.getExtraTimes() != 0) {
          setExtraTimes(other.getExtraTimes());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                id_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                exp_ = input.readUInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                extraTimes_ = input.readUInt32();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int id_ ;
      /**
       * <code>uint32 id = 1;</code>
       * @return The id.
       */
      @java.lang.Override
      public int getId() {
        return id_;
      }
      /**
       * <code>uint32 id = 1;</code>
       * @param value The id to set.
       * @return This builder for chaining.
       */
      public Builder setId(int value) {

        id_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        id_ = 0;
        onChanged();
        return this;
      }

      private int exp_ ;
      /**
       * <code>uint32 exp = 2;</code>
       * @return The exp.
       */
      @java.lang.Override
      public int getExp() {
        return exp_;
      }
      /**
       * <code>uint32 exp = 2;</code>
       * @param value The exp to set.
       * @return This builder for chaining.
       */
      public Builder setExp(int value) {

        exp_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 exp = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearExp() {
        bitField0_ = (bitField0_ & ~0x00000002);
        exp_ = 0;
        onChanged();
        return this;
      }

      private int extraTimes_ ;
      /**
       * <code>uint32 extra_times = 3;</code>
       * @return The extraTimes.
       */
      @java.lang.Override
      public int getExtraTimes() {
        return extraTimes_;
      }
      /**
       * <code>uint32 extra_times = 3;</code>
       * @param value The extraTimes to set.
       * @return This builder for chaining.
       */
      public Builder setExtraTimes(int value) {

        extraTimes_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 extra_times = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearExtraTimes() {
        bitField0_ = (bitField0_ & ~0x00000004);
        extraTimes_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.war_token_add_reward_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.war_token_add_reward_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<war_token_add_reward_s2c>
        PARSER = new com.google.protobuf.AbstractParser<war_token_add_reward_s2c>() {
      @java.lang.Override
      public war_token_add_reward_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<war_token_add_reward_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<war_token_add_reward_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_add_reward_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface war_token_update_exp_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.war_token_update_exp_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 id = 1;</code>
     * @return The id.
     */
    int getId();

    /**
     * <code>uint32 lev = 2;</code>
     * @return The lev.
     */
    int getLev();

    /**
     * <code>uint32 exp = 3;</code>
     * @return The exp.
     */
    int getExp();

    /**
     * <code>uint32 total_exp = 4;</code>
     * @return The totalExp.
     */
    int getTotalExp();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.war_token_update_exp_s2c}
   */
  public static final class war_token_update_exp_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.war_token_update_exp_s2c)
      war_token_update_exp_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use war_token_update_exp_s2c.newBuilder() to construct.
    private war_token_update_exp_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private war_token_update_exp_s2c() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new war_token_update_exp_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_update_exp_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_update_exp_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_exp_s2c.class, org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_exp_s2c.Builder.class);
    }

    public static final int ID_FIELD_NUMBER = 1;
    private int id_ = 0;
    /**
     * <code>uint32 id = 1;</code>
     * @return The id.
     */
    @java.lang.Override
    public int getId() {
      return id_;
    }

    public static final int LEV_FIELD_NUMBER = 2;
    private int lev_ = 0;
    /**
     * <code>uint32 lev = 2;</code>
     * @return The lev.
     */
    @java.lang.Override
    public int getLev() {
      return lev_;
    }

    public static final int EXP_FIELD_NUMBER = 3;
    private int exp_ = 0;
    /**
     * <code>uint32 exp = 3;</code>
     * @return The exp.
     */
    @java.lang.Override
    public int getExp() {
      return exp_;
    }

    public static final int TOTAL_EXP_FIELD_NUMBER = 4;
    private int totalExp_ = 0;
    /**
     * <code>uint32 total_exp = 4;</code>
     * @return The totalExp.
     */
    @java.lang.Override
    public int getTotalExp() {
      return totalExp_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (id_ != 0) {
        output.writeUInt32(1, id_);
      }
      if (lev_ != 0) {
        output.writeUInt32(2, lev_);
      }
      if (exp_ != 0) {
        output.writeUInt32(3, exp_);
      }
      if (totalExp_ != 0) {
        output.writeUInt32(4, totalExp_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (id_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, id_);
      }
      if (lev_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, lev_);
      }
      if (exp_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(3, exp_);
      }
      if (totalExp_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(4, totalExp_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_exp_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_exp_s2c other = (org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_exp_s2c) obj;

      if (getId()
          != other.getId()) return false;
      if (getLev()
          != other.getLev()) return false;
      if (getExp()
          != other.getExp()) return false;
      if (getTotalExp()
          != other.getTotalExp()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + getId();
      hash = (37 * hash) + LEV_FIELD_NUMBER;
      hash = (53 * hash) + getLev();
      hash = (37 * hash) + EXP_FIELD_NUMBER;
      hash = (53 * hash) + getExp();
      hash = (37 * hash) + TOTAL_EXP_FIELD_NUMBER;
      hash = (53 * hash) + getTotalExp();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_exp_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_exp_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_exp_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_exp_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_exp_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_exp_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_exp_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_exp_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_exp_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_exp_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_exp_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_exp_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_exp_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.war_token_update_exp_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.war_token_update_exp_s2c)
        org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_exp_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_update_exp_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_update_exp_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_exp_s2c.class, org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_exp_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_exp_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        id_ = 0;
        lev_ = 0;
        exp_ = 0;
        totalExp_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_update_exp_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_exp_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_exp_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_exp_s2c build() {
        org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_exp_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_exp_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_exp_s2c result = new org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_exp_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_exp_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.id_ = id_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.lev_ = lev_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.exp_ = exp_;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.totalExp_ = totalExp_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_exp_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_exp_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_exp_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_exp_s2c.getDefaultInstance()) return this;
        if (other.getId() != 0) {
          setId(other.getId());
        }
        if (other.getLev() != 0) {
          setLev(other.getLev());
        }
        if (other.getExp() != 0) {
          setExp(other.getExp());
        }
        if (other.getTotalExp() != 0) {
          setTotalExp(other.getTotalExp());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                id_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                lev_ = input.readUInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                exp_ = input.readUInt32();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              case 32: {
                totalExp_ = input.readUInt32();
                bitField0_ |= 0x00000008;
                break;
              } // case 32
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int id_ ;
      /**
       * <code>uint32 id = 1;</code>
       * @return The id.
       */
      @java.lang.Override
      public int getId() {
        return id_;
      }
      /**
       * <code>uint32 id = 1;</code>
       * @param value The id to set.
       * @return This builder for chaining.
       */
      public Builder setId(int value) {

        id_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        id_ = 0;
        onChanged();
        return this;
      }

      private int lev_ ;
      /**
       * <code>uint32 lev = 2;</code>
       * @return The lev.
       */
      @java.lang.Override
      public int getLev() {
        return lev_;
      }
      /**
       * <code>uint32 lev = 2;</code>
       * @param value The lev to set.
       * @return This builder for chaining.
       */
      public Builder setLev(int value) {

        lev_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 lev = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearLev() {
        bitField0_ = (bitField0_ & ~0x00000002);
        lev_ = 0;
        onChanged();
        return this;
      }

      private int exp_ ;
      /**
       * <code>uint32 exp = 3;</code>
       * @return The exp.
       */
      @java.lang.Override
      public int getExp() {
        return exp_;
      }
      /**
       * <code>uint32 exp = 3;</code>
       * @param value The exp to set.
       * @return This builder for chaining.
       */
      public Builder setExp(int value) {

        exp_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 exp = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearExp() {
        bitField0_ = (bitField0_ & ~0x00000004);
        exp_ = 0;
        onChanged();
        return this;
      }

      private int totalExp_ ;
      /**
       * <code>uint32 total_exp = 4;</code>
       * @return The totalExp.
       */
      @java.lang.Override
      public int getTotalExp() {
        return totalExp_;
      }
      /**
       * <code>uint32 total_exp = 4;</code>
       * @param value The totalExp to set.
       * @return This builder for chaining.
       */
      public Builder setTotalExp(int value) {

        totalExp_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 total_exp = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearTotalExp() {
        bitField0_ = (bitField0_ & ~0x00000008);
        totalExp_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.war_token_update_exp_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.war_token_update_exp_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_exp_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_exp_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_exp_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<war_token_update_exp_s2c>
        PARSER = new com.google.protobuf.AbstractParser<war_token_update_exp_s2c>() {
      @java.lang.Override
      public war_token_update_exp_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<war_token_update_exp_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<war_token_update_exp_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_update_exp_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface war_token_claim_special_reward_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.war_token_claim_special_reward_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 id = 1;</code>
     * @return The id.
     */
    int getId();

    /**
     * <code>uint32 cfg_id = 2;</code>
     * @return The cfgId.
     */
    int getCfgId();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.war_token_claim_special_reward_c2s}
   */
  public static final class war_token_claim_special_reward_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.war_token_claim_special_reward_c2s)
      war_token_claim_special_reward_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use war_token_claim_special_reward_c2s.newBuilder() to construct.
    private war_token_claim_special_reward_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private war_token_claim_special_reward_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new war_token_claim_special_reward_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_claim_special_reward_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_claim_special_reward_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_c2s.class, org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_c2s.Builder.class);
    }

    public static final int ID_FIELD_NUMBER = 1;
    private int id_ = 0;
    /**
     * <code>uint32 id = 1;</code>
     * @return The id.
     */
    @java.lang.Override
    public int getId() {
      return id_;
    }

    public static final int CFG_ID_FIELD_NUMBER = 2;
    private int cfgId_ = 0;
    /**
     * <code>uint32 cfg_id = 2;</code>
     * @return The cfgId.
     */
    @java.lang.Override
    public int getCfgId() {
      return cfgId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (id_ != 0) {
        output.writeUInt32(1, id_);
      }
      if (cfgId_ != 0) {
        output.writeUInt32(2, cfgId_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (id_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, id_);
      }
      if (cfgId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, cfgId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_c2s other = (org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_c2s) obj;

      if (getId()
          != other.getId()) return false;
      if (getCfgId()
          != other.getCfgId()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + getId();
      hash = (37 * hash) + CFG_ID_FIELD_NUMBER;
      hash = (53 * hash) + getCfgId();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.war_token_claim_special_reward_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.war_token_claim_special_reward_c2s)
        org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_claim_special_reward_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_claim_special_reward_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_c2s.class, org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        id_ = 0;
        cfgId_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_claim_special_reward_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_c2s build() {
        org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_c2s result = new org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.id_ = id_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.cfgId_ = cfgId_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_c2s.getDefaultInstance()) return this;
        if (other.getId() != 0) {
          setId(other.getId());
        }
        if (other.getCfgId() != 0) {
          setCfgId(other.getCfgId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                id_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                cfgId_ = input.readUInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int id_ ;
      /**
       * <code>uint32 id = 1;</code>
       * @return The id.
       */
      @java.lang.Override
      public int getId() {
        return id_;
      }
      /**
       * <code>uint32 id = 1;</code>
       * @param value The id to set.
       * @return This builder for chaining.
       */
      public Builder setId(int value) {

        id_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        id_ = 0;
        onChanged();
        return this;
      }

      private int cfgId_ ;
      /**
       * <code>uint32 cfg_id = 2;</code>
       * @return The cfgId.
       */
      @java.lang.Override
      public int getCfgId() {
        return cfgId_;
      }
      /**
       * <code>uint32 cfg_id = 2;</code>
       * @param value The cfgId to set.
       * @return This builder for chaining.
       */
      public Builder setCfgId(int value) {

        cfgId_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 cfg_id = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearCfgId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        cfgId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.war_token_claim_special_reward_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.war_token_claim_special_reward_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<war_token_claim_special_reward_c2s>
        PARSER = new com.google.protobuf.AbstractParser<war_token_claim_special_reward_c2s>() {
      @java.lang.Override
      public war_token_claim_special_reward_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<war_token_claim_special_reward_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<war_token_claim_special_reward_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface war_token_claim_special_reward_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.war_token_claim_special_reward_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 id = 1;</code>
     * @return The id.
     */
    int getId();

    /**
     * <code>repeated uint32 special_reward_list = 2 [packed = false];</code>
     * @return A list containing the specialRewardList.
     */
    java.util.List<java.lang.Integer> getSpecialRewardListList();
    /**
     * <code>repeated uint32 special_reward_list = 2 [packed = false];</code>
     * @return The count of specialRewardList.
     */
    int getSpecialRewardListCount();
    /**
     * <code>repeated uint32 special_reward_list = 2 [packed = false];</code>
     * @param index The index of the element to return.
     * @return The specialRewardList at the given index.
     */
    int getSpecialRewardList(int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.war_token_claim_special_reward_s2c}
   */
  public static final class war_token_claim_special_reward_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.war_token_claim_special_reward_s2c)
      war_token_claim_special_reward_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use war_token_claim_special_reward_s2c.newBuilder() to construct.
    private war_token_claim_special_reward_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private war_token_claim_special_reward_s2c() {
      specialRewardList_ = emptyIntList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new war_token_claim_special_reward_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_claim_special_reward_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_claim_special_reward_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_s2c.class, org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_s2c.Builder.class);
    }

    public static final int ID_FIELD_NUMBER = 1;
    private int id_ = 0;
    /**
     * <code>uint32 id = 1;</code>
     * @return The id.
     */
    @java.lang.Override
    public int getId() {
      return id_;
    }

    public static final int SPECIAL_REWARD_LIST_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.IntList specialRewardList_ =
        emptyIntList();
    /**
     * <code>repeated uint32 special_reward_list = 2 [packed = false];</code>
     * @return A list containing the specialRewardList.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getSpecialRewardListList() {
      return specialRewardList_;
    }
    /**
     * <code>repeated uint32 special_reward_list = 2 [packed = false];</code>
     * @return The count of specialRewardList.
     */
    public int getSpecialRewardListCount() {
      return specialRewardList_.size();
    }
    /**
     * <code>repeated uint32 special_reward_list = 2 [packed = false];</code>
     * @param index The index of the element to return.
     * @return The specialRewardList at the given index.
     */
    public int getSpecialRewardList(int index) {
      return specialRewardList_.getInt(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (id_ != 0) {
        output.writeUInt32(1, id_);
      }
      for (int i = 0; i < specialRewardList_.size(); i++) {
        output.writeUInt32(2, specialRewardList_.getInt(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (id_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, id_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < specialRewardList_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeUInt32SizeNoTag(specialRewardList_.getInt(i));
        }
        size += dataSize;
        size += 1 * getSpecialRewardListList().size();
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_s2c other = (org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_s2c) obj;

      if (getId()
          != other.getId()) return false;
      if (!getSpecialRewardListList()
          .equals(other.getSpecialRewardListList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + getId();
      if (getSpecialRewardListCount() > 0) {
        hash = (37 * hash) + SPECIAL_REWARD_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getSpecialRewardListList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.war_token_claim_special_reward_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.war_token_claim_special_reward_s2c)
        org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_claim_special_reward_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_claim_special_reward_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_s2c.class, org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        id_ = 0;
        specialRewardList_ = emptyIntList();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_claim_special_reward_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_s2c build() {
        org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_s2c result = new org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.id_ = id_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          specialRewardList_.makeImmutable();
          result.specialRewardList_ = specialRewardList_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_s2c.getDefaultInstance()) return this;
        if (other.getId() != 0) {
          setId(other.getId());
        }
        if (!other.specialRewardList_.isEmpty()) {
          if (specialRewardList_.isEmpty()) {
            specialRewardList_ = other.specialRewardList_;
            specialRewardList_.makeImmutable();
            bitField0_ |= 0x00000002;
          } else {
            ensureSpecialRewardListIsMutable();
            specialRewardList_.addAll(other.specialRewardList_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                id_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                int v = input.readUInt32();
                ensureSpecialRewardListIsMutable();
                specialRewardList_.addInt(v);
                break;
              } // case 16
              case 18: {
                int length = input.readRawVarint32();
                int limit = input.pushLimit(length);
                ensureSpecialRewardListIsMutable();
                while (input.getBytesUntilLimit() > 0) {
                  specialRewardList_.addInt(input.readUInt32());
                }
                input.popLimit(limit);
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int id_ ;
      /**
       * <code>uint32 id = 1;</code>
       * @return The id.
       */
      @java.lang.Override
      public int getId() {
        return id_;
      }
      /**
       * <code>uint32 id = 1;</code>
       * @param value The id to set.
       * @return This builder for chaining.
       */
      public Builder setId(int value) {

        id_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        id_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.IntList specialRewardList_ = emptyIntList();
      private void ensureSpecialRewardListIsMutable() {
        if (!specialRewardList_.isModifiable()) {
          specialRewardList_ = makeMutableCopy(specialRewardList_);
        }
        bitField0_ |= 0x00000002;
      }
      /**
       * <code>repeated uint32 special_reward_list = 2 [packed = false];</code>
       * @return A list containing the specialRewardList.
       */
      public java.util.List<java.lang.Integer>
          getSpecialRewardListList() {
        specialRewardList_.makeImmutable();
        return specialRewardList_;
      }
      /**
       * <code>repeated uint32 special_reward_list = 2 [packed = false];</code>
       * @return The count of specialRewardList.
       */
      public int getSpecialRewardListCount() {
        return specialRewardList_.size();
      }
      /**
       * <code>repeated uint32 special_reward_list = 2 [packed = false];</code>
       * @param index The index of the element to return.
       * @return The specialRewardList at the given index.
       */
      public int getSpecialRewardList(int index) {
        return specialRewardList_.getInt(index);
      }
      /**
       * <code>repeated uint32 special_reward_list = 2 [packed = false];</code>
       * @param index The index to set the value at.
       * @param value The specialRewardList to set.
       * @return This builder for chaining.
       */
      public Builder setSpecialRewardList(
          int index, int value) {

        ensureSpecialRewardListIsMutable();
        specialRewardList_.setInt(index, value);
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint32 special_reward_list = 2 [packed = false];</code>
       * @param value The specialRewardList to add.
       * @return This builder for chaining.
       */
      public Builder addSpecialRewardList(int value) {

        ensureSpecialRewardListIsMutable();
        specialRewardList_.addInt(value);
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint32 special_reward_list = 2 [packed = false];</code>
       * @param values The specialRewardList to add.
       * @return This builder for chaining.
       */
      public Builder addAllSpecialRewardList(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureSpecialRewardListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, specialRewardList_);
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint32 special_reward_list = 2 [packed = false];</code>
       * @return This builder for chaining.
       */
      public Builder clearSpecialRewardList() {
        specialRewardList_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.war_token_claim_special_reward_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.war_token_claim_special_reward_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<war_token_claim_special_reward_s2c>
        PARSER = new com.google.protobuf.AbstractParser<war_token_claim_special_reward_s2c>() {
      @java.lang.Override
      public war_token_claim_special_reward_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<war_token_claim_special_reward_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<war_token_claim_special_reward_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_claim_special_reward_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface war_token_buy_level_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.war_token_buy_level_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 id = 1;</code>
     * @return The id.
     */
    int getId();

    /**
     * <code>uint32 num = 2;</code>
     * @return The num.
     */
    int getNum();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.war_token_buy_level_c2s}
   */
  public static final class war_token_buy_level_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.war_token_buy_level_c2s)
      war_token_buy_level_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use war_token_buy_level_c2s.newBuilder() to construct.
    private war_token_buy_level_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private war_token_buy_level_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new war_token_buy_level_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_buy_level_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_buy_level_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgWarToken.war_token_buy_level_c2s.class, org.gof.demo.worldsrv.msg.MsgWarToken.war_token_buy_level_c2s.Builder.class);
    }

    public static final int ID_FIELD_NUMBER = 1;
    private int id_ = 0;
    /**
     * <code>uint32 id = 1;</code>
     * @return The id.
     */
    @java.lang.Override
    public int getId() {
      return id_;
    }

    public static final int NUM_FIELD_NUMBER = 2;
    private int num_ = 0;
    /**
     * <code>uint32 num = 2;</code>
     * @return The num.
     */
    @java.lang.Override
    public int getNum() {
      return num_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (id_ != 0) {
        output.writeUInt32(1, id_);
      }
      if (num_ != 0) {
        output.writeUInt32(2, num_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (id_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, id_);
      }
      if (num_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, num_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgWarToken.war_token_buy_level_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgWarToken.war_token_buy_level_c2s other = (org.gof.demo.worldsrv.msg.MsgWarToken.war_token_buy_level_c2s) obj;

      if (getId()
          != other.getId()) return false;
      if (getNum()
          != other.getNum()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + getId();
      hash = (37 * hash) + NUM_FIELD_NUMBER;
      hash = (53 * hash) + getNum();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_buy_level_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_buy_level_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_buy_level_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_buy_level_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_buy_level_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_buy_level_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_buy_level_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_buy_level_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_buy_level_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_buy_level_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_buy_level_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_buy_level_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgWarToken.war_token_buy_level_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.war_token_buy_level_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.war_token_buy_level_c2s)
        org.gof.demo.worldsrv.msg.MsgWarToken.war_token_buy_level_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_buy_level_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_buy_level_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgWarToken.war_token_buy_level_c2s.class, org.gof.demo.worldsrv.msg.MsgWarToken.war_token_buy_level_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgWarToken.war_token_buy_level_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        id_ = 0;
        num_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.internal_static_org_gof_demo_worldsrv_msg_war_token_buy_level_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_buy_level_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgWarToken.war_token_buy_level_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_buy_level_c2s build() {
        org.gof.demo.worldsrv.msg.MsgWarToken.war_token_buy_level_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_buy_level_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgWarToken.war_token_buy_level_c2s result = new org.gof.demo.worldsrv.msg.MsgWarToken.war_token_buy_level_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgWarToken.war_token_buy_level_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.id_ = id_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.num_ = num_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgWarToken.war_token_buy_level_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgWarToken.war_token_buy_level_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgWarToken.war_token_buy_level_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgWarToken.war_token_buy_level_c2s.getDefaultInstance()) return this;
        if (other.getId() != 0) {
          setId(other.getId());
        }
        if (other.getNum() != 0) {
          setNum(other.getNum());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                id_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                num_ = input.readUInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int id_ ;
      /**
       * <code>uint32 id = 1;</code>
       * @return The id.
       */
      @java.lang.Override
      public int getId() {
        return id_;
      }
      /**
       * <code>uint32 id = 1;</code>
       * @param value The id to set.
       * @return This builder for chaining.
       */
      public Builder setId(int value) {

        id_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        id_ = 0;
        onChanged();
        return this;
      }

      private int num_ ;
      /**
       * <code>uint32 num = 2;</code>
       * @return The num.
       */
      @java.lang.Override
      public int getNum() {
        return num_;
      }
      /**
       * <code>uint32 num = 2;</code>
       * @param value The num to set.
       * @return This builder for chaining.
       */
      public Builder setNum(int value) {

        num_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 num = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearNum() {
        bitField0_ = (bitField0_ & ~0x00000002);
        num_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.war_token_buy_level_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.war_token_buy_level_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgWarToken.war_token_buy_level_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgWarToken.war_token_buy_level_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgWarToken.war_token_buy_level_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<war_token_buy_level_c2s>
        PARSER = new com.google.protobuf.AbstractParser<war_token_buy_level_c2s>() {
      @java.lang.Override
      public war_token_buy_level_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<war_token_buy_level_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<war_token_buy_level_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgWarToken.war_token_buy_level_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_war_token_info_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_war_token_info_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_war_token_info_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_war_token_info_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_war_token_close_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_war_token_close_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_war_token_update_task_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_war_token_update_task_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_war_token_task_reward_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_war_token_task_reward_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_war_token_task_reward_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_war_token_task_reward_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_war_token_lev_reward_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_war_token_lev_reward_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_war_token_lev_reward_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_war_token_lev_reward_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_war_token_add_reward_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_war_token_add_reward_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_war_token_add_reward_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_war_token_add_reward_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_war_token_update_exp_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_war_token_update_exp_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_war_token_claim_special_reward_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_war_token_claim_special_reward_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_war_token_claim_special_reward_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_war_token_claim_special_reward_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_war_token_buy_level_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_war_token_buy_level_c2s_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\022msg.warToken.proto\022\031org.gof.demo.world" +
      "srv.msg\032\roptions.proto\032\014define.proto\"\'\n\022" +
      "war_token_info_c2s\022\n\n\002id\030\001 \001(\r:\005\210\303\032\201P\"\210\005" +
      "\n\022war_token_info_s2c\022\n\n\002id\030\001 \001(\r\022\016\n\006if_b" +
      "uy\030\002 \001(\r\022\020\n\010end_time\030\003 \001(\r\022\013\n\003lev\030\004 \001(\r\022" +
      "\017\n\007max_lev\030\005 \001(\r\022\013\n\003exp\030\006 \001(\r\022\020\n\010need_ex" +
      "p\030\007 \001(\r\022\023\n\013extra_times\030\010 \001(\r\022\027\n\017extra_ma" +
      "x_times\030\t \001(\r\022\026\n\016extra_need_exp\030\n \001(\r\022\021\n" +
      "\tbundle_id\030\013 \001(\r\0229\n\014extra_reward\030\014 \003(\0132#" +
      ".org.gof.demo.worldsrv.msg.p_reward\022D\n\013f" +
      "ree_reward\030\r \003(\0132/.org.gof.demo.worldsrv" +
      ".msg.p_reward_status_list\022D\n\013high_reward" +
      "\030\016 \003(\0132/.org.gof.demo.worldsrv.msg.p_rew" +
      "ard_status_list\022>\n\ttask_list\030\017 \003(\0132+.org" +
      ".gof.demo.worldsrv.msg.p_war_token_task\022" +
      "\037\n\023special_reward_list\030\020 \003(\rB\002\020\000\022\021\n\ttota" +
      "l_exp\030\021 \001(\r\022\020\n\010if_buy_2\030\022 \001(\r\022F\n\rhigh_re" +
      "ward_2\030\023 \003(\0132/.org.gof.demo.worldsrv.msg" +
      ".p_reward_status_list\022\022\n\nmerge_flag\030\024 \001(" +
      "\r:\005\210\303\032\201P\"(\n\023war_token_close_s2c\022\n\n\002id\030\001 " +
      "\001(\r:\005\210\303\032\202P\"n\n\031war_token_update_task_s2c\022" +
      "\n\n\002id\030\001 \001(\r\022>\n\ttask_list\030\002 \003(\0132+.org.gof" +
      ".demo.worldsrv.msg.p_war_token_task:\005\210\303\032" +
      "\203P\"?\n\031war_token_task_reward_c2s\022\n\n\002id\030\001 " +
      "\001(\r\022\017\n\007task_id\030\002 \001(\r:\005\210\303\032\204P\"j\n\031war_token" +
      "_task_reward_s2c\022\n\n\002id\030\001 \001(\r\022\017\n\007task_id\030" +
      "\002 \001(\r\022\017\n\007add_exp\030\003 \001(\r\022\013\n\003lev\030\004 \001(\r\022\013\n\003e" +
      "xp\030\005 \001(\r:\005\210\303\032\204P\"M\n\030war_token_lev_reward_" +
      "c2s\022\n\n\002id\030\001 \001(\r\022\020\n\010take_lev\030\002 \001(\r\022\014\n\004typ" +
      "e\030\003 \001(\r:\005\210\303\032\205P\"M\n\030war_token_lev_reward_s" +
      "2c\022\n\n\002id\030\001 \001(\r\022\020\n\010take_lev\030\002 \001(\r\022\014\n\004type" +
      "\030\003 \001(\r:\005\210\303\032\205P\"-\n\030war_token_add_reward_c2" +
      "s\022\n\n\002id\030\001 \001(\r:\005\210\303\032\206P\"O\n\030war_token_add_re" +
      "ward_s2c\022\n\n\002id\030\001 \001(\r\022\013\n\003exp\030\002 \001(\r\022\023\n\013ext" +
      "ra_times\030\003 \001(\r:\005\210\303\032\206P\"Z\n\030war_token_updat" +
      "e_exp_s2c\022\n\n\002id\030\001 \001(\r\022\013\n\003lev\030\002 \001(\r\022\013\n\003ex" +
      "p\030\003 \001(\r\022\021\n\ttotal_exp\030\004 \001(\r:\005\210\303\032\207P\"G\n\"war" +
      "_token_claim_special_reward_c2s\022\n\n\002id\030\001 " +
      "\001(\r\022\016\n\006cfg_id\030\002 \001(\r:\005\210\303\032\210P\"X\n\"war_token_" +
      "claim_special_reward_s2c\022\n\n\002id\030\001 \001(\r\022\037\n\023" +
      "special_reward_list\030\002 \003(\rB\002\020\000:\005\210\303\032\210P\"9\n\027" +
      "war_token_buy_level_c2s\022\n\n\002id\030\001 \001(\r\022\013\n\003n" +
      "um\030\002 \001(\r:\005\210\303\032\211Pb\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          org.gof.demo.worldsrv.msg.Options.getDescriptor(),
          org.gof.demo.worldsrv.msg.Define.getDescriptor(),
        });
    internal_static_org_gof_demo_worldsrv_msg_war_token_info_c2s_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_org_gof_demo_worldsrv_msg_war_token_info_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_war_token_info_c2s_descriptor,
        new java.lang.String[] { "Id", });
    internal_static_org_gof_demo_worldsrv_msg_war_token_info_s2c_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_org_gof_demo_worldsrv_msg_war_token_info_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_war_token_info_s2c_descriptor,
        new java.lang.String[] { "Id", "IfBuy", "EndTime", "Lev", "MaxLev", "Exp", "NeedExp", "ExtraTimes", "ExtraMaxTimes", "ExtraNeedExp", "BundleId", "ExtraReward", "FreeReward", "HighReward", "TaskList", "SpecialRewardList", "TotalExp", "IfBuy2", "HighReward2", "MergeFlag", });
    internal_static_org_gof_demo_worldsrv_msg_war_token_close_s2c_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_org_gof_demo_worldsrv_msg_war_token_close_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_war_token_close_s2c_descriptor,
        new java.lang.String[] { "Id", });
    internal_static_org_gof_demo_worldsrv_msg_war_token_update_task_s2c_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_org_gof_demo_worldsrv_msg_war_token_update_task_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_war_token_update_task_s2c_descriptor,
        new java.lang.String[] { "Id", "TaskList", });
    internal_static_org_gof_demo_worldsrv_msg_war_token_task_reward_c2s_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_org_gof_demo_worldsrv_msg_war_token_task_reward_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_war_token_task_reward_c2s_descriptor,
        new java.lang.String[] { "Id", "TaskId", });
    internal_static_org_gof_demo_worldsrv_msg_war_token_task_reward_s2c_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_org_gof_demo_worldsrv_msg_war_token_task_reward_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_war_token_task_reward_s2c_descriptor,
        new java.lang.String[] { "Id", "TaskId", "AddExp", "Lev", "Exp", });
    internal_static_org_gof_demo_worldsrv_msg_war_token_lev_reward_c2s_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_org_gof_demo_worldsrv_msg_war_token_lev_reward_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_war_token_lev_reward_c2s_descriptor,
        new java.lang.String[] { "Id", "TakeLev", "Type", });
    internal_static_org_gof_demo_worldsrv_msg_war_token_lev_reward_s2c_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_org_gof_demo_worldsrv_msg_war_token_lev_reward_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_war_token_lev_reward_s2c_descriptor,
        new java.lang.String[] { "Id", "TakeLev", "Type", });
    internal_static_org_gof_demo_worldsrv_msg_war_token_add_reward_c2s_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_org_gof_demo_worldsrv_msg_war_token_add_reward_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_war_token_add_reward_c2s_descriptor,
        new java.lang.String[] { "Id", });
    internal_static_org_gof_demo_worldsrv_msg_war_token_add_reward_s2c_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_org_gof_demo_worldsrv_msg_war_token_add_reward_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_war_token_add_reward_s2c_descriptor,
        new java.lang.String[] { "Id", "Exp", "ExtraTimes", });
    internal_static_org_gof_demo_worldsrv_msg_war_token_update_exp_s2c_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_org_gof_demo_worldsrv_msg_war_token_update_exp_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_war_token_update_exp_s2c_descriptor,
        new java.lang.String[] { "Id", "Lev", "Exp", "TotalExp", });
    internal_static_org_gof_demo_worldsrv_msg_war_token_claim_special_reward_c2s_descriptor =
      getDescriptor().getMessageTypes().get(11);
    internal_static_org_gof_demo_worldsrv_msg_war_token_claim_special_reward_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_war_token_claim_special_reward_c2s_descriptor,
        new java.lang.String[] { "Id", "CfgId", });
    internal_static_org_gof_demo_worldsrv_msg_war_token_claim_special_reward_s2c_descriptor =
      getDescriptor().getMessageTypes().get(12);
    internal_static_org_gof_demo_worldsrv_msg_war_token_claim_special_reward_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_war_token_claim_special_reward_s2c_descriptor,
        new java.lang.String[] { "Id", "SpecialRewardList", });
    internal_static_org_gof_demo_worldsrv_msg_war_token_buy_level_c2s_descriptor =
      getDescriptor().getMessageTypes().get(13);
    internal_static_org_gof_demo_worldsrv_msg_war_token_buy_level_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_war_token_buy_level_c2s_descriptor,
        new java.lang.String[] { "Id", "Num", });
    com.google.protobuf.ExtensionRegistry registry =
        com.google.protobuf.ExtensionRegistry.newInstance();
    registry.add(org.gof.demo.worldsrv.msg.Options.msgid);
    com.google.protobuf.Descriptors.FileDescriptor
        .internalUpdateFileDescriptor(descriptor, registry);
    org.gof.demo.worldsrv.msg.Options.getDescriptor();
    org.gof.demo.worldsrv.msg.Define.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
