// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: msg.draw.proto

// Protobuf Java Version: 3.25.3
package org.gof.demo.worldsrv.msg;

public final class MsgDraw {
  private MsgDraw() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface draw_info_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.draw_info_c2s)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.draw_info_c2s}
   */
  public static final class draw_info_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.draw_info_c2s)
      draw_info_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use draw_info_c2s.newBuilder() to construct.
    private draw_info_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private draw_info_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new draw_info_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgDraw.internal_static_org_gof_demo_worldsrv_msg_draw_info_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgDraw.internal_static_org_gof_demo_worldsrv_msg_draw_info_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgDraw.draw_info_c2s.class, org.gof.demo.worldsrv.msg.MsgDraw.draw_info_c2s.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgDraw.draw_info_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgDraw.draw_info_c2s other = (org.gof.demo.worldsrv.msg.MsgDraw.draw_info_c2s) obj;

      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgDraw.draw_info_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgDraw.draw_info_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgDraw.draw_info_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgDraw.draw_info_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgDraw.draw_info_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgDraw.draw_info_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgDraw.draw_info_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgDraw.draw_info_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgDraw.draw_info_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgDraw.draw_info_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgDraw.draw_info_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgDraw.draw_info_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgDraw.draw_info_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.draw_info_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.draw_info_c2s)
        org.gof.demo.worldsrv.msg.MsgDraw.draw_info_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgDraw.internal_static_org_gof_demo_worldsrv_msg_draw_info_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgDraw.internal_static_org_gof_demo_worldsrv_msg_draw_info_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgDraw.draw_info_c2s.class, org.gof.demo.worldsrv.msg.MsgDraw.draw_info_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgDraw.draw_info_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgDraw.internal_static_org_gof_demo_worldsrv_msg_draw_info_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgDraw.draw_info_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgDraw.draw_info_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgDraw.draw_info_c2s build() {
        org.gof.demo.worldsrv.msg.MsgDraw.draw_info_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgDraw.draw_info_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgDraw.draw_info_c2s result = new org.gof.demo.worldsrv.msg.MsgDraw.draw_info_c2s(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgDraw.draw_info_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgDraw.draw_info_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgDraw.draw_info_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgDraw.draw_info_c2s.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.draw_info_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.draw_info_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgDraw.draw_info_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgDraw.draw_info_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgDraw.draw_info_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<draw_info_c2s>
        PARSER = new com.google.protobuf.AbstractParser<draw_info_c2s>() {
      @java.lang.Override
      public draw_info_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<draw_info_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<draw_info_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgDraw.draw_info_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface draw_info_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.draw_info_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_draw_info pool_list = 1;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_draw_info> 
        getPoolListList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_draw_info pool_list = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_draw_info getPoolList(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_draw_info pool_list = 1;</code>
     */
    int getPoolListCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_draw_info pool_list = 1;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_draw_infoOrBuilder> 
        getPoolListOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_draw_info pool_list = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_draw_infoOrBuilder getPoolListOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.draw_info_s2c}
   */
  public static final class draw_info_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.draw_info_s2c)
      draw_info_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use draw_info_s2c.newBuilder() to construct.
    private draw_info_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private draw_info_s2c() {
      poolList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new draw_info_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgDraw.internal_static_org_gof_demo_worldsrv_msg_draw_info_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgDraw.internal_static_org_gof_demo_worldsrv_msg_draw_info_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgDraw.draw_info_s2c.class, org.gof.demo.worldsrv.msg.MsgDraw.draw_info_s2c.Builder.class);
    }

    public static final int POOL_LIST_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_draw_info> poolList_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_draw_info pool_list = 1;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_draw_info> getPoolListList() {
      return poolList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_draw_info pool_list = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_draw_infoOrBuilder> 
        getPoolListOrBuilderList() {
      return poolList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_draw_info pool_list = 1;</code>
     */
    @java.lang.Override
    public int getPoolListCount() {
      return poolList_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_draw_info pool_list = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_draw_info getPoolList(int index) {
      return poolList_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_draw_info pool_list = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_draw_infoOrBuilder getPoolListOrBuilder(
        int index) {
      return poolList_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < poolList_.size(); i++) {
        output.writeMessage(1, poolList_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < poolList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, poolList_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgDraw.draw_info_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgDraw.draw_info_s2c other = (org.gof.demo.worldsrv.msg.MsgDraw.draw_info_s2c) obj;

      if (!getPoolListList()
          .equals(other.getPoolListList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getPoolListCount() > 0) {
        hash = (37 * hash) + POOL_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getPoolListList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgDraw.draw_info_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgDraw.draw_info_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgDraw.draw_info_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgDraw.draw_info_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgDraw.draw_info_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgDraw.draw_info_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgDraw.draw_info_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgDraw.draw_info_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgDraw.draw_info_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgDraw.draw_info_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgDraw.draw_info_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgDraw.draw_info_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgDraw.draw_info_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.draw_info_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.draw_info_s2c)
        org.gof.demo.worldsrv.msg.MsgDraw.draw_info_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgDraw.internal_static_org_gof_demo_worldsrv_msg_draw_info_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgDraw.internal_static_org_gof_demo_worldsrv_msg_draw_info_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgDraw.draw_info_s2c.class, org.gof.demo.worldsrv.msg.MsgDraw.draw_info_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgDraw.draw_info_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        if (poolListBuilder_ == null) {
          poolList_ = java.util.Collections.emptyList();
        } else {
          poolList_ = null;
          poolListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgDraw.internal_static_org_gof_demo_worldsrv_msg_draw_info_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgDraw.draw_info_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgDraw.draw_info_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgDraw.draw_info_s2c build() {
        org.gof.demo.worldsrv.msg.MsgDraw.draw_info_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgDraw.draw_info_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgDraw.draw_info_s2c result = new org.gof.demo.worldsrv.msg.MsgDraw.draw_info_s2c(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgDraw.draw_info_s2c result) {
        if (poolListBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            poolList_ = java.util.Collections.unmodifiableList(poolList_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.poolList_ = poolList_;
        } else {
          result.poolList_ = poolListBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgDraw.draw_info_s2c result) {
        int from_bitField0_ = bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgDraw.draw_info_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgDraw.draw_info_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgDraw.draw_info_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgDraw.draw_info_s2c.getDefaultInstance()) return this;
        if (poolListBuilder_ == null) {
          if (!other.poolList_.isEmpty()) {
            if (poolList_.isEmpty()) {
              poolList_ = other.poolList_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensurePoolListIsMutable();
              poolList_.addAll(other.poolList_);
            }
            onChanged();
          }
        } else {
          if (!other.poolList_.isEmpty()) {
            if (poolListBuilder_.isEmpty()) {
              poolListBuilder_.dispose();
              poolListBuilder_ = null;
              poolList_ = other.poolList_;
              bitField0_ = (bitField0_ & ~0x00000001);
              poolListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getPoolListFieldBuilder() : null;
            } else {
              poolListBuilder_.addAllMessages(other.poolList_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                org.gof.demo.worldsrv.msg.Define.p_draw_info m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_draw_info.parser(),
                        extensionRegistry);
                if (poolListBuilder_ == null) {
                  ensurePoolListIsMutable();
                  poolList_.add(m);
                } else {
                  poolListBuilder_.addMessage(m);
                }
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_draw_info> poolList_ =
        java.util.Collections.emptyList();
      private void ensurePoolListIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          poolList_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_draw_info>(poolList_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_draw_info, org.gof.demo.worldsrv.msg.Define.p_draw_info.Builder, org.gof.demo.worldsrv.msg.Define.p_draw_infoOrBuilder> poolListBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_draw_info pool_list = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_draw_info> getPoolListList() {
        if (poolListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(poolList_);
        } else {
          return poolListBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_draw_info pool_list = 1;</code>
       */
      public int getPoolListCount() {
        if (poolListBuilder_ == null) {
          return poolList_.size();
        } else {
          return poolListBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_draw_info pool_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_draw_info getPoolList(int index) {
        if (poolListBuilder_ == null) {
          return poolList_.get(index);
        } else {
          return poolListBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_draw_info pool_list = 1;</code>
       */
      public Builder setPoolList(
          int index, org.gof.demo.worldsrv.msg.Define.p_draw_info value) {
        if (poolListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePoolListIsMutable();
          poolList_.set(index, value);
          onChanged();
        } else {
          poolListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_draw_info pool_list = 1;</code>
       */
      public Builder setPoolList(
          int index, org.gof.demo.worldsrv.msg.Define.p_draw_info.Builder builderForValue) {
        if (poolListBuilder_ == null) {
          ensurePoolListIsMutable();
          poolList_.set(index, builderForValue.build());
          onChanged();
        } else {
          poolListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_draw_info pool_list = 1;</code>
       */
      public Builder addPoolList(org.gof.demo.worldsrv.msg.Define.p_draw_info value) {
        if (poolListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePoolListIsMutable();
          poolList_.add(value);
          onChanged();
        } else {
          poolListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_draw_info pool_list = 1;</code>
       */
      public Builder addPoolList(
          int index, org.gof.demo.worldsrv.msg.Define.p_draw_info value) {
        if (poolListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePoolListIsMutable();
          poolList_.add(index, value);
          onChanged();
        } else {
          poolListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_draw_info pool_list = 1;</code>
       */
      public Builder addPoolList(
          org.gof.demo.worldsrv.msg.Define.p_draw_info.Builder builderForValue) {
        if (poolListBuilder_ == null) {
          ensurePoolListIsMutable();
          poolList_.add(builderForValue.build());
          onChanged();
        } else {
          poolListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_draw_info pool_list = 1;</code>
       */
      public Builder addPoolList(
          int index, org.gof.demo.worldsrv.msg.Define.p_draw_info.Builder builderForValue) {
        if (poolListBuilder_ == null) {
          ensurePoolListIsMutable();
          poolList_.add(index, builderForValue.build());
          onChanged();
        } else {
          poolListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_draw_info pool_list = 1;</code>
       */
      public Builder addAllPoolList(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_draw_info> values) {
        if (poolListBuilder_ == null) {
          ensurePoolListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, poolList_);
          onChanged();
        } else {
          poolListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_draw_info pool_list = 1;</code>
       */
      public Builder clearPoolList() {
        if (poolListBuilder_ == null) {
          poolList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          poolListBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_draw_info pool_list = 1;</code>
       */
      public Builder removePoolList(int index) {
        if (poolListBuilder_ == null) {
          ensurePoolListIsMutable();
          poolList_.remove(index);
          onChanged();
        } else {
          poolListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_draw_info pool_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_draw_info.Builder getPoolListBuilder(
          int index) {
        return getPoolListFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_draw_info pool_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_draw_infoOrBuilder getPoolListOrBuilder(
          int index) {
        if (poolListBuilder_ == null) {
          return poolList_.get(index);  } else {
          return poolListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_draw_info pool_list = 1;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_draw_infoOrBuilder> 
           getPoolListOrBuilderList() {
        if (poolListBuilder_ != null) {
          return poolListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(poolList_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_draw_info pool_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_draw_info.Builder addPoolListBuilder() {
        return getPoolListFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_draw_info.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_draw_info pool_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_draw_info.Builder addPoolListBuilder(
          int index) {
        return getPoolListFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_draw_info.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_draw_info pool_list = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_draw_info.Builder> 
           getPoolListBuilderList() {
        return getPoolListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_draw_info, org.gof.demo.worldsrv.msg.Define.p_draw_info.Builder, org.gof.demo.worldsrv.msg.Define.p_draw_infoOrBuilder> 
          getPoolListFieldBuilder() {
        if (poolListBuilder_ == null) {
          poolListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_draw_info, org.gof.demo.worldsrv.msg.Define.p_draw_info.Builder, org.gof.demo.worldsrv.msg.Define.p_draw_infoOrBuilder>(
                  poolList_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          poolList_ = null;
        }
        return poolListBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.draw_info_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.draw_info_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgDraw.draw_info_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgDraw.draw_info_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgDraw.draw_info_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<draw_info_s2c>
        PARSER = new com.google.protobuf.AbstractParser<draw_info_s2c>() {
      @java.lang.Override
      public draw_info_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<draw_info_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<draw_info_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgDraw.draw_info_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface draw_card_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.draw_card_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 pool_id = 1;</code>
     * @return The poolId.
     */
    int getPoolId();

    /**
     * <code>int32 num = 2;</code>
     * @return The num.
     */
    int getNum();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.draw_card_c2s}
   */
  public static final class draw_card_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.draw_card_c2s)
      draw_card_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use draw_card_c2s.newBuilder() to construct.
    private draw_card_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private draw_card_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new draw_card_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgDraw.internal_static_org_gof_demo_worldsrv_msg_draw_card_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgDraw.internal_static_org_gof_demo_worldsrv_msg_draw_card_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgDraw.draw_card_c2s.class, org.gof.demo.worldsrv.msg.MsgDraw.draw_card_c2s.Builder.class);
    }

    public static final int POOL_ID_FIELD_NUMBER = 1;
    private int poolId_ = 0;
    /**
     * <code>int32 pool_id = 1;</code>
     * @return The poolId.
     */
    @java.lang.Override
    public int getPoolId() {
      return poolId_;
    }

    public static final int NUM_FIELD_NUMBER = 2;
    private int num_ = 0;
    /**
     * <code>int32 num = 2;</code>
     * @return The num.
     */
    @java.lang.Override
    public int getNum() {
      return num_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (poolId_ != 0) {
        output.writeInt32(1, poolId_);
      }
      if (num_ != 0) {
        output.writeInt32(2, num_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (poolId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, poolId_);
      }
      if (num_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, num_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgDraw.draw_card_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgDraw.draw_card_c2s other = (org.gof.demo.worldsrv.msg.MsgDraw.draw_card_c2s) obj;

      if (getPoolId()
          != other.getPoolId()) return false;
      if (getNum()
          != other.getNum()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + POOL_ID_FIELD_NUMBER;
      hash = (53 * hash) + getPoolId();
      hash = (37 * hash) + NUM_FIELD_NUMBER;
      hash = (53 * hash) + getNum();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgDraw.draw_card_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgDraw.draw_card_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgDraw.draw_card_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgDraw.draw_card_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgDraw.draw_card_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgDraw.draw_card_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgDraw.draw_card_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgDraw.draw_card_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgDraw.draw_card_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgDraw.draw_card_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgDraw.draw_card_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgDraw.draw_card_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgDraw.draw_card_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.draw_card_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.draw_card_c2s)
        org.gof.demo.worldsrv.msg.MsgDraw.draw_card_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgDraw.internal_static_org_gof_demo_worldsrv_msg_draw_card_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgDraw.internal_static_org_gof_demo_worldsrv_msg_draw_card_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgDraw.draw_card_c2s.class, org.gof.demo.worldsrv.msg.MsgDraw.draw_card_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgDraw.draw_card_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        poolId_ = 0;
        num_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgDraw.internal_static_org_gof_demo_worldsrv_msg_draw_card_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgDraw.draw_card_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgDraw.draw_card_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgDraw.draw_card_c2s build() {
        org.gof.demo.worldsrv.msg.MsgDraw.draw_card_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgDraw.draw_card_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgDraw.draw_card_c2s result = new org.gof.demo.worldsrv.msg.MsgDraw.draw_card_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgDraw.draw_card_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.poolId_ = poolId_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.num_ = num_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgDraw.draw_card_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgDraw.draw_card_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgDraw.draw_card_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgDraw.draw_card_c2s.getDefaultInstance()) return this;
        if (other.getPoolId() != 0) {
          setPoolId(other.getPoolId());
        }
        if (other.getNum() != 0) {
          setNum(other.getNum());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                poolId_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                num_ = input.readInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int poolId_ ;
      /**
       * <code>int32 pool_id = 1;</code>
       * @return The poolId.
       */
      @java.lang.Override
      public int getPoolId() {
        return poolId_;
      }
      /**
       * <code>int32 pool_id = 1;</code>
       * @param value The poolId to set.
       * @return This builder for chaining.
       */
      public Builder setPoolId(int value) {

        poolId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>int32 pool_id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPoolId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        poolId_ = 0;
        onChanged();
        return this;
      }

      private int num_ ;
      /**
       * <code>int32 num = 2;</code>
       * @return The num.
       */
      @java.lang.Override
      public int getNum() {
        return num_;
      }
      /**
       * <code>int32 num = 2;</code>
       * @param value The num to set.
       * @return This builder for chaining.
       */
      public Builder setNum(int value) {

        num_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>int32 num = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearNum() {
        bitField0_ = (bitField0_ & ~0x00000002);
        num_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.draw_card_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.draw_card_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgDraw.draw_card_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgDraw.draw_card_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgDraw.draw_card_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<draw_card_c2s>
        PARSER = new com.google.protobuf.AbstractParser<draw_card_c2s>() {
      @java.lang.Override
      public draw_card_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<draw_card_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<draw_card_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgDraw.draw_card_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface draw_card_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.draw_card_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.org.gof.demo.worldsrv.msg.p_draw_info pool_list = 1;</code>
     * @return Whether the poolList field is set.
     */
    boolean hasPoolList();
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_draw_info pool_list = 1;</code>
     * @return The poolList.
     */
    org.gof.demo.worldsrv.msg.Define.p_draw_info getPoolList();
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_draw_info pool_list = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_draw_infoOrBuilder getPoolListOrBuilder();

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward> 
        getRewardListList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_reward getRewardList(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
     */
    int getRewardListCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder> 
        getRewardListOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder getRewardListOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.draw_card_s2c}
   */
  public static final class draw_card_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.draw_card_s2c)
      draw_card_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use draw_card_s2c.newBuilder() to construct.
    private draw_card_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private draw_card_s2c() {
      rewardList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new draw_card_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgDraw.internal_static_org_gof_demo_worldsrv_msg_draw_card_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgDraw.internal_static_org_gof_demo_worldsrv_msg_draw_card_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgDraw.draw_card_s2c.class, org.gof.demo.worldsrv.msg.MsgDraw.draw_card_s2c.Builder.class);
    }

    private int bitField0_;
    public static final int POOL_LIST_FIELD_NUMBER = 1;
    private org.gof.demo.worldsrv.msg.Define.p_draw_info poolList_;
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_draw_info pool_list = 1;</code>
     * @return Whether the poolList field is set.
     */
    @java.lang.Override
    public boolean hasPoolList() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_draw_info pool_list = 1;</code>
     * @return The poolList.
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_draw_info getPoolList() {
      return poolList_ == null ? org.gof.demo.worldsrv.msg.Define.p_draw_info.getDefaultInstance() : poolList_;
    }
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_draw_info pool_list = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_draw_infoOrBuilder getPoolListOrBuilder() {
      return poolList_ == null ? org.gof.demo.worldsrv.msg.Define.p_draw_info.getDefaultInstance() : poolList_;
    }

    public static final int REWARD_LIST_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward> rewardList_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward> getRewardListList() {
      return rewardList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder> 
        getRewardListOrBuilderList() {
      return rewardList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
     */
    @java.lang.Override
    public int getRewardListCount() {
      return rewardList_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_reward getRewardList(int index) {
      return rewardList_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder getRewardListOrBuilder(
        int index) {
      return rewardList_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getPoolList());
      }
      for (int i = 0; i < rewardList_.size(); i++) {
        output.writeMessage(2, rewardList_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getPoolList());
      }
      for (int i = 0; i < rewardList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, rewardList_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgDraw.draw_card_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgDraw.draw_card_s2c other = (org.gof.demo.worldsrv.msg.MsgDraw.draw_card_s2c) obj;

      if (hasPoolList() != other.hasPoolList()) return false;
      if (hasPoolList()) {
        if (!getPoolList()
            .equals(other.getPoolList())) return false;
      }
      if (!getRewardListList()
          .equals(other.getRewardListList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPoolList()) {
        hash = (37 * hash) + POOL_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getPoolList().hashCode();
      }
      if (getRewardListCount() > 0) {
        hash = (37 * hash) + REWARD_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getRewardListList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgDraw.draw_card_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgDraw.draw_card_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgDraw.draw_card_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgDraw.draw_card_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgDraw.draw_card_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgDraw.draw_card_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgDraw.draw_card_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgDraw.draw_card_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgDraw.draw_card_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgDraw.draw_card_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgDraw.draw_card_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgDraw.draw_card_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgDraw.draw_card_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.draw_card_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.draw_card_s2c)
        org.gof.demo.worldsrv.msg.MsgDraw.draw_card_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgDraw.internal_static_org_gof_demo_worldsrv_msg_draw_card_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgDraw.internal_static_org_gof_demo_worldsrv_msg_draw_card_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgDraw.draw_card_s2c.class, org.gof.demo.worldsrv.msg.MsgDraw.draw_card_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgDraw.draw_card_s2c.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getPoolListFieldBuilder();
          getRewardListFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        poolList_ = null;
        if (poolListBuilder_ != null) {
          poolListBuilder_.dispose();
          poolListBuilder_ = null;
        }
        if (rewardListBuilder_ == null) {
          rewardList_ = java.util.Collections.emptyList();
        } else {
          rewardList_ = null;
          rewardListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgDraw.internal_static_org_gof_demo_worldsrv_msg_draw_card_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgDraw.draw_card_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgDraw.draw_card_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgDraw.draw_card_s2c build() {
        org.gof.demo.worldsrv.msg.MsgDraw.draw_card_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgDraw.draw_card_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgDraw.draw_card_s2c result = new org.gof.demo.worldsrv.msg.MsgDraw.draw_card_s2c(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgDraw.draw_card_s2c result) {
        if (rewardListBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0)) {
            rewardList_ = java.util.Collections.unmodifiableList(rewardList_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.rewardList_ = rewardList_;
        } else {
          result.rewardList_ = rewardListBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgDraw.draw_card_s2c result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.poolList_ = poolListBuilder_ == null
              ? poolList_
              : poolListBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgDraw.draw_card_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgDraw.draw_card_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgDraw.draw_card_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgDraw.draw_card_s2c.getDefaultInstance()) return this;
        if (other.hasPoolList()) {
          mergePoolList(other.getPoolList());
        }
        if (rewardListBuilder_ == null) {
          if (!other.rewardList_.isEmpty()) {
            if (rewardList_.isEmpty()) {
              rewardList_ = other.rewardList_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensureRewardListIsMutable();
              rewardList_.addAll(other.rewardList_);
            }
            onChanged();
          }
        } else {
          if (!other.rewardList_.isEmpty()) {
            if (rewardListBuilder_.isEmpty()) {
              rewardListBuilder_.dispose();
              rewardListBuilder_ = null;
              rewardList_ = other.rewardList_;
              bitField0_ = (bitField0_ & ~0x00000002);
              rewardListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getRewardListFieldBuilder() : null;
            } else {
              rewardListBuilder_.addAllMessages(other.rewardList_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getPoolListFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 18: {
                org.gof.demo.worldsrv.msg.Define.p_reward m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_reward.parser(),
                        extensionRegistry);
                if (rewardListBuilder_ == null) {
                  ensureRewardListIsMutable();
                  rewardList_.add(m);
                } else {
                  rewardListBuilder_.addMessage(m);
                }
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private org.gof.demo.worldsrv.msg.Define.p_draw_info poolList_;
      private com.google.protobuf.SingleFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_draw_info, org.gof.demo.worldsrv.msg.Define.p_draw_info.Builder, org.gof.demo.worldsrv.msg.Define.p_draw_infoOrBuilder> poolListBuilder_;
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_draw_info pool_list = 1;</code>
       * @return Whether the poolList field is set.
       */
      public boolean hasPoolList() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_draw_info pool_list = 1;</code>
       * @return The poolList.
       */
      public org.gof.demo.worldsrv.msg.Define.p_draw_info getPoolList() {
        if (poolListBuilder_ == null) {
          return poolList_ == null ? org.gof.demo.worldsrv.msg.Define.p_draw_info.getDefaultInstance() : poolList_;
        } else {
          return poolListBuilder_.getMessage();
        }
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_draw_info pool_list = 1;</code>
       */
      public Builder setPoolList(org.gof.demo.worldsrv.msg.Define.p_draw_info value) {
        if (poolListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          poolList_ = value;
        } else {
          poolListBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_draw_info pool_list = 1;</code>
       */
      public Builder setPoolList(
          org.gof.demo.worldsrv.msg.Define.p_draw_info.Builder builderForValue) {
        if (poolListBuilder_ == null) {
          poolList_ = builderForValue.build();
        } else {
          poolListBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_draw_info pool_list = 1;</code>
       */
      public Builder mergePoolList(org.gof.demo.worldsrv.msg.Define.p_draw_info value) {
        if (poolListBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
            poolList_ != null &&
            poolList_ != org.gof.demo.worldsrv.msg.Define.p_draw_info.getDefaultInstance()) {
            getPoolListBuilder().mergeFrom(value);
          } else {
            poolList_ = value;
          }
        } else {
          poolListBuilder_.mergeFrom(value);
        }
        if (poolList_ != null) {
          bitField0_ |= 0x00000001;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_draw_info pool_list = 1;</code>
       */
      public Builder clearPoolList() {
        bitField0_ = (bitField0_ & ~0x00000001);
        poolList_ = null;
        if (poolListBuilder_ != null) {
          poolListBuilder_.dispose();
          poolListBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_draw_info pool_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_draw_info.Builder getPoolListBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getPoolListFieldBuilder().getBuilder();
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_draw_info pool_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_draw_infoOrBuilder getPoolListOrBuilder() {
        if (poolListBuilder_ != null) {
          return poolListBuilder_.getMessageOrBuilder();
        } else {
          return poolList_ == null ?
              org.gof.demo.worldsrv.msg.Define.p_draw_info.getDefaultInstance() : poolList_;
        }
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_draw_info pool_list = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_draw_info, org.gof.demo.worldsrv.msg.Define.p_draw_info.Builder, org.gof.demo.worldsrv.msg.Define.p_draw_infoOrBuilder> 
          getPoolListFieldBuilder() {
        if (poolListBuilder_ == null) {
          poolListBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_draw_info, org.gof.demo.worldsrv.msg.Define.p_draw_info.Builder, org.gof.demo.worldsrv.msg.Define.p_draw_infoOrBuilder>(
                  getPoolList(),
                  getParentForChildren(),
                  isClean());
          poolList_ = null;
        }
        return poolListBuilder_;
      }

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward> rewardList_ =
        java.util.Collections.emptyList();
      private void ensureRewardListIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          rewardList_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_reward>(rewardList_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_reward, org.gof.demo.worldsrv.msg.Define.p_reward.Builder, org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder> rewardListBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward> getRewardListList() {
        if (rewardListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(rewardList_);
        } else {
          return rewardListBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
       */
      public int getRewardListCount() {
        if (rewardListBuilder_ == null) {
          return rewardList_.size();
        } else {
          return rewardListBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_reward getRewardList(int index) {
        if (rewardListBuilder_ == null) {
          return rewardList_.get(index);
        } else {
          return rewardListBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
       */
      public Builder setRewardList(
          int index, org.gof.demo.worldsrv.msg.Define.p_reward value) {
        if (rewardListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRewardListIsMutable();
          rewardList_.set(index, value);
          onChanged();
        } else {
          rewardListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
       */
      public Builder setRewardList(
          int index, org.gof.demo.worldsrv.msg.Define.p_reward.Builder builderForValue) {
        if (rewardListBuilder_ == null) {
          ensureRewardListIsMutable();
          rewardList_.set(index, builderForValue.build());
          onChanged();
        } else {
          rewardListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
       */
      public Builder addRewardList(org.gof.demo.worldsrv.msg.Define.p_reward value) {
        if (rewardListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRewardListIsMutable();
          rewardList_.add(value);
          onChanged();
        } else {
          rewardListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
       */
      public Builder addRewardList(
          int index, org.gof.demo.worldsrv.msg.Define.p_reward value) {
        if (rewardListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRewardListIsMutable();
          rewardList_.add(index, value);
          onChanged();
        } else {
          rewardListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
       */
      public Builder addRewardList(
          org.gof.demo.worldsrv.msg.Define.p_reward.Builder builderForValue) {
        if (rewardListBuilder_ == null) {
          ensureRewardListIsMutable();
          rewardList_.add(builderForValue.build());
          onChanged();
        } else {
          rewardListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
       */
      public Builder addRewardList(
          int index, org.gof.demo.worldsrv.msg.Define.p_reward.Builder builderForValue) {
        if (rewardListBuilder_ == null) {
          ensureRewardListIsMutable();
          rewardList_.add(index, builderForValue.build());
          onChanged();
        } else {
          rewardListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
       */
      public Builder addAllRewardList(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_reward> values) {
        if (rewardListBuilder_ == null) {
          ensureRewardListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, rewardList_);
          onChanged();
        } else {
          rewardListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
       */
      public Builder clearRewardList() {
        if (rewardListBuilder_ == null) {
          rewardList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          rewardListBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
       */
      public Builder removeRewardList(int index) {
        if (rewardListBuilder_ == null) {
          ensureRewardListIsMutable();
          rewardList_.remove(index);
          onChanged();
        } else {
          rewardListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_reward.Builder getRewardListBuilder(
          int index) {
        return getRewardListFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder getRewardListOrBuilder(
          int index) {
        if (rewardListBuilder_ == null) {
          return rewardList_.get(index);  } else {
          return rewardListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder> 
           getRewardListOrBuilderList() {
        if (rewardListBuilder_ != null) {
          return rewardListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(rewardList_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_reward.Builder addRewardListBuilder() {
        return getRewardListFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_reward.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_reward.Builder addRewardListBuilder(
          int index) {
        return getRewardListFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_reward.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward.Builder> 
           getRewardListBuilderList() {
        return getRewardListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_reward, org.gof.demo.worldsrv.msg.Define.p_reward.Builder, org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder> 
          getRewardListFieldBuilder() {
        if (rewardListBuilder_ == null) {
          rewardListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_reward, org.gof.demo.worldsrv.msg.Define.p_reward.Builder, org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder>(
                  rewardList_,
                  ((bitField0_ & 0x00000002) != 0),
                  getParentForChildren(),
                  isClean());
          rewardList_ = null;
        }
        return rewardListBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.draw_card_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.draw_card_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgDraw.draw_card_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgDraw.draw_card_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgDraw.draw_card_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<draw_card_s2c>
        PARSER = new com.google.protobuf.AbstractParser<draw_card_s2c>() {
      @java.lang.Override
      public draw_card_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<draw_card_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<draw_card_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgDraw.draw_card_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_draw_info_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_draw_info_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_draw_info_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_draw_info_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_draw_card_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_draw_card_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_draw_card_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_draw_card_s2c_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\016msg.draw.proto\022\031org.gof.demo.worldsrv." +
      "msg\032\roptions.proto\032\014define.proto\"\026\n\rdraw" +
      "_info_c2s:\005\210\303\032\201\022\"Q\n\rdraw_info_s2c\0229\n\tpoo" +
      "l_list\030\001 \003(\0132&.org.gof.demo.worldsrv.msg" +
      ".p_draw_info:\005\210\303\032\201\022\"4\n\rdraw_card_c2s\022\017\n\007" +
      "pool_id\030\001 \001(\005\022\013\n\003num\030\002 \001(\005:\005\210\303\032\202\022\"\213\001\n\rdr" +
      "aw_card_s2c\0229\n\tpool_list\030\001 \001(\0132&.org.gof" +
      ".demo.worldsrv.msg.p_draw_info\0228\n\013reward" +
      "_list\030\002 \003(\0132#.org.gof.demo.worldsrv.msg." +
      "p_reward:\005\210\303\032\202\022b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          org.gof.demo.worldsrv.msg.Options.getDescriptor(),
          org.gof.demo.worldsrv.msg.Define.getDescriptor(),
        });
    internal_static_org_gof_demo_worldsrv_msg_draw_info_c2s_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_org_gof_demo_worldsrv_msg_draw_info_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_draw_info_c2s_descriptor,
        new java.lang.String[] { });
    internal_static_org_gof_demo_worldsrv_msg_draw_info_s2c_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_org_gof_demo_worldsrv_msg_draw_info_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_draw_info_s2c_descriptor,
        new java.lang.String[] { "PoolList", });
    internal_static_org_gof_demo_worldsrv_msg_draw_card_c2s_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_org_gof_demo_worldsrv_msg_draw_card_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_draw_card_c2s_descriptor,
        new java.lang.String[] { "PoolId", "Num", });
    internal_static_org_gof_demo_worldsrv_msg_draw_card_s2c_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_org_gof_demo_worldsrv_msg_draw_card_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_draw_card_s2c_descriptor,
        new java.lang.String[] { "PoolList", "RewardList", });
    com.google.protobuf.ExtensionRegistry registry =
        com.google.protobuf.ExtensionRegistry.newInstance();
    registry.add(org.gof.demo.worldsrv.msg.Options.msgid);
    com.google.protobuf.Descriptors.FileDescriptor
        .internalUpdateFileDescriptor(descriptor, registry);
    org.gof.demo.worldsrv.msg.Options.getDescriptor();
    org.gof.demo.worldsrv.msg.Define.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
