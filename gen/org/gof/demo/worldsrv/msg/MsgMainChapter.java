// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: msg.mainChapter.proto

// Protobuf Java Version: 3.25.3
package org.gof.demo.worldsrv.msg;

public final class MsgMainChapter {
  private MsgMainChapter() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface main_chapter_info_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.main_chapter_info_c2s)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.main_chapter_info_c2s}
   */
  public static final class main_chapter_info_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.main_chapter_info_c2s)
      main_chapter_info_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use main_chapter_info_c2s.newBuilder() to construct.
    private main_chapter_info_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private main_chapter_info_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new main_chapter_info_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_info_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_info_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_c2s.class, org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_c2s.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_c2s other = (org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_c2s) obj;

      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.main_chapter_info_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.main_chapter_info_c2s)
        org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_info_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_info_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_c2s.class, org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_info_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_c2s build() {
        org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_c2s result = new org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_c2s(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_c2s.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.main_chapter_info_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.main_chapter_info_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<main_chapter_info_c2s>
        PARSER = new com.google.protobuf.AbstractParser<main_chapter_info_c2s>() {
      @java.lang.Override
      public main_chapter_info_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<main_chapter_info_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<main_chapter_info_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface main_chapter_info_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.main_chapter_info_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 part_id = 1;</code>
     * @return The partId.
     */
    int getPartId();

    /**
     * <code>uint32 is_unlimited = 2;</code>
     * @return The isUnlimited.
     */
    int getIsUnlimited();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.main_chapter_info_s2c}
   */
  public static final class main_chapter_info_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.main_chapter_info_s2c)
      main_chapter_info_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use main_chapter_info_s2c.newBuilder() to construct.
    private main_chapter_info_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private main_chapter_info_s2c() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new main_chapter_info_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_info_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_info_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_s2c.class, org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_s2c.Builder.class);
    }

    public static final int PART_ID_FIELD_NUMBER = 1;
    private int partId_ = 0;
    /**
     * <code>uint32 part_id = 1;</code>
     * @return The partId.
     */
    @java.lang.Override
    public int getPartId() {
      return partId_;
    }

    public static final int IS_UNLIMITED_FIELD_NUMBER = 2;
    private int isUnlimited_ = 0;
    /**
     * <code>uint32 is_unlimited = 2;</code>
     * @return The isUnlimited.
     */
    @java.lang.Override
    public int getIsUnlimited() {
      return isUnlimited_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (partId_ != 0) {
        output.writeUInt32(1, partId_);
      }
      if (isUnlimited_ != 0) {
        output.writeUInt32(2, isUnlimited_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (partId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, partId_);
      }
      if (isUnlimited_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, isUnlimited_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_s2c other = (org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_s2c) obj;

      if (getPartId()
          != other.getPartId()) return false;
      if (getIsUnlimited()
          != other.getIsUnlimited()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + PART_ID_FIELD_NUMBER;
      hash = (53 * hash) + getPartId();
      hash = (37 * hash) + IS_UNLIMITED_FIELD_NUMBER;
      hash = (53 * hash) + getIsUnlimited();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.main_chapter_info_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.main_chapter_info_s2c)
        org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_info_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_info_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_s2c.class, org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        partId_ = 0;
        isUnlimited_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_info_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_s2c build() {
        org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_s2c result = new org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.partId_ = partId_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.isUnlimited_ = isUnlimited_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_s2c.getDefaultInstance()) return this;
        if (other.getPartId() != 0) {
          setPartId(other.getPartId());
        }
        if (other.getIsUnlimited() != 0) {
          setIsUnlimited(other.getIsUnlimited());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                partId_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                isUnlimited_ = input.readUInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int partId_ ;
      /**
       * <code>uint32 part_id = 1;</code>
       * @return The partId.
       */
      @java.lang.Override
      public int getPartId() {
        return partId_;
      }
      /**
       * <code>uint32 part_id = 1;</code>
       * @param value The partId to set.
       * @return This builder for chaining.
       */
      public Builder setPartId(int value) {

        partId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 part_id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPartId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        partId_ = 0;
        onChanged();
        return this;
      }

      private int isUnlimited_ ;
      /**
       * <code>uint32 is_unlimited = 2;</code>
       * @return The isUnlimited.
       */
      @java.lang.Override
      public int getIsUnlimited() {
        return isUnlimited_;
      }
      /**
       * <code>uint32 is_unlimited = 2;</code>
       * @param value The isUnlimited to set.
       * @return This builder for chaining.
       */
      public Builder setIsUnlimited(int value) {

        isUnlimited_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 is_unlimited = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsUnlimited() {
        bitField0_ = (bitField0_ & ~0x00000002);
        isUnlimited_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.main_chapter_info_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.main_chapter_info_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<main_chapter_info_s2c>
        PARSER = new com.google.protobuf.AbstractParser<main_chapter_info_s2c>() {
      @java.lang.Override
      public main_chapter_info_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<main_chapter_info_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<main_chapter_info_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_info_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface main_chapter_enter_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.main_chapter_enter_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 part_id = 1;</code>
     * @return The partId.
     */
    int getPartId();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.main_chapter_enter_c2s}
   */
  public static final class main_chapter_enter_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.main_chapter_enter_c2s)
      main_chapter_enter_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use main_chapter_enter_c2s.newBuilder() to construct.
    private main_chapter_enter_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private main_chapter_enter_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new main_chapter_enter_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_enter_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_enter_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_c2s.class, org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_c2s.Builder.class);
    }

    public static final int PART_ID_FIELD_NUMBER = 1;
    private int partId_ = 0;
    /**
     * <code>uint32 part_id = 1;</code>
     * @return The partId.
     */
    @java.lang.Override
    public int getPartId() {
      return partId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (partId_ != 0) {
        output.writeUInt32(1, partId_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (partId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, partId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_c2s other = (org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_c2s) obj;

      if (getPartId()
          != other.getPartId()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + PART_ID_FIELD_NUMBER;
      hash = (53 * hash) + getPartId();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.main_chapter_enter_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.main_chapter_enter_c2s)
        org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_enter_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_enter_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_c2s.class, org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        partId_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_enter_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_c2s build() {
        org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_c2s result = new org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.partId_ = partId_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_c2s.getDefaultInstance()) return this;
        if (other.getPartId() != 0) {
          setPartId(other.getPartId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                partId_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int partId_ ;
      /**
       * <code>uint32 part_id = 1;</code>
       * @return The partId.
       */
      @java.lang.Override
      public int getPartId() {
        return partId_;
      }
      /**
       * <code>uint32 part_id = 1;</code>
       * @param value The partId to set.
       * @return This builder for chaining.
       */
      public Builder setPartId(int value) {

        partId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 part_id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPartId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        partId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.main_chapter_enter_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.main_chapter_enter_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<main_chapter_enter_c2s>
        PARSER = new com.google.protobuf.AbstractParser<main_chapter_enter_c2s>() {
      @java.lang.Override
      public main_chapter_enter_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<main_chapter_enter_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<main_chapter_enter_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface main_chapter_enter_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.main_chapter_enter_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 part_id = 1;</code>
     * @return The partId.
     */
    int getPartId();

    /**
     * <code>uint32 is_unlimited = 2;</code>
     * @return The isUnlimited.
     */
    int getIsUnlimited();

    /**
     * <code>uint32 battle_checkout = 3;</code>
     * @return The battleCheckout.
     */
    int getBattleCheckout();

    /**
     * <code>uint64 random_seed = 4;</code>
     * @return The randomSeed.
     */
    long getRandomSeed();

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_role roles = 5;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_battle_role> 
        getRolesList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_role roles = 5;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_battle_role getRoles(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_role roles = 5;</code>
     */
    int getRolesCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_role roles = 5;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_battle_roleOrBuilder> 
        getRolesOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_role roles = 5;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_battle_roleOrBuilder getRolesOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.main_chapter_enter_s2c}
   */
  public static final class main_chapter_enter_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.main_chapter_enter_s2c)
      main_chapter_enter_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use main_chapter_enter_s2c.newBuilder() to construct.
    private main_chapter_enter_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private main_chapter_enter_s2c() {
      roles_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new main_chapter_enter_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_enter_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_enter_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_s2c.class, org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_s2c.Builder.class);
    }

    public static final int PART_ID_FIELD_NUMBER = 1;
    private int partId_ = 0;
    /**
     * <code>uint32 part_id = 1;</code>
     * @return The partId.
     */
    @java.lang.Override
    public int getPartId() {
      return partId_;
    }

    public static final int IS_UNLIMITED_FIELD_NUMBER = 2;
    private int isUnlimited_ = 0;
    /**
     * <code>uint32 is_unlimited = 2;</code>
     * @return The isUnlimited.
     */
    @java.lang.Override
    public int getIsUnlimited() {
      return isUnlimited_;
    }

    public static final int BATTLE_CHECKOUT_FIELD_NUMBER = 3;
    private int battleCheckout_ = 0;
    /**
     * <code>uint32 battle_checkout = 3;</code>
     * @return The battleCheckout.
     */
    @java.lang.Override
    public int getBattleCheckout() {
      return battleCheckout_;
    }

    public static final int RANDOM_SEED_FIELD_NUMBER = 4;
    private long randomSeed_ = 0L;
    /**
     * <code>uint64 random_seed = 4;</code>
     * @return The randomSeed.
     */
    @java.lang.Override
    public long getRandomSeed() {
      return randomSeed_;
    }

    public static final int ROLES_FIELD_NUMBER = 5;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_battle_role> roles_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_role roles = 5;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_battle_role> getRolesList() {
      return roles_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_role roles = 5;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_battle_roleOrBuilder> 
        getRolesOrBuilderList() {
      return roles_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_role roles = 5;</code>
     */
    @java.lang.Override
    public int getRolesCount() {
      return roles_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_role roles = 5;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_battle_role getRoles(int index) {
      return roles_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_role roles = 5;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_battle_roleOrBuilder getRolesOrBuilder(
        int index) {
      return roles_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (partId_ != 0) {
        output.writeUInt32(1, partId_);
      }
      if (isUnlimited_ != 0) {
        output.writeUInt32(2, isUnlimited_);
      }
      if (battleCheckout_ != 0) {
        output.writeUInt32(3, battleCheckout_);
      }
      if (randomSeed_ != 0L) {
        output.writeUInt64(4, randomSeed_);
      }
      for (int i = 0; i < roles_.size(); i++) {
        output.writeMessage(5, roles_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (partId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, partId_);
      }
      if (isUnlimited_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, isUnlimited_);
      }
      if (battleCheckout_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(3, battleCheckout_);
      }
      if (randomSeed_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(4, randomSeed_);
      }
      for (int i = 0; i < roles_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, roles_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_s2c other = (org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_s2c) obj;

      if (getPartId()
          != other.getPartId()) return false;
      if (getIsUnlimited()
          != other.getIsUnlimited()) return false;
      if (getBattleCheckout()
          != other.getBattleCheckout()) return false;
      if (getRandomSeed()
          != other.getRandomSeed()) return false;
      if (!getRolesList()
          .equals(other.getRolesList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + PART_ID_FIELD_NUMBER;
      hash = (53 * hash) + getPartId();
      hash = (37 * hash) + IS_UNLIMITED_FIELD_NUMBER;
      hash = (53 * hash) + getIsUnlimited();
      hash = (37 * hash) + BATTLE_CHECKOUT_FIELD_NUMBER;
      hash = (53 * hash) + getBattleCheckout();
      hash = (37 * hash) + RANDOM_SEED_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getRandomSeed());
      if (getRolesCount() > 0) {
        hash = (37 * hash) + ROLES_FIELD_NUMBER;
        hash = (53 * hash) + getRolesList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.main_chapter_enter_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.main_chapter_enter_s2c)
        org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_enter_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_enter_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_s2c.class, org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        partId_ = 0;
        isUnlimited_ = 0;
        battleCheckout_ = 0;
        randomSeed_ = 0L;
        if (rolesBuilder_ == null) {
          roles_ = java.util.Collections.emptyList();
        } else {
          roles_ = null;
          rolesBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000010);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_enter_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_s2c build() {
        org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_s2c result = new org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_s2c(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_s2c result) {
        if (rolesBuilder_ == null) {
          if (((bitField0_ & 0x00000010) != 0)) {
            roles_ = java.util.Collections.unmodifiableList(roles_);
            bitField0_ = (bitField0_ & ~0x00000010);
          }
          result.roles_ = roles_;
        } else {
          result.roles_ = rolesBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.partId_ = partId_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.isUnlimited_ = isUnlimited_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.battleCheckout_ = battleCheckout_;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.randomSeed_ = randomSeed_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_s2c.getDefaultInstance()) return this;
        if (other.getPartId() != 0) {
          setPartId(other.getPartId());
        }
        if (other.getIsUnlimited() != 0) {
          setIsUnlimited(other.getIsUnlimited());
        }
        if (other.getBattleCheckout() != 0) {
          setBattleCheckout(other.getBattleCheckout());
        }
        if (other.getRandomSeed() != 0L) {
          setRandomSeed(other.getRandomSeed());
        }
        if (rolesBuilder_ == null) {
          if (!other.roles_.isEmpty()) {
            if (roles_.isEmpty()) {
              roles_ = other.roles_;
              bitField0_ = (bitField0_ & ~0x00000010);
            } else {
              ensureRolesIsMutable();
              roles_.addAll(other.roles_);
            }
            onChanged();
          }
        } else {
          if (!other.roles_.isEmpty()) {
            if (rolesBuilder_.isEmpty()) {
              rolesBuilder_.dispose();
              rolesBuilder_ = null;
              roles_ = other.roles_;
              bitField0_ = (bitField0_ & ~0x00000010);
              rolesBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getRolesFieldBuilder() : null;
            } else {
              rolesBuilder_.addAllMessages(other.roles_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                partId_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                isUnlimited_ = input.readUInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                battleCheckout_ = input.readUInt32();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              case 32: {
                randomSeed_ = input.readUInt64();
                bitField0_ |= 0x00000008;
                break;
              } // case 32
              case 42: {
                org.gof.demo.worldsrv.msg.Define.p_battle_role m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_battle_role.parser(),
                        extensionRegistry);
                if (rolesBuilder_ == null) {
                  ensureRolesIsMutable();
                  roles_.add(m);
                } else {
                  rolesBuilder_.addMessage(m);
                }
                break;
              } // case 42
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int partId_ ;
      /**
       * <code>uint32 part_id = 1;</code>
       * @return The partId.
       */
      @java.lang.Override
      public int getPartId() {
        return partId_;
      }
      /**
       * <code>uint32 part_id = 1;</code>
       * @param value The partId to set.
       * @return This builder for chaining.
       */
      public Builder setPartId(int value) {

        partId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 part_id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPartId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        partId_ = 0;
        onChanged();
        return this;
      }

      private int isUnlimited_ ;
      /**
       * <code>uint32 is_unlimited = 2;</code>
       * @return The isUnlimited.
       */
      @java.lang.Override
      public int getIsUnlimited() {
        return isUnlimited_;
      }
      /**
       * <code>uint32 is_unlimited = 2;</code>
       * @param value The isUnlimited to set.
       * @return This builder for chaining.
       */
      public Builder setIsUnlimited(int value) {

        isUnlimited_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 is_unlimited = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsUnlimited() {
        bitField0_ = (bitField0_ & ~0x00000002);
        isUnlimited_ = 0;
        onChanged();
        return this;
      }

      private int battleCheckout_ ;
      /**
       * <code>uint32 battle_checkout = 3;</code>
       * @return The battleCheckout.
       */
      @java.lang.Override
      public int getBattleCheckout() {
        return battleCheckout_;
      }
      /**
       * <code>uint32 battle_checkout = 3;</code>
       * @param value The battleCheckout to set.
       * @return This builder for chaining.
       */
      public Builder setBattleCheckout(int value) {

        battleCheckout_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 battle_checkout = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearBattleCheckout() {
        bitField0_ = (bitField0_ & ~0x00000004);
        battleCheckout_ = 0;
        onChanged();
        return this;
      }

      private long randomSeed_ ;
      /**
       * <code>uint64 random_seed = 4;</code>
       * @return The randomSeed.
       */
      @java.lang.Override
      public long getRandomSeed() {
        return randomSeed_;
      }
      /**
       * <code>uint64 random_seed = 4;</code>
       * @param value The randomSeed to set.
       * @return This builder for chaining.
       */
      public Builder setRandomSeed(long value) {

        randomSeed_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 random_seed = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearRandomSeed() {
        bitField0_ = (bitField0_ & ~0x00000008);
        randomSeed_ = 0L;
        onChanged();
        return this;
      }

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_battle_role> roles_ =
        java.util.Collections.emptyList();
      private void ensureRolesIsMutable() {
        if (!((bitField0_ & 0x00000010) != 0)) {
          roles_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_battle_role>(roles_);
          bitField0_ |= 0x00000010;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_battle_role, org.gof.demo.worldsrv.msg.Define.p_battle_role.Builder, org.gof.demo.worldsrv.msg.Define.p_battle_roleOrBuilder> rolesBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_role roles = 5;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_battle_role> getRolesList() {
        if (rolesBuilder_ == null) {
          return java.util.Collections.unmodifiableList(roles_);
        } else {
          return rolesBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_role roles = 5;</code>
       */
      public int getRolesCount() {
        if (rolesBuilder_ == null) {
          return roles_.size();
        } else {
          return rolesBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_role roles = 5;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_battle_role getRoles(int index) {
        if (rolesBuilder_ == null) {
          return roles_.get(index);
        } else {
          return rolesBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_role roles = 5;</code>
       */
      public Builder setRoles(
          int index, org.gof.demo.worldsrv.msg.Define.p_battle_role value) {
        if (rolesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRolesIsMutable();
          roles_.set(index, value);
          onChanged();
        } else {
          rolesBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_role roles = 5;</code>
       */
      public Builder setRoles(
          int index, org.gof.demo.worldsrv.msg.Define.p_battle_role.Builder builderForValue) {
        if (rolesBuilder_ == null) {
          ensureRolesIsMutable();
          roles_.set(index, builderForValue.build());
          onChanged();
        } else {
          rolesBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_role roles = 5;</code>
       */
      public Builder addRoles(org.gof.demo.worldsrv.msg.Define.p_battle_role value) {
        if (rolesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRolesIsMutable();
          roles_.add(value);
          onChanged();
        } else {
          rolesBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_role roles = 5;</code>
       */
      public Builder addRoles(
          int index, org.gof.demo.worldsrv.msg.Define.p_battle_role value) {
        if (rolesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRolesIsMutable();
          roles_.add(index, value);
          onChanged();
        } else {
          rolesBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_role roles = 5;</code>
       */
      public Builder addRoles(
          org.gof.demo.worldsrv.msg.Define.p_battle_role.Builder builderForValue) {
        if (rolesBuilder_ == null) {
          ensureRolesIsMutable();
          roles_.add(builderForValue.build());
          onChanged();
        } else {
          rolesBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_role roles = 5;</code>
       */
      public Builder addRoles(
          int index, org.gof.demo.worldsrv.msg.Define.p_battle_role.Builder builderForValue) {
        if (rolesBuilder_ == null) {
          ensureRolesIsMutable();
          roles_.add(index, builderForValue.build());
          onChanged();
        } else {
          rolesBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_role roles = 5;</code>
       */
      public Builder addAllRoles(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_battle_role> values) {
        if (rolesBuilder_ == null) {
          ensureRolesIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, roles_);
          onChanged();
        } else {
          rolesBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_role roles = 5;</code>
       */
      public Builder clearRoles() {
        if (rolesBuilder_ == null) {
          roles_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000010);
          onChanged();
        } else {
          rolesBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_role roles = 5;</code>
       */
      public Builder removeRoles(int index) {
        if (rolesBuilder_ == null) {
          ensureRolesIsMutable();
          roles_.remove(index);
          onChanged();
        } else {
          rolesBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_role roles = 5;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_battle_role.Builder getRolesBuilder(
          int index) {
        return getRolesFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_role roles = 5;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_battle_roleOrBuilder getRolesOrBuilder(
          int index) {
        if (rolesBuilder_ == null) {
          return roles_.get(index);  } else {
          return rolesBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_role roles = 5;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_battle_roleOrBuilder> 
           getRolesOrBuilderList() {
        if (rolesBuilder_ != null) {
          return rolesBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(roles_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_role roles = 5;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_battle_role.Builder addRolesBuilder() {
        return getRolesFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_battle_role.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_role roles = 5;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_battle_role.Builder addRolesBuilder(
          int index) {
        return getRolesFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_battle_role.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_role roles = 5;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_battle_role.Builder> 
           getRolesBuilderList() {
        return getRolesFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_battle_role, org.gof.demo.worldsrv.msg.Define.p_battle_role.Builder, org.gof.demo.worldsrv.msg.Define.p_battle_roleOrBuilder> 
          getRolesFieldBuilder() {
        if (rolesBuilder_ == null) {
          rolesBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_battle_role, org.gof.demo.worldsrv.msg.Define.p_battle_role.Builder, org.gof.demo.worldsrv.msg.Define.p_battle_roleOrBuilder>(
                  roles_,
                  ((bitField0_ & 0x00000010) != 0),
                  getParentForChildren(),
                  isClean());
          roles_ = null;
        }
        return rolesBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.main_chapter_enter_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.main_chapter_enter_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<main_chapter_enter_s2c>
        PARSER = new com.google.protobuf.AbstractParser<main_chapter_enter_s2c>() {
      @java.lang.Override
      public main_chapter_enter_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<main_chapter_enter_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<main_chapter_enter_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_enter_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface main_chapter_result_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.main_chapter_result_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 part_id = 1;</code>
     * @return The partId.
     */
    int getPartId();

    /**
     * <code>uint32 result = 2;</code>
     * @return The result.
     */
    int getResult();

    /**
     * <code>uint32 manual_operators = 3;</code>
     * @return The manualOperators.
     */
    int getManualOperators();

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_operator operators = 4;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_battle_operator> 
        getOperatorsList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_operator operators = 4;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_battle_operator getOperators(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_operator operators = 4;</code>
     */
    int getOperatorsCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_operator operators = 4;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_battle_operatorOrBuilder> 
        getOperatorsOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_operator operators = 4;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_battle_operatorOrBuilder getOperatorsOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.main_chapter_result_c2s}
   */
  public static final class main_chapter_result_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.main_chapter_result_c2s)
      main_chapter_result_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use main_chapter_result_c2s.newBuilder() to construct.
    private main_chapter_result_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private main_chapter_result_c2s() {
      operators_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new main_chapter_result_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_result_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_result_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_c2s.class, org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_c2s.Builder.class);
    }

    public static final int PART_ID_FIELD_NUMBER = 1;
    private int partId_ = 0;
    /**
     * <code>uint32 part_id = 1;</code>
     * @return The partId.
     */
    @java.lang.Override
    public int getPartId() {
      return partId_;
    }

    public static final int RESULT_FIELD_NUMBER = 2;
    private int result_ = 0;
    /**
     * <code>uint32 result = 2;</code>
     * @return The result.
     */
    @java.lang.Override
    public int getResult() {
      return result_;
    }

    public static final int MANUAL_OPERATORS_FIELD_NUMBER = 3;
    private int manualOperators_ = 0;
    /**
     * <code>uint32 manual_operators = 3;</code>
     * @return The manualOperators.
     */
    @java.lang.Override
    public int getManualOperators() {
      return manualOperators_;
    }

    public static final int OPERATORS_FIELD_NUMBER = 4;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_battle_operator> operators_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_operator operators = 4;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_battle_operator> getOperatorsList() {
      return operators_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_operator operators = 4;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_battle_operatorOrBuilder> 
        getOperatorsOrBuilderList() {
      return operators_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_operator operators = 4;</code>
     */
    @java.lang.Override
    public int getOperatorsCount() {
      return operators_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_operator operators = 4;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_battle_operator getOperators(int index) {
      return operators_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_operator operators = 4;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_battle_operatorOrBuilder getOperatorsOrBuilder(
        int index) {
      return operators_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (partId_ != 0) {
        output.writeUInt32(1, partId_);
      }
      if (result_ != 0) {
        output.writeUInt32(2, result_);
      }
      if (manualOperators_ != 0) {
        output.writeUInt32(3, manualOperators_);
      }
      for (int i = 0; i < operators_.size(); i++) {
        output.writeMessage(4, operators_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (partId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, partId_);
      }
      if (result_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, result_);
      }
      if (manualOperators_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(3, manualOperators_);
      }
      for (int i = 0; i < operators_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, operators_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_c2s other = (org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_c2s) obj;

      if (getPartId()
          != other.getPartId()) return false;
      if (getResult()
          != other.getResult()) return false;
      if (getManualOperators()
          != other.getManualOperators()) return false;
      if (!getOperatorsList()
          .equals(other.getOperatorsList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + PART_ID_FIELD_NUMBER;
      hash = (53 * hash) + getPartId();
      hash = (37 * hash) + RESULT_FIELD_NUMBER;
      hash = (53 * hash) + getResult();
      hash = (37 * hash) + MANUAL_OPERATORS_FIELD_NUMBER;
      hash = (53 * hash) + getManualOperators();
      if (getOperatorsCount() > 0) {
        hash = (37 * hash) + OPERATORS_FIELD_NUMBER;
        hash = (53 * hash) + getOperatorsList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.main_chapter_result_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.main_chapter_result_c2s)
        org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_result_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_result_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_c2s.class, org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        partId_ = 0;
        result_ = 0;
        manualOperators_ = 0;
        if (operatorsBuilder_ == null) {
          operators_ = java.util.Collections.emptyList();
        } else {
          operators_ = null;
          operatorsBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_result_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_c2s build() {
        org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_c2s result = new org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_c2s(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_c2s result) {
        if (operatorsBuilder_ == null) {
          if (((bitField0_ & 0x00000008) != 0)) {
            operators_ = java.util.Collections.unmodifiableList(operators_);
            bitField0_ = (bitField0_ & ~0x00000008);
          }
          result.operators_ = operators_;
        } else {
          result.operators_ = operatorsBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.partId_ = partId_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.result_ = result_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.manualOperators_ = manualOperators_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_c2s.getDefaultInstance()) return this;
        if (other.getPartId() != 0) {
          setPartId(other.getPartId());
        }
        if (other.getResult() != 0) {
          setResult(other.getResult());
        }
        if (other.getManualOperators() != 0) {
          setManualOperators(other.getManualOperators());
        }
        if (operatorsBuilder_ == null) {
          if (!other.operators_.isEmpty()) {
            if (operators_.isEmpty()) {
              operators_ = other.operators_;
              bitField0_ = (bitField0_ & ~0x00000008);
            } else {
              ensureOperatorsIsMutable();
              operators_.addAll(other.operators_);
            }
            onChanged();
          }
        } else {
          if (!other.operators_.isEmpty()) {
            if (operatorsBuilder_.isEmpty()) {
              operatorsBuilder_.dispose();
              operatorsBuilder_ = null;
              operators_ = other.operators_;
              bitField0_ = (bitField0_ & ~0x00000008);
              operatorsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getOperatorsFieldBuilder() : null;
            } else {
              operatorsBuilder_.addAllMessages(other.operators_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                partId_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                result_ = input.readUInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                manualOperators_ = input.readUInt32();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              case 34: {
                org.gof.demo.worldsrv.msg.Define.p_battle_operator m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_battle_operator.parser(),
                        extensionRegistry);
                if (operatorsBuilder_ == null) {
                  ensureOperatorsIsMutable();
                  operators_.add(m);
                } else {
                  operatorsBuilder_.addMessage(m);
                }
                break;
              } // case 34
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int partId_ ;
      /**
       * <code>uint32 part_id = 1;</code>
       * @return The partId.
       */
      @java.lang.Override
      public int getPartId() {
        return partId_;
      }
      /**
       * <code>uint32 part_id = 1;</code>
       * @param value The partId to set.
       * @return This builder for chaining.
       */
      public Builder setPartId(int value) {

        partId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 part_id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPartId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        partId_ = 0;
        onChanged();
        return this;
      }

      private int result_ ;
      /**
       * <code>uint32 result = 2;</code>
       * @return The result.
       */
      @java.lang.Override
      public int getResult() {
        return result_;
      }
      /**
       * <code>uint32 result = 2;</code>
       * @param value The result to set.
       * @return This builder for chaining.
       */
      public Builder setResult(int value) {

        result_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 result = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearResult() {
        bitField0_ = (bitField0_ & ~0x00000002);
        result_ = 0;
        onChanged();
        return this;
      }

      private int manualOperators_ ;
      /**
       * <code>uint32 manual_operators = 3;</code>
       * @return The manualOperators.
       */
      @java.lang.Override
      public int getManualOperators() {
        return manualOperators_;
      }
      /**
       * <code>uint32 manual_operators = 3;</code>
       * @param value The manualOperators to set.
       * @return This builder for chaining.
       */
      public Builder setManualOperators(int value) {

        manualOperators_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 manual_operators = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearManualOperators() {
        bitField0_ = (bitField0_ & ~0x00000004);
        manualOperators_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_battle_operator> operators_ =
        java.util.Collections.emptyList();
      private void ensureOperatorsIsMutable() {
        if (!((bitField0_ & 0x00000008) != 0)) {
          operators_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_battle_operator>(operators_);
          bitField0_ |= 0x00000008;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_battle_operator, org.gof.demo.worldsrv.msg.Define.p_battle_operator.Builder, org.gof.demo.worldsrv.msg.Define.p_battle_operatorOrBuilder> operatorsBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_operator operators = 4;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_battle_operator> getOperatorsList() {
        if (operatorsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(operators_);
        } else {
          return operatorsBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_operator operators = 4;</code>
       */
      public int getOperatorsCount() {
        if (operatorsBuilder_ == null) {
          return operators_.size();
        } else {
          return operatorsBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_operator operators = 4;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_battle_operator getOperators(int index) {
        if (operatorsBuilder_ == null) {
          return operators_.get(index);
        } else {
          return operatorsBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_operator operators = 4;</code>
       */
      public Builder setOperators(
          int index, org.gof.demo.worldsrv.msg.Define.p_battle_operator value) {
        if (operatorsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureOperatorsIsMutable();
          operators_.set(index, value);
          onChanged();
        } else {
          operatorsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_operator operators = 4;</code>
       */
      public Builder setOperators(
          int index, org.gof.demo.worldsrv.msg.Define.p_battle_operator.Builder builderForValue) {
        if (operatorsBuilder_ == null) {
          ensureOperatorsIsMutable();
          operators_.set(index, builderForValue.build());
          onChanged();
        } else {
          operatorsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_operator operators = 4;</code>
       */
      public Builder addOperators(org.gof.demo.worldsrv.msg.Define.p_battle_operator value) {
        if (operatorsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureOperatorsIsMutable();
          operators_.add(value);
          onChanged();
        } else {
          operatorsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_operator operators = 4;</code>
       */
      public Builder addOperators(
          int index, org.gof.demo.worldsrv.msg.Define.p_battle_operator value) {
        if (operatorsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureOperatorsIsMutable();
          operators_.add(index, value);
          onChanged();
        } else {
          operatorsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_operator operators = 4;</code>
       */
      public Builder addOperators(
          org.gof.demo.worldsrv.msg.Define.p_battle_operator.Builder builderForValue) {
        if (operatorsBuilder_ == null) {
          ensureOperatorsIsMutable();
          operators_.add(builderForValue.build());
          onChanged();
        } else {
          operatorsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_operator operators = 4;</code>
       */
      public Builder addOperators(
          int index, org.gof.demo.worldsrv.msg.Define.p_battle_operator.Builder builderForValue) {
        if (operatorsBuilder_ == null) {
          ensureOperatorsIsMutable();
          operators_.add(index, builderForValue.build());
          onChanged();
        } else {
          operatorsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_operator operators = 4;</code>
       */
      public Builder addAllOperators(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_battle_operator> values) {
        if (operatorsBuilder_ == null) {
          ensureOperatorsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, operators_);
          onChanged();
        } else {
          operatorsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_operator operators = 4;</code>
       */
      public Builder clearOperators() {
        if (operatorsBuilder_ == null) {
          operators_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000008);
          onChanged();
        } else {
          operatorsBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_operator operators = 4;</code>
       */
      public Builder removeOperators(int index) {
        if (operatorsBuilder_ == null) {
          ensureOperatorsIsMutable();
          operators_.remove(index);
          onChanged();
        } else {
          operatorsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_operator operators = 4;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_battle_operator.Builder getOperatorsBuilder(
          int index) {
        return getOperatorsFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_operator operators = 4;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_battle_operatorOrBuilder getOperatorsOrBuilder(
          int index) {
        if (operatorsBuilder_ == null) {
          return operators_.get(index);  } else {
          return operatorsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_operator operators = 4;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_battle_operatorOrBuilder> 
           getOperatorsOrBuilderList() {
        if (operatorsBuilder_ != null) {
          return operatorsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(operators_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_operator operators = 4;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_battle_operator.Builder addOperatorsBuilder() {
        return getOperatorsFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_battle_operator.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_operator operators = 4;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_battle_operator.Builder addOperatorsBuilder(
          int index) {
        return getOperatorsFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_battle_operator.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_battle_operator operators = 4;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_battle_operator.Builder> 
           getOperatorsBuilderList() {
        return getOperatorsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_battle_operator, org.gof.demo.worldsrv.msg.Define.p_battle_operator.Builder, org.gof.demo.worldsrv.msg.Define.p_battle_operatorOrBuilder> 
          getOperatorsFieldBuilder() {
        if (operatorsBuilder_ == null) {
          operatorsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_battle_operator, org.gof.demo.worldsrv.msg.Define.p_battle_operator.Builder, org.gof.demo.worldsrv.msg.Define.p_battle_operatorOrBuilder>(
                  operators_,
                  ((bitField0_ & 0x00000008) != 0),
                  getParentForChildren(),
                  isClean());
          operators_ = null;
        }
        return operatorsBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.main_chapter_result_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.main_chapter_result_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<main_chapter_result_c2s>
        PARSER = new com.google.protobuf.AbstractParser<main_chapter_result_c2s>() {
      @java.lang.Override
      public main_chapter_result_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<main_chapter_result_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<main_chapter_result_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface main_chapter_result_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.main_chapter_result_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <code>uint32 part_id = 2;</code>
     * @return The partId.
     */
    int getPartId();

    /**
     * <code>uint32 is_unlimited = 3;</code>
     * @return The isUnlimited.
     */
    int getIsUnlimited();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.main_chapter_result_s2c}
   */
  public static final class main_chapter_result_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.main_chapter_result_s2c)
      main_chapter_result_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use main_chapter_result_s2c.newBuilder() to construct.
    private main_chapter_result_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private main_chapter_result_s2c() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new main_chapter_result_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_result_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_result_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_s2c.class, org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_s2c.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private int code_ = 0;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int PART_ID_FIELD_NUMBER = 2;
    private int partId_ = 0;
    /**
     * <code>uint32 part_id = 2;</code>
     * @return The partId.
     */
    @java.lang.Override
    public int getPartId() {
      return partId_;
    }

    public static final int IS_UNLIMITED_FIELD_NUMBER = 3;
    private int isUnlimited_ = 0;
    /**
     * <code>uint32 is_unlimited = 3;</code>
     * @return The isUnlimited.
     */
    @java.lang.Override
    public int getIsUnlimited() {
      return isUnlimited_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (partId_ != 0) {
        output.writeUInt32(2, partId_);
      }
      if (isUnlimited_ != 0) {
        output.writeUInt32(3, isUnlimited_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (partId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, partId_);
      }
      if (isUnlimited_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(3, isUnlimited_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_s2c other = (org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_s2c) obj;

      if (getCode()
          != other.getCode()) return false;
      if (getPartId()
          != other.getPartId()) return false;
      if (getIsUnlimited()
          != other.getIsUnlimited()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      hash = (37 * hash) + PART_ID_FIELD_NUMBER;
      hash = (53 * hash) + getPartId();
      hash = (37 * hash) + IS_UNLIMITED_FIELD_NUMBER;
      hash = (53 * hash) + getIsUnlimited();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.main_chapter_result_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.main_chapter_result_s2c)
        org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_result_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_result_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_s2c.class, org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        code_ = 0;
        partId_ = 0;
        isUnlimited_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_result_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_s2c build() {
        org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_s2c result = new org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.code_ = code_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.partId_ = partId_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.isUnlimited_ = isUnlimited_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_s2c.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.getPartId() != 0) {
          setPartId(other.getPartId());
        }
        if (other.getIsUnlimited() != 0) {
          setIsUnlimited(other.getIsUnlimited());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                code_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                partId_ = input.readUInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                isUnlimited_ = input.readUInt32();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {

        code_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        bitField0_ = (bitField0_ & ~0x00000001);
        code_ = 0;
        onChanged();
        return this;
      }

      private int partId_ ;
      /**
       * <code>uint32 part_id = 2;</code>
       * @return The partId.
       */
      @java.lang.Override
      public int getPartId() {
        return partId_;
      }
      /**
       * <code>uint32 part_id = 2;</code>
       * @param value The partId to set.
       * @return This builder for chaining.
       */
      public Builder setPartId(int value) {

        partId_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 part_id = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearPartId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        partId_ = 0;
        onChanged();
        return this;
      }

      private int isUnlimited_ ;
      /**
       * <code>uint32 is_unlimited = 3;</code>
       * @return The isUnlimited.
       */
      @java.lang.Override
      public int getIsUnlimited() {
        return isUnlimited_;
      }
      /**
       * <code>uint32 is_unlimited = 3;</code>
       * @param value The isUnlimited to set.
       * @return This builder for chaining.
       */
      public Builder setIsUnlimited(int value) {

        isUnlimited_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 is_unlimited = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsUnlimited() {
        bitField0_ = (bitField0_ & ~0x00000004);
        isUnlimited_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.main_chapter_result_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.main_chapter_result_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<main_chapter_result_s2c>
        PARSER = new com.google.protobuf.AbstractParser<main_chapter_result_s2c>() {
      @java.lang.Override
      public main_chapter_result_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<main_chapter_result_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<main_chapter_result_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_result_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface main_chapter_kill_reward_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.main_chapter_kill_reward_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 part_id = 1;</code>
     * @return The partId.
     */
    int getPartId();

    /**
     * <code>int64 unit_id = 2;</code>
     * @return The unitId.
     */
    long getUnitId();

    /**
     * <code>.org.gof.demo.worldsrv.msg.p_pos pos = 3;</code>
     * @return Whether the pos field is set.
     */
    boolean hasPos();
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_pos pos = 3;</code>
     * @return The pos.
     */
    org.gof.demo.worldsrv.msg.Define.p_pos getPos();
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_pos pos = 3;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_posOrBuilder getPosOrBuilder();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.main_chapter_kill_reward_c2s}
   */
  public static final class main_chapter_kill_reward_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.main_chapter_kill_reward_c2s)
      main_chapter_kill_reward_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use main_chapter_kill_reward_c2s.newBuilder() to construct.
    private main_chapter_kill_reward_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private main_chapter_kill_reward_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new main_chapter_kill_reward_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_kill_reward_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_kill_reward_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_c2s.class, org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_c2s.Builder.class);
    }

    private int bitField0_;
    public static final int PART_ID_FIELD_NUMBER = 1;
    private int partId_ = 0;
    /**
     * <code>uint32 part_id = 1;</code>
     * @return The partId.
     */
    @java.lang.Override
    public int getPartId() {
      return partId_;
    }

    public static final int UNIT_ID_FIELD_NUMBER = 2;
    private long unitId_ = 0L;
    /**
     * <code>int64 unit_id = 2;</code>
     * @return The unitId.
     */
    @java.lang.Override
    public long getUnitId() {
      return unitId_;
    }

    public static final int POS_FIELD_NUMBER = 3;
    private org.gof.demo.worldsrv.msg.Define.p_pos pos_;
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_pos pos = 3;</code>
     * @return Whether the pos field is set.
     */
    @java.lang.Override
    public boolean hasPos() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_pos pos = 3;</code>
     * @return The pos.
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_pos getPos() {
      return pos_ == null ? org.gof.demo.worldsrv.msg.Define.p_pos.getDefaultInstance() : pos_;
    }
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_pos pos = 3;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_posOrBuilder getPosOrBuilder() {
      return pos_ == null ? org.gof.demo.worldsrv.msg.Define.p_pos.getDefaultInstance() : pos_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (partId_ != 0) {
        output.writeUInt32(1, partId_);
      }
      if (unitId_ != 0L) {
        output.writeInt64(2, unitId_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(3, getPos());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (partId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, partId_);
      }
      if (unitId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, unitId_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getPos());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_c2s other = (org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_c2s) obj;

      if (getPartId()
          != other.getPartId()) return false;
      if (getUnitId()
          != other.getUnitId()) return false;
      if (hasPos() != other.hasPos()) return false;
      if (hasPos()) {
        if (!getPos()
            .equals(other.getPos())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + PART_ID_FIELD_NUMBER;
      hash = (53 * hash) + getPartId();
      hash = (37 * hash) + UNIT_ID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getUnitId());
      if (hasPos()) {
        hash = (37 * hash) + POS_FIELD_NUMBER;
        hash = (53 * hash) + getPos().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.main_chapter_kill_reward_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.main_chapter_kill_reward_c2s)
        org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_kill_reward_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_kill_reward_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_c2s.class, org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_c2s.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getPosFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        partId_ = 0;
        unitId_ = 0L;
        pos_ = null;
        if (posBuilder_ != null) {
          posBuilder_.dispose();
          posBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_kill_reward_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_c2s build() {
        org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_c2s result = new org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.partId_ = partId_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.unitId_ = unitId_;
        }
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.pos_ = posBuilder_ == null
              ? pos_
              : posBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_c2s.getDefaultInstance()) return this;
        if (other.getPartId() != 0) {
          setPartId(other.getPartId());
        }
        if (other.getUnitId() != 0L) {
          setUnitId(other.getUnitId());
        }
        if (other.hasPos()) {
          mergePos(other.getPos());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                partId_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                unitId_ = input.readInt64();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 26: {
                input.readMessage(
                    getPosFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000004;
                break;
              } // case 26
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int partId_ ;
      /**
       * <code>uint32 part_id = 1;</code>
       * @return The partId.
       */
      @java.lang.Override
      public int getPartId() {
        return partId_;
      }
      /**
       * <code>uint32 part_id = 1;</code>
       * @param value The partId to set.
       * @return This builder for chaining.
       */
      public Builder setPartId(int value) {

        partId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 part_id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPartId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        partId_ = 0;
        onChanged();
        return this;
      }

      private long unitId_ ;
      /**
       * <code>int64 unit_id = 2;</code>
       * @return The unitId.
       */
      @java.lang.Override
      public long getUnitId() {
        return unitId_;
      }
      /**
       * <code>int64 unit_id = 2;</code>
       * @param value The unitId to set.
       * @return This builder for chaining.
       */
      public Builder setUnitId(long value) {

        unitId_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>int64 unit_id = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearUnitId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        unitId_ = 0L;
        onChanged();
        return this;
      }

      private org.gof.demo.worldsrv.msg.Define.p_pos pos_;
      private com.google.protobuf.SingleFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_pos, org.gof.demo.worldsrv.msg.Define.p_pos.Builder, org.gof.demo.worldsrv.msg.Define.p_posOrBuilder> posBuilder_;
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_pos pos = 3;</code>
       * @return Whether the pos field is set.
       */
      public boolean hasPos() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_pos pos = 3;</code>
       * @return The pos.
       */
      public org.gof.demo.worldsrv.msg.Define.p_pos getPos() {
        if (posBuilder_ == null) {
          return pos_ == null ? org.gof.demo.worldsrv.msg.Define.p_pos.getDefaultInstance() : pos_;
        } else {
          return posBuilder_.getMessage();
        }
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_pos pos = 3;</code>
       */
      public Builder setPos(org.gof.demo.worldsrv.msg.Define.p_pos value) {
        if (posBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          pos_ = value;
        } else {
          posBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_pos pos = 3;</code>
       */
      public Builder setPos(
          org.gof.demo.worldsrv.msg.Define.p_pos.Builder builderForValue) {
        if (posBuilder_ == null) {
          pos_ = builderForValue.build();
        } else {
          posBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_pos pos = 3;</code>
       */
      public Builder mergePos(org.gof.demo.worldsrv.msg.Define.p_pos value) {
        if (posBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0) &&
            pos_ != null &&
            pos_ != org.gof.demo.worldsrv.msg.Define.p_pos.getDefaultInstance()) {
            getPosBuilder().mergeFrom(value);
          } else {
            pos_ = value;
          }
        } else {
          posBuilder_.mergeFrom(value);
        }
        if (pos_ != null) {
          bitField0_ |= 0x00000004;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_pos pos = 3;</code>
       */
      public Builder clearPos() {
        bitField0_ = (bitField0_ & ~0x00000004);
        pos_ = null;
        if (posBuilder_ != null) {
          posBuilder_.dispose();
          posBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_pos pos = 3;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_pos.Builder getPosBuilder() {
        bitField0_ |= 0x00000004;
        onChanged();
        return getPosFieldBuilder().getBuilder();
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_pos pos = 3;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_posOrBuilder getPosOrBuilder() {
        if (posBuilder_ != null) {
          return posBuilder_.getMessageOrBuilder();
        } else {
          return pos_ == null ?
              org.gof.demo.worldsrv.msg.Define.p_pos.getDefaultInstance() : pos_;
        }
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_pos pos = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_pos, org.gof.demo.worldsrv.msg.Define.p_pos.Builder, org.gof.demo.worldsrv.msg.Define.p_posOrBuilder> 
          getPosFieldBuilder() {
        if (posBuilder_ == null) {
          posBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_pos, org.gof.demo.worldsrv.msg.Define.p_pos.Builder, org.gof.demo.worldsrv.msg.Define.p_posOrBuilder>(
                  getPos(),
                  getParentForChildren(),
                  isClean());
          pos_ = null;
        }
        return posBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.main_chapter_kill_reward_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.main_chapter_kill_reward_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<main_chapter_kill_reward_c2s>
        PARSER = new com.google.protobuf.AbstractParser<main_chapter_kill_reward_c2s>() {
      @java.lang.Override
      public main_chapter_kill_reward_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<main_chapter_kill_reward_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<main_chapter_kill_reward_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface main_chapter_kill_reward_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.main_chapter_kill_reward_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 part_id = 1;</code>
     * @return The partId.
     */
    int getPartId();

    /**
     * <code>uint32 unit_id = 2;</code>
     * @return The unitId.
     */
    int getUnitId();

    /**
     * <code>.org.gof.demo.worldsrv.msg.p_pos pos = 3;</code>
     * @return Whether the pos field is set.
     */
    boolean hasPos();
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_pos pos = 3;</code>
     * @return The pos.
     */
    org.gof.demo.worldsrv.msg.Define.p_pos getPos();
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_pos pos = 3;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_posOrBuilder getPosOrBuilder();

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 4;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward> 
        getRewardListList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 4;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_reward getRewardList(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 4;</code>
     */
    int getRewardListCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 4;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder> 
        getRewardListOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 4;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder getRewardListOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.main_chapter_kill_reward_s2c}
   */
  public static final class main_chapter_kill_reward_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.main_chapter_kill_reward_s2c)
      main_chapter_kill_reward_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use main_chapter_kill_reward_s2c.newBuilder() to construct.
    private main_chapter_kill_reward_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private main_chapter_kill_reward_s2c() {
      rewardList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new main_chapter_kill_reward_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_kill_reward_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_kill_reward_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_s2c.class, org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_s2c.Builder.class);
    }

    private int bitField0_;
    public static final int PART_ID_FIELD_NUMBER = 1;
    private int partId_ = 0;
    /**
     * <code>uint32 part_id = 1;</code>
     * @return The partId.
     */
    @java.lang.Override
    public int getPartId() {
      return partId_;
    }

    public static final int UNIT_ID_FIELD_NUMBER = 2;
    private int unitId_ = 0;
    /**
     * <code>uint32 unit_id = 2;</code>
     * @return The unitId.
     */
    @java.lang.Override
    public int getUnitId() {
      return unitId_;
    }

    public static final int POS_FIELD_NUMBER = 3;
    private org.gof.demo.worldsrv.msg.Define.p_pos pos_;
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_pos pos = 3;</code>
     * @return Whether the pos field is set.
     */
    @java.lang.Override
    public boolean hasPos() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_pos pos = 3;</code>
     * @return The pos.
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_pos getPos() {
      return pos_ == null ? org.gof.demo.worldsrv.msg.Define.p_pos.getDefaultInstance() : pos_;
    }
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_pos pos = 3;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_posOrBuilder getPosOrBuilder() {
      return pos_ == null ? org.gof.demo.worldsrv.msg.Define.p_pos.getDefaultInstance() : pos_;
    }

    public static final int REWARD_LIST_FIELD_NUMBER = 4;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward> rewardList_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 4;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward> getRewardListList() {
      return rewardList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 4;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder> 
        getRewardListOrBuilderList() {
      return rewardList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 4;</code>
     */
    @java.lang.Override
    public int getRewardListCount() {
      return rewardList_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 4;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_reward getRewardList(int index) {
      return rewardList_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 4;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder getRewardListOrBuilder(
        int index) {
      return rewardList_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (partId_ != 0) {
        output.writeUInt32(1, partId_);
      }
      if (unitId_ != 0) {
        output.writeUInt32(2, unitId_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(3, getPos());
      }
      for (int i = 0; i < rewardList_.size(); i++) {
        output.writeMessage(4, rewardList_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (partId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, partId_);
      }
      if (unitId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, unitId_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getPos());
      }
      for (int i = 0; i < rewardList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, rewardList_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_s2c other = (org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_s2c) obj;

      if (getPartId()
          != other.getPartId()) return false;
      if (getUnitId()
          != other.getUnitId()) return false;
      if (hasPos() != other.hasPos()) return false;
      if (hasPos()) {
        if (!getPos()
            .equals(other.getPos())) return false;
      }
      if (!getRewardListList()
          .equals(other.getRewardListList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + PART_ID_FIELD_NUMBER;
      hash = (53 * hash) + getPartId();
      hash = (37 * hash) + UNIT_ID_FIELD_NUMBER;
      hash = (53 * hash) + getUnitId();
      if (hasPos()) {
        hash = (37 * hash) + POS_FIELD_NUMBER;
        hash = (53 * hash) + getPos().hashCode();
      }
      if (getRewardListCount() > 0) {
        hash = (37 * hash) + REWARD_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getRewardListList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.main_chapter_kill_reward_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.main_chapter_kill_reward_s2c)
        org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_kill_reward_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_kill_reward_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_s2c.class, org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_s2c.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getPosFieldBuilder();
          getRewardListFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        partId_ = 0;
        unitId_ = 0;
        pos_ = null;
        if (posBuilder_ != null) {
          posBuilder_.dispose();
          posBuilder_ = null;
        }
        if (rewardListBuilder_ == null) {
          rewardList_ = java.util.Collections.emptyList();
        } else {
          rewardList_ = null;
          rewardListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_kill_reward_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_s2c build() {
        org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_s2c result = new org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_s2c(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_s2c result) {
        if (rewardListBuilder_ == null) {
          if (((bitField0_ & 0x00000008) != 0)) {
            rewardList_ = java.util.Collections.unmodifiableList(rewardList_);
            bitField0_ = (bitField0_ & ~0x00000008);
          }
          result.rewardList_ = rewardList_;
        } else {
          result.rewardList_ = rewardListBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.partId_ = partId_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.unitId_ = unitId_;
        }
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.pos_ = posBuilder_ == null
              ? pos_
              : posBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_s2c.getDefaultInstance()) return this;
        if (other.getPartId() != 0) {
          setPartId(other.getPartId());
        }
        if (other.getUnitId() != 0) {
          setUnitId(other.getUnitId());
        }
        if (other.hasPos()) {
          mergePos(other.getPos());
        }
        if (rewardListBuilder_ == null) {
          if (!other.rewardList_.isEmpty()) {
            if (rewardList_.isEmpty()) {
              rewardList_ = other.rewardList_;
              bitField0_ = (bitField0_ & ~0x00000008);
            } else {
              ensureRewardListIsMutable();
              rewardList_.addAll(other.rewardList_);
            }
            onChanged();
          }
        } else {
          if (!other.rewardList_.isEmpty()) {
            if (rewardListBuilder_.isEmpty()) {
              rewardListBuilder_.dispose();
              rewardListBuilder_ = null;
              rewardList_ = other.rewardList_;
              bitField0_ = (bitField0_ & ~0x00000008);
              rewardListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getRewardListFieldBuilder() : null;
            } else {
              rewardListBuilder_.addAllMessages(other.rewardList_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                partId_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                unitId_ = input.readUInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 26: {
                input.readMessage(
                    getPosFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000004;
                break;
              } // case 26
              case 34: {
                org.gof.demo.worldsrv.msg.Define.p_reward m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_reward.parser(),
                        extensionRegistry);
                if (rewardListBuilder_ == null) {
                  ensureRewardListIsMutable();
                  rewardList_.add(m);
                } else {
                  rewardListBuilder_.addMessage(m);
                }
                break;
              } // case 34
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int partId_ ;
      /**
       * <code>uint32 part_id = 1;</code>
       * @return The partId.
       */
      @java.lang.Override
      public int getPartId() {
        return partId_;
      }
      /**
       * <code>uint32 part_id = 1;</code>
       * @param value The partId to set.
       * @return This builder for chaining.
       */
      public Builder setPartId(int value) {

        partId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 part_id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPartId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        partId_ = 0;
        onChanged();
        return this;
      }

      private int unitId_ ;
      /**
       * <code>uint32 unit_id = 2;</code>
       * @return The unitId.
       */
      @java.lang.Override
      public int getUnitId() {
        return unitId_;
      }
      /**
       * <code>uint32 unit_id = 2;</code>
       * @param value The unitId to set.
       * @return This builder for chaining.
       */
      public Builder setUnitId(int value) {

        unitId_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 unit_id = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearUnitId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        unitId_ = 0;
        onChanged();
        return this;
      }

      private org.gof.demo.worldsrv.msg.Define.p_pos pos_;
      private com.google.protobuf.SingleFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_pos, org.gof.demo.worldsrv.msg.Define.p_pos.Builder, org.gof.demo.worldsrv.msg.Define.p_posOrBuilder> posBuilder_;
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_pos pos = 3;</code>
       * @return Whether the pos field is set.
       */
      public boolean hasPos() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_pos pos = 3;</code>
       * @return The pos.
       */
      public org.gof.demo.worldsrv.msg.Define.p_pos getPos() {
        if (posBuilder_ == null) {
          return pos_ == null ? org.gof.demo.worldsrv.msg.Define.p_pos.getDefaultInstance() : pos_;
        } else {
          return posBuilder_.getMessage();
        }
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_pos pos = 3;</code>
       */
      public Builder setPos(org.gof.demo.worldsrv.msg.Define.p_pos value) {
        if (posBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          pos_ = value;
        } else {
          posBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_pos pos = 3;</code>
       */
      public Builder setPos(
          org.gof.demo.worldsrv.msg.Define.p_pos.Builder builderForValue) {
        if (posBuilder_ == null) {
          pos_ = builderForValue.build();
        } else {
          posBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_pos pos = 3;</code>
       */
      public Builder mergePos(org.gof.demo.worldsrv.msg.Define.p_pos value) {
        if (posBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0) &&
            pos_ != null &&
            pos_ != org.gof.demo.worldsrv.msg.Define.p_pos.getDefaultInstance()) {
            getPosBuilder().mergeFrom(value);
          } else {
            pos_ = value;
          }
        } else {
          posBuilder_.mergeFrom(value);
        }
        if (pos_ != null) {
          bitField0_ |= 0x00000004;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_pos pos = 3;</code>
       */
      public Builder clearPos() {
        bitField0_ = (bitField0_ & ~0x00000004);
        pos_ = null;
        if (posBuilder_ != null) {
          posBuilder_.dispose();
          posBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_pos pos = 3;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_pos.Builder getPosBuilder() {
        bitField0_ |= 0x00000004;
        onChanged();
        return getPosFieldBuilder().getBuilder();
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_pos pos = 3;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_posOrBuilder getPosOrBuilder() {
        if (posBuilder_ != null) {
          return posBuilder_.getMessageOrBuilder();
        } else {
          return pos_ == null ?
              org.gof.demo.worldsrv.msg.Define.p_pos.getDefaultInstance() : pos_;
        }
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_pos pos = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_pos, org.gof.demo.worldsrv.msg.Define.p_pos.Builder, org.gof.demo.worldsrv.msg.Define.p_posOrBuilder> 
          getPosFieldBuilder() {
        if (posBuilder_ == null) {
          posBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_pos, org.gof.demo.worldsrv.msg.Define.p_pos.Builder, org.gof.demo.worldsrv.msg.Define.p_posOrBuilder>(
                  getPos(),
                  getParentForChildren(),
                  isClean());
          pos_ = null;
        }
        return posBuilder_;
      }

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward> rewardList_ =
        java.util.Collections.emptyList();
      private void ensureRewardListIsMutable() {
        if (!((bitField0_ & 0x00000008) != 0)) {
          rewardList_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_reward>(rewardList_);
          bitField0_ |= 0x00000008;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_reward, org.gof.demo.worldsrv.msg.Define.p_reward.Builder, org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder> rewardListBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 4;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward> getRewardListList() {
        if (rewardListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(rewardList_);
        } else {
          return rewardListBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 4;</code>
       */
      public int getRewardListCount() {
        if (rewardListBuilder_ == null) {
          return rewardList_.size();
        } else {
          return rewardListBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 4;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_reward getRewardList(int index) {
        if (rewardListBuilder_ == null) {
          return rewardList_.get(index);
        } else {
          return rewardListBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 4;</code>
       */
      public Builder setRewardList(
          int index, org.gof.demo.worldsrv.msg.Define.p_reward value) {
        if (rewardListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRewardListIsMutable();
          rewardList_.set(index, value);
          onChanged();
        } else {
          rewardListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 4;</code>
       */
      public Builder setRewardList(
          int index, org.gof.demo.worldsrv.msg.Define.p_reward.Builder builderForValue) {
        if (rewardListBuilder_ == null) {
          ensureRewardListIsMutable();
          rewardList_.set(index, builderForValue.build());
          onChanged();
        } else {
          rewardListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 4;</code>
       */
      public Builder addRewardList(org.gof.demo.worldsrv.msg.Define.p_reward value) {
        if (rewardListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRewardListIsMutable();
          rewardList_.add(value);
          onChanged();
        } else {
          rewardListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 4;</code>
       */
      public Builder addRewardList(
          int index, org.gof.demo.worldsrv.msg.Define.p_reward value) {
        if (rewardListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRewardListIsMutable();
          rewardList_.add(index, value);
          onChanged();
        } else {
          rewardListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 4;</code>
       */
      public Builder addRewardList(
          org.gof.demo.worldsrv.msg.Define.p_reward.Builder builderForValue) {
        if (rewardListBuilder_ == null) {
          ensureRewardListIsMutable();
          rewardList_.add(builderForValue.build());
          onChanged();
        } else {
          rewardListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 4;</code>
       */
      public Builder addRewardList(
          int index, org.gof.demo.worldsrv.msg.Define.p_reward.Builder builderForValue) {
        if (rewardListBuilder_ == null) {
          ensureRewardListIsMutable();
          rewardList_.add(index, builderForValue.build());
          onChanged();
        } else {
          rewardListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 4;</code>
       */
      public Builder addAllRewardList(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_reward> values) {
        if (rewardListBuilder_ == null) {
          ensureRewardListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, rewardList_);
          onChanged();
        } else {
          rewardListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 4;</code>
       */
      public Builder clearRewardList() {
        if (rewardListBuilder_ == null) {
          rewardList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000008);
          onChanged();
        } else {
          rewardListBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 4;</code>
       */
      public Builder removeRewardList(int index) {
        if (rewardListBuilder_ == null) {
          ensureRewardListIsMutable();
          rewardList_.remove(index);
          onChanged();
        } else {
          rewardListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 4;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_reward.Builder getRewardListBuilder(
          int index) {
        return getRewardListFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 4;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder getRewardListOrBuilder(
          int index) {
        if (rewardListBuilder_ == null) {
          return rewardList_.get(index);  } else {
          return rewardListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 4;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder> 
           getRewardListOrBuilderList() {
        if (rewardListBuilder_ != null) {
          return rewardListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(rewardList_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 4;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_reward.Builder addRewardListBuilder() {
        return getRewardListFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_reward.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 4;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_reward.Builder addRewardListBuilder(
          int index) {
        return getRewardListFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_reward.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 4;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward.Builder> 
           getRewardListBuilderList() {
        return getRewardListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_reward, org.gof.demo.worldsrv.msg.Define.p_reward.Builder, org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder> 
          getRewardListFieldBuilder() {
        if (rewardListBuilder_ == null) {
          rewardListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_reward, org.gof.demo.worldsrv.msg.Define.p_reward.Builder, org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder>(
                  rewardList_,
                  ((bitField0_ & 0x00000008) != 0),
                  getParentForChildren(),
                  isClean());
          rewardList_ = null;
        }
        return rewardListBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.main_chapter_kill_reward_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.main_chapter_kill_reward_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<main_chapter_kill_reward_s2c>
        PARSER = new com.google.protobuf.AbstractParser<main_chapter_kill_reward_s2c>() {
      @java.lang.Override
      public main_chapter_kill_reward_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<main_chapter_kill_reward_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<main_chapter_kill_reward_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_kill_reward_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface main_chapter_reward_info_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.main_chapter_reward_info_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 type = 1;</code>
     * @return The type.
     */
    int getType();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.main_chapter_reward_info_c2s}
   */
  public static final class main_chapter_reward_info_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.main_chapter_reward_info_c2s)
      main_chapter_reward_info_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use main_chapter_reward_info_c2s.newBuilder() to construct.
    private main_chapter_reward_info_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private main_chapter_reward_info_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new main_chapter_reward_info_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_reward_info_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_reward_info_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_c2s.class, org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_c2s.Builder.class);
    }

    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_ = 0;
    /**
     * <code>uint32 type = 1;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (type_ != 0) {
        output.writeUInt32(1, type_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, type_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_c2s other = (org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_c2s) obj;

      if (getType()
          != other.getType()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.main_chapter_reward_info_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.main_chapter_reward_info_c2s)
        org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_reward_info_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_reward_info_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_c2s.class, org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        type_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_reward_info_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_c2s build() {
        org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_c2s result = new org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.type_ = type_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_c2s.getDefaultInstance()) return this;
        if (other.getType() != 0) {
          setType(other.getType());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                type_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int type_ ;
      /**
       * <code>uint32 type = 1;</code>
       * @return The type.
       */
      @java.lang.Override
      public int getType() {
        return type_;
      }
      /**
       * <code>uint32 type = 1;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(int value) {

        type_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 type = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        type_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.main_chapter_reward_info_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.main_chapter_reward_info_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<main_chapter_reward_info_c2s>
        PARSER = new com.google.protobuf.AbstractParser<main_chapter_reward_info_c2s>() {
      @java.lang.Override
      public main_chapter_reward_info_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<main_chapter_reward_info_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<main_chapter_reward_info_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface main_chapter_reward_info_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.main_chapter_reward_info_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 type = 1;</code>
     * @return The type.
     */
    int getType();

    /**
     * <code>uint32 time = 2;</code>
     * @return The time.
     */
    int getTime();

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward res_list = 3;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward> 
        getResListList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward res_list = 3;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_reward getResList(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward res_list = 3;</code>
     */
    int getResListCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward res_list = 3;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder> 
        getResListOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward res_list = 3;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder getResListOrBuilder(
        int index);

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward item_list = 4;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward> 
        getItemListList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward item_list = 4;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_reward getItemList(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward item_list = 4;</code>
     */
    int getItemListCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward item_list = 4;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder> 
        getItemListOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward item_list = 4;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder getItemListOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.main_chapter_reward_info_s2c}
   */
  public static final class main_chapter_reward_info_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.main_chapter_reward_info_s2c)
      main_chapter_reward_info_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use main_chapter_reward_info_s2c.newBuilder() to construct.
    private main_chapter_reward_info_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private main_chapter_reward_info_s2c() {
      resList_ = java.util.Collections.emptyList();
      itemList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new main_chapter_reward_info_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_reward_info_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_reward_info_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_s2c.class, org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_s2c.Builder.class);
    }

    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_ = 0;
    /**
     * <code>uint32 type = 1;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }

    public static final int TIME_FIELD_NUMBER = 2;
    private int time_ = 0;
    /**
     * <code>uint32 time = 2;</code>
     * @return The time.
     */
    @java.lang.Override
    public int getTime() {
      return time_;
    }

    public static final int RES_LIST_FIELD_NUMBER = 3;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward> resList_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward res_list = 3;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward> getResListList() {
      return resList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward res_list = 3;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder> 
        getResListOrBuilderList() {
      return resList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward res_list = 3;</code>
     */
    @java.lang.Override
    public int getResListCount() {
      return resList_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward res_list = 3;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_reward getResList(int index) {
      return resList_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward res_list = 3;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder getResListOrBuilder(
        int index) {
      return resList_.get(index);
    }

    public static final int ITEM_LIST_FIELD_NUMBER = 4;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward> itemList_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward item_list = 4;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward> getItemListList() {
      return itemList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward item_list = 4;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder> 
        getItemListOrBuilderList() {
      return itemList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward item_list = 4;</code>
     */
    @java.lang.Override
    public int getItemListCount() {
      return itemList_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward item_list = 4;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_reward getItemList(int index) {
      return itemList_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward item_list = 4;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder getItemListOrBuilder(
        int index) {
      return itemList_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (type_ != 0) {
        output.writeUInt32(1, type_);
      }
      if (time_ != 0) {
        output.writeUInt32(2, time_);
      }
      for (int i = 0; i < resList_.size(); i++) {
        output.writeMessage(3, resList_.get(i));
      }
      for (int i = 0; i < itemList_.size(); i++) {
        output.writeMessage(4, itemList_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, type_);
      }
      if (time_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, time_);
      }
      for (int i = 0; i < resList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, resList_.get(i));
      }
      for (int i = 0; i < itemList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, itemList_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_s2c other = (org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_s2c) obj;

      if (getType()
          != other.getType()) return false;
      if (getTime()
          != other.getTime()) return false;
      if (!getResListList()
          .equals(other.getResListList())) return false;
      if (!getItemListList()
          .equals(other.getItemListList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      hash = (37 * hash) + TIME_FIELD_NUMBER;
      hash = (53 * hash) + getTime();
      if (getResListCount() > 0) {
        hash = (37 * hash) + RES_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getResListList().hashCode();
      }
      if (getItemListCount() > 0) {
        hash = (37 * hash) + ITEM_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getItemListList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.main_chapter_reward_info_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.main_chapter_reward_info_s2c)
        org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_reward_info_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_reward_info_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_s2c.class, org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        type_ = 0;
        time_ = 0;
        if (resListBuilder_ == null) {
          resList_ = java.util.Collections.emptyList();
        } else {
          resList_ = null;
          resListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        if (itemListBuilder_ == null) {
          itemList_ = java.util.Collections.emptyList();
        } else {
          itemList_ = null;
          itemListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_reward_info_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_s2c build() {
        org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_s2c result = new org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_s2c(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_s2c result) {
        if (resListBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0)) {
            resList_ = java.util.Collections.unmodifiableList(resList_);
            bitField0_ = (bitField0_ & ~0x00000004);
          }
          result.resList_ = resList_;
        } else {
          result.resList_ = resListBuilder_.build();
        }
        if (itemListBuilder_ == null) {
          if (((bitField0_ & 0x00000008) != 0)) {
            itemList_ = java.util.Collections.unmodifiableList(itemList_);
            bitField0_ = (bitField0_ & ~0x00000008);
          }
          result.itemList_ = itemList_;
        } else {
          result.itemList_ = itemListBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.type_ = type_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.time_ = time_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_s2c.getDefaultInstance()) return this;
        if (other.getType() != 0) {
          setType(other.getType());
        }
        if (other.getTime() != 0) {
          setTime(other.getTime());
        }
        if (resListBuilder_ == null) {
          if (!other.resList_.isEmpty()) {
            if (resList_.isEmpty()) {
              resList_ = other.resList_;
              bitField0_ = (bitField0_ & ~0x00000004);
            } else {
              ensureResListIsMutable();
              resList_.addAll(other.resList_);
            }
            onChanged();
          }
        } else {
          if (!other.resList_.isEmpty()) {
            if (resListBuilder_.isEmpty()) {
              resListBuilder_.dispose();
              resListBuilder_ = null;
              resList_ = other.resList_;
              bitField0_ = (bitField0_ & ~0x00000004);
              resListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getResListFieldBuilder() : null;
            } else {
              resListBuilder_.addAllMessages(other.resList_);
            }
          }
        }
        if (itemListBuilder_ == null) {
          if (!other.itemList_.isEmpty()) {
            if (itemList_.isEmpty()) {
              itemList_ = other.itemList_;
              bitField0_ = (bitField0_ & ~0x00000008);
            } else {
              ensureItemListIsMutable();
              itemList_.addAll(other.itemList_);
            }
            onChanged();
          }
        } else {
          if (!other.itemList_.isEmpty()) {
            if (itemListBuilder_.isEmpty()) {
              itemListBuilder_.dispose();
              itemListBuilder_ = null;
              itemList_ = other.itemList_;
              bitField0_ = (bitField0_ & ~0x00000008);
              itemListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getItemListFieldBuilder() : null;
            } else {
              itemListBuilder_.addAllMessages(other.itemList_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                type_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                time_ = input.readUInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 26: {
                org.gof.demo.worldsrv.msg.Define.p_reward m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_reward.parser(),
                        extensionRegistry);
                if (resListBuilder_ == null) {
                  ensureResListIsMutable();
                  resList_.add(m);
                } else {
                  resListBuilder_.addMessage(m);
                }
                break;
              } // case 26
              case 34: {
                org.gof.demo.worldsrv.msg.Define.p_reward m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_reward.parser(),
                        extensionRegistry);
                if (itemListBuilder_ == null) {
                  ensureItemListIsMutable();
                  itemList_.add(m);
                } else {
                  itemListBuilder_.addMessage(m);
                }
                break;
              } // case 34
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int type_ ;
      /**
       * <code>uint32 type = 1;</code>
       * @return The type.
       */
      @java.lang.Override
      public int getType() {
        return type_;
      }
      /**
       * <code>uint32 type = 1;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(int value) {

        type_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 type = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        type_ = 0;
        onChanged();
        return this;
      }

      private int time_ ;
      /**
       * <code>uint32 time = 2;</code>
       * @return The time.
       */
      @java.lang.Override
      public int getTime() {
        return time_;
      }
      /**
       * <code>uint32 time = 2;</code>
       * @param value The time to set.
       * @return This builder for chaining.
       */
      public Builder setTime(int value) {

        time_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 time = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearTime() {
        bitField0_ = (bitField0_ & ~0x00000002);
        time_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward> resList_ =
        java.util.Collections.emptyList();
      private void ensureResListIsMutable() {
        if (!((bitField0_ & 0x00000004) != 0)) {
          resList_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_reward>(resList_);
          bitField0_ |= 0x00000004;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_reward, org.gof.demo.worldsrv.msg.Define.p_reward.Builder, org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder> resListBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward res_list = 3;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward> getResListList() {
        if (resListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(resList_);
        } else {
          return resListBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward res_list = 3;</code>
       */
      public int getResListCount() {
        if (resListBuilder_ == null) {
          return resList_.size();
        } else {
          return resListBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward res_list = 3;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_reward getResList(int index) {
        if (resListBuilder_ == null) {
          return resList_.get(index);
        } else {
          return resListBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward res_list = 3;</code>
       */
      public Builder setResList(
          int index, org.gof.demo.worldsrv.msg.Define.p_reward value) {
        if (resListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureResListIsMutable();
          resList_.set(index, value);
          onChanged();
        } else {
          resListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward res_list = 3;</code>
       */
      public Builder setResList(
          int index, org.gof.demo.worldsrv.msg.Define.p_reward.Builder builderForValue) {
        if (resListBuilder_ == null) {
          ensureResListIsMutable();
          resList_.set(index, builderForValue.build());
          onChanged();
        } else {
          resListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward res_list = 3;</code>
       */
      public Builder addResList(org.gof.demo.worldsrv.msg.Define.p_reward value) {
        if (resListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureResListIsMutable();
          resList_.add(value);
          onChanged();
        } else {
          resListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward res_list = 3;</code>
       */
      public Builder addResList(
          int index, org.gof.demo.worldsrv.msg.Define.p_reward value) {
        if (resListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureResListIsMutable();
          resList_.add(index, value);
          onChanged();
        } else {
          resListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward res_list = 3;</code>
       */
      public Builder addResList(
          org.gof.demo.worldsrv.msg.Define.p_reward.Builder builderForValue) {
        if (resListBuilder_ == null) {
          ensureResListIsMutable();
          resList_.add(builderForValue.build());
          onChanged();
        } else {
          resListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward res_list = 3;</code>
       */
      public Builder addResList(
          int index, org.gof.demo.worldsrv.msg.Define.p_reward.Builder builderForValue) {
        if (resListBuilder_ == null) {
          ensureResListIsMutable();
          resList_.add(index, builderForValue.build());
          onChanged();
        } else {
          resListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward res_list = 3;</code>
       */
      public Builder addAllResList(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_reward> values) {
        if (resListBuilder_ == null) {
          ensureResListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, resList_);
          onChanged();
        } else {
          resListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward res_list = 3;</code>
       */
      public Builder clearResList() {
        if (resListBuilder_ == null) {
          resList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);
          onChanged();
        } else {
          resListBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward res_list = 3;</code>
       */
      public Builder removeResList(int index) {
        if (resListBuilder_ == null) {
          ensureResListIsMutable();
          resList_.remove(index);
          onChanged();
        } else {
          resListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward res_list = 3;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_reward.Builder getResListBuilder(
          int index) {
        return getResListFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward res_list = 3;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder getResListOrBuilder(
          int index) {
        if (resListBuilder_ == null) {
          return resList_.get(index);  } else {
          return resListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward res_list = 3;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder> 
           getResListOrBuilderList() {
        if (resListBuilder_ != null) {
          return resListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(resList_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward res_list = 3;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_reward.Builder addResListBuilder() {
        return getResListFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_reward.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward res_list = 3;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_reward.Builder addResListBuilder(
          int index) {
        return getResListFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_reward.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward res_list = 3;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward.Builder> 
           getResListBuilderList() {
        return getResListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_reward, org.gof.demo.worldsrv.msg.Define.p_reward.Builder, org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder> 
          getResListFieldBuilder() {
        if (resListBuilder_ == null) {
          resListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_reward, org.gof.demo.worldsrv.msg.Define.p_reward.Builder, org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder>(
                  resList_,
                  ((bitField0_ & 0x00000004) != 0),
                  getParentForChildren(),
                  isClean());
          resList_ = null;
        }
        return resListBuilder_;
      }

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward> itemList_ =
        java.util.Collections.emptyList();
      private void ensureItemListIsMutable() {
        if (!((bitField0_ & 0x00000008) != 0)) {
          itemList_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_reward>(itemList_);
          bitField0_ |= 0x00000008;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_reward, org.gof.demo.worldsrv.msg.Define.p_reward.Builder, org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder> itemListBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward item_list = 4;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward> getItemListList() {
        if (itemListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(itemList_);
        } else {
          return itemListBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward item_list = 4;</code>
       */
      public int getItemListCount() {
        if (itemListBuilder_ == null) {
          return itemList_.size();
        } else {
          return itemListBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward item_list = 4;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_reward getItemList(int index) {
        if (itemListBuilder_ == null) {
          return itemList_.get(index);
        } else {
          return itemListBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward item_list = 4;</code>
       */
      public Builder setItemList(
          int index, org.gof.demo.worldsrv.msg.Define.p_reward value) {
        if (itemListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureItemListIsMutable();
          itemList_.set(index, value);
          onChanged();
        } else {
          itemListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward item_list = 4;</code>
       */
      public Builder setItemList(
          int index, org.gof.demo.worldsrv.msg.Define.p_reward.Builder builderForValue) {
        if (itemListBuilder_ == null) {
          ensureItemListIsMutable();
          itemList_.set(index, builderForValue.build());
          onChanged();
        } else {
          itemListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward item_list = 4;</code>
       */
      public Builder addItemList(org.gof.demo.worldsrv.msg.Define.p_reward value) {
        if (itemListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureItemListIsMutable();
          itemList_.add(value);
          onChanged();
        } else {
          itemListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward item_list = 4;</code>
       */
      public Builder addItemList(
          int index, org.gof.demo.worldsrv.msg.Define.p_reward value) {
        if (itemListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureItemListIsMutable();
          itemList_.add(index, value);
          onChanged();
        } else {
          itemListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward item_list = 4;</code>
       */
      public Builder addItemList(
          org.gof.demo.worldsrv.msg.Define.p_reward.Builder builderForValue) {
        if (itemListBuilder_ == null) {
          ensureItemListIsMutable();
          itemList_.add(builderForValue.build());
          onChanged();
        } else {
          itemListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward item_list = 4;</code>
       */
      public Builder addItemList(
          int index, org.gof.demo.worldsrv.msg.Define.p_reward.Builder builderForValue) {
        if (itemListBuilder_ == null) {
          ensureItemListIsMutable();
          itemList_.add(index, builderForValue.build());
          onChanged();
        } else {
          itemListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward item_list = 4;</code>
       */
      public Builder addAllItemList(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_reward> values) {
        if (itemListBuilder_ == null) {
          ensureItemListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, itemList_);
          onChanged();
        } else {
          itemListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward item_list = 4;</code>
       */
      public Builder clearItemList() {
        if (itemListBuilder_ == null) {
          itemList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000008);
          onChanged();
        } else {
          itemListBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward item_list = 4;</code>
       */
      public Builder removeItemList(int index) {
        if (itemListBuilder_ == null) {
          ensureItemListIsMutable();
          itemList_.remove(index);
          onChanged();
        } else {
          itemListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward item_list = 4;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_reward.Builder getItemListBuilder(
          int index) {
        return getItemListFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward item_list = 4;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder getItemListOrBuilder(
          int index) {
        if (itemListBuilder_ == null) {
          return itemList_.get(index);  } else {
          return itemListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward item_list = 4;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder> 
           getItemListOrBuilderList() {
        if (itemListBuilder_ != null) {
          return itemListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(itemList_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward item_list = 4;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_reward.Builder addItemListBuilder() {
        return getItemListFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_reward.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward item_list = 4;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_reward.Builder addItemListBuilder(
          int index) {
        return getItemListFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_reward.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward item_list = 4;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward.Builder> 
           getItemListBuilderList() {
        return getItemListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_reward, org.gof.demo.worldsrv.msg.Define.p_reward.Builder, org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder> 
          getItemListFieldBuilder() {
        if (itemListBuilder_ == null) {
          itemListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_reward, org.gof.demo.worldsrv.msg.Define.p_reward.Builder, org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder>(
                  itemList_,
                  ((bitField0_ & 0x00000008) != 0),
                  getParentForChildren(),
                  isClean());
          itemList_ = null;
        }
        return itemListBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.main_chapter_reward_info_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.main_chapter_reward_info_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<main_chapter_reward_info_s2c>
        PARSER = new com.google.protobuf.AbstractParser<main_chapter_reward_info_s2c>() {
      @java.lang.Override
      public main_chapter_reward_info_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<main_chapter_reward_info_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<main_chapter_reward_info_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_reward_info_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface main_chapter_claim_reward_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.main_chapter_claim_reward_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 type = 1;</code>
     * @return The type.
     */
    int getType();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.main_chapter_claim_reward_c2s}
   */
  public static final class main_chapter_claim_reward_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.main_chapter_claim_reward_c2s)
      main_chapter_claim_reward_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use main_chapter_claim_reward_c2s.newBuilder() to construct.
    private main_chapter_claim_reward_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private main_chapter_claim_reward_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new main_chapter_claim_reward_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_claim_reward_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_claim_reward_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_c2s.class, org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_c2s.Builder.class);
    }

    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_ = 0;
    /**
     * <code>uint32 type = 1;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (type_ != 0) {
        output.writeUInt32(1, type_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, type_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_c2s other = (org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_c2s) obj;

      if (getType()
          != other.getType()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.main_chapter_claim_reward_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.main_chapter_claim_reward_c2s)
        org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_claim_reward_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_claim_reward_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_c2s.class, org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        type_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_claim_reward_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_c2s build() {
        org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_c2s result = new org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.type_ = type_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_c2s.getDefaultInstance()) return this;
        if (other.getType() != 0) {
          setType(other.getType());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                type_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int type_ ;
      /**
       * <code>uint32 type = 1;</code>
       * @return The type.
       */
      @java.lang.Override
      public int getType() {
        return type_;
      }
      /**
       * <code>uint32 type = 1;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(int value) {

        type_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 type = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        type_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.main_chapter_claim_reward_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.main_chapter_claim_reward_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<main_chapter_claim_reward_c2s>
        PARSER = new com.google.protobuf.AbstractParser<main_chapter_claim_reward_c2s>() {
      @java.lang.Override
      public main_chapter_claim_reward_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<main_chapter_claim_reward_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<main_chapter_claim_reward_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface main_chapter_claim_reward_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.main_chapter_claim_reward_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 type = 1;</code>
     * @return The type.
     */
    int getType();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.main_chapter_claim_reward_s2c}
   */
  public static final class main_chapter_claim_reward_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.main_chapter_claim_reward_s2c)
      main_chapter_claim_reward_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use main_chapter_claim_reward_s2c.newBuilder() to construct.
    private main_chapter_claim_reward_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private main_chapter_claim_reward_s2c() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new main_chapter_claim_reward_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_claim_reward_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_claim_reward_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_s2c.class, org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_s2c.Builder.class);
    }

    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_ = 0;
    /**
     * <code>uint32 type = 1;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (type_ != 0) {
        output.writeUInt32(1, type_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, type_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_s2c other = (org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_s2c) obj;

      if (getType()
          != other.getType()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.main_chapter_claim_reward_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.main_chapter_claim_reward_s2c)
        org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_claim_reward_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_claim_reward_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_s2c.class, org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        type_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgMainChapter.internal_static_org_gof_demo_worldsrv_msg_main_chapter_claim_reward_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_s2c build() {
        org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_s2c result = new org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.type_ = type_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_s2c.getDefaultInstance()) return this;
        if (other.getType() != 0) {
          setType(other.getType());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                type_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int type_ ;
      /**
       * <code>uint32 type = 1;</code>
       * @return The type.
       */
      @java.lang.Override
      public int getType() {
        return type_;
      }
      /**
       * <code>uint32 type = 1;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(int value) {

        type_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 type = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        type_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.main_chapter_claim_reward_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.main_chapter_claim_reward_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<main_chapter_claim_reward_s2c>
        PARSER = new com.google.protobuf.AbstractParser<main_chapter_claim_reward_s2c>() {
      @java.lang.Override
      public main_chapter_claim_reward_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<main_chapter_claim_reward_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<main_chapter_claim_reward_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgMainChapter.main_chapter_claim_reward_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_main_chapter_info_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_main_chapter_info_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_main_chapter_info_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_main_chapter_info_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_main_chapter_enter_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_main_chapter_enter_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_main_chapter_enter_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_main_chapter_enter_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_main_chapter_result_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_main_chapter_result_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_main_chapter_result_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_main_chapter_result_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_main_chapter_kill_reward_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_main_chapter_kill_reward_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_main_chapter_kill_reward_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_main_chapter_kill_reward_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_main_chapter_reward_info_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_main_chapter_reward_info_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_main_chapter_reward_info_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_main_chapter_reward_info_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_main_chapter_claim_reward_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_main_chapter_claim_reward_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_main_chapter_claim_reward_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_main_chapter_claim_reward_s2c_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\025msg.mainChapter.proto\022\031org.gof.demo.wo" +
      "rldsrv.msg\032\roptions.proto\032\014define.proto\"" +
      "\036\n\025main_chapter_info_c2s:\005\210\303\032\201\032\"E\n\025main_" +
      "chapter_info_s2c\022\017\n\007part_id\030\001 \001(\r\022\024\n\014is_" +
      "unlimited\030\002 \001(\r:\005\210\303\032\201\032\"0\n\026main_chapter_e" +
      "nter_c2s\022\017\n\007part_id\030\001 \001(\r:\005\210\303\032\202\032\"\255\001\n\026mai" +
      "n_chapter_enter_s2c\022\017\n\007part_id\030\001 \001(\r\022\024\n\014" +
      "is_unlimited\030\002 \001(\r\022\027\n\017battle_checkout\030\003 " +
      "\001(\r\022\023\n\013random_seed\030\004 \001(\004\0227\n\005roles\030\005 \003(\0132" +
      "(.org.gof.demo.worldsrv.msg.p_battle_rol" +
      "e:\005\210\303\032\202\032\"\234\001\n\027main_chapter_result_c2s\022\017\n\007" +
      "part_id\030\001 \001(\r\022\016\n\006result\030\002 \001(\r\022\030\n\020manual_" +
      "operators\030\003 \001(\r\022?\n\toperators\030\004 \003(\0132,.org" +
      ".gof.demo.worldsrv.msg.p_battle_operator" +
      ":\005\210\303\032\203\032\"U\n\027main_chapter_result_s2c\022\014\n\004co" +
      "de\030\001 \001(\005\022\017\n\007part_id\030\002 \001(\r\022\024\n\014is_unlimite" +
      "d\030\003 \001(\r:\005\210\303\032\203\032\"v\n\034main_chapter_kill_rewa" +
      "rd_c2s\022\017\n\007part_id\030\001 \001(\r\022\017\n\007unit_id\030\002 \001(\003" +
      "\022-\n\003pos\030\003 \001(\0132 .org.gof.demo.worldsrv.ms" +
      "g.p_pos:\005\210\303\032\204\032\"\260\001\n\034main_chapter_kill_rew" +
      "ard_s2c\022\017\n\007part_id\030\001 \001(\r\022\017\n\007unit_id\030\002 \001(" +
      "\r\022-\n\003pos\030\003 \001(\0132 .org.gof.demo.worldsrv.m" +
      "sg.p_pos\0228\n\013reward_list\030\004 \003(\0132#.org.gof." +
      "demo.worldsrv.msg.p_reward:\005\210\303\032\204\032\"3\n\034mai" +
      "n_chapter_reward_info_c2s\022\014\n\004type\030\001 \001(\r:" +
      "\005\210\303\032\205\032\"\260\001\n\034main_chapter_reward_info_s2c\022" +
      "\014\n\004type\030\001 \001(\r\022\014\n\004time\030\002 \001(\r\0225\n\010res_list\030" +
      "\003 \003(\0132#.org.gof.demo.worldsrv.msg.p_rewa" +
      "rd\0226\n\titem_list\030\004 \003(\0132#.org.gof.demo.wor" +
      "ldsrv.msg.p_reward:\005\210\303\032\205\032\"4\n\035main_chapte" +
      "r_claim_reward_c2s\022\014\n\004type\030\001 \001(\r:\005\210\303\032\206\032\"" +
      "4\n\035main_chapter_claim_reward_s2c\022\014\n\004type" +
      "\030\001 \001(\r:\005\210\303\032\206\032b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          org.gof.demo.worldsrv.msg.Options.getDescriptor(),
          org.gof.demo.worldsrv.msg.Define.getDescriptor(),
        });
    internal_static_org_gof_demo_worldsrv_msg_main_chapter_info_c2s_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_org_gof_demo_worldsrv_msg_main_chapter_info_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_main_chapter_info_c2s_descriptor,
        new java.lang.String[] { });
    internal_static_org_gof_demo_worldsrv_msg_main_chapter_info_s2c_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_org_gof_demo_worldsrv_msg_main_chapter_info_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_main_chapter_info_s2c_descriptor,
        new java.lang.String[] { "PartId", "IsUnlimited", });
    internal_static_org_gof_demo_worldsrv_msg_main_chapter_enter_c2s_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_org_gof_demo_worldsrv_msg_main_chapter_enter_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_main_chapter_enter_c2s_descriptor,
        new java.lang.String[] { "PartId", });
    internal_static_org_gof_demo_worldsrv_msg_main_chapter_enter_s2c_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_org_gof_demo_worldsrv_msg_main_chapter_enter_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_main_chapter_enter_s2c_descriptor,
        new java.lang.String[] { "PartId", "IsUnlimited", "BattleCheckout", "RandomSeed", "Roles", });
    internal_static_org_gof_demo_worldsrv_msg_main_chapter_result_c2s_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_org_gof_demo_worldsrv_msg_main_chapter_result_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_main_chapter_result_c2s_descriptor,
        new java.lang.String[] { "PartId", "Result", "ManualOperators", "Operators", });
    internal_static_org_gof_demo_worldsrv_msg_main_chapter_result_s2c_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_org_gof_demo_worldsrv_msg_main_chapter_result_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_main_chapter_result_s2c_descriptor,
        new java.lang.String[] { "Code", "PartId", "IsUnlimited", });
    internal_static_org_gof_demo_worldsrv_msg_main_chapter_kill_reward_c2s_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_org_gof_demo_worldsrv_msg_main_chapter_kill_reward_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_main_chapter_kill_reward_c2s_descriptor,
        new java.lang.String[] { "PartId", "UnitId", "Pos", });
    internal_static_org_gof_demo_worldsrv_msg_main_chapter_kill_reward_s2c_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_org_gof_demo_worldsrv_msg_main_chapter_kill_reward_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_main_chapter_kill_reward_s2c_descriptor,
        new java.lang.String[] { "PartId", "UnitId", "Pos", "RewardList", });
    internal_static_org_gof_demo_worldsrv_msg_main_chapter_reward_info_c2s_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_org_gof_demo_worldsrv_msg_main_chapter_reward_info_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_main_chapter_reward_info_c2s_descriptor,
        new java.lang.String[] { "Type", });
    internal_static_org_gof_demo_worldsrv_msg_main_chapter_reward_info_s2c_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_org_gof_demo_worldsrv_msg_main_chapter_reward_info_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_main_chapter_reward_info_s2c_descriptor,
        new java.lang.String[] { "Type", "Time", "ResList", "ItemList", });
    internal_static_org_gof_demo_worldsrv_msg_main_chapter_claim_reward_c2s_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_org_gof_demo_worldsrv_msg_main_chapter_claim_reward_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_main_chapter_claim_reward_c2s_descriptor,
        new java.lang.String[] { "Type", });
    internal_static_org_gof_demo_worldsrv_msg_main_chapter_claim_reward_s2c_descriptor =
      getDescriptor().getMessageTypes().get(11);
    internal_static_org_gof_demo_worldsrv_msg_main_chapter_claim_reward_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_main_chapter_claim_reward_s2c_descriptor,
        new java.lang.String[] { "Type", });
    com.google.protobuf.ExtensionRegistry registry =
        com.google.protobuf.ExtensionRegistry.newInstance();
    registry.add(org.gof.demo.worldsrv.msg.Options.msgid);
    com.google.protobuf.Descriptors.FileDescriptor
        .internalUpdateFileDescriptor(descriptor, registry);
    org.gof.demo.worldsrv.msg.Options.getDescriptor();
    org.gof.demo.worldsrv.msg.Define.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
