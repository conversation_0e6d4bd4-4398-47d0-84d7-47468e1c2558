// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: msg.skill.proto

// Protobuf Java Version: 3.25.3
package org.gof.demo.worldsrv.msg;

public final class MsgSkill {
  private MsgSkill() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface skill_list_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.skill_list_c2s)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.skill_list_c2s}
   */
  public static final class skill_list_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.skill_list_c2s)
      skill_list_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use skill_list_c2s.newBuilder() to construct.
    private skill_list_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private skill_list_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new skill_list_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_list_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_list_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgSkill.skill_list_c2s.class, org.gof.demo.worldsrv.msg.MsgSkill.skill_list_c2s.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgSkill.skill_list_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgSkill.skill_list_c2s other = (org.gof.demo.worldsrv.msg.MsgSkill.skill_list_c2s) obj;

      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_list_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_list_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_list_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_list_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_list_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_list_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_list_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_list_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_list_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_list_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_list_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_list_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgSkill.skill_list_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.skill_list_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.skill_list_c2s)
        org.gof.demo.worldsrv.msg.MsgSkill.skill_list_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_list_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_list_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgSkill.skill_list_c2s.class, org.gof.demo.worldsrv.msg.MsgSkill.skill_list_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgSkill.skill_list_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_list_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_list_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgSkill.skill_list_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_list_c2s build() {
        org.gof.demo.worldsrv.msg.MsgSkill.skill_list_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_list_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgSkill.skill_list_c2s result = new org.gof.demo.worldsrv.msg.MsgSkill.skill_list_c2s(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgSkill.skill_list_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgSkill.skill_list_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgSkill.skill_list_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgSkill.skill_list_c2s.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.skill_list_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.skill_list_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgSkill.skill_list_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgSkill.skill_list_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_list_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<skill_list_c2s>
        PARSER = new com.google.protobuf.AbstractParser<skill_list_c2s>() {
      @java.lang.Override
      public skill_list_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<skill_list_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<skill_list_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgSkill.skill_list_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface skill_list_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.skill_list_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_active_skill> 
        getActiveSkillsList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_active_skill getActiveSkills(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
     */
    int getActiveSkillsCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_active_skillOrBuilder> 
        getActiveSkillsOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_active_skillOrBuilder getActiveSkillsOrBuilder(
        int index);

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill passive_skills = 2;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_passive_skill> 
        getPassiveSkillsList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill passive_skills = 2;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_passive_skill getPassiveSkills(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill passive_skills = 2;</code>
     */
    int getPassiveSkillsCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill passive_skills = 2;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_passive_skillOrBuilder> 
        getPassiveSkillsOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill passive_skills = 2;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_passive_skillOrBuilder getPassiveSkillsOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.skill_list_s2c}
   */
  public static final class skill_list_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.skill_list_s2c)
      skill_list_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use skill_list_s2c.newBuilder() to construct.
    private skill_list_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private skill_list_s2c() {
      activeSkills_ = java.util.Collections.emptyList();
      passiveSkills_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new skill_list_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_list_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_list_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgSkill.skill_list_s2c.class, org.gof.demo.worldsrv.msg.MsgSkill.skill_list_s2c.Builder.class);
    }

    public static final int ACTIVE_SKILLS_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_active_skill> activeSkills_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_active_skill> getActiveSkillsList() {
      return activeSkills_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_active_skillOrBuilder> 
        getActiveSkillsOrBuilderList() {
      return activeSkills_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
     */
    @java.lang.Override
    public int getActiveSkillsCount() {
      return activeSkills_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_active_skill getActiveSkills(int index) {
      return activeSkills_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_active_skillOrBuilder getActiveSkillsOrBuilder(
        int index) {
      return activeSkills_.get(index);
    }

    public static final int PASSIVE_SKILLS_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_passive_skill> passiveSkills_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill passive_skills = 2;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_passive_skill> getPassiveSkillsList() {
      return passiveSkills_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill passive_skills = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_passive_skillOrBuilder> 
        getPassiveSkillsOrBuilderList() {
      return passiveSkills_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill passive_skills = 2;</code>
     */
    @java.lang.Override
    public int getPassiveSkillsCount() {
      return passiveSkills_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill passive_skills = 2;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_passive_skill getPassiveSkills(int index) {
      return passiveSkills_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill passive_skills = 2;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_passive_skillOrBuilder getPassiveSkillsOrBuilder(
        int index) {
      return passiveSkills_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < activeSkills_.size(); i++) {
        output.writeMessage(1, activeSkills_.get(i));
      }
      for (int i = 0; i < passiveSkills_.size(); i++) {
        output.writeMessage(2, passiveSkills_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < activeSkills_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, activeSkills_.get(i));
      }
      for (int i = 0; i < passiveSkills_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, passiveSkills_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgSkill.skill_list_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgSkill.skill_list_s2c other = (org.gof.demo.worldsrv.msg.MsgSkill.skill_list_s2c) obj;

      if (!getActiveSkillsList()
          .equals(other.getActiveSkillsList())) return false;
      if (!getPassiveSkillsList()
          .equals(other.getPassiveSkillsList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getActiveSkillsCount() > 0) {
        hash = (37 * hash) + ACTIVE_SKILLS_FIELD_NUMBER;
        hash = (53 * hash) + getActiveSkillsList().hashCode();
      }
      if (getPassiveSkillsCount() > 0) {
        hash = (37 * hash) + PASSIVE_SKILLS_FIELD_NUMBER;
        hash = (53 * hash) + getPassiveSkillsList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_list_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_list_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_list_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_list_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_list_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_list_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_list_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_list_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_list_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_list_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_list_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_list_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgSkill.skill_list_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.skill_list_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.skill_list_s2c)
        org.gof.demo.worldsrv.msg.MsgSkill.skill_list_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_list_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_list_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgSkill.skill_list_s2c.class, org.gof.demo.worldsrv.msg.MsgSkill.skill_list_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgSkill.skill_list_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        if (activeSkillsBuilder_ == null) {
          activeSkills_ = java.util.Collections.emptyList();
        } else {
          activeSkills_ = null;
          activeSkillsBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        if (passiveSkillsBuilder_ == null) {
          passiveSkills_ = java.util.Collections.emptyList();
        } else {
          passiveSkills_ = null;
          passiveSkillsBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_list_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_list_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgSkill.skill_list_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_list_s2c build() {
        org.gof.demo.worldsrv.msg.MsgSkill.skill_list_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_list_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgSkill.skill_list_s2c result = new org.gof.demo.worldsrv.msg.MsgSkill.skill_list_s2c(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgSkill.skill_list_s2c result) {
        if (activeSkillsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            activeSkills_ = java.util.Collections.unmodifiableList(activeSkills_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.activeSkills_ = activeSkills_;
        } else {
          result.activeSkills_ = activeSkillsBuilder_.build();
        }
        if (passiveSkillsBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0)) {
            passiveSkills_ = java.util.Collections.unmodifiableList(passiveSkills_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.passiveSkills_ = passiveSkills_;
        } else {
          result.passiveSkills_ = passiveSkillsBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgSkill.skill_list_s2c result) {
        int from_bitField0_ = bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgSkill.skill_list_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgSkill.skill_list_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgSkill.skill_list_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgSkill.skill_list_s2c.getDefaultInstance()) return this;
        if (activeSkillsBuilder_ == null) {
          if (!other.activeSkills_.isEmpty()) {
            if (activeSkills_.isEmpty()) {
              activeSkills_ = other.activeSkills_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureActiveSkillsIsMutable();
              activeSkills_.addAll(other.activeSkills_);
            }
            onChanged();
          }
        } else {
          if (!other.activeSkills_.isEmpty()) {
            if (activeSkillsBuilder_.isEmpty()) {
              activeSkillsBuilder_.dispose();
              activeSkillsBuilder_ = null;
              activeSkills_ = other.activeSkills_;
              bitField0_ = (bitField0_ & ~0x00000001);
              activeSkillsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getActiveSkillsFieldBuilder() : null;
            } else {
              activeSkillsBuilder_.addAllMessages(other.activeSkills_);
            }
          }
        }
        if (passiveSkillsBuilder_ == null) {
          if (!other.passiveSkills_.isEmpty()) {
            if (passiveSkills_.isEmpty()) {
              passiveSkills_ = other.passiveSkills_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensurePassiveSkillsIsMutable();
              passiveSkills_.addAll(other.passiveSkills_);
            }
            onChanged();
          }
        } else {
          if (!other.passiveSkills_.isEmpty()) {
            if (passiveSkillsBuilder_.isEmpty()) {
              passiveSkillsBuilder_.dispose();
              passiveSkillsBuilder_ = null;
              passiveSkills_ = other.passiveSkills_;
              bitField0_ = (bitField0_ & ~0x00000002);
              passiveSkillsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getPassiveSkillsFieldBuilder() : null;
            } else {
              passiveSkillsBuilder_.addAllMessages(other.passiveSkills_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                org.gof.demo.worldsrv.msg.Define.p_active_skill m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_active_skill.parser(),
                        extensionRegistry);
                if (activeSkillsBuilder_ == null) {
                  ensureActiveSkillsIsMutable();
                  activeSkills_.add(m);
                } else {
                  activeSkillsBuilder_.addMessage(m);
                }
                break;
              } // case 10
              case 18: {
                org.gof.demo.worldsrv.msg.Define.p_passive_skill m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_passive_skill.parser(),
                        extensionRegistry);
                if (passiveSkillsBuilder_ == null) {
                  ensurePassiveSkillsIsMutable();
                  passiveSkills_.add(m);
                } else {
                  passiveSkillsBuilder_.addMessage(m);
                }
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_active_skill> activeSkills_ =
        java.util.Collections.emptyList();
      private void ensureActiveSkillsIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          activeSkills_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_active_skill>(activeSkills_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_active_skill, org.gof.demo.worldsrv.msg.Define.p_active_skill.Builder, org.gof.demo.worldsrv.msg.Define.p_active_skillOrBuilder> activeSkillsBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_active_skill> getActiveSkillsList() {
        if (activeSkillsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(activeSkills_);
        } else {
          return activeSkillsBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
       */
      public int getActiveSkillsCount() {
        if (activeSkillsBuilder_ == null) {
          return activeSkills_.size();
        } else {
          return activeSkillsBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_active_skill getActiveSkills(int index) {
        if (activeSkillsBuilder_ == null) {
          return activeSkills_.get(index);
        } else {
          return activeSkillsBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
       */
      public Builder setActiveSkills(
          int index, org.gof.demo.worldsrv.msg.Define.p_active_skill value) {
        if (activeSkillsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureActiveSkillsIsMutable();
          activeSkills_.set(index, value);
          onChanged();
        } else {
          activeSkillsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
       */
      public Builder setActiveSkills(
          int index, org.gof.demo.worldsrv.msg.Define.p_active_skill.Builder builderForValue) {
        if (activeSkillsBuilder_ == null) {
          ensureActiveSkillsIsMutable();
          activeSkills_.set(index, builderForValue.build());
          onChanged();
        } else {
          activeSkillsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
       */
      public Builder addActiveSkills(org.gof.demo.worldsrv.msg.Define.p_active_skill value) {
        if (activeSkillsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureActiveSkillsIsMutable();
          activeSkills_.add(value);
          onChanged();
        } else {
          activeSkillsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
       */
      public Builder addActiveSkills(
          int index, org.gof.demo.worldsrv.msg.Define.p_active_skill value) {
        if (activeSkillsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureActiveSkillsIsMutable();
          activeSkills_.add(index, value);
          onChanged();
        } else {
          activeSkillsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
       */
      public Builder addActiveSkills(
          org.gof.demo.worldsrv.msg.Define.p_active_skill.Builder builderForValue) {
        if (activeSkillsBuilder_ == null) {
          ensureActiveSkillsIsMutable();
          activeSkills_.add(builderForValue.build());
          onChanged();
        } else {
          activeSkillsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
       */
      public Builder addActiveSkills(
          int index, org.gof.demo.worldsrv.msg.Define.p_active_skill.Builder builderForValue) {
        if (activeSkillsBuilder_ == null) {
          ensureActiveSkillsIsMutable();
          activeSkills_.add(index, builderForValue.build());
          onChanged();
        } else {
          activeSkillsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
       */
      public Builder addAllActiveSkills(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_active_skill> values) {
        if (activeSkillsBuilder_ == null) {
          ensureActiveSkillsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, activeSkills_);
          onChanged();
        } else {
          activeSkillsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
       */
      public Builder clearActiveSkills() {
        if (activeSkillsBuilder_ == null) {
          activeSkills_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          activeSkillsBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
       */
      public Builder removeActiveSkills(int index) {
        if (activeSkillsBuilder_ == null) {
          ensureActiveSkillsIsMutable();
          activeSkills_.remove(index);
          onChanged();
        } else {
          activeSkillsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_active_skill.Builder getActiveSkillsBuilder(
          int index) {
        return getActiveSkillsFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_active_skillOrBuilder getActiveSkillsOrBuilder(
          int index) {
        if (activeSkillsBuilder_ == null) {
          return activeSkills_.get(index);  } else {
          return activeSkillsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_active_skillOrBuilder> 
           getActiveSkillsOrBuilderList() {
        if (activeSkillsBuilder_ != null) {
          return activeSkillsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(activeSkills_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_active_skill.Builder addActiveSkillsBuilder() {
        return getActiveSkillsFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_active_skill.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_active_skill.Builder addActiveSkillsBuilder(
          int index) {
        return getActiveSkillsFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_active_skill.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_active_skill.Builder> 
           getActiveSkillsBuilderList() {
        return getActiveSkillsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_active_skill, org.gof.demo.worldsrv.msg.Define.p_active_skill.Builder, org.gof.demo.worldsrv.msg.Define.p_active_skillOrBuilder> 
          getActiveSkillsFieldBuilder() {
        if (activeSkillsBuilder_ == null) {
          activeSkillsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_active_skill, org.gof.demo.worldsrv.msg.Define.p_active_skill.Builder, org.gof.demo.worldsrv.msg.Define.p_active_skillOrBuilder>(
                  activeSkills_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          activeSkills_ = null;
        }
        return activeSkillsBuilder_;
      }

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_passive_skill> passiveSkills_ =
        java.util.Collections.emptyList();
      private void ensurePassiveSkillsIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          passiveSkills_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_passive_skill>(passiveSkills_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_passive_skill, org.gof.demo.worldsrv.msg.Define.p_passive_skill.Builder, org.gof.demo.worldsrv.msg.Define.p_passive_skillOrBuilder> passiveSkillsBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill passive_skills = 2;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_passive_skill> getPassiveSkillsList() {
        if (passiveSkillsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(passiveSkills_);
        } else {
          return passiveSkillsBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill passive_skills = 2;</code>
       */
      public int getPassiveSkillsCount() {
        if (passiveSkillsBuilder_ == null) {
          return passiveSkills_.size();
        } else {
          return passiveSkillsBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill passive_skills = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_passive_skill getPassiveSkills(int index) {
        if (passiveSkillsBuilder_ == null) {
          return passiveSkills_.get(index);
        } else {
          return passiveSkillsBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill passive_skills = 2;</code>
       */
      public Builder setPassiveSkills(
          int index, org.gof.demo.worldsrv.msg.Define.p_passive_skill value) {
        if (passiveSkillsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePassiveSkillsIsMutable();
          passiveSkills_.set(index, value);
          onChanged();
        } else {
          passiveSkillsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill passive_skills = 2;</code>
       */
      public Builder setPassiveSkills(
          int index, org.gof.demo.worldsrv.msg.Define.p_passive_skill.Builder builderForValue) {
        if (passiveSkillsBuilder_ == null) {
          ensurePassiveSkillsIsMutable();
          passiveSkills_.set(index, builderForValue.build());
          onChanged();
        } else {
          passiveSkillsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill passive_skills = 2;</code>
       */
      public Builder addPassiveSkills(org.gof.demo.worldsrv.msg.Define.p_passive_skill value) {
        if (passiveSkillsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePassiveSkillsIsMutable();
          passiveSkills_.add(value);
          onChanged();
        } else {
          passiveSkillsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill passive_skills = 2;</code>
       */
      public Builder addPassiveSkills(
          int index, org.gof.demo.worldsrv.msg.Define.p_passive_skill value) {
        if (passiveSkillsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePassiveSkillsIsMutable();
          passiveSkills_.add(index, value);
          onChanged();
        } else {
          passiveSkillsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill passive_skills = 2;</code>
       */
      public Builder addPassiveSkills(
          org.gof.demo.worldsrv.msg.Define.p_passive_skill.Builder builderForValue) {
        if (passiveSkillsBuilder_ == null) {
          ensurePassiveSkillsIsMutable();
          passiveSkills_.add(builderForValue.build());
          onChanged();
        } else {
          passiveSkillsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill passive_skills = 2;</code>
       */
      public Builder addPassiveSkills(
          int index, org.gof.demo.worldsrv.msg.Define.p_passive_skill.Builder builderForValue) {
        if (passiveSkillsBuilder_ == null) {
          ensurePassiveSkillsIsMutable();
          passiveSkills_.add(index, builderForValue.build());
          onChanged();
        } else {
          passiveSkillsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill passive_skills = 2;</code>
       */
      public Builder addAllPassiveSkills(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_passive_skill> values) {
        if (passiveSkillsBuilder_ == null) {
          ensurePassiveSkillsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, passiveSkills_);
          onChanged();
        } else {
          passiveSkillsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill passive_skills = 2;</code>
       */
      public Builder clearPassiveSkills() {
        if (passiveSkillsBuilder_ == null) {
          passiveSkills_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          passiveSkillsBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill passive_skills = 2;</code>
       */
      public Builder removePassiveSkills(int index) {
        if (passiveSkillsBuilder_ == null) {
          ensurePassiveSkillsIsMutable();
          passiveSkills_.remove(index);
          onChanged();
        } else {
          passiveSkillsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill passive_skills = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_passive_skill.Builder getPassiveSkillsBuilder(
          int index) {
        return getPassiveSkillsFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill passive_skills = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_passive_skillOrBuilder getPassiveSkillsOrBuilder(
          int index) {
        if (passiveSkillsBuilder_ == null) {
          return passiveSkills_.get(index);  } else {
          return passiveSkillsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill passive_skills = 2;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_passive_skillOrBuilder> 
           getPassiveSkillsOrBuilderList() {
        if (passiveSkillsBuilder_ != null) {
          return passiveSkillsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(passiveSkills_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill passive_skills = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_passive_skill.Builder addPassiveSkillsBuilder() {
        return getPassiveSkillsFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_passive_skill.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill passive_skills = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_passive_skill.Builder addPassiveSkillsBuilder(
          int index) {
        return getPassiveSkillsFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_passive_skill.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill passive_skills = 2;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_passive_skill.Builder> 
           getPassiveSkillsBuilderList() {
        return getPassiveSkillsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_passive_skill, org.gof.demo.worldsrv.msg.Define.p_passive_skill.Builder, org.gof.demo.worldsrv.msg.Define.p_passive_skillOrBuilder> 
          getPassiveSkillsFieldBuilder() {
        if (passiveSkillsBuilder_ == null) {
          passiveSkillsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_passive_skill, org.gof.demo.worldsrv.msg.Define.p_passive_skill.Builder, org.gof.demo.worldsrv.msg.Define.p_passive_skillOrBuilder>(
                  passiveSkills_,
                  ((bitField0_ & 0x00000002) != 0),
                  getParentForChildren(),
                  isClean());
          passiveSkills_ = null;
        }
        return passiveSkillsBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.skill_list_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.skill_list_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgSkill.skill_list_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgSkill.skill_list_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_list_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<skill_list_s2c>
        PARSER = new com.google.protobuf.AbstractParser<skill_list_s2c>() {
      @java.lang.Override
      public skill_list_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<skill_list_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<skill_list_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgSkill.skill_list_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface skill_active_update_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.skill_active_update_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_active_skill> 
        getActiveSkillsList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_active_skill getActiveSkills(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
     */
    int getActiveSkillsCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_active_skillOrBuilder> 
        getActiveSkillsOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_active_skillOrBuilder getActiveSkillsOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.skill_active_update_s2c}
   */
  public static final class skill_active_update_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.skill_active_update_s2c)
      skill_active_update_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use skill_active_update_s2c.newBuilder() to construct.
    private skill_active_update_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private skill_active_update_s2c() {
      activeSkills_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new skill_active_update_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_active_update_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_active_update_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgSkill.skill_active_update_s2c.class, org.gof.demo.worldsrv.msg.MsgSkill.skill_active_update_s2c.Builder.class);
    }

    public static final int ACTIVE_SKILLS_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_active_skill> activeSkills_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_active_skill> getActiveSkillsList() {
      return activeSkills_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_active_skillOrBuilder> 
        getActiveSkillsOrBuilderList() {
      return activeSkills_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
     */
    @java.lang.Override
    public int getActiveSkillsCount() {
      return activeSkills_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_active_skill getActiveSkills(int index) {
      return activeSkills_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_active_skillOrBuilder getActiveSkillsOrBuilder(
        int index) {
      return activeSkills_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < activeSkills_.size(); i++) {
        output.writeMessage(1, activeSkills_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < activeSkills_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, activeSkills_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgSkill.skill_active_update_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgSkill.skill_active_update_s2c other = (org.gof.demo.worldsrv.msg.MsgSkill.skill_active_update_s2c) obj;

      if (!getActiveSkillsList()
          .equals(other.getActiveSkillsList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getActiveSkillsCount() > 0) {
        hash = (37 * hash) + ACTIVE_SKILLS_FIELD_NUMBER;
        hash = (53 * hash) + getActiveSkillsList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_active_update_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_active_update_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_active_update_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_active_update_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_active_update_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_active_update_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_active_update_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_active_update_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_active_update_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_active_update_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_active_update_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_active_update_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgSkill.skill_active_update_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.skill_active_update_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.skill_active_update_s2c)
        org.gof.demo.worldsrv.msg.MsgSkill.skill_active_update_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_active_update_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_active_update_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgSkill.skill_active_update_s2c.class, org.gof.demo.worldsrv.msg.MsgSkill.skill_active_update_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgSkill.skill_active_update_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        if (activeSkillsBuilder_ == null) {
          activeSkills_ = java.util.Collections.emptyList();
        } else {
          activeSkills_ = null;
          activeSkillsBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_active_update_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_active_update_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgSkill.skill_active_update_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_active_update_s2c build() {
        org.gof.demo.worldsrv.msg.MsgSkill.skill_active_update_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_active_update_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgSkill.skill_active_update_s2c result = new org.gof.demo.worldsrv.msg.MsgSkill.skill_active_update_s2c(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgSkill.skill_active_update_s2c result) {
        if (activeSkillsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            activeSkills_ = java.util.Collections.unmodifiableList(activeSkills_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.activeSkills_ = activeSkills_;
        } else {
          result.activeSkills_ = activeSkillsBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgSkill.skill_active_update_s2c result) {
        int from_bitField0_ = bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgSkill.skill_active_update_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgSkill.skill_active_update_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgSkill.skill_active_update_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgSkill.skill_active_update_s2c.getDefaultInstance()) return this;
        if (activeSkillsBuilder_ == null) {
          if (!other.activeSkills_.isEmpty()) {
            if (activeSkills_.isEmpty()) {
              activeSkills_ = other.activeSkills_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureActiveSkillsIsMutable();
              activeSkills_.addAll(other.activeSkills_);
            }
            onChanged();
          }
        } else {
          if (!other.activeSkills_.isEmpty()) {
            if (activeSkillsBuilder_.isEmpty()) {
              activeSkillsBuilder_.dispose();
              activeSkillsBuilder_ = null;
              activeSkills_ = other.activeSkills_;
              bitField0_ = (bitField0_ & ~0x00000001);
              activeSkillsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getActiveSkillsFieldBuilder() : null;
            } else {
              activeSkillsBuilder_.addAllMessages(other.activeSkills_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                org.gof.demo.worldsrv.msg.Define.p_active_skill m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_active_skill.parser(),
                        extensionRegistry);
                if (activeSkillsBuilder_ == null) {
                  ensureActiveSkillsIsMutable();
                  activeSkills_.add(m);
                } else {
                  activeSkillsBuilder_.addMessage(m);
                }
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_active_skill> activeSkills_ =
        java.util.Collections.emptyList();
      private void ensureActiveSkillsIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          activeSkills_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_active_skill>(activeSkills_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_active_skill, org.gof.demo.worldsrv.msg.Define.p_active_skill.Builder, org.gof.demo.worldsrv.msg.Define.p_active_skillOrBuilder> activeSkillsBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_active_skill> getActiveSkillsList() {
        if (activeSkillsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(activeSkills_);
        } else {
          return activeSkillsBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
       */
      public int getActiveSkillsCount() {
        if (activeSkillsBuilder_ == null) {
          return activeSkills_.size();
        } else {
          return activeSkillsBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_active_skill getActiveSkills(int index) {
        if (activeSkillsBuilder_ == null) {
          return activeSkills_.get(index);
        } else {
          return activeSkillsBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
       */
      public Builder setActiveSkills(
          int index, org.gof.demo.worldsrv.msg.Define.p_active_skill value) {
        if (activeSkillsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureActiveSkillsIsMutable();
          activeSkills_.set(index, value);
          onChanged();
        } else {
          activeSkillsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
       */
      public Builder setActiveSkills(
          int index, org.gof.demo.worldsrv.msg.Define.p_active_skill.Builder builderForValue) {
        if (activeSkillsBuilder_ == null) {
          ensureActiveSkillsIsMutable();
          activeSkills_.set(index, builderForValue.build());
          onChanged();
        } else {
          activeSkillsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
       */
      public Builder addActiveSkills(org.gof.demo.worldsrv.msg.Define.p_active_skill value) {
        if (activeSkillsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureActiveSkillsIsMutable();
          activeSkills_.add(value);
          onChanged();
        } else {
          activeSkillsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
       */
      public Builder addActiveSkills(
          int index, org.gof.demo.worldsrv.msg.Define.p_active_skill value) {
        if (activeSkillsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureActiveSkillsIsMutable();
          activeSkills_.add(index, value);
          onChanged();
        } else {
          activeSkillsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
       */
      public Builder addActiveSkills(
          org.gof.demo.worldsrv.msg.Define.p_active_skill.Builder builderForValue) {
        if (activeSkillsBuilder_ == null) {
          ensureActiveSkillsIsMutable();
          activeSkills_.add(builderForValue.build());
          onChanged();
        } else {
          activeSkillsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
       */
      public Builder addActiveSkills(
          int index, org.gof.demo.worldsrv.msg.Define.p_active_skill.Builder builderForValue) {
        if (activeSkillsBuilder_ == null) {
          ensureActiveSkillsIsMutable();
          activeSkills_.add(index, builderForValue.build());
          onChanged();
        } else {
          activeSkillsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
       */
      public Builder addAllActiveSkills(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_active_skill> values) {
        if (activeSkillsBuilder_ == null) {
          ensureActiveSkillsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, activeSkills_);
          onChanged();
        } else {
          activeSkillsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
       */
      public Builder clearActiveSkills() {
        if (activeSkillsBuilder_ == null) {
          activeSkills_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          activeSkillsBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
       */
      public Builder removeActiveSkills(int index) {
        if (activeSkillsBuilder_ == null) {
          ensureActiveSkillsIsMutable();
          activeSkills_.remove(index);
          onChanged();
        } else {
          activeSkillsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_active_skill.Builder getActiveSkillsBuilder(
          int index) {
        return getActiveSkillsFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_active_skillOrBuilder getActiveSkillsOrBuilder(
          int index) {
        if (activeSkillsBuilder_ == null) {
          return activeSkills_.get(index);  } else {
          return activeSkillsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_active_skillOrBuilder> 
           getActiveSkillsOrBuilderList() {
        if (activeSkillsBuilder_ != null) {
          return activeSkillsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(activeSkills_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_active_skill.Builder addActiveSkillsBuilder() {
        return getActiveSkillsFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_active_skill.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_active_skill.Builder addActiveSkillsBuilder(
          int index) {
        return getActiveSkillsFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_active_skill.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_active_skill active_skills = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_active_skill.Builder> 
           getActiveSkillsBuilderList() {
        return getActiveSkillsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_active_skill, org.gof.demo.worldsrv.msg.Define.p_active_skill.Builder, org.gof.demo.worldsrv.msg.Define.p_active_skillOrBuilder> 
          getActiveSkillsFieldBuilder() {
        if (activeSkillsBuilder_ == null) {
          activeSkillsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_active_skill, org.gof.demo.worldsrv.msg.Define.p_active_skill.Builder, org.gof.demo.worldsrv.msg.Define.p_active_skillOrBuilder>(
                  activeSkills_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          activeSkills_ = null;
        }
        return activeSkillsBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.skill_active_update_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.skill_active_update_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgSkill.skill_active_update_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgSkill.skill_active_update_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_active_update_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<skill_active_update_s2c>
        PARSER = new com.google.protobuf.AbstractParser<skill_active_update_s2c>() {
      @java.lang.Override
      public skill_active_update_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<skill_active_update_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<skill_active_update_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgSkill.skill_active_update_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface skill_passive_update_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.skill_passive_update_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill update_list = 1;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_passive_skill> 
        getUpdateListList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill update_list = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_passive_skill getUpdateList(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill update_list = 1;</code>
     */
    int getUpdateListCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill update_list = 1;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_passive_skillOrBuilder> 
        getUpdateListOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill update_list = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_passive_skillOrBuilder getUpdateListOrBuilder(
        int index);

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill delete_list = 2;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_passive_skill> 
        getDeleteListList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill delete_list = 2;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_passive_skill getDeleteList(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill delete_list = 2;</code>
     */
    int getDeleteListCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill delete_list = 2;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_passive_skillOrBuilder> 
        getDeleteListOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill delete_list = 2;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_passive_skillOrBuilder getDeleteListOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.skill_passive_update_s2c}
   */
  public static final class skill_passive_update_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.skill_passive_update_s2c)
      skill_passive_update_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use skill_passive_update_s2c.newBuilder() to construct.
    private skill_passive_update_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private skill_passive_update_s2c() {
      updateList_ = java.util.Collections.emptyList();
      deleteList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new skill_passive_update_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_passive_update_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_passive_update_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgSkill.skill_passive_update_s2c.class, org.gof.demo.worldsrv.msg.MsgSkill.skill_passive_update_s2c.Builder.class);
    }

    public static final int UPDATE_LIST_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_passive_skill> updateList_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill update_list = 1;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_passive_skill> getUpdateListList() {
      return updateList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill update_list = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_passive_skillOrBuilder> 
        getUpdateListOrBuilderList() {
      return updateList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill update_list = 1;</code>
     */
    @java.lang.Override
    public int getUpdateListCount() {
      return updateList_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill update_list = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_passive_skill getUpdateList(int index) {
      return updateList_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill update_list = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_passive_skillOrBuilder getUpdateListOrBuilder(
        int index) {
      return updateList_.get(index);
    }

    public static final int DELETE_LIST_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_passive_skill> deleteList_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill delete_list = 2;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_passive_skill> getDeleteListList() {
      return deleteList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill delete_list = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_passive_skillOrBuilder> 
        getDeleteListOrBuilderList() {
      return deleteList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill delete_list = 2;</code>
     */
    @java.lang.Override
    public int getDeleteListCount() {
      return deleteList_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill delete_list = 2;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_passive_skill getDeleteList(int index) {
      return deleteList_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill delete_list = 2;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_passive_skillOrBuilder getDeleteListOrBuilder(
        int index) {
      return deleteList_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < updateList_.size(); i++) {
        output.writeMessage(1, updateList_.get(i));
      }
      for (int i = 0; i < deleteList_.size(); i++) {
        output.writeMessage(2, deleteList_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < updateList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, updateList_.get(i));
      }
      for (int i = 0; i < deleteList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, deleteList_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgSkill.skill_passive_update_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgSkill.skill_passive_update_s2c other = (org.gof.demo.worldsrv.msg.MsgSkill.skill_passive_update_s2c) obj;

      if (!getUpdateListList()
          .equals(other.getUpdateListList())) return false;
      if (!getDeleteListList()
          .equals(other.getDeleteListList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getUpdateListCount() > 0) {
        hash = (37 * hash) + UPDATE_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getUpdateListList().hashCode();
      }
      if (getDeleteListCount() > 0) {
        hash = (37 * hash) + DELETE_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getDeleteListList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_passive_update_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_passive_update_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_passive_update_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_passive_update_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_passive_update_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_passive_update_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_passive_update_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_passive_update_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_passive_update_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_passive_update_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_passive_update_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_passive_update_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgSkill.skill_passive_update_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.skill_passive_update_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.skill_passive_update_s2c)
        org.gof.demo.worldsrv.msg.MsgSkill.skill_passive_update_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_passive_update_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_passive_update_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgSkill.skill_passive_update_s2c.class, org.gof.demo.worldsrv.msg.MsgSkill.skill_passive_update_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgSkill.skill_passive_update_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        if (updateListBuilder_ == null) {
          updateList_ = java.util.Collections.emptyList();
        } else {
          updateList_ = null;
          updateListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        if (deleteListBuilder_ == null) {
          deleteList_ = java.util.Collections.emptyList();
        } else {
          deleteList_ = null;
          deleteListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_passive_update_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_passive_update_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgSkill.skill_passive_update_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_passive_update_s2c build() {
        org.gof.demo.worldsrv.msg.MsgSkill.skill_passive_update_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_passive_update_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgSkill.skill_passive_update_s2c result = new org.gof.demo.worldsrv.msg.MsgSkill.skill_passive_update_s2c(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgSkill.skill_passive_update_s2c result) {
        if (updateListBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            updateList_ = java.util.Collections.unmodifiableList(updateList_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.updateList_ = updateList_;
        } else {
          result.updateList_ = updateListBuilder_.build();
        }
        if (deleteListBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0)) {
            deleteList_ = java.util.Collections.unmodifiableList(deleteList_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.deleteList_ = deleteList_;
        } else {
          result.deleteList_ = deleteListBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgSkill.skill_passive_update_s2c result) {
        int from_bitField0_ = bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgSkill.skill_passive_update_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgSkill.skill_passive_update_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgSkill.skill_passive_update_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgSkill.skill_passive_update_s2c.getDefaultInstance()) return this;
        if (updateListBuilder_ == null) {
          if (!other.updateList_.isEmpty()) {
            if (updateList_.isEmpty()) {
              updateList_ = other.updateList_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureUpdateListIsMutable();
              updateList_.addAll(other.updateList_);
            }
            onChanged();
          }
        } else {
          if (!other.updateList_.isEmpty()) {
            if (updateListBuilder_.isEmpty()) {
              updateListBuilder_.dispose();
              updateListBuilder_ = null;
              updateList_ = other.updateList_;
              bitField0_ = (bitField0_ & ~0x00000001);
              updateListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getUpdateListFieldBuilder() : null;
            } else {
              updateListBuilder_.addAllMessages(other.updateList_);
            }
          }
        }
        if (deleteListBuilder_ == null) {
          if (!other.deleteList_.isEmpty()) {
            if (deleteList_.isEmpty()) {
              deleteList_ = other.deleteList_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensureDeleteListIsMutable();
              deleteList_.addAll(other.deleteList_);
            }
            onChanged();
          }
        } else {
          if (!other.deleteList_.isEmpty()) {
            if (deleteListBuilder_.isEmpty()) {
              deleteListBuilder_.dispose();
              deleteListBuilder_ = null;
              deleteList_ = other.deleteList_;
              bitField0_ = (bitField0_ & ~0x00000002);
              deleteListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getDeleteListFieldBuilder() : null;
            } else {
              deleteListBuilder_.addAllMessages(other.deleteList_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                org.gof.demo.worldsrv.msg.Define.p_passive_skill m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_passive_skill.parser(),
                        extensionRegistry);
                if (updateListBuilder_ == null) {
                  ensureUpdateListIsMutable();
                  updateList_.add(m);
                } else {
                  updateListBuilder_.addMessage(m);
                }
                break;
              } // case 10
              case 18: {
                org.gof.demo.worldsrv.msg.Define.p_passive_skill m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_passive_skill.parser(),
                        extensionRegistry);
                if (deleteListBuilder_ == null) {
                  ensureDeleteListIsMutable();
                  deleteList_.add(m);
                } else {
                  deleteListBuilder_.addMessage(m);
                }
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_passive_skill> updateList_ =
        java.util.Collections.emptyList();
      private void ensureUpdateListIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          updateList_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_passive_skill>(updateList_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_passive_skill, org.gof.demo.worldsrv.msg.Define.p_passive_skill.Builder, org.gof.demo.worldsrv.msg.Define.p_passive_skillOrBuilder> updateListBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill update_list = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_passive_skill> getUpdateListList() {
        if (updateListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(updateList_);
        } else {
          return updateListBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill update_list = 1;</code>
       */
      public int getUpdateListCount() {
        if (updateListBuilder_ == null) {
          return updateList_.size();
        } else {
          return updateListBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill update_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_passive_skill getUpdateList(int index) {
        if (updateListBuilder_ == null) {
          return updateList_.get(index);
        } else {
          return updateListBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill update_list = 1;</code>
       */
      public Builder setUpdateList(
          int index, org.gof.demo.worldsrv.msg.Define.p_passive_skill value) {
        if (updateListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureUpdateListIsMutable();
          updateList_.set(index, value);
          onChanged();
        } else {
          updateListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill update_list = 1;</code>
       */
      public Builder setUpdateList(
          int index, org.gof.demo.worldsrv.msg.Define.p_passive_skill.Builder builderForValue) {
        if (updateListBuilder_ == null) {
          ensureUpdateListIsMutable();
          updateList_.set(index, builderForValue.build());
          onChanged();
        } else {
          updateListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill update_list = 1;</code>
       */
      public Builder addUpdateList(org.gof.demo.worldsrv.msg.Define.p_passive_skill value) {
        if (updateListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureUpdateListIsMutable();
          updateList_.add(value);
          onChanged();
        } else {
          updateListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill update_list = 1;</code>
       */
      public Builder addUpdateList(
          int index, org.gof.demo.worldsrv.msg.Define.p_passive_skill value) {
        if (updateListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureUpdateListIsMutable();
          updateList_.add(index, value);
          onChanged();
        } else {
          updateListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill update_list = 1;</code>
       */
      public Builder addUpdateList(
          org.gof.demo.worldsrv.msg.Define.p_passive_skill.Builder builderForValue) {
        if (updateListBuilder_ == null) {
          ensureUpdateListIsMutable();
          updateList_.add(builderForValue.build());
          onChanged();
        } else {
          updateListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill update_list = 1;</code>
       */
      public Builder addUpdateList(
          int index, org.gof.demo.worldsrv.msg.Define.p_passive_skill.Builder builderForValue) {
        if (updateListBuilder_ == null) {
          ensureUpdateListIsMutable();
          updateList_.add(index, builderForValue.build());
          onChanged();
        } else {
          updateListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill update_list = 1;</code>
       */
      public Builder addAllUpdateList(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_passive_skill> values) {
        if (updateListBuilder_ == null) {
          ensureUpdateListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, updateList_);
          onChanged();
        } else {
          updateListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill update_list = 1;</code>
       */
      public Builder clearUpdateList() {
        if (updateListBuilder_ == null) {
          updateList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          updateListBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill update_list = 1;</code>
       */
      public Builder removeUpdateList(int index) {
        if (updateListBuilder_ == null) {
          ensureUpdateListIsMutable();
          updateList_.remove(index);
          onChanged();
        } else {
          updateListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill update_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_passive_skill.Builder getUpdateListBuilder(
          int index) {
        return getUpdateListFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill update_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_passive_skillOrBuilder getUpdateListOrBuilder(
          int index) {
        if (updateListBuilder_ == null) {
          return updateList_.get(index);  } else {
          return updateListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill update_list = 1;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_passive_skillOrBuilder> 
           getUpdateListOrBuilderList() {
        if (updateListBuilder_ != null) {
          return updateListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(updateList_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill update_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_passive_skill.Builder addUpdateListBuilder() {
        return getUpdateListFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_passive_skill.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill update_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_passive_skill.Builder addUpdateListBuilder(
          int index) {
        return getUpdateListFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_passive_skill.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill update_list = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_passive_skill.Builder> 
           getUpdateListBuilderList() {
        return getUpdateListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_passive_skill, org.gof.demo.worldsrv.msg.Define.p_passive_skill.Builder, org.gof.demo.worldsrv.msg.Define.p_passive_skillOrBuilder> 
          getUpdateListFieldBuilder() {
        if (updateListBuilder_ == null) {
          updateListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_passive_skill, org.gof.demo.worldsrv.msg.Define.p_passive_skill.Builder, org.gof.demo.worldsrv.msg.Define.p_passive_skillOrBuilder>(
                  updateList_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          updateList_ = null;
        }
        return updateListBuilder_;
      }

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_passive_skill> deleteList_ =
        java.util.Collections.emptyList();
      private void ensureDeleteListIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          deleteList_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_passive_skill>(deleteList_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_passive_skill, org.gof.demo.worldsrv.msg.Define.p_passive_skill.Builder, org.gof.demo.worldsrv.msg.Define.p_passive_skillOrBuilder> deleteListBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill delete_list = 2;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_passive_skill> getDeleteListList() {
        if (deleteListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(deleteList_);
        } else {
          return deleteListBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill delete_list = 2;</code>
       */
      public int getDeleteListCount() {
        if (deleteListBuilder_ == null) {
          return deleteList_.size();
        } else {
          return deleteListBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill delete_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_passive_skill getDeleteList(int index) {
        if (deleteListBuilder_ == null) {
          return deleteList_.get(index);
        } else {
          return deleteListBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill delete_list = 2;</code>
       */
      public Builder setDeleteList(
          int index, org.gof.demo.worldsrv.msg.Define.p_passive_skill value) {
        if (deleteListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDeleteListIsMutable();
          deleteList_.set(index, value);
          onChanged();
        } else {
          deleteListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill delete_list = 2;</code>
       */
      public Builder setDeleteList(
          int index, org.gof.demo.worldsrv.msg.Define.p_passive_skill.Builder builderForValue) {
        if (deleteListBuilder_ == null) {
          ensureDeleteListIsMutable();
          deleteList_.set(index, builderForValue.build());
          onChanged();
        } else {
          deleteListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill delete_list = 2;</code>
       */
      public Builder addDeleteList(org.gof.demo.worldsrv.msg.Define.p_passive_skill value) {
        if (deleteListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDeleteListIsMutable();
          deleteList_.add(value);
          onChanged();
        } else {
          deleteListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill delete_list = 2;</code>
       */
      public Builder addDeleteList(
          int index, org.gof.demo.worldsrv.msg.Define.p_passive_skill value) {
        if (deleteListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDeleteListIsMutable();
          deleteList_.add(index, value);
          onChanged();
        } else {
          deleteListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill delete_list = 2;</code>
       */
      public Builder addDeleteList(
          org.gof.demo.worldsrv.msg.Define.p_passive_skill.Builder builderForValue) {
        if (deleteListBuilder_ == null) {
          ensureDeleteListIsMutable();
          deleteList_.add(builderForValue.build());
          onChanged();
        } else {
          deleteListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill delete_list = 2;</code>
       */
      public Builder addDeleteList(
          int index, org.gof.demo.worldsrv.msg.Define.p_passive_skill.Builder builderForValue) {
        if (deleteListBuilder_ == null) {
          ensureDeleteListIsMutable();
          deleteList_.add(index, builderForValue.build());
          onChanged();
        } else {
          deleteListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill delete_list = 2;</code>
       */
      public Builder addAllDeleteList(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_passive_skill> values) {
        if (deleteListBuilder_ == null) {
          ensureDeleteListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, deleteList_);
          onChanged();
        } else {
          deleteListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill delete_list = 2;</code>
       */
      public Builder clearDeleteList() {
        if (deleteListBuilder_ == null) {
          deleteList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          deleteListBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill delete_list = 2;</code>
       */
      public Builder removeDeleteList(int index) {
        if (deleteListBuilder_ == null) {
          ensureDeleteListIsMutable();
          deleteList_.remove(index);
          onChanged();
        } else {
          deleteListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill delete_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_passive_skill.Builder getDeleteListBuilder(
          int index) {
        return getDeleteListFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill delete_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_passive_skillOrBuilder getDeleteListOrBuilder(
          int index) {
        if (deleteListBuilder_ == null) {
          return deleteList_.get(index);  } else {
          return deleteListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill delete_list = 2;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_passive_skillOrBuilder> 
           getDeleteListOrBuilderList() {
        if (deleteListBuilder_ != null) {
          return deleteListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(deleteList_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill delete_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_passive_skill.Builder addDeleteListBuilder() {
        return getDeleteListFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_passive_skill.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill delete_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_passive_skill.Builder addDeleteListBuilder(
          int index) {
        return getDeleteListFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_passive_skill.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_passive_skill delete_list = 2;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_passive_skill.Builder> 
           getDeleteListBuilderList() {
        return getDeleteListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_passive_skill, org.gof.demo.worldsrv.msg.Define.p_passive_skill.Builder, org.gof.demo.worldsrv.msg.Define.p_passive_skillOrBuilder> 
          getDeleteListFieldBuilder() {
        if (deleteListBuilder_ == null) {
          deleteListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_passive_skill, org.gof.demo.worldsrv.msg.Define.p_passive_skill.Builder, org.gof.demo.worldsrv.msg.Define.p_passive_skillOrBuilder>(
                  deleteList_,
                  ((bitField0_ & 0x00000002) != 0),
                  getParentForChildren(),
                  isClean());
          deleteList_ = null;
        }
        return deleteListBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.skill_passive_update_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.skill_passive_update_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgSkill.skill_passive_update_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgSkill.skill_passive_update_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_passive_update_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<skill_passive_update_s2c>
        PARSER = new com.google.protobuf.AbstractParser<skill_passive_update_s2c>() {
      @java.lang.Override
      public skill_passive_update_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<skill_passive_update_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<skill_passive_update_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgSkill.skill_passive_update_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface skill_system_info_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.skill_system_info_c2s)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.skill_system_info_c2s}
   */
  public static final class skill_system_info_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.skill_system_info_c2s)
      skill_system_info_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use skill_system_info_c2s.newBuilder() to construct.
    private skill_system_info_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private skill_system_info_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new skill_system_info_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_system_info_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_system_info_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_c2s.class, org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_c2s.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_c2s other = (org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_c2s) obj;

      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.skill_system_info_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.skill_system_info_c2s)
        org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_system_info_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_system_info_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_c2s.class, org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_system_info_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_c2s build() {
        org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_c2s result = new org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_c2s(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_c2s.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.skill_system_info_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.skill_system_info_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<skill_system_info_c2s>
        PARSER = new com.google.protobuf.AbstractParser<skill_system_info_c2s>() {
      @java.lang.Override
      public skill_system_info_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<skill_system_info_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<skill_system_info_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface skill_system_info_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.skill_system_info_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 1;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_skill_system> 
        getSkillsList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_skill_system getSkills(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 1;</code>
     */
    int getSkillsCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 1;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_skill_systemOrBuilder> 
        getSkillsOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_skill_systemOrBuilder getSkillsOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.skill_system_info_s2c}
   */
  public static final class skill_system_info_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.skill_system_info_s2c)
      skill_system_info_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use skill_system_info_s2c.newBuilder() to construct.
    private skill_system_info_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private skill_system_info_s2c() {
      skills_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new skill_system_info_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_system_info_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_system_info_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_s2c.class, org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_s2c.Builder.class);
    }

    public static final int SKILLS_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_skill_system> skills_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 1;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_skill_system> getSkillsList() {
      return skills_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_skill_systemOrBuilder> 
        getSkillsOrBuilderList() {
      return skills_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 1;</code>
     */
    @java.lang.Override
    public int getSkillsCount() {
      return skills_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_skill_system getSkills(int index) {
      return skills_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_skill_systemOrBuilder getSkillsOrBuilder(
        int index) {
      return skills_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < skills_.size(); i++) {
        output.writeMessage(1, skills_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < skills_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, skills_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_s2c other = (org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_s2c) obj;

      if (!getSkillsList()
          .equals(other.getSkillsList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getSkillsCount() > 0) {
        hash = (37 * hash) + SKILLS_FIELD_NUMBER;
        hash = (53 * hash) + getSkillsList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.skill_system_info_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.skill_system_info_s2c)
        org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_system_info_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_system_info_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_s2c.class, org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        if (skillsBuilder_ == null) {
          skills_ = java.util.Collections.emptyList();
        } else {
          skills_ = null;
          skillsBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_system_info_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_s2c build() {
        org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_s2c result = new org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_s2c(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_s2c result) {
        if (skillsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            skills_ = java.util.Collections.unmodifiableList(skills_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.skills_ = skills_;
        } else {
          result.skills_ = skillsBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_s2c result) {
        int from_bitField0_ = bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_s2c.getDefaultInstance()) return this;
        if (skillsBuilder_ == null) {
          if (!other.skills_.isEmpty()) {
            if (skills_.isEmpty()) {
              skills_ = other.skills_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureSkillsIsMutable();
              skills_.addAll(other.skills_);
            }
            onChanged();
          }
        } else {
          if (!other.skills_.isEmpty()) {
            if (skillsBuilder_.isEmpty()) {
              skillsBuilder_.dispose();
              skillsBuilder_ = null;
              skills_ = other.skills_;
              bitField0_ = (bitField0_ & ~0x00000001);
              skillsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getSkillsFieldBuilder() : null;
            } else {
              skillsBuilder_.addAllMessages(other.skills_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                org.gof.demo.worldsrv.msg.Define.p_skill_system m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_skill_system.parser(),
                        extensionRegistry);
                if (skillsBuilder_ == null) {
                  ensureSkillsIsMutable();
                  skills_.add(m);
                } else {
                  skillsBuilder_.addMessage(m);
                }
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_skill_system> skills_ =
        java.util.Collections.emptyList();
      private void ensureSkillsIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          skills_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_skill_system>(skills_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_skill_system, org.gof.demo.worldsrv.msg.Define.p_skill_system.Builder, org.gof.demo.worldsrv.msg.Define.p_skill_systemOrBuilder> skillsBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_skill_system> getSkillsList() {
        if (skillsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(skills_);
        } else {
          return skillsBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 1;</code>
       */
      public int getSkillsCount() {
        if (skillsBuilder_ == null) {
          return skills_.size();
        } else {
          return skillsBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_skill_system getSkills(int index) {
        if (skillsBuilder_ == null) {
          return skills_.get(index);
        } else {
          return skillsBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 1;</code>
       */
      public Builder setSkills(
          int index, org.gof.demo.worldsrv.msg.Define.p_skill_system value) {
        if (skillsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSkillsIsMutable();
          skills_.set(index, value);
          onChanged();
        } else {
          skillsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 1;</code>
       */
      public Builder setSkills(
          int index, org.gof.demo.worldsrv.msg.Define.p_skill_system.Builder builderForValue) {
        if (skillsBuilder_ == null) {
          ensureSkillsIsMutable();
          skills_.set(index, builderForValue.build());
          onChanged();
        } else {
          skillsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 1;</code>
       */
      public Builder addSkills(org.gof.demo.worldsrv.msg.Define.p_skill_system value) {
        if (skillsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSkillsIsMutable();
          skills_.add(value);
          onChanged();
        } else {
          skillsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 1;</code>
       */
      public Builder addSkills(
          int index, org.gof.demo.worldsrv.msg.Define.p_skill_system value) {
        if (skillsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSkillsIsMutable();
          skills_.add(index, value);
          onChanged();
        } else {
          skillsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 1;</code>
       */
      public Builder addSkills(
          org.gof.demo.worldsrv.msg.Define.p_skill_system.Builder builderForValue) {
        if (skillsBuilder_ == null) {
          ensureSkillsIsMutable();
          skills_.add(builderForValue.build());
          onChanged();
        } else {
          skillsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 1;</code>
       */
      public Builder addSkills(
          int index, org.gof.demo.worldsrv.msg.Define.p_skill_system.Builder builderForValue) {
        if (skillsBuilder_ == null) {
          ensureSkillsIsMutable();
          skills_.add(index, builderForValue.build());
          onChanged();
        } else {
          skillsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 1;</code>
       */
      public Builder addAllSkills(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_skill_system> values) {
        if (skillsBuilder_ == null) {
          ensureSkillsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, skills_);
          onChanged();
        } else {
          skillsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 1;</code>
       */
      public Builder clearSkills() {
        if (skillsBuilder_ == null) {
          skills_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          skillsBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 1;</code>
       */
      public Builder removeSkills(int index) {
        if (skillsBuilder_ == null) {
          ensureSkillsIsMutable();
          skills_.remove(index);
          onChanged();
        } else {
          skillsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_skill_system.Builder getSkillsBuilder(
          int index) {
        return getSkillsFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_skill_systemOrBuilder getSkillsOrBuilder(
          int index) {
        if (skillsBuilder_ == null) {
          return skills_.get(index);  } else {
          return skillsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 1;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_skill_systemOrBuilder> 
           getSkillsOrBuilderList() {
        if (skillsBuilder_ != null) {
          return skillsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(skills_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_skill_system.Builder addSkillsBuilder() {
        return getSkillsFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_skill_system.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_skill_system.Builder addSkillsBuilder(
          int index) {
        return getSkillsFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_skill_system.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_skill_system.Builder> 
           getSkillsBuilderList() {
        return getSkillsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_skill_system, org.gof.demo.worldsrv.msg.Define.p_skill_system.Builder, org.gof.demo.worldsrv.msg.Define.p_skill_systemOrBuilder> 
          getSkillsFieldBuilder() {
        if (skillsBuilder_ == null) {
          skillsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_skill_system, org.gof.demo.worldsrv.msg.Define.p_skill_system.Builder, org.gof.demo.worldsrv.msg.Define.p_skill_systemOrBuilder>(
                  skills_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          skills_ = null;
        }
        return skillsBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.skill_system_info_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.skill_system_info_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<skill_system_info_s2c>
        PARSER = new com.google.protobuf.AbstractParser<skill_system_info_s2c>() {
      @java.lang.Override
      public skill_system_info_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<skill_system_info_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<skill_system_info_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgSkill.skill_system_info_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface skill_system_update_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.skill_system_update_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 type = 1;</code>
     * @return The type.
     */
    int getType();

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 2;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_skill_system> 
        getSkillsList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 2;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_skill_system getSkills(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 2;</code>
     */
    int getSkillsCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 2;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_skill_systemOrBuilder> 
        getSkillsOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 2;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_skill_systemOrBuilder getSkillsOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.skill_system_update_s2c}
   */
  public static final class skill_system_update_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.skill_system_update_s2c)
      skill_system_update_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use skill_system_update_s2c.newBuilder() to construct.
    private skill_system_update_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private skill_system_update_s2c() {
      skills_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new skill_system_update_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_system_update_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_system_update_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgSkill.skill_system_update_s2c.class, org.gof.demo.worldsrv.msg.MsgSkill.skill_system_update_s2c.Builder.class);
    }

    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_ = 0;
    /**
     * <code>int32 type = 1;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }

    public static final int SKILLS_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_skill_system> skills_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 2;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_skill_system> getSkillsList() {
      return skills_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_skill_systemOrBuilder> 
        getSkillsOrBuilderList() {
      return skills_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 2;</code>
     */
    @java.lang.Override
    public int getSkillsCount() {
      return skills_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 2;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_skill_system getSkills(int index) {
      return skills_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 2;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_skill_systemOrBuilder getSkillsOrBuilder(
        int index) {
      return skills_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (type_ != 0) {
        output.writeInt32(1, type_);
      }
      for (int i = 0; i < skills_.size(); i++) {
        output.writeMessage(2, skills_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, type_);
      }
      for (int i = 0; i < skills_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, skills_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgSkill.skill_system_update_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgSkill.skill_system_update_s2c other = (org.gof.demo.worldsrv.msg.MsgSkill.skill_system_update_s2c) obj;

      if (getType()
          != other.getType()) return false;
      if (!getSkillsList()
          .equals(other.getSkillsList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      if (getSkillsCount() > 0) {
        hash = (37 * hash) + SKILLS_FIELD_NUMBER;
        hash = (53 * hash) + getSkillsList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_system_update_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_system_update_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_system_update_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_system_update_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_system_update_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_system_update_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_system_update_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_system_update_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_system_update_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_system_update_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_system_update_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_system_update_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgSkill.skill_system_update_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.skill_system_update_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.skill_system_update_s2c)
        org.gof.demo.worldsrv.msg.MsgSkill.skill_system_update_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_system_update_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_system_update_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgSkill.skill_system_update_s2c.class, org.gof.demo.worldsrv.msg.MsgSkill.skill_system_update_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgSkill.skill_system_update_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        type_ = 0;
        if (skillsBuilder_ == null) {
          skills_ = java.util.Collections.emptyList();
        } else {
          skills_ = null;
          skillsBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_system_update_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_system_update_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgSkill.skill_system_update_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_system_update_s2c build() {
        org.gof.demo.worldsrv.msg.MsgSkill.skill_system_update_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_system_update_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgSkill.skill_system_update_s2c result = new org.gof.demo.worldsrv.msg.MsgSkill.skill_system_update_s2c(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgSkill.skill_system_update_s2c result) {
        if (skillsBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0)) {
            skills_ = java.util.Collections.unmodifiableList(skills_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.skills_ = skills_;
        } else {
          result.skills_ = skillsBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgSkill.skill_system_update_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.type_ = type_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgSkill.skill_system_update_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgSkill.skill_system_update_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgSkill.skill_system_update_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgSkill.skill_system_update_s2c.getDefaultInstance()) return this;
        if (other.getType() != 0) {
          setType(other.getType());
        }
        if (skillsBuilder_ == null) {
          if (!other.skills_.isEmpty()) {
            if (skills_.isEmpty()) {
              skills_ = other.skills_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensureSkillsIsMutable();
              skills_.addAll(other.skills_);
            }
            onChanged();
          }
        } else {
          if (!other.skills_.isEmpty()) {
            if (skillsBuilder_.isEmpty()) {
              skillsBuilder_.dispose();
              skillsBuilder_ = null;
              skills_ = other.skills_;
              bitField0_ = (bitField0_ & ~0x00000002);
              skillsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getSkillsFieldBuilder() : null;
            } else {
              skillsBuilder_.addAllMessages(other.skills_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                type_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 18: {
                org.gof.demo.worldsrv.msg.Define.p_skill_system m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_skill_system.parser(),
                        extensionRegistry);
                if (skillsBuilder_ == null) {
                  ensureSkillsIsMutable();
                  skills_.add(m);
                } else {
                  skillsBuilder_.addMessage(m);
                }
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int type_ ;
      /**
       * <code>int32 type = 1;</code>
       * @return The type.
       */
      @java.lang.Override
      public int getType() {
        return type_;
      }
      /**
       * <code>int32 type = 1;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(int value) {

        type_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>int32 type = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        type_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_skill_system> skills_ =
        java.util.Collections.emptyList();
      private void ensureSkillsIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          skills_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_skill_system>(skills_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_skill_system, org.gof.demo.worldsrv.msg.Define.p_skill_system.Builder, org.gof.demo.worldsrv.msg.Define.p_skill_systemOrBuilder> skillsBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 2;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_skill_system> getSkillsList() {
        if (skillsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(skills_);
        } else {
          return skillsBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 2;</code>
       */
      public int getSkillsCount() {
        if (skillsBuilder_ == null) {
          return skills_.size();
        } else {
          return skillsBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_skill_system getSkills(int index) {
        if (skillsBuilder_ == null) {
          return skills_.get(index);
        } else {
          return skillsBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 2;</code>
       */
      public Builder setSkills(
          int index, org.gof.demo.worldsrv.msg.Define.p_skill_system value) {
        if (skillsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSkillsIsMutable();
          skills_.set(index, value);
          onChanged();
        } else {
          skillsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 2;</code>
       */
      public Builder setSkills(
          int index, org.gof.demo.worldsrv.msg.Define.p_skill_system.Builder builderForValue) {
        if (skillsBuilder_ == null) {
          ensureSkillsIsMutable();
          skills_.set(index, builderForValue.build());
          onChanged();
        } else {
          skillsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 2;</code>
       */
      public Builder addSkills(org.gof.demo.worldsrv.msg.Define.p_skill_system value) {
        if (skillsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSkillsIsMutable();
          skills_.add(value);
          onChanged();
        } else {
          skillsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 2;</code>
       */
      public Builder addSkills(
          int index, org.gof.demo.worldsrv.msg.Define.p_skill_system value) {
        if (skillsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSkillsIsMutable();
          skills_.add(index, value);
          onChanged();
        } else {
          skillsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 2;</code>
       */
      public Builder addSkills(
          org.gof.demo.worldsrv.msg.Define.p_skill_system.Builder builderForValue) {
        if (skillsBuilder_ == null) {
          ensureSkillsIsMutable();
          skills_.add(builderForValue.build());
          onChanged();
        } else {
          skillsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 2;</code>
       */
      public Builder addSkills(
          int index, org.gof.demo.worldsrv.msg.Define.p_skill_system.Builder builderForValue) {
        if (skillsBuilder_ == null) {
          ensureSkillsIsMutable();
          skills_.add(index, builderForValue.build());
          onChanged();
        } else {
          skillsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 2;</code>
       */
      public Builder addAllSkills(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_skill_system> values) {
        if (skillsBuilder_ == null) {
          ensureSkillsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, skills_);
          onChanged();
        } else {
          skillsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 2;</code>
       */
      public Builder clearSkills() {
        if (skillsBuilder_ == null) {
          skills_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          skillsBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 2;</code>
       */
      public Builder removeSkills(int index) {
        if (skillsBuilder_ == null) {
          ensureSkillsIsMutable();
          skills_.remove(index);
          onChanged();
        } else {
          skillsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_skill_system.Builder getSkillsBuilder(
          int index) {
        return getSkillsFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_skill_systemOrBuilder getSkillsOrBuilder(
          int index) {
        if (skillsBuilder_ == null) {
          return skills_.get(index);  } else {
          return skillsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 2;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_skill_systemOrBuilder> 
           getSkillsOrBuilderList() {
        if (skillsBuilder_ != null) {
          return skillsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(skills_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_skill_system.Builder addSkillsBuilder() {
        return getSkillsFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_skill_system.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_skill_system.Builder addSkillsBuilder(
          int index) {
        return getSkillsFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_skill_system.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_system skills = 2;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_skill_system.Builder> 
           getSkillsBuilderList() {
        return getSkillsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_skill_system, org.gof.demo.worldsrv.msg.Define.p_skill_system.Builder, org.gof.demo.worldsrv.msg.Define.p_skill_systemOrBuilder> 
          getSkillsFieldBuilder() {
        if (skillsBuilder_ == null) {
          skillsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_skill_system, org.gof.demo.worldsrv.msg.Define.p_skill_system.Builder, org.gof.demo.worldsrv.msg.Define.p_skill_systemOrBuilder>(
                  skills_,
                  ((bitField0_ & 0x00000002) != 0),
                  getParentForChildren(),
                  isClean());
          skills_ = null;
        }
        return skillsBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.skill_system_update_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.skill_system_update_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgSkill.skill_system_update_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgSkill.skill_system_update_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_system_update_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<skill_system_update_s2c>
        PARSER = new com.google.protobuf.AbstractParser<skill_system_update_s2c>() {
      @java.lang.Override
      public skill_system_update_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<skill_system_update_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<skill_system_update_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgSkill.skill_system_update_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface skill_lv_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.skill_lv_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated int32 skill_ids = 1 [packed = false];</code>
     * @return A list containing the skillIds.
     */
    java.util.List<java.lang.Integer> getSkillIdsList();
    /**
     * <code>repeated int32 skill_ids = 1 [packed = false];</code>
     * @return The count of skillIds.
     */
    int getSkillIdsCount();
    /**
     * <code>repeated int32 skill_ids = 1 [packed = false];</code>
     * @param index The index of the element to return.
     * @return The skillIds at the given index.
     */
    int getSkillIds(int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.skill_lv_c2s}
   */
  public static final class skill_lv_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.skill_lv_c2s)
      skill_lv_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use skill_lv_c2s.newBuilder() to construct.
    private skill_lv_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private skill_lv_c2s() {
      skillIds_ = emptyIntList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new skill_lv_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_lv_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_lv_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgSkill.skill_lv_c2s.class, org.gof.demo.worldsrv.msg.MsgSkill.skill_lv_c2s.Builder.class);
    }

    public static final int SKILL_IDS_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.IntList skillIds_ =
        emptyIntList();
    /**
     * <code>repeated int32 skill_ids = 1 [packed = false];</code>
     * @return A list containing the skillIds.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getSkillIdsList() {
      return skillIds_;
    }
    /**
     * <code>repeated int32 skill_ids = 1 [packed = false];</code>
     * @return The count of skillIds.
     */
    public int getSkillIdsCount() {
      return skillIds_.size();
    }
    /**
     * <code>repeated int32 skill_ids = 1 [packed = false];</code>
     * @param index The index of the element to return.
     * @return The skillIds at the given index.
     */
    public int getSkillIds(int index) {
      return skillIds_.getInt(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < skillIds_.size(); i++) {
        output.writeInt32(1, skillIds_.getInt(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        for (int i = 0; i < skillIds_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(skillIds_.getInt(i));
        }
        size += dataSize;
        size += 1 * getSkillIdsList().size();
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgSkill.skill_lv_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgSkill.skill_lv_c2s other = (org.gof.demo.worldsrv.msg.MsgSkill.skill_lv_c2s) obj;

      if (!getSkillIdsList()
          .equals(other.getSkillIdsList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getSkillIdsCount() > 0) {
        hash = (37 * hash) + SKILL_IDS_FIELD_NUMBER;
        hash = (53 * hash) + getSkillIdsList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_lv_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_lv_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_lv_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_lv_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_lv_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_lv_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_lv_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_lv_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_lv_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_lv_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_lv_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_lv_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgSkill.skill_lv_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.skill_lv_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.skill_lv_c2s)
        org.gof.demo.worldsrv.msg.MsgSkill.skill_lv_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_lv_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_lv_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgSkill.skill_lv_c2s.class, org.gof.demo.worldsrv.msg.MsgSkill.skill_lv_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgSkill.skill_lv_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        skillIds_ = emptyIntList();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_lv_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_lv_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgSkill.skill_lv_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_lv_c2s build() {
        org.gof.demo.worldsrv.msg.MsgSkill.skill_lv_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_lv_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgSkill.skill_lv_c2s result = new org.gof.demo.worldsrv.msg.MsgSkill.skill_lv_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgSkill.skill_lv_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          skillIds_.makeImmutable();
          result.skillIds_ = skillIds_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgSkill.skill_lv_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgSkill.skill_lv_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgSkill.skill_lv_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgSkill.skill_lv_c2s.getDefaultInstance()) return this;
        if (!other.skillIds_.isEmpty()) {
          if (skillIds_.isEmpty()) {
            skillIds_ = other.skillIds_;
            skillIds_.makeImmutable();
            bitField0_ |= 0x00000001;
          } else {
            ensureSkillIdsIsMutable();
            skillIds_.addAll(other.skillIds_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                int v = input.readInt32();
                ensureSkillIdsIsMutable();
                skillIds_.addInt(v);
                break;
              } // case 8
              case 10: {
                int length = input.readRawVarint32();
                int limit = input.pushLimit(length);
                ensureSkillIdsIsMutable();
                while (input.getBytesUntilLimit() > 0) {
                  skillIds_.addInt(input.readInt32());
                }
                input.popLimit(limit);
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.Internal.IntList skillIds_ = emptyIntList();
      private void ensureSkillIdsIsMutable() {
        if (!skillIds_.isModifiable()) {
          skillIds_ = makeMutableCopy(skillIds_);
        }
        bitField0_ |= 0x00000001;
      }
      /**
       * <code>repeated int32 skill_ids = 1 [packed = false];</code>
       * @return A list containing the skillIds.
       */
      public java.util.List<java.lang.Integer>
          getSkillIdsList() {
        skillIds_.makeImmutable();
        return skillIds_;
      }
      /**
       * <code>repeated int32 skill_ids = 1 [packed = false];</code>
       * @return The count of skillIds.
       */
      public int getSkillIdsCount() {
        return skillIds_.size();
      }
      /**
       * <code>repeated int32 skill_ids = 1 [packed = false];</code>
       * @param index The index of the element to return.
       * @return The skillIds at the given index.
       */
      public int getSkillIds(int index) {
        return skillIds_.getInt(index);
      }
      /**
       * <code>repeated int32 skill_ids = 1 [packed = false];</code>
       * @param index The index to set the value at.
       * @param value The skillIds to set.
       * @return This builder for chaining.
       */
      public Builder setSkillIds(
          int index, int value) {

        ensureSkillIdsIsMutable();
        skillIds_.setInt(index, value);
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 skill_ids = 1 [packed = false];</code>
       * @param value The skillIds to add.
       * @return This builder for chaining.
       */
      public Builder addSkillIds(int value) {

        ensureSkillIdsIsMutable();
        skillIds_.addInt(value);
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 skill_ids = 1 [packed = false];</code>
       * @param values The skillIds to add.
       * @return This builder for chaining.
       */
      public Builder addAllSkillIds(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureSkillIdsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, skillIds_);
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 skill_ids = 1 [packed = false];</code>
       * @return This builder for chaining.
       */
      public Builder clearSkillIds() {
        skillIds_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.skill_lv_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.skill_lv_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgSkill.skill_lv_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgSkill.skill_lv_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_lv_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<skill_lv_c2s>
        PARSER = new com.google.protobuf.AbstractParser<skill_lv_c2s>() {
      @java.lang.Override
      public skill_lv_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<skill_lv_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<skill_lv_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgSkill.skill_lv_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface skill_equip_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.skill_equip_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 1;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> 
        getPosInfoList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_key_value getPosInfo(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 1;</code>
     */
    int getPosInfoCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 1;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
        getPosInfoOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder getPosInfoOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.skill_equip_c2s}
   */
  public static final class skill_equip_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.skill_equip_c2s)
      skill_equip_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use skill_equip_c2s.newBuilder() to construct.
    private skill_equip_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private skill_equip_c2s() {
      posInfo_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new skill_equip_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_equip_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_equip_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgSkill.skill_equip_c2s.class, org.gof.demo.worldsrv.msg.MsgSkill.skill_equip_c2s.Builder.class);
    }

    public static final int POS_INFO_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> posInfo_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 1;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> getPosInfoList() {
      return posInfo_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
        getPosInfoOrBuilderList() {
      return posInfo_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 1;</code>
     */
    @java.lang.Override
    public int getPosInfoCount() {
      return posInfo_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_key_value getPosInfo(int index) {
      return posInfo_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder getPosInfoOrBuilder(
        int index) {
      return posInfo_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < posInfo_.size(); i++) {
        output.writeMessage(1, posInfo_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < posInfo_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, posInfo_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgSkill.skill_equip_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgSkill.skill_equip_c2s other = (org.gof.demo.worldsrv.msg.MsgSkill.skill_equip_c2s) obj;

      if (!getPosInfoList()
          .equals(other.getPosInfoList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getPosInfoCount() > 0) {
        hash = (37 * hash) + POS_INFO_FIELD_NUMBER;
        hash = (53 * hash) + getPosInfoList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_equip_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_equip_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_equip_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_equip_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_equip_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_equip_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_equip_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_equip_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_equip_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_equip_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_equip_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_equip_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgSkill.skill_equip_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.skill_equip_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.skill_equip_c2s)
        org.gof.demo.worldsrv.msg.MsgSkill.skill_equip_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_equip_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_equip_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgSkill.skill_equip_c2s.class, org.gof.demo.worldsrv.msg.MsgSkill.skill_equip_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgSkill.skill_equip_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        if (posInfoBuilder_ == null) {
          posInfo_ = java.util.Collections.emptyList();
        } else {
          posInfo_ = null;
          posInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_equip_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_equip_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgSkill.skill_equip_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_equip_c2s build() {
        org.gof.demo.worldsrv.msg.MsgSkill.skill_equip_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_equip_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgSkill.skill_equip_c2s result = new org.gof.demo.worldsrv.msg.MsgSkill.skill_equip_c2s(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgSkill.skill_equip_c2s result) {
        if (posInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            posInfo_ = java.util.Collections.unmodifiableList(posInfo_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.posInfo_ = posInfo_;
        } else {
          result.posInfo_ = posInfoBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgSkill.skill_equip_c2s result) {
        int from_bitField0_ = bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgSkill.skill_equip_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgSkill.skill_equip_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgSkill.skill_equip_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgSkill.skill_equip_c2s.getDefaultInstance()) return this;
        if (posInfoBuilder_ == null) {
          if (!other.posInfo_.isEmpty()) {
            if (posInfo_.isEmpty()) {
              posInfo_ = other.posInfo_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensurePosInfoIsMutable();
              posInfo_.addAll(other.posInfo_);
            }
            onChanged();
          }
        } else {
          if (!other.posInfo_.isEmpty()) {
            if (posInfoBuilder_.isEmpty()) {
              posInfoBuilder_.dispose();
              posInfoBuilder_ = null;
              posInfo_ = other.posInfo_;
              bitField0_ = (bitField0_ & ~0x00000001);
              posInfoBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getPosInfoFieldBuilder() : null;
            } else {
              posInfoBuilder_.addAllMessages(other.posInfo_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                org.gof.demo.worldsrv.msg.Define.p_key_value m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_key_value.parser(),
                        extensionRegistry);
                if (posInfoBuilder_ == null) {
                  ensurePosInfoIsMutable();
                  posInfo_.add(m);
                } else {
                  posInfoBuilder_.addMessage(m);
                }
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> posInfo_ =
        java.util.Collections.emptyList();
      private void ensurePosInfoIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          posInfo_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_key_value>(posInfo_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_key_value, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder, org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> posInfoBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> getPosInfoList() {
        if (posInfoBuilder_ == null) {
          return java.util.Collections.unmodifiableList(posInfo_);
        } else {
          return posInfoBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 1;</code>
       */
      public int getPosInfoCount() {
        if (posInfoBuilder_ == null) {
          return posInfo_.size();
        } else {
          return posInfoBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value getPosInfo(int index) {
        if (posInfoBuilder_ == null) {
          return posInfo_.get(index);
        } else {
          return posInfoBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 1;</code>
       */
      public Builder setPosInfo(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value value) {
        if (posInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePosInfoIsMutable();
          posInfo_.set(index, value);
          onChanged();
        } else {
          posInfoBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 1;</code>
       */
      public Builder setPosInfo(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder builderForValue) {
        if (posInfoBuilder_ == null) {
          ensurePosInfoIsMutable();
          posInfo_.set(index, builderForValue.build());
          onChanged();
        } else {
          posInfoBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 1;</code>
       */
      public Builder addPosInfo(org.gof.demo.worldsrv.msg.Define.p_key_value value) {
        if (posInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePosInfoIsMutable();
          posInfo_.add(value);
          onChanged();
        } else {
          posInfoBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 1;</code>
       */
      public Builder addPosInfo(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value value) {
        if (posInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePosInfoIsMutable();
          posInfo_.add(index, value);
          onChanged();
        } else {
          posInfoBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 1;</code>
       */
      public Builder addPosInfo(
          org.gof.demo.worldsrv.msg.Define.p_key_value.Builder builderForValue) {
        if (posInfoBuilder_ == null) {
          ensurePosInfoIsMutable();
          posInfo_.add(builderForValue.build());
          onChanged();
        } else {
          posInfoBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 1;</code>
       */
      public Builder addPosInfo(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder builderForValue) {
        if (posInfoBuilder_ == null) {
          ensurePosInfoIsMutable();
          posInfo_.add(index, builderForValue.build());
          onChanged();
        } else {
          posInfoBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 1;</code>
       */
      public Builder addAllPosInfo(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_key_value> values) {
        if (posInfoBuilder_ == null) {
          ensurePosInfoIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, posInfo_);
          onChanged();
        } else {
          posInfoBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 1;</code>
       */
      public Builder clearPosInfo() {
        if (posInfoBuilder_ == null) {
          posInfo_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          posInfoBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 1;</code>
       */
      public Builder removePosInfo(int index) {
        if (posInfoBuilder_ == null) {
          ensurePosInfoIsMutable();
          posInfo_.remove(index);
          onChanged();
        } else {
          posInfoBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value.Builder getPosInfoBuilder(
          int index) {
        return getPosInfoFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder getPosInfoOrBuilder(
          int index) {
        if (posInfoBuilder_ == null) {
          return posInfo_.get(index);  } else {
          return posInfoBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 1;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
           getPosInfoOrBuilderList() {
        if (posInfoBuilder_ != null) {
          return posInfoBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(posInfo_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value.Builder addPosInfoBuilder() {
        return getPosInfoFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_key_value.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value.Builder addPosInfoBuilder(
          int index) {
        return getPosInfoFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_key_value.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value.Builder> 
           getPosInfoBuilderList() {
        return getPosInfoFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_key_value, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder, org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
          getPosInfoFieldBuilder() {
        if (posInfoBuilder_ == null) {
          posInfoBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_key_value, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder, org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder>(
                  posInfo_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          posInfo_ = null;
        }
        return posInfoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.skill_equip_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.skill_equip_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgSkill.skill_equip_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgSkill.skill_equip_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_equip_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<skill_equip_c2s>
        PARSER = new com.google.protobuf.AbstractParser<skill_equip_c2s>() {
      @java.lang.Override
      public skill_equip_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<skill_equip_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<skill_equip_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgSkill.skill_equip_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface skill_tab_info_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.skill_tab_info_c2s)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.skill_tab_info_c2s}
   */
  public static final class skill_tab_info_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.skill_tab_info_c2s)
      skill_tab_info_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use skill_tab_info_c2s.newBuilder() to construct.
    private skill_tab_info_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private skill_tab_info_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new skill_tab_info_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_tab_info_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_tab_info_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_c2s.class, org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_c2s.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_c2s other = (org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_c2s) obj;

      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.skill_tab_info_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.skill_tab_info_c2s)
        org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_tab_info_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_tab_info_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_c2s.class, org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_tab_info_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_c2s build() {
        org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_c2s result = new org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_c2s(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_c2s.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.skill_tab_info_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.skill_tab_info_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<skill_tab_info_c2s>
        PARSER = new com.google.protobuf.AbstractParser<skill_tab_info_c2s>() {
      @java.lang.Override
      public skill_tab_info_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<skill_tab_info_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<skill_tab_info_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface skill_tab_info_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.skill_tab_info_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 tab = 1;</code>
     * @return The tab.
     */
    int getTab();

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_tab_info tab_list = 2;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_skill_tab_info> 
        getTabListList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_tab_info tab_list = 2;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_skill_tab_info getTabList(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_tab_info tab_list = 2;</code>
     */
    int getTabListCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_tab_info tab_list = 2;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_skill_tab_infoOrBuilder> 
        getTabListOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_tab_info tab_list = 2;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_skill_tab_infoOrBuilder getTabListOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.skill_tab_info_s2c}
   */
  public static final class skill_tab_info_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.skill_tab_info_s2c)
      skill_tab_info_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use skill_tab_info_s2c.newBuilder() to construct.
    private skill_tab_info_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private skill_tab_info_s2c() {
      tabList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new skill_tab_info_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_tab_info_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_tab_info_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_s2c.class, org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_s2c.Builder.class);
    }

    public static final int TAB_FIELD_NUMBER = 1;
    private int tab_ = 0;
    /**
     * <code>uint32 tab = 1;</code>
     * @return The tab.
     */
    @java.lang.Override
    public int getTab() {
      return tab_;
    }

    public static final int TAB_LIST_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_skill_tab_info> tabList_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_tab_info tab_list = 2;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_skill_tab_info> getTabListList() {
      return tabList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_tab_info tab_list = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_skill_tab_infoOrBuilder> 
        getTabListOrBuilderList() {
      return tabList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_tab_info tab_list = 2;</code>
     */
    @java.lang.Override
    public int getTabListCount() {
      return tabList_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_tab_info tab_list = 2;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_skill_tab_info getTabList(int index) {
      return tabList_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_tab_info tab_list = 2;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_skill_tab_infoOrBuilder getTabListOrBuilder(
        int index) {
      return tabList_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (tab_ != 0) {
        output.writeUInt32(1, tab_);
      }
      for (int i = 0; i < tabList_.size(); i++) {
        output.writeMessage(2, tabList_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (tab_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, tab_);
      }
      for (int i = 0; i < tabList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, tabList_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_s2c other = (org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_s2c) obj;

      if (getTab()
          != other.getTab()) return false;
      if (!getTabListList()
          .equals(other.getTabListList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TAB_FIELD_NUMBER;
      hash = (53 * hash) + getTab();
      if (getTabListCount() > 0) {
        hash = (37 * hash) + TAB_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getTabListList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.skill_tab_info_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.skill_tab_info_s2c)
        org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_tab_info_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_tab_info_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_s2c.class, org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        tab_ = 0;
        if (tabListBuilder_ == null) {
          tabList_ = java.util.Collections.emptyList();
        } else {
          tabList_ = null;
          tabListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_tab_info_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_s2c build() {
        org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_s2c result = new org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_s2c(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_s2c result) {
        if (tabListBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0)) {
            tabList_ = java.util.Collections.unmodifiableList(tabList_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.tabList_ = tabList_;
        } else {
          result.tabList_ = tabListBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.tab_ = tab_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_s2c.getDefaultInstance()) return this;
        if (other.getTab() != 0) {
          setTab(other.getTab());
        }
        if (tabListBuilder_ == null) {
          if (!other.tabList_.isEmpty()) {
            if (tabList_.isEmpty()) {
              tabList_ = other.tabList_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensureTabListIsMutable();
              tabList_.addAll(other.tabList_);
            }
            onChanged();
          }
        } else {
          if (!other.tabList_.isEmpty()) {
            if (tabListBuilder_.isEmpty()) {
              tabListBuilder_.dispose();
              tabListBuilder_ = null;
              tabList_ = other.tabList_;
              bitField0_ = (bitField0_ & ~0x00000002);
              tabListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getTabListFieldBuilder() : null;
            } else {
              tabListBuilder_.addAllMessages(other.tabList_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                tab_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 18: {
                org.gof.demo.worldsrv.msg.Define.p_skill_tab_info m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_skill_tab_info.parser(),
                        extensionRegistry);
                if (tabListBuilder_ == null) {
                  ensureTabListIsMutable();
                  tabList_.add(m);
                } else {
                  tabListBuilder_.addMessage(m);
                }
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int tab_ ;
      /**
       * <code>uint32 tab = 1;</code>
       * @return The tab.
       */
      @java.lang.Override
      public int getTab() {
        return tab_;
      }
      /**
       * <code>uint32 tab = 1;</code>
       * @param value The tab to set.
       * @return This builder for chaining.
       */
      public Builder setTab(int value) {

        tab_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 tab = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTab() {
        bitField0_ = (bitField0_ & ~0x00000001);
        tab_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_skill_tab_info> tabList_ =
        java.util.Collections.emptyList();
      private void ensureTabListIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          tabList_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_skill_tab_info>(tabList_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_skill_tab_info, org.gof.demo.worldsrv.msg.Define.p_skill_tab_info.Builder, org.gof.demo.worldsrv.msg.Define.p_skill_tab_infoOrBuilder> tabListBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_tab_info tab_list = 2;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_skill_tab_info> getTabListList() {
        if (tabListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(tabList_);
        } else {
          return tabListBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_tab_info tab_list = 2;</code>
       */
      public int getTabListCount() {
        if (tabListBuilder_ == null) {
          return tabList_.size();
        } else {
          return tabListBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_tab_info tab_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_skill_tab_info getTabList(int index) {
        if (tabListBuilder_ == null) {
          return tabList_.get(index);
        } else {
          return tabListBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_tab_info tab_list = 2;</code>
       */
      public Builder setTabList(
          int index, org.gof.demo.worldsrv.msg.Define.p_skill_tab_info value) {
        if (tabListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTabListIsMutable();
          tabList_.set(index, value);
          onChanged();
        } else {
          tabListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_tab_info tab_list = 2;</code>
       */
      public Builder setTabList(
          int index, org.gof.demo.worldsrv.msg.Define.p_skill_tab_info.Builder builderForValue) {
        if (tabListBuilder_ == null) {
          ensureTabListIsMutable();
          tabList_.set(index, builderForValue.build());
          onChanged();
        } else {
          tabListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_tab_info tab_list = 2;</code>
       */
      public Builder addTabList(org.gof.demo.worldsrv.msg.Define.p_skill_tab_info value) {
        if (tabListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTabListIsMutable();
          tabList_.add(value);
          onChanged();
        } else {
          tabListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_tab_info tab_list = 2;</code>
       */
      public Builder addTabList(
          int index, org.gof.demo.worldsrv.msg.Define.p_skill_tab_info value) {
        if (tabListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTabListIsMutable();
          tabList_.add(index, value);
          onChanged();
        } else {
          tabListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_tab_info tab_list = 2;</code>
       */
      public Builder addTabList(
          org.gof.demo.worldsrv.msg.Define.p_skill_tab_info.Builder builderForValue) {
        if (tabListBuilder_ == null) {
          ensureTabListIsMutable();
          tabList_.add(builderForValue.build());
          onChanged();
        } else {
          tabListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_tab_info tab_list = 2;</code>
       */
      public Builder addTabList(
          int index, org.gof.demo.worldsrv.msg.Define.p_skill_tab_info.Builder builderForValue) {
        if (tabListBuilder_ == null) {
          ensureTabListIsMutable();
          tabList_.add(index, builderForValue.build());
          onChanged();
        } else {
          tabListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_tab_info tab_list = 2;</code>
       */
      public Builder addAllTabList(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_skill_tab_info> values) {
        if (tabListBuilder_ == null) {
          ensureTabListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, tabList_);
          onChanged();
        } else {
          tabListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_tab_info tab_list = 2;</code>
       */
      public Builder clearTabList() {
        if (tabListBuilder_ == null) {
          tabList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          tabListBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_tab_info tab_list = 2;</code>
       */
      public Builder removeTabList(int index) {
        if (tabListBuilder_ == null) {
          ensureTabListIsMutable();
          tabList_.remove(index);
          onChanged();
        } else {
          tabListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_tab_info tab_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_skill_tab_info.Builder getTabListBuilder(
          int index) {
        return getTabListFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_tab_info tab_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_skill_tab_infoOrBuilder getTabListOrBuilder(
          int index) {
        if (tabListBuilder_ == null) {
          return tabList_.get(index);  } else {
          return tabListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_tab_info tab_list = 2;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_skill_tab_infoOrBuilder> 
           getTabListOrBuilderList() {
        if (tabListBuilder_ != null) {
          return tabListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(tabList_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_tab_info tab_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_skill_tab_info.Builder addTabListBuilder() {
        return getTabListFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_skill_tab_info.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_tab_info tab_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_skill_tab_info.Builder addTabListBuilder(
          int index) {
        return getTabListFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_skill_tab_info.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_skill_tab_info tab_list = 2;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_skill_tab_info.Builder> 
           getTabListBuilderList() {
        return getTabListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_skill_tab_info, org.gof.demo.worldsrv.msg.Define.p_skill_tab_info.Builder, org.gof.demo.worldsrv.msg.Define.p_skill_tab_infoOrBuilder> 
          getTabListFieldBuilder() {
        if (tabListBuilder_ == null) {
          tabListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_skill_tab_info, org.gof.demo.worldsrv.msg.Define.p_skill_tab_info.Builder, org.gof.demo.worldsrv.msg.Define.p_skill_tab_infoOrBuilder>(
                  tabList_,
                  ((bitField0_ & 0x00000002) != 0),
                  getParentForChildren(),
                  isClean());
          tabList_ = null;
        }
        return tabListBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.skill_tab_info_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.skill_tab_info_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<skill_tab_info_s2c>
        PARSER = new com.google.protobuf.AbstractParser<skill_tab_info_s2c>() {
      @java.lang.Override
      public skill_tab_info_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<skill_tab_info_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<skill_tab_info_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface skill_tab_info_update_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.skill_tab_info_update_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 tab = 1;</code>
     * @return The tab.
     */
    int getTab();

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> 
        getPosInfoList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_key_value getPosInfo(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
     */
    int getPosInfoCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
        getPosInfoOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder getPosInfoOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.skill_tab_info_update_s2c}
   */
  public static final class skill_tab_info_update_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.skill_tab_info_update_s2c)
      skill_tab_info_update_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use skill_tab_info_update_s2c.newBuilder() to construct.
    private skill_tab_info_update_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private skill_tab_info_update_s2c() {
      posInfo_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new skill_tab_info_update_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_tab_info_update_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_tab_info_update_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_update_s2c.class, org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_update_s2c.Builder.class);
    }

    public static final int TAB_FIELD_NUMBER = 1;
    private int tab_ = 0;
    /**
     * <code>uint32 tab = 1;</code>
     * @return The tab.
     */
    @java.lang.Override
    public int getTab() {
      return tab_;
    }

    public static final int POS_INFO_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> posInfo_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> getPosInfoList() {
      return posInfo_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
        getPosInfoOrBuilderList() {
      return posInfo_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
     */
    @java.lang.Override
    public int getPosInfoCount() {
      return posInfo_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_key_value getPosInfo(int index) {
      return posInfo_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder getPosInfoOrBuilder(
        int index) {
      return posInfo_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (tab_ != 0) {
        output.writeUInt32(1, tab_);
      }
      for (int i = 0; i < posInfo_.size(); i++) {
        output.writeMessage(2, posInfo_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (tab_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, tab_);
      }
      for (int i = 0; i < posInfo_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, posInfo_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_update_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_update_s2c other = (org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_update_s2c) obj;

      if (getTab()
          != other.getTab()) return false;
      if (!getPosInfoList()
          .equals(other.getPosInfoList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TAB_FIELD_NUMBER;
      hash = (53 * hash) + getTab();
      if (getPosInfoCount() > 0) {
        hash = (37 * hash) + POS_INFO_FIELD_NUMBER;
        hash = (53 * hash) + getPosInfoList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_update_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_update_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_update_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_update_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_update_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_update_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_update_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_update_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_update_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_update_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_update_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_update_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_update_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.skill_tab_info_update_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.skill_tab_info_update_s2c)
        org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_update_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_tab_info_update_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_tab_info_update_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_update_s2c.class, org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_update_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_update_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        tab_ = 0;
        if (posInfoBuilder_ == null) {
          posInfo_ = java.util.Collections.emptyList();
        } else {
          posInfo_ = null;
          posInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_tab_info_update_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_update_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_update_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_update_s2c build() {
        org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_update_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_update_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_update_s2c result = new org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_update_s2c(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_update_s2c result) {
        if (posInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0)) {
            posInfo_ = java.util.Collections.unmodifiableList(posInfo_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.posInfo_ = posInfo_;
        } else {
          result.posInfo_ = posInfoBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_update_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.tab_ = tab_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_update_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_update_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_update_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_update_s2c.getDefaultInstance()) return this;
        if (other.getTab() != 0) {
          setTab(other.getTab());
        }
        if (posInfoBuilder_ == null) {
          if (!other.posInfo_.isEmpty()) {
            if (posInfo_.isEmpty()) {
              posInfo_ = other.posInfo_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensurePosInfoIsMutable();
              posInfo_.addAll(other.posInfo_);
            }
            onChanged();
          }
        } else {
          if (!other.posInfo_.isEmpty()) {
            if (posInfoBuilder_.isEmpty()) {
              posInfoBuilder_.dispose();
              posInfoBuilder_ = null;
              posInfo_ = other.posInfo_;
              bitField0_ = (bitField0_ & ~0x00000002);
              posInfoBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getPosInfoFieldBuilder() : null;
            } else {
              posInfoBuilder_.addAllMessages(other.posInfo_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                tab_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 18: {
                org.gof.demo.worldsrv.msg.Define.p_key_value m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_key_value.parser(),
                        extensionRegistry);
                if (posInfoBuilder_ == null) {
                  ensurePosInfoIsMutable();
                  posInfo_.add(m);
                } else {
                  posInfoBuilder_.addMessage(m);
                }
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int tab_ ;
      /**
       * <code>uint32 tab = 1;</code>
       * @return The tab.
       */
      @java.lang.Override
      public int getTab() {
        return tab_;
      }
      /**
       * <code>uint32 tab = 1;</code>
       * @param value The tab to set.
       * @return This builder for chaining.
       */
      public Builder setTab(int value) {

        tab_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 tab = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTab() {
        bitField0_ = (bitField0_ & ~0x00000001);
        tab_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> posInfo_ =
        java.util.Collections.emptyList();
      private void ensurePosInfoIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          posInfo_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_key_value>(posInfo_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_key_value, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder, org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> posInfoBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> getPosInfoList() {
        if (posInfoBuilder_ == null) {
          return java.util.Collections.unmodifiableList(posInfo_);
        } else {
          return posInfoBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public int getPosInfoCount() {
        if (posInfoBuilder_ == null) {
          return posInfo_.size();
        } else {
          return posInfoBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value getPosInfo(int index) {
        if (posInfoBuilder_ == null) {
          return posInfo_.get(index);
        } else {
          return posInfoBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public Builder setPosInfo(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value value) {
        if (posInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePosInfoIsMutable();
          posInfo_.set(index, value);
          onChanged();
        } else {
          posInfoBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public Builder setPosInfo(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder builderForValue) {
        if (posInfoBuilder_ == null) {
          ensurePosInfoIsMutable();
          posInfo_.set(index, builderForValue.build());
          onChanged();
        } else {
          posInfoBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public Builder addPosInfo(org.gof.demo.worldsrv.msg.Define.p_key_value value) {
        if (posInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePosInfoIsMutable();
          posInfo_.add(value);
          onChanged();
        } else {
          posInfoBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public Builder addPosInfo(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value value) {
        if (posInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePosInfoIsMutable();
          posInfo_.add(index, value);
          onChanged();
        } else {
          posInfoBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public Builder addPosInfo(
          org.gof.demo.worldsrv.msg.Define.p_key_value.Builder builderForValue) {
        if (posInfoBuilder_ == null) {
          ensurePosInfoIsMutable();
          posInfo_.add(builderForValue.build());
          onChanged();
        } else {
          posInfoBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public Builder addPosInfo(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder builderForValue) {
        if (posInfoBuilder_ == null) {
          ensurePosInfoIsMutable();
          posInfo_.add(index, builderForValue.build());
          onChanged();
        } else {
          posInfoBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public Builder addAllPosInfo(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_key_value> values) {
        if (posInfoBuilder_ == null) {
          ensurePosInfoIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, posInfo_);
          onChanged();
        } else {
          posInfoBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public Builder clearPosInfo() {
        if (posInfoBuilder_ == null) {
          posInfo_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          posInfoBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public Builder removePosInfo(int index) {
        if (posInfoBuilder_ == null) {
          ensurePosInfoIsMutable();
          posInfo_.remove(index);
          onChanged();
        } else {
          posInfoBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value.Builder getPosInfoBuilder(
          int index) {
        return getPosInfoFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder getPosInfoOrBuilder(
          int index) {
        if (posInfoBuilder_ == null) {
          return posInfo_.get(index);  } else {
          return posInfoBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
           getPosInfoOrBuilderList() {
        if (posInfoBuilder_ != null) {
          return posInfoBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(posInfo_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value.Builder addPosInfoBuilder() {
        return getPosInfoFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_key_value.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value.Builder addPosInfoBuilder(
          int index) {
        return getPosInfoFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_key_value.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value.Builder> 
           getPosInfoBuilderList() {
        return getPosInfoFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_key_value, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder, org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
          getPosInfoFieldBuilder() {
        if (posInfoBuilder_ == null) {
          posInfoBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_key_value, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder, org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder>(
                  posInfo_,
                  ((bitField0_ & 0x00000002) != 0),
                  getParentForChildren(),
                  isClean());
          posInfo_ = null;
        }
        return posInfoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.skill_tab_info_update_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.skill_tab_info_update_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_update_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_update_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_update_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<skill_tab_info_update_s2c>
        PARSER = new com.google.protobuf.AbstractParser<skill_tab_info_update_s2c>() {
      @java.lang.Override
      public skill_tab_info_update_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<skill_tab_info_update_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<skill_tab_info_update_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgSkill.skill_tab_info_update_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface skill_choose_tab_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.skill_choose_tab_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 tab = 1;</code>
     * @return The tab.
     */
    int getTab();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.skill_choose_tab_c2s}
   */
  public static final class skill_choose_tab_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.skill_choose_tab_c2s)
      skill_choose_tab_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use skill_choose_tab_c2s.newBuilder() to construct.
    private skill_choose_tab_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private skill_choose_tab_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new skill_choose_tab_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_choose_tab_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_choose_tab_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_c2s.class, org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_c2s.Builder.class);
    }

    public static final int TAB_FIELD_NUMBER = 1;
    private int tab_ = 0;
    /**
     * <code>uint32 tab = 1;</code>
     * @return The tab.
     */
    @java.lang.Override
    public int getTab() {
      return tab_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (tab_ != 0) {
        output.writeUInt32(1, tab_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (tab_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, tab_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_c2s other = (org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_c2s) obj;

      if (getTab()
          != other.getTab()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TAB_FIELD_NUMBER;
      hash = (53 * hash) + getTab();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.skill_choose_tab_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.skill_choose_tab_c2s)
        org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_choose_tab_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_choose_tab_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_c2s.class, org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        tab_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_choose_tab_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_c2s build() {
        org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_c2s result = new org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.tab_ = tab_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_c2s.getDefaultInstance()) return this;
        if (other.getTab() != 0) {
          setTab(other.getTab());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                tab_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int tab_ ;
      /**
       * <code>uint32 tab = 1;</code>
       * @return The tab.
       */
      @java.lang.Override
      public int getTab() {
        return tab_;
      }
      /**
       * <code>uint32 tab = 1;</code>
       * @param value The tab to set.
       * @return This builder for chaining.
       */
      public Builder setTab(int value) {

        tab_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 tab = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTab() {
        bitField0_ = (bitField0_ & ~0x00000001);
        tab_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.skill_choose_tab_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.skill_choose_tab_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<skill_choose_tab_c2s>
        PARSER = new com.google.protobuf.AbstractParser<skill_choose_tab_c2s>() {
      @java.lang.Override
      public skill_choose_tab_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<skill_choose_tab_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<skill_choose_tab_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface skill_choose_tab_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.skill_choose_tab_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 new_tab = 1;</code>
     * @return The newTab.
     */
    int getNewTab();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.skill_choose_tab_s2c}
   */
  public static final class skill_choose_tab_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.skill_choose_tab_s2c)
      skill_choose_tab_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use skill_choose_tab_s2c.newBuilder() to construct.
    private skill_choose_tab_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private skill_choose_tab_s2c() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new skill_choose_tab_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_choose_tab_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_choose_tab_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_s2c.class, org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_s2c.Builder.class);
    }

    public static final int NEW_TAB_FIELD_NUMBER = 1;
    private int newTab_ = 0;
    /**
     * <code>uint32 new_tab = 1;</code>
     * @return The newTab.
     */
    @java.lang.Override
    public int getNewTab() {
      return newTab_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (newTab_ != 0) {
        output.writeUInt32(1, newTab_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (newTab_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, newTab_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_s2c other = (org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_s2c) obj;

      if (getNewTab()
          != other.getNewTab()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + NEW_TAB_FIELD_NUMBER;
      hash = (53 * hash) + getNewTab();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.skill_choose_tab_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.skill_choose_tab_s2c)
        org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_choose_tab_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_choose_tab_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_s2c.class, org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        newTab_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_choose_tab_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_s2c build() {
        org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_s2c result = new org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.newTab_ = newTab_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_s2c.getDefaultInstance()) return this;
        if (other.getNewTab() != 0) {
          setNewTab(other.getNewTab());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                newTab_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int newTab_ ;
      /**
       * <code>uint32 new_tab = 1;</code>
       * @return The newTab.
       */
      @java.lang.Override
      public int getNewTab() {
        return newTab_;
      }
      /**
       * <code>uint32 new_tab = 1;</code>
       * @param value The newTab to set.
       * @return This builder for chaining.
       */
      public Builder setNewTab(int value) {

        newTab_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 new_tab = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearNewTab() {
        bitField0_ = (bitField0_ & ~0x00000001);
        newTab_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.skill_choose_tab_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.skill_choose_tab_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<skill_choose_tab_s2c>
        PARSER = new com.google.protobuf.AbstractParser<skill_choose_tab_s2c>() {
      @java.lang.Override
      public skill_choose_tab_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<skill_choose_tab_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<skill_choose_tab_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgSkill.skill_choose_tab_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface skill_change_tab_name_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.skill_change_tab_name_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 tab = 1;</code>
     * @return The tab.
     */
    int getTab();

    /**
     * <code>string name = 2;</code>
     * @return The name.
     */
    java.lang.String getName();
    /**
     * <code>string name = 2;</code>
     * @return The bytes for name.
     */
    com.google.protobuf.ByteString
        getNameBytes();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.skill_change_tab_name_c2s}
   */
  public static final class skill_change_tab_name_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.skill_change_tab_name_c2s)
      skill_change_tab_name_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use skill_change_tab_name_c2s.newBuilder() to construct.
    private skill_change_tab_name_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private skill_change_tab_name_c2s() {
      name_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new skill_change_tab_name_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_change_tab_name_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_change_tab_name_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_c2s.class, org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_c2s.Builder.class);
    }

    public static final int TAB_FIELD_NUMBER = 1;
    private int tab_ = 0;
    /**
     * <code>uint32 tab = 1;</code>
     * @return The tab.
     */
    @java.lang.Override
    public int getTab() {
      return tab_;
    }

    public static final int NAME_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private volatile java.lang.Object name_ = "";
    /**
     * <code>string name = 2;</code>
     * @return The name.
     */
    @java.lang.Override
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        name_ = s;
        return s;
      }
    }
    /**
     * <code>string name = 2;</code>
     * @return The bytes for name.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (tab_ != 0) {
        output.writeUInt32(1, tab_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(name_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, name_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (tab_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, tab_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(name_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, name_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_c2s other = (org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_c2s) obj;

      if (getTab()
          != other.getTab()) return false;
      if (!getName()
          .equals(other.getName())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TAB_FIELD_NUMBER;
      hash = (53 * hash) + getTab();
      hash = (37 * hash) + NAME_FIELD_NUMBER;
      hash = (53 * hash) + getName().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.skill_change_tab_name_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.skill_change_tab_name_c2s)
        org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_change_tab_name_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_change_tab_name_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_c2s.class, org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        tab_ = 0;
        name_ = "";
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_change_tab_name_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_c2s build() {
        org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_c2s result = new org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.tab_ = tab_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.name_ = name_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_c2s.getDefaultInstance()) return this;
        if (other.getTab() != 0) {
          setTab(other.getTab());
        }
        if (!other.getName().isEmpty()) {
          name_ = other.name_;
          bitField0_ |= 0x00000002;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                tab_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 18: {
                name_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int tab_ ;
      /**
       * <code>uint32 tab = 1;</code>
       * @return The tab.
       */
      @java.lang.Override
      public int getTab() {
        return tab_;
      }
      /**
       * <code>uint32 tab = 1;</code>
       * @param value The tab to set.
       * @return This builder for chaining.
       */
      public Builder setTab(int value) {

        tab_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 tab = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTab() {
        bitField0_ = (bitField0_ & ~0x00000001);
        tab_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object name_ = "";
      /**
       * <code>string name = 2;</code>
       * @return The name.
       */
      public java.lang.String getName() {
        java.lang.Object ref = name_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          name_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string name = 2;</code>
       * @return The bytes for name.
       */
      public com.google.protobuf.ByteString
          getNameBytes() {
        java.lang.Object ref = name_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          name_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string name = 2;</code>
       * @param value The name to set.
       * @return This builder for chaining.
       */
      public Builder setName(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        name_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>string name = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearName() {
        name_ = getDefaultInstance().getName();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }
      /**
       * <code>string name = 2;</code>
       * @param value The bytes for name to set.
       * @return This builder for chaining.
       */
      public Builder setNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        name_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.skill_change_tab_name_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.skill_change_tab_name_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<skill_change_tab_name_c2s>
        PARSER = new com.google.protobuf.AbstractParser<skill_change_tab_name_c2s>() {
      @java.lang.Override
      public skill_change_tab_name_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<skill_change_tab_name_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<skill_change_tab_name_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface skill_change_tab_name_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.skill_change_tab_name_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 tab = 1;</code>
     * @return The tab.
     */
    int getTab();

    /**
     * <code>string name = 2;</code>
     * @return The name.
     */
    java.lang.String getName();
    /**
     * <code>string name = 2;</code>
     * @return The bytes for name.
     */
    com.google.protobuf.ByteString
        getNameBytes();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.skill_change_tab_name_s2c}
   */
  public static final class skill_change_tab_name_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.skill_change_tab_name_s2c)
      skill_change_tab_name_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use skill_change_tab_name_s2c.newBuilder() to construct.
    private skill_change_tab_name_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private skill_change_tab_name_s2c() {
      name_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new skill_change_tab_name_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_change_tab_name_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_change_tab_name_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_s2c.class, org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_s2c.Builder.class);
    }

    public static final int TAB_FIELD_NUMBER = 1;
    private int tab_ = 0;
    /**
     * <code>uint32 tab = 1;</code>
     * @return The tab.
     */
    @java.lang.Override
    public int getTab() {
      return tab_;
    }

    public static final int NAME_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private volatile java.lang.Object name_ = "";
    /**
     * <code>string name = 2;</code>
     * @return The name.
     */
    @java.lang.Override
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        name_ = s;
        return s;
      }
    }
    /**
     * <code>string name = 2;</code>
     * @return The bytes for name.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (tab_ != 0) {
        output.writeUInt32(1, tab_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(name_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, name_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (tab_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, tab_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(name_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, name_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_s2c other = (org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_s2c) obj;

      if (getTab()
          != other.getTab()) return false;
      if (!getName()
          .equals(other.getName())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TAB_FIELD_NUMBER;
      hash = (53 * hash) + getTab();
      hash = (37 * hash) + NAME_FIELD_NUMBER;
      hash = (53 * hash) + getName().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.skill_change_tab_name_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.skill_change_tab_name_s2c)
        org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_change_tab_name_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_change_tab_name_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_s2c.class, org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        tab_ = 0;
        name_ = "";
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_change_tab_name_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_s2c build() {
        org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_s2c result = new org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.tab_ = tab_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.name_ = name_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_s2c.getDefaultInstance()) return this;
        if (other.getTab() != 0) {
          setTab(other.getTab());
        }
        if (!other.getName().isEmpty()) {
          name_ = other.name_;
          bitField0_ |= 0x00000002;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                tab_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 18: {
                name_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int tab_ ;
      /**
       * <code>uint32 tab = 1;</code>
       * @return The tab.
       */
      @java.lang.Override
      public int getTab() {
        return tab_;
      }
      /**
       * <code>uint32 tab = 1;</code>
       * @param value The tab to set.
       * @return This builder for chaining.
       */
      public Builder setTab(int value) {

        tab_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 tab = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTab() {
        bitField0_ = (bitField0_ & ~0x00000001);
        tab_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object name_ = "";
      /**
       * <code>string name = 2;</code>
       * @return The name.
       */
      public java.lang.String getName() {
        java.lang.Object ref = name_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          name_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string name = 2;</code>
       * @return The bytes for name.
       */
      public com.google.protobuf.ByteString
          getNameBytes() {
        java.lang.Object ref = name_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          name_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string name = 2;</code>
       * @param value The name to set.
       * @return This builder for chaining.
       */
      public Builder setName(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        name_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>string name = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearName() {
        name_ = getDefaultInstance().getName();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }
      /**
       * <code>string name = 2;</code>
       * @param value The bytes for name to set.
       * @return This builder for chaining.
       */
      public Builder setNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        name_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.skill_change_tab_name_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.skill_change_tab_name_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<skill_change_tab_name_s2c>
        PARSER = new com.google.protobuf.AbstractParser<skill_change_tab_name_s2c>() {
      @java.lang.Override
      public skill_change_tab_name_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<skill_change_tab_name_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<skill_change_tab_name_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgSkill.skill_change_tab_name_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface skill_set_delay_time_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.skill_set_delay_time_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 tab = 1;</code>
     * @return The tab.
     */
    int getTab();

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value set_delay_time = 2;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> 
        getSetDelayTimeList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value set_delay_time = 2;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_key_value getSetDelayTime(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value set_delay_time = 2;</code>
     */
    int getSetDelayTimeCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value set_delay_time = 2;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
        getSetDelayTimeOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value set_delay_time = 2;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder getSetDelayTimeOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.skill_set_delay_time_c2s}
   */
  public static final class skill_set_delay_time_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.skill_set_delay_time_c2s)
      skill_set_delay_time_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use skill_set_delay_time_c2s.newBuilder() to construct.
    private skill_set_delay_time_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private skill_set_delay_time_c2s() {
      setDelayTime_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new skill_set_delay_time_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_set_delay_time_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_set_delay_time_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_c2s.class, org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_c2s.Builder.class);
    }

    public static final int TAB_FIELD_NUMBER = 1;
    private int tab_ = 0;
    /**
     * <code>uint32 tab = 1;</code>
     * @return The tab.
     */
    @java.lang.Override
    public int getTab() {
      return tab_;
    }

    public static final int SET_DELAY_TIME_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> setDelayTime_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value set_delay_time = 2;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> getSetDelayTimeList() {
      return setDelayTime_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value set_delay_time = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
        getSetDelayTimeOrBuilderList() {
      return setDelayTime_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value set_delay_time = 2;</code>
     */
    @java.lang.Override
    public int getSetDelayTimeCount() {
      return setDelayTime_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value set_delay_time = 2;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_key_value getSetDelayTime(int index) {
      return setDelayTime_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value set_delay_time = 2;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder getSetDelayTimeOrBuilder(
        int index) {
      return setDelayTime_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (tab_ != 0) {
        output.writeUInt32(1, tab_);
      }
      for (int i = 0; i < setDelayTime_.size(); i++) {
        output.writeMessage(2, setDelayTime_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (tab_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, tab_);
      }
      for (int i = 0; i < setDelayTime_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, setDelayTime_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_c2s other = (org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_c2s) obj;

      if (getTab()
          != other.getTab()) return false;
      if (!getSetDelayTimeList()
          .equals(other.getSetDelayTimeList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TAB_FIELD_NUMBER;
      hash = (53 * hash) + getTab();
      if (getSetDelayTimeCount() > 0) {
        hash = (37 * hash) + SET_DELAY_TIME_FIELD_NUMBER;
        hash = (53 * hash) + getSetDelayTimeList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.skill_set_delay_time_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.skill_set_delay_time_c2s)
        org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_set_delay_time_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_set_delay_time_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_c2s.class, org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        tab_ = 0;
        if (setDelayTimeBuilder_ == null) {
          setDelayTime_ = java.util.Collections.emptyList();
        } else {
          setDelayTime_ = null;
          setDelayTimeBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_set_delay_time_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_c2s build() {
        org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_c2s result = new org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_c2s(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_c2s result) {
        if (setDelayTimeBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0)) {
            setDelayTime_ = java.util.Collections.unmodifiableList(setDelayTime_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.setDelayTime_ = setDelayTime_;
        } else {
          result.setDelayTime_ = setDelayTimeBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.tab_ = tab_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_c2s.getDefaultInstance()) return this;
        if (other.getTab() != 0) {
          setTab(other.getTab());
        }
        if (setDelayTimeBuilder_ == null) {
          if (!other.setDelayTime_.isEmpty()) {
            if (setDelayTime_.isEmpty()) {
              setDelayTime_ = other.setDelayTime_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensureSetDelayTimeIsMutable();
              setDelayTime_.addAll(other.setDelayTime_);
            }
            onChanged();
          }
        } else {
          if (!other.setDelayTime_.isEmpty()) {
            if (setDelayTimeBuilder_.isEmpty()) {
              setDelayTimeBuilder_.dispose();
              setDelayTimeBuilder_ = null;
              setDelayTime_ = other.setDelayTime_;
              bitField0_ = (bitField0_ & ~0x00000002);
              setDelayTimeBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getSetDelayTimeFieldBuilder() : null;
            } else {
              setDelayTimeBuilder_.addAllMessages(other.setDelayTime_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                tab_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 18: {
                org.gof.demo.worldsrv.msg.Define.p_key_value m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_key_value.parser(),
                        extensionRegistry);
                if (setDelayTimeBuilder_ == null) {
                  ensureSetDelayTimeIsMutable();
                  setDelayTime_.add(m);
                } else {
                  setDelayTimeBuilder_.addMessage(m);
                }
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int tab_ ;
      /**
       * <code>uint32 tab = 1;</code>
       * @return The tab.
       */
      @java.lang.Override
      public int getTab() {
        return tab_;
      }
      /**
       * <code>uint32 tab = 1;</code>
       * @param value The tab to set.
       * @return This builder for chaining.
       */
      public Builder setTab(int value) {

        tab_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 tab = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTab() {
        bitField0_ = (bitField0_ & ~0x00000001);
        tab_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> setDelayTime_ =
        java.util.Collections.emptyList();
      private void ensureSetDelayTimeIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          setDelayTime_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_key_value>(setDelayTime_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_key_value, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder, org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> setDelayTimeBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value set_delay_time = 2;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> getSetDelayTimeList() {
        if (setDelayTimeBuilder_ == null) {
          return java.util.Collections.unmodifiableList(setDelayTime_);
        } else {
          return setDelayTimeBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value set_delay_time = 2;</code>
       */
      public int getSetDelayTimeCount() {
        if (setDelayTimeBuilder_ == null) {
          return setDelayTime_.size();
        } else {
          return setDelayTimeBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value set_delay_time = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value getSetDelayTime(int index) {
        if (setDelayTimeBuilder_ == null) {
          return setDelayTime_.get(index);
        } else {
          return setDelayTimeBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value set_delay_time = 2;</code>
       */
      public Builder setSetDelayTime(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value value) {
        if (setDelayTimeBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSetDelayTimeIsMutable();
          setDelayTime_.set(index, value);
          onChanged();
        } else {
          setDelayTimeBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value set_delay_time = 2;</code>
       */
      public Builder setSetDelayTime(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder builderForValue) {
        if (setDelayTimeBuilder_ == null) {
          ensureSetDelayTimeIsMutable();
          setDelayTime_.set(index, builderForValue.build());
          onChanged();
        } else {
          setDelayTimeBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value set_delay_time = 2;</code>
       */
      public Builder addSetDelayTime(org.gof.demo.worldsrv.msg.Define.p_key_value value) {
        if (setDelayTimeBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSetDelayTimeIsMutable();
          setDelayTime_.add(value);
          onChanged();
        } else {
          setDelayTimeBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value set_delay_time = 2;</code>
       */
      public Builder addSetDelayTime(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value value) {
        if (setDelayTimeBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSetDelayTimeIsMutable();
          setDelayTime_.add(index, value);
          onChanged();
        } else {
          setDelayTimeBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value set_delay_time = 2;</code>
       */
      public Builder addSetDelayTime(
          org.gof.demo.worldsrv.msg.Define.p_key_value.Builder builderForValue) {
        if (setDelayTimeBuilder_ == null) {
          ensureSetDelayTimeIsMutable();
          setDelayTime_.add(builderForValue.build());
          onChanged();
        } else {
          setDelayTimeBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value set_delay_time = 2;</code>
       */
      public Builder addSetDelayTime(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder builderForValue) {
        if (setDelayTimeBuilder_ == null) {
          ensureSetDelayTimeIsMutable();
          setDelayTime_.add(index, builderForValue.build());
          onChanged();
        } else {
          setDelayTimeBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value set_delay_time = 2;</code>
       */
      public Builder addAllSetDelayTime(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_key_value> values) {
        if (setDelayTimeBuilder_ == null) {
          ensureSetDelayTimeIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, setDelayTime_);
          onChanged();
        } else {
          setDelayTimeBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value set_delay_time = 2;</code>
       */
      public Builder clearSetDelayTime() {
        if (setDelayTimeBuilder_ == null) {
          setDelayTime_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          setDelayTimeBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value set_delay_time = 2;</code>
       */
      public Builder removeSetDelayTime(int index) {
        if (setDelayTimeBuilder_ == null) {
          ensureSetDelayTimeIsMutable();
          setDelayTime_.remove(index);
          onChanged();
        } else {
          setDelayTimeBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value set_delay_time = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value.Builder getSetDelayTimeBuilder(
          int index) {
        return getSetDelayTimeFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value set_delay_time = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder getSetDelayTimeOrBuilder(
          int index) {
        if (setDelayTimeBuilder_ == null) {
          return setDelayTime_.get(index);  } else {
          return setDelayTimeBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value set_delay_time = 2;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
           getSetDelayTimeOrBuilderList() {
        if (setDelayTimeBuilder_ != null) {
          return setDelayTimeBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(setDelayTime_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value set_delay_time = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value.Builder addSetDelayTimeBuilder() {
        return getSetDelayTimeFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_key_value.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value set_delay_time = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value.Builder addSetDelayTimeBuilder(
          int index) {
        return getSetDelayTimeFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_key_value.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value set_delay_time = 2;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value.Builder> 
           getSetDelayTimeBuilderList() {
        return getSetDelayTimeFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_key_value, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder, org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
          getSetDelayTimeFieldBuilder() {
        if (setDelayTimeBuilder_ == null) {
          setDelayTimeBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_key_value, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder, org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder>(
                  setDelayTime_,
                  ((bitField0_ & 0x00000002) != 0),
                  getParentForChildren(),
                  isClean());
          setDelayTime_ = null;
        }
        return setDelayTimeBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.skill_set_delay_time_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.skill_set_delay_time_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<skill_set_delay_time_c2s>
        PARSER = new com.google.protobuf.AbstractParser<skill_set_delay_time_c2s>() {
      @java.lang.Override
      public skill_set_delay_time_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<skill_set_delay_time_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<skill_set_delay_time_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface skill_set_delay_time_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.skill_set_delay_time_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 tab = 1;</code>
     * @return The tab.
     */
    int getTab();

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value delay_time_list = 2;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> 
        getDelayTimeListList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value delay_time_list = 2;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_key_value getDelayTimeList(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value delay_time_list = 2;</code>
     */
    int getDelayTimeListCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value delay_time_list = 2;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
        getDelayTimeListOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value delay_time_list = 2;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder getDelayTimeListOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.skill_set_delay_time_s2c}
   */
  public static final class skill_set_delay_time_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.skill_set_delay_time_s2c)
      skill_set_delay_time_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use skill_set_delay_time_s2c.newBuilder() to construct.
    private skill_set_delay_time_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private skill_set_delay_time_s2c() {
      delayTimeList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new skill_set_delay_time_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_set_delay_time_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_set_delay_time_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_s2c.class, org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_s2c.Builder.class);
    }

    public static final int TAB_FIELD_NUMBER = 1;
    private int tab_ = 0;
    /**
     * <code>uint32 tab = 1;</code>
     * @return The tab.
     */
    @java.lang.Override
    public int getTab() {
      return tab_;
    }

    public static final int DELAY_TIME_LIST_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> delayTimeList_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value delay_time_list = 2;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> getDelayTimeListList() {
      return delayTimeList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value delay_time_list = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
        getDelayTimeListOrBuilderList() {
      return delayTimeList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value delay_time_list = 2;</code>
     */
    @java.lang.Override
    public int getDelayTimeListCount() {
      return delayTimeList_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value delay_time_list = 2;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_key_value getDelayTimeList(int index) {
      return delayTimeList_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value delay_time_list = 2;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder getDelayTimeListOrBuilder(
        int index) {
      return delayTimeList_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (tab_ != 0) {
        output.writeUInt32(1, tab_);
      }
      for (int i = 0; i < delayTimeList_.size(); i++) {
        output.writeMessage(2, delayTimeList_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (tab_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, tab_);
      }
      for (int i = 0; i < delayTimeList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, delayTimeList_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_s2c other = (org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_s2c) obj;

      if (getTab()
          != other.getTab()) return false;
      if (!getDelayTimeListList()
          .equals(other.getDelayTimeListList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TAB_FIELD_NUMBER;
      hash = (53 * hash) + getTab();
      if (getDelayTimeListCount() > 0) {
        hash = (37 * hash) + DELAY_TIME_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getDelayTimeListList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.skill_set_delay_time_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.skill_set_delay_time_s2c)
        org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_set_delay_time_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_set_delay_time_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_s2c.class, org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        tab_ = 0;
        if (delayTimeListBuilder_ == null) {
          delayTimeList_ = java.util.Collections.emptyList();
        } else {
          delayTimeList_ = null;
          delayTimeListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgSkill.internal_static_org_gof_demo_worldsrv_msg_skill_set_delay_time_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_s2c build() {
        org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_s2c result = new org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_s2c(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_s2c result) {
        if (delayTimeListBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0)) {
            delayTimeList_ = java.util.Collections.unmodifiableList(delayTimeList_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.delayTimeList_ = delayTimeList_;
        } else {
          result.delayTimeList_ = delayTimeListBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.tab_ = tab_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_s2c.getDefaultInstance()) return this;
        if (other.getTab() != 0) {
          setTab(other.getTab());
        }
        if (delayTimeListBuilder_ == null) {
          if (!other.delayTimeList_.isEmpty()) {
            if (delayTimeList_.isEmpty()) {
              delayTimeList_ = other.delayTimeList_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensureDelayTimeListIsMutable();
              delayTimeList_.addAll(other.delayTimeList_);
            }
            onChanged();
          }
        } else {
          if (!other.delayTimeList_.isEmpty()) {
            if (delayTimeListBuilder_.isEmpty()) {
              delayTimeListBuilder_.dispose();
              delayTimeListBuilder_ = null;
              delayTimeList_ = other.delayTimeList_;
              bitField0_ = (bitField0_ & ~0x00000002);
              delayTimeListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getDelayTimeListFieldBuilder() : null;
            } else {
              delayTimeListBuilder_.addAllMessages(other.delayTimeList_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                tab_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 18: {
                org.gof.demo.worldsrv.msg.Define.p_key_value m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_key_value.parser(),
                        extensionRegistry);
                if (delayTimeListBuilder_ == null) {
                  ensureDelayTimeListIsMutable();
                  delayTimeList_.add(m);
                } else {
                  delayTimeListBuilder_.addMessage(m);
                }
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int tab_ ;
      /**
       * <code>uint32 tab = 1;</code>
       * @return The tab.
       */
      @java.lang.Override
      public int getTab() {
        return tab_;
      }
      /**
       * <code>uint32 tab = 1;</code>
       * @param value The tab to set.
       * @return This builder for chaining.
       */
      public Builder setTab(int value) {

        tab_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 tab = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTab() {
        bitField0_ = (bitField0_ & ~0x00000001);
        tab_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> delayTimeList_ =
        java.util.Collections.emptyList();
      private void ensureDelayTimeListIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          delayTimeList_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_key_value>(delayTimeList_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_key_value, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder, org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> delayTimeListBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value delay_time_list = 2;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> getDelayTimeListList() {
        if (delayTimeListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(delayTimeList_);
        } else {
          return delayTimeListBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value delay_time_list = 2;</code>
       */
      public int getDelayTimeListCount() {
        if (delayTimeListBuilder_ == null) {
          return delayTimeList_.size();
        } else {
          return delayTimeListBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value delay_time_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value getDelayTimeList(int index) {
        if (delayTimeListBuilder_ == null) {
          return delayTimeList_.get(index);
        } else {
          return delayTimeListBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value delay_time_list = 2;</code>
       */
      public Builder setDelayTimeList(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value value) {
        if (delayTimeListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDelayTimeListIsMutable();
          delayTimeList_.set(index, value);
          onChanged();
        } else {
          delayTimeListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value delay_time_list = 2;</code>
       */
      public Builder setDelayTimeList(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder builderForValue) {
        if (delayTimeListBuilder_ == null) {
          ensureDelayTimeListIsMutable();
          delayTimeList_.set(index, builderForValue.build());
          onChanged();
        } else {
          delayTimeListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value delay_time_list = 2;</code>
       */
      public Builder addDelayTimeList(org.gof.demo.worldsrv.msg.Define.p_key_value value) {
        if (delayTimeListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDelayTimeListIsMutable();
          delayTimeList_.add(value);
          onChanged();
        } else {
          delayTimeListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value delay_time_list = 2;</code>
       */
      public Builder addDelayTimeList(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value value) {
        if (delayTimeListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDelayTimeListIsMutable();
          delayTimeList_.add(index, value);
          onChanged();
        } else {
          delayTimeListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value delay_time_list = 2;</code>
       */
      public Builder addDelayTimeList(
          org.gof.demo.worldsrv.msg.Define.p_key_value.Builder builderForValue) {
        if (delayTimeListBuilder_ == null) {
          ensureDelayTimeListIsMutable();
          delayTimeList_.add(builderForValue.build());
          onChanged();
        } else {
          delayTimeListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value delay_time_list = 2;</code>
       */
      public Builder addDelayTimeList(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder builderForValue) {
        if (delayTimeListBuilder_ == null) {
          ensureDelayTimeListIsMutable();
          delayTimeList_.add(index, builderForValue.build());
          onChanged();
        } else {
          delayTimeListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value delay_time_list = 2;</code>
       */
      public Builder addAllDelayTimeList(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_key_value> values) {
        if (delayTimeListBuilder_ == null) {
          ensureDelayTimeListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, delayTimeList_);
          onChanged();
        } else {
          delayTimeListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value delay_time_list = 2;</code>
       */
      public Builder clearDelayTimeList() {
        if (delayTimeListBuilder_ == null) {
          delayTimeList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          delayTimeListBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value delay_time_list = 2;</code>
       */
      public Builder removeDelayTimeList(int index) {
        if (delayTimeListBuilder_ == null) {
          ensureDelayTimeListIsMutable();
          delayTimeList_.remove(index);
          onChanged();
        } else {
          delayTimeListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value delay_time_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value.Builder getDelayTimeListBuilder(
          int index) {
        return getDelayTimeListFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value delay_time_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder getDelayTimeListOrBuilder(
          int index) {
        if (delayTimeListBuilder_ == null) {
          return delayTimeList_.get(index);  } else {
          return delayTimeListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value delay_time_list = 2;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
           getDelayTimeListOrBuilderList() {
        if (delayTimeListBuilder_ != null) {
          return delayTimeListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(delayTimeList_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value delay_time_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value.Builder addDelayTimeListBuilder() {
        return getDelayTimeListFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_key_value.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value delay_time_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value.Builder addDelayTimeListBuilder(
          int index) {
        return getDelayTimeListFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_key_value.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value delay_time_list = 2;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value.Builder> 
           getDelayTimeListBuilderList() {
        return getDelayTimeListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_key_value, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder, org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
          getDelayTimeListFieldBuilder() {
        if (delayTimeListBuilder_ == null) {
          delayTimeListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_key_value, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder, org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder>(
                  delayTimeList_,
                  ((bitField0_ & 0x00000002) != 0),
                  getParentForChildren(),
                  isClean());
          delayTimeList_ = null;
        }
        return delayTimeListBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.skill_set_delay_time_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.skill_set_delay_time_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<skill_set_delay_time_s2c>
        PARSER = new com.google.protobuf.AbstractParser<skill_set_delay_time_s2c>() {
      @java.lang.Override
      public skill_set_delay_time_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<skill_set_delay_time_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<skill_set_delay_time_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgSkill.skill_set_delay_time_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_skill_list_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_skill_list_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_skill_list_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_skill_list_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_skill_active_update_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_skill_active_update_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_skill_passive_update_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_skill_passive_update_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_skill_system_info_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_skill_system_info_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_skill_system_info_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_skill_system_info_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_skill_system_update_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_skill_system_update_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_skill_lv_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_skill_lv_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_skill_equip_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_skill_equip_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_skill_tab_info_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_skill_tab_info_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_skill_tab_info_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_skill_tab_info_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_skill_tab_info_update_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_skill_tab_info_update_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_skill_choose_tab_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_skill_choose_tab_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_skill_choose_tab_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_skill_choose_tab_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_skill_change_tab_name_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_skill_change_tab_name_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_skill_change_tab_name_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_skill_change_tab_name_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_skill_set_delay_time_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_skill_set_delay_time_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_skill_set_delay_time_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_skill_set_delay_time_s2c_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\017msg.skill.proto\022\031org.gof.demo.worldsrv" +
      ".msg\032\roptions.proto\032\014define.proto\"\027\n\016ski" +
      "ll_list_c2s:\005\210\303\032\201\020\"\235\001\n\016skill_list_s2c\022@\n" +
      "\ractive_skills\030\001 \003(\0132).org.gof.demo.worl" +
      "dsrv.msg.p_active_skill\022B\n\016passive_skill" +
      "s\030\002 \003(\0132*.org.gof.demo.worldsrv.msg.p_pa" +
      "ssive_skill:\005\210\303\032\201\020\"b\n\027skill_active_updat" +
      "e_s2c\022@\n\ractive_skills\030\001 \003(\0132).org.gof.d" +
      "emo.worldsrv.msg.p_active_skill:\005\210\303\032\202\020\"\243" +
      "\001\n\030skill_passive_update_s2c\022?\n\013update_li" +
      "st\030\001 \003(\0132*.org.gof.demo.worldsrv.msg.p_p" +
      "assive_skill\022?\n\013delete_list\030\002 \003(\0132*.org." +
      "gof.demo.worldsrv.msg.p_passive_skill:\005\210" +
      "\303\032\203\020\"\036\n\025skill_system_info_c2s:\005\210\303\032\205\020\"Y\n\025" +
      "skill_system_info_s2c\0229\n\006skills\030\001 \003(\0132)." +
      "org.gof.demo.worldsrv.msg.p_skill_system" +
      ":\005\210\303\032\205\020\"i\n\027skill_system_update_s2c\022\014\n\004ty" +
      "pe\030\001 \001(\005\0229\n\006skills\030\002 \003(\0132).org.gof.demo." +
      "worldsrv.msg.p_skill_system:\005\210\303\032\206\020\",\n\014sk" +
      "ill_lv_c2s\022\025\n\tskill_ids\030\001 \003(\005B\002\020\000:\005\210\303\032\207\020" +
      "\"R\n\017skill_equip_c2s\0228\n\010pos_info\030\001 \003(\0132&." +
      "org.gof.demo.worldsrv.msg.p_key_value:\005\210" +
      "\303\032\210\020\"\033\n\022skill_tab_info_c2s:\005\210\303\032\211\020\"g\n\022ski" +
      "ll_tab_info_s2c\022\013\n\003tab\030\001 \001(\r\022=\n\010tab_list" +
      "\030\002 \003(\0132+.org.gof.demo.worldsrv.msg.p_ski" +
      "ll_tab_info:\005\210\303\032\211\020\"i\n\031skill_tab_info_upd" +
      "ate_s2c\022\013\n\003tab\030\001 \001(\r\0228\n\010pos_info\030\002 \003(\0132&" +
      ".org.gof.demo.worldsrv.msg.p_key_value:\005" +
      "\210\303\032\212\020\"*\n\024skill_choose_tab_c2s\022\013\n\003tab\030\001 \001" +
      "(\r:\005\210\303\032\213\020\".\n\024skill_choose_tab_s2c\022\017\n\007new" +
      "_tab\030\001 \001(\r:\005\210\303\032\213\020\"=\n\031skill_change_tab_na" +
      "me_c2s\022\013\n\003tab\030\001 \001(\r\022\014\n\004name\030\002 \001(\t:\005\210\303\032\214\020" +
      "\"=\n\031skill_change_tab_name_s2c\022\013\n\003tab\030\001 \001" +
      "(\r\022\014\n\004name\030\002 \001(\t:\005\210\303\032\214\020\"n\n\030skill_set_del" +
      "ay_time_c2s\022\013\n\003tab\030\001 \001(\r\022>\n\016set_delay_ti" +
      "me\030\002 \003(\0132&.org.gof.demo.worldsrv.msg.p_k" +
      "ey_value:\005\210\303\032\215\020\"o\n\030skill_set_delay_time_" +
      "s2c\022\013\n\003tab\030\001 \001(\r\022?\n\017delay_time_list\030\002 \003(" +
      "\0132&.org.gof.demo.worldsrv.msg.p_key_valu" +
      "e:\005\210\303\032\215\020b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          org.gof.demo.worldsrv.msg.Options.getDescriptor(),
          org.gof.demo.worldsrv.msg.Define.getDescriptor(),
        });
    internal_static_org_gof_demo_worldsrv_msg_skill_list_c2s_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_org_gof_demo_worldsrv_msg_skill_list_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_skill_list_c2s_descriptor,
        new java.lang.String[] { });
    internal_static_org_gof_demo_worldsrv_msg_skill_list_s2c_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_org_gof_demo_worldsrv_msg_skill_list_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_skill_list_s2c_descriptor,
        new java.lang.String[] { "ActiveSkills", "PassiveSkills", });
    internal_static_org_gof_demo_worldsrv_msg_skill_active_update_s2c_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_org_gof_demo_worldsrv_msg_skill_active_update_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_skill_active_update_s2c_descriptor,
        new java.lang.String[] { "ActiveSkills", });
    internal_static_org_gof_demo_worldsrv_msg_skill_passive_update_s2c_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_org_gof_demo_worldsrv_msg_skill_passive_update_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_skill_passive_update_s2c_descriptor,
        new java.lang.String[] { "UpdateList", "DeleteList", });
    internal_static_org_gof_demo_worldsrv_msg_skill_system_info_c2s_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_org_gof_demo_worldsrv_msg_skill_system_info_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_skill_system_info_c2s_descriptor,
        new java.lang.String[] { });
    internal_static_org_gof_demo_worldsrv_msg_skill_system_info_s2c_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_org_gof_demo_worldsrv_msg_skill_system_info_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_skill_system_info_s2c_descriptor,
        new java.lang.String[] { "Skills", });
    internal_static_org_gof_demo_worldsrv_msg_skill_system_update_s2c_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_org_gof_demo_worldsrv_msg_skill_system_update_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_skill_system_update_s2c_descriptor,
        new java.lang.String[] { "Type", "Skills", });
    internal_static_org_gof_demo_worldsrv_msg_skill_lv_c2s_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_org_gof_demo_worldsrv_msg_skill_lv_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_skill_lv_c2s_descriptor,
        new java.lang.String[] { "SkillIds", });
    internal_static_org_gof_demo_worldsrv_msg_skill_equip_c2s_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_org_gof_demo_worldsrv_msg_skill_equip_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_skill_equip_c2s_descriptor,
        new java.lang.String[] { "PosInfo", });
    internal_static_org_gof_demo_worldsrv_msg_skill_tab_info_c2s_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_org_gof_demo_worldsrv_msg_skill_tab_info_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_skill_tab_info_c2s_descriptor,
        new java.lang.String[] { });
    internal_static_org_gof_demo_worldsrv_msg_skill_tab_info_s2c_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_org_gof_demo_worldsrv_msg_skill_tab_info_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_skill_tab_info_s2c_descriptor,
        new java.lang.String[] { "Tab", "TabList", });
    internal_static_org_gof_demo_worldsrv_msg_skill_tab_info_update_s2c_descriptor =
      getDescriptor().getMessageTypes().get(11);
    internal_static_org_gof_demo_worldsrv_msg_skill_tab_info_update_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_skill_tab_info_update_s2c_descriptor,
        new java.lang.String[] { "Tab", "PosInfo", });
    internal_static_org_gof_demo_worldsrv_msg_skill_choose_tab_c2s_descriptor =
      getDescriptor().getMessageTypes().get(12);
    internal_static_org_gof_demo_worldsrv_msg_skill_choose_tab_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_skill_choose_tab_c2s_descriptor,
        new java.lang.String[] { "Tab", });
    internal_static_org_gof_demo_worldsrv_msg_skill_choose_tab_s2c_descriptor =
      getDescriptor().getMessageTypes().get(13);
    internal_static_org_gof_demo_worldsrv_msg_skill_choose_tab_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_skill_choose_tab_s2c_descriptor,
        new java.lang.String[] { "NewTab", });
    internal_static_org_gof_demo_worldsrv_msg_skill_change_tab_name_c2s_descriptor =
      getDescriptor().getMessageTypes().get(14);
    internal_static_org_gof_demo_worldsrv_msg_skill_change_tab_name_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_skill_change_tab_name_c2s_descriptor,
        new java.lang.String[] { "Tab", "Name", });
    internal_static_org_gof_demo_worldsrv_msg_skill_change_tab_name_s2c_descriptor =
      getDescriptor().getMessageTypes().get(15);
    internal_static_org_gof_demo_worldsrv_msg_skill_change_tab_name_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_skill_change_tab_name_s2c_descriptor,
        new java.lang.String[] { "Tab", "Name", });
    internal_static_org_gof_demo_worldsrv_msg_skill_set_delay_time_c2s_descriptor =
      getDescriptor().getMessageTypes().get(16);
    internal_static_org_gof_demo_worldsrv_msg_skill_set_delay_time_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_skill_set_delay_time_c2s_descriptor,
        new java.lang.String[] { "Tab", "SetDelayTime", });
    internal_static_org_gof_demo_worldsrv_msg_skill_set_delay_time_s2c_descriptor =
      getDescriptor().getMessageTypes().get(17);
    internal_static_org_gof_demo_worldsrv_msg_skill_set_delay_time_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_skill_set_delay_time_s2c_descriptor,
        new java.lang.String[] { "Tab", "DelayTimeList", });
    com.google.protobuf.ExtensionRegistry registry =
        com.google.protobuf.ExtensionRegistry.newInstance();
    registry.add(org.gof.demo.worldsrv.msg.Options.msgid);
    com.google.protobuf.Descriptors.FileDescriptor
        .internalUpdateFileDescriptor(descriptor, registry);
    org.gof.demo.worldsrv.msg.Options.getDescriptor();
    org.gof.demo.worldsrv.msg.Define.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
