// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: msg.collection.proto

// Protobuf Java Version: 3.25.3
package org.gof.demo.worldsrv.msg;

public final class MsgCollection {
  private MsgCollection() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface collection_info_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.collection_info_c2s)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.collection_info_c2s}
   */
  public static final class collection_info_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.collection_info_c2s)
      collection_info_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use collection_info_c2s.newBuilder() to construct.
    private collection_info_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private collection_info_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new collection_info_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgCollection.internal_static_org_gof_demo_worldsrv_msg_collection_info_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgCollection.internal_static_org_gof_demo_worldsrv_msg_collection_info_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgCollection.collection_info_c2s.class, org.gof.demo.worldsrv.msg.MsgCollection.collection_info_c2s.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgCollection.collection_info_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgCollection.collection_info_c2s other = (org.gof.demo.worldsrv.msg.MsgCollection.collection_info_c2s) obj;

      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgCollection.collection_info_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCollection.collection_info_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCollection.collection_info_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCollection.collection_info_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCollection.collection_info_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCollection.collection_info_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCollection.collection_info_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCollection.collection_info_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgCollection.collection_info_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgCollection.collection_info_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCollection.collection_info_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCollection.collection_info_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgCollection.collection_info_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.collection_info_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.collection_info_c2s)
        org.gof.demo.worldsrv.msg.MsgCollection.collection_info_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgCollection.internal_static_org_gof_demo_worldsrv_msg_collection_info_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgCollection.internal_static_org_gof_demo_worldsrv_msg_collection_info_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgCollection.collection_info_c2s.class, org.gof.demo.worldsrv.msg.MsgCollection.collection_info_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgCollection.collection_info_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgCollection.internal_static_org_gof_demo_worldsrv_msg_collection_info_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCollection.collection_info_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgCollection.collection_info_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCollection.collection_info_c2s build() {
        org.gof.demo.worldsrv.msg.MsgCollection.collection_info_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCollection.collection_info_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgCollection.collection_info_c2s result = new org.gof.demo.worldsrv.msg.MsgCollection.collection_info_c2s(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgCollection.collection_info_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgCollection.collection_info_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgCollection.collection_info_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgCollection.collection_info_c2s.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.collection_info_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.collection_info_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgCollection.collection_info_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgCollection.collection_info_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgCollection.collection_info_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<collection_info_c2s>
        PARSER = new com.google.protobuf.AbstractParser<collection_info_c2s>() {
      @java.lang.Override
      public collection_info_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<collection_info_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<collection_info_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgCollection.collection_info_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface collection_info_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.collection_info_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_collection_list type_list = 1;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_collection_list> 
        getTypeListList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_collection_list type_list = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_collection_list getTypeList(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_collection_list type_list = 1;</code>
     */
    int getTypeListCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_collection_list type_list = 1;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_collection_listOrBuilder> 
        getTypeListOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_collection_list type_list = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_collection_listOrBuilder getTypeListOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.collection_info_s2c}
   */
  public static final class collection_info_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.collection_info_s2c)
      collection_info_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use collection_info_s2c.newBuilder() to construct.
    private collection_info_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private collection_info_s2c() {
      typeList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new collection_info_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgCollection.internal_static_org_gof_demo_worldsrv_msg_collection_info_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgCollection.internal_static_org_gof_demo_worldsrv_msg_collection_info_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgCollection.collection_info_s2c.class, org.gof.demo.worldsrv.msg.MsgCollection.collection_info_s2c.Builder.class);
    }

    public static final int TYPE_LIST_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_collection_list> typeList_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_collection_list type_list = 1;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_collection_list> getTypeListList() {
      return typeList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_collection_list type_list = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_collection_listOrBuilder> 
        getTypeListOrBuilderList() {
      return typeList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_collection_list type_list = 1;</code>
     */
    @java.lang.Override
    public int getTypeListCount() {
      return typeList_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_collection_list type_list = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_collection_list getTypeList(int index) {
      return typeList_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_collection_list type_list = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_collection_listOrBuilder getTypeListOrBuilder(
        int index) {
      return typeList_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < typeList_.size(); i++) {
        output.writeMessage(1, typeList_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < typeList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, typeList_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgCollection.collection_info_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgCollection.collection_info_s2c other = (org.gof.demo.worldsrv.msg.MsgCollection.collection_info_s2c) obj;

      if (!getTypeListList()
          .equals(other.getTypeListList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getTypeListCount() > 0) {
        hash = (37 * hash) + TYPE_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getTypeListList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgCollection.collection_info_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCollection.collection_info_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCollection.collection_info_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCollection.collection_info_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCollection.collection_info_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCollection.collection_info_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCollection.collection_info_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCollection.collection_info_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgCollection.collection_info_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgCollection.collection_info_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCollection.collection_info_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCollection.collection_info_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgCollection.collection_info_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.collection_info_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.collection_info_s2c)
        org.gof.demo.worldsrv.msg.MsgCollection.collection_info_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgCollection.internal_static_org_gof_demo_worldsrv_msg_collection_info_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgCollection.internal_static_org_gof_demo_worldsrv_msg_collection_info_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgCollection.collection_info_s2c.class, org.gof.demo.worldsrv.msg.MsgCollection.collection_info_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgCollection.collection_info_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        if (typeListBuilder_ == null) {
          typeList_ = java.util.Collections.emptyList();
        } else {
          typeList_ = null;
          typeListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgCollection.internal_static_org_gof_demo_worldsrv_msg_collection_info_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCollection.collection_info_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgCollection.collection_info_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCollection.collection_info_s2c build() {
        org.gof.demo.worldsrv.msg.MsgCollection.collection_info_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCollection.collection_info_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgCollection.collection_info_s2c result = new org.gof.demo.worldsrv.msg.MsgCollection.collection_info_s2c(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgCollection.collection_info_s2c result) {
        if (typeListBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            typeList_ = java.util.Collections.unmodifiableList(typeList_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.typeList_ = typeList_;
        } else {
          result.typeList_ = typeListBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgCollection.collection_info_s2c result) {
        int from_bitField0_ = bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgCollection.collection_info_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgCollection.collection_info_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgCollection.collection_info_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgCollection.collection_info_s2c.getDefaultInstance()) return this;
        if (typeListBuilder_ == null) {
          if (!other.typeList_.isEmpty()) {
            if (typeList_.isEmpty()) {
              typeList_ = other.typeList_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureTypeListIsMutable();
              typeList_.addAll(other.typeList_);
            }
            onChanged();
          }
        } else {
          if (!other.typeList_.isEmpty()) {
            if (typeListBuilder_.isEmpty()) {
              typeListBuilder_.dispose();
              typeListBuilder_ = null;
              typeList_ = other.typeList_;
              bitField0_ = (bitField0_ & ~0x00000001);
              typeListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getTypeListFieldBuilder() : null;
            } else {
              typeListBuilder_.addAllMessages(other.typeList_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                org.gof.demo.worldsrv.msg.Define.p_collection_list m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_collection_list.parser(),
                        extensionRegistry);
                if (typeListBuilder_ == null) {
                  ensureTypeListIsMutable();
                  typeList_.add(m);
                } else {
                  typeListBuilder_.addMessage(m);
                }
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_collection_list> typeList_ =
        java.util.Collections.emptyList();
      private void ensureTypeListIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          typeList_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_collection_list>(typeList_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_collection_list, org.gof.demo.worldsrv.msg.Define.p_collection_list.Builder, org.gof.demo.worldsrv.msg.Define.p_collection_listOrBuilder> typeListBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_collection_list type_list = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_collection_list> getTypeListList() {
        if (typeListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(typeList_);
        } else {
          return typeListBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_collection_list type_list = 1;</code>
       */
      public int getTypeListCount() {
        if (typeListBuilder_ == null) {
          return typeList_.size();
        } else {
          return typeListBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_collection_list type_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_collection_list getTypeList(int index) {
        if (typeListBuilder_ == null) {
          return typeList_.get(index);
        } else {
          return typeListBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_collection_list type_list = 1;</code>
       */
      public Builder setTypeList(
          int index, org.gof.demo.worldsrv.msg.Define.p_collection_list value) {
        if (typeListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTypeListIsMutable();
          typeList_.set(index, value);
          onChanged();
        } else {
          typeListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_collection_list type_list = 1;</code>
       */
      public Builder setTypeList(
          int index, org.gof.demo.worldsrv.msg.Define.p_collection_list.Builder builderForValue) {
        if (typeListBuilder_ == null) {
          ensureTypeListIsMutable();
          typeList_.set(index, builderForValue.build());
          onChanged();
        } else {
          typeListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_collection_list type_list = 1;</code>
       */
      public Builder addTypeList(org.gof.demo.worldsrv.msg.Define.p_collection_list value) {
        if (typeListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTypeListIsMutable();
          typeList_.add(value);
          onChanged();
        } else {
          typeListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_collection_list type_list = 1;</code>
       */
      public Builder addTypeList(
          int index, org.gof.demo.worldsrv.msg.Define.p_collection_list value) {
        if (typeListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTypeListIsMutable();
          typeList_.add(index, value);
          onChanged();
        } else {
          typeListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_collection_list type_list = 1;</code>
       */
      public Builder addTypeList(
          org.gof.demo.worldsrv.msg.Define.p_collection_list.Builder builderForValue) {
        if (typeListBuilder_ == null) {
          ensureTypeListIsMutable();
          typeList_.add(builderForValue.build());
          onChanged();
        } else {
          typeListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_collection_list type_list = 1;</code>
       */
      public Builder addTypeList(
          int index, org.gof.demo.worldsrv.msg.Define.p_collection_list.Builder builderForValue) {
        if (typeListBuilder_ == null) {
          ensureTypeListIsMutable();
          typeList_.add(index, builderForValue.build());
          onChanged();
        } else {
          typeListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_collection_list type_list = 1;</code>
       */
      public Builder addAllTypeList(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_collection_list> values) {
        if (typeListBuilder_ == null) {
          ensureTypeListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, typeList_);
          onChanged();
        } else {
          typeListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_collection_list type_list = 1;</code>
       */
      public Builder clearTypeList() {
        if (typeListBuilder_ == null) {
          typeList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          typeListBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_collection_list type_list = 1;</code>
       */
      public Builder removeTypeList(int index) {
        if (typeListBuilder_ == null) {
          ensureTypeListIsMutable();
          typeList_.remove(index);
          onChanged();
        } else {
          typeListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_collection_list type_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_collection_list.Builder getTypeListBuilder(
          int index) {
        return getTypeListFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_collection_list type_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_collection_listOrBuilder getTypeListOrBuilder(
          int index) {
        if (typeListBuilder_ == null) {
          return typeList_.get(index);  } else {
          return typeListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_collection_list type_list = 1;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_collection_listOrBuilder> 
           getTypeListOrBuilderList() {
        if (typeListBuilder_ != null) {
          return typeListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(typeList_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_collection_list type_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_collection_list.Builder addTypeListBuilder() {
        return getTypeListFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_collection_list.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_collection_list type_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_collection_list.Builder addTypeListBuilder(
          int index) {
        return getTypeListFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_collection_list.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_collection_list type_list = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_collection_list.Builder> 
           getTypeListBuilderList() {
        return getTypeListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_collection_list, org.gof.demo.worldsrv.msg.Define.p_collection_list.Builder, org.gof.demo.worldsrv.msg.Define.p_collection_listOrBuilder> 
          getTypeListFieldBuilder() {
        if (typeListBuilder_ == null) {
          typeListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_collection_list, org.gof.demo.worldsrv.msg.Define.p_collection_list.Builder, org.gof.demo.worldsrv.msg.Define.p_collection_listOrBuilder>(
                  typeList_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          typeList_ = null;
        }
        return typeListBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.collection_info_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.collection_info_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgCollection.collection_info_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgCollection.collection_info_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgCollection.collection_info_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<collection_info_s2c>
        PARSER = new com.google.protobuf.AbstractParser<collection_info_s2c>() {
      @java.lang.Override
      public collection_info_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<collection_info_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<collection_info_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgCollection.collection_info_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface collection_enhance_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.collection_enhance_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 cfg_id = 1;</code>
     * @return The cfgId.
     */
    int getCfgId();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.collection_enhance_c2s}
   */
  public static final class collection_enhance_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.collection_enhance_c2s)
      collection_enhance_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use collection_enhance_c2s.newBuilder() to construct.
    private collection_enhance_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private collection_enhance_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new collection_enhance_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgCollection.internal_static_org_gof_demo_worldsrv_msg_collection_enhance_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgCollection.internal_static_org_gof_demo_worldsrv_msg_collection_enhance_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_c2s.class, org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_c2s.Builder.class);
    }

    public static final int CFG_ID_FIELD_NUMBER = 1;
    private int cfgId_ = 0;
    /**
     * <code>uint32 cfg_id = 1;</code>
     * @return The cfgId.
     */
    @java.lang.Override
    public int getCfgId() {
      return cfgId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (cfgId_ != 0) {
        output.writeUInt32(1, cfgId_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (cfgId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, cfgId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_c2s other = (org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_c2s) obj;

      if (getCfgId()
          != other.getCfgId()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CFG_ID_FIELD_NUMBER;
      hash = (53 * hash) + getCfgId();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.collection_enhance_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.collection_enhance_c2s)
        org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgCollection.internal_static_org_gof_demo_worldsrv_msg_collection_enhance_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgCollection.internal_static_org_gof_demo_worldsrv_msg_collection_enhance_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_c2s.class, org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        cfgId_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgCollection.internal_static_org_gof_demo_worldsrv_msg_collection_enhance_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_c2s build() {
        org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_c2s result = new org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.cfgId_ = cfgId_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_c2s.getDefaultInstance()) return this;
        if (other.getCfgId() != 0) {
          setCfgId(other.getCfgId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                cfgId_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int cfgId_ ;
      /**
       * <code>uint32 cfg_id = 1;</code>
       * @return The cfgId.
       */
      @java.lang.Override
      public int getCfgId() {
        return cfgId_;
      }
      /**
       * <code>uint32 cfg_id = 1;</code>
       * @param value The cfgId to set.
       * @return This builder for chaining.
       */
      public Builder setCfgId(int value) {

        cfgId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 cfg_id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCfgId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        cfgId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.collection_enhance_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.collection_enhance_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<collection_enhance_c2s>
        PARSER = new com.google.protobuf.AbstractParser<collection_enhance_c2s>() {
      @java.lang.Override
      public collection_enhance_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<collection_enhance_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<collection_enhance_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface collection_enhance_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.collection_enhance_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.org.gof.demo.worldsrv.msg.p_collection collection = 1;</code>
     * @return Whether the collection field is set.
     */
    boolean hasCollection();
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_collection collection = 1;</code>
     * @return The collection.
     */
    org.gof.demo.worldsrv.msg.Define.p_collection getCollection();
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_collection collection = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_collectionOrBuilder getCollectionOrBuilder();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.collection_enhance_s2c}
   */
  public static final class collection_enhance_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.collection_enhance_s2c)
      collection_enhance_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use collection_enhance_s2c.newBuilder() to construct.
    private collection_enhance_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private collection_enhance_s2c() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new collection_enhance_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgCollection.internal_static_org_gof_demo_worldsrv_msg_collection_enhance_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgCollection.internal_static_org_gof_demo_worldsrv_msg_collection_enhance_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_s2c.class, org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_s2c.Builder.class);
    }

    private int bitField0_;
    public static final int COLLECTION_FIELD_NUMBER = 1;
    private org.gof.demo.worldsrv.msg.Define.p_collection collection_;
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_collection collection = 1;</code>
     * @return Whether the collection field is set.
     */
    @java.lang.Override
    public boolean hasCollection() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_collection collection = 1;</code>
     * @return The collection.
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_collection getCollection() {
      return collection_ == null ? org.gof.demo.worldsrv.msg.Define.p_collection.getDefaultInstance() : collection_;
    }
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_collection collection = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_collectionOrBuilder getCollectionOrBuilder() {
      return collection_ == null ? org.gof.demo.worldsrv.msg.Define.p_collection.getDefaultInstance() : collection_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getCollection());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCollection());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_s2c other = (org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_s2c) obj;

      if (hasCollection() != other.hasCollection()) return false;
      if (hasCollection()) {
        if (!getCollection()
            .equals(other.getCollection())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCollection()) {
        hash = (37 * hash) + COLLECTION_FIELD_NUMBER;
        hash = (53 * hash) + getCollection().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.collection_enhance_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.collection_enhance_s2c)
        org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgCollection.internal_static_org_gof_demo_worldsrv_msg_collection_enhance_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgCollection.internal_static_org_gof_demo_worldsrv_msg_collection_enhance_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_s2c.class, org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_s2c.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getCollectionFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        collection_ = null;
        if (collectionBuilder_ != null) {
          collectionBuilder_.dispose();
          collectionBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgCollection.internal_static_org_gof_demo_worldsrv_msg_collection_enhance_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_s2c build() {
        org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_s2c result = new org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_s2c result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.collection_ = collectionBuilder_ == null
              ? collection_
              : collectionBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_s2c.getDefaultInstance()) return this;
        if (other.hasCollection()) {
          mergeCollection(other.getCollection());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getCollectionFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private org.gof.demo.worldsrv.msg.Define.p_collection collection_;
      private com.google.protobuf.SingleFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_collection, org.gof.demo.worldsrv.msg.Define.p_collection.Builder, org.gof.demo.worldsrv.msg.Define.p_collectionOrBuilder> collectionBuilder_;
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_collection collection = 1;</code>
       * @return Whether the collection field is set.
       */
      public boolean hasCollection() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_collection collection = 1;</code>
       * @return The collection.
       */
      public org.gof.demo.worldsrv.msg.Define.p_collection getCollection() {
        if (collectionBuilder_ == null) {
          return collection_ == null ? org.gof.demo.worldsrv.msg.Define.p_collection.getDefaultInstance() : collection_;
        } else {
          return collectionBuilder_.getMessage();
        }
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_collection collection = 1;</code>
       */
      public Builder setCollection(org.gof.demo.worldsrv.msg.Define.p_collection value) {
        if (collectionBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          collection_ = value;
        } else {
          collectionBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_collection collection = 1;</code>
       */
      public Builder setCollection(
          org.gof.demo.worldsrv.msg.Define.p_collection.Builder builderForValue) {
        if (collectionBuilder_ == null) {
          collection_ = builderForValue.build();
        } else {
          collectionBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_collection collection = 1;</code>
       */
      public Builder mergeCollection(org.gof.demo.worldsrv.msg.Define.p_collection value) {
        if (collectionBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
            collection_ != null &&
            collection_ != org.gof.demo.worldsrv.msg.Define.p_collection.getDefaultInstance()) {
            getCollectionBuilder().mergeFrom(value);
          } else {
            collection_ = value;
          }
        } else {
          collectionBuilder_.mergeFrom(value);
        }
        if (collection_ != null) {
          bitField0_ |= 0x00000001;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_collection collection = 1;</code>
       */
      public Builder clearCollection() {
        bitField0_ = (bitField0_ & ~0x00000001);
        collection_ = null;
        if (collectionBuilder_ != null) {
          collectionBuilder_.dispose();
          collectionBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_collection collection = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_collection.Builder getCollectionBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getCollectionFieldBuilder().getBuilder();
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_collection collection = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_collectionOrBuilder getCollectionOrBuilder() {
        if (collectionBuilder_ != null) {
          return collectionBuilder_.getMessageOrBuilder();
        } else {
          return collection_ == null ?
              org.gof.demo.worldsrv.msg.Define.p_collection.getDefaultInstance() : collection_;
        }
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_collection collection = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_collection, org.gof.demo.worldsrv.msg.Define.p_collection.Builder, org.gof.demo.worldsrv.msg.Define.p_collectionOrBuilder> 
          getCollectionFieldBuilder() {
        if (collectionBuilder_ == null) {
          collectionBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_collection, org.gof.demo.worldsrv.msg.Define.p_collection.Builder, org.gof.demo.worldsrv.msg.Define.p_collectionOrBuilder>(
                  getCollection(),
                  getParentForChildren(),
                  isClean());
          collection_ = null;
        }
        return collectionBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.collection_enhance_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.collection_enhance_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<collection_enhance_s2c>
        PARSER = new com.google.protobuf.AbstractParser<collection_enhance_s2c>() {
      @java.lang.Override
      public collection_enhance_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<collection_enhance_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<collection_enhance_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgCollection.collection_enhance_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_collection_info_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_collection_info_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_collection_info_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_collection_info_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_collection_enhance_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_collection_enhance_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_collection_enhance_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_collection_enhance_s2c_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\024msg.collection.proto\022\031org.gof.demo.wor" +
      "ldsrv.msg\032\roptions.proto\032\014define.proto\"\034" +
      "\n\023collection_info_c2s:\005\210\303\032\201$\"]\n\023collecti" +
      "on_info_s2c\022?\n\ttype_list\030\001 \003(\0132,.org.gof" +
      ".demo.worldsrv.msg.p_collection_list:\005\210\303" +
      "\032\201$\"/\n\026collection_enhance_c2s\022\016\n\006cfg_id\030" +
      "\001 \001(\r:\005\210\303\032\202$\"\\\n\026collection_enhance_s2c\022;" +
      "\n\ncollection\030\001 \001(\0132\'.org.gof.demo.worlds" +
      "rv.msg.p_collection:\005\210\303\032\202$b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          org.gof.demo.worldsrv.msg.Options.getDescriptor(),
          org.gof.demo.worldsrv.msg.Define.getDescriptor(),
        });
    internal_static_org_gof_demo_worldsrv_msg_collection_info_c2s_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_org_gof_demo_worldsrv_msg_collection_info_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_collection_info_c2s_descriptor,
        new java.lang.String[] { });
    internal_static_org_gof_demo_worldsrv_msg_collection_info_s2c_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_org_gof_demo_worldsrv_msg_collection_info_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_collection_info_s2c_descriptor,
        new java.lang.String[] { "TypeList", });
    internal_static_org_gof_demo_worldsrv_msg_collection_enhance_c2s_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_org_gof_demo_worldsrv_msg_collection_enhance_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_collection_enhance_c2s_descriptor,
        new java.lang.String[] { "CfgId", });
    internal_static_org_gof_demo_worldsrv_msg_collection_enhance_s2c_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_org_gof_demo_worldsrv_msg_collection_enhance_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_collection_enhance_s2c_descriptor,
        new java.lang.String[] { "Collection", });
    com.google.protobuf.ExtensionRegistry registry =
        com.google.protobuf.ExtensionRegistry.newInstance();
    registry.add(org.gof.demo.worldsrv.msg.Options.msgid);
    com.google.protobuf.Descriptors.FileDescriptor
        .internalUpdateFileDescriptor(descriptor, registry);
    org.gof.demo.worldsrv.msg.Options.getDescriptor();
    org.gof.demo.worldsrv.msg.Define.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
