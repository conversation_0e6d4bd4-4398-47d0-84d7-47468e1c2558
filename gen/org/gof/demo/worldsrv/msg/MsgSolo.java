// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: msg.solo.proto

// Protobuf Java Version: 3.25.3
package org.gof.demo.worldsrv.msg;

public final class MsgSolo {
  private MsgSolo() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface solo_start_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.solo_start_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint64 target_id = 1;</code>
     * @return The targetId.
     */
    long getTargetId();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.solo_start_c2s}
   */
  public static final class solo_start_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.solo_start_c2s)
      solo_start_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use solo_start_c2s.newBuilder() to construct.
    private solo_start_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private solo_start_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new solo_start_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgSolo.internal_static_org_gof_demo_worldsrv_msg_solo_start_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgSolo.internal_static_org_gof_demo_worldsrv_msg_solo_start_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgSolo.solo_start_c2s.class, org.gof.demo.worldsrv.msg.MsgSolo.solo_start_c2s.Builder.class);
    }

    public static final int TARGET_ID_FIELD_NUMBER = 1;
    private long targetId_ = 0L;
    /**
     * <code>uint64 target_id = 1;</code>
     * @return The targetId.
     */
    @java.lang.Override
    public long getTargetId() {
      return targetId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (targetId_ != 0L) {
        output.writeUInt64(1, targetId_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (targetId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(1, targetId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgSolo.solo_start_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgSolo.solo_start_c2s other = (org.gof.demo.worldsrv.msg.MsgSolo.solo_start_c2s) obj;

      if (getTargetId()
          != other.getTargetId()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TARGET_ID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTargetId());
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_start_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_start_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_start_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_start_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_start_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_start_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_start_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_start_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_start_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_start_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_start_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_start_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgSolo.solo_start_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.solo_start_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.solo_start_c2s)
        org.gof.demo.worldsrv.msg.MsgSolo.solo_start_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgSolo.internal_static_org_gof_demo_worldsrv_msg_solo_start_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgSolo.internal_static_org_gof_demo_worldsrv_msg_solo_start_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgSolo.solo_start_c2s.class, org.gof.demo.worldsrv.msg.MsgSolo.solo_start_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgSolo.solo_start_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        targetId_ = 0L;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgSolo.internal_static_org_gof_demo_worldsrv_msg_solo_start_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSolo.solo_start_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgSolo.solo_start_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSolo.solo_start_c2s build() {
        org.gof.demo.worldsrv.msg.MsgSolo.solo_start_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSolo.solo_start_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgSolo.solo_start_c2s result = new org.gof.demo.worldsrv.msg.MsgSolo.solo_start_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgSolo.solo_start_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.targetId_ = targetId_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgSolo.solo_start_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgSolo.solo_start_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgSolo.solo_start_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgSolo.solo_start_c2s.getDefaultInstance()) return this;
        if (other.getTargetId() != 0L) {
          setTargetId(other.getTargetId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                targetId_ = input.readUInt64();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private long targetId_ ;
      /**
       * <code>uint64 target_id = 1;</code>
       * @return The targetId.
       */
      @java.lang.Override
      public long getTargetId() {
        return targetId_;
      }
      /**
       * <code>uint64 target_id = 1;</code>
       * @param value The targetId to set.
       * @return This builder for chaining.
       */
      public Builder setTargetId(long value) {

        targetId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 target_id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTargetId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        targetId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.solo_start_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.solo_start_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgSolo.solo_start_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgSolo.solo_start_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_start_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<solo_start_c2s>
        PARSER = new com.google.protobuf.AbstractParser<solo_start_c2s>() {
      @java.lang.Override
      public solo_start_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<solo_start_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<solo_start_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgSolo.solo_start_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface solo_start_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.solo_start_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <code>uint64 target_id = 2;</code>
     * @return The targetId.
     */
    long getTargetId();

    /**
     * <code>uint64 vid = 3;</code>
     * @return The vid.
     */
    long getVid();

    /**
     * <code>uint64 seed = 4;</code>
     * @return The seed.
     */
    long getSeed();

    /**
     * <code>.org.gof.demo.worldsrv.msg.p_base_fighter atk_data = 5;</code>
     * @return Whether the atkData field is set.
     */
    boolean hasAtkData();
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_base_fighter atk_data = 5;</code>
     * @return The atkData.
     */
    org.gof.demo.worldsrv.msg.Define.p_base_fighter getAtkData();
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_base_fighter atk_data = 5;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_base_fighterOrBuilder getAtkDataOrBuilder();

    /**
     * <code>.org.gof.demo.worldsrv.msg.p_base_fighter def_data = 6;</code>
     * @return Whether the defData field is set.
     */
    boolean hasDefData();
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_base_fighter def_data = 6;</code>
     * @return The defData.
     */
    org.gof.demo.worldsrv.msg.Define.p_base_fighter getDefData();
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_base_fighter def_data = 6;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_base_fighterOrBuilder getDefDataOrBuilder();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.solo_start_s2c}
   */
  public static final class solo_start_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.solo_start_s2c)
      solo_start_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use solo_start_s2c.newBuilder() to construct.
    private solo_start_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private solo_start_s2c() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new solo_start_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgSolo.internal_static_org_gof_demo_worldsrv_msg_solo_start_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgSolo.internal_static_org_gof_demo_worldsrv_msg_solo_start_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgSolo.solo_start_s2c.class, org.gof.demo.worldsrv.msg.MsgSolo.solo_start_s2c.Builder.class);
    }

    private int bitField0_;
    public static final int CODE_FIELD_NUMBER = 1;
    private int code_ = 0;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int TARGET_ID_FIELD_NUMBER = 2;
    private long targetId_ = 0L;
    /**
     * <code>uint64 target_id = 2;</code>
     * @return The targetId.
     */
    @java.lang.Override
    public long getTargetId() {
      return targetId_;
    }

    public static final int VID_FIELD_NUMBER = 3;
    private long vid_ = 0L;
    /**
     * <code>uint64 vid = 3;</code>
     * @return The vid.
     */
    @java.lang.Override
    public long getVid() {
      return vid_;
    }

    public static final int SEED_FIELD_NUMBER = 4;
    private long seed_ = 0L;
    /**
     * <code>uint64 seed = 4;</code>
     * @return The seed.
     */
    @java.lang.Override
    public long getSeed() {
      return seed_;
    }

    public static final int ATK_DATA_FIELD_NUMBER = 5;
    private org.gof.demo.worldsrv.msg.Define.p_base_fighter atkData_;
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_base_fighter atk_data = 5;</code>
     * @return Whether the atkData field is set.
     */
    @java.lang.Override
    public boolean hasAtkData() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_base_fighter atk_data = 5;</code>
     * @return The atkData.
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_base_fighter getAtkData() {
      return atkData_ == null ? org.gof.demo.worldsrv.msg.Define.p_base_fighter.getDefaultInstance() : atkData_;
    }
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_base_fighter atk_data = 5;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_base_fighterOrBuilder getAtkDataOrBuilder() {
      return atkData_ == null ? org.gof.demo.worldsrv.msg.Define.p_base_fighter.getDefaultInstance() : atkData_;
    }

    public static final int DEF_DATA_FIELD_NUMBER = 6;
    private org.gof.demo.worldsrv.msg.Define.p_base_fighter defData_;
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_base_fighter def_data = 6;</code>
     * @return Whether the defData field is set.
     */
    @java.lang.Override
    public boolean hasDefData() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_base_fighter def_data = 6;</code>
     * @return The defData.
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_base_fighter getDefData() {
      return defData_ == null ? org.gof.demo.worldsrv.msg.Define.p_base_fighter.getDefaultInstance() : defData_;
    }
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_base_fighter def_data = 6;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_base_fighterOrBuilder getDefDataOrBuilder() {
      return defData_ == null ? org.gof.demo.worldsrv.msg.Define.p_base_fighter.getDefaultInstance() : defData_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (targetId_ != 0L) {
        output.writeUInt64(2, targetId_);
      }
      if (vid_ != 0L) {
        output.writeUInt64(3, vid_);
      }
      if (seed_ != 0L) {
        output.writeUInt64(4, seed_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(5, getAtkData());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(6, getDefData());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (targetId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(2, targetId_);
      }
      if (vid_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(3, vid_);
      }
      if (seed_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(4, seed_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, getAtkData());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(6, getDefData());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgSolo.solo_start_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgSolo.solo_start_s2c other = (org.gof.demo.worldsrv.msg.MsgSolo.solo_start_s2c) obj;

      if (getCode()
          != other.getCode()) return false;
      if (getTargetId()
          != other.getTargetId()) return false;
      if (getVid()
          != other.getVid()) return false;
      if (getSeed()
          != other.getSeed()) return false;
      if (hasAtkData() != other.hasAtkData()) return false;
      if (hasAtkData()) {
        if (!getAtkData()
            .equals(other.getAtkData())) return false;
      }
      if (hasDefData() != other.hasDefData()) return false;
      if (hasDefData()) {
        if (!getDefData()
            .equals(other.getDefData())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      hash = (37 * hash) + TARGET_ID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTargetId());
      hash = (37 * hash) + VID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getVid());
      hash = (37 * hash) + SEED_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getSeed());
      if (hasAtkData()) {
        hash = (37 * hash) + ATK_DATA_FIELD_NUMBER;
        hash = (53 * hash) + getAtkData().hashCode();
      }
      if (hasDefData()) {
        hash = (37 * hash) + DEF_DATA_FIELD_NUMBER;
        hash = (53 * hash) + getDefData().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_start_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_start_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_start_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_start_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_start_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_start_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_start_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_start_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_start_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_start_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_start_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_start_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgSolo.solo_start_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.solo_start_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.solo_start_s2c)
        org.gof.demo.worldsrv.msg.MsgSolo.solo_start_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgSolo.internal_static_org_gof_demo_worldsrv_msg_solo_start_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgSolo.internal_static_org_gof_demo_worldsrv_msg_solo_start_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgSolo.solo_start_s2c.class, org.gof.demo.worldsrv.msg.MsgSolo.solo_start_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgSolo.solo_start_s2c.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getAtkDataFieldBuilder();
          getDefDataFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        code_ = 0;
        targetId_ = 0L;
        vid_ = 0L;
        seed_ = 0L;
        atkData_ = null;
        if (atkDataBuilder_ != null) {
          atkDataBuilder_.dispose();
          atkDataBuilder_ = null;
        }
        defData_ = null;
        if (defDataBuilder_ != null) {
          defDataBuilder_.dispose();
          defDataBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgSolo.internal_static_org_gof_demo_worldsrv_msg_solo_start_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSolo.solo_start_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgSolo.solo_start_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSolo.solo_start_s2c build() {
        org.gof.demo.worldsrv.msg.MsgSolo.solo_start_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSolo.solo_start_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgSolo.solo_start_s2c result = new org.gof.demo.worldsrv.msg.MsgSolo.solo_start_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgSolo.solo_start_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.code_ = code_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.targetId_ = targetId_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.vid_ = vid_;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.seed_ = seed_;
        }
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.atkData_ = atkDataBuilder_ == null
              ? atkData_
              : atkDataBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.defData_ = defDataBuilder_ == null
              ? defData_
              : defDataBuilder_.build();
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgSolo.solo_start_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgSolo.solo_start_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgSolo.solo_start_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgSolo.solo_start_s2c.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.getTargetId() != 0L) {
          setTargetId(other.getTargetId());
        }
        if (other.getVid() != 0L) {
          setVid(other.getVid());
        }
        if (other.getSeed() != 0L) {
          setSeed(other.getSeed());
        }
        if (other.hasAtkData()) {
          mergeAtkData(other.getAtkData());
        }
        if (other.hasDefData()) {
          mergeDefData(other.getDefData());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                code_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                targetId_ = input.readUInt64();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                vid_ = input.readUInt64();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              case 32: {
                seed_ = input.readUInt64();
                bitField0_ |= 0x00000008;
                break;
              } // case 32
              case 42: {
                input.readMessage(
                    getAtkDataFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000010;
                break;
              } // case 42
              case 50: {
                input.readMessage(
                    getDefDataFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000020;
                break;
              } // case 50
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {

        code_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        bitField0_ = (bitField0_ & ~0x00000001);
        code_ = 0;
        onChanged();
        return this;
      }

      private long targetId_ ;
      /**
       * <code>uint64 target_id = 2;</code>
       * @return The targetId.
       */
      @java.lang.Override
      public long getTargetId() {
        return targetId_;
      }
      /**
       * <code>uint64 target_id = 2;</code>
       * @param value The targetId to set.
       * @return This builder for chaining.
       */
      public Builder setTargetId(long value) {

        targetId_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 target_id = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearTargetId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        targetId_ = 0L;
        onChanged();
        return this;
      }

      private long vid_ ;
      /**
       * <code>uint64 vid = 3;</code>
       * @return The vid.
       */
      @java.lang.Override
      public long getVid() {
        return vid_;
      }
      /**
       * <code>uint64 vid = 3;</code>
       * @param value The vid to set.
       * @return This builder for chaining.
       */
      public Builder setVid(long value) {

        vid_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 vid = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearVid() {
        bitField0_ = (bitField0_ & ~0x00000004);
        vid_ = 0L;
        onChanged();
        return this;
      }

      private long seed_ ;
      /**
       * <code>uint64 seed = 4;</code>
       * @return The seed.
       */
      @java.lang.Override
      public long getSeed() {
        return seed_;
      }
      /**
       * <code>uint64 seed = 4;</code>
       * @param value The seed to set.
       * @return This builder for chaining.
       */
      public Builder setSeed(long value) {

        seed_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 seed = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearSeed() {
        bitField0_ = (bitField0_ & ~0x00000008);
        seed_ = 0L;
        onChanged();
        return this;
      }

      private org.gof.demo.worldsrv.msg.Define.p_base_fighter atkData_;
      private com.google.protobuf.SingleFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_base_fighter, org.gof.demo.worldsrv.msg.Define.p_base_fighter.Builder, org.gof.demo.worldsrv.msg.Define.p_base_fighterOrBuilder> atkDataBuilder_;
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter atk_data = 5;</code>
       * @return Whether the atkData field is set.
       */
      public boolean hasAtkData() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter atk_data = 5;</code>
       * @return The atkData.
       */
      public org.gof.demo.worldsrv.msg.Define.p_base_fighter getAtkData() {
        if (atkDataBuilder_ == null) {
          return atkData_ == null ? org.gof.demo.worldsrv.msg.Define.p_base_fighter.getDefaultInstance() : atkData_;
        } else {
          return atkDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter atk_data = 5;</code>
       */
      public Builder setAtkData(org.gof.demo.worldsrv.msg.Define.p_base_fighter value) {
        if (atkDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          atkData_ = value;
        } else {
          atkDataBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter atk_data = 5;</code>
       */
      public Builder setAtkData(
          org.gof.demo.worldsrv.msg.Define.p_base_fighter.Builder builderForValue) {
        if (atkDataBuilder_ == null) {
          atkData_ = builderForValue.build();
        } else {
          atkDataBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter atk_data = 5;</code>
       */
      public Builder mergeAtkData(org.gof.demo.worldsrv.msg.Define.p_base_fighter value) {
        if (atkDataBuilder_ == null) {
          if (((bitField0_ & 0x00000010) != 0) &&
            atkData_ != null &&
            atkData_ != org.gof.demo.worldsrv.msg.Define.p_base_fighter.getDefaultInstance()) {
            getAtkDataBuilder().mergeFrom(value);
          } else {
            atkData_ = value;
          }
        } else {
          atkDataBuilder_.mergeFrom(value);
        }
        if (atkData_ != null) {
          bitField0_ |= 0x00000010;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter atk_data = 5;</code>
       */
      public Builder clearAtkData() {
        bitField0_ = (bitField0_ & ~0x00000010);
        atkData_ = null;
        if (atkDataBuilder_ != null) {
          atkDataBuilder_.dispose();
          atkDataBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter atk_data = 5;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_base_fighter.Builder getAtkDataBuilder() {
        bitField0_ |= 0x00000010;
        onChanged();
        return getAtkDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter atk_data = 5;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_base_fighterOrBuilder getAtkDataOrBuilder() {
        if (atkDataBuilder_ != null) {
          return atkDataBuilder_.getMessageOrBuilder();
        } else {
          return atkData_ == null ?
              org.gof.demo.worldsrv.msg.Define.p_base_fighter.getDefaultInstance() : atkData_;
        }
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter atk_data = 5;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_base_fighter, org.gof.demo.worldsrv.msg.Define.p_base_fighter.Builder, org.gof.demo.worldsrv.msg.Define.p_base_fighterOrBuilder> 
          getAtkDataFieldBuilder() {
        if (atkDataBuilder_ == null) {
          atkDataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_base_fighter, org.gof.demo.worldsrv.msg.Define.p_base_fighter.Builder, org.gof.demo.worldsrv.msg.Define.p_base_fighterOrBuilder>(
                  getAtkData(),
                  getParentForChildren(),
                  isClean());
          atkData_ = null;
        }
        return atkDataBuilder_;
      }

      private org.gof.demo.worldsrv.msg.Define.p_base_fighter defData_;
      private com.google.protobuf.SingleFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_base_fighter, org.gof.demo.worldsrv.msg.Define.p_base_fighter.Builder, org.gof.demo.worldsrv.msg.Define.p_base_fighterOrBuilder> defDataBuilder_;
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter def_data = 6;</code>
       * @return Whether the defData field is set.
       */
      public boolean hasDefData() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter def_data = 6;</code>
       * @return The defData.
       */
      public org.gof.demo.worldsrv.msg.Define.p_base_fighter getDefData() {
        if (defDataBuilder_ == null) {
          return defData_ == null ? org.gof.demo.worldsrv.msg.Define.p_base_fighter.getDefaultInstance() : defData_;
        } else {
          return defDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter def_data = 6;</code>
       */
      public Builder setDefData(org.gof.demo.worldsrv.msg.Define.p_base_fighter value) {
        if (defDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          defData_ = value;
        } else {
          defDataBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter def_data = 6;</code>
       */
      public Builder setDefData(
          org.gof.demo.worldsrv.msg.Define.p_base_fighter.Builder builderForValue) {
        if (defDataBuilder_ == null) {
          defData_ = builderForValue.build();
        } else {
          defDataBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter def_data = 6;</code>
       */
      public Builder mergeDefData(org.gof.demo.worldsrv.msg.Define.p_base_fighter value) {
        if (defDataBuilder_ == null) {
          if (((bitField0_ & 0x00000020) != 0) &&
            defData_ != null &&
            defData_ != org.gof.demo.worldsrv.msg.Define.p_base_fighter.getDefaultInstance()) {
            getDefDataBuilder().mergeFrom(value);
          } else {
            defData_ = value;
          }
        } else {
          defDataBuilder_.mergeFrom(value);
        }
        if (defData_ != null) {
          bitField0_ |= 0x00000020;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter def_data = 6;</code>
       */
      public Builder clearDefData() {
        bitField0_ = (bitField0_ & ~0x00000020);
        defData_ = null;
        if (defDataBuilder_ != null) {
          defDataBuilder_.dispose();
          defDataBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter def_data = 6;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_base_fighter.Builder getDefDataBuilder() {
        bitField0_ |= 0x00000020;
        onChanged();
        return getDefDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter def_data = 6;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_base_fighterOrBuilder getDefDataOrBuilder() {
        if (defDataBuilder_ != null) {
          return defDataBuilder_.getMessageOrBuilder();
        } else {
          return defData_ == null ?
              org.gof.demo.worldsrv.msg.Define.p_base_fighter.getDefaultInstance() : defData_;
        }
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter def_data = 6;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_base_fighter, org.gof.demo.worldsrv.msg.Define.p_base_fighter.Builder, org.gof.demo.worldsrv.msg.Define.p_base_fighterOrBuilder> 
          getDefDataFieldBuilder() {
        if (defDataBuilder_ == null) {
          defDataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_base_fighter, org.gof.demo.worldsrv.msg.Define.p_base_fighter.Builder, org.gof.demo.worldsrv.msg.Define.p_base_fighterOrBuilder>(
                  getDefData(),
                  getParentForChildren(),
                  isClean());
          defData_ = null;
        }
        return defDataBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.solo_start_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.solo_start_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgSolo.solo_start_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgSolo.solo_start_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_start_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<solo_start_s2c>
        PARSER = new com.google.protobuf.AbstractParser<solo_start_s2c>() {
      @java.lang.Override
      public solo_start_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<solo_start_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<solo_start_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgSolo.solo_start_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface solo_result_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.solo_result_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint64 vid = 1;</code>
     * @return The vid.
     */
    long getVid();

    /**
     * <code>uint64 winner = 2;</code>
     * @return The winner.
     */
    long getWinner();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.solo_result_c2s}
   */
  public static final class solo_result_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.solo_result_c2s)
      solo_result_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use solo_result_c2s.newBuilder() to construct.
    private solo_result_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private solo_result_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new solo_result_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgSolo.internal_static_org_gof_demo_worldsrv_msg_solo_result_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgSolo.internal_static_org_gof_demo_worldsrv_msg_solo_result_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgSolo.solo_result_c2s.class, org.gof.demo.worldsrv.msg.MsgSolo.solo_result_c2s.Builder.class);
    }

    public static final int VID_FIELD_NUMBER = 1;
    private long vid_ = 0L;
    /**
     * <code>uint64 vid = 1;</code>
     * @return The vid.
     */
    @java.lang.Override
    public long getVid() {
      return vid_;
    }

    public static final int WINNER_FIELD_NUMBER = 2;
    private long winner_ = 0L;
    /**
     * <code>uint64 winner = 2;</code>
     * @return The winner.
     */
    @java.lang.Override
    public long getWinner() {
      return winner_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (vid_ != 0L) {
        output.writeUInt64(1, vid_);
      }
      if (winner_ != 0L) {
        output.writeUInt64(2, winner_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (vid_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(1, vid_);
      }
      if (winner_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(2, winner_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgSolo.solo_result_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgSolo.solo_result_c2s other = (org.gof.demo.worldsrv.msg.MsgSolo.solo_result_c2s) obj;

      if (getVid()
          != other.getVid()) return false;
      if (getWinner()
          != other.getWinner()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + VID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getVid());
      hash = (37 * hash) + WINNER_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getWinner());
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_result_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_result_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_result_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_result_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_result_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_result_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_result_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_result_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_result_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_result_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_result_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_result_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgSolo.solo_result_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.solo_result_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.solo_result_c2s)
        org.gof.demo.worldsrv.msg.MsgSolo.solo_result_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgSolo.internal_static_org_gof_demo_worldsrv_msg_solo_result_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgSolo.internal_static_org_gof_demo_worldsrv_msg_solo_result_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgSolo.solo_result_c2s.class, org.gof.demo.worldsrv.msg.MsgSolo.solo_result_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgSolo.solo_result_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        vid_ = 0L;
        winner_ = 0L;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgSolo.internal_static_org_gof_demo_worldsrv_msg_solo_result_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSolo.solo_result_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgSolo.solo_result_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSolo.solo_result_c2s build() {
        org.gof.demo.worldsrv.msg.MsgSolo.solo_result_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSolo.solo_result_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgSolo.solo_result_c2s result = new org.gof.demo.worldsrv.msg.MsgSolo.solo_result_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgSolo.solo_result_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.vid_ = vid_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.winner_ = winner_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgSolo.solo_result_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgSolo.solo_result_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgSolo.solo_result_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgSolo.solo_result_c2s.getDefaultInstance()) return this;
        if (other.getVid() != 0L) {
          setVid(other.getVid());
        }
        if (other.getWinner() != 0L) {
          setWinner(other.getWinner());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                vid_ = input.readUInt64();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                winner_ = input.readUInt64();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private long vid_ ;
      /**
       * <code>uint64 vid = 1;</code>
       * @return The vid.
       */
      @java.lang.Override
      public long getVid() {
        return vid_;
      }
      /**
       * <code>uint64 vid = 1;</code>
       * @param value The vid to set.
       * @return This builder for chaining.
       */
      public Builder setVid(long value) {

        vid_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 vid = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearVid() {
        bitField0_ = (bitField0_ & ~0x00000001);
        vid_ = 0L;
        onChanged();
        return this;
      }

      private long winner_ ;
      /**
       * <code>uint64 winner = 2;</code>
       * @return The winner.
       */
      @java.lang.Override
      public long getWinner() {
        return winner_;
      }
      /**
       * <code>uint64 winner = 2;</code>
       * @param value The winner to set.
       * @return This builder for chaining.
       */
      public Builder setWinner(long value) {

        winner_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 winner = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearWinner() {
        bitField0_ = (bitField0_ & ~0x00000002);
        winner_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.solo_result_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.solo_result_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgSolo.solo_result_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgSolo.solo_result_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_result_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<solo_result_c2s>
        PARSER = new com.google.protobuf.AbstractParser<solo_result_c2s>() {
      @java.lang.Override
      public solo_result_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<solo_result_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<solo_result_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgSolo.solo_result_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface solo_result_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.solo_result_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint64 vid = 1;</code>
     * @return The vid.
     */
    long getVid();

    /**
     * <code>uint64 winner = 2;</code>
     * @return The winner.
     */
    long getWinner();

    /**
     * <code>.org.gof.demo.worldsrv.msg.p_base_fighter atk_data = 3;</code>
     * @return Whether the atkData field is set.
     */
    boolean hasAtkData();
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_base_fighter atk_data = 3;</code>
     * @return The atkData.
     */
    org.gof.demo.worldsrv.msg.Define.p_base_fighter getAtkData();
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_base_fighter atk_data = 3;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_base_fighterOrBuilder getAtkDataOrBuilder();

    /**
     * <code>.org.gof.demo.worldsrv.msg.p_base_fighter def_data = 4;</code>
     * @return Whether the defData field is set.
     */
    boolean hasDefData();
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_base_fighter def_data = 4;</code>
     * @return The defData.
     */
    org.gof.demo.worldsrv.msg.Define.p_base_fighter getDefData();
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_base_fighter def_data = 4;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_base_fighterOrBuilder getDefDataOrBuilder();

    /**
     * <code>.org.gof.demo.worldsrv.msg.p_head target_head = 5;</code>
     * @return Whether the targetHead field is set.
     */
    boolean hasTargetHead();
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_head target_head = 5;</code>
     * @return The targetHead.
     */
    org.gof.demo.worldsrv.msg.Define.p_head getTargetHead();
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_head target_head = 5;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_headOrBuilder getTargetHeadOrBuilder();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.solo_result_s2c}
   */
  public static final class solo_result_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.solo_result_s2c)
      solo_result_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use solo_result_s2c.newBuilder() to construct.
    private solo_result_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private solo_result_s2c() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new solo_result_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgSolo.internal_static_org_gof_demo_worldsrv_msg_solo_result_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgSolo.internal_static_org_gof_demo_worldsrv_msg_solo_result_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgSolo.solo_result_s2c.class, org.gof.demo.worldsrv.msg.MsgSolo.solo_result_s2c.Builder.class);
    }

    private int bitField0_;
    public static final int VID_FIELD_NUMBER = 1;
    private long vid_ = 0L;
    /**
     * <code>uint64 vid = 1;</code>
     * @return The vid.
     */
    @java.lang.Override
    public long getVid() {
      return vid_;
    }

    public static final int WINNER_FIELD_NUMBER = 2;
    private long winner_ = 0L;
    /**
     * <code>uint64 winner = 2;</code>
     * @return The winner.
     */
    @java.lang.Override
    public long getWinner() {
      return winner_;
    }

    public static final int ATK_DATA_FIELD_NUMBER = 3;
    private org.gof.demo.worldsrv.msg.Define.p_base_fighter atkData_;
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_base_fighter atk_data = 3;</code>
     * @return Whether the atkData field is set.
     */
    @java.lang.Override
    public boolean hasAtkData() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_base_fighter atk_data = 3;</code>
     * @return The atkData.
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_base_fighter getAtkData() {
      return atkData_ == null ? org.gof.demo.worldsrv.msg.Define.p_base_fighter.getDefaultInstance() : atkData_;
    }
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_base_fighter atk_data = 3;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_base_fighterOrBuilder getAtkDataOrBuilder() {
      return atkData_ == null ? org.gof.demo.worldsrv.msg.Define.p_base_fighter.getDefaultInstance() : atkData_;
    }

    public static final int DEF_DATA_FIELD_NUMBER = 4;
    private org.gof.demo.worldsrv.msg.Define.p_base_fighter defData_;
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_base_fighter def_data = 4;</code>
     * @return Whether the defData field is set.
     */
    @java.lang.Override
    public boolean hasDefData() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_base_fighter def_data = 4;</code>
     * @return The defData.
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_base_fighter getDefData() {
      return defData_ == null ? org.gof.demo.worldsrv.msg.Define.p_base_fighter.getDefaultInstance() : defData_;
    }
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_base_fighter def_data = 4;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_base_fighterOrBuilder getDefDataOrBuilder() {
      return defData_ == null ? org.gof.demo.worldsrv.msg.Define.p_base_fighter.getDefaultInstance() : defData_;
    }

    public static final int TARGET_HEAD_FIELD_NUMBER = 5;
    private org.gof.demo.worldsrv.msg.Define.p_head targetHead_;
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_head target_head = 5;</code>
     * @return Whether the targetHead field is set.
     */
    @java.lang.Override
    public boolean hasTargetHead() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_head target_head = 5;</code>
     * @return The targetHead.
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_head getTargetHead() {
      return targetHead_ == null ? org.gof.demo.worldsrv.msg.Define.p_head.getDefaultInstance() : targetHead_;
    }
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_head target_head = 5;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_headOrBuilder getTargetHeadOrBuilder() {
      return targetHead_ == null ? org.gof.demo.worldsrv.msg.Define.p_head.getDefaultInstance() : targetHead_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (vid_ != 0L) {
        output.writeUInt64(1, vid_);
      }
      if (winner_ != 0L) {
        output.writeUInt64(2, winner_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(3, getAtkData());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(4, getDefData());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeMessage(5, getTargetHead());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (vid_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(1, vid_);
      }
      if (winner_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(2, winner_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getAtkData());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, getDefData());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, getTargetHead());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgSolo.solo_result_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgSolo.solo_result_s2c other = (org.gof.demo.worldsrv.msg.MsgSolo.solo_result_s2c) obj;

      if (getVid()
          != other.getVid()) return false;
      if (getWinner()
          != other.getWinner()) return false;
      if (hasAtkData() != other.hasAtkData()) return false;
      if (hasAtkData()) {
        if (!getAtkData()
            .equals(other.getAtkData())) return false;
      }
      if (hasDefData() != other.hasDefData()) return false;
      if (hasDefData()) {
        if (!getDefData()
            .equals(other.getDefData())) return false;
      }
      if (hasTargetHead() != other.hasTargetHead()) return false;
      if (hasTargetHead()) {
        if (!getTargetHead()
            .equals(other.getTargetHead())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + VID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getVid());
      hash = (37 * hash) + WINNER_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getWinner());
      if (hasAtkData()) {
        hash = (37 * hash) + ATK_DATA_FIELD_NUMBER;
        hash = (53 * hash) + getAtkData().hashCode();
      }
      if (hasDefData()) {
        hash = (37 * hash) + DEF_DATA_FIELD_NUMBER;
        hash = (53 * hash) + getDefData().hashCode();
      }
      if (hasTargetHead()) {
        hash = (37 * hash) + TARGET_HEAD_FIELD_NUMBER;
        hash = (53 * hash) + getTargetHead().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_result_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_result_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_result_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_result_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_result_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_result_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_result_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_result_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_result_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_result_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_result_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_result_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgSolo.solo_result_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.solo_result_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.solo_result_s2c)
        org.gof.demo.worldsrv.msg.MsgSolo.solo_result_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgSolo.internal_static_org_gof_demo_worldsrv_msg_solo_result_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgSolo.internal_static_org_gof_demo_worldsrv_msg_solo_result_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgSolo.solo_result_s2c.class, org.gof.demo.worldsrv.msg.MsgSolo.solo_result_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgSolo.solo_result_s2c.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getAtkDataFieldBuilder();
          getDefDataFieldBuilder();
          getTargetHeadFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        vid_ = 0L;
        winner_ = 0L;
        atkData_ = null;
        if (atkDataBuilder_ != null) {
          atkDataBuilder_.dispose();
          atkDataBuilder_ = null;
        }
        defData_ = null;
        if (defDataBuilder_ != null) {
          defDataBuilder_.dispose();
          defDataBuilder_ = null;
        }
        targetHead_ = null;
        if (targetHeadBuilder_ != null) {
          targetHeadBuilder_.dispose();
          targetHeadBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgSolo.internal_static_org_gof_demo_worldsrv_msg_solo_result_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSolo.solo_result_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgSolo.solo_result_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSolo.solo_result_s2c build() {
        org.gof.demo.worldsrv.msg.MsgSolo.solo_result_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSolo.solo_result_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgSolo.solo_result_s2c result = new org.gof.demo.worldsrv.msg.MsgSolo.solo_result_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgSolo.solo_result_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.vid_ = vid_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.winner_ = winner_;
        }
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.atkData_ = atkDataBuilder_ == null
              ? atkData_
              : atkDataBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.defData_ = defDataBuilder_ == null
              ? defData_
              : defDataBuilder_.build();
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.targetHead_ = targetHeadBuilder_ == null
              ? targetHead_
              : targetHeadBuilder_.build();
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgSolo.solo_result_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgSolo.solo_result_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgSolo.solo_result_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgSolo.solo_result_s2c.getDefaultInstance()) return this;
        if (other.getVid() != 0L) {
          setVid(other.getVid());
        }
        if (other.getWinner() != 0L) {
          setWinner(other.getWinner());
        }
        if (other.hasAtkData()) {
          mergeAtkData(other.getAtkData());
        }
        if (other.hasDefData()) {
          mergeDefData(other.getDefData());
        }
        if (other.hasTargetHead()) {
          mergeTargetHead(other.getTargetHead());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                vid_ = input.readUInt64();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                winner_ = input.readUInt64();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 26: {
                input.readMessage(
                    getAtkDataFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000004;
                break;
              } // case 26
              case 34: {
                input.readMessage(
                    getDefDataFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000008;
                break;
              } // case 34
              case 42: {
                input.readMessage(
                    getTargetHeadFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000010;
                break;
              } // case 42
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private long vid_ ;
      /**
       * <code>uint64 vid = 1;</code>
       * @return The vid.
       */
      @java.lang.Override
      public long getVid() {
        return vid_;
      }
      /**
       * <code>uint64 vid = 1;</code>
       * @param value The vid to set.
       * @return This builder for chaining.
       */
      public Builder setVid(long value) {

        vid_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 vid = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearVid() {
        bitField0_ = (bitField0_ & ~0x00000001);
        vid_ = 0L;
        onChanged();
        return this;
      }

      private long winner_ ;
      /**
       * <code>uint64 winner = 2;</code>
       * @return The winner.
       */
      @java.lang.Override
      public long getWinner() {
        return winner_;
      }
      /**
       * <code>uint64 winner = 2;</code>
       * @param value The winner to set.
       * @return This builder for chaining.
       */
      public Builder setWinner(long value) {

        winner_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 winner = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearWinner() {
        bitField0_ = (bitField0_ & ~0x00000002);
        winner_ = 0L;
        onChanged();
        return this;
      }

      private org.gof.demo.worldsrv.msg.Define.p_base_fighter atkData_;
      private com.google.protobuf.SingleFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_base_fighter, org.gof.demo.worldsrv.msg.Define.p_base_fighter.Builder, org.gof.demo.worldsrv.msg.Define.p_base_fighterOrBuilder> atkDataBuilder_;
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter atk_data = 3;</code>
       * @return Whether the atkData field is set.
       */
      public boolean hasAtkData() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter atk_data = 3;</code>
       * @return The atkData.
       */
      public org.gof.demo.worldsrv.msg.Define.p_base_fighter getAtkData() {
        if (atkDataBuilder_ == null) {
          return atkData_ == null ? org.gof.demo.worldsrv.msg.Define.p_base_fighter.getDefaultInstance() : atkData_;
        } else {
          return atkDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter atk_data = 3;</code>
       */
      public Builder setAtkData(org.gof.demo.worldsrv.msg.Define.p_base_fighter value) {
        if (atkDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          atkData_ = value;
        } else {
          atkDataBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter atk_data = 3;</code>
       */
      public Builder setAtkData(
          org.gof.demo.worldsrv.msg.Define.p_base_fighter.Builder builderForValue) {
        if (atkDataBuilder_ == null) {
          atkData_ = builderForValue.build();
        } else {
          atkDataBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter atk_data = 3;</code>
       */
      public Builder mergeAtkData(org.gof.demo.worldsrv.msg.Define.p_base_fighter value) {
        if (atkDataBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0) &&
            atkData_ != null &&
            atkData_ != org.gof.demo.worldsrv.msg.Define.p_base_fighter.getDefaultInstance()) {
            getAtkDataBuilder().mergeFrom(value);
          } else {
            atkData_ = value;
          }
        } else {
          atkDataBuilder_.mergeFrom(value);
        }
        if (atkData_ != null) {
          bitField0_ |= 0x00000004;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter atk_data = 3;</code>
       */
      public Builder clearAtkData() {
        bitField0_ = (bitField0_ & ~0x00000004);
        atkData_ = null;
        if (atkDataBuilder_ != null) {
          atkDataBuilder_.dispose();
          atkDataBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter atk_data = 3;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_base_fighter.Builder getAtkDataBuilder() {
        bitField0_ |= 0x00000004;
        onChanged();
        return getAtkDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter atk_data = 3;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_base_fighterOrBuilder getAtkDataOrBuilder() {
        if (atkDataBuilder_ != null) {
          return atkDataBuilder_.getMessageOrBuilder();
        } else {
          return atkData_ == null ?
              org.gof.demo.worldsrv.msg.Define.p_base_fighter.getDefaultInstance() : atkData_;
        }
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter atk_data = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_base_fighter, org.gof.demo.worldsrv.msg.Define.p_base_fighter.Builder, org.gof.demo.worldsrv.msg.Define.p_base_fighterOrBuilder> 
          getAtkDataFieldBuilder() {
        if (atkDataBuilder_ == null) {
          atkDataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_base_fighter, org.gof.demo.worldsrv.msg.Define.p_base_fighter.Builder, org.gof.demo.worldsrv.msg.Define.p_base_fighterOrBuilder>(
                  getAtkData(),
                  getParentForChildren(),
                  isClean());
          atkData_ = null;
        }
        return atkDataBuilder_;
      }

      private org.gof.demo.worldsrv.msg.Define.p_base_fighter defData_;
      private com.google.protobuf.SingleFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_base_fighter, org.gof.demo.worldsrv.msg.Define.p_base_fighter.Builder, org.gof.demo.worldsrv.msg.Define.p_base_fighterOrBuilder> defDataBuilder_;
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter def_data = 4;</code>
       * @return Whether the defData field is set.
       */
      public boolean hasDefData() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter def_data = 4;</code>
       * @return The defData.
       */
      public org.gof.demo.worldsrv.msg.Define.p_base_fighter getDefData() {
        if (defDataBuilder_ == null) {
          return defData_ == null ? org.gof.demo.worldsrv.msg.Define.p_base_fighter.getDefaultInstance() : defData_;
        } else {
          return defDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter def_data = 4;</code>
       */
      public Builder setDefData(org.gof.demo.worldsrv.msg.Define.p_base_fighter value) {
        if (defDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          defData_ = value;
        } else {
          defDataBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter def_data = 4;</code>
       */
      public Builder setDefData(
          org.gof.demo.worldsrv.msg.Define.p_base_fighter.Builder builderForValue) {
        if (defDataBuilder_ == null) {
          defData_ = builderForValue.build();
        } else {
          defDataBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter def_data = 4;</code>
       */
      public Builder mergeDefData(org.gof.demo.worldsrv.msg.Define.p_base_fighter value) {
        if (defDataBuilder_ == null) {
          if (((bitField0_ & 0x00000008) != 0) &&
            defData_ != null &&
            defData_ != org.gof.demo.worldsrv.msg.Define.p_base_fighter.getDefaultInstance()) {
            getDefDataBuilder().mergeFrom(value);
          } else {
            defData_ = value;
          }
        } else {
          defDataBuilder_.mergeFrom(value);
        }
        if (defData_ != null) {
          bitField0_ |= 0x00000008;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter def_data = 4;</code>
       */
      public Builder clearDefData() {
        bitField0_ = (bitField0_ & ~0x00000008);
        defData_ = null;
        if (defDataBuilder_ != null) {
          defDataBuilder_.dispose();
          defDataBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter def_data = 4;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_base_fighter.Builder getDefDataBuilder() {
        bitField0_ |= 0x00000008;
        onChanged();
        return getDefDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter def_data = 4;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_base_fighterOrBuilder getDefDataOrBuilder() {
        if (defDataBuilder_ != null) {
          return defDataBuilder_.getMessageOrBuilder();
        } else {
          return defData_ == null ?
              org.gof.demo.worldsrv.msg.Define.p_base_fighter.getDefaultInstance() : defData_;
        }
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter def_data = 4;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_base_fighter, org.gof.demo.worldsrv.msg.Define.p_base_fighter.Builder, org.gof.demo.worldsrv.msg.Define.p_base_fighterOrBuilder> 
          getDefDataFieldBuilder() {
        if (defDataBuilder_ == null) {
          defDataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_base_fighter, org.gof.demo.worldsrv.msg.Define.p_base_fighter.Builder, org.gof.demo.worldsrv.msg.Define.p_base_fighterOrBuilder>(
                  getDefData(),
                  getParentForChildren(),
                  isClean());
          defData_ = null;
        }
        return defDataBuilder_;
      }

      private org.gof.demo.worldsrv.msg.Define.p_head targetHead_;
      private com.google.protobuf.SingleFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_head, org.gof.demo.worldsrv.msg.Define.p_head.Builder, org.gof.demo.worldsrv.msg.Define.p_headOrBuilder> targetHeadBuilder_;
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_head target_head = 5;</code>
       * @return Whether the targetHead field is set.
       */
      public boolean hasTargetHead() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_head target_head = 5;</code>
       * @return The targetHead.
       */
      public org.gof.demo.worldsrv.msg.Define.p_head getTargetHead() {
        if (targetHeadBuilder_ == null) {
          return targetHead_ == null ? org.gof.demo.worldsrv.msg.Define.p_head.getDefaultInstance() : targetHead_;
        } else {
          return targetHeadBuilder_.getMessage();
        }
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_head target_head = 5;</code>
       */
      public Builder setTargetHead(org.gof.demo.worldsrv.msg.Define.p_head value) {
        if (targetHeadBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          targetHead_ = value;
        } else {
          targetHeadBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_head target_head = 5;</code>
       */
      public Builder setTargetHead(
          org.gof.demo.worldsrv.msg.Define.p_head.Builder builderForValue) {
        if (targetHeadBuilder_ == null) {
          targetHead_ = builderForValue.build();
        } else {
          targetHeadBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_head target_head = 5;</code>
       */
      public Builder mergeTargetHead(org.gof.demo.worldsrv.msg.Define.p_head value) {
        if (targetHeadBuilder_ == null) {
          if (((bitField0_ & 0x00000010) != 0) &&
            targetHead_ != null &&
            targetHead_ != org.gof.demo.worldsrv.msg.Define.p_head.getDefaultInstance()) {
            getTargetHeadBuilder().mergeFrom(value);
          } else {
            targetHead_ = value;
          }
        } else {
          targetHeadBuilder_.mergeFrom(value);
        }
        if (targetHead_ != null) {
          bitField0_ |= 0x00000010;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_head target_head = 5;</code>
       */
      public Builder clearTargetHead() {
        bitField0_ = (bitField0_ & ~0x00000010);
        targetHead_ = null;
        if (targetHeadBuilder_ != null) {
          targetHeadBuilder_.dispose();
          targetHeadBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_head target_head = 5;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_head.Builder getTargetHeadBuilder() {
        bitField0_ |= 0x00000010;
        onChanged();
        return getTargetHeadFieldBuilder().getBuilder();
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_head target_head = 5;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_headOrBuilder getTargetHeadOrBuilder() {
        if (targetHeadBuilder_ != null) {
          return targetHeadBuilder_.getMessageOrBuilder();
        } else {
          return targetHead_ == null ?
              org.gof.demo.worldsrv.msg.Define.p_head.getDefaultInstance() : targetHead_;
        }
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_head target_head = 5;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_head, org.gof.demo.worldsrv.msg.Define.p_head.Builder, org.gof.demo.worldsrv.msg.Define.p_headOrBuilder> 
          getTargetHeadFieldBuilder() {
        if (targetHeadBuilder_ == null) {
          targetHeadBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_head, org.gof.demo.worldsrv.msg.Define.p_head.Builder, org.gof.demo.worldsrv.msg.Define.p_headOrBuilder>(
                  getTargetHead(),
                  getParentForChildren(),
                  isClean());
          targetHead_ = null;
        }
        return targetHeadBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.solo_result_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.solo_result_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgSolo.solo_result_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgSolo.solo_result_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_result_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<solo_result_s2c>
        PARSER = new com.google.protobuf.AbstractParser<solo_result_s2c>() {
      @java.lang.Override
      public solo_result_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<solo_result_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<solo_result_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgSolo.solo_result_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface solo_video_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.solo_video_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint64 vid = 1;</code>
     * @return The vid.
     */
    long getVid();

    /**
     * <code>uint32 source = 2;</code>
     * @return The source.
     */
    int getSource();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.solo_video_c2s}
   */
  public static final class solo_video_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.solo_video_c2s)
      solo_video_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use solo_video_c2s.newBuilder() to construct.
    private solo_video_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private solo_video_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new solo_video_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgSolo.internal_static_org_gof_demo_worldsrv_msg_solo_video_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgSolo.internal_static_org_gof_demo_worldsrv_msg_solo_video_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgSolo.solo_video_c2s.class, org.gof.demo.worldsrv.msg.MsgSolo.solo_video_c2s.Builder.class);
    }

    public static final int VID_FIELD_NUMBER = 1;
    private long vid_ = 0L;
    /**
     * <code>uint64 vid = 1;</code>
     * @return The vid.
     */
    @java.lang.Override
    public long getVid() {
      return vid_;
    }

    public static final int SOURCE_FIELD_NUMBER = 2;
    private int source_ = 0;
    /**
     * <code>uint32 source = 2;</code>
     * @return The source.
     */
    @java.lang.Override
    public int getSource() {
      return source_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (vid_ != 0L) {
        output.writeUInt64(1, vid_);
      }
      if (source_ != 0) {
        output.writeUInt32(2, source_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (vid_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(1, vid_);
      }
      if (source_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, source_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgSolo.solo_video_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgSolo.solo_video_c2s other = (org.gof.demo.worldsrv.msg.MsgSolo.solo_video_c2s) obj;

      if (getVid()
          != other.getVid()) return false;
      if (getSource()
          != other.getSource()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + VID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getVid());
      hash = (37 * hash) + SOURCE_FIELD_NUMBER;
      hash = (53 * hash) + getSource();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_video_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_video_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_video_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_video_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_video_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_video_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_video_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_video_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_video_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_video_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_video_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_video_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgSolo.solo_video_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.solo_video_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.solo_video_c2s)
        org.gof.demo.worldsrv.msg.MsgSolo.solo_video_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgSolo.internal_static_org_gof_demo_worldsrv_msg_solo_video_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgSolo.internal_static_org_gof_demo_worldsrv_msg_solo_video_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgSolo.solo_video_c2s.class, org.gof.demo.worldsrv.msg.MsgSolo.solo_video_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgSolo.solo_video_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        vid_ = 0L;
        source_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgSolo.internal_static_org_gof_demo_worldsrv_msg_solo_video_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSolo.solo_video_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgSolo.solo_video_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSolo.solo_video_c2s build() {
        org.gof.demo.worldsrv.msg.MsgSolo.solo_video_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSolo.solo_video_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgSolo.solo_video_c2s result = new org.gof.demo.worldsrv.msg.MsgSolo.solo_video_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgSolo.solo_video_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.vid_ = vid_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.source_ = source_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgSolo.solo_video_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgSolo.solo_video_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgSolo.solo_video_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgSolo.solo_video_c2s.getDefaultInstance()) return this;
        if (other.getVid() != 0L) {
          setVid(other.getVid());
        }
        if (other.getSource() != 0) {
          setSource(other.getSource());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                vid_ = input.readUInt64();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                source_ = input.readUInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private long vid_ ;
      /**
       * <code>uint64 vid = 1;</code>
       * @return The vid.
       */
      @java.lang.Override
      public long getVid() {
        return vid_;
      }
      /**
       * <code>uint64 vid = 1;</code>
       * @param value The vid to set.
       * @return This builder for chaining.
       */
      public Builder setVid(long value) {

        vid_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 vid = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearVid() {
        bitField0_ = (bitField0_ & ~0x00000001);
        vid_ = 0L;
        onChanged();
        return this;
      }

      private int source_ ;
      /**
       * <code>uint32 source = 2;</code>
       * @return The source.
       */
      @java.lang.Override
      public int getSource() {
        return source_;
      }
      /**
       * <code>uint32 source = 2;</code>
       * @param value The source to set.
       * @return This builder for chaining.
       */
      public Builder setSource(int value) {

        source_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 source = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearSource() {
        bitField0_ = (bitField0_ & ~0x00000002);
        source_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.solo_video_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.solo_video_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgSolo.solo_video_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgSolo.solo_video_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_video_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<solo_video_c2s>
        PARSER = new com.google.protobuf.AbstractParser<solo_video_c2s>() {
      @java.lang.Override
      public solo_video_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<solo_video_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<solo_video_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgSolo.solo_video_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface solo_video_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.solo_video_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <code>uint64 vid = 2;</code>
     * @return The vid.
     */
    long getVid();

    /**
     * <code>uint64 seed = 3;</code>
     * @return The seed.
     */
    long getSeed();

    /**
     * <code>.org.gof.demo.worldsrv.msg.p_base_fighter atk_data = 4;</code>
     * @return Whether the atkData field is set.
     */
    boolean hasAtkData();
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_base_fighter atk_data = 4;</code>
     * @return The atkData.
     */
    org.gof.demo.worldsrv.msg.Define.p_base_fighter getAtkData();
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_base_fighter atk_data = 4;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_base_fighterOrBuilder getAtkDataOrBuilder();

    /**
     * <code>.org.gof.demo.worldsrv.msg.p_base_fighter def_data = 5;</code>
     * @return Whether the defData field is set.
     */
    boolean hasDefData();
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_base_fighter def_data = 5;</code>
     * @return The defData.
     */
    org.gof.demo.worldsrv.msg.Define.p_base_fighter getDefData();
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_base_fighter def_data = 5;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_base_fighterOrBuilder getDefDataOrBuilder();

    /**
     * <code>uint32 source = 6;</code>
     * @return The source.
     */
    int getSource();

    /**
     * <code>uint64 winner_id = 7;</code>
     * @return The winnerId.
     */
    long getWinnerId();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.solo_video_s2c}
   */
  public static final class solo_video_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.solo_video_s2c)
      solo_video_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use solo_video_s2c.newBuilder() to construct.
    private solo_video_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private solo_video_s2c() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new solo_video_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgSolo.internal_static_org_gof_demo_worldsrv_msg_solo_video_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgSolo.internal_static_org_gof_demo_worldsrv_msg_solo_video_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgSolo.solo_video_s2c.class, org.gof.demo.worldsrv.msg.MsgSolo.solo_video_s2c.Builder.class);
    }

    private int bitField0_;
    public static final int CODE_FIELD_NUMBER = 1;
    private int code_ = 0;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int VID_FIELD_NUMBER = 2;
    private long vid_ = 0L;
    /**
     * <code>uint64 vid = 2;</code>
     * @return The vid.
     */
    @java.lang.Override
    public long getVid() {
      return vid_;
    }

    public static final int SEED_FIELD_NUMBER = 3;
    private long seed_ = 0L;
    /**
     * <code>uint64 seed = 3;</code>
     * @return The seed.
     */
    @java.lang.Override
    public long getSeed() {
      return seed_;
    }

    public static final int ATK_DATA_FIELD_NUMBER = 4;
    private org.gof.demo.worldsrv.msg.Define.p_base_fighter atkData_;
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_base_fighter atk_data = 4;</code>
     * @return Whether the atkData field is set.
     */
    @java.lang.Override
    public boolean hasAtkData() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_base_fighter atk_data = 4;</code>
     * @return The atkData.
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_base_fighter getAtkData() {
      return atkData_ == null ? org.gof.demo.worldsrv.msg.Define.p_base_fighter.getDefaultInstance() : atkData_;
    }
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_base_fighter atk_data = 4;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_base_fighterOrBuilder getAtkDataOrBuilder() {
      return atkData_ == null ? org.gof.demo.worldsrv.msg.Define.p_base_fighter.getDefaultInstance() : atkData_;
    }

    public static final int DEF_DATA_FIELD_NUMBER = 5;
    private org.gof.demo.worldsrv.msg.Define.p_base_fighter defData_;
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_base_fighter def_data = 5;</code>
     * @return Whether the defData field is set.
     */
    @java.lang.Override
    public boolean hasDefData() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_base_fighter def_data = 5;</code>
     * @return The defData.
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_base_fighter getDefData() {
      return defData_ == null ? org.gof.demo.worldsrv.msg.Define.p_base_fighter.getDefaultInstance() : defData_;
    }
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_base_fighter def_data = 5;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_base_fighterOrBuilder getDefDataOrBuilder() {
      return defData_ == null ? org.gof.demo.worldsrv.msg.Define.p_base_fighter.getDefaultInstance() : defData_;
    }

    public static final int SOURCE_FIELD_NUMBER = 6;
    private int source_ = 0;
    /**
     * <code>uint32 source = 6;</code>
     * @return The source.
     */
    @java.lang.Override
    public int getSource() {
      return source_;
    }

    public static final int WINNER_ID_FIELD_NUMBER = 7;
    private long winnerId_ = 0L;
    /**
     * <code>uint64 winner_id = 7;</code>
     * @return The winnerId.
     */
    @java.lang.Override
    public long getWinnerId() {
      return winnerId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (vid_ != 0L) {
        output.writeUInt64(2, vid_);
      }
      if (seed_ != 0L) {
        output.writeUInt64(3, seed_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(4, getAtkData());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(5, getDefData());
      }
      if (source_ != 0) {
        output.writeUInt32(6, source_);
      }
      if (winnerId_ != 0L) {
        output.writeUInt64(7, winnerId_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (vid_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(2, vid_);
      }
      if (seed_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(3, seed_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, getAtkData());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, getDefData());
      }
      if (source_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(6, source_);
      }
      if (winnerId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(7, winnerId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgSolo.solo_video_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgSolo.solo_video_s2c other = (org.gof.demo.worldsrv.msg.MsgSolo.solo_video_s2c) obj;

      if (getCode()
          != other.getCode()) return false;
      if (getVid()
          != other.getVid()) return false;
      if (getSeed()
          != other.getSeed()) return false;
      if (hasAtkData() != other.hasAtkData()) return false;
      if (hasAtkData()) {
        if (!getAtkData()
            .equals(other.getAtkData())) return false;
      }
      if (hasDefData() != other.hasDefData()) return false;
      if (hasDefData()) {
        if (!getDefData()
            .equals(other.getDefData())) return false;
      }
      if (getSource()
          != other.getSource()) return false;
      if (getWinnerId()
          != other.getWinnerId()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      hash = (37 * hash) + VID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getVid());
      hash = (37 * hash) + SEED_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getSeed());
      if (hasAtkData()) {
        hash = (37 * hash) + ATK_DATA_FIELD_NUMBER;
        hash = (53 * hash) + getAtkData().hashCode();
      }
      if (hasDefData()) {
        hash = (37 * hash) + DEF_DATA_FIELD_NUMBER;
        hash = (53 * hash) + getDefData().hashCode();
      }
      hash = (37 * hash) + SOURCE_FIELD_NUMBER;
      hash = (53 * hash) + getSource();
      hash = (37 * hash) + WINNER_ID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getWinnerId());
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_video_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_video_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_video_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_video_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_video_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_video_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_video_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_video_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_video_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_video_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_video_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_video_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgSolo.solo_video_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.solo_video_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.solo_video_s2c)
        org.gof.demo.worldsrv.msg.MsgSolo.solo_video_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgSolo.internal_static_org_gof_demo_worldsrv_msg_solo_video_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgSolo.internal_static_org_gof_demo_worldsrv_msg_solo_video_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgSolo.solo_video_s2c.class, org.gof.demo.worldsrv.msg.MsgSolo.solo_video_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgSolo.solo_video_s2c.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getAtkDataFieldBuilder();
          getDefDataFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        code_ = 0;
        vid_ = 0L;
        seed_ = 0L;
        atkData_ = null;
        if (atkDataBuilder_ != null) {
          atkDataBuilder_.dispose();
          atkDataBuilder_ = null;
        }
        defData_ = null;
        if (defDataBuilder_ != null) {
          defDataBuilder_.dispose();
          defDataBuilder_ = null;
        }
        source_ = 0;
        winnerId_ = 0L;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgSolo.internal_static_org_gof_demo_worldsrv_msg_solo_video_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSolo.solo_video_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgSolo.solo_video_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSolo.solo_video_s2c build() {
        org.gof.demo.worldsrv.msg.MsgSolo.solo_video_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSolo.solo_video_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgSolo.solo_video_s2c result = new org.gof.demo.worldsrv.msg.MsgSolo.solo_video_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgSolo.solo_video_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.code_ = code_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.vid_ = vid_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.seed_ = seed_;
        }
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.atkData_ = atkDataBuilder_ == null
              ? atkData_
              : atkDataBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.defData_ = defDataBuilder_ == null
              ? defData_
              : defDataBuilder_.build();
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.source_ = source_;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.winnerId_ = winnerId_;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgSolo.solo_video_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgSolo.solo_video_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgSolo.solo_video_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgSolo.solo_video_s2c.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.getVid() != 0L) {
          setVid(other.getVid());
        }
        if (other.getSeed() != 0L) {
          setSeed(other.getSeed());
        }
        if (other.hasAtkData()) {
          mergeAtkData(other.getAtkData());
        }
        if (other.hasDefData()) {
          mergeDefData(other.getDefData());
        }
        if (other.getSource() != 0) {
          setSource(other.getSource());
        }
        if (other.getWinnerId() != 0L) {
          setWinnerId(other.getWinnerId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                code_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                vid_ = input.readUInt64();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                seed_ = input.readUInt64();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              case 34: {
                input.readMessage(
                    getAtkDataFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000008;
                break;
              } // case 34
              case 42: {
                input.readMessage(
                    getDefDataFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000010;
                break;
              } // case 42
              case 48: {
                source_ = input.readUInt32();
                bitField0_ |= 0x00000020;
                break;
              } // case 48
              case 56: {
                winnerId_ = input.readUInt64();
                bitField0_ |= 0x00000040;
                break;
              } // case 56
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {

        code_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        bitField0_ = (bitField0_ & ~0x00000001);
        code_ = 0;
        onChanged();
        return this;
      }

      private long vid_ ;
      /**
       * <code>uint64 vid = 2;</code>
       * @return The vid.
       */
      @java.lang.Override
      public long getVid() {
        return vid_;
      }
      /**
       * <code>uint64 vid = 2;</code>
       * @param value The vid to set.
       * @return This builder for chaining.
       */
      public Builder setVid(long value) {

        vid_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 vid = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearVid() {
        bitField0_ = (bitField0_ & ~0x00000002);
        vid_ = 0L;
        onChanged();
        return this;
      }

      private long seed_ ;
      /**
       * <code>uint64 seed = 3;</code>
       * @return The seed.
       */
      @java.lang.Override
      public long getSeed() {
        return seed_;
      }
      /**
       * <code>uint64 seed = 3;</code>
       * @param value The seed to set.
       * @return This builder for chaining.
       */
      public Builder setSeed(long value) {

        seed_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 seed = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearSeed() {
        bitField0_ = (bitField0_ & ~0x00000004);
        seed_ = 0L;
        onChanged();
        return this;
      }

      private org.gof.demo.worldsrv.msg.Define.p_base_fighter atkData_;
      private com.google.protobuf.SingleFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_base_fighter, org.gof.demo.worldsrv.msg.Define.p_base_fighter.Builder, org.gof.demo.worldsrv.msg.Define.p_base_fighterOrBuilder> atkDataBuilder_;
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter atk_data = 4;</code>
       * @return Whether the atkData field is set.
       */
      public boolean hasAtkData() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter atk_data = 4;</code>
       * @return The atkData.
       */
      public org.gof.demo.worldsrv.msg.Define.p_base_fighter getAtkData() {
        if (atkDataBuilder_ == null) {
          return atkData_ == null ? org.gof.demo.worldsrv.msg.Define.p_base_fighter.getDefaultInstance() : atkData_;
        } else {
          return atkDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter atk_data = 4;</code>
       */
      public Builder setAtkData(org.gof.demo.worldsrv.msg.Define.p_base_fighter value) {
        if (atkDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          atkData_ = value;
        } else {
          atkDataBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter atk_data = 4;</code>
       */
      public Builder setAtkData(
          org.gof.demo.worldsrv.msg.Define.p_base_fighter.Builder builderForValue) {
        if (atkDataBuilder_ == null) {
          atkData_ = builderForValue.build();
        } else {
          atkDataBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter atk_data = 4;</code>
       */
      public Builder mergeAtkData(org.gof.demo.worldsrv.msg.Define.p_base_fighter value) {
        if (atkDataBuilder_ == null) {
          if (((bitField0_ & 0x00000008) != 0) &&
            atkData_ != null &&
            atkData_ != org.gof.demo.worldsrv.msg.Define.p_base_fighter.getDefaultInstance()) {
            getAtkDataBuilder().mergeFrom(value);
          } else {
            atkData_ = value;
          }
        } else {
          atkDataBuilder_.mergeFrom(value);
        }
        if (atkData_ != null) {
          bitField0_ |= 0x00000008;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter atk_data = 4;</code>
       */
      public Builder clearAtkData() {
        bitField0_ = (bitField0_ & ~0x00000008);
        atkData_ = null;
        if (atkDataBuilder_ != null) {
          atkDataBuilder_.dispose();
          atkDataBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter atk_data = 4;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_base_fighter.Builder getAtkDataBuilder() {
        bitField0_ |= 0x00000008;
        onChanged();
        return getAtkDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter atk_data = 4;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_base_fighterOrBuilder getAtkDataOrBuilder() {
        if (atkDataBuilder_ != null) {
          return atkDataBuilder_.getMessageOrBuilder();
        } else {
          return atkData_ == null ?
              org.gof.demo.worldsrv.msg.Define.p_base_fighter.getDefaultInstance() : atkData_;
        }
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter atk_data = 4;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_base_fighter, org.gof.demo.worldsrv.msg.Define.p_base_fighter.Builder, org.gof.demo.worldsrv.msg.Define.p_base_fighterOrBuilder> 
          getAtkDataFieldBuilder() {
        if (atkDataBuilder_ == null) {
          atkDataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_base_fighter, org.gof.demo.worldsrv.msg.Define.p_base_fighter.Builder, org.gof.demo.worldsrv.msg.Define.p_base_fighterOrBuilder>(
                  getAtkData(),
                  getParentForChildren(),
                  isClean());
          atkData_ = null;
        }
        return atkDataBuilder_;
      }

      private org.gof.demo.worldsrv.msg.Define.p_base_fighter defData_;
      private com.google.protobuf.SingleFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_base_fighter, org.gof.demo.worldsrv.msg.Define.p_base_fighter.Builder, org.gof.demo.worldsrv.msg.Define.p_base_fighterOrBuilder> defDataBuilder_;
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter def_data = 5;</code>
       * @return Whether the defData field is set.
       */
      public boolean hasDefData() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter def_data = 5;</code>
       * @return The defData.
       */
      public org.gof.demo.worldsrv.msg.Define.p_base_fighter getDefData() {
        if (defDataBuilder_ == null) {
          return defData_ == null ? org.gof.demo.worldsrv.msg.Define.p_base_fighter.getDefaultInstance() : defData_;
        } else {
          return defDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter def_data = 5;</code>
       */
      public Builder setDefData(org.gof.demo.worldsrv.msg.Define.p_base_fighter value) {
        if (defDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          defData_ = value;
        } else {
          defDataBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter def_data = 5;</code>
       */
      public Builder setDefData(
          org.gof.demo.worldsrv.msg.Define.p_base_fighter.Builder builderForValue) {
        if (defDataBuilder_ == null) {
          defData_ = builderForValue.build();
        } else {
          defDataBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter def_data = 5;</code>
       */
      public Builder mergeDefData(org.gof.demo.worldsrv.msg.Define.p_base_fighter value) {
        if (defDataBuilder_ == null) {
          if (((bitField0_ & 0x00000010) != 0) &&
            defData_ != null &&
            defData_ != org.gof.demo.worldsrv.msg.Define.p_base_fighter.getDefaultInstance()) {
            getDefDataBuilder().mergeFrom(value);
          } else {
            defData_ = value;
          }
        } else {
          defDataBuilder_.mergeFrom(value);
        }
        if (defData_ != null) {
          bitField0_ |= 0x00000010;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter def_data = 5;</code>
       */
      public Builder clearDefData() {
        bitField0_ = (bitField0_ & ~0x00000010);
        defData_ = null;
        if (defDataBuilder_ != null) {
          defDataBuilder_.dispose();
          defDataBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter def_data = 5;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_base_fighter.Builder getDefDataBuilder() {
        bitField0_ |= 0x00000010;
        onChanged();
        return getDefDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter def_data = 5;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_base_fighterOrBuilder getDefDataOrBuilder() {
        if (defDataBuilder_ != null) {
          return defDataBuilder_.getMessageOrBuilder();
        } else {
          return defData_ == null ?
              org.gof.demo.worldsrv.msg.Define.p_base_fighter.getDefaultInstance() : defData_;
        }
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_base_fighter def_data = 5;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_base_fighter, org.gof.demo.worldsrv.msg.Define.p_base_fighter.Builder, org.gof.demo.worldsrv.msg.Define.p_base_fighterOrBuilder> 
          getDefDataFieldBuilder() {
        if (defDataBuilder_ == null) {
          defDataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_base_fighter, org.gof.demo.worldsrv.msg.Define.p_base_fighter.Builder, org.gof.demo.worldsrv.msg.Define.p_base_fighterOrBuilder>(
                  getDefData(),
                  getParentForChildren(),
                  isClean());
          defData_ = null;
        }
        return defDataBuilder_;
      }

      private int source_ ;
      /**
       * <code>uint32 source = 6;</code>
       * @return The source.
       */
      @java.lang.Override
      public int getSource() {
        return source_;
      }
      /**
       * <code>uint32 source = 6;</code>
       * @param value The source to set.
       * @return This builder for chaining.
       */
      public Builder setSource(int value) {

        source_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 source = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearSource() {
        bitField0_ = (bitField0_ & ~0x00000020);
        source_ = 0;
        onChanged();
        return this;
      }

      private long winnerId_ ;
      /**
       * <code>uint64 winner_id = 7;</code>
       * @return The winnerId.
       */
      @java.lang.Override
      public long getWinnerId() {
        return winnerId_;
      }
      /**
       * <code>uint64 winner_id = 7;</code>
       * @param value The winnerId to set.
       * @return This builder for chaining.
       */
      public Builder setWinnerId(long value) {

        winnerId_ = value;
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 winner_id = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearWinnerId() {
        bitField0_ = (bitField0_ & ~0x00000040);
        winnerId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.solo_video_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.solo_video_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgSolo.solo_video_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgSolo.solo_video_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_video_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<solo_video_s2c>
        PARSER = new com.google.protobuf.AbstractParser<solo_video_s2c>() {
      @java.lang.Override
      public solo_video_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<solo_video_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<solo_video_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgSolo.solo_video_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface solo_video_share_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.solo_video_share_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint64 vid = 1;</code>
     * @return The vid.
     */
    long getVid();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.solo_video_share_c2s}
   */
  public static final class solo_video_share_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.solo_video_share_c2s)
      solo_video_share_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use solo_video_share_c2s.newBuilder() to construct.
    private solo_video_share_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private solo_video_share_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new solo_video_share_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgSolo.internal_static_org_gof_demo_worldsrv_msg_solo_video_share_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgSolo.internal_static_org_gof_demo_worldsrv_msg_solo_video_share_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgSolo.solo_video_share_c2s.class, org.gof.demo.worldsrv.msg.MsgSolo.solo_video_share_c2s.Builder.class);
    }

    public static final int VID_FIELD_NUMBER = 1;
    private long vid_ = 0L;
    /**
     * <code>uint64 vid = 1;</code>
     * @return The vid.
     */
    @java.lang.Override
    public long getVid() {
      return vid_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (vid_ != 0L) {
        output.writeUInt64(1, vid_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (vid_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(1, vid_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgSolo.solo_video_share_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgSolo.solo_video_share_c2s other = (org.gof.demo.worldsrv.msg.MsgSolo.solo_video_share_c2s) obj;

      if (getVid()
          != other.getVid()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + VID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getVid());
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_video_share_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_video_share_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_video_share_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_video_share_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_video_share_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_video_share_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_video_share_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_video_share_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_video_share_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_video_share_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_video_share_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_video_share_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgSolo.solo_video_share_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.solo_video_share_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.solo_video_share_c2s)
        org.gof.demo.worldsrv.msg.MsgSolo.solo_video_share_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgSolo.internal_static_org_gof_demo_worldsrv_msg_solo_video_share_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgSolo.internal_static_org_gof_demo_worldsrv_msg_solo_video_share_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgSolo.solo_video_share_c2s.class, org.gof.demo.worldsrv.msg.MsgSolo.solo_video_share_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgSolo.solo_video_share_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        vid_ = 0L;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgSolo.internal_static_org_gof_demo_worldsrv_msg_solo_video_share_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSolo.solo_video_share_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgSolo.solo_video_share_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSolo.solo_video_share_c2s build() {
        org.gof.demo.worldsrv.msg.MsgSolo.solo_video_share_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgSolo.solo_video_share_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgSolo.solo_video_share_c2s result = new org.gof.demo.worldsrv.msg.MsgSolo.solo_video_share_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgSolo.solo_video_share_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.vid_ = vid_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgSolo.solo_video_share_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgSolo.solo_video_share_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgSolo.solo_video_share_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgSolo.solo_video_share_c2s.getDefaultInstance()) return this;
        if (other.getVid() != 0L) {
          setVid(other.getVid());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                vid_ = input.readUInt64();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private long vid_ ;
      /**
       * <code>uint64 vid = 1;</code>
       * @return The vid.
       */
      @java.lang.Override
      public long getVid() {
        return vid_;
      }
      /**
       * <code>uint64 vid = 1;</code>
       * @param value The vid to set.
       * @return This builder for chaining.
       */
      public Builder setVid(long value) {

        vid_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 vid = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearVid() {
        bitField0_ = (bitField0_ & ~0x00000001);
        vid_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.solo_video_share_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.solo_video_share_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgSolo.solo_video_share_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgSolo.solo_video_share_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgSolo.solo_video_share_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<solo_video_share_c2s>
        PARSER = new com.google.protobuf.AbstractParser<solo_video_share_c2s>() {
      @java.lang.Override
      public solo_video_share_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<solo_video_share_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<solo_video_share_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgSolo.solo_video_share_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_solo_start_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_solo_start_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_solo_start_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_solo_start_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_solo_result_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_solo_result_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_solo_result_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_solo_result_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_solo_video_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_solo_video_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_solo_video_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_solo_video_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_solo_video_share_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_solo_video_share_c2s_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\016msg.solo.proto\022\031org.gof.demo.worldsrv." +
      "msg\032\roptions.proto\032\014define.proto\"*\n\016solo" +
      "_start_c2s\022\021\n\ttarget_id\030\001 \001(\004:\005\210\303\032\201H\"\315\001\n" +
      "\016solo_start_s2c\022\014\n\004code\030\001 \001(\005\022\021\n\ttarget_" +
      "id\030\002 \001(\004\022\013\n\003vid\030\003 \001(\004\022\014\n\004seed\030\004 \001(\004\022;\n\010a" +
      "tk_data\030\005 \001(\0132).org.gof.demo.worldsrv.ms" +
      "g.p_base_fighter\022;\n\010def_data\030\006 \001(\0132).org" +
      ".gof.demo.worldsrv.msg.p_base_fighter:\005\210" +
      "\303\032\201H\"5\n\017solo_result_c2s\022\013\n\003vid\030\001 \001(\004\022\016\n\006" +
      "winner\030\002 \001(\004:\005\210\303\032\202H\"\347\001\n\017solo_result_s2c\022" +
      "\013\n\003vid\030\001 \001(\004\022\016\n\006winner\030\002 \001(\004\022;\n\010atk_data" +
      "\030\003 \001(\0132).org.gof.demo.worldsrv.msg.p_bas" +
      "e_fighter\022;\n\010def_data\030\004 \001(\0132).org.gof.de" +
      "mo.worldsrv.msg.p_base_fighter\0226\n\013target" +
      "_head\030\005 \001(\0132!.org.gof.demo.worldsrv.msg." +
      "p_head:\005\210\303\032\202H\"4\n\016solo_video_c2s\022\013\n\003vid\030\001" +
      " \001(\004\022\016\n\006source\030\002 \001(\r:\005\210\303\032\203H\"\335\001\n\016solo_vid" +
      "eo_s2c\022\014\n\004code\030\001 \001(\005\022\013\n\003vid\030\002 \001(\004\022\014\n\004see" +
      "d\030\003 \001(\004\022;\n\010atk_data\030\004 \001(\0132).org.gof.demo" +
      ".worldsrv.msg.p_base_fighter\022;\n\010def_data" +
      "\030\005 \001(\0132).org.gof.demo.worldsrv.msg.p_bas" +
      "e_fighter\022\016\n\006source\030\006 \001(\r\022\021\n\twinner_id\030\007" +
      " \001(\004:\005\210\303\032\203H\"*\n\024solo_video_share_c2s\022\013\n\003v" +
      "id\030\001 \001(\004:\005\210\303\032\204Hb\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          org.gof.demo.worldsrv.msg.Options.getDescriptor(),
          org.gof.demo.worldsrv.msg.Define.getDescriptor(),
        });
    internal_static_org_gof_demo_worldsrv_msg_solo_start_c2s_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_org_gof_demo_worldsrv_msg_solo_start_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_solo_start_c2s_descriptor,
        new java.lang.String[] { "TargetId", });
    internal_static_org_gof_demo_worldsrv_msg_solo_start_s2c_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_org_gof_demo_worldsrv_msg_solo_start_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_solo_start_s2c_descriptor,
        new java.lang.String[] { "Code", "TargetId", "Vid", "Seed", "AtkData", "DefData", });
    internal_static_org_gof_demo_worldsrv_msg_solo_result_c2s_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_org_gof_demo_worldsrv_msg_solo_result_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_solo_result_c2s_descriptor,
        new java.lang.String[] { "Vid", "Winner", });
    internal_static_org_gof_demo_worldsrv_msg_solo_result_s2c_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_org_gof_demo_worldsrv_msg_solo_result_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_solo_result_s2c_descriptor,
        new java.lang.String[] { "Vid", "Winner", "AtkData", "DefData", "TargetHead", });
    internal_static_org_gof_demo_worldsrv_msg_solo_video_c2s_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_org_gof_demo_worldsrv_msg_solo_video_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_solo_video_c2s_descriptor,
        new java.lang.String[] { "Vid", "Source", });
    internal_static_org_gof_demo_worldsrv_msg_solo_video_s2c_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_org_gof_demo_worldsrv_msg_solo_video_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_solo_video_s2c_descriptor,
        new java.lang.String[] { "Code", "Vid", "Seed", "AtkData", "DefData", "Source", "WinnerId", });
    internal_static_org_gof_demo_worldsrv_msg_solo_video_share_c2s_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_org_gof_demo_worldsrv_msg_solo_video_share_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_solo_video_share_c2s_descriptor,
        new java.lang.String[] { "Vid", });
    com.google.protobuf.ExtensionRegistry registry =
        com.google.protobuf.ExtensionRegistry.newInstance();
    registry.add(org.gof.demo.worldsrv.msg.Options.msgid);
    com.google.protobuf.Descriptors.FileDescriptor
        .internalUpdateFileDescriptor(descriptor, registry);
    org.gof.demo.worldsrv.msg.Options.getDescriptor();
    org.gof.demo.worldsrv.msg.Define.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
