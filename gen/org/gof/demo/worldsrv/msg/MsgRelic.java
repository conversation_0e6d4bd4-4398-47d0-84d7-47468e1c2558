// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: msg.relic.proto

// Protobuf Java Version: 3.25.3
package org.gof.demo.worldsrv.msg;

public final class MsgRelic {
  private MsgRelic() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface relic_info_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.relic_info_c2s)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.relic_info_c2s}
   */
  public static final class relic_info_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.relic_info_c2s)
      relic_info_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use relic_info_c2s.newBuilder() to construct.
    private relic_info_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private relic_info_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new relic_info_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_info_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_info_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgRelic.relic_info_c2s.class, org.gof.demo.worldsrv.msg.MsgRelic.relic_info_c2s.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgRelic.relic_info_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgRelic.relic_info_c2s other = (org.gof.demo.worldsrv.msg.MsgRelic.relic_info_c2s) obj;

      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_info_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_info_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_info_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_info_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_info_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_info_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_info_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_info_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_info_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_info_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_info_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_info_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgRelic.relic_info_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.relic_info_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.relic_info_c2s)
        org.gof.demo.worldsrv.msg.MsgRelic.relic_info_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_info_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_info_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgRelic.relic_info_c2s.class, org.gof.demo.worldsrv.msg.MsgRelic.relic_info_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgRelic.relic_info_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_info_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_info_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgRelic.relic_info_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_info_c2s build() {
        org.gof.demo.worldsrv.msg.MsgRelic.relic_info_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_info_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgRelic.relic_info_c2s result = new org.gof.demo.worldsrv.msg.MsgRelic.relic_info_c2s(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgRelic.relic_info_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgRelic.relic_info_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgRelic.relic_info_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgRelic.relic_info_c2s.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.relic_info_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.relic_info_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgRelic.relic_info_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgRelic.relic_info_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_info_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<relic_info_c2s>
        PARSER = new com.google.protobuf.AbstractParser<relic_info_c2s>() {
      @java.lang.Override
      public relic_info_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<relic_info_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<relic_info_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgRelic.relic_info_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface relic_info_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.relic_info_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_relic relic_list = 1;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_relic> 
        getRelicListList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_relic relic_list = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_relic getRelicList(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_relic relic_list = 1;</code>
     */
    int getRelicListCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_relic relic_list = 1;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_relicOrBuilder> 
        getRelicListOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_relic relic_list = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_relicOrBuilder getRelicListOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.relic_info_s2c}
   */
  public static final class relic_info_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.relic_info_s2c)
      relic_info_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use relic_info_s2c.newBuilder() to construct.
    private relic_info_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private relic_info_s2c() {
      relicList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new relic_info_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_info_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_info_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgRelic.relic_info_s2c.class, org.gof.demo.worldsrv.msg.MsgRelic.relic_info_s2c.Builder.class);
    }

    public static final int RELIC_LIST_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_relic> relicList_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_relic relic_list = 1;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_relic> getRelicListList() {
      return relicList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_relic relic_list = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_relicOrBuilder> 
        getRelicListOrBuilderList() {
      return relicList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_relic relic_list = 1;</code>
     */
    @java.lang.Override
    public int getRelicListCount() {
      return relicList_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_relic relic_list = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_relic getRelicList(int index) {
      return relicList_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_relic relic_list = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_relicOrBuilder getRelicListOrBuilder(
        int index) {
      return relicList_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < relicList_.size(); i++) {
        output.writeMessage(1, relicList_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < relicList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, relicList_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgRelic.relic_info_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgRelic.relic_info_s2c other = (org.gof.demo.worldsrv.msg.MsgRelic.relic_info_s2c) obj;

      if (!getRelicListList()
          .equals(other.getRelicListList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getRelicListCount() > 0) {
        hash = (37 * hash) + RELIC_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getRelicListList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_info_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_info_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_info_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_info_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_info_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_info_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_info_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_info_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_info_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_info_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_info_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_info_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgRelic.relic_info_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.relic_info_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.relic_info_s2c)
        org.gof.demo.worldsrv.msg.MsgRelic.relic_info_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_info_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_info_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgRelic.relic_info_s2c.class, org.gof.demo.worldsrv.msg.MsgRelic.relic_info_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgRelic.relic_info_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        if (relicListBuilder_ == null) {
          relicList_ = java.util.Collections.emptyList();
        } else {
          relicList_ = null;
          relicListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_info_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_info_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgRelic.relic_info_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_info_s2c build() {
        org.gof.demo.worldsrv.msg.MsgRelic.relic_info_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_info_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgRelic.relic_info_s2c result = new org.gof.demo.worldsrv.msg.MsgRelic.relic_info_s2c(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgRelic.relic_info_s2c result) {
        if (relicListBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            relicList_ = java.util.Collections.unmodifiableList(relicList_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.relicList_ = relicList_;
        } else {
          result.relicList_ = relicListBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgRelic.relic_info_s2c result) {
        int from_bitField0_ = bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgRelic.relic_info_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgRelic.relic_info_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgRelic.relic_info_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgRelic.relic_info_s2c.getDefaultInstance()) return this;
        if (relicListBuilder_ == null) {
          if (!other.relicList_.isEmpty()) {
            if (relicList_.isEmpty()) {
              relicList_ = other.relicList_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureRelicListIsMutable();
              relicList_.addAll(other.relicList_);
            }
            onChanged();
          }
        } else {
          if (!other.relicList_.isEmpty()) {
            if (relicListBuilder_.isEmpty()) {
              relicListBuilder_.dispose();
              relicListBuilder_ = null;
              relicList_ = other.relicList_;
              bitField0_ = (bitField0_ & ~0x00000001);
              relicListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getRelicListFieldBuilder() : null;
            } else {
              relicListBuilder_.addAllMessages(other.relicList_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                org.gof.demo.worldsrv.msg.Define.p_relic m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_relic.parser(),
                        extensionRegistry);
                if (relicListBuilder_ == null) {
                  ensureRelicListIsMutable();
                  relicList_.add(m);
                } else {
                  relicListBuilder_.addMessage(m);
                }
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_relic> relicList_ =
        java.util.Collections.emptyList();
      private void ensureRelicListIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          relicList_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_relic>(relicList_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_relic, org.gof.demo.worldsrv.msg.Define.p_relic.Builder, org.gof.demo.worldsrv.msg.Define.p_relicOrBuilder> relicListBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic relic_list = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_relic> getRelicListList() {
        if (relicListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(relicList_);
        } else {
          return relicListBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic relic_list = 1;</code>
       */
      public int getRelicListCount() {
        if (relicListBuilder_ == null) {
          return relicList_.size();
        } else {
          return relicListBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic relic_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_relic getRelicList(int index) {
        if (relicListBuilder_ == null) {
          return relicList_.get(index);
        } else {
          return relicListBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic relic_list = 1;</code>
       */
      public Builder setRelicList(
          int index, org.gof.demo.worldsrv.msg.Define.p_relic value) {
        if (relicListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRelicListIsMutable();
          relicList_.set(index, value);
          onChanged();
        } else {
          relicListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic relic_list = 1;</code>
       */
      public Builder setRelicList(
          int index, org.gof.demo.worldsrv.msg.Define.p_relic.Builder builderForValue) {
        if (relicListBuilder_ == null) {
          ensureRelicListIsMutable();
          relicList_.set(index, builderForValue.build());
          onChanged();
        } else {
          relicListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic relic_list = 1;</code>
       */
      public Builder addRelicList(org.gof.demo.worldsrv.msg.Define.p_relic value) {
        if (relicListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRelicListIsMutable();
          relicList_.add(value);
          onChanged();
        } else {
          relicListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic relic_list = 1;</code>
       */
      public Builder addRelicList(
          int index, org.gof.demo.worldsrv.msg.Define.p_relic value) {
        if (relicListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRelicListIsMutable();
          relicList_.add(index, value);
          onChanged();
        } else {
          relicListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic relic_list = 1;</code>
       */
      public Builder addRelicList(
          org.gof.demo.worldsrv.msg.Define.p_relic.Builder builderForValue) {
        if (relicListBuilder_ == null) {
          ensureRelicListIsMutable();
          relicList_.add(builderForValue.build());
          onChanged();
        } else {
          relicListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic relic_list = 1;</code>
       */
      public Builder addRelicList(
          int index, org.gof.demo.worldsrv.msg.Define.p_relic.Builder builderForValue) {
        if (relicListBuilder_ == null) {
          ensureRelicListIsMutable();
          relicList_.add(index, builderForValue.build());
          onChanged();
        } else {
          relicListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic relic_list = 1;</code>
       */
      public Builder addAllRelicList(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_relic> values) {
        if (relicListBuilder_ == null) {
          ensureRelicListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, relicList_);
          onChanged();
        } else {
          relicListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic relic_list = 1;</code>
       */
      public Builder clearRelicList() {
        if (relicListBuilder_ == null) {
          relicList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          relicListBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic relic_list = 1;</code>
       */
      public Builder removeRelicList(int index) {
        if (relicListBuilder_ == null) {
          ensureRelicListIsMutable();
          relicList_.remove(index);
          onChanged();
        } else {
          relicListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic relic_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_relic.Builder getRelicListBuilder(
          int index) {
        return getRelicListFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic relic_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_relicOrBuilder getRelicListOrBuilder(
          int index) {
        if (relicListBuilder_ == null) {
          return relicList_.get(index);  } else {
          return relicListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic relic_list = 1;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_relicOrBuilder> 
           getRelicListOrBuilderList() {
        if (relicListBuilder_ != null) {
          return relicListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(relicList_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic relic_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_relic.Builder addRelicListBuilder() {
        return getRelicListFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_relic.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic relic_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_relic.Builder addRelicListBuilder(
          int index) {
        return getRelicListFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_relic.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic relic_list = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_relic.Builder> 
           getRelicListBuilderList() {
        return getRelicListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_relic, org.gof.demo.worldsrv.msg.Define.p_relic.Builder, org.gof.demo.worldsrv.msg.Define.p_relicOrBuilder> 
          getRelicListFieldBuilder() {
        if (relicListBuilder_ == null) {
          relicListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_relic, org.gof.demo.worldsrv.msg.Define.p_relic.Builder, org.gof.demo.worldsrv.msg.Define.p_relicOrBuilder>(
                  relicList_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          relicList_ = null;
        }
        return relicListBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.relic_info_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.relic_info_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgRelic.relic_info_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgRelic.relic_info_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_info_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<relic_info_s2c>
        PARSER = new com.google.protobuf.AbstractParser<relic_info_s2c>() {
      @java.lang.Override
      public relic_info_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<relic_info_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<relic_info_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgRelic.relic_info_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface relic_equip_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.relic_equip_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint64 id = 1;</code>
     * @return The id.
     */
    long getId();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.relic_equip_c2s}
   */
  public static final class relic_equip_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.relic_equip_c2s)
      relic_equip_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use relic_equip_c2s.newBuilder() to construct.
    private relic_equip_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private relic_equip_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new relic_equip_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_equip_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_equip_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_c2s.class, org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_c2s.Builder.class);
    }

    public static final int ID_FIELD_NUMBER = 1;
    private long id_ = 0L;
    /**
     * <code>uint64 id = 1;</code>
     * @return The id.
     */
    @java.lang.Override
    public long getId() {
      return id_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (id_ != 0L) {
        output.writeUInt64(1, id_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (id_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(1, id_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_c2s other = (org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_c2s) obj;

      if (getId()
          != other.getId()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getId());
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.relic_equip_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.relic_equip_c2s)
        org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_equip_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_equip_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_c2s.class, org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        id_ = 0L;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_equip_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_c2s build() {
        org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_c2s result = new org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.id_ = id_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_c2s.getDefaultInstance()) return this;
        if (other.getId() != 0L) {
          setId(other.getId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                id_ = input.readUInt64();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private long id_ ;
      /**
       * <code>uint64 id = 1;</code>
       * @return The id.
       */
      @java.lang.Override
      public long getId() {
        return id_;
      }
      /**
       * <code>uint64 id = 1;</code>
       * @param value The id to set.
       * @return This builder for chaining.
       */
      public Builder setId(long value) {

        id_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        id_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.relic_equip_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.relic_equip_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<relic_equip_c2s>
        PARSER = new com.google.protobuf.AbstractParser<relic_equip_c2s>() {
      @java.lang.Override
      public relic_equip_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<relic_equip_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<relic_equip_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface relic_equip_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.relic_equip_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_relic change_list = 1;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_relic> 
        getChangeListList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_relic change_list = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_relic getChangeList(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_relic change_list = 1;</code>
     */
    int getChangeListCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_relic change_list = 1;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_relicOrBuilder> 
        getChangeListOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_relic change_list = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_relicOrBuilder getChangeListOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.relic_equip_s2c}
   */
  public static final class relic_equip_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.relic_equip_s2c)
      relic_equip_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use relic_equip_s2c.newBuilder() to construct.
    private relic_equip_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private relic_equip_s2c() {
      changeList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new relic_equip_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_equip_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_equip_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_s2c.class, org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_s2c.Builder.class);
    }

    public static final int CHANGE_LIST_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_relic> changeList_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_relic change_list = 1;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_relic> getChangeListList() {
      return changeList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_relic change_list = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_relicOrBuilder> 
        getChangeListOrBuilderList() {
      return changeList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_relic change_list = 1;</code>
     */
    @java.lang.Override
    public int getChangeListCount() {
      return changeList_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_relic change_list = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_relic getChangeList(int index) {
      return changeList_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_relic change_list = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_relicOrBuilder getChangeListOrBuilder(
        int index) {
      return changeList_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < changeList_.size(); i++) {
        output.writeMessage(1, changeList_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < changeList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, changeList_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_s2c other = (org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_s2c) obj;

      if (!getChangeListList()
          .equals(other.getChangeListList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getChangeListCount() > 0) {
        hash = (37 * hash) + CHANGE_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getChangeListList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.relic_equip_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.relic_equip_s2c)
        org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_equip_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_equip_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_s2c.class, org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        if (changeListBuilder_ == null) {
          changeList_ = java.util.Collections.emptyList();
        } else {
          changeList_ = null;
          changeListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_equip_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_s2c build() {
        org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_s2c result = new org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_s2c(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_s2c result) {
        if (changeListBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            changeList_ = java.util.Collections.unmodifiableList(changeList_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.changeList_ = changeList_;
        } else {
          result.changeList_ = changeListBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_s2c result) {
        int from_bitField0_ = bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_s2c.getDefaultInstance()) return this;
        if (changeListBuilder_ == null) {
          if (!other.changeList_.isEmpty()) {
            if (changeList_.isEmpty()) {
              changeList_ = other.changeList_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureChangeListIsMutable();
              changeList_.addAll(other.changeList_);
            }
            onChanged();
          }
        } else {
          if (!other.changeList_.isEmpty()) {
            if (changeListBuilder_.isEmpty()) {
              changeListBuilder_.dispose();
              changeListBuilder_ = null;
              changeList_ = other.changeList_;
              bitField0_ = (bitField0_ & ~0x00000001);
              changeListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getChangeListFieldBuilder() : null;
            } else {
              changeListBuilder_.addAllMessages(other.changeList_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                org.gof.demo.worldsrv.msg.Define.p_relic m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_relic.parser(),
                        extensionRegistry);
                if (changeListBuilder_ == null) {
                  ensureChangeListIsMutable();
                  changeList_.add(m);
                } else {
                  changeListBuilder_.addMessage(m);
                }
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_relic> changeList_ =
        java.util.Collections.emptyList();
      private void ensureChangeListIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          changeList_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_relic>(changeList_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_relic, org.gof.demo.worldsrv.msg.Define.p_relic.Builder, org.gof.demo.worldsrv.msg.Define.p_relicOrBuilder> changeListBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic change_list = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_relic> getChangeListList() {
        if (changeListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(changeList_);
        } else {
          return changeListBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic change_list = 1;</code>
       */
      public int getChangeListCount() {
        if (changeListBuilder_ == null) {
          return changeList_.size();
        } else {
          return changeListBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic change_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_relic getChangeList(int index) {
        if (changeListBuilder_ == null) {
          return changeList_.get(index);
        } else {
          return changeListBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic change_list = 1;</code>
       */
      public Builder setChangeList(
          int index, org.gof.demo.worldsrv.msg.Define.p_relic value) {
        if (changeListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureChangeListIsMutable();
          changeList_.set(index, value);
          onChanged();
        } else {
          changeListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic change_list = 1;</code>
       */
      public Builder setChangeList(
          int index, org.gof.demo.worldsrv.msg.Define.p_relic.Builder builderForValue) {
        if (changeListBuilder_ == null) {
          ensureChangeListIsMutable();
          changeList_.set(index, builderForValue.build());
          onChanged();
        } else {
          changeListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic change_list = 1;</code>
       */
      public Builder addChangeList(org.gof.demo.worldsrv.msg.Define.p_relic value) {
        if (changeListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureChangeListIsMutable();
          changeList_.add(value);
          onChanged();
        } else {
          changeListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic change_list = 1;</code>
       */
      public Builder addChangeList(
          int index, org.gof.demo.worldsrv.msg.Define.p_relic value) {
        if (changeListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureChangeListIsMutable();
          changeList_.add(index, value);
          onChanged();
        } else {
          changeListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic change_list = 1;</code>
       */
      public Builder addChangeList(
          org.gof.demo.worldsrv.msg.Define.p_relic.Builder builderForValue) {
        if (changeListBuilder_ == null) {
          ensureChangeListIsMutable();
          changeList_.add(builderForValue.build());
          onChanged();
        } else {
          changeListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic change_list = 1;</code>
       */
      public Builder addChangeList(
          int index, org.gof.demo.worldsrv.msg.Define.p_relic.Builder builderForValue) {
        if (changeListBuilder_ == null) {
          ensureChangeListIsMutable();
          changeList_.add(index, builderForValue.build());
          onChanged();
        } else {
          changeListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic change_list = 1;</code>
       */
      public Builder addAllChangeList(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_relic> values) {
        if (changeListBuilder_ == null) {
          ensureChangeListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, changeList_);
          onChanged();
        } else {
          changeListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic change_list = 1;</code>
       */
      public Builder clearChangeList() {
        if (changeListBuilder_ == null) {
          changeList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          changeListBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic change_list = 1;</code>
       */
      public Builder removeChangeList(int index) {
        if (changeListBuilder_ == null) {
          ensureChangeListIsMutable();
          changeList_.remove(index);
          onChanged();
        } else {
          changeListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic change_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_relic.Builder getChangeListBuilder(
          int index) {
        return getChangeListFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic change_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_relicOrBuilder getChangeListOrBuilder(
          int index) {
        if (changeListBuilder_ == null) {
          return changeList_.get(index);  } else {
          return changeListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic change_list = 1;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_relicOrBuilder> 
           getChangeListOrBuilderList() {
        if (changeListBuilder_ != null) {
          return changeListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(changeList_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic change_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_relic.Builder addChangeListBuilder() {
        return getChangeListFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_relic.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic change_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_relic.Builder addChangeListBuilder(
          int index) {
        return getChangeListFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_relic.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic change_list = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_relic.Builder> 
           getChangeListBuilderList() {
        return getChangeListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_relic, org.gof.demo.worldsrv.msg.Define.p_relic.Builder, org.gof.demo.worldsrv.msg.Define.p_relicOrBuilder> 
          getChangeListFieldBuilder() {
        if (changeListBuilder_ == null) {
          changeListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_relic, org.gof.demo.worldsrv.msg.Define.p_relic.Builder, org.gof.demo.worldsrv.msg.Define.p_relicOrBuilder>(
                  changeList_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          changeList_ = null;
        }
        return changeListBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.relic_equip_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.relic_equip_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<relic_equip_s2c>
        PARSER = new com.google.protobuf.AbstractParser<relic_equip_s2c>() {
      @java.lang.Override
      public relic_equip_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<relic_equip_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<relic_equip_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgRelic.relic_equip_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface relic_level_up_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.relic_level_up_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint64 id = 1;</code>
     * @return The id.
     */
    long getId();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.relic_level_up_c2s}
   */
  public static final class relic_level_up_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.relic_level_up_c2s)
      relic_level_up_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use relic_level_up_c2s.newBuilder() to construct.
    private relic_level_up_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private relic_level_up_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new relic_level_up_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_level_up_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_level_up_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_c2s.class, org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_c2s.Builder.class);
    }

    public static final int ID_FIELD_NUMBER = 1;
    private long id_ = 0L;
    /**
     * <code>uint64 id = 1;</code>
     * @return The id.
     */
    @java.lang.Override
    public long getId() {
      return id_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (id_ != 0L) {
        output.writeUInt64(1, id_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (id_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(1, id_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_c2s other = (org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_c2s) obj;

      if (getId()
          != other.getId()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getId());
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.relic_level_up_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.relic_level_up_c2s)
        org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_level_up_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_level_up_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_c2s.class, org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        id_ = 0L;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_level_up_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_c2s build() {
        org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_c2s result = new org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.id_ = id_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_c2s.getDefaultInstance()) return this;
        if (other.getId() != 0L) {
          setId(other.getId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                id_ = input.readUInt64();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private long id_ ;
      /**
       * <code>uint64 id = 1;</code>
       * @return The id.
       */
      @java.lang.Override
      public long getId() {
        return id_;
      }
      /**
       * <code>uint64 id = 1;</code>
       * @param value The id to set.
       * @return This builder for chaining.
       */
      public Builder setId(long value) {

        id_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        id_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.relic_level_up_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.relic_level_up_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<relic_level_up_c2s>
        PARSER = new com.google.protobuf.AbstractParser<relic_level_up_c2s>() {
      @java.lang.Override
      public relic_level_up_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<relic_level_up_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<relic_level_up_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface relic_level_up_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.relic_level_up_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.org.gof.demo.worldsrv.msg.p_relic relic = 1;</code>
     * @return Whether the relic field is set.
     */
    boolean hasRelic();
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_relic relic = 1;</code>
     * @return The relic.
     */
    org.gof.demo.worldsrv.msg.Define.p_relic getRelic();
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_relic relic = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_relicOrBuilder getRelicOrBuilder();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.relic_level_up_s2c}
   */
  public static final class relic_level_up_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.relic_level_up_s2c)
      relic_level_up_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use relic_level_up_s2c.newBuilder() to construct.
    private relic_level_up_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private relic_level_up_s2c() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new relic_level_up_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_level_up_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_level_up_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_s2c.class, org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_s2c.Builder.class);
    }

    private int bitField0_;
    public static final int RELIC_FIELD_NUMBER = 1;
    private org.gof.demo.worldsrv.msg.Define.p_relic relic_;
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_relic relic = 1;</code>
     * @return Whether the relic field is set.
     */
    @java.lang.Override
    public boolean hasRelic() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_relic relic = 1;</code>
     * @return The relic.
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_relic getRelic() {
      return relic_ == null ? org.gof.demo.worldsrv.msg.Define.p_relic.getDefaultInstance() : relic_;
    }
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_relic relic = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_relicOrBuilder getRelicOrBuilder() {
      return relic_ == null ? org.gof.demo.worldsrv.msg.Define.p_relic.getDefaultInstance() : relic_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getRelic());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getRelic());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_s2c other = (org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_s2c) obj;

      if (hasRelic() != other.hasRelic()) return false;
      if (hasRelic()) {
        if (!getRelic()
            .equals(other.getRelic())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasRelic()) {
        hash = (37 * hash) + RELIC_FIELD_NUMBER;
        hash = (53 * hash) + getRelic().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.relic_level_up_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.relic_level_up_s2c)
        org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_level_up_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_level_up_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_s2c.class, org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_s2c.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getRelicFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        relic_ = null;
        if (relicBuilder_ != null) {
          relicBuilder_.dispose();
          relicBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_level_up_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_s2c build() {
        org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_s2c result = new org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_s2c result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.relic_ = relicBuilder_ == null
              ? relic_
              : relicBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_s2c.getDefaultInstance()) return this;
        if (other.hasRelic()) {
          mergeRelic(other.getRelic());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getRelicFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private org.gof.demo.worldsrv.msg.Define.p_relic relic_;
      private com.google.protobuf.SingleFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_relic, org.gof.demo.worldsrv.msg.Define.p_relic.Builder, org.gof.demo.worldsrv.msg.Define.p_relicOrBuilder> relicBuilder_;
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_relic relic = 1;</code>
       * @return Whether the relic field is set.
       */
      public boolean hasRelic() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_relic relic = 1;</code>
       * @return The relic.
       */
      public org.gof.demo.worldsrv.msg.Define.p_relic getRelic() {
        if (relicBuilder_ == null) {
          return relic_ == null ? org.gof.demo.worldsrv.msg.Define.p_relic.getDefaultInstance() : relic_;
        } else {
          return relicBuilder_.getMessage();
        }
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_relic relic = 1;</code>
       */
      public Builder setRelic(org.gof.demo.worldsrv.msg.Define.p_relic value) {
        if (relicBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          relic_ = value;
        } else {
          relicBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_relic relic = 1;</code>
       */
      public Builder setRelic(
          org.gof.demo.worldsrv.msg.Define.p_relic.Builder builderForValue) {
        if (relicBuilder_ == null) {
          relic_ = builderForValue.build();
        } else {
          relicBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_relic relic = 1;</code>
       */
      public Builder mergeRelic(org.gof.demo.worldsrv.msg.Define.p_relic value) {
        if (relicBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
            relic_ != null &&
            relic_ != org.gof.demo.worldsrv.msg.Define.p_relic.getDefaultInstance()) {
            getRelicBuilder().mergeFrom(value);
          } else {
            relic_ = value;
          }
        } else {
          relicBuilder_.mergeFrom(value);
        }
        if (relic_ != null) {
          bitField0_ |= 0x00000001;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_relic relic = 1;</code>
       */
      public Builder clearRelic() {
        bitField0_ = (bitField0_ & ~0x00000001);
        relic_ = null;
        if (relicBuilder_ != null) {
          relicBuilder_.dispose();
          relicBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_relic relic = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_relic.Builder getRelicBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getRelicFieldBuilder().getBuilder();
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_relic relic = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_relicOrBuilder getRelicOrBuilder() {
        if (relicBuilder_ != null) {
          return relicBuilder_.getMessageOrBuilder();
        } else {
          return relic_ == null ?
              org.gof.demo.worldsrv.msg.Define.p_relic.getDefaultInstance() : relic_;
        }
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_relic relic = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_relic, org.gof.demo.worldsrv.msg.Define.p_relic.Builder, org.gof.demo.worldsrv.msg.Define.p_relicOrBuilder> 
          getRelicFieldBuilder() {
        if (relicBuilder_ == null) {
          relicBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_relic, org.gof.demo.worldsrv.msg.Define.p_relic.Builder, org.gof.demo.worldsrv.msg.Define.p_relicOrBuilder>(
                  getRelic(),
                  getParentForChildren(),
                  isClean());
          relic_ = null;
        }
        return relicBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.relic_level_up_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.relic_level_up_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<relic_level_up_s2c>
        PARSER = new com.google.protobuf.AbstractParser<relic_level_up_s2c>() {
      @java.lang.Override
      public relic_level_up_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<relic_level_up_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<relic_level_up_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgRelic.relic_level_up_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface relic_find_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.relic_find_c2s)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.relic_find_c2s}
   */
  public static final class relic_find_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.relic_find_c2s)
      relic_find_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use relic_find_c2s.newBuilder() to construct.
    private relic_find_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private relic_find_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new relic_find_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_find_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_find_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgRelic.relic_find_c2s.class, org.gof.demo.worldsrv.msg.MsgRelic.relic_find_c2s.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgRelic.relic_find_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgRelic.relic_find_c2s other = (org.gof.demo.worldsrv.msg.MsgRelic.relic_find_c2s) obj;

      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_find_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_find_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_find_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_find_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_find_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_find_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_find_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_find_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_find_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_find_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_find_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_find_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgRelic.relic_find_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.relic_find_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.relic_find_c2s)
        org.gof.demo.worldsrv.msg.MsgRelic.relic_find_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_find_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_find_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgRelic.relic_find_c2s.class, org.gof.demo.worldsrv.msg.MsgRelic.relic_find_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgRelic.relic_find_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_find_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_find_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgRelic.relic_find_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_find_c2s build() {
        org.gof.demo.worldsrv.msg.MsgRelic.relic_find_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_find_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgRelic.relic_find_c2s result = new org.gof.demo.worldsrv.msg.MsgRelic.relic_find_c2s(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgRelic.relic_find_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgRelic.relic_find_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgRelic.relic_find_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgRelic.relic_find_c2s.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.relic_find_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.relic_find_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgRelic.relic_find_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgRelic.relic_find_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_find_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<relic_find_c2s>
        PARSER = new com.google.protobuf.AbstractParser<relic_find_c2s>() {
      @java.lang.Override
      public relic_find_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<relic_find_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<relic_find_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgRelic.relic_find_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface relic_find_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.relic_find_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.org.gof.demo.worldsrv.msg.p_relic relic = 1;</code>
     * @return Whether the relic field is set.
     */
    boolean hasRelic();
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_relic relic = 1;</code>
     * @return The relic.
     */
    org.gof.demo.worldsrv.msg.Define.p_relic getRelic();
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_relic relic = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_relicOrBuilder getRelicOrBuilder();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.relic_find_s2c}
   */
  public static final class relic_find_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.relic_find_s2c)
      relic_find_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use relic_find_s2c.newBuilder() to construct.
    private relic_find_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private relic_find_s2c() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new relic_find_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_find_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_find_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgRelic.relic_find_s2c.class, org.gof.demo.worldsrv.msg.MsgRelic.relic_find_s2c.Builder.class);
    }

    private int bitField0_;
    public static final int RELIC_FIELD_NUMBER = 1;
    private org.gof.demo.worldsrv.msg.Define.p_relic relic_;
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_relic relic = 1;</code>
     * @return Whether the relic field is set.
     */
    @java.lang.Override
    public boolean hasRelic() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_relic relic = 1;</code>
     * @return The relic.
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_relic getRelic() {
      return relic_ == null ? org.gof.demo.worldsrv.msg.Define.p_relic.getDefaultInstance() : relic_;
    }
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_relic relic = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_relicOrBuilder getRelicOrBuilder() {
      return relic_ == null ? org.gof.demo.worldsrv.msg.Define.p_relic.getDefaultInstance() : relic_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getRelic());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getRelic());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgRelic.relic_find_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgRelic.relic_find_s2c other = (org.gof.demo.worldsrv.msg.MsgRelic.relic_find_s2c) obj;

      if (hasRelic() != other.hasRelic()) return false;
      if (hasRelic()) {
        if (!getRelic()
            .equals(other.getRelic())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasRelic()) {
        hash = (37 * hash) + RELIC_FIELD_NUMBER;
        hash = (53 * hash) + getRelic().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_find_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_find_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_find_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_find_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_find_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_find_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_find_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_find_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_find_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_find_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_find_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_find_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgRelic.relic_find_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.relic_find_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.relic_find_s2c)
        org.gof.demo.worldsrv.msg.MsgRelic.relic_find_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_find_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_find_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgRelic.relic_find_s2c.class, org.gof.demo.worldsrv.msg.MsgRelic.relic_find_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgRelic.relic_find_s2c.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getRelicFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        relic_ = null;
        if (relicBuilder_ != null) {
          relicBuilder_.dispose();
          relicBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_find_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_find_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgRelic.relic_find_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_find_s2c build() {
        org.gof.demo.worldsrv.msg.MsgRelic.relic_find_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_find_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgRelic.relic_find_s2c result = new org.gof.demo.worldsrv.msg.MsgRelic.relic_find_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgRelic.relic_find_s2c result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.relic_ = relicBuilder_ == null
              ? relic_
              : relicBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgRelic.relic_find_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgRelic.relic_find_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgRelic.relic_find_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgRelic.relic_find_s2c.getDefaultInstance()) return this;
        if (other.hasRelic()) {
          mergeRelic(other.getRelic());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getRelicFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private org.gof.demo.worldsrv.msg.Define.p_relic relic_;
      private com.google.protobuf.SingleFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_relic, org.gof.demo.worldsrv.msg.Define.p_relic.Builder, org.gof.demo.worldsrv.msg.Define.p_relicOrBuilder> relicBuilder_;
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_relic relic = 1;</code>
       * @return Whether the relic field is set.
       */
      public boolean hasRelic() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_relic relic = 1;</code>
       * @return The relic.
       */
      public org.gof.demo.worldsrv.msg.Define.p_relic getRelic() {
        if (relicBuilder_ == null) {
          return relic_ == null ? org.gof.demo.worldsrv.msg.Define.p_relic.getDefaultInstance() : relic_;
        } else {
          return relicBuilder_.getMessage();
        }
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_relic relic = 1;</code>
       */
      public Builder setRelic(org.gof.demo.worldsrv.msg.Define.p_relic value) {
        if (relicBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          relic_ = value;
        } else {
          relicBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_relic relic = 1;</code>
       */
      public Builder setRelic(
          org.gof.demo.worldsrv.msg.Define.p_relic.Builder builderForValue) {
        if (relicBuilder_ == null) {
          relic_ = builderForValue.build();
        } else {
          relicBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_relic relic = 1;</code>
       */
      public Builder mergeRelic(org.gof.demo.worldsrv.msg.Define.p_relic value) {
        if (relicBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
            relic_ != null &&
            relic_ != org.gof.demo.worldsrv.msg.Define.p_relic.getDefaultInstance()) {
            getRelicBuilder().mergeFrom(value);
          } else {
            relic_ = value;
          }
        } else {
          relicBuilder_.mergeFrom(value);
        }
        if (relic_ != null) {
          bitField0_ |= 0x00000001;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_relic relic = 1;</code>
       */
      public Builder clearRelic() {
        bitField0_ = (bitField0_ & ~0x00000001);
        relic_ = null;
        if (relicBuilder_ != null) {
          relicBuilder_.dispose();
          relicBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_relic relic = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_relic.Builder getRelicBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getRelicFieldBuilder().getBuilder();
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_relic relic = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_relicOrBuilder getRelicOrBuilder() {
        if (relicBuilder_ != null) {
          return relicBuilder_.getMessageOrBuilder();
        } else {
          return relic_ == null ?
              org.gof.demo.worldsrv.msg.Define.p_relic.getDefaultInstance() : relic_;
        }
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_relic relic = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_relic, org.gof.demo.worldsrv.msg.Define.p_relic.Builder, org.gof.demo.worldsrv.msg.Define.p_relicOrBuilder> 
          getRelicFieldBuilder() {
        if (relicBuilder_ == null) {
          relicBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_relic, org.gof.demo.worldsrv.msg.Define.p_relic.Builder, org.gof.demo.worldsrv.msg.Define.p_relicOrBuilder>(
                  getRelic(),
                  getParentForChildren(),
                  isClean());
          relic_ = null;
        }
        return relicBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.relic_find_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.relic_find_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgRelic.relic_find_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgRelic.relic_find_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_find_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<relic_find_s2c>
        PARSER = new com.google.protobuf.AbstractParser<relic_find_s2c>() {
      @java.lang.Override
      public relic_find_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<relic_find_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<relic_find_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgRelic.relic_find_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface relic_tab_info_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.relic_tab_info_c2s)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.relic_tab_info_c2s}
   */
  public static final class relic_tab_info_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.relic_tab_info_c2s)
      relic_tab_info_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use relic_tab_info_c2s.newBuilder() to construct.
    private relic_tab_info_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private relic_tab_info_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new relic_tab_info_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_tab_info_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_tab_info_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_c2s.class, org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_c2s.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_c2s other = (org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_c2s) obj;

      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.relic_tab_info_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.relic_tab_info_c2s)
        org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_tab_info_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_tab_info_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_c2s.class, org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_tab_info_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_c2s build() {
        org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_c2s result = new org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_c2s(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_c2s.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.relic_tab_info_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.relic_tab_info_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<relic_tab_info_c2s>
        PARSER = new com.google.protobuf.AbstractParser<relic_tab_info_c2s>() {
      @java.lang.Override
      public relic_tab_info_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<relic_tab_info_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<relic_tab_info_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface relic_tab_info_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.relic_tab_info_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 tab = 1;</code>
     * @return The tab.
     */
    int getTab();

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_relic_tab_info tab_list = 2;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_relic_tab_info> 
        getTabListList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_relic_tab_info tab_list = 2;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_relic_tab_info getTabList(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_relic_tab_info tab_list = 2;</code>
     */
    int getTabListCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_relic_tab_info tab_list = 2;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_relic_tab_infoOrBuilder> 
        getTabListOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_relic_tab_info tab_list = 2;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_relic_tab_infoOrBuilder getTabListOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.relic_tab_info_s2c}
   */
  public static final class relic_tab_info_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.relic_tab_info_s2c)
      relic_tab_info_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use relic_tab_info_s2c.newBuilder() to construct.
    private relic_tab_info_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private relic_tab_info_s2c() {
      tabList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new relic_tab_info_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_tab_info_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_tab_info_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_s2c.class, org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_s2c.Builder.class);
    }

    public static final int TAB_FIELD_NUMBER = 1;
    private int tab_ = 0;
    /**
     * <code>uint32 tab = 1;</code>
     * @return The tab.
     */
    @java.lang.Override
    public int getTab() {
      return tab_;
    }

    public static final int TAB_LIST_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_relic_tab_info> tabList_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_relic_tab_info tab_list = 2;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_relic_tab_info> getTabListList() {
      return tabList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_relic_tab_info tab_list = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_relic_tab_infoOrBuilder> 
        getTabListOrBuilderList() {
      return tabList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_relic_tab_info tab_list = 2;</code>
     */
    @java.lang.Override
    public int getTabListCount() {
      return tabList_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_relic_tab_info tab_list = 2;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_relic_tab_info getTabList(int index) {
      return tabList_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_relic_tab_info tab_list = 2;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_relic_tab_infoOrBuilder getTabListOrBuilder(
        int index) {
      return tabList_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (tab_ != 0) {
        output.writeUInt32(1, tab_);
      }
      for (int i = 0; i < tabList_.size(); i++) {
        output.writeMessage(2, tabList_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (tab_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, tab_);
      }
      for (int i = 0; i < tabList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, tabList_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_s2c other = (org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_s2c) obj;

      if (getTab()
          != other.getTab()) return false;
      if (!getTabListList()
          .equals(other.getTabListList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TAB_FIELD_NUMBER;
      hash = (53 * hash) + getTab();
      if (getTabListCount() > 0) {
        hash = (37 * hash) + TAB_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getTabListList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.relic_tab_info_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.relic_tab_info_s2c)
        org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_tab_info_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_tab_info_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_s2c.class, org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        tab_ = 0;
        if (tabListBuilder_ == null) {
          tabList_ = java.util.Collections.emptyList();
        } else {
          tabList_ = null;
          tabListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_tab_info_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_s2c build() {
        org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_s2c result = new org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_s2c(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_s2c result) {
        if (tabListBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0)) {
            tabList_ = java.util.Collections.unmodifiableList(tabList_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.tabList_ = tabList_;
        } else {
          result.tabList_ = tabListBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.tab_ = tab_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_s2c.getDefaultInstance()) return this;
        if (other.getTab() != 0) {
          setTab(other.getTab());
        }
        if (tabListBuilder_ == null) {
          if (!other.tabList_.isEmpty()) {
            if (tabList_.isEmpty()) {
              tabList_ = other.tabList_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensureTabListIsMutable();
              tabList_.addAll(other.tabList_);
            }
            onChanged();
          }
        } else {
          if (!other.tabList_.isEmpty()) {
            if (tabListBuilder_.isEmpty()) {
              tabListBuilder_.dispose();
              tabListBuilder_ = null;
              tabList_ = other.tabList_;
              bitField0_ = (bitField0_ & ~0x00000002);
              tabListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getTabListFieldBuilder() : null;
            } else {
              tabListBuilder_.addAllMessages(other.tabList_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                tab_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 18: {
                org.gof.demo.worldsrv.msg.Define.p_relic_tab_info m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_relic_tab_info.parser(),
                        extensionRegistry);
                if (tabListBuilder_ == null) {
                  ensureTabListIsMutable();
                  tabList_.add(m);
                } else {
                  tabListBuilder_.addMessage(m);
                }
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int tab_ ;
      /**
       * <code>uint32 tab = 1;</code>
       * @return The tab.
       */
      @java.lang.Override
      public int getTab() {
        return tab_;
      }
      /**
       * <code>uint32 tab = 1;</code>
       * @param value The tab to set.
       * @return This builder for chaining.
       */
      public Builder setTab(int value) {

        tab_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 tab = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTab() {
        bitField0_ = (bitField0_ & ~0x00000001);
        tab_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_relic_tab_info> tabList_ =
        java.util.Collections.emptyList();
      private void ensureTabListIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          tabList_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_relic_tab_info>(tabList_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_relic_tab_info, org.gof.demo.worldsrv.msg.Define.p_relic_tab_info.Builder, org.gof.demo.worldsrv.msg.Define.p_relic_tab_infoOrBuilder> tabListBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic_tab_info tab_list = 2;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_relic_tab_info> getTabListList() {
        if (tabListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(tabList_);
        } else {
          return tabListBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic_tab_info tab_list = 2;</code>
       */
      public int getTabListCount() {
        if (tabListBuilder_ == null) {
          return tabList_.size();
        } else {
          return tabListBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic_tab_info tab_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_relic_tab_info getTabList(int index) {
        if (tabListBuilder_ == null) {
          return tabList_.get(index);
        } else {
          return tabListBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic_tab_info tab_list = 2;</code>
       */
      public Builder setTabList(
          int index, org.gof.demo.worldsrv.msg.Define.p_relic_tab_info value) {
        if (tabListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTabListIsMutable();
          tabList_.set(index, value);
          onChanged();
        } else {
          tabListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic_tab_info tab_list = 2;</code>
       */
      public Builder setTabList(
          int index, org.gof.demo.worldsrv.msg.Define.p_relic_tab_info.Builder builderForValue) {
        if (tabListBuilder_ == null) {
          ensureTabListIsMutable();
          tabList_.set(index, builderForValue.build());
          onChanged();
        } else {
          tabListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic_tab_info tab_list = 2;</code>
       */
      public Builder addTabList(org.gof.demo.worldsrv.msg.Define.p_relic_tab_info value) {
        if (tabListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTabListIsMutable();
          tabList_.add(value);
          onChanged();
        } else {
          tabListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic_tab_info tab_list = 2;</code>
       */
      public Builder addTabList(
          int index, org.gof.demo.worldsrv.msg.Define.p_relic_tab_info value) {
        if (tabListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTabListIsMutable();
          tabList_.add(index, value);
          onChanged();
        } else {
          tabListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic_tab_info tab_list = 2;</code>
       */
      public Builder addTabList(
          org.gof.demo.worldsrv.msg.Define.p_relic_tab_info.Builder builderForValue) {
        if (tabListBuilder_ == null) {
          ensureTabListIsMutable();
          tabList_.add(builderForValue.build());
          onChanged();
        } else {
          tabListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic_tab_info tab_list = 2;</code>
       */
      public Builder addTabList(
          int index, org.gof.demo.worldsrv.msg.Define.p_relic_tab_info.Builder builderForValue) {
        if (tabListBuilder_ == null) {
          ensureTabListIsMutable();
          tabList_.add(index, builderForValue.build());
          onChanged();
        } else {
          tabListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic_tab_info tab_list = 2;</code>
       */
      public Builder addAllTabList(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_relic_tab_info> values) {
        if (tabListBuilder_ == null) {
          ensureTabListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, tabList_);
          onChanged();
        } else {
          tabListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic_tab_info tab_list = 2;</code>
       */
      public Builder clearTabList() {
        if (tabListBuilder_ == null) {
          tabList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          tabListBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic_tab_info tab_list = 2;</code>
       */
      public Builder removeTabList(int index) {
        if (tabListBuilder_ == null) {
          ensureTabListIsMutable();
          tabList_.remove(index);
          onChanged();
        } else {
          tabListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic_tab_info tab_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_relic_tab_info.Builder getTabListBuilder(
          int index) {
        return getTabListFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic_tab_info tab_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_relic_tab_infoOrBuilder getTabListOrBuilder(
          int index) {
        if (tabListBuilder_ == null) {
          return tabList_.get(index);  } else {
          return tabListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic_tab_info tab_list = 2;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_relic_tab_infoOrBuilder> 
           getTabListOrBuilderList() {
        if (tabListBuilder_ != null) {
          return tabListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(tabList_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic_tab_info tab_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_relic_tab_info.Builder addTabListBuilder() {
        return getTabListFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_relic_tab_info.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic_tab_info tab_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_relic_tab_info.Builder addTabListBuilder(
          int index) {
        return getTabListFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_relic_tab_info.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_relic_tab_info tab_list = 2;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_relic_tab_info.Builder> 
           getTabListBuilderList() {
        return getTabListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_relic_tab_info, org.gof.demo.worldsrv.msg.Define.p_relic_tab_info.Builder, org.gof.demo.worldsrv.msg.Define.p_relic_tab_infoOrBuilder> 
          getTabListFieldBuilder() {
        if (tabListBuilder_ == null) {
          tabListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_relic_tab_info, org.gof.demo.worldsrv.msg.Define.p_relic_tab_info.Builder, org.gof.demo.worldsrv.msg.Define.p_relic_tab_infoOrBuilder>(
                  tabList_,
                  ((bitField0_ & 0x00000002) != 0),
                  getParentForChildren(),
                  isClean());
          tabList_ = null;
        }
        return tabListBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.relic_tab_info_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.relic_tab_info_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<relic_tab_info_s2c>
        PARSER = new com.google.protobuf.AbstractParser<relic_tab_info_s2c>() {
      @java.lang.Override
      public relic_tab_info_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<relic_tab_info_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<relic_tab_info_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface relic_tab_info_update_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.relic_tab_info_update_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 tab = 1;</code>
     * @return The tab.
     */
    int getTab();

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> 
        getPosInfoList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_key_value getPosInfo(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
     */
    int getPosInfoCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
        getPosInfoOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder getPosInfoOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.relic_tab_info_update_s2c}
   */
  public static final class relic_tab_info_update_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.relic_tab_info_update_s2c)
      relic_tab_info_update_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use relic_tab_info_update_s2c.newBuilder() to construct.
    private relic_tab_info_update_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private relic_tab_info_update_s2c() {
      posInfo_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new relic_tab_info_update_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_tab_info_update_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_tab_info_update_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_update_s2c.class, org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_update_s2c.Builder.class);
    }

    public static final int TAB_FIELD_NUMBER = 1;
    private int tab_ = 0;
    /**
     * <code>uint32 tab = 1;</code>
     * @return The tab.
     */
    @java.lang.Override
    public int getTab() {
      return tab_;
    }

    public static final int POS_INFO_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> posInfo_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> getPosInfoList() {
      return posInfo_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
        getPosInfoOrBuilderList() {
      return posInfo_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
     */
    @java.lang.Override
    public int getPosInfoCount() {
      return posInfo_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_key_value getPosInfo(int index) {
      return posInfo_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder getPosInfoOrBuilder(
        int index) {
      return posInfo_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (tab_ != 0) {
        output.writeUInt32(1, tab_);
      }
      for (int i = 0; i < posInfo_.size(); i++) {
        output.writeMessage(2, posInfo_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (tab_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, tab_);
      }
      for (int i = 0; i < posInfo_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, posInfo_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_update_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_update_s2c other = (org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_update_s2c) obj;

      if (getTab()
          != other.getTab()) return false;
      if (!getPosInfoList()
          .equals(other.getPosInfoList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TAB_FIELD_NUMBER;
      hash = (53 * hash) + getTab();
      if (getPosInfoCount() > 0) {
        hash = (37 * hash) + POS_INFO_FIELD_NUMBER;
        hash = (53 * hash) + getPosInfoList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_update_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_update_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_update_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_update_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_update_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_update_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_update_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_update_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_update_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_update_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_update_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_update_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_update_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.relic_tab_info_update_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.relic_tab_info_update_s2c)
        org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_update_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_tab_info_update_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_tab_info_update_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_update_s2c.class, org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_update_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_update_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        tab_ = 0;
        if (posInfoBuilder_ == null) {
          posInfo_ = java.util.Collections.emptyList();
        } else {
          posInfo_ = null;
          posInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_tab_info_update_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_update_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_update_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_update_s2c build() {
        org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_update_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_update_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_update_s2c result = new org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_update_s2c(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_update_s2c result) {
        if (posInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0)) {
            posInfo_ = java.util.Collections.unmodifiableList(posInfo_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.posInfo_ = posInfo_;
        } else {
          result.posInfo_ = posInfoBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_update_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.tab_ = tab_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_update_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_update_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_update_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_update_s2c.getDefaultInstance()) return this;
        if (other.getTab() != 0) {
          setTab(other.getTab());
        }
        if (posInfoBuilder_ == null) {
          if (!other.posInfo_.isEmpty()) {
            if (posInfo_.isEmpty()) {
              posInfo_ = other.posInfo_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensurePosInfoIsMutable();
              posInfo_.addAll(other.posInfo_);
            }
            onChanged();
          }
        } else {
          if (!other.posInfo_.isEmpty()) {
            if (posInfoBuilder_.isEmpty()) {
              posInfoBuilder_.dispose();
              posInfoBuilder_ = null;
              posInfo_ = other.posInfo_;
              bitField0_ = (bitField0_ & ~0x00000002);
              posInfoBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getPosInfoFieldBuilder() : null;
            } else {
              posInfoBuilder_.addAllMessages(other.posInfo_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                tab_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 18: {
                org.gof.demo.worldsrv.msg.Define.p_key_value m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_key_value.parser(),
                        extensionRegistry);
                if (posInfoBuilder_ == null) {
                  ensurePosInfoIsMutable();
                  posInfo_.add(m);
                } else {
                  posInfoBuilder_.addMessage(m);
                }
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int tab_ ;
      /**
       * <code>uint32 tab = 1;</code>
       * @return The tab.
       */
      @java.lang.Override
      public int getTab() {
        return tab_;
      }
      /**
       * <code>uint32 tab = 1;</code>
       * @param value The tab to set.
       * @return This builder for chaining.
       */
      public Builder setTab(int value) {

        tab_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 tab = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTab() {
        bitField0_ = (bitField0_ & ~0x00000001);
        tab_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> posInfo_ =
        java.util.Collections.emptyList();
      private void ensurePosInfoIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          posInfo_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_key_value>(posInfo_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_key_value, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder, org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> posInfoBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> getPosInfoList() {
        if (posInfoBuilder_ == null) {
          return java.util.Collections.unmodifiableList(posInfo_);
        } else {
          return posInfoBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public int getPosInfoCount() {
        if (posInfoBuilder_ == null) {
          return posInfo_.size();
        } else {
          return posInfoBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value getPosInfo(int index) {
        if (posInfoBuilder_ == null) {
          return posInfo_.get(index);
        } else {
          return posInfoBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public Builder setPosInfo(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value value) {
        if (posInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePosInfoIsMutable();
          posInfo_.set(index, value);
          onChanged();
        } else {
          posInfoBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public Builder setPosInfo(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder builderForValue) {
        if (posInfoBuilder_ == null) {
          ensurePosInfoIsMutable();
          posInfo_.set(index, builderForValue.build());
          onChanged();
        } else {
          posInfoBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public Builder addPosInfo(org.gof.demo.worldsrv.msg.Define.p_key_value value) {
        if (posInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePosInfoIsMutable();
          posInfo_.add(value);
          onChanged();
        } else {
          posInfoBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public Builder addPosInfo(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value value) {
        if (posInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePosInfoIsMutable();
          posInfo_.add(index, value);
          onChanged();
        } else {
          posInfoBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public Builder addPosInfo(
          org.gof.demo.worldsrv.msg.Define.p_key_value.Builder builderForValue) {
        if (posInfoBuilder_ == null) {
          ensurePosInfoIsMutable();
          posInfo_.add(builderForValue.build());
          onChanged();
        } else {
          posInfoBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public Builder addPosInfo(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder builderForValue) {
        if (posInfoBuilder_ == null) {
          ensurePosInfoIsMutable();
          posInfo_.add(index, builderForValue.build());
          onChanged();
        } else {
          posInfoBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public Builder addAllPosInfo(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_key_value> values) {
        if (posInfoBuilder_ == null) {
          ensurePosInfoIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, posInfo_);
          onChanged();
        } else {
          posInfoBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public Builder clearPosInfo() {
        if (posInfoBuilder_ == null) {
          posInfo_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          posInfoBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public Builder removePosInfo(int index) {
        if (posInfoBuilder_ == null) {
          ensurePosInfoIsMutable();
          posInfo_.remove(index);
          onChanged();
        } else {
          posInfoBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value.Builder getPosInfoBuilder(
          int index) {
        return getPosInfoFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder getPosInfoOrBuilder(
          int index) {
        if (posInfoBuilder_ == null) {
          return posInfo_.get(index);  } else {
          return posInfoBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
           getPosInfoOrBuilderList() {
        if (posInfoBuilder_ != null) {
          return posInfoBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(posInfo_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value.Builder addPosInfoBuilder() {
        return getPosInfoFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_key_value.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value.Builder addPosInfoBuilder(
          int index) {
        return getPosInfoFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_key_value.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value.Builder> 
           getPosInfoBuilderList() {
        return getPosInfoFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_key_value, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder, org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
          getPosInfoFieldBuilder() {
        if (posInfoBuilder_ == null) {
          posInfoBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_key_value, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder, org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder>(
                  posInfo_,
                  ((bitField0_ & 0x00000002) != 0),
                  getParentForChildren(),
                  isClean());
          posInfo_ = null;
        }
        return posInfoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.relic_tab_info_update_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.relic_tab_info_update_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_update_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_update_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_update_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<relic_tab_info_update_s2c>
        PARSER = new com.google.protobuf.AbstractParser<relic_tab_info_update_s2c>() {
      @java.lang.Override
      public relic_tab_info_update_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<relic_tab_info_update_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<relic_tab_info_update_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgRelic.relic_tab_info_update_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface relic_choose_tab_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.relic_choose_tab_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 tab = 1;</code>
     * @return The tab.
     */
    int getTab();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.relic_choose_tab_c2s}
   */
  public static final class relic_choose_tab_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.relic_choose_tab_c2s)
      relic_choose_tab_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use relic_choose_tab_c2s.newBuilder() to construct.
    private relic_choose_tab_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private relic_choose_tab_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new relic_choose_tab_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_choose_tab_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_choose_tab_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_c2s.class, org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_c2s.Builder.class);
    }

    public static final int TAB_FIELD_NUMBER = 1;
    private int tab_ = 0;
    /**
     * <code>uint32 tab = 1;</code>
     * @return The tab.
     */
    @java.lang.Override
    public int getTab() {
      return tab_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (tab_ != 0) {
        output.writeUInt32(1, tab_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (tab_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, tab_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_c2s other = (org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_c2s) obj;

      if (getTab()
          != other.getTab()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TAB_FIELD_NUMBER;
      hash = (53 * hash) + getTab();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.relic_choose_tab_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.relic_choose_tab_c2s)
        org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_choose_tab_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_choose_tab_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_c2s.class, org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        tab_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_choose_tab_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_c2s build() {
        org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_c2s result = new org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.tab_ = tab_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_c2s.getDefaultInstance()) return this;
        if (other.getTab() != 0) {
          setTab(other.getTab());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                tab_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int tab_ ;
      /**
       * <code>uint32 tab = 1;</code>
       * @return The tab.
       */
      @java.lang.Override
      public int getTab() {
        return tab_;
      }
      /**
       * <code>uint32 tab = 1;</code>
       * @param value The tab to set.
       * @return This builder for chaining.
       */
      public Builder setTab(int value) {

        tab_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 tab = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTab() {
        bitField0_ = (bitField0_ & ~0x00000001);
        tab_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.relic_choose_tab_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.relic_choose_tab_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<relic_choose_tab_c2s>
        PARSER = new com.google.protobuf.AbstractParser<relic_choose_tab_c2s>() {
      @java.lang.Override
      public relic_choose_tab_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<relic_choose_tab_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<relic_choose_tab_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface relic_choose_tab_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.relic_choose_tab_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 new_tab = 1;</code>
     * @return The newTab.
     */
    int getNewTab();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.relic_choose_tab_s2c}
   */
  public static final class relic_choose_tab_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.relic_choose_tab_s2c)
      relic_choose_tab_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use relic_choose_tab_s2c.newBuilder() to construct.
    private relic_choose_tab_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private relic_choose_tab_s2c() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new relic_choose_tab_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_choose_tab_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_choose_tab_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_s2c.class, org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_s2c.Builder.class);
    }

    public static final int NEW_TAB_FIELD_NUMBER = 1;
    private int newTab_ = 0;
    /**
     * <code>uint32 new_tab = 1;</code>
     * @return The newTab.
     */
    @java.lang.Override
    public int getNewTab() {
      return newTab_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (newTab_ != 0) {
        output.writeUInt32(1, newTab_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (newTab_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, newTab_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_s2c other = (org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_s2c) obj;

      if (getNewTab()
          != other.getNewTab()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + NEW_TAB_FIELD_NUMBER;
      hash = (53 * hash) + getNewTab();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.relic_choose_tab_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.relic_choose_tab_s2c)
        org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_choose_tab_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_choose_tab_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_s2c.class, org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        newTab_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_choose_tab_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_s2c build() {
        org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_s2c result = new org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.newTab_ = newTab_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_s2c.getDefaultInstance()) return this;
        if (other.getNewTab() != 0) {
          setNewTab(other.getNewTab());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                newTab_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int newTab_ ;
      /**
       * <code>uint32 new_tab = 1;</code>
       * @return The newTab.
       */
      @java.lang.Override
      public int getNewTab() {
        return newTab_;
      }
      /**
       * <code>uint32 new_tab = 1;</code>
       * @param value The newTab to set.
       * @return This builder for chaining.
       */
      public Builder setNewTab(int value) {

        newTab_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 new_tab = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearNewTab() {
        bitField0_ = (bitField0_ & ~0x00000001);
        newTab_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.relic_choose_tab_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.relic_choose_tab_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<relic_choose_tab_s2c>
        PARSER = new com.google.protobuf.AbstractParser<relic_choose_tab_s2c>() {
      @java.lang.Override
      public relic_choose_tab_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<relic_choose_tab_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<relic_choose_tab_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgRelic.relic_choose_tab_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface relic_change_tab_name_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.relic_change_tab_name_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 tab = 1;</code>
     * @return The tab.
     */
    int getTab();

    /**
     * <code>string name = 2;</code>
     * @return The name.
     */
    java.lang.String getName();
    /**
     * <code>string name = 2;</code>
     * @return The bytes for name.
     */
    com.google.protobuf.ByteString
        getNameBytes();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.relic_change_tab_name_c2s}
   */
  public static final class relic_change_tab_name_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.relic_change_tab_name_c2s)
      relic_change_tab_name_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use relic_change_tab_name_c2s.newBuilder() to construct.
    private relic_change_tab_name_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private relic_change_tab_name_c2s() {
      name_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new relic_change_tab_name_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_change_tab_name_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_change_tab_name_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_c2s.class, org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_c2s.Builder.class);
    }

    public static final int TAB_FIELD_NUMBER = 1;
    private int tab_ = 0;
    /**
     * <code>uint32 tab = 1;</code>
     * @return The tab.
     */
    @java.lang.Override
    public int getTab() {
      return tab_;
    }

    public static final int NAME_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private volatile java.lang.Object name_ = "";
    /**
     * <code>string name = 2;</code>
     * @return The name.
     */
    @java.lang.Override
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        name_ = s;
        return s;
      }
    }
    /**
     * <code>string name = 2;</code>
     * @return The bytes for name.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (tab_ != 0) {
        output.writeUInt32(1, tab_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(name_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, name_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (tab_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, tab_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(name_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, name_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_c2s other = (org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_c2s) obj;

      if (getTab()
          != other.getTab()) return false;
      if (!getName()
          .equals(other.getName())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TAB_FIELD_NUMBER;
      hash = (53 * hash) + getTab();
      hash = (37 * hash) + NAME_FIELD_NUMBER;
      hash = (53 * hash) + getName().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.relic_change_tab_name_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.relic_change_tab_name_c2s)
        org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_change_tab_name_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_change_tab_name_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_c2s.class, org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        tab_ = 0;
        name_ = "";
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_change_tab_name_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_c2s build() {
        org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_c2s result = new org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.tab_ = tab_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.name_ = name_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_c2s.getDefaultInstance()) return this;
        if (other.getTab() != 0) {
          setTab(other.getTab());
        }
        if (!other.getName().isEmpty()) {
          name_ = other.name_;
          bitField0_ |= 0x00000002;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                tab_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 18: {
                name_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int tab_ ;
      /**
       * <code>uint32 tab = 1;</code>
       * @return The tab.
       */
      @java.lang.Override
      public int getTab() {
        return tab_;
      }
      /**
       * <code>uint32 tab = 1;</code>
       * @param value The tab to set.
       * @return This builder for chaining.
       */
      public Builder setTab(int value) {

        tab_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 tab = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTab() {
        bitField0_ = (bitField0_ & ~0x00000001);
        tab_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object name_ = "";
      /**
       * <code>string name = 2;</code>
       * @return The name.
       */
      public java.lang.String getName() {
        java.lang.Object ref = name_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          name_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string name = 2;</code>
       * @return The bytes for name.
       */
      public com.google.protobuf.ByteString
          getNameBytes() {
        java.lang.Object ref = name_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          name_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string name = 2;</code>
       * @param value The name to set.
       * @return This builder for chaining.
       */
      public Builder setName(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        name_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>string name = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearName() {
        name_ = getDefaultInstance().getName();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }
      /**
       * <code>string name = 2;</code>
       * @param value The bytes for name to set.
       * @return This builder for chaining.
       */
      public Builder setNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        name_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.relic_change_tab_name_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.relic_change_tab_name_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<relic_change_tab_name_c2s>
        PARSER = new com.google.protobuf.AbstractParser<relic_change_tab_name_c2s>() {
      @java.lang.Override
      public relic_change_tab_name_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<relic_change_tab_name_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<relic_change_tab_name_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface relic_change_tab_name_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.relic_change_tab_name_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 tab = 1;</code>
     * @return The tab.
     */
    int getTab();

    /**
     * <code>string name = 2;</code>
     * @return The name.
     */
    java.lang.String getName();
    /**
     * <code>string name = 2;</code>
     * @return The bytes for name.
     */
    com.google.protobuf.ByteString
        getNameBytes();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.relic_change_tab_name_s2c}
   */
  public static final class relic_change_tab_name_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.relic_change_tab_name_s2c)
      relic_change_tab_name_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use relic_change_tab_name_s2c.newBuilder() to construct.
    private relic_change_tab_name_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private relic_change_tab_name_s2c() {
      name_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new relic_change_tab_name_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_change_tab_name_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_change_tab_name_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_s2c.class, org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_s2c.Builder.class);
    }

    public static final int TAB_FIELD_NUMBER = 1;
    private int tab_ = 0;
    /**
     * <code>uint32 tab = 1;</code>
     * @return The tab.
     */
    @java.lang.Override
    public int getTab() {
      return tab_;
    }

    public static final int NAME_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private volatile java.lang.Object name_ = "";
    /**
     * <code>string name = 2;</code>
     * @return The name.
     */
    @java.lang.Override
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        name_ = s;
        return s;
      }
    }
    /**
     * <code>string name = 2;</code>
     * @return The bytes for name.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (tab_ != 0) {
        output.writeUInt32(1, tab_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(name_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, name_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (tab_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, tab_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(name_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, name_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_s2c other = (org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_s2c) obj;

      if (getTab()
          != other.getTab()) return false;
      if (!getName()
          .equals(other.getName())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TAB_FIELD_NUMBER;
      hash = (53 * hash) + getTab();
      hash = (37 * hash) + NAME_FIELD_NUMBER;
      hash = (53 * hash) + getName().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.relic_change_tab_name_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.relic_change_tab_name_s2c)
        org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_change_tab_name_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_change_tab_name_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_s2c.class, org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        tab_ = 0;
        name_ = "";
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_change_tab_name_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_s2c build() {
        org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_s2c result = new org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.tab_ = tab_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.name_ = name_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_s2c.getDefaultInstance()) return this;
        if (other.getTab() != 0) {
          setTab(other.getTab());
        }
        if (!other.getName().isEmpty()) {
          name_ = other.name_;
          bitField0_ |= 0x00000002;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                tab_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 18: {
                name_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int tab_ ;
      /**
       * <code>uint32 tab = 1;</code>
       * @return The tab.
       */
      @java.lang.Override
      public int getTab() {
        return tab_;
      }
      /**
       * <code>uint32 tab = 1;</code>
       * @param value The tab to set.
       * @return This builder for chaining.
       */
      public Builder setTab(int value) {

        tab_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 tab = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTab() {
        bitField0_ = (bitField0_ & ~0x00000001);
        tab_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object name_ = "";
      /**
       * <code>string name = 2;</code>
       * @return The name.
       */
      public java.lang.String getName() {
        java.lang.Object ref = name_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          name_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string name = 2;</code>
       * @return The bytes for name.
       */
      public com.google.protobuf.ByteString
          getNameBytes() {
        java.lang.Object ref = name_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          name_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string name = 2;</code>
       * @param value The name to set.
       * @return This builder for chaining.
       */
      public Builder setName(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        name_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>string name = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearName() {
        name_ = getDefaultInstance().getName();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }
      /**
       * <code>string name = 2;</code>
       * @param value The bytes for name to set.
       * @return This builder for chaining.
       */
      public Builder setNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        name_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.relic_change_tab_name_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.relic_change_tab_name_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<relic_change_tab_name_s2c>
        PARSER = new com.google.protobuf.AbstractParser<relic_change_tab_name_s2c>() {
      @java.lang.Override
      public relic_change_tab_name_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<relic_change_tab_name_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<relic_change_tab_name_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_name_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface relic_change_tab_info_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.relic_change_tab_info_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 tab = 1;</code>
     * @return The tab.
     */
    int getTab();

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> 
        getPosInfoList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_key_value getPosInfo(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
     */
    int getPosInfoCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
        getPosInfoOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder getPosInfoOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.relic_change_tab_info_c2s}
   */
  public static final class relic_change_tab_info_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.relic_change_tab_info_c2s)
      relic_change_tab_info_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use relic_change_tab_info_c2s.newBuilder() to construct.
    private relic_change_tab_info_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private relic_change_tab_info_c2s() {
      posInfo_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new relic_change_tab_info_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_change_tab_info_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_change_tab_info_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_info_c2s.class, org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_info_c2s.Builder.class);
    }

    public static final int TAB_FIELD_NUMBER = 1;
    private int tab_ = 0;
    /**
     * <code>uint32 tab = 1;</code>
     * @return The tab.
     */
    @java.lang.Override
    public int getTab() {
      return tab_;
    }

    public static final int POS_INFO_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> posInfo_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> getPosInfoList() {
      return posInfo_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
        getPosInfoOrBuilderList() {
      return posInfo_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
     */
    @java.lang.Override
    public int getPosInfoCount() {
      return posInfo_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_key_value getPosInfo(int index) {
      return posInfo_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder getPosInfoOrBuilder(
        int index) {
      return posInfo_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (tab_ != 0) {
        output.writeUInt32(1, tab_);
      }
      for (int i = 0; i < posInfo_.size(); i++) {
        output.writeMessage(2, posInfo_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (tab_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, tab_);
      }
      for (int i = 0; i < posInfo_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, posInfo_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_info_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_info_c2s other = (org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_info_c2s) obj;

      if (getTab()
          != other.getTab()) return false;
      if (!getPosInfoList()
          .equals(other.getPosInfoList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TAB_FIELD_NUMBER;
      hash = (53 * hash) + getTab();
      if (getPosInfoCount() > 0) {
        hash = (37 * hash) + POS_INFO_FIELD_NUMBER;
        hash = (53 * hash) + getPosInfoList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_info_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_info_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_info_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_info_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_info_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_info_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_info_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_info_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_info_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_info_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_info_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_info_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_info_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.relic_change_tab_info_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.relic_change_tab_info_c2s)
        org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_info_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_change_tab_info_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_change_tab_info_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_info_c2s.class, org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_info_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_info_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        tab_ = 0;
        if (posInfoBuilder_ == null) {
          posInfo_ = java.util.Collections.emptyList();
        } else {
          posInfo_ = null;
          posInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_change_tab_info_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_info_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_info_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_info_c2s build() {
        org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_info_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_info_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_info_c2s result = new org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_info_c2s(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_info_c2s result) {
        if (posInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0)) {
            posInfo_ = java.util.Collections.unmodifiableList(posInfo_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.posInfo_ = posInfo_;
        } else {
          result.posInfo_ = posInfoBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_info_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.tab_ = tab_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_info_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_info_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_info_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_info_c2s.getDefaultInstance()) return this;
        if (other.getTab() != 0) {
          setTab(other.getTab());
        }
        if (posInfoBuilder_ == null) {
          if (!other.posInfo_.isEmpty()) {
            if (posInfo_.isEmpty()) {
              posInfo_ = other.posInfo_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensurePosInfoIsMutable();
              posInfo_.addAll(other.posInfo_);
            }
            onChanged();
          }
        } else {
          if (!other.posInfo_.isEmpty()) {
            if (posInfoBuilder_.isEmpty()) {
              posInfoBuilder_.dispose();
              posInfoBuilder_ = null;
              posInfo_ = other.posInfo_;
              bitField0_ = (bitField0_ & ~0x00000002);
              posInfoBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getPosInfoFieldBuilder() : null;
            } else {
              posInfoBuilder_.addAllMessages(other.posInfo_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                tab_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 18: {
                org.gof.demo.worldsrv.msg.Define.p_key_value m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_key_value.parser(),
                        extensionRegistry);
                if (posInfoBuilder_ == null) {
                  ensurePosInfoIsMutable();
                  posInfo_.add(m);
                } else {
                  posInfoBuilder_.addMessage(m);
                }
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int tab_ ;
      /**
       * <code>uint32 tab = 1;</code>
       * @return The tab.
       */
      @java.lang.Override
      public int getTab() {
        return tab_;
      }
      /**
       * <code>uint32 tab = 1;</code>
       * @param value The tab to set.
       * @return This builder for chaining.
       */
      public Builder setTab(int value) {

        tab_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 tab = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTab() {
        bitField0_ = (bitField0_ & ~0x00000001);
        tab_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> posInfo_ =
        java.util.Collections.emptyList();
      private void ensurePosInfoIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          posInfo_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_key_value>(posInfo_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_key_value, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder, org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> posInfoBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> getPosInfoList() {
        if (posInfoBuilder_ == null) {
          return java.util.Collections.unmodifiableList(posInfo_);
        } else {
          return posInfoBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public int getPosInfoCount() {
        if (posInfoBuilder_ == null) {
          return posInfo_.size();
        } else {
          return posInfoBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value getPosInfo(int index) {
        if (posInfoBuilder_ == null) {
          return posInfo_.get(index);
        } else {
          return posInfoBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public Builder setPosInfo(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value value) {
        if (posInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePosInfoIsMutable();
          posInfo_.set(index, value);
          onChanged();
        } else {
          posInfoBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public Builder setPosInfo(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder builderForValue) {
        if (posInfoBuilder_ == null) {
          ensurePosInfoIsMutable();
          posInfo_.set(index, builderForValue.build());
          onChanged();
        } else {
          posInfoBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public Builder addPosInfo(org.gof.demo.worldsrv.msg.Define.p_key_value value) {
        if (posInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePosInfoIsMutable();
          posInfo_.add(value);
          onChanged();
        } else {
          posInfoBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public Builder addPosInfo(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value value) {
        if (posInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePosInfoIsMutable();
          posInfo_.add(index, value);
          onChanged();
        } else {
          posInfoBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public Builder addPosInfo(
          org.gof.demo.worldsrv.msg.Define.p_key_value.Builder builderForValue) {
        if (posInfoBuilder_ == null) {
          ensurePosInfoIsMutable();
          posInfo_.add(builderForValue.build());
          onChanged();
        } else {
          posInfoBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public Builder addPosInfo(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder builderForValue) {
        if (posInfoBuilder_ == null) {
          ensurePosInfoIsMutable();
          posInfo_.add(index, builderForValue.build());
          onChanged();
        } else {
          posInfoBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public Builder addAllPosInfo(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_key_value> values) {
        if (posInfoBuilder_ == null) {
          ensurePosInfoIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, posInfo_);
          onChanged();
        } else {
          posInfoBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public Builder clearPosInfo() {
        if (posInfoBuilder_ == null) {
          posInfo_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          posInfoBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public Builder removePosInfo(int index) {
        if (posInfoBuilder_ == null) {
          ensurePosInfoIsMutable();
          posInfo_.remove(index);
          onChanged();
        } else {
          posInfoBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value.Builder getPosInfoBuilder(
          int index) {
        return getPosInfoFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder getPosInfoOrBuilder(
          int index) {
        if (posInfoBuilder_ == null) {
          return posInfo_.get(index);  } else {
          return posInfoBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
           getPosInfoOrBuilderList() {
        if (posInfoBuilder_ != null) {
          return posInfoBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(posInfo_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value.Builder addPosInfoBuilder() {
        return getPosInfoFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_key_value.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value.Builder addPosInfoBuilder(
          int index) {
        return getPosInfoFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_key_value.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value pos_info = 2;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value.Builder> 
           getPosInfoBuilderList() {
        return getPosInfoFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_key_value, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder, org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
          getPosInfoFieldBuilder() {
        if (posInfoBuilder_ == null) {
          posInfoBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_key_value, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder, org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder>(
                  posInfo_,
                  ((bitField0_ & 0x00000002) != 0),
                  getParentForChildren(),
                  isClean());
          posInfo_ = null;
        }
        return posInfoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.relic_change_tab_info_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.relic_change_tab_info_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_info_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_info_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_info_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<relic_change_tab_info_c2s>
        PARSER = new com.google.protobuf.AbstractParser<relic_change_tab_info_c2s>() {
      @java.lang.Override
      public relic_change_tab_info_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<relic_change_tab_info_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<relic_change_tab_info_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgRelic.relic_change_tab_info_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface relic_unlock_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.relic_unlock_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 cfg_id = 1;</code>
     * @return The cfgId.
     */
    int getCfgId();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.relic_unlock_c2s}
   */
  public static final class relic_unlock_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.relic_unlock_c2s)
      relic_unlock_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use relic_unlock_c2s.newBuilder() to construct.
    private relic_unlock_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private relic_unlock_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new relic_unlock_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_unlock_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_unlock_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_c2s.class, org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_c2s.Builder.class);
    }

    public static final int CFG_ID_FIELD_NUMBER = 1;
    private int cfgId_ = 0;
    /**
     * <code>uint32 cfg_id = 1;</code>
     * @return The cfgId.
     */
    @java.lang.Override
    public int getCfgId() {
      return cfgId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (cfgId_ != 0) {
        output.writeUInt32(1, cfgId_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (cfgId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, cfgId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_c2s other = (org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_c2s) obj;

      if (getCfgId()
          != other.getCfgId()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CFG_ID_FIELD_NUMBER;
      hash = (53 * hash) + getCfgId();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.relic_unlock_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.relic_unlock_c2s)
        org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_unlock_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_unlock_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_c2s.class, org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        cfgId_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_unlock_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_c2s build() {
        org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_c2s result = new org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.cfgId_ = cfgId_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_c2s.getDefaultInstance()) return this;
        if (other.getCfgId() != 0) {
          setCfgId(other.getCfgId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                cfgId_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int cfgId_ ;
      /**
       * <code>uint32 cfg_id = 1;</code>
       * @return The cfgId.
       */
      @java.lang.Override
      public int getCfgId() {
        return cfgId_;
      }
      /**
       * <code>uint32 cfg_id = 1;</code>
       * @param value The cfgId to set.
       * @return This builder for chaining.
       */
      public Builder setCfgId(int value) {

        cfgId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 cfg_id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCfgId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        cfgId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.relic_unlock_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.relic_unlock_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<relic_unlock_c2s>
        PARSER = new com.google.protobuf.AbstractParser<relic_unlock_c2s>() {
      @java.lang.Override
      public relic_unlock_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<relic_unlock_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<relic_unlock_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface relic_unlock_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.relic_unlock_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.org.gof.demo.worldsrv.msg.p_relic relic = 1;</code>
     * @return Whether the relic field is set.
     */
    boolean hasRelic();
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_relic relic = 1;</code>
     * @return The relic.
     */
    org.gof.demo.worldsrv.msg.Define.p_relic getRelic();
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_relic relic = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_relicOrBuilder getRelicOrBuilder();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.relic_unlock_s2c}
   */
  public static final class relic_unlock_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.relic_unlock_s2c)
      relic_unlock_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use relic_unlock_s2c.newBuilder() to construct.
    private relic_unlock_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private relic_unlock_s2c() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new relic_unlock_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_unlock_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_unlock_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_s2c.class, org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_s2c.Builder.class);
    }

    private int bitField0_;
    public static final int RELIC_FIELD_NUMBER = 1;
    private org.gof.demo.worldsrv.msg.Define.p_relic relic_;
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_relic relic = 1;</code>
     * @return Whether the relic field is set.
     */
    @java.lang.Override
    public boolean hasRelic() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_relic relic = 1;</code>
     * @return The relic.
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_relic getRelic() {
      return relic_ == null ? org.gof.demo.worldsrv.msg.Define.p_relic.getDefaultInstance() : relic_;
    }
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_relic relic = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_relicOrBuilder getRelicOrBuilder() {
      return relic_ == null ? org.gof.demo.worldsrv.msg.Define.p_relic.getDefaultInstance() : relic_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getRelic());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getRelic());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_s2c other = (org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_s2c) obj;

      if (hasRelic() != other.hasRelic()) return false;
      if (hasRelic()) {
        if (!getRelic()
            .equals(other.getRelic())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasRelic()) {
        hash = (37 * hash) + RELIC_FIELD_NUMBER;
        hash = (53 * hash) + getRelic().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.relic_unlock_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.relic_unlock_s2c)
        org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_unlock_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_unlock_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_s2c.class, org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_s2c.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getRelicFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        relic_ = null;
        if (relicBuilder_ != null) {
          relicBuilder_.dispose();
          relicBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgRelic.internal_static_org_gof_demo_worldsrv_msg_relic_unlock_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_s2c build() {
        org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_s2c result = new org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_s2c result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.relic_ = relicBuilder_ == null
              ? relic_
              : relicBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_s2c.getDefaultInstance()) return this;
        if (other.hasRelic()) {
          mergeRelic(other.getRelic());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getRelicFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private org.gof.demo.worldsrv.msg.Define.p_relic relic_;
      private com.google.protobuf.SingleFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_relic, org.gof.demo.worldsrv.msg.Define.p_relic.Builder, org.gof.demo.worldsrv.msg.Define.p_relicOrBuilder> relicBuilder_;
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_relic relic = 1;</code>
       * @return Whether the relic field is set.
       */
      public boolean hasRelic() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_relic relic = 1;</code>
       * @return The relic.
       */
      public org.gof.demo.worldsrv.msg.Define.p_relic getRelic() {
        if (relicBuilder_ == null) {
          return relic_ == null ? org.gof.demo.worldsrv.msg.Define.p_relic.getDefaultInstance() : relic_;
        } else {
          return relicBuilder_.getMessage();
        }
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_relic relic = 1;</code>
       */
      public Builder setRelic(org.gof.demo.worldsrv.msg.Define.p_relic value) {
        if (relicBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          relic_ = value;
        } else {
          relicBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_relic relic = 1;</code>
       */
      public Builder setRelic(
          org.gof.demo.worldsrv.msg.Define.p_relic.Builder builderForValue) {
        if (relicBuilder_ == null) {
          relic_ = builderForValue.build();
        } else {
          relicBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_relic relic = 1;</code>
       */
      public Builder mergeRelic(org.gof.demo.worldsrv.msg.Define.p_relic value) {
        if (relicBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
            relic_ != null &&
            relic_ != org.gof.demo.worldsrv.msg.Define.p_relic.getDefaultInstance()) {
            getRelicBuilder().mergeFrom(value);
          } else {
            relic_ = value;
          }
        } else {
          relicBuilder_.mergeFrom(value);
        }
        if (relic_ != null) {
          bitField0_ |= 0x00000001;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_relic relic = 1;</code>
       */
      public Builder clearRelic() {
        bitField0_ = (bitField0_ & ~0x00000001);
        relic_ = null;
        if (relicBuilder_ != null) {
          relicBuilder_.dispose();
          relicBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_relic relic = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_relic.Builder getRelicBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getRelicFieldBuilder().getBuilder();
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_relic relic = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_relicOrBuilder getRelicOrBuilder() {
        if (relicBuilder_ != null) {
          return relicBuilder_.getMessageOrBuilder();
        } else {
          return relic_ == null ?
              org.gof.demo.worldsrv.msg.Define.p_relic.getDefaultInstance() : relic_;
        }
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_relic relic = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_relic, org.gof.demo.worldsrv.msg.Define.p_relic.Builder, org.gof.demo.worldsrv.msg.Define.p_relicOrBuilder> 
          getRelicFieldBuilder() {
        if (relicBuilder_ == null) {
          relicBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_relic, org.gof.demo.worldsrv.msg.Define.p_relic.Builder, org.gof.demo.worldsrv.msg.Define.p_relicOrBuilder>(
                  getRelic(),
                  getParentForChildren(),
                  isClean());
          relic_ = null;
        }
        return relicBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.relic_unlock_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.relic_unlock_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<relic_unlock_s2c>
        PARSER = new com.google.protobuf.AbstractParser<relic_unlock_s2c>() {
      @java.lang.Override
      public relic_unlock_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<relic_unlock_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<relic_unlock_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgRelic.relic_unlock_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_relic_info_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_relic_info_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_relic_info_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_relic_info_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_relic_equip_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_relic_equip_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_relic_equip_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_relic_equip_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_relic_level_up_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_relic_level_up_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_relic_level_up_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_relic_level_up_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_relic_find_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_relic_find_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_relic_find_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_relic_find_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_relic_tab_info_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_relic_tab_info_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_relic_tab_info_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_relic_tab_info_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_relic_tab_info_update_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_relic_tab_info_update_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_relic_choose_tab_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_relic_choose_tab_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_relic_choose_tab_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_relic_choose_tab_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_relic_change_tab_name_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_relic_change_tab_name_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_relic_change_tab_name_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_relic_change_tab_name_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_relic_change_tab_info_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_relic_change_tab_info_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_relic_unlock_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_relic_unlock_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_relic_unlock_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_relic_unlock_s2c_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\017msg.relic.proto\022\031org.gof.demo.worldsrv" +
      ".msg\032\roptions.proto\032\014define.proto\"\027\n\016rel" +
      "ic_info_c2s:\005\210\303\032\201\"\"O\n\016relic_info_s2c\0226\n\n" +
      "relic_list\030\001 \003(\0132\".org.gof.demo.worldsrv" +
      ".msg.p_relic:\005\210\303\032\201\"\"$\n\017relic_equip_c2s\022\n" +
      "\n\002id\030\001 \001(\004:\005\210\303\032\202\"\"Q\n\017relic_equip_s2c\0227\n\013" +
      "change_list\030\001 \003(\0132\".org.gof.demo.worldsr" +
      "v.msg.p_relic:\005\210\303\032\202\"\"\'\n\022relic_level_up_c" +
      "2s\022\n\n\002id\030\001 \001(\004:\005\210\303\032\203\"\"N\n\022relic_level_up_" +
      "s2c\0221\n\005relic\030\001 \001(\0132\".org.gof.demo.worlds" +
      "rv.msg.p_relic:\005\210\303\032\203\"\"\027\n\016relic_find_c2s:" +
      "\005\210\303\032\204\"\"J\n\016relic_find_s2c\0221\n\005relic\030\001 \001(\0132" +
      "\".org.gof.demo.worldsrv.msg.p_relic:\005\210\303\032" +
      "\204\"\"\033\n\022relic_tab_info_c2s:\005\210\303\032\205\"\"g\n\022relic" +
      "_tab_info_s2c\022\013\n\003tab\030\001 \001(\r\022=\n\010tab_list\030\002" +
      " \003(\0132+.org.gof.demo.worldsrv.msg.p_relic" +
      "_tab_info:\005\210\303\032\205\"\"i\n\031relic_tab_info_updat" +
      "e_s2c\022\013\n\003tab\030\001 \001(\r\0228\n\010pos_info\030\002 \003(\0132&.o" +
      "rg.gof.demo.worldsrv.msg.p_key_value:\005\210\303" +
      "\032\206\"\"*\n\024relic_choose_tab_c2s\022\013\n\003tab\030\001 \001(\r" +
      ":\005\210\303\032\207\"\".\n\024relic_choose_tab_s2c\022\017\n\007new_t" +
      "ab\030\001 \001(\r:\005\210\303\032\207\"\"=\n\031relic_change_tab_name" +
      "_c2s\022\013\n\003tab\030\001 \001(\r\022\014\n\004name\030\002 \001(\t:\005\210\303\032\210\"\"=" +
      "\n\031relic_change_tab_name_s2c\022\013\n\003tab\030\001 \001(\r" +
      "\022\014\n\004name\030\002 \001(\t:\005\210\303\032\210\"\"i\n\031relic_change_ta" +
      "b_info_c2s\022\013\n\003tab\030\001 \001(\r\0228\n\010pos_info\030\002 \003(" +
      "\0132&.org.gof.demo.worldsrv.msg.p_key_valu" +
      "e:\005\210\303\032\211\"\")\n\020relic_unlock_c2s\022\016\n\006cfg_id\030\001" +
      " \001(\r:\005\210\303\032\212\"\"L\n\020relic_unlock_s2c\0221\n\005relic" +
      "\030\001 \001(\0132\".org.gof.demo.worldsrv.msg.p_rel" +
      "ic:\005\210\303\032\212\"b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          org.gof.demo.worldsrv.msg.Options.getDescriptor(),
          org.gof.demo.worldsrv.msg.Define.getDescriptor(),
        });
    internal_static_org_gof_demo_worldsrv_msg_relic_info_c2s_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_org_gof_demo_worldsrv_msg_relic_info_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_relic_info_c2s_descriptor,
        new java.lang.String[] { });
    internal_static_org_gof_demo_worldsrv_msg_relic_info_s2c_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_org_gof_demo_worldsrv_msg_relic_info_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_relic_info_s2c_descriptor,
        new java.lang.String[] { "RelicList", });
    internal_static_org_gof_demo_worldsrv_msg_relic_equip_c2s_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_org_gof_demo_worldsrv_msg_relic_equip_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_relic_equip_c2s_descriptor,
        new java.lang.String[] { "Id", });
    internal_static_org_gof_demo_worldsrv_msg_relic_equip_s2c_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_org_gof_demo_worldsrv_msg_relic_equip_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_relic_equip_s2c_descriptor,
        new java.lang.String[] { "ChangeList", });
    internal_static_org_gof_demo_worldsrv_msg_relic_level_up_c2s_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_org_gof_demo_worldsrv_msg_relic_level_up_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_relic_level_up_c2s_descriptor,
        new java.lang.String[] { "Id", });
    internal_static_org_gof_demo_worldsrv_msg_relic_level_up_s2c_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_org_gof_demo_worldsrv_msg_relic_level_up_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_relic_level_up_s2c_descriptor,
        new java.lang.String[] { "Relic", });
    internal_static_org_gof_demo_worldsrv_msg_relic_find_c2s_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_org_gof_demo_worldsrv_msg_relic_find_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_relic_find_c2s_descriptor,
        new java.lang.String[] { });
    internal_static_org_gof_demo_worldsrv_msg_relic_find_s2c_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_org_gof_demo_worldsrv_msg_relic_find_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_relic_find_s2c_descriptor,
        new java.lang.String[] { "Relic", });
    internal_static_org_gof_demo_worldsrv_msg_relic_tab_info_c2s_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_org_gof_demo_worldsrv_msg_relic_tab_info_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_relic_tab_info_c2s_descriptor,
        new java.lang.String[] { });
    internal_static_org_gof_demo_worldsrv_msg_relic_tab_info_s2c_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_org_gof_demo_worldsrv_msg_relic_tab_info_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_relic_tab_info_s2c_descriptor,
        new java.lang.String[] { "Tab", "TabList", });
    internal_static_org_gof_demo_worldsrv_msg_relic_tab_info_update_s2c_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_org_gof_demo_worldsrv_msg_relic_tab_info_update_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_relic_tab_info_update_s2c_descriptor,
        new java.lang.String[] { "Tab", "PosInfo", });
    internal_static_org_gof_demo_worldsrv_msg_relic_choose_tab_c2s_descriptor =
      getDescriptor().getMessageTypes().get(11);
    internal_static_org_gof_demo_worldsrv_msg_relic_choose_tab_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_relic_choose_tab_c2s_descriptor,
        new java.lang.String[] { "Tab", });
    internal_static_org_gof_demo_worldsrv_msg_relic_choose_tab_s2c_descriptor =
      getDescriptor().getMessageTypes().get(12);
    internal_static_org_gof_demo_worldsrv_msg_relic_choose_tab_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_relic_choose_tab_s2c_descriptor,
        new java.lang.String[] { "NewTab", });
    internal_static_org_gof_demo_worldsrv_msg_relic_change_tab_name_c2s_descriptor =
      getDescriptor().getMessageTypes().get(13);
    internal_static_org_gof_demo_worldsrv_msg_relic_change_tab_name_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_relic_change_tab_name_c2s_descriptor,
        new java.lang.String[] { "Tab", "Name", });
    internal_static_org_gof_demo_worldsrv_msg_relic_change_tab_name_s2c_descriptor =
      getDescriptor().getMessageTypes().get(14);
    internal_static_org_gof_demo_worldsrv_msg_relic_change_tab_name_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_relic_change_tab_name_s2c_descriptor,
        new java.lang.String[] { "Tab", "Name", });
    internal_static_org_gof_demo_worldsrv_msg_relic_change_tab_info_c2s_descriptor =
      getDescriptor().getMessageTypes().get(15);
    internal_static_org_gof_demo_worldsrv_msg_relic_change_tab_info_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_relic_change_tab_info_c2s_descriptor,
        new java.lang.String[] { "Tab", "PosInfo", });
    internal_static_org_gof_demo_worldsrv_msg_relic_unlock_c2s_descriptor =
      getDescriptor().getMessageTypes().get(16);
    internal_static_org_gof_demo_worldsrv_msg_relic_unlock_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_relic_unlock_c2s_descriptor,
        new java.lang.String[] { "CfgId", });
    internal_static_org_gof_demo_worldsrv_msg_relic_unlock_s2c_descriptor =
      getDescriptor().getMessageTypes().get(17);
    internal_static_org_gof_demo_worldsrv_msg_relic_unlock_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_relic_unlock_s2c_descriptor,
        new java.lang.String[] { "Relic", });
    com.google.protobuf.ExtensionRegistry registry =
        com.google.protobuf.ExtensionRegistry.newInstance();
    registry.add(org.gof.demo.worldsrv.msg.Options.msgid);
    com.google.protobuf.Descriptors.FileDescriptor
        .internalUpdateFileDescriptor(descriptor, registry);
    org.gof.demo.worldsrv.msg.Options.getDescriptor();
    org.gof.demo.worldsrv.msg.Define.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
