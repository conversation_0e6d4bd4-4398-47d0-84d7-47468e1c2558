package org.gof.demo.worldsrv.team;

import org.gof.core.Service;
import org.gof.core.gen.proxy.ProxyBase;
import org.gof.core.support.Param;
import org.gof.core.support.function.GofFunction2;
import org.gof.core.support.function.GofFunction3;

public final class TeamGlobalServiceProxy extends ProxyBase {
	public final class EnumCall {
		public static final String TeamGlobalService_createTeam_int_long_int = "TeamGlobalService.createTeam(int, long, int)";
		public static final String TeamGlobalService_mergeTeam_TeamData_int = "TeamGlobalService.mergeTeam(TeamData, int)";
		public static final String TeamGlobalService_update_Objects = "TeamGlobalService.update(Object[])";
		public static final String TeamGlobalService_updateSrvTeamCount_int_int = "TeamGlobalService.updateSrvTeamCount(int, int)";
	}
	// 调用者信息
	private String callerInfo;
	// 当此参数为true时同NODE间传递将不在进行克隆，直接使用此对象进行。可极大提高性能，但如果设置不当可能引起错误。
	private boolean immutableOnce;
	
	/**
	 * 私有构造函数
	 * 防止实例被私自创建 必须通过newInstance函数
	 */
	private TeamGlobalServiceProxy() {}
	

	@Override
	public void listenResult(GofFunction2<Param, Param> method, Object... context) {

	}

	@Override
	public void listenResult(GofFunction3<Boolean, Param, Param> method, Object... context) {

	}

	@Override
	public Param waitForResult() {
		return null;
	}

	@Override
	public <T> T getMethodFunction(Service serv, int methodKey) {
		return null;
	}

}
