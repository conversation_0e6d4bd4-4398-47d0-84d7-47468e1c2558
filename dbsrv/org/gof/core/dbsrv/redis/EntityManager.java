package org.gof.core.dbsrv.redis;

import co.paralleluniverse.fibers.Suspendable;
import com.alibaba.fastjson.JSONObject;
import io.vertx.core.*;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.sync.SyncVerticle;
import io.vertx.mysqlclient.MySQLPool;
import io.vertx.redis.client.*;
import org.gof.core.Port;
import org.gof.core.Record;
import org.gof.core.dbsrv.DB;
import org.gof.core.dbsrv.redis.support.EntityMeta;
import org.gof.core.entity.EntityBase;
import org.gof.core.support.*;
import org.gof.core.support.log.LogCore;
import org.gof.core.utils.StrUtil;
import org.gof.demo.worldsrv.entity.Equip;
import org.gof.demo.worldsrv.support.C;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.Util;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.util.*;
import java.util.Map.Entry;
import java.util.stream.Collectors;

import static org.gof.core.dbsrv.DBPartService.MAX_GET_COUNT;

public class EntityManager extends SyncVerticle{
    public final static Logger logger = LoggerFactory.getLogger(EntityManager.class);
    //wrapped client
    public static RedisAPI redisClient;
    //real client, support batch option
    public static Redis redis;
    public static MySQLPool jdbcClientGame;
    public static final long lock_timeout = 3000;// 单次锁超时时间（单位毫秒）
    public static final int lock_expire = 5;// 锁过期时间（单位秒）
    public static final int lock_sleep = 10;// 锁请求休眠时间（单位毫秒）
    public static final String ALL_LIST_KEY = "-1";// 全表列表关键字

    public static long TABLE_INIT_ID = 1;//初始表ID
    public static int REDIS_EXPIRE_TIME = 7 * Tool.DAY;//redis实体类过期时间（单位秒，如果大于0表示会过期，并且要从数据库加载）
    public static int UPDATE_DB_DELAY_TIME = 5 * Tool.MINUTE;// 延迟入库时间
    public static int REDIS_EXPIRE_UPDATE_TIME = 7 * Tool.DAY;// 提前多久更新redis过期时间

    public static RedisAPI redisClientAdmin;
    public static RedisAPI redisClientCross;

    /**
     * 初始化配置
     */
    public static void init(RedisAPI redis, MySQLPool jdbcClientGame) {
        logger.info("EntityManager init ");
        EntityManager.redisClient = redis;
        EntityManager.jdbcClientGame = jdbcClientGame;
    }

    public static void initAdmin(RedisAPI redis) {
        logger.info("EntityManager init admin redis ");
        EntityManager.redisClientAdmin = redis;
    }

    public static void initCross(RedisAPI redis) {
        logger.info("EntityManager init admin cross redis ");
        EntityManager.redisClientCross = redis;
    }
    /**
     * 初始化参数（每次重载serverParams表需要调用这个方法）
     */
    public static void initParams(JsonArray list) {
        logger.info("EntityManager initParams !!!!!!!!!!!!");
        for (int i = 0, size = list.size(); i < size; i++) {
            JsonObject obj = list.getJsonObject(i);
            String name = obj.getString("name");
            String value = obj.getString("value");
            if (name.equals("TABLE_INIT_ID")) {
                TABLE_INIT_ID = Long.parseLong(value);
                logger.info("TABLE_INIT_ID = {}" + TABLE_INIT_ID);
            } else if (name.equals("REDIS_EXPIRE_TIME")) {
                REDIS_EXPIRE_TIME = Integer.parseInt(value);
                logger.info("REDIS_EXPIRE_TIME = {}" + REDIS_EXPIRE_TIME);
            } else if (name.equals("UPDATE_DB_DELAY_TIME")) {
                UPDATE_DB_DELAY_TIME = Integer.parseInt(value);
                logger.info("UPDATE_DB_DELAY_TIME = {}" + UPDATE_DB_DELAY_TIME);
            } else if (name.equals("REDIS_EXPIRE_UPDATE_TIME")) {
                REDIS_EXPIRE_UPDATE_TIME = Integer.parseInt(value);
                logger.info("REDIS_EXPIRE_UPDATE_TIME = {}" + REDIS_EXPIRE_UPDATE_TIME);
            }
        }
    }

    public static RedisAPI getRedisClient() {
        return redisClient;
    }

    // redis不存在时是否读取数据库
    private static boolean isLoadDB() {
        return REDIS_EXPIRE_TIME > 0;
    }

    /**
     * 获取用户延迟入库列表key
     */
    public static String getUserUpdateDbListKey(long userId) {
        return "list.updatedb.user." + userId;
    }

    private static final String db_update_queue_key="vdb.list";

    /***
     * 获取更新数据库key
     */
    public static final String getUpdateDbKey() {
        if(S.isGameServer){
            return db_update_queue_key+":game-"+ C.GAME_SERVER_ID;
        }else{
            return db_update_queue_key;
        }

    }

    /**
     * 生成redisKey
     * @param entityName
     * @param values
     * @return
     */
    public static String getKey(String entityName, Object... values) {
        String key = entityName;
        for (int i = 0; i < values.length; i++) {
            key += "." + values[i];
        }
        return key;
    }

    public static String getValue(Object... values) {
        String value = "";
        for (int i = 0; i < values.length; i++) {
            if (value.equals("")) {
                value = String.valueOf(values[i]);
            } else {
                value += "." + values[i];
            }
        }
        return value;
    }

    /**
     *
     * [Entity类名].[LISTKEY_1].[LISTKEY_2]
     * key结构：GuildWeekBattleLog.week.guildId
     * 示例key:GuildWeekBattleLog.2023.3000110002
     * 存储的hash结构为：key-战斗日志的ID，value-战斗日志的具体内容
     *
     * @param entityName GuildWeekBattleLog
     * @param listKeyField String LISTKEY = "groupPvp.seasonSn";
     * @param listKeyId groupPvp的值,seasonSn的值
     * @return
     */
    public static String getListKey(String entityName, String listKeyField, Object... listKeyId) {
        if (listKeyField.equals(ALL_LIST_KEY)) {
            return entityName;
        } else {
            return getKey(entityName, listKeyId);
        }
    }

    public static String getEntityInitKey(String entityKey) {
        return "entity.init." + entityKey;
    }

    /**
     * 获取额外的排序列表key
     * @param listKey
     */
    public static String getOrderListKey(String listKey) {
        return "list." + listKey;
    }


    /**
     * 根据ID获取实体
     * @param cls
     * @param id
     * @return
     * @param <E>
     */
    @Suspendable
    public static <E extends EntityBase> E getEntity(Class<E> cls, long id) {
        return loadEntity(cls, false, id);
    }

    /**
     * 根据ID获取实体类，不存在返回null
     */
    @Suspendable
    public static <E extends EntityBase> void getEntityAsync(Class<E> cls, long id, Handler<AsyncResult<E>> handler) {
        loadEntityAsync(cls, false,handler, id );
    }

    private static String getTableName(Class<?> cls){
        // 获取指定的字段
        Field field = null;
        try {
            field = cls.getDeclaredField("tableName");
            // 设置字段为可访问
            field.setAccessible(true);
            // 获取静态字段的值
            Object value = field.get(null);  // 对于静态字段，传递null作为实例对象
            return (String) value;
        } catch (Exception e) {
            throw new SysException(e);
        }
    }

    private static <E extends EntityBase> boolean isAutoCache(Class<E> cls){
        // 获取指定的字段
        Field field = null;
        try {
            field = cls.getDeclaredField("autoCache");
            // 设置字段为可访问
            field.setAccessible(true);
            // 获取静态字段的值
            Object value = field.get(null);  // 对于静态字段，传递null作为实例对象
            return (Boolean) value;
        } catch (Exception e) {
            throw new SysException(e);
        }
    }

    /**
     * 根据ID从redis批量获取实例
     * @param port
     * @param redisBaseKey
     * @param ids
     * @param handler
     */
    private static void batchGetEntityFromRedis(Port port, String redisBaseKey, List<Long> ids, Handler<AsyncResult<List<JsonObject>>> handler){
        List<Request> batchReq = buildBatchHGetAllReq(redisBaseKey, ids);
        RedisTools.batch(redis, batchReq, redisRes -> {
           if(redisRes.failed()){
               Log.temp.info("===batchGetEntityFromRedis failed! redisRes={}, ids={}", redisRes.cause(), ids);
               AsyncActionResult.fail(port,handler,redisRes.cause());
               return;
           }
            AsyncActionResult.success(port,handler,redisRes.result());
        });
    }

    /**
     * 根据ID列表批量查询数据库(仅供外部调用)
     * @param cls 类名
     * @param ids id列表
     * @param handler
     * @param <E>
     */
    public static <E extends EntityBase> void batchGetEntityFromDB(Class<E> cls, List<Long> ids, Handler<AsyncResult<List<E>>> handler){
        Port port = Port.getCurrent();
        String tableName = getTableName(cls);
        batchGetEntityFromDB(port,cls,tableName,ids,handler);
    }

    /**
     * 根据ID列表批量查询数据库
     * @param port port线程
     * @param cls 类名
     * @param tableName db表名
     * @param ids id列表
     * @param handler
     * @param <E>
     */
    private static <E extends EntityBase> void batchGetEntityFromDB(Port port, Class<E> cls, String tableName, List<Long> ids, Handler<AsyncResult<List<E>>> handler){
        if (ids.isEmpty()) {
            handler.handle(Future.succeededFuture(new ArrayList<>()));
            return;
        }
        DB db = DB.newInstance(tableName);
        db.find(ids);
        //需要再回调中返回
        db.listenResult((timeout, returns, context) -> {
            if (timeout) {
                //查询超时
                String errMsg = Util.createStr("wait for mysql[dbPart] return data timeout! cls={},tableName={},ids={}", cls, tableName, ids);
                logger.error(errMsg);
                handler.handle(Future.failedFuture(errMsg));
                return;
            }
            List<Record> records = returns.get();
            List<E> entityList;
            if (records == null || records.isEmpty()) {
                entityList = new ArrayList<>(0);
            } else {
                entityList = new ArrayList<>(records.size());
                if (!records.isEmpty()) {
                    for (Record record : records) {
                        try {
                            Constructor<E> constructor = cls.getConstructor(Record.class);
                            E entity = constructor.newInstance(record);
                            entity.reset();
                            entityList.add(entity);
                        } catch (Exception ex) {
                            logger.error("convert mysql[dbPart] returns data to entity failed! cls={},tableName={},record={}", cls, tableName, record, ex);
                            handler.handle(Future.failedFuture(ex));
                        }
                    }
                }
            }
            //处理合并结果并返回
            handler.handle(Future.succeededFuture(entityList));
        });
    }

    /**
     * 只做批量查询数据库，不反写redis缓存
     * @param cls
     * @param tableName
     * @param colName
     * @param ids
     * @param needFlush
     * @param handler
     * @param <E>
     */
    private static <E extends EntityBase> void batchFindByDB(Class<E> cls, String tableName, String colName, List<Long> ids, boolean needFlush, Handler<AsyncResult<List<E>>> handler){
        if (ids.isEmpty()) {
            handler.handle(Future.succeededFuture(new ArrayList<>()));
            return;
        }
        DB db = DB.newInstance(tableName);

        String sql = Utils.createStr(" WHERE {} IN ({})", colName, Utils.listToSqlString(ids));
        db.findByQuery(needFlush, sql);
        //需要在回调中返回
        db.listenResult((timeout, returns, context) -> {
            if (timeout) {
                //查询超时
                String errMsg = Util.createStr("wait for mysql[dbPart] return data timeout! cls={},tableName={},ids={}", cls, tableName, ids);
                logger.error(errMsg);
                handler.handle(Future.failedFuture(errMsg));
                return;
            }
            List<Record> records = returns.get();
            List<E> entityList;
            if (records == null || records.isEmpty()) {
                entityList = new ArrayList<>(0);
            } else {
                entityList = new ArrayList<>(records.size());
                if (!records.isEmpty()) {
                    for (Record record : records) {
                        try {
                            Constructor<E> constructor = cls.getConstructor(Record.class);
                            E entity = constructor.newInstance(record);
                            entity.reset();
                            entityList.add(entity);
                        } catch (Exception ex) {
                            logger.error("convert mysql[dbPart] returns data to entity failed! cls={},tableName={},record={}", cls, tableName, record, ex);
                            handler.handle(Future.failedFuture(ex));
                        }
                    }
                }
            }
            //处理合并结果并返回
            handler.handle(Future.succeededFuture(entityList));
        });
    }

    /**
     *   根据主键批量返回数据
     *   <pre>注意listkey类型的无法从redis查询数据，如果要优先查询redis，请使用其他方法</pre>
     * @param cls 实体类
     * @param ids
     * @param handler
     *
     */
    @Suspendable
    public static <E extends EntityBase> void batchGetEntity(Class<E> cls, List<Long> ids, Handler<AsyncResult<List<E>>> handler) {
        String redisEntityKey = cls.getSimpleName();
        String tableName = getTableName(cls);
        String listKeyField = getListKeyField(cls);// 获取列表关键字段
        Port port = Port.getCurrent();
        // 单条数据
        boolean oneToMany = !Tool.isEmpty(listKeyField);
        if (!oneToMany) {
            // 单条数据的传参只能有1个值，就是dbid
            batchGetEntityFromRedis(port, redisEntityKey, ids, redisRes -> {
                if (redisRes.failed()) {
                    logger.error("===batchGetEntity failed! redisRes={}, ids={}", redisRes.cause(), ids);
                    handler.handle(Future.failedFuture(redisRes.cause()));
                    return;
                }
                List<Long> findIds = new ArrayList<>();
                List<E> entityList = new ArrayList<>();
                List<JsonObject> cacheResult = redisRes.result();
                cacheResult.forEach(r -> {
                    if (!r.isEmpty()) {
                        if(r.containsKey("id")){
                            findIds.add(Long.parseLong(r.getString("id")));//主键名称固定id
                        } else {
                            logger.error("===数据可能存在问题, 无id字段, table:{}, res={}", cls.getSimpleName(), r);
                        }
                        E entity = toEntity(cls, r);
                        entityList.add(entity);
                    }
                });
                if(ids.size()==findIds.size()){
                    handler.handle(Future.succeededFuture(entityList));
                    return;
                }
                List<Long> remainIds = Utils.diffElement(ids, findIds);
                batchGetEntityFromDB(port, cls, tableName, remainIds, dbRes -> {
                    if (dbRes.failed()) {
                        logger.error("===batchGetEntityFromDB failed! redisRes={}, tableName={}, ids={}", dbRes.cause(), tableName,remainIds);
                        handler.handle(Future.failedFuture(dbRes.cause()));
                        return;
                    }
                    entityList.addAll(dbRes.result());
                    handler.handle(Future.succeededFuture(entityList));
                    redisSetEntities(entityList);
                });
            });
        }else { //ListKey类型的Entity查询,此接口只能通过数据库查询，查询redis需要listKey对应的参数值
            String colName=listKeyField,listKeyTemp = listKeyField;
            if(listKeyTemp.contains(".")){
                colName = listKeyTemp.split(".")[0];
            }
            String columnName = colName;
            // 列表类型的数据，返回值是列表
            batchFindListByRedis(port, cls, redisEntityKey, ids, redisRes -> {
                if (redisRes.failed()) {
                    logger.error("===batchGetEntity failed! redisRes={}, ids={}", redisRes.cause(), ids);
                    handler.handle(Future.failedFuture(redisRes.cause()));
                    return;
                }
                Set<Long> findIds = new HashSet<>();
                List<E> entityList = redisRes.result();
                entityList.forEach(r -> {
                    Long withId = r.getRecordValue(columnName);
                    findIds.add(withId);
                });
                if(ids.size()==findIds.size()){
                    handler.handle(Future.succeededFuture(entityList));
                    return;
                }
                Set<Long> allIds = new HashSet<>(ids);
                List<Long> remainIds = Utils.notContainedElements(findIds, ids);
                if(remainIds.isEmpty()){
                    handler.handle(Future.succeededFuture(entityList));
                    return;
                }
                batchFindByDB(cls, tableName, columnName, remainIds, false, dbRes -> {
                    if (dbRes.failed()) {
                        logger.error("===batchFindByDB failed! redisRes={}, tableName={}, ids={}", dbRes.cause(), tableName, ids);
                        handler.handle(Future.failedFuture(dbRes.cause()));
                        return;
                    }
                    List<E> dbEntityList = dbRes.result();
                    entityList.addAll(dbEntityList);
                    handler.handle(Future.succeededFuture(entityList));
                    //可能这些Ids在redis中隶属不同的分组,需要分组设置到缓存,暂不处理
                    //                redisSetHashEntityList(entityList);
                });
            });
        }
    }

    private static <E extends EntityBase> void batchFindListByRedis(Port port, Class<E> cls, String redisEntityKeyPrefix, List<Long> ids, Handler<AsyncResult<List<E>>> handler){
        List<Request> reqs = buildBatchHGetAllReq(redisEntityKeyPrefix, ids);
        RedisTools.batch(EntityManager.redis,reqs,res->{
            if(res.failed()){
                AsyncActionResult.fail(port,handler,res.cause());
                return;
            }
            List<E> list = new ArrayList<>();
            List<List<E>> result = res.result();
            result.forEach(lst->list.addAll(lst));
            AsyncActionResult.success(port,handler,list);
        },(index,response)->{// listType类型的数据都是BULK类型存储的
            List<E> list = new ArrayList<>();
            if (response.type() == ResponseType.MULTI) {
                boolean isKey = false;
                for (Response r : response) {
                    isKey = !isKey;
                    if (!isKey) {
                        String jsonStr = r.toString();
                        try {
                            E entity = RedisTools.parseEntity(cls, jsonStr);
                            if(entity.hasValidId()) {
                                list.add(entity);
                            }
                        } catch (Exception ex) {
                            logger.error("从redis取出数据后，解析json对象出错，cls={},jsonStr={}", cls, jsonStr);
                        }
                    }
                }
            }
            return list;
        });
    }

    private static List<Request> buildBatchHGetAllReq(String redisEntityKeyPrefix, List<Long> ids){
        List<Request> batchReq = new ArrayList<>(ids.size());
        ids.forEach(id->{
            String redisKey = redisEntityKeyPrefix+"."+id;
            Request cmd = Request.cmd(Command.HGETALL, redisKey);
            batchReq.add(cmd);
        });
        return batchReq;
    }




    private static void batchHMGetFromRedis(Port port, String redisBaseKey, List<Long> ids, List<String> fields, Handler<AsyncResult<List<JsonObject>>> handler){
        List<Request> batchReq = buildBatchHMGetReq(redisBaseKey, ids, fields);
        RedisTools.batch(redis, batchReq, redisRes -> {
            if(redisRes.failed()){
                Log.temp.info("===batchGetEntityFromRedis failed! redisRes={}, ids={}", redisRes.cause(), ids);
                AsyncActionResult.fail(port,handler,redisRes.cause());
                return;
            }
            AsyncActionResult.success(port,handler,redisRes.result());
        });
    }

    @Suspendable
    public static <E extends EntityBase> void batchHMGetFields(Class<E> cls, List<Long> ids, List<String> fields, Handler<AsyncResult<List<JSONObject>>> handler) {
        String redisEntityKey = cls.getSimpleName();
        String tableName = getTableName(cls);
        String listKeyField = getListKeyField(cls);// 获取列表关键字段
        Port port = Port.getCurrent();
        // 单条数据
        boolean oneToMany = !Tool.isEmpty(listKeyField);
        if (!oneToMany) {
            // 单条数据的传参只能有1个值，就是dbid
            batchHMGetFromRedis(port, redisEntityKey, ids, fields, redisRes -> {
                if (redisRes.failed()) {
                    logger.error("===batchHMGetFields failed! redisRes={}, ids={}", redisRes.cause(), ids);
                    handler.handle(Future.failedFuture(redisRes.cause()));
                    return;
                }
                List<Long> findIds = new ArrayList<>();
                List<JSONObject> entityList = new ArrayList<>();
                List<JsonObject> cacheResult = redisRes.result();
                cacheResult.forEach(r -> {
                    if (!r.isEmpty()) {
                        if(r.containsKey("id")){
                            findIds.add(Long.parseLong(r.getString("id")));//主键名称固定id
                        } else {
                            logger.error("===数据可能存在问题, 无id字段, table:{}, res={}", cls.getSimpleName(), r);
                        }
                        JSONObject jo = new JSONObject();
                        jo.put("fields", r);
                        entityList.add(jo);
                    }
                });
                if(ids.size()==findIds.size()){
                    handler.handle(Future.succeededFuture(entityList));
                    return;
                }
                List<Long> remainIds = Utils.diffElement(ids, findIds);
                List<E> tableList = new ArrayList<>();
                batchGetEntityFromDB(port, cls, tableName, remainIds, dbRes -> {
                    if (dbRes.failed()) {
                        logger.error("===batchGetEntityFromDB failed! redisRes={}, tableName={}, ids={}", dbRes.cause(), tableName,remainIds);
                        handler.handle(Future.failedFuture(dbRes.cause()));
                        return;
                    }
                    tableList.addAll(dbRes.result());
                    JSONObject jo = new JSONObject();
                    jo.put("table", dbRes.result());
                    entityList.add(jo);
                    handler.handle(Future.succeededFuture(entityList));
                    redisSetEntities(tableList);
                });
            });
        } else {
            Log.game.error("====不支持列表数据");
        }
    }
    private static List<Request> buildBatchHMGetReq(String redisEntityKeyPrefix, List<Long> ids, List<String> fields){
        List<Request> batchReq = new ArrayList<>(ids.size());
        ids.forEach(id->{
            String redisKey = redisEntityKeyPrefix+"."+id;
//            Request cmd = Request.cmd(Command.HMGET, redisKey, fields.toArray());
            Request cmd = Request.cmd(Command.HGETALL, redisKey);
            batchReq.add(cmd);
        });
        return batchReq;
    }

    private static <E extends BaseModel> E toEntity(Class<E> cls, JsonObject jsonObj){
        E entity = jsonObj.mapTo(cls);
        entity.reset();
        return entity;
    }


    // 根据关键字获取列表信息
    @Suspendable
    public static <E extends EntityBase> List<E> getEntityList(Class<E> cls, long listKeyId) {
        return loadEntityList(cls, String.valueOf(listKeyId));
    }

    /**
     * 根据listKey过滤返回数据
     * @param cls 实体类型
     * @param listKeyId cls定义中listKey属性对应的值
     * @param handler 回调执行
     * @param <E>
     */
    @Suspendable
    public static <E extends EntityBase> void  getEntityListAsync(Class<E> cls, long listKeyId, Handler<AsyncResult<List<E>>> handler) {
        getEntityListAsync(cls, String.valueOf(listKeyId), handler);
    }

    // 根据关键字获取列表信息，没有limit限制
    @Suspendable
    public static <E extends EntityBase> void getEntityAllListAsync(Class<E> cls, long listKeyId, Handler<AsyncResult<List<E>>> handler) {
        getEntityAllListAsync(cls, String.valueOf(listKeyId), handler);
    }

    /**
     * 根据listKey过滤返回数据
     * @param cls 实体类型
     * @param listKeyId cls定义中listKey属性对应的值
     * @return
     * @param <E>
     */
    @Suspendable
    public static <E extends EntityBase> List<E> getEntityList(Class<E> cls, String listKeyId) {
        return loadEntityList(cls, listKeyId);
    }


    // 根据关键字获取列表信息，有limit的限制
    @Suspendable
    public static <E extends EntityBase> void getEntityListAsync(Class<E> cls, String listKeyId, Handler<AsyncResult<List<E>>> handler) {
        loadEntityListAsync(cls, listKeyId, MAX_GET_COUNT, handler);
    }

    // 根据关键字获取列表信息，没有limit限制
    @Suspendable
    public static <E extends EntityBase> void getEntityAllListAsync(Class<E> cls, String listKeyId, Handler<AsyncResult<List<E>>> handler) {
        loadEntityListAsync(cls, listKeyId, Integer.MAX_VALUE, handler);
    }

    // 根据关键字和id数组获取列表信息
    @Suspendable
    public static <E extends EntityBase> void getEntityListAsync(Class<E> cls, String listKeyId, List<Long> ids, Handler<AsyncResult<List<E>>> handler) {
        loadEntityListAsync(cls, listKeyId, ids, MAX_GET_COUNT, handler);
    }

    // 获取全部列表信息
    @Suspendable
    public static <E extends EntityBase> List<E> getEntityList(Class<E> cls) {
        return loadEntityList(cls, null);
    }

    // 根据id数组获取列表信息
    @Suspendable
    public static <E extends EntityBase> List<E> getEntityList(Class<E> cls, List<Long> ids) {
        return loadEntityList(cls, null, ids);
    }

//    // 获取列表数量
//    @Suspendable
//    @Deprecated
//    public static <E extends EntityBase> long getEntityCount(Class<E> cls, long listKeyId) {
//        return countListEntity(cls, listKeyId).result();
//    }
//
//    // 获取列表数量
//    @Suspendable
//    public static <E extends EntityBase> Future<Long> countListEntity(Class<E> cls, long listKeyId) {
//        return getEntityCount(cls, String.valueOf(listKeyId));
//    }
//
//    // 获取列表数量
//    @Suspendable
//    public static <E extends EntityBase> Future<Long> getEntityCount(Class<E> cls, String... listKeyId) {
//        String entityKey = cls.getSimpleName();
//        String listKeyField = getListKeyField(cls);
//        String listKey = getListKey(entityKey, listKeyField, listKeyId);
//        String _tableName = getTableName(cls);
//        Promise<Long> promise = Promise.promise();
//        // 列表数据如果过期从数据库加载
//        Future<Boolean> existFuture = EntityManager.listExpireLoadFromDB(cls, listKeyField, listKey, _tableName);
//        existFuture.onFailure(cause->{
//            promise.fail(cause);
//        });
//        Port port = Port.getCurrent();
//        AsyncActionResult.futureSuccess(port,existFuture,exist->{
//            if(!exist){
//                promise.complete(0L);
//                return;
//            }
//            RedisTools.getHashLen(getRedisClient(), listKey, h -> {
//                if (h.succeeded()) {
//                    promise.complete(h.result());
//                } else {
//                    promise.fail(h.cause());
//                }
//            });
//        });
//        return promise.future();
//    }

    private static <E extends EntityBase> E loadEntity(Class<E> cls, boolean noExistInsert, Object... values) {
        return loadEntity(cls, true, noExistInsert, values);// 默认需要执行reset方法
    }

    private static <E extends EntityBase> E loadEntity(Class<E> cls, boolean reset, boolean noExistInsert, Object... values) {
        Future<E> future = loadJsonObjectAsync(cls, noExistInsert, values);
        return AwaitUtil.awaitResult((handler)-> {
            future.onComplete(handler);
        });
    }

    /**
     * 根据参数查询listKey类型的实体, 不会触发reset,没有数据不会自动插入
     * @param cls
     * @param handler
     * @param listKeyValues
     * @param <E>
     */
    public static <E extends EntityBase> void getEntityAsync(Class<E> cls, Long id, Handler<AsyncResult<E>> handler, Object... listKeyValues) {
        Port port = Port.getCurrent();
        Future<E> future = loadJsonObjectAsync(cls, false, listKeyValues);
        future.onFailure(cause->{
            AsyncActionResult.fail(port,handler,cause);
        });
        AsyncActionResult.futureSuccess(port,future,entity->{
            AsyncActionResult.success(port,handler,entity);
        });
    }

    /**
     * 从redis或数据库中查询指定id的entity
     * @param cls
     * @param noExistInsert
     * @param handler
     * @param values 根据entity是否是listType类型而传递不同的值
     *               entity.ListType=="", values[0] 为主键ID
     *               entity.ListType=="key1.key2", 则values为[]{主键id,value1,value2]}
     * @param <E>
     */
    private static <E extends EntityBase> void loadEntityAsync(Class<E> cls, boolean noExistInsert, Handler<AsyncResult<E>> handler, Object... values) {
        loadEntityAsync(cls, true, noExistInsert, handler, values);// 默认需要执行reset方法
    }

    private static <E extends EntityBase> void loadEntityAsync(Class<E> cls, boolean reset, boolean noExistInsert, Handler<AsyncResult<E>> handler, Object... values) {
        //调用线程
        Port port = Port.getCurrent();
        Future<E> future = loadJsonObjectAsync(cls, noExistInsert, values);
        future.onFailure(cause->{
            AsyncActionResult.fail(port,handler,cause);
        });
        AsyncActionResult.futureSuccess(port,future,entity->{
            AsyncActionResult.success(port,handler,entity);
        });
    }

    /**
     *
     * @param cls
     * @param noExistInsert
     * @param values 根据entity是否是listType类型而传递不同的值
     *               entity.ListType=="", values[0] 为主键ID
     *               entity.ListType=="key1.key2", 则values为[]{主键id,value1,value2]}
     * @return
     * @param <E>
     */
    private static <E extends EntityBase> Future<E> loadJsonObjectAsync(Class<E> cls, boolean noExistInsert, Object... values) {
        Promise<E> promise = Promise.promise();
        String entityName = cls.getSimpleName();
        Port port = Port.getCurrent();
        EntityMeta entityMeta = new EntityMeta(cls);
        String tableName = entityMeta.tableName;
        String listKeyField = entityMeta.listKey;// 获取列表关键字段
        // 单条数据
        if (Tool.isEmpty(listKeyField)) {
            // 单条数据的传参只能有1个值，就是dbid
            if (values.length != 1) {
                String errMsg = "loadEntity error get one data values != 1 class name = "+ cls.getSimpleName();
                logger.error(errMsg);
                promise.fail(errMsg);
                return promise.future();
            }
            long id = Long.parseLong(values[0].toString());
            String entityKey = getKey(entityName, values[0]);//对应redis中的Key
            if(entityMeta.autoCache) {
                Future<JsonObject> future = RedisTools.getHashJsonObject(getRedisClient(), entityKey);
                future.onFailure(cause -> {
                    promise.fail(cause);
                });
                AsyncActionResult.futureSuccess(port, future, result -> {
                    // 数据不存在从数据库加载
                    if (!isEntityNotNull(result)) {
                        Future<E> dbFuture = loadFromDbToCache(cls, tableName, entityKey, id, noExistInsert);
                        dbFuture.onFailure(cause -> {
                            promise.fail(cause);
                        });
                        AsyncActionResult.futureSuccess(port, dbFuture, entity -> {
                            promise.complete(entity);
                        });
                    } else {
                        E entity = toEntity(cls, result);
                        promise.complete(entity);
                    }
                });
            }else{
                Future<E> dbFuture = loadFromDB(cls, tableName, id);
                dbFuture.onFailure(cause -> {
                    promise.fail(cause);
                });
                AsyncActionResult.futureSuccess(port, dbFuture, entity -> {
                    promise.complete(entity);
                });
            }
        } else {// 列表数据
            // 不是全表数据的列表数据的传参必须>=2，第一个是列表的关键值
            if (!listKeyField.equals(ALL_LIST_KEY) && values.length < 2) {
                String errMsg = "loadEntity error get list data values < 2 class name = "+ cls.getSimpleName();
                logger.error(errMsg);
                promise.fail(errMsg);
                return promise.future();
            }
            String listKeyId = String.valueOf(values[0]);// 不是全表数据才有用, 对应Entity的id
            String listKey = getListKey(entityName, listKeyField, listKeyId);
            String listItemKey = getListItemKey(values);
            long id = Long.parseLong(values[values.length-1].toString()); //最后一个是ID
            if(entityMeta.autoCache) {
                // 判断列表是否存在
                Future<Boolean> existFuture = RedisTools.exists(getRedisClient(), listKey);
                existFuture.onFailure(cause -> {
                    promise.fail(cause);
                });
                AsyncActionResult.futureSuccess(port, existFuture, exists -> {
                    if (exists) {
                        // 列表数据存在，则从缓存中获取
                        Future<String> hashFieldFuture = RedisTools.getHashField(getRedisClient(), listKey, listItemKey);
                        hashFieldFuture.onFailure(cause -> {
                            promise.fail(cause);
                        });
                        AsyncActionResult.futureSuccess(port, hashFieldFuture, item -> {
                            if (!Tool.isEmpty(item)) {
                                E entity = toEntity(cls, new JsonObject(item));
                                promise.complete(entity);
                            } else {
                                // 不存在从数据库读取列表
                                Future<E> dbFuture = loadFromDB(cls, tableName, id);
                                dbFuture.onFailure(cause -> {
                                    promise.fail(cause);
                                });
                                AsyncActionResult.futureSuccess(port, dbFuture, result -> {
                                    promise.complete(result);
                                });
                            }
                        });
                    } else {
                        // 不存在从数据库读取列表
                        Future<E> dbFuture = loadFromDB(cls, tableName, id);
                        dbFuture.onFailure(cause -> {
                            promise.fail(cause);
                        });
                        AsyncActionResult.futureSuccess(port, dbFuture, result -> {
                            promise.complete(result);
                        });
                    }
                });
            }else{
                // 不存在从数据库读取列表
                Future<E> dbFuture = loadFromDB(cls, tableName, id);
                dbFuture.onFailure(cause -> {
                    promise.fail(cause);
                });
                AsyncActionResult.futureSuccess(port, dbFuture, result -> {
                    promise.complete(result);
                });
            }
        }
        return promise.future();
    }

    private static <E extends EntityBase> Future<E> loadFromDB(Class<E> cls, String tableName, Long id){
        Promise<E> promise = Promise.promise();
        int updateDbType = getUpdateDbType(cls); // 更新数据库类型
        if (updateDbType != BaseModel.UPDATE_DB_TYPE_NOT) {
            // 单条数据根据id查询
            DB db = DB.newInstance(tableName);
            db.get(id);
            db.listenResult((timeout, returns, context) -> {
                if (timeout) {
                    String errMsg = Utils.createStr("db get Entity timout! tableName={}, id={}", tableName, id);
                    logger.error(errMsg);
                    promise.fail(errMsg);
                } else {
                    E entity = null;
                    Record record = returns.get();
                    if (record != null) {
                        try {
                            entity = cls.getConstructor(Record.class).newInstance(record);
                            entity.reset();
                        } catch (Exception e) {
                            logger.error("查询数据库已查到出记录record，构建Entity出错，tableName={},id={},e=" + e, tableName, id, e);
                            promise.fail(e);
                            return;
                        }
                    }
                    promise.complete(entity);
                }
            });
        }else{
            promise.complete(null);
        }
        return promise.future();
    }

    private static <E extends EntityBase> List<E> loadEntityList(Class<E> cls, String listKeyId) {
        return loadEntityList(cls, listKeyId, (List<Long>) null);
    }

    private static <E extends EntityBase> void loadEntityListAsync(Class<E> cls, String listKeyId, int maxGetCount, Handler<AsyncResult<List<E>>> handler) {
        loadEntityListAsync(cls, listKeyId, null, maxGetCount, handler);
    }

    private static <E extends EntityBase> List<E> loadEntityList(Class<E> cls, String listKeyId, List<Long> ids) {
        List<E> list = AwaitUtil.awaitResult(resultHandler -> getEntityListAsync(cls, listKeyId, ids, resultHandler));
        return list;
    }

    private static <E extends EntityBase> void loadEntityListAsync(Class<E> cls, String listKeyId, List<Long> ids, int maxGetCount, Handler<AsyncResult<List<E>>> handler) {
        String entityKey = cls.getSimpleName();
        EntityMeta<E> entityMeta = new EntityMeta<>(cls);
        String tableName =entityMeta.tableName;
        String listKeyField = entityMeta.listKey;
        String listKey = getListKey(entityKey, listKeyField, listKeyId);
        boolean isOrderList = isOrderList(cls);
        Port port = Port.getCurrent();
        if(entityMeta.autoCache) {
            // 判断列表是否存在
            RedisAPI rClient = getRedisClient();
            Future<Boolean> existsFuture = RedisTools.exists(rClient, listKey);
            existsFuture.onFailure(cause -> {
                AsyncActionResult.fail(port, handler, cause);
            });
            AsyncActionResult.futureSuccess(port, existsFuture, exists -> {
                try {
                    if (exists) {
                        if (ids != null && !ids.isEmpty()) {
                            // 根据listKey和idArray获取
                            RedisTools.getHashJsonObject(rClient, listKey, ids, h2 -> {
                                if (!h2.succeeded()) {
                                    AsyncActionResult.fail(port, handler, h2.cause());
                                    return;
                                }
                                List<E> modelList = toModelList(h2.result(), cls, listKey, isOrderList);
                                AsyncActionResult.success(port, handler, modelList);
                            });
                        } else {
                            // 获取listKey获取
                            RedisTools.getHashJsonObject(rClient, listKey, h3 -> {
                                if (!h3.succeeded()) {
                                    AsyncActionResult.fail(port, handler, h3.cause());
                                    return;
                                }
                                List<E> modelList = toModelList(h3.result(), cls, listKey, isOrderList);
                                AsyncActionResult.success(port, handler, modelList);
                            });
                        }
                    } else {
                        // 不存在从数据库读取列表
                        int updateDbType = getUpdateDbType(cls); // 更新数据库类型
                        if (updateDbType != BaseModel.UPDATE_DB_TYPE_NOT) {
                            //异步处理
                            Future<List<E>> dbFuture = loadListFromDBAsync(cls, tableName, listKeyField, listKey, listKeyId, ids, maxGetCount);
                            dbFuture.onFailure(cause -> {
                                AsyncActionResult.fail(port, handler, cause);
                            });
                            AsyncActionResult.futureSuccess(port, dbFuture, result -> {
                                AsyncActionResult.success(port, handler, result);
                            });
                        } else {
                            AsyncActionResult.success(port, handler, new ArrayList<>());
                        }
                    }
                } catch (Exception ex) {
                    Log.temp.error("loadEntityList cls={}, listKeyId={}, error={}", cls.getSimpleName(), listKeyId, ex);
                    AsyncActionResult.fail(port, handler, ex);
                }
            });
        }else{
            // 不存在从数据库读取列表
            int updateDbType = getUpdateDbType(cls); // 更新数据库类型
            if (updateDbType != BaseModel.UPDATE_DB_TYPE_NOT) {
                //异步处理
                Future<List<E>> dbFuture = loadListFromDB(entityMeta, listKey, listKeyId, ids, maxGetCount);
                dbFuture.onFailure(cause -> {
                    AsyncActionResult.fail(port, handler, cause);
                });
                AsyncActionResult.futureSuccess(port, dbFuture, result -> {
                    AsyncActionResult.success(port, handler, result);
                });
            } else {
                AsyncActionResult.success(port, handler, new ArrayList<>());
            }
        }
    }

    public static <E extends EntityBase> List<E> toModelList(JsonObject result, Class<E> cls, String listKey, boolean isOrderList){
        List<E> list = new ArrayList<>();
        if (result == null) {
            return list;
        }
        // 创建实体类列表
        for (Iterator<Entry<String, Object>> iter = result.iterator(); iter.hasNext(); ) {
            Entry<String, Object> entry = iter.next();
            Object value = entry.getValue();
            JsonObject jsonObject = null;
            if (value instanceof String){
                try {
                    jsonObject = new JsonObject((String) entry.getValue());
                }catch (Exception ex){
                    logger.error("convertJsonToEntity failed");
                }
            } else if (value instanceof JsonObject){
                jsonObject = (JsonObject)value;
            }
            if (jsonObject != null){
                E entity = jsonObject.mapTo(cls);
                entity.reset();
                list.add(entity);
            }
        }
        // 有序列表默认按ID升序
        if (isOrderList && list.size() > 1){
            list.sort(Comparator.comparing(Model::getId));
        }
        // 每次获取列表时都重新设置一下过期时间
        int expireTime = setRedisExpireTime(listKey, cls);
        if (isOrderList){
            setRedisExpireTime(EntityManager.getOrderListKey(listKey), expireTime);
        }
        return list;
    }

    private static JsonArray toJsonArray(JsonObject result, boolean isOrderList) {
        JsonArray jsonArray = new JsonArray();
        if (result == null) {
            return jsonArray;
        }
        // 创建列表
        for (Iterator<Entry<String, Object>> iter = result.iterator(); iter.hasNext(); ) {
            Entry<String, Object> entry = iter.next();
            JsonObject jsonObject = new JsonObject((String) entry.getValue());
            jsonArray.add(jsonObject);
        }
        // 有序列表默认按ID升序
        if (isOrderList && jsonArray.size() > 1){
            Collections.sort(jsonArray.getList(), Comparator.comparing((JsonObject o) -> o.getInteger("id")));
        }
        return jsonArray;
    }

    @Suspendable
    private static <E extends EntityBase> E loadEntityFromDB(Class<E> cls, String tableName, String key, boolean noExistInsert, Object... values) {
        return AwaitUtil.awaitResult(f-> loadFromDbToCache(cls, tableName, key, Long.parseLong(values[0].toString()), noExistInsert));
    }

    // 标记数据已经初始化（数据过期如果有初始化标记才需要从数据库加载）
    @Suspendable
    private static void setEntityInit(String key) {
        RedisTools.set(EntityManager.getRedisClient(), getEntityInitKey(key), "1");
    }

    /**
     * 从数据库加载数据，并将缓存数据更新
     * @param cls
     * @param tableName
     * @param entityKey
     * @param id
     * @param noExistInsert
     * @return
     * @param <E>
     */
    @Suspendable
    private static <E extends EntityBase> Future<E> loadFromDbToCache(Class<E> cls, String tableName, String entityKey, Long id, boolean noExistInsert) {
        Port port = Port.getCurrent();
        // 获取更新数据库类型
        int updateDbType = getUpdateDbType(cls);
        Promise<E> promise = Promise.promise();
        // 是否需要从数据库加载
        if (EntityManager.isLoadDB() && updateDbType != BaseModel.UPDATE_DB_TYPE_NOT) {
            // 先判断数据有没有初始化过
            Future<Boolean> existsFuture = RedisTools.exists(getRedisClient(), getEntityInitKey(entityKey));
            existsFuture.onFailure(cause->{
                promise.fail(cause);
            });
            AsyncActionResult.futureSuccess(port, existsFuture, entityInit -> {
                // 有初始化过才需要从数据库读取
                if (entityInit || Config.DATA_DEBUG) {
                    // 单条数据根据id查询
                    DB db = DB.newInstance(tableName);
                    db.get(id);
                    db.listenResult((timeout, returns, context) -> {
                        if (timeout) {
                            String errMsg = Utils.createStr("db get Entity timout! tableName={}, id={}", tableName, id);
                            logger.error(errMsg);
                            promise.fail(errMsg);
                        } else {
                            E entity = null;
                            Record record = returns.get();
                            if (record == null) {
//                                if (entityInit) {
//                                    promise.fail(cls.getSimpleName() + " entityInit but load from db failed, id=" + id);
//                                    return;
//                                }
                                if (noExistInsert) { // 新增一条数据
                                    try {
                                        entity = cls.newInstance();
                                        entity.setId(id);
                                        entity.insertNew();
                                    } catch (Exception e) {
                                        logger.error("查询数据库未查到该记录，自动新增出错，tableName={},id={},e=" + e, tableName, id, e);
                                    }
                                }
                            } else { // 数据如果存在，则更新到redis中
                                try {
                                    entity = cls.getConstructor(Record.class).newInstance(record);
                                    entity.reset();
                                } catch (Exception e) {
                                    logger.error("查询数据库已查到出记录record，构建Entity出错，tableName={},id={},e=" + e, tableName, id, e);
                                    promise.fail(e);
                                    return;
                                }
                                updateRedisCache(cls,entity,entityKey,entityInit);
                            }
                            promise.complete(entity);
                        }
                    });
                } else {  // 数据没有初始化过，表示不存在
                    E entity = null;
                    if (noExistInsert) { // 新增一条数据
                        try {
                            entity = cls.newInstance();
                            entity.setId(id);
                        } catch (Exception e) {
                            logger.error(Tool.getException(e));
                        }
                    }
                    updateRedisCache(cls,entity,entityKey,entityInit);
                    promise.complete(entity);
                }
            });
        } else {  // 无需入库也不用从数据库加载
            // 判断是否需要新增
            E entity = notExistInsert(cls, id, noExistInsert); // 根据参数没有就新增一条数据
            promise.complete(entity);
        }
        return promise.future();
    }

    @Suspendable
    private static <E extends EntityBase> void loadFromDB(Class<E> cls, String tableName, String entityKey, Long id, Handler<AsyncResult<E>> handler) {
        Port port = Port.getCurrent();
        // 获取更新数据库类型
        int updateDbType = getUpdateDbType(cls);
        // 是否需要从数据库加载
        if (EntityManager.isLoadDB() && updateDbType != BaseModel.UPDATE_DB_TYPE_NOT) {
            // 先判断数据有没有初始化过
            Future<Boolean> existsFuture = RedisTools.exists(getRedisClient(), getEntityInitKey(entityKey));
            existsFuture.onFailure(cause->{
                AsyncActionResult.fail(port,handler,cause);
            });
            AsyncActionResult.futureSuccess(port, existsFuture, entityInit -> {
                // 有初始化过才需要从数据库读取
                if (entityInit || Config.DATA_DEBUG) {
                    // 单条数据根据id查询
                    DB db = DB.newInstance(tableName);
                    db.get(id);
                    db.listenResult((timeout, returns, context) -> {
                        if (timeout) {
                            String errMsg = Utils.createStr("db get Entity timout! tableName={}, id={}", tableName, id);
                            logger.error(errMsg);
                            AsyncActionResult.fail(port, handler, errMsg);
                        } else {
                            E entity = null;
                            Record record = returns.get();
                            if (record == null) {
                                if (entityInit) {
                                    AsyncActionResult.fail(port, handler, cls.getSimpleName() + " entityInit but load from db failed, id=" + id);
                                    return;
                                }
                            } else { // 数据如果存在，则更新到redis中
                                try {
                                    entity = cls.getConstructor(Record.class).newInstance(record);
                                    entity.reset();
                                } catch (Exception e) {
                                    logger.error("查询数据库已查到出记录record，构建Entity出错，tableName={},id={},e=" + e, tableName, id, e);
                                    AsyncActionResult.fail(port, handler, e);
                                    return;
                                }
                            }
                            AsyncActionResult.success(port, handler, entity);
                        }
                    });
                } else {  // 数据没有初始化过，表示不存在
                    AsyncActionResult.success(port, handler, null);
                }
            });
        } else {
            AsyncActionResult.success(port,handler,null);
        }
    }

    /**
     * 将entity更新到redis中
     * @param cls
     * @param entity
     * @param key
     * @param entityInit
     * @param <E>
     */
    private static <E extends EntityBase> void updateRedisCache(Class<E> cls, E entity, String key, boolean entityInit){
        if(entity==null){
            return;
        }
        int expireTime = getRedisExpireTime(cls); // 过期时间
        int expireTimestamp = Tool.getCurTime() + expireTime; // 到期时间戳
        JsonObject _jsonObject = entity.getAllObjNew();
        // json对象设置到期时间戳
        if (expireTime > 0) {
            setJsonObjectExpireTime(_jsonObject, expireTimestamp);
        }
        // 更新到redis中
        RedisTools.setHashJsonObject(redis, key, _jsonObject, h2 -> {
            if (h2.succeeded()) {
                // 设置过期时间
                if (expireTime > 0) {
                    setRedisExpireTime(key, expireTime);
                }
                logger.error("数据库数据更新到redis：{}",key);
            }
        });
        if (!entityInit) {
            setEntityInit(key);
        }
    }

    private static <E extends EntityBase> E notExistInsert(Class<E> cls, long id,  boolean noExistInsert){
        E entity = null;
        if (noExistInsert) { // 新增一条数据
            try {
                entity = cls.newInstance();
                entity.setId(id);
                entity.insertNew();
            } catch (Exception e) {
                logger.error(" auto insert entity by notExistInsert, cls={}, id={}",cls.getSimpleName(),id, e);
            }
        }
        return entity;
    }

    /**
     *
     * @param listKey
     * @param listKeyId
     * @param <E>
     */
    @Suspendable
    private static <E extends EntityBase> Future<List<E>> loadListFromDB(EntityMeta entityMeta, String listKey, String listKeyId, List<Long> ids, int maxGetCount) {
        Promise<List<E>> promise = Promise.promise();
        // 是否需要从数据库加载
        Port port = Port.getCurrent();
        String tableName = entityMeta.tableName;
        String listKeyField = entityMeta.listKey;
        Class<E> cls = entityMeta.entityCls;
        DB db = DB.newInstance(tableName);
        String[] fields = listKeyField.split("\\.");
        if (ids != null && !ids.isEmpty()) {
            db.find(ids);
        } else {
            if (listKeyField.equals(ALL_LIST_KEY)) {
                // 全表搜索
                db.findBy(false, 0, maxGetCount);
            } else {
                String[] values = listKeyId.split("\\.");
                Object[] params = StrUtil.mergeFieldsAndValues(fields, values);
                // 根据列表字段搜索
                db.findBy(false, 0, maxGetCount, params);
            }
        }
        db.listenResult((timeout, returns, context) -> {
            if (timeout) {
                String errMsg = Utils.createStr("db find list callback timeout, tableName={}, listKeyField={}, listKyeId={}", tableName, listKeyField, listKeyId);
                Log.game.error(errMsg);
                promise.fail(errMsg);
                return;
            }
            List<Record> list = returns.get();
            if (list == null || list.isEmpty()) {
//                    Log.game.info("loadListFromDB array is empty, sql = {} sqlParams = {}", sql, sqlParams);
                promise.complete(new ArrayList<>());
                return;
            }
            // 列表数据保存到JsonObject中
            int expireTime = getRedisExpireTime(cls);// 过期时间
            int expireTimestamp = Tool.getCurTime() + expireTime;// 到期时间戳
            List<E> entityList = new ArrayList<>(list.size());
            boolean isOrderList = isOrderList(cls);
            List<String> orderList = new ArrayList<>();
            for (int i = 0, size = list.size(); i < size; i++) {
                Record record = list.get(i);
                // json对象设置到期时间戳
                if (expireTime > 0) {
                    setJsonObjectExpireTime(record, expireTimestamp);
                }
                E entity = null;
                try {
                    entity = cls.getConstructor(Record.class).newInstance(record);
                    entity.reset();
                } catch (Exception e) {
                    logger.error("查询数据库已查到出记录record，构建Entity出错，tableName={},id={},e=" + e, tableName, record.get("id"), e);
                    promise.fail(e);
                    return;
                }
                // 把对象更新到列表中
                entityList.add(entity);
            }
            if (!entityList.isEmpty()) {
                if(entityMeta.autoCache){
                    // 保存到redis
                    redisSetHashEntityList(listKey, entityList, h1 -> {
                        if (h1.failed()) {
                            logger.warn("set list to redis failed after load from db ,listKey={}", listKey);
                        } else {
                            // 设置过期时间
                            setRedisExpireTime(listKey, expireTime);
                        }
                    });
                }
            }
            // 添加有序列表
            if (isOrderList && entityMeta.autoCache) {
                Collections.sort(orderList);// 排序
                String orderListKey = EntityManager.getOrderListKey(listKey);
                RedisTools.del(EntityManager.getRedisClient(), orderListKey, h2 -> {
                });// 先删除再添加，防止有序列表数据重复
                RedisTools.pushToList(EntityManager.getRedisClient(), orderListKey, orderList, h3 -> {
                    if (h3.succeeded()) {
                        // 设置过期时间
                        setRedisExpireTime(orderListKey, expireTime);
                    }
                });
            }
            promise.complete(entityList);
        });
        return promise.future();
    }

    private static <E extends EntityBase> Future<List<E>> loadListFromDBAsync(Class<E> cls, String tableName, String listKeyField, String listKey, String listKeyId, List<Long> ids, int maxGetCount) {
        Promise<List<E>> promise = Promise.promise();
        // 是否需要从数据库加载
        Port port = Port.getCurrent();
        if (!EntityManager.isLoadDB()) {
            promise.fail("please load data from redis");
            return promise.future();
        }
        // 判断列表是否已经初始化，如果没有初始化则不用从数据库加载
        Future<Boolean> existsFuture = RedisTools.exists(getRedisClient(), getEntityInitKey(listKey));
        existsFuture.onFailure(cause->{
            promise.fail(cause);
        });
        AsyncActionResult.futureSuccess(port,existsFuture,listInit->{
            if (!listInit && !Config.DATA_DEBUG) {
                promise.complete(new ArrayList<>());
                return ;
            }
            DB db = DB.newInstance(tableName);
            String[] fields = listKeyField.split("\\.");
            if(ids!=null&& !ids.isEmpty()){
                db.find(ids);
            }else {
                if (listKeyField.equals(ALL_LIST_KEY)) {
                    // 全表搜索
                    db.findBy(false, 0, maxGetCount);
                } else {
                    String[] values = listKeyId.split("\\.");
                    Object[] params = StrUtil.mergeFieldsAndValues(fields, values);
                    // 根据列表字段搜索
                    db.findBy(false, 0, maxGetCount, params);
                }
            }
            db.listenResult((timeout,returns,context)->{
                if(timeout){
                    String errMsg = Utils.createStr("db find list callback timeout, tableName={}, listKeyField={}, listKyeId={}", tableName,listKeyField,listKeyId);
                    Log.game.error(errMsg);
                    promise.fail(errMsg);
                    return;
                }
                List<Record> list = returns.get();
                if (list == null || list.isEmpty()) {
//                    Log.game.info("loadListFromDB array is empty, sql = {} sqlParams = {}", sql, sqlParams);
                    promise.complete(new ArrayList<>());
                    return;
                }
                // 列表数据保存到JsonObject中
                int expireTime = getRedisExpireTime(cls);// 过期时间
                int expireTimestamp = Tool.getCurTime() + expireTime;// 到期时间戳
                List<E> entityList = new ArrayList<>(list.size());
                boolean isOrderList = isOrderList(cls);
                List<String> orderList = new ArrayList<>();
                for (int i = 0, size = list.size(); i < size; i++) {
                    Record record = list.get(i);
                    // json对象设置到期时间戳
                    if (expireTime > 0){
                        setJsonObjectExpireTime(record, expireTimestamp);
                    }
                    E entity = null;
                    try {
                        entity = cls.getConstructor(Record.class).newInstance(record);
                        entity.reset();
                    } catch (Exception e) {
                        logger.error("查询数据库已查到出记录record，构建Entity出错，tableName={},id={},e=" + e, tableName, record.get("id"), e);
                        promise.fail(e);
                        return;
                    }
                    // 把对象更新到列表中
                    entityList.add(entity);
                }
                if (!entityList.isEmpty()){
                    // 保存到redis
                    redisSetHashEntityList(listKey, entityList, h1 -> {
                        if(h1.failed()){
                            logger.warn("set list to redis failed after load from db ,listKey={}",listKey);
                        } else {
                            // 设置过期时间
                            setRedisExpireTime(listKey, expireTime);
                        }
                    });
                    if(!listInit){
                        setEntityInit(listKey);
                    }
                }

                // 添加有序列表
                if (isOrderList){
                    Collections.sort(orderList);// 排序
                    String orderListKey = EntityManager.getOrderListKey(listKey);
                    RedisTools.del(EntityManager.getRedisClient(), orderListKey, h2->{});// 先删除再添加，防止有序列表数据重复
                    RedisTools.pushToList(EntityManager.getRedisClient(), orderListKey, orderList, h3->{
                        if (h3.succeeded()) {
                            // 设置过期时间
                            setRedisExpireTime(orderListKey, expireTime);
                        }
                    });
                }
                promise.complete(entityList);
            });
        });
        return promise.future();
    }

    public static <E extends EntityBase> void redisSetHashEntityList(String redisKey, List<E> entityList, Handler<AsyncResult<Boolean>> onComplete) {
        if (redisKey == null || redisKey.isEmpty()) {
            logger.error("redis error setHashEntity: key is null or empty");
            onComplete.handle(Future.failedFuture("Key is null or empty"));
            return;
        }

        if (entityList == null || entityList.isEmpty()) {
            logger.warn("redis error setHashEntity: entity is null or empty for key: {}", redisKey);
            onComplete.handle(Future.failedFuture("entity is null or empty"));
            return;
        }

        List<String> list = new ArrayList<>();
        list.add(redisKey);
        for(E entity : entityList) {
            list.add(entity.getId()+"");
            list.add(entity.getAllObjNew().toString());
        }
        if (list.size() < 3) {
            logger.error("redis error setHashJsonObject: No valid field-value pairs for key: {}", redisKey);
            onComplete.handle(Future.failedFuture("No valid field-value pairs"));
            return;
        }

        redisClient.hmset(list, res -> {
            if (res.failed()) {
                logger.error("redis error setHashJsonObject key:{} entityList exception:{}", redisKey, res.cause());
                onComplete.handle(Future.failedFuture(res.cause()));
            } else {
                onComplete.handle(Future.succeededFuture(true));
            }
        });

    }

    /**
     * 设置非ListType类型数据到redis，每条数据是单独的key
     * @param entityList
     * @param <E>
     */
    public static <E extends EntityBase> void redisSetEntities(List<E> entityList) {
        if (entityList == null || entityList.isEmpty()) {
            return;
        }
        Class<?> cls = entityList.get(0).getClass();
        String entityName = cls.getSimpleName();
        int redisExpireTime = getRedisExpireTime(cls);
        int remain = entityList.size();
        int batchSize = 50;
        do {
            int counter = Math.min(batchSize, remain);
            for (int i = 0; i < counter; i++) {
                List<Request> reqList = new ArrayList<>();
                for (E entity : entityList) {
                    if(!entity.isAutoCache()){
                        continue;
                    }
                    List<Object> list = new ArrayList<>();
                    String redisKey = getKey(entityName, entity.getId());
                    list.add(redisKey);
                    list.addAll(entity.getKeyValueList());
                    Request req = Request.cmd(Command.HSET, list.toArray());
                    reqList.add(req);
                    //设置过期时间
                    if(redisExpireTime>0) {
                        Request reqTtl = Request.cmd(Command.EXPIRE, redisKey, redisExpireTime);
                        reqList.add(reqTtl);
                    }
                }
                RedisTools.batch(redis, reqList, h -> {
                    if(h.failed()){
                        logger.error("EntityManager.redisSetEntities doBatch set failed! entityName={}",entityName);
                    }
                });
            }
            remain = remain-counter;
        }while (remain>0);
    }

    public static <E extends EntityBase> void redisSetHashEntity(Redis client, String key, E entity, Handler<AsyncResult<Boolean>> onComplete) {
        if (key == null || key.isEmpty()) {
            logger.error("redis error setHashEntity: key is null or empty");
            onComplete.handle(Future.failedFuture("Key is null or empty"));
            return;
        }

        if (entity == null || entity.getId()==0) {
            logger.warn("redis error setHashEntity: entity is null or empty for key: {}", key);
            onComplete.handle(Future.failedFuture("entity is null or empty"));
            return;
        }

        List<Object> list = entity.getKeyValueList();
        if (list.size() < 3) {
            logger.error("redis error setHashJsonObject: No valid field-value pairs for key: {}", key);
            onComplete.handle(Future.failedFuture("No valid field-value pairs"));
            return;
        }

        Request request = Request.cmd(Command.HSET,list.toArray());
        client.send(request, res -> {
            if (res.failed()) {
                logger.error("redis error setHashJsonObject key:{} value:{} exception:{}", key, entity, Tool.getException(res.cause()));
                onComplete.handle(Future.failedFuture(res.cause()));
            } else {
                onComplete.handle(Future.succeededFuture(true));
            }
        });

    }

    /**
     * 列表数据如果过期从数据库加载
     *
     * @param cls     实体类
     * @param listKey 列表关键字值
     * @return 数据是否存在
     */
    @SuppressWarnings("rawtypes")
    @Suspendable
    public static Future<Boolean> listExpireLoadFromDB(Class cls, String listKeyField, String listKey, String tableName, int maxGetCount) {
        Promise<Boolean> promise = Promise.promise();
        if (!EntityManager.isLoadDB()) {
            promise.complete(false);
            return promise.future();
        }
        Promise<Boolean> existsPromise = Promise.promise();
        RedisTools.exists(getRedisClient(), listKey, h -> {
            if (h.succeeded()) {
                existsPromise.complete(h.result());
            } else {
                existsPromise.fail(h.cause());
            }
        });
        Port port = Port.getCurrent();
        AsyncActionResult.futureSuccess(port,existsPromise.future(),exist->{
            if(exist){
                promise.complete(true);
            }else{
                String[] listKeys = listKey.split("\\.");
                String entityKey = listKeys[0];
                String listKeyId = null;
                if (listKeys.length > 1) {
                    listKeyId = listKeys[1];
                }
                Future dbFuture = loadListFromDBAsync(cls, tableName, listKeyField, listKey, listKeyId, null, maxGetCount);
                dbFuture.onFailure(cause->{
                    promise.fail((Throwable) cause);
                });
                AsyncActionResult.futureSuccess(port,dbFuture,result->{
                    promise.complete(true);
                });
            }
        });
        return promise.future();
    }

    /***
     * 等待所有的事务都完成以后再回调
     * @param futures
     * @param onComplete
     */
    public static void waitAll(List<Future> futures, Handler<AsyncResult<JsonArray>> onComplete) {
        CompositeFuture.all(futures).onComplete(handler -> {
            JsonArray result = new JsonArray();
            for (Future fut : futures) {
                JsonObject obj = (JsonObject) fut.result();
                if (obj != null) {
                    result.add(obj);
                }
            }
            onComplete.handle(Future.succeededFuture(result));

        });
    }

    /**
     * 获取redis实体类过期时间
     *
     * @param cls 实体类
     */
    public static int getRedisExpireTime(Class cls) {
        if (REDIS_EXPIRE_TIME == 0) {
            return 0;
        }
        int expireTime;// redis过期时间（秒）
        try {
            Integer REDIS_EXPIRE_TIME = (Integer) cls.getField("REDIS_EXPIRE_TIME").get(cls);// 实体类配置的redis过期时间
            expireTime = REDIS_EXPIRE_TIME.intValue();
        } catch (Exception e) {
            expireTime = 0;
        }
        if (expireTime == 0) {
            expireTime = REDIS_EXPIRE_TIME;
        }
        return expireTime;
    }

    /**
     * 获取对象里的过期时间（手动赋值是int类型，从redis取出来是string类型，所以需要区分）
     */
    public static int getJsonObjectExpireTime(JsonObject jsonObject) {
        if (jsonObject == null || !jsonObject.containsKey(BaseModel.REDIS_EXPIRE_TIME)){
            return 0;
        }
        Object value = jsonObject.getValue(BaseModel.REDIS_EXPIRE_TIME);
        if (value instanceof CharSequence) {
            CharSequence cs = (CharSequence)value;
            return cs == null ? 0 : Integer.parseInt(cs.toString());
        } else if (value instanceof Number) {
            Number number = (Number)value;
            if (number == null) {
                return 0;
            } else if (number instanceof Integer) {
                return (Integer)number;  // Avoids unnecessary unbox/box
            } else {
                return number.intValue();
            }
        } else{
            return 0;
        }
    }

    /**
     * 设置redis实体类过期时间
     *
     * @param key redis key
     * @param cls 实体类
     */
    public static int setRedisExpireTime(String key, Class cls) {
        int expireTime = getRedisExpireTime(cls);// redis过期时间（秒）
        if (expireTime > 0) {
            RedisTools.expire(EntityManager.getRedisClient(), key, expireTime);
        }
        return expireTime;
    }

    /**
     * 设置redis实体类过期时间
     *
     * @param key        redis key
     * @param expireTime 过期时间
     */
    public static int setRedisExpireTime(String key, int expireTime) {
        if (expireTime > 0) {
            RedisTools.expire(EntityManager.getRedisClient(), key, expireTime);
        }
        return expireTime;
    }

    /**
     * 获取更新数据库类型
     */
    public static int getUpdateDbType(Class cls) {
        int UPDATE_DB_TYPE;
        try {
            UPDATE_DB_TYPE = (Integer) cls.getField("UPDATE_DB_TYPE").get(cls);
        } catch (Exception e) {
            UPDATE_DB_TYPE = BaseModel.UPDATE_DB_TYPE_QUEUE;
        }
        return UPDATE_DB_TYPE;
    }

    /**
     * 设置读取对象的过期时间
     */
    public static void setEntityExpireTime(String key, Class cls, JsonObject jsonObject) {
        if (REDIS_EXPIRE_TIME == 0) {
            return;
        }
        int curTime = Tool.getCurTime();
        int redisExpireTime = getJsonObjectExpireTime(jsonObject);
        if (redisExpireTime - curTime < REDIS_EXPIRE_UPDATE_TIME) {
            int expireTime = setRedisExpireTime(key, cls);
            if (expireTime > 0){
                jsonObject.put(BaseModel.REDIS_EXPIRE_TIME, curTime + expireTime);
                RedisTools.setHashJsonObject(redis, key, new JsonObject().put(BaseModel.REDIS_EXPIRE_TIME, curTime + expireTime));
            }
        }
    }

    /**
     * JsonObject对象设置过期时间
     *
     * @param jsonObject      json对象
     * @param redisExpireTime 到期时间戳
     */
    private static void setJsonObjectExpireTime(JsonObject jsonObject, int redisExpireTime) {
        jsonObject.put(BaseModel.REDIS_EXPIRE_TIME, redisExpireTime);
    }

    /**
     * Record对象设置过期时间
     *
     * @param record      record对象
     * @param redisExpireTime 到期时间戳
     */
    private static void setJsonObjectExpireTime(Record record, int redisExpireTime) {
        record.set(BaseModel.REDIS_EXPIRE_TIME, redisExpireTime);
    }

    /**
     * 获取实体类列表关键字段
     *
     * @param cls 实体类
     * @return 列表关键字段
     */
    public static String getListKeyField(Class cls) {
        String LISTKEY;
        try {
            LISTKEY = String.valueOf(cls.getField("LISTKEY").get(cls));
        } catch (Exception e) {
            logger.error("get LISTKEY error class name = {}", cls.getSimpleName());
            return null;
        }
        return LISTKEY;
    }


    /**
     * 实体类是否包含排序列表
     */
    public static boolean isOrderList(Class cls) {
        // TODO 目前不需要且也沒有此功能
        return false;
    }

    /**
     * 获取列表对象的key
     *
     * @param listKeyFields 列表关键字段数组
     * @param item          列表单个对象信息
     * @return 列表对象的key
     */
    public static String getListItemKey(String[] listKeyFields, JsonObject item) {
        if (listKeyFields.length > 1) {
            // 如果列表有多个字段，则排除第一个列表关键字，其他字段组合成对象key（例如userid.itemid，那么只需要用itemid作为对象的key）
            Object[] values = new Object[listKeyFields.length - 1];
            for (int i = 1; i < listKeyFields.length; i++) {
                values[i - 1] = item.getValue(listKeyFields[i]).toString();
            }
            return getValue(values);
        } else {
            // 如果列表没有字段或者只有1个字段，那么用id作为对象key
            return item.getValue("id").toString();
        }
    }

    /**
     * 获取列表对象的key
     *
     * @param listKeyFields 列表关键字段数组
     * @param item          列表单个对象信息
     * @return 列表对象的key
     */
    public static String getListItemKey(String[] listKeyFields, Record item) {
        if (listKeyFields.length > 1) {
            // 如果列表有多个字段，则排除第一个列表关键字，其他字段组合成对象key（例如userid.itemid，那么只需要用itemid作为对象的key）
            Object[] values = new Object[listKeyFields.length - 1];
            for (int i = 1; i < listKeyFields.length; i++) {
                values[i - 1] = item.get(listKeyFields[i]).toString();
            }
            return getValue(values);
        } else {
            // 如果列表没有字段或者只有1个字段，那么用id作为对象key
            return item.get("id").toString();
        }
    }

    /**
     * 获取列表对象的key
     *
     * @param values 获取实体对象传参
     * @return 列表对象的key
     */
    public static String getListItemKey(Object... values) {
        if (values.length <= 2) {
            // 2个（包含）字段以下都是取最后一个值作为key
            return String.valueOf(values[values.length - 1]);
        } else {
            String result = "";
            for (int i = 1; i < values.length; i++) {
                if (Tool.isEmpty(result)) {
                    result = String.valueOf(values[i]);
                } else {
                    result += "." + values[i];
                }
            }
            return result;
        }
    }

    /**
     * 判断实体对象是否为空
     *
     * @param entity
     */
    public static boolean isEntityNotNull(JsonObject entity) {
        return entity != null && entity.containsKey("id");
    }

    /**
     * 获取实体的指定字段，先从Redis获取，获取不到则从数据库加载
     * @param cls 实体类
     * @param id 实体ID
     * @param fields 需要获取的字段列表
     * @param handler 回调函数,返回JsonObject包含请求的字段值
     * @param <E> 实体类型
     */
    public static <E extends EntityBase> void getEntityFields(Class<E> cls, Long id, List<String> fields, Handler<AsyncResult<JsonObject>> handler) {
        Port port = Port.getCurrent();

        // 构建Redis key
        String entityKey = getKey(cls.getSimpleName(), id);

        // 先从Redis获取
        RedisTools.getHashJsonObject(getRedisClient(), entityKey, fields, res -> {
            if (res.succeeded() && res.result() != null && !res.result().isEmpty()) {
                // Redis中存在数据，直接返回
                AsyncActionResult.success(port, handler, res.result());
            } else {
                // Redis中没有数据，从数据库加载
                EntityManager.getEntityAsync(cls, id, dbRes -> {
                    if (dbRes.failed()) {
                        Log.game.error("getEntityFields failed, class={}, id={}", cls.getSimpleName(), id);
                        AsyncActionResult.fail(port, handler, dbRes.cause());
                        return;
                    }

                    E entity = dbRes.result();
                    if (entity == null) {
                        AsyncActionResult.fail(port, handler, new Exception("Entity not found"));
                        return;
                    }

                    JsonObject result = new JsonObject();
                    for (String field : fields) {
                        Object value = entity.getRecordValue(field);
                        if (value != null) {
                            result.put(field, value.toString());
                        }
                    }
                    AsyncActionResult.success(port, handler, result);
                });
            }
        });
    }
}
