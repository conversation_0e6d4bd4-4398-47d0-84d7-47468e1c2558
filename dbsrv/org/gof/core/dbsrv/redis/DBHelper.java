package org.gof.core.dbsrv.redis;

import org.gof.core.Port;
import org.gof.core.Record;
import org.gof.core.dbsrv.DB;
import org.gof.core.entity.EntityBase;
import org.gof.core.entity.EntityRecordAccess;
import org.gof.core.support.Param;
import org.gof.core.support.function.GofFunction3;

import java.util.ArrayList;
import java.util.List;

public class DBHelper {

    /**
     * 多表自由查询，忽略缓存
     * @param sql
     * @param resultHandler
     */
    public static void freeQuery(String sql, GofFunction3<Boolean,Param,Param> resultHandler){
        DB db = DB.newInstance(Port.getTime()+"");
        db.query(sql);
        db.listenResult(resultHandler);
    }

    /**
     * 多表自由查询，忽略缓存
     * @param sql
     * @param resultHandler
     */
    public static void freeQuery(String sql, Object[] sqlParams, GofFunction3<Boolean,Param,Param> resultHandler){
        DB db = DB.newInstance(Port.getTime()+"");
        db.query(sql);
        db.listenResult(resultHandler);
    }

    /**
     * 多表自由查询，指定要刷新的表数据
     * @param tableName
     * @param sql
     * @param resultHandler
     */
    public static void freeQuery(String[] flushTables, String sql, Object[] sqlParams, GofFunction3<Boolean,Param,Param> resultHandler){
        DB db;
        if(flushTables!=null&&flushTables.length>0) {
            db = DB.newInstance(flushTables[0]);
        }else{
            db = DB.newInstance(Port.getTime()+"");
        }
        db.query(flushTables,sql,sqlParams);
        db.listenResult(resultHandler);
    }

    /**
     * 指定表，锁定执行线程变更数据库
     * 如果只是查询请调用 {@link #freeQuery(String[], String, Object[], GofFunction3)} 或 {@link #freeQuery(String, GofFunction3)}
     * @param tableName
     * @param sql
     * @param resultHandler
     */
    public static void executeSql(String tableName, String sql, GofFunction3<Boolean,Param,Param> resultHandler){
        DB db = DB.newInstance(tableName);
        db.sql(true,false,sql);
        db.listenResult(resultHandler);
    }

    /**
     * 查询数量，result Integer
     * @param tableName
     * @param whereAndOther
     * @param resultHandler
     */
    public static void countByQuery(String tableName, String whereAndOther, GofFunction3<Boolean,Param,Param> resultHandler){
        DB db = DB.newInstance(tableName);
        db.countByQuery(false,whereAndOther);
        db.listenResult(resultHandler);
    }

    /**
     * 同步执行sql，慎用
     * @param tableName
     * @param sql
     * @return
     */
    public static Param executeSql(String tableName, String sql){
        DB db = DB.newInstance(tableName);
        db.sql(true,false,sql);
        return db.waitForResult();
    }

    /**
     * 同步查询数量，慎用
     * @param tableName
     * @param whereAndOther
     * @return
     */
    public static Integer countByQuery(String tableName, String whereAndOther){
        DB db = DB.newInstance(tableName);
        db.countByQuery(false,whereAndOther);
        Param param = db.waitForResult();
        return param.getInt();
    }


    /**
     * 多表自由查询，忽略缓存
     * @param sql
     * @param resultHandler
     */
    public static <E extends EntityBase> void batchInsert(List<E> entityList, GofFunction3<Boolean,Param,Param> resultHandler){
        if(entityList==null || entityList.isEmpty()){
            resultHandler.apply(false,null,null);
            return;
        }
        List<Record> records = new ArrayList<>();
        entityList.forEach(e->records.add(EntityRecordAccess.getRecord(e)));
        DB db = DB.newInstance(records.get(0).getTableName());
        if(resultHandler!=null) {
            db.insert(records,true);
            db.listenResult(resultHandler);
        }else{
            db.insert(records);
        }
    }
}
