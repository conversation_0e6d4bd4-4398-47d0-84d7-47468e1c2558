package org.gof.core.dbsrv.redis;


import com.fasterxml.jackson.databind.DeserializationFeature;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.DeploymentOptions;
import io.vertx.core.Promise;
import io.vertx.core.Vertx;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.core.json.jackson.DatabindCodec;
import io.vertx.mysqlclient.MySQLPool;
import io.vertx.redis.client.Redis;
import io.vertx.redis.client.RedisAPI;
import io.vertx.redis.client.RedisOptions;
import org.gof.core.support.Config;
import org.gof.core.support.S;
import org.gof.core.support.Sys;
import org.gof.core.support.Utils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Set;

public class DBMainVerticle extends AbstractVerticle {
	public static final Logger logger = LoggerFactory.getLogger(DBMainVerticle.class);

	public static DBMainVerticle instance;
	public Vertx vertxInstance;
	public MySQLPool jdbcClientGame;
	private RedisOptions redisOptions;
	public RedisAPI redisClient;
	public JsonObject appConfig = new JsonObject();
	public JsonObject appConfigAdmin = new JsonObject();
	public JsonObject appConfigCross = new JsonObject();
	public static final String MYSQL_CONFIG_KEY_GAME = "mysql";// 数据库类型

	@Override
	public void start(Promise<Void> future) throws Exception {
		// 支持entity表格字段增删

//		Json.mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES,false);
		DatabindCodec.mapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES,false);


//		JsonObject object = new JsonObject();
//		object.put("host", Config.NEW_DB_HOST);
//		object.put("port", Integer.parseInt(Config.NEW_DB_PORT));
//		object.put("database", Config.NEW_DB_SCHEMA);
//		object.put("user", Config.NEW_DB_USER);
//		object.put("password", Config.NEW_DB_PWD);
//		object.put("pollsize", Integer.parseInt(Config.NEW_DB_POOL));
//		object.put("pool_idle_timeout", Config.NEW_DB_POOL_IDLE_TIME);
//		appConfig.put("mysql", object);

		JsonArray array = new JsonArray();
		String[] split = Config.NEW_REDIS.split(",");
		for (String redisUrl : split) {
			array.add(redisUrl);
		}
		appConfig.put("redis", array);

		instance = this;

		this.vertxInstance = vertx;

		redisOptions = new RedisOptions();
		redisOptions.setMaxPoolSize(Config.NEW_REDIS_MAX_POOL_SIZE);
		redisOptions.setMaxWaitingHandlers(Config.NEW_REDIS_MAX_WAITING_HANDLERS);// 调高redis参数maxWaitingHandlers，默认值是2048，如果redis请求数超过这个值会请求失败
		redisOptions.setMaxPoolWaiting(Config.NEW_REDIS_MAX_POOL_WAITING);
		EntityManager.redis = RedisTools.createRedisClient(vertx, redisOptions, appConfig, redisHandler -> {
			if (redisHandler.failed()) {
				logger.error("redis init failed! err={}", redisHandler.cause(), redisHandler.cause());
				System.exit(1);
				return;
			}
			redisClient = redisHandler.result();
//			jdbcClientGame = MysqlTool.createMySQLPool(vertx, appConfig, MYSQL_CONFIG_KEY_GAME);

			EntityManager.init(redisClient, jdbcClientGame);

			if(S.isAdmin){
				AdminRedis.init(redisClient);
			} else if(S.isCross){
				CrossRedis.init(redisClient);
			}

			future.complete();
		}, reconnectionCallback -> {
			// redis重连成功给redisClient重新赋值
			redisClient = reconnectionCallback.result();
			EntityManager.redisClient = redisClient;
			logger.info("redis reconnection callback update redisClient");
			future.complete();
		});
		if(S.isAdmin){
			RedisTools.adminRedis = EntityManager.redis;
			AdminRedis.redis = EntityManager.redis;
		} else if(S.isCross){
			RedisTools.crossRedis = EntityManager.redis;
			CrossRedis.redis = EntityManager.redis;
		}



		if(S.isGameServer || S.isCross){
			JsonArray arrayAdmin = new JsonArray();
			String[] splitAdmin = Config.ADMIN_REDIS.split(",");
			for (String redisUrl : splitAdmin) {
				arrayAdmin.add(redisUrl);
			}
			appConfigAdmin.put("redis", arrayAdmin);
			RedisOptions redisOptionsAdmin = new RedisOptions();
			redisOptionsAdmin.setMaxPoolSize(Config.ADMIN_REDIS_MAX_POOL_SIZE);
			redisOptionsAdmin.setMaxWaitingHandlers(Config.ADMIN_REDIS_MAX_WAITING_HANDLERS);
			redisOptionsAdmin.setMaxPoolWaiting(Config.ADMIN_REDIS_MAX_POOL_WAITING);
			RedisTools.adminRedis = RedisTools.createRedisClient(vertx, redisOptionsAdmin, appConfigAdmin, redisHandler -> {
				if (redisHandler.failed()) {
					logger.error("redisClientAdmin init failed! err={}", redisHandler.cause(), redisHandler.cause());
					System.exit(1);
					return;
				}
				EntityManager.initAdmin(redisHandler.result());
				AdminRedis.init(redisHandler.result());
			}, reconnectionCallback -> {
				EntityManager.redisClientAdmin = reconnectionCallback.result();
				AdminRedis.init(reconnectionCallback.result());
				logger.info("redis reconnection callback update redisClientAdmin");
			});
			AdminRedis.redis = RedisTools.adminRedis;

			if(S.isGameServer){
				JsonArray arrayCross = new JsonArray();
				String[] splitCross = Config.CROSS_REDIS.split(",");
				for (String redisUrl : splitCross) {
					arrayCross.add(redisUrl);
				}
				appConfigCross.put("redis", arrayCross);
				RedisOptions redisOptionsCross = new RedisOptions();
				redisOptionsCross.setMaxPoolSize(Config.CROSS_REDIS_MAX_POOL_SIZE);
				redisOptionsCross.setMaxWaitingHandlers(Config.CROSS_REDIS_MAX_WAITING_HANDLERS);
				redisOptionsCross.setMaxPoolWaiting(Config.CROSS_REDIS_MAX_POOL_WAITING);
				RedisTools.crossRedis = RedisTools.createRedisClient(vertx, redisOptionsCross, appConfigCross, redisHandler -> {
					if (redisHandler.failed()) {
						logger.error("redisClientCross init failed! err={}", redisHandler.cause(), redisHandler.cause());
						System.exit(1);
						return;
					}
					EntityManager.initCross(redisHandler.result());
					CrossRedis.init(redisHandler.result());
				}, reconnectionCallback -> {
					// redis重连成功给redisClient重新赋值
					EntityManager.redisClientCross = reconnectionCallback.result();
					CrossRedis.init(reconnectionCallback.result());
					logger.info("redis reconnection callback update redisClientCross");
				});
				CrossRedis.redis = RedisTools.crossRedis;
			}


		}


	}
}
