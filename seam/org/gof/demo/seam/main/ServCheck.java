package org.gof.demo.seam.main;

import org.gof.core.support.Config;
import org.gof.core.support.prettytable.FlipTableConverters;
import org.gof.demo.worldsrv.support.Log;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class ServCheck {

    public static ConcurrentHashMap<String,Boolean> servs = new ConcurrentHashMap<>();

    public static ConcurrentHashMap<String,Long> servCostMap = new ConcurrentHashMap<>();

    public static void addServ(String serv){
        servs.put(serv, false);
    }

    public static void servStarted(String serv){
        if(serv.contains(serv)){
            servs.put(serv,true);
        }
    }

    public static void record(String serv,Long timestamp){
        servCostMap.put(serv,timestamp);
    }

    public static boolean isAllStarted(){
        boolean all = true;
        for(Map.Entry<String,Boolean> entry : servs.entrySet()){
            String serv = entry.getKey();
            boolean start = entry.getValue();
            if(!start) {
                all = false;
                break;
            }
        }
        return all;
    }

    public static void printServTimeResult(){

        String[] headers = {"service","costTime(ms)"};
        int titleLen = headers.length;
        int dataLen = servCostMap.size();
        Object[][] table = new String[dataLen][titleLen];

        int i = 0;

        Map<String, Long> result2 = new LinkedHashMap<>();
        servCostMap.entrySet().stream()
                .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
                .forEachOrdered(x -> result2.put(x.getKey(), x.getValue()));

        for(Map.Entry<String, Long> entry : result2.entrySet()){
            table[i] = new String[]{
                    entry.getKey(),
                    String.valueOf(entry.getValue())
            };
            i++;
        }

        String tableStr = FlipTableConverters.fromObjects(headers, table);
        if(Config.STATISTICS_ENABLE){
            Log.game.error("\n{}",tableStr);
        }
    }

}
