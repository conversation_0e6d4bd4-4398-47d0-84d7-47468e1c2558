package org.gof.demo.seam.main;

import com.pwrd.op.LogOp;
import io.vertx.core.AsyncResult;
import io.vertx.core.Future;
import io.vertx.core.Handler;
import io.vertx.core.Vertx;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.logging.log4j.LogManager;
import org.gof.core.Node;
import org.gof.core.Port;
import org.gof.core.PortPulseQueue;
import org.gof.core.connsrv.Connection;
import org.gof.core.connsrv.main.ConnStartup;
import org.gof.core.dbsrv.DBPort;
import org.gof.core.dbsrv.main.DBStartup;
import org.gof.core.dbsrv.redis.AwaitUtil;
import org.gof.core.dbsrv.redis.DBMainVerticle;
import org.gof.core.dbsrv.redis.EntityManager;
import org.gof.core.dbsrv.redis.RedisTools;
import org.gof.core.statistics.StatisticsService;
import org.gof.core.support.*;
import org.gof.core.support.idAllot.CrossIdAllotService;
import org.gof.core.support.idAllot.IdAllotService;
import org.gof.core.support.log.LogCore;
import org.gof.core.support.observer.MsgSender;
import org.gof.demo.CommonSerializer;
import org.gof.demo.ListenerInit;
import org.gof.demo.MsgReceiverInit;
import org.gof.demo.MsgSerializer;
import org.gof.demo.seam.DefaultPort;
import org.gof.demo.seam.SeamService;
import org.gof.demo.seam.account.AccountLoginService;
import org.gof.demo.seam.account.AccountPort;
import org.gof.demo.seam.account.AccountService;
import org.gof.demo.support.ClassScanProcess;
import org.gof.demo.support.DataReloadManager;
import org.gof.demo.support.DataScanProcess;
import org.gof.demo.worldsrv.carPark.CarParkService;
import org.gof.demo.worldsrv.common.GameService;
import org.gof.demo.worldsrv.common.ServerListService;
import org.gof.demo.worldsrv.human.HumanGlobalService;
import org.gof.demo.worldsrv.human.HumanGlobalServiceProxy;
import org.gof.demo.worldsrv.human.HumanPort;
import org.gof.demo.worldsrv.human.HumanService;
import org.gof.demo.worldsrv.intergration.PFService;
import org.gof.demo.worldsrv.msg.MsgIds;
import org.gof.demo.worldsrv.redis.RedisKeys;
import org.gof.demo.worldsrv.support.D;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.Util;
import org.gof.demo.worldsrv.support.observer.Event;
import org.gof.demo.worldsrv.support.observer.EventKey;
import org.gof.demo.worldsrv.test.TestService;
import org.gof.platform.main.PlatformStartup;

import java.util.List;

public class WorldStartup {


    public static void main(String[] args) throws Exception {

		S.isGameServer = true;
		//设置个默认值 便于系统调试
		if (args.length == 0) {
			args = new String[]{"0"};
		}

		System.setProperty("logFileName", "world" + args[0]);
		System.setProperty("vertx.logger-delegate-factory-class-name", "io.vertx.core.logging.Log4j2LogDelegateFactory");
		Log.game.info("正在启动游戏服务器");

		//验证参数数量
		if (args.length < 1) {
			Log.common.error("useage: serverNum");
			return;
		}

		//初始化commonUID
		CommonUid.initServerId(Integer.parseInt(Config.GAME_SERVER_ID));

		//初始化基本环境
		//MsgSender.init();
//		Event.init();
		Log.game.info("正在初始化事件容器");
		MsgReceiverInit.init(MsgSender.instance);
		ListenerInit.init(Event.instance);
		Log.game.info("正在初始化协议函数指针池");
		MsgSerializer.init();
		CommonSerializer.init();

//        Log.game.info("开始初始化高度文件");
//        HeightFinding.init();

		//强制加载全部数据，这样可以避免业务执行过程中加载。
		Log.game.info("加载策划数据");
		DataReloadManager.inst().initAllData();
		int worldNum = Integer.parseInt(args[0]);
		AwaitUtil.awaitResult(handler->{
			startVertxRedis(res->{
				if(res.failed()){
					Log.game.error("启动redis服务失败！！！", res.cause());
					System.exit(1);
				}
				handler.handle(Future.succeededFuture());
			});
		});
		try {
			startServices(worldNum);
		}catch (Exception ex){
			Log.game.error("启动服务器失败！！！", ex);
			System.exit(1);
		}
	}

	//启动redis服务
	private static void startVertxRedis(Handler<AsyncResult<Boolean>> handler) {
		// 启动vertx服务
		Vertx vertx = Vertx.vertx();
		Future<String> stringFuture = vertx.deployVerticle(new DBMainVerticle());
		stringFuture.onFailure(cause -> handler.handle(Future.failedFuture(cause)));
		stringFuture.onSuccess(result -> handler.handle(Future.succeededFuture(true)));
	}

	//启动其他服务
	private static void startServices(int worldNum) throws Exception {
		//创建Node
		String nodeId = D.NODE_WORLD_PREFIX + worldNum;
		Distr.NODE_ID = nodeId;
		String nodeAddr = Distr.getNodeAddr(nodeId);
		Log.game.info("前置准备完成，开始启动Node, nodeId={}, nodeAddr={}", nodeId, nodeAddr);
		Node node = new Node(nodeId, nodeAddr,NodeType.WORLD.getCode());

		Log.game.info("server init : begin start node...");
		DBStartup.startup(node);

		//1.3 游戏服务器
		for(int i = 0; i < D.NODE_WORLD_STARTUP_NUM; i++) {
			//不用连接自己
			if(i == worldNum) continue;
			//远程nodeId
			String nid= D.NODE_WORLD_PREFIX + i;
			if(!node.isRemoteNodeConnected(nid)) {
				//连接远程
				node.addRemoteNode(nid,NodeType.WORLD.getCode());
			}
		}

		//1.4 平台服务器
		Log.game.info("server init : begin start platform...");
        PlatformStartup.startup(node);

        //启动远程数据日志
        String logPath = Utils.class.getClassLoader().getResource("operlog.properties").getPath();
        LogOp.init(logPath);
		
		/* 2 加载系统数据 */
		//2.1 创建个临时Port
		DefaultPort portDef = new DefaultPort(Distr.PORT_DEFAULT);
		portDef.startup(node);

		Log.game.info("server init : default port started...");

		//2.3整合服务
		SeamService seamServ = new SeamService(portDef);
		seamServ.startup();
		portDef.addService(seamServ);
		
		//平台服务
		PFService pfService = new PFService(portDef);
        pfService.startup();
        portDef.addService(pfService);

        //TestService也放在默认的服务启动
        TestService testService = new TestService(portDef);
        testService.startup();
        portDef.addService(testService);

        //gameService
        GameService gameService = new GameService(portDef);
        gameService.startup();
        portDef.addService(gameService);
        portDef.addQueue(new PortPulseQueue() {
            @Override
            public void execute(Port port) {
                gameService.init();
            }
        });

        //拉取服务器列表
        ServerListService slService = new ServerListService(portDef);
        slService.startup();
        portDef.addService(slService);


        /* 3 启动系统默认服务 */
		//只在默认Node上启动
		Log.game.info("server nodeID={}, defaultNodeId={}",nodeId, Distr.NODE_DEFAULT);
		if(nodeId.equals(Distr.NODE_DEFAULT)) {
			//登陆服务,单独线程处理
			AccountService gateServ = new AccountService(portDef);
			gateServ.startup();
			portDef.addService(gateServ);
			initLoginService(node);

			//运行信息统计
			if(Config.STATISTICS_ENABLE) {
				StatisticsService statisticsService = new StatisticsService(portDef);
				statisticsService.startup();
				portDef.addService(statisticsService);
			}
			initHumanService(node);
		}

		if(Config.SERVER_ID != Util.getServerIdReal(Config.SERVER_ID)){
			Log.game.error("合服后，子服不能单独启动  启动服务器失败！！！");
			System.exit(1);
			return;
		}
		Log.temp.info("===服务器id={}, 是否合服={} mergeId={}", Config.SERVER_ID, S.isServerMerge, Util.getServerIdReal(Config.SERVER_ID));

		//发布服务器初始化开始事件
		Event.fire(EventKey.GAME_STARTUP_BEFORE, "node", node);

		//Node正式启动
		node.startup();

		while(!ServCheck.isAllStarted()) {
			try {
				Thread.sleep(10);
				//发布服务器初始化结束事件

			} catch (InterruptedException e) {
				LogCore.core.error(ExceptionUtils.getStackTrace(e));
			}
		}

		Log.game.info("server init : begin connect admin...");
		// 连接管理服（中心服admin）
		String adminNodeId = D.NODE_ADMIN_PREFIX + 0;
		//连接远程
		node.addRemoteNode(adminNodeId, NodeType.ADMIN.getCode());
		Log.temp.info("===连接中心管理服， nid={}, localBridgeId={}， {}", adminNodeId , nodeId, node.getRemoteNode(adminNodeId));

		Event.fire(EventKey.GAME_STARTUP_FINISH, "node", node);



		//启动日志信息
		Log.game.info("====================");
		Log.game.info(nodeId + " started.");
		Log.game.info("Listen:" + node.getAddr());
		Log.game.info("====================");
		
		//系统关闭时进行清理
		Runtime.getRuntime().addShutdownHook(new Thread(() -> {
			try {
				Log.game.info("触发关闭服务器操作,开始踢人");
				S.isClose = true;
				Port port = node.getPort("game0");
				boolean isFinish = AwaitUtil.awaitResult(handler -> {
					port.addQueue(new PortPulseQueue() {
						@Override
						public void execute(Port port) {
							HumanGlobalService serv = port.getServices(D.SERV_HUMAN_GLOBAL);
							serv.kickAll();
							handler.handle(Future.succeededFuture(true));
						}
					});
				});
				Port port3 = node.getPort("game3");
				AwaitUtil.awaitResult(handler -> {
					port3.addQueue(new PortPulseQueue() {
						@Override
						public void execute(Port port3) {
							CarParkService serv2 = port3.getServices(D.SERV_CAR_PARK);
							serv2.saveAll();
							handler.handle(Future.succeededFuture(true));
						}
					});
				});
				Thread.sleep(15 * Time.SEC);
				DBStartup.shutDownAction(node);
				Log.game.info("关闭服务器-检查db更新队列完成! 15s后服务器将关闭");
				// 等待10秒后再关闭（关服前等待游戏服踢出所有玩家）
				Thread.sleep(10 * Time.SEC);
				LogManager.shutdown();
			} catch (Exception e) {
				e.printStackTrace();
			}
		}));
		
		//设定support外围环境并加载
		if(Config.RELOAD_CONF_DATA){
			DataReloadManager.inst().initReloadSupport();
			DataScanProcess.getInstance().init();
			Log.game.info("开启数据热更新扫描...");
		}
		if(Config.RELOAD_CLASS){
			ClassScanProcess.getInstance().init();
			Log.game.info("开启类热更新扫描...");
		}

		/* 1 设置远程Node */
		//1.1 连接服务器
		ConnStartup.startup(node);
		//默认Ping注册(拦截)
		Connection.registerPing(MsgIds.heart_beat_c2s, MsgIds.heart_beat_s2c);
		//测试websocket服务
//		if(Config.DEBUG_PATTERN){
//			new TestWsServer().start();
//		}
        Log.game.error("开启连接服务");

        Log.game.error("启动完成...");
        ServCheck.printServTimeResult();
		S.gameStartupFinish = true;

    }


	/**
	 * 初始化玩家服务线程
	 *
	 * @param node
	 */
	private static void initHumanService(Node node) {
		try {
			// 启动玩家线程
			for (int i = 0; i < D.PORT_STARTUP_HUMAN_NUM; ++i) {
				// 创建Port
				HumanPort humanPort = new HumanPort(D.PORT_HUMAN + i);
				humanPort.startup(node);
				// 启动并加入服务
				HumanService servHuman = new HumanService(humanPort, node.getId());
				servHuman.startup();
				humanPort.addService(servHuman);
			}
		} catch (Exception e) {
			Log.temp.error("===e={}", e);
		}
	}

	private static void initLoginService(Node node) {
		for (int i = 0; i < D.PORT_STARTUP_NUM_ACCOUNT_LOGIN; ++i) {
			// 创建Port
			AccountPort accountPort = new AccountPort(D.PORT_ACCOUNT + i);
			accountPort.startup(node);
			// 启动并加入服务
			AccountLoginService servLogin = new AccountLoginService(accountPort, node.getId());
			servLogin.startup();
			accountPort.addService(servLogin);
		}
	}

}
