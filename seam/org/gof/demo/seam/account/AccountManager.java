package org.gof.demo.seam.account;

import org.apache.commons.lang3.StringUtils;
import org.gof.core.*;
import org.gof.core.connsrv.ConnectionBuf;
import org.gof.core.connsrv.ConnectionProxy;
import org.gof.core.db.<PERSON>ey;
import org.gof.core.dbsrv.DB;
import org.gof.core.dbsrv.redis.EntityManager;
import org.gof.core.scheduler.ScheduleTask;
import org.gof.core.support.*;
import org.gof.core.support.S;
import org.gof.core.support.observer.Listener;
import org.gof.demo.battlesrv.support.Vector2D;
import org.gof.demo.seam.msg.MsgParamAccount;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.character.HumanObjectServiceProxy;
import org.gof.demo.worldsrv.check.CheckManager;
import org.gof.demo.worldsrv.check.CheckWorldServiceProxy;
import org.gof.demo.worldsrv.common.HumanCreateApplyServiceProxy;
import org.gof.demo.worldsrv.entity.Account;
import org.gof.demo.worldsrv.entity.Human;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.human.*;
import org.gof.demo.worldsrv.inform.ErrorTip;
import org.gof.demo.worldsrv.inform.Inform;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgIds;
import org.gof.demo.worldsrv.msg.MsgLogin;
import org.gof.demo.worldsrv.name.NameServiceProxy;
import org.gof.demo.worldsrv.singleton.ParamManager;
import org.gof.demo.worldsrv.stage.StageGlobalServiceProxy;
import org.gof.demo.worldsrv.support.*;
import org.gof.demo.worldsrv.support.observer.Event;
import org.gof.demo.worldsrv.support.observer.EventKey;

import java.util.*;

public class AccountManager extends ManagerBase {	
	
	public static final String PASS_INNER = "WoShiDaGe!!!";
	// 操作成功，无错误发生
	public static final int SUCCESS = 0;
	// 系统或服务器处于维护中状态
	public static final int Maintenance = 3;

	// 某种检查失败
	public static final int CHECK_FAIL = 25;

	// 操作超时
	public static final int TIMEOUT = 26;

	// 服务器正在维护
	public static final int SERVER_MAINTAIN = 23;

	// IP地址被封锁或禁止
	public static final int IP_SEALED = 8;

	// 操作失败
	public static final int FAIL = 29;

	// 创建角色失败
	public static final int CREATE_ROLE_FAIL = 30;

	// 角色数量达到限制
	public static final int ROLE_LIMIT = 31;

	// 版本不正确或不受支持
	public static final int VERSION_NOT_RIGHT = 164;

	// 没有角色信息
	public static final int NO_ROLE_INFO = 187;

	// 需要转到新服务器（可能是负载均衡或服务器迁移）
	public static final int GO_TO_NEW_SERVER = 200;

	// 创建角色数量达到限制（注意：这里可能是RROR_CREATE_ROLE_LIMIT的拼写错误）
	public static final int RROR_CREATE_ROLE_LIMIT = 201;

	// IP地址创建角色被禁止
	public static final int ERROR_IP_CREATE_ROLE_BANNED = 202;


	/**
	 * 获取实例
	 * @return
	 */
	public static AccountManager inst() {
		return inst(AccountManager.class);
	}


	public static AccountServiceProxy createAccountServProxy(){
		return AccountServiceProxy.newInstance(AccountService.nodeId,AccountService.portId,Distr.SERV_GATE);
	}

	public void accountLogin(CallPoint connPoint, ConnectionStatus connStatus, MsgLogin.role_login_c2s msg){
		String account = msg.getUid();
		Param param = new Param();
		param.put("connPoint", connPoint);
		param.put("connStatus", connStatus);
		param.put("account", account);
		param.put("msg", msg);
		Log.game.info("loginTrace[{}] login_step_50 ====玩家请求登录： account={}，connId={}, msg={}", connPoint.servId, account, connPoint.servId, msg);
		AccountLoginServiceProxy proxy = AccountLoginService.createProxy(account);
		proxy.login(account, param);
	}

	/** 
	 * 玩家登陆/自动创角登录
	 * <AUTHOR>
	 * @Date 2024/2/26
	 * @Param 
	 */
	public void login(CallPoint connPoint, ConnectionStatus connStatus, MsgLogin.role_login_c2s msg) {
		String account = msg.getUid();// 账号，即用户ID
		Log.game.info("loginTrace[{}] login_step_100 ====玩家请求登录： account={}，connId={}", connPoint.servId, account, connPoint.servId);
		//账号名为空，直接返回
		if(StringUtils.isEmpty(account)){
			sendMsg_role_login_s2c(connPoint, CHECK_FAIL);
			return;
		}
		String channel = msg.getPlat();
		connStatus.channel = channel;
		int serverId = msg.getServerId(); //服务器ID
		int eLanguage = msg.getELanguage() == null ? 0 : msg.getELanguage().getNumber();
		String regional = msg.getPKey();

        List<Integer> list = Util.getServerTagList(C.GAME_SERVER_ID);
        if(!list.contains(serverId) && !Config.DATA_DEBUG){
            Log.game.error("loginTrace[{}] login_step 200 不是这个服的玩家登录了！请检查配置！serverTag={}, serverId={} not in serverTagList={}， account={}", connPoint.servId, C.GAME_SERVER_ID, serverId, list, account);
			sendMsg_role_login_s2c(connPoint, GO_TO_NEW_SERVER);
			return;
        }

		AccountObject loginAccount = new AccountObject((Long)connPoint.servId, connStatus,connPoint);
		loginAccount.eLanguage = eLanguage;
		loginAccount.regional = regional;
		loginAccount.serverId = serverId;
		loginAccount.status.account = account;

		Param param = new Param();
		param.put("connPoint", connPoint);
		param.put("connStatus", connStatus);
		param.put("channel", channel);
		param.put("account", account);
		param.put("serverId", serverId);
		param.put("accountObj", loginAccount);
		param.put("eLanguage", eLanguage);
		param.put("regional", regional);
		int zone = Util.getServerIdZoneCode(serverId);
		param.put("zone", zone);

		Log.game.info("loginTrace[{}] login_step_300====accout={}, serverId={}, param={}", connPoint.servId, account, serverId, param);

		if(Config.DATA_DEBUG) {
			HumanGlobalServiceProxy.newInstance().loadServerId(serverId);
		}
		login(account, channel, param);
	}

	public void login(String account, String channel, Param param) {
		HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance();
		prx.isLogined(account, channel, param.getInt("serverId"));
		prx.listenResult(this::_result_login2, param);
	}

	/**
	 * 根据IP地址验证，用于内部登陆玩家账号，或者内部研发登陆
	 * @param results
	 * @param context
	 */
	public void _result_login2(Param results, Param context) {
		CallPoint connPoint = context.get("connPoint");
		Log.game.info("loginTrace[{}] login_step_400, results={}", connPoint.servId, results);
		String account = context.get("account");
		boolean noLogin = Utils.getParamValue(results, "noLogin", false);
		if(noLogin){
			Log.game.info("loginTrace[{}] login_step_500 账号{}不能登录这个服务器", connPoint.servId, account);
			ConnectionProxy connPrx = ConnectionProxy.newInstance(connPoint);
			MsgLogin.logout_s2c.Builder logout = MsgLogin.logout_s2c.newBuilder();
			logout.setCode(23);//踢下线
			connPrx.sendMsg(MsgIds.logout_s2c, new Chunk(logout));
			return;
		}
		int serverId = context.get("serverId");
		ConnectionStatus status = context.get("connStatus");

		//环境 这里应该只有account会调用此函数


		boolean logined = results.get("logined");
		boolean isFull = results.get("isFull");
		long loginedHumanId = results.get("humanId");
		
		boolean isServerFull = results.getBoolean("isServerFull");


		AccountObject accountObj = context.get("accountObj");
		String channel = context.get("channel");
		int eLanguage = context.get("eLanguage");
		String regional = context.get("regional");
		Port port = Port.getCurrent();
		//如果账号之前已登录 则踢出 本次登录也失败
		if(logined) {
			//踢出玩家
			HumanGlobalServiceProxy hgPrx = HumanGlobalServiceProxy.newInstance();
			if(accountObj.eCheckType == null){
				hgPrx.kick(loginedHumanId, ErrorTip.AccountLoginOther);
			} else {
				hgPrx.kickCheckRobotHuman(loginedHumanId, Inform.getServerData(8502));
				CheckWorldServiceProxy proxy = CheckWorldServiceProxy.newInstance();
				proxy.removeOnlineHumanId(loginedHumanId);
			}
			// 踢出玩家后，延迟2秒登录
			Log.game.info("loginTrace[{}] login_step_700, 踢出在线玩家后延迟2s登录 account={},loginedHumanId={}",connPoint.servId, account,loginedHumanId);
			Service loginService = port.getServices(D.SERV_ACCOUNT_LOGIN_SERVER);
			loginService.scheduleOnce(new ScheduleTask() {
				@Override
				public void execute() {
					//登录角色信息检查
					loginCheck(account, connPoint, status, accountObj, channel, eLanguage, serverId, regional);
				}
			}, 2 * Time.SEC);
			return;
		}

		AccountServiceProxy accServProxy = createAccountServProxy();
		//登录排队检查，通过后才去处理账号和角色的检查
		accServProxy.applyLogin(accountObj);
//		//登录角色信息检查
//		loginCheck(account, connPoint, status, accountObj, channel, eLanguage, serverId, regional);
	}

	/**
	 * 查询玩家角色信息
	 */
	public void loginCheck(String account, CallPoint connPoint, ConnectionStatus status, AccountObject accountObj,
						   String channel, int eLanguage, int serverId, String regional) {
		Log.game.info("loginTrace[{}] login_step_800 loginCheck,account={} ", connPoint.servId, account);
		loginContinue(accountObj);
	}

	public void loginContinue(AccountObject loginAccount) {
		if(loginAccount.eCheckType!=null){//机器人自动登录
			autoLogin(loginAccount);
			return;
		}
		String account = loginAccount.status.account;
		EntityManager.getEntityListAsync(Account.class, account, res -> {
			CallPoint connPoint = loginAccount.connPoint;
			int serverId = loginAccount.serverId;
			if (res.failed()) {
				Log.game.error("loginTrace[{}] login_step_900 accountManager.loginContinue failed: account={}, serverId={}", connPoint.servId, account, serverId, res.cause());
				return;
			}
			List<Account> accountList = res.result();
			Log.game.info("loginTrace[{}] login_step_1000 accountManager.loginContinue: account={}, size={}, serverId={}", connPoint.servId, account, accountList.size(), serverId);

			long humanId = 0;
			long nowTime = Port.getTime();
			for (Account accountTemp : accountList) {
				if(Config.DATA_DEBUG){
					Log.game.error("====内网测试，account={}, serverId={}, accountServerId={}, humanId={}",
							account, serverId, accountTemp.getServerId(), accountTemp.getId());
				}
				if (accountTemp.getSealEndTime() > nowTime) {
					kickAndCloseConn(accountTemp.getId(), 8, connPoint);
					return;
				}
				if (accountTemp.getDelTime() > nowTime) {
					continue;
				}

				// 非debug模式校验serverId
				if (accountTemp.getServerId() != serverId) {
					continue;
				}
				humanId = accountTemp.getId();
				break;
			}
			//注册玩家消息
			long connId = (long) connPoint.servId;
			AccountObject obj = new AccountObject(connId, loginAccount.status, connPoint);
			obj.status.humanId = humanId;
			obj.status.account = account;
			obj.status.channel = loginAccount.status.channel;
			obj.eLanguage = loginAccount.eLanguage;
			obj.humanId = humanId;
			obj.serverId = serverId;
			obj.zone = Util.getServerIdZoneCode(serverId);
			obj.regional = loginAccount.regional;
//			//登录排队
//			Port port = Port.getCurrent();
//			AccountService serv = port.getServices(Distr.SERV_GATE);
//			serv.addAccount(obj);
//			serv.loginApplyAdd(connId);
			autoLogin(obj);
		});

	}

	private void kickAndCloseConn(long humanId, int code, CallPoint connPoint){
		ConnectionProxy connPrxNew = ConnectionProxy.newInstance(connPoint);
		MsgLogin.logout_s2c.Builder msg = MsgLogin.logout_s2c.newBuilder();
		msg.setCode(code);
		connPrxNew.sendMsg(MsgIds.logout_s2c, new Chunk(msg));

		Port port = Port.getCurrent();
		AccountService serv = port.getServices(Distr.SERV_GATE);
		serv.scheduleOnce(new ScheduleTask() {
			@Override
			public void execute() {
				HumanGlobalServiceProxy hgPrx = HumanGlobalServiceProxy.newInstance();
				hgPrx.kick(humanId, ErrorTip.AccountIpLock);
				connPrxNew.close();
			}
		}, 2 * Time.SEC);
	}


	/** 
	 * 登录/自动创角
	 * <AUTHOR>
	 * @Date 2024/2/26
	 * @Param 
	 */
	public void autoLogin(AccountObject obj){
		Log.game.info("loginTrace[{}] login_step_1100 autoLogin: account={} ", obj.connPoint.servId, obj.status.account);
		long humanId = obj.humanId;
		if(humanId == 0){
			Log.game.error("loginTrace[{}] login_step_1101 自动自动创角，humanid={}, account={}, serverId={}", obj.connPoint.servId, obj.humanId, obj.status.account, obj.serverId);
			// 自动创角
			characterCreate(obj, false, false);
			return;
		}
		characterLogin(obj, humanId, false);
	}

	public String defaultName(){
		// 随机名字：读取全局表id=1前缀，后缀服务器从0-9随机4个数，A-Z随机2个数，随机打乱顺序
		String name = GlobalConfVal.defaultNameHead;
		List<String> strList = new ArrayList<>();
		for(int i = 0; i < 2; i++){
			int index =(int)(Math.random()*(GlobalConfVal.abcs.length));
			strList.add(GlobalConfVal.abcs[index]);
		}
		for(int i = 0; i < 4; i++){
			strList.add(String.valueOf(Utils.random(10)));
		}
		Collections.shuffle(strList);// 乱序
		for(String str : strList){
			name = Utils.createStr("{}{}", name, str);
		}

		return name;
	}



	/**
	 * 查询玩家角色信息
	 */
	public void queryCharacters(AccountObject accObj, String account, int serverId) {
		DB db = DB.newInstance(Human.tableName);
		Log.game.error("查询玩家角色信息 account={} serverId={} debug={}",account,serverId,Config.DEBUG_PATTERN);
		//debug模式不校验serverId
		if(Config.DEBUG_PATTERN){
			db.findBy(false, Human.K.account, account, DBKey.ORDER_DESC, Human.K.timeLogin);
		}else {
			db.findBy(false, Human.K.account, account, Human.K.serverId, serverId, DBKey.ORDER_DESC, Human.K.timeLogin);
		}

		db.listenResult(this::_result_queryCharacters, "connPoint", accObj.connPoint);
	}

	public void _result_queryCharacters(Param results, Param context) {
		CallPoint connPoint = context.get("connPoint");

//		SCQueryCharactersResult.Builder builder = SCQueryCharactersResult.newBuilder();
//
//		List<Record> records = results.get();
//		if(records.size() == 0){
//			ConnectionProxy prx = ConnectionProxy.newInstance(connPoint);
//			//TODO 客户端要求未来打开
//			prx.sendMsg(MsgIds.SCQueryCharactersResult, new Chunk(builder));
//			return;
//		}
//		for(int i = 0; i < records.size(); i++) {
//			Record r = records.get(i);
//			Human human = new Human(r);
//			if (human.getDeleteTime() > 0) {
//				continue;
//			}
//			long deleteTime = (human.getApplyDeleteTime() + (ConfGlobalUtils.getValue(ConfGlobalKey.删除角色倒计时参数) * 1000)) - System.currentTimeMillis();
//			DCharacter.Builder info = DCharacter.newBuilder();
//			info.setId(human.getId());
//			info.setLevel(human.getLevel());
//			info.setName(human.getName());
//			info.setProfession(human.getProfession());
//			info.setSn(human.getSn());
//			info.setCombat(human.getCombat());
//			info.setRoleSn(human.getRoleSn());
//			info.setSoul(human.getSoul());
//			info.setDeleteTime(deleteTime <= 0 ? 0 : deleteTime);
//
//			Collection<Integer> fashionSnList= FashionManager.inst().equipFashionSn(human.getEquipFashionSnMap());
//			if (fashionSnList != null) {
//				info.addAllFashionSn(fashionSnList);
//			}
//
//			builder.addCharacters(info);
//		}
//
//
//		ConnectionProxy prx = ConnectionProxy.newInstance(connPoint);
//		prx.sendMsg(MsgIds.SCQueryCharactersResult, new Chunk(builder));


	}

	/**
	 * 查询玩家角色信息
	 */
	public void queryCharactersOnPlay(CallPoint connPoint, ConnectionStatus connStatus, String account) {
		//从humanGlobal 中获得人物的连接
		HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance();
//		prx.isLogined(account, connStatus.channel);
		prx.listenResult(this::_result_queryCharactersOnPlay, "connPoint", connPoint, "connStatus", connStatus, "account", account);
	}

	private void _result_queryCharactersOnPlay(Param results, Param context){
		boolean logined = results.get("logined");
		if(!logined) //非法请求
			return;

		HumanGlobalInfo humanInfo = results.get("humanInfo");
		String account = context.get("account");
		context.put("humanInfo", humanInfo);

		DB db = DB.newInstance(Human.tableName);
		db.findBy(false, "account", account);
		db.listenResult(this::_result_queryCharactersOnPlay2, context);
	}

	public void _result_queryCharactersOnPlay2(Param results, Param context) {
		HumanGlobalInfo humanInfo = context.get("humanInfo");

		Log.game.error("_result_queryCharactersOnPlay2 kick human = {}",humanInfo.account);
		//踢掉玩家
		HumanGlobalServiceProxy hgPrx = HumanGlobalServiceProxy.newInstance();
		hgPrx.kick(humanInfo.id, 8502);

		//创建一个新的数据
		CallPoint connPoint = context.get("connPoint");
		ConnectionStatus status = context.get("connStatus");

		//环境 这里应该只有account会调用此函数
		Port port = Port.getCurrent();
		AccountService serv = port.getServices(Distr.SERV_GATE);

		ConnectionProxy prx = ConnectionProxy.newInstance(connPoint);
		//更新链接状态
		status.status = ConnectionStatus.STATUS_GATE;
		status.account = humanInfo.account;
		status.channel = humanInfo.channel;
		prx.updateStatus(status);

		AccountObject obj = new AccountObject((long)connPoint.servId, status, connPoint);
		serv.addAccount(obj);
		serv.checkGateNum(status.status);
		//返回角色列表
		_result_queryCharacters(results, context);
	}

	/**
	 * 角色登陆
	 * @param accObj
	 * @param humanId
	 * @param reconnect false=正常登陆 true=断线重连
	 */
	public void characterLogin(AccountObject accObj, long humanId, boolean reconnect) {
		accObj.humanId = humanId;
		EntityManager.getEntityAsync(Human.class, humanId, res -> {
			Port port = Port.getCurrent();
			AccountLoginService serv = port.getServices(D.SERV_ACCOUNT_LOGIN_SERVER);
			if(serv == null){
				serv = port.getNode().getPort(Distr.PORT_DEFAULT).getServices(Distr.SERV_GATE);
			}
			if(res.failed()){
				Log.game.error("loginTrace[{}] login_step_1401 玩家数据查找出错account={}, humanId={}", accObj.connPoint.servId, accObj.status.account, humanId, res.failed());
				loginFailed(serv,accObj,6);
				return;
			}
			Human human = res.result();
			if (human == null) {
				Log.game.error("loginTrace[{}] login_step_1401.1 玩家数据找不到account={}, humanId={}", accObj.connPoint.servId, accObj.status.account, humanId);
				loginFailed(serv, accObj,6);
				return;
			}
			//获取并处理地图历史路径
			int zone = human.getZone();
			String clientIP = accObj.status.clientIP;
			Param param = new Param();
			param.put("humanId", accObj.humanId);
			param.put("connPoint", accObj.connPoint);
			param.put("zone", zone);
			param.put("firstStory", -1);
			param.put("clientIP", clientIP);
			param.put("pid", accObj.pid);
			param.put("eLanguage", accObj.eLanguage);
			param.put("account", accObj.status.account);
			param.put("serverId", human.getServerId());
			param.put("regional", accObj.regional);

			Log.game.error("loginTrace[{}] login_step_1402 account={}, humanId={}", accObj.connPoint.servId, accObj.status.account, humanId);
			HumanServiceProxy proxy = HumanService.createProxy(humanId);
			proxy.login(accObj.humanId, param);
			proxy.listenResult(this::_result_loginRedis, "accObj", accObj, "reconnect", reconnect);
		});
	}

	/**
	 *
	 * @param accObj
	 * @param errorCode 6-数据加载错误 <br />
	 *                  9-登录人数过多，稍后再试 <br />
	 */
	public void loginFailed(Service service, AccountObject accObj, int errorCode){
		ConnectionProxy prxConn = ConnectionProxy.newInstance(accObj.connPoint);
		AccountServiceProxy accountServProxy = createAccountServProxy();
		accountServProxy.checkGateNum(ConnectionStatus.STATUS_LOSTED);
		accountServProxy.removeAccount(accObj);
		MsgLogin.logout_s2c.Builder logout = MsgLogin.logout_s2c.newBuilder();
		logout.setCode(errorCode);//断线，提示重新登录
		prxConn.sendMsg(MsgIds.logout_s2c, new Chunk(logout));
		service.scheduleOnce(new ScheduleTask() {
			@Override
			public void execute() {
				prxConn.close();
			}
		}, 2 * Time.SEC);
	}

	public void _result_loginRedis(boolean timeout, Param results, Param context) {
		AccountObject accObj = context.get("accObj");
		Log.game.error("loginTrace[{}] login_step_1500 account={}, timeout={}, results",accObj.connPoint.servId, accObj.status.account, timeout,results);
		//连接代理
		Port port = Port.getCurrent();
		AccountLoginService serv = port.getServices(D.SERV_ACCOUNT_LOGIN_SERVER);
		if(serv == null){
			serv = port.getNode().getPort(Distr.PORT_DEFAULT).getServices(Distr.SERV_GATE);
		}
		ConnectionProxy prxConn = ConnectionProxy.newInstance(accObj.connPoint);
		if(timeout){
			loginFailed(serv,accObj,6);
			return;
		}
		String loadDataErr = results.get("loadDataErr");
		if(loadDataErr!=null&&!loadDataErr.isEmpty()){
			loginFailed(serv,accObj,6);
			return;
		}
		//更新连接状态
		String node = results.get("node");
		String stagePort = results.get("port");

		ConnectionStatus status = accObj.status;
		status.humanId = accObj.humanId;
		status.stageNodeId = node;
		status.stagePortId = stagePort;
		status.status = ConnectionStatus.STATUS_PLAYING;
		prxConn.updateStatus(status);
		AccountServiceProxy accountServProxy = createAccountServProxy();
		accountServProxy.checkGateNum(status.status);
		accountServProxy.removeAccount(accObj);

		if(accObj.eCheckType != null){
			CheckManager.inst().checkLongin(accObj.humanId, accObj.status.account, true, accObj.eCheckType);
			if(S.isTestLog) {
				Log.temp.info("===humanId={}, eCheckType={}", accObj.humanId, accObj.eCheckType);
			}
			return;
		}
//		Log.temp.info("===状态改变humanId={}, status={}, info={}", status.humanId, status.status, status);

		HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance();
		proxy.humanLoginGameState(accObj.humanId);


	}

	public void _result_characterLogin(Param results, Param context) {
		RecordTransient r = results.get();
		AccountObject accObj = context.get("accObj");
		boolean reconnect = context.get("reconnect");
		if(r == null){
			Log.temp.error("===r={}, results={}, context={}", r, results, context);
			return;
		}
//		Log.temp.info("人物已经登录：连接ID={}, HumanID={}", accObj.getId(), accObj.humanId);

//		//获取并处理地图历史路径
		int zone = r.get(Human.K.zone);

		//根据地图历史路径获取可登陆地图
		StageGlobalServiceProxy prx1 = StageGlobalServiceProxy.newInstance();
		String clientIP = accObj.status.clientIP;
		Param param = new Param();
		param.put("humanId", accObj.humanId);
		param.put("connPoint", accObj.connPoint);
		param.put("zone", zone);
		param.put("firstStory", -1);
		param.put("clientIP", clientIP);
		param.put("pid", accObj.pid);
		param.put("eLanguage", accObj.eLanguage);
		param.put("account", accObj.status.account);
		prx1.login(accObj.humanId, param);
		prx1.listenResult(this::_result_characterLogin2, "accObj", accObj, "reconnect", reconnect);

		if(accObj.eCheckType != null){
			return;
		}
//		ConnectionProxy prxConn = ConnectionProxy.newInstance(accObj.connPoint);
//		MsgLogin.role_login_s2c.Builder msg = MsgLogin.role_login_s2c.newBuilder();
//		msg.setCode(0);
//		msg.setRoleId(accObj.humanId);
//		msg.setServerId(accObj.serverId);
//		msg.setServTime((int)(Port.getTime()/ Time.SEC));
//		msg.setRoleName(r.get(Human.K.name));
//		msg.setServerName(Util.getServerNameReal(accObj.serverId));
//		msg.setTimeZone(Util.getServerIdZoneCode(accObj.serverId));
//		msg.setOpenTime((int)(Util.getOpenServerTime(accObj.serverId) / Time.SEC));// TODO 开服时间
//
//		Define.p_role_change.Builder dInfo = Define.p_role_change.newBuilder();
//		Define.p_key_value.Builder kv = Define.p_key_value.newBuilder();
//		kv.setK(RoleInfoKey.ROLE_ATTR_LVL.getKey());
//		kv.setV();
//		dInfo.addKv();
//
//
//		prxConn.sendMsg(MsgIds.role_login_s2c, new Chunk(msg));
	}
	public void _result_characterLogin2(Param results, Param context) {
		AccountObject accObj = context.get("accObj");

		//连接代理
		ConnectionProxy prxConn = ConnectionProxy.newInstance(accObj.connPoint);

		//更新连接状态
		String node = results.get("node");
		String port = results.get("port");

		ConnectionStatus status = accObj.status;
		status.humanId = accObj.humanId;
		status.stageNodeId = node;
		status.stagePortId = port;
		status.status = ConnectionStatus.STATUS_PLAYING;
		AccountServiceProxy accountServProxy = createAccountServProxy();
		if(accObj.eCheckType != null){
			CheckManager.inst().checkLongin(accObj.humanId, accObj.status.account, true, accObj.eCheckType);
			if(S.isTestLog) {
				Log.temp.info("===humanId={}, eCheckType={}", accObj.humanId, accObj.eCheckType);
			}
			if(Utils.isDebugMode()){
				accountServProxy.removeAccount(accObj);
				HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance();
				proxy.humanLoginGameState(accObj.humanId);
			}
			return;
		}
		Log.temp.info("===状态改变humanId={}, status={}, info={}", status.humanId, status.status, status);
		prxConn.updateStatus(status);

		accountServProxy.checkGateNum(status.status);
		//清理当前的缓存数据
		accountServProxy.removeAccount(accObj);


//		String bridgeStageHistory = context.get("bridgeStageHistory");
//		boolean bridgeStageIn = context.get("bridgeStageIn");
//
//		List<StageHistory> stageHistoryList = StageHistory.stageHistoryList(bridgeStageHistory);
//		if(bridgeStageIn && !stageHistoryList.isEmpty()) {
//			boolean isLeague = false;
//			StageGlobalServiceProxy prx = StageGlobalServiceProxy.bridgeInstance(NodeAdapter.bridge(isLeague));
//			prx.canLoginBridgeStage(stageHistoryList);
//			prx.listenResult(this::_result_characterLogin3, "accObj", accObj, "isLeague", isLeague, "isLogin", true);
//		} else{
//			//清理当前的缓存数据
//			accObj.serv.removeAccount(accObj);
//		}

		HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance();
		proxy.humanLoginGameState(accObj.humanId);


	}


	public void _result_characterLogin3(boolean timeout, Param results, Param context) {
		AccountObject accObj = context.get("accObj");
		AccountServiceProxy accountServProxy = createAccountServProxy();
		if (timeout) {
//			Event.fire(EventKey.HUMAN_LOGIN_BRIDGE_FAIL, "humanId", accObj.humanId);
			HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance();
//			prx.bridgeStageInSet(accObj.humanId, false);

			//清理当前的缓存数据
			accountServProxy.removeAccount(accObj);
			return;
		}

		boolean canEnter = results.getBoolean("canEnter");
		boolean isLeague = context.getBoolean("isLeague");
		if(canEnter) {
			long stageId = results.get("stageId");
			Vector2D pos = results.get("pos");
			HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance();
//			prx.bridgeStageEnter(accObj.humanId, stageId, "posAppear",pos , "bridge", true, "isLeague", isLeague, "isLogin", true);
		} else{
//			Event.fire(EventKey.HUMAN_LOGIN_BRIDGE_FAIL, "humanId", accObj.humanId);
			HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance();
			prx.bridgeStageInSet(accObj.humanId, false);
		}

		//清理当前的缓存数据
		accountServProxy.removeAccount(accObj);
	}

	/**
	 * 在登陆信息中 查找同account的数据
	 * @param serv
	 * @param account
	 * @return
	 */
	public AccountObject getByAccount(AccountService serv, String account) {
		//遍历寻找登陆中的玩家信息 看有没有同account的
		return serv.findAccount(account);
	}

	/**
	 * 断线重连
	 * <AUTHOR>
	 * @Date 2022/10/27
	 * @Param
	 */
	public void onCSAccountReconnect(long humanId, MsgParamAccount msgParam){
		Log.temp.error("===收到重连， humanId={}, msg={}", humanId, msgParam);
		//从humanGlobal 中获得人物的连接
		try {
			HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance();
			prx.getInfo(humanId);
			prx.listenResult(this::_result_getInfo, "msgParam", msgParam);
		} catch (Exception e){
			sendMsg_role_reconnect_s2c(msgParam.getConnPoint(), ParamManager.loginReconnectError1);
			Log.temp.error("===重连失败，onCSAccountReconnect 报错了（重连失败，需要重登） humanId={},error", humanId, e);
		}
	}

	public void _result_getInfo(boolean timeout, Param results, Param context) {
		MsgParamAccount msgParam = context.get("msgParam");
		if(timeout){
			Log.temp.error("===重连失败，获取人物信息超时，msg={}", msgParam);
			sendMsg_role_reconnect_s2c(msgParam.getConnPoint(), ParamManager.loginReconnectError1);
			return;
		}
		AccountService serv = msgParam.getService();
		ConnectionStatus status = msgParam.getConnStatus();
		CallPoint connPoint = msgParam.getConnPoint();
		MsgLogin.role_reconnect_c2s msg = msgParam.getMsg();
		String msgAccount = msg.getUid();

		try {
			HumanGlobalInfo humanGlobalInfo = results.get();
			if (humanGlobalInfo == null) {
				//如果找不到恢复数据
				sendMsg_role_reconnect_s2c(msgParam.getConnPoint(), ParamManager.loginReconnectError1);
				return;
			}

			//返回恢复状态
			int errorCode = ParamManager.loginReconnectError1;
			//返回结果

			Human human = (Human) EntityManager.getEntity(Human.class, humanGlobalInfo.id);
			//无法通过SessionKey恢复
			if (human == null) {
				Log.game.error("===重连失败，玩家不存在 msgAcount={}, humanId={}", msgAccount, humanGlobalInfo.id);
				errorCode = ParamManager.loginReconnectError4;
				sendMsg_role_reconnect_s2c(connPoint, errorCode);
				return;
			}
			//可以恢复？
			boolean recover = true;
			//检查下如果此account正在登陆中 那么就断线重连失败
			//如果玩家已登陆到游戏中，那么sessionKey会改变，所以不用考虑已在游戏中的玩家
			AccountObject accOld = AccountManager.inst().getByAccount(serv, human.getAccount());
			if (accOld != null) {
				recover = false;
				errorCode = ParamManager.loginReconnectError3;
				Log.game.error("===重连失败，重连玩家正在登录中 msgAcount={}, humanId={}", msgAccount, human.getId());
			}

			String account = human.getAccount();
			if (!msgAccount.equals(account)) {
				Log.game.error("===重连失败，错误的账号信息 msgAcount={}, account ={}, humanId={}", msgAccount, account, human.getId());
				recover = false;
				errorCode = ParamManager.loginReconnectError4;
			}
			//如果找不到恢复数据 则恢复失败 否则就当做成功
			if (!recover) {
				sendMsg_role_reconnect_s2c(connPoint, errorCode);
				return;
			}

			reconnect(humanGlobalInfo, connPoint, status);
		} catch (Exception e) {
			sendMsg_role_reconnect_s2c(msgParam.getConnPoint(), ParamManager.loginReconnectError1);
			Log.temp.error("===重连失败 onCSAccountReconnect account={}, error", msgAccount, e);
		}

		//查询要恢复的数据
//		DB db = DB.newInstance(Human.tableName);
//		db.getBy(false, Human.K.id, humanGlobalInfo.id);
//		db.listenResult(this::_result_onCSAccountReconnect, "msgParam", msgParam, "humanGlobalInfo", humanGlobalInfo);
	}

	/**
	 * 断线重连 返回值处理
	 * @param results
	 * @param context
	 */
	public void _result_onCSAccountReconnect(Param results, Param context) {
		MsgParamAccount msgParam = context.get("msgParam");
		HumanGlobalInfo humanGlobalInfo = context.get("humanGlobalInfo");
		//参数
		Long connId = msgParam.getConnId();
		AccountService serv = msgParam.getService();
		ConnectionStatus status = msgParam.getConnStatus();
		CallPoint connPoint = msgParam.getConnPoint();
		MsgLogin.role_reconnect_c2s msg = msgParam.getMsg();
		String msgAccount = msg.getUid();

		//返回恢复状态
		int errorCode = ParamManager.loginReconnectError1;
		//返回结果
		Record r = results.get();

		//无法通过SessionKey恢复
		if(r == null){
			errorCode = ParamManager.loginReconnectError2;
			sendMsg_role_reconnect_s2c(connPoint, errorCode);
			return;
		}
		//恢复玩家数据
		Human human = new Human(r);
		//可以恢复？
		boolean recover = true;
		//检查下如果此account正在登陆中 那么就断线重连失败
		//如果玩家已登陆到游戏中，那么sessionKey会改变，所以不用考虑已在游戏中的玩家
		AccountObject accOld = AccountManager.inst().getByAccount(serv, human.getAccount());
		if(accOld != null) {
			recover = false;
			errorCode = ParamManager.loginReconnectError3;
		}

		String account = human.getAccount();
		if(!msgAccount.equals(account)){
			Log.game.error("错误的账号信息 msgAcount ={}, account ={}", msgAccount, account);
			recover = false;
			errorCode = ParamManager.loginReconnectError4;
		}
		//如果找不到恢复数据 则恢复失败 否则就当做成功
		if(!recover) {
			sendMsg_role_reconnect_s2c(connPoint, errorCode);
			return;
		}
			reconnect(humanGlobalInfo, connPoint, status);
		}

	/**
	 * 本服重连
	 * <AUTHOR>
	 * @Date 2023/11/4
	 * @Param
	 */
	private void reconnect(HumanGlobalInfo humanGlobalInfo, CallPoint connPoint, ConnectionStatus status){

		//更新humanObject的conn
		HumanObjectServiceProxy prxHumanObj = HumanObjectServiceProxy.newInstance(humanGlobalInfo.nodeId, humanGlobalInfo.portId, humanGlobalInfo.id);
		prxHumanObj.ChangeConnPoint(connPoint);

		//更新humanGlobal对应的HumanObject 的conn
		HumanGlobalServiceProxy prxHumanGol = HumanGlobalServiceProxy.newInstance();
		prxHumanGol.ChangeConnPoint(humanGlobalInfo.id, connPoint);

		status.humanId = humanGlobalInfo.id;
		status.stageNodeId =  humanGlobalInfo.nodeId;
		status.stagePortId = humanGlobalInfo.portId;
		//打开新的发送新的人物连接 并更新conn 的状态
		status.status = ConnectionStatus.STATUS_PLAYING;
		status.channel = humanGlobalInfo.channel;
		status.isReconnect = true;
		status.account = humanGlobalInfo.account;
		//发送恢复状态消息
		ConnectionProxy prx = ConnectionProxy.newInstance(connPoint);
		prx.updateStatus(status);

		// 先通知重连成功
		sendMsg_role_reconnect_s2c(connPoint, ParamManager.loginReconnectScuccess);
		Log.temp.error("===重连成功 humanId={}, account={}, serverId={} ", humanGlobalInfo.id, humanGlobalInfo.account, connPoint.servId);

		ConnectionProxy connPrx = ConnectionProxy.newInstance(humanGlobalInfo.connPoint);
		if (connPrx != null) {
			connPrx.getMsgBuf();
			connPrx.listenResult(this::_result_InitMsgBuf, "callPoint", connPoint, "oldConnPoint", humanGlobalInfo.connPoint);
		}
		Event.fire(EventKey.HUMAN_RETABLISH_CONNECTION, "humanId", humanGlobalInfo.id);

		HumanObjectServiceProxy proxy = HumanObjectServiceProxy.newInstance(humanGlobalInfo.nodeId, humanGlobalInfo.portId, humanGlobalInfo.id);
		proxy.connLoadPocketLine();
	}



	public void _result_InitMsgBuf(Param results, Param context) {
		ConnectionBuf conBuf = results.get();
		CallPoint callPoint = context.get("callPoint");
		CallPoint callPointOld = context.get("oldConnPoint");
		ConnectionProxy prxNew = ConnectionProxy.newInstance(callPoint);
		ConnectionProxy prxOld = ConnectionProxy.newInstance(callPointOld);
		if(conBuf == null){
			prxOld.close();
			return;
		}
		prxNew.replaceMsgBuf(conBuf);
		//发送缓冲数据
		prxNew.sendMsgBuf();

		prxOld.close();
	}

	/** 
	 * 跨服重连 
	 * <AUTHOR>
	 * @Date 2023/11/4
	 * @Param 
	 */
	public void _result_onCSAccountReconnect_bridge(boolean timeout, Param results, Param context) {
		long humanId = context.get("humanId");
		CallPoint callPoint = context.get("connPoint");
		if (timeout) {
			sendMsg_role_reconnect_s2c(callPoint, ParamManager.loginReconnectError5);
			HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance();
//			prx.bridgeStageInSet(humanId, false);
			return;
		}
		boolean canEnter = results.getBoolean("canEnter");
		if(!canEnter) {
			sendMsg_role_reconnect_s2c(callPoint, ParamManager.loginReconnectError6);
			HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance();
//			prx.bridgeStageInSet(humanId, false);
			return;
		}
		String nodeId = results.get("nodeId");
		ConnectionStatus status = context.get("status");
		HumanGlobalInfo humanGlobalInfo = context.get("humanGlobalInfo");
		//更新humanGlobal对应的HumanObject 的conn
		HumanGlobalServiceProxy prxBridge = HumanGlobalServiceProxy.newInstance(nodeId);
		prxBridge.getInfo(humanId);
		prxBridge.listenResult(this::_reconnect_bridge,"connPoint", callPoint, "status", status,
				"nodeId", nodeId, "humanGlobalInfo", humanGlobalInfo);
	}

	/** 
	 * 跨服重連
	 * <AUTHOR>
	 * @Date 2023/11/4
	 * @Param 
	 */
	private void _reconnect_bridge(Param results, Param context){
		CallPoint callPoint = context.get("connPoint");
		String nodeId = context.get("nodeId");
		ConnectionStatus status = context.get("status");
		HumanGlobalInfo humanGlobalInfo = context.get("humanGlobalInfo");
		ConnectionProxy connPrxNew = ConnectionProxy.newInstance(callPoint);
		HumanGlobalInfo humanGlobalInfoBridge = results.get();
		if(humanGlobalInfoBridge == null){
			sendMsg_role_reconnect_s2c(callPoint, ParamManager.loginReconnectError7);
			HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance();
//			prx.bridgeStageInSet(humanGlobalInfo.id, false);
			return;
		}
		status.account = humanGlobalInfoBridge.account;
		status.humanId = humanGlobalInfoBridge.id;
		status.stageNodeId = humanGlobalInfoBridge.nodeId;
		status.stagePortId = humanGlobalInfoBridge.portId;
		status.channel = humanGlobalInfoBridge.channel;
		status.status = ConnectionStatus.STATUS_PLAYING;
		connPrxNew.updateStatus(status);

		// 先通知重连成功

		//更新humanObject的conn
		HumanObjectServiceProxy prxHumanObj = HumanObjectServiceProxy.newInstance(humanGlobalInfo.nodeId, humanGlobalInfo.portId, humanGlobalInfo.id);
		prxHumanObj.ChangeConnPoint(callPoint);
		//更新humanGlobal对应的HumanObject 的conn
		HumanGlobalServiceProxy prxHumanGol = HumanGlobalServiceProxy.newInstance();
//		prxHumanGol.ChangeConnPoint(humanGlobalInfoBridge.id, callPoint);

		HumanGlobalServiceProxy prxHumanGolB = HumanGlobalServiceProxy.newInstance(nodeId);
//		prxHumanGolB.ChangeConnPoint(humanGlobalInfoBridge.id, callPoint);
		//更新humanObject的conn
		HumanObjectServiceProxy prxb = HumanObjectServiceProxy.newInstance(humanGlobalInfoBridge.nodeId, humanGlobalInfoBridge.portId, humanGlobalInfoBridge.id);
		prxb.ChangeConnPoint(callPoint);

		//copy 旧的连接 缓冲过来
		ConnectionProxy connPrx = ConnectionProxy.newInstance(humanGlobalInfo.connPoint);
		if (connPrx != null) {
			connPrx.getMsgBuf();
			connPrx.listenResult(this::_result_InitMsgBuf, "callPoint", callPoint, "oldConnPoint", humanGlobalInfo.connPoint);
		}

		Event.fire(EventKey.HUMAN_RETABLISH_CONNECTION, "humanId", humanGlobalInfoBridge.id);
	}

	/** 
	 * 返回登录结果
	 * <AUTHOR>
	 * @Date 2024/2/29
	 * @Param 
	 */
	public void sendMsg_role_login_s2c(CallPoint callPoint, int code, Object... objects){
		ConnectionProxy connPrxNew = ConnectionProxy.newInstance(callPoint);
		MsgLogin.role_login_s2c.Builder msg = MsgLogin.role_login_s2c.newBuilder();
		msg.setCode(code);
		connPrxNew.sendMsg(MsgIds.role_login_s2c, new Chunk(msg));
	}

	/** 
	 * 通知重连结果 
	 * <AUTHOR>
	 * @Date 2024/2/29
	 * @Param 
	 */
	private void sendMsg_role_reconnect_s2c(CallPoint callPoint, int errorCode){
		ConnectionProxy connPrxNew = ConnectionProxy.newInstance(callPoint);
		MsgLogin.role_reconnect_s2c.Builder msg = MsgLogin.role_reconnect_s2c.newBuilder();
		msg.setCode(errorCode);
		connPrxNew.sendMsg(MsgIds.role_reconnect_s2c, new Chunk(msg));
	}


	@Listener(EventKey.HUMAN_LOGIN_FINISH)
	public void onHumanLogin(Param param) {
		HumanObject humanObj = param.get("humanObj");
		Human human = humanObj.getHuman();
		
//		human.setSessionKey((long)humanObj.connPoint.servId);
		//这里做同步操作 避免登陆根据sessionKey查询时需要刷新写缓存
//		human.update(true);
//		if (humanObj.humanPatchDispose("transfer", 2)){
//			if (humanObj.getHumanSubjoinInfo().getTransferTime() == 0){
//				return;
//			}
//			ConfCharacterHuman confCharacterHuman = ConfCharacterHuman.get(String.valueOf(human.getSoulSn()));
//			if (confCharacterHuman == null) {
//				return;
//			}
//			human.setProfession(confCharacterHuman.profession);
//
//
//			UnitManager.inst().calcSoulPtPlusCombat(humanObj);
//			UnitManager.inst().propCalc(humanObj, null);
//			HumanInfoChange.listen(humanObj);
//		}
	}
	


	/** 
	 * 创建角色
	 * <AUTHOR>
	 * @Date 2023/4/17
	 * @Param 
	 */
	public void characterCreate(AccountObject obj, boolean test, boolean isCheck){
		NameServiceProxy prx = NameServiceProxy.newInstance();
		prx.randomName(obj.serverId);
		prx.listenResult(this::_result_randomNameRepeat, "obj", obj, "test", test, "isCheck", isCheck);
	}
	public void _result_randomNameRepeat(Param results, Param context) {
		// 上下文环境
		AccountObject obj = context.get("obj");
		String randomName = results.getString("randomName");
		boolean test= context.get("test");
		boolean isCheck= context.get("isCheck");
		Log.game.info("loginTrace[{}] login_step_1200 自动自动创角，humanId={}, account={}, context={}", obj.connPoint.servId, obj.humanId, obj.status.account, context);
		//先查询验证是否能创建角色
		HumanCreateApplyServiceProxy prx = HumanCreateApplyServiceProxy.newInstance();
		prx.apply(obj.serverId, obj.status.account, randomName);
		prx.listenResult(this::_result_onCSCharacterCreate, "gateObj", obj, "name", randomName,
				"test", test, "isCheck", isCheck);
	}

	/**
	 * 角色创建请求 查询角色信息 是否可以被创建
	 * @param results
	 * @param context
	 */
	public void _result_onCSCharacterCreate(Param results, Param context) {
		//返回值
		boolean succeed = results.get("result");
		String reason = results.get("reason");

		//上下文
		AccountObject obj = context.get("gateObj");
		int serverId = obj.serverId;
		String name = context.get("name");
		int zone = obj.zone;
		boolean test = context.get("test");
		boolean isCheck = context.get("isCheck");

		//验证是否可以创建角色
		if(!succeed) {
			if(isCheck){
				Log.game.error("loginTrace[{}] login_step_1301 ===角色创建请求 失败 results={} context={}", obj.connPoint.servId,results, context);
				return;
			}
			//返回消息
			ConnectionProxy prx = ConnectionProxy.newInstance(obj.connPoint);
//			prx.sendMsg(MsgIds.SCCharacterCreateResult, new Chunk(msgSender));
			Log.game.info("loginTrace[{}] login_step_1302 创建角色失败 原因{} ‘{}’",obj.connPoint.servId, name, reason);
			return;
		}

		//获取最大值
		long maxShowId = results.get("maxShowId");

		String account = obj.status.account;
		String channel = obj.status.channel;

		Param param = new Param();
		//验证通过 正式创建角色
		Human human = HumanManager.inst().create(maxShowId, serverId, account, channel, name, zone, test, isCheck, param);
		obj.humanId = human.getId();
		// 新创角不走登录直接进游戏
		Log.game.info("loginTrace[{}] login_step_1303===角色准备登录 humanId={} account={}", obj.connPoint.servId, human.getId(), account);
//		AccountManager.inst().characterLogin(obj, human.getId(), false);

		String clientIP = obj.status.clientIP;

		param.put("humanId", obj.humanId);
		param.put("connPoint", obj.connPoint);
		param.put("zone", zone);
		param.put("firstStory", -1);
		param.put("clientIP", clientIP);
		param.put("pid", obj.pid);
		param.put("eLanguage", obj.eLanguage);
		param.put("account", obj.status.account);
		param.put("serverId", human.getServerId());
		param.put("regional", obj.regional);
		param.put("isNewCreate", true);

		Log.game.error("loginTrace[{}] login_step_1402 account={}, humanId={}", obj.connPoint.servId, obj.status.account, human.getId());
		HumanServiceProxy proxy = HumanService.createProxy(human.getId());
		proxy.login(obj.humanId, param);
		proxy.listenResult(this::_result_loginRedis, "accObj", obj, "reconnect", false);

	}




	private void _result_callback_backup1(Param results, Param context) {

	}

	private void _result_callback_backup2(Param results, Param context) {

	}

	private void _result_callback_backup3(Param results, Param context) {

	}

	private void _result_callback_backup4(Param results, Param context) {

	}

	public void onCheckLoginLose(long humanId, String account, String param){
//		Event.fire(EventKey.CHECK_LOGIN_LOSE, "humanId", humanId);

	}




	//==============================================

	public void _msg_login_auth_c2s(CallPoint connPoint, ConnectionStatus connStatus, MsgLogin.login_auth_c2s msg) {
		int serverId = 0;//msg.getServerId(); //服务器ID
		String account = msg.getUid();// 账号，即用户ID
		//账号名为空，直接返回
		if(StringUtils.isEmpty(account)){
			sendMsg_login_auth_s2c(connPoint, CHECK_FAIL, serverId, new ArrayList<>());
			return;
		}
		String channel = "1";// TODO 临时，渠道后面要改
		connStatus.channel = channel;

		int eLanguage = Define.ELanguage.zh.getNumber();//msg.getELanguage();

		Param param = new Param();
		param.put("connPoint", connPoint);
		param.put("connStatus", connStatus);
		param.put("channel", channel);
		param.put("account", account);
		param.put("serverId", serverId);
		param.put("accountObj", null);
		param.put("eLanguage", eLanguage);
//		 param.put("zone", msg.getZone()); TODO

		login(account, channel, param);
	}

	private void sendMsg_login_auth_s2c(CallPoint connPoint, int code, int serverId, List<Define.p_login_info> dInfoList){
		ConnectionProxy prx = ConnectionProxy.newInstance(connPoint);
		MsgLogin.login_auth_s2c.Builder msg = MsgLogin.login_auth_s2c.newBuilder();
		msg.setCode(code);
		msg.setTimeZone((int)(TimeZone.getDefault().getRawOffset() / Time.HOUR));// 时区
		msg.setOpenTime(DateTimeUtils.getStartServerTime());// TODO 开服时间
		msg.addAllRoleList(dInfoList);
		prx.sendMsg(MsgIds.login_auth_s2c, new Chunk(msg));
	}

	/**
	 * 角色登录请求
	 * <AUTHOR>
	 * @Date 2024/3/11
	 * @Param
	 */
	public void _msg_role_login_c2s(CallPoint connPoint, ConnectionStatus connStatus, MsgLogin.role_login_c2s msg, AccountObject obj){
		String account = msg.getUid();
		long humanId = msg.getRoleId();
		if(humanId == 0){
			// 自动创角
			characterCreate(obj, false, false);
			return;
		}
		characterLogin(obj, humanId, false);
	}

	
}