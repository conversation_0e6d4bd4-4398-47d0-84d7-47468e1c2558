package org.gof.demo.seam.msg;

import com.google.protobuf.CodedInputStream;
import com.google.protobuf.GeneratedMessageV3;
import org.gof.core.statistics.StatisticsOB;
import org.gof.core.support.Config;
import org.gof.core.support.MsgHandler;
import org.gof.core.support.Param;
import org.gof.core.support.observer.MsgSender;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.msg.MsgIds;
import org.gof.demo.worldsrv.support.Log;

import java.io.IOException;

public class HumanExtendMsgHandler extends MsgHandler {
	
	private HumanExtendMsgHandler() {}
	
	@Override
	protected void fire(GeneratedMessageV3 msg, Param param) {
		
		//如果用户正在切换地图中，则不接受任何请求
		HumanObject humanObj = param.get("humanObj");
		
		//切换过地图后可以认为OK了
//		if(msg instanceof CSStageEnter){
//			humanObj.setStageSwitching(false);
//		}
		
		if(humanObj.isStageSwitching()) {
			// 玩家切图时收到消息特殊处理
			stageSwitchingMsg(humanObj, msg);
			return;
		}
		
		MsgParam mp = new MsgParam(msg);
		mp.setHumanObject(humanObj);
		
		//执行消息，并且执行统计
		long start = Config.STATISTICS_ENABLE ? System.nanoTime() : 0;
		MsgSender.fire(mp);
		if(start > 0)
			StatisticsOB.msg(msg.getClass().getName(), System.nanoTime()-start);
		
	}

	/** 
	 * 玩家切图时收到消息特殊处理
	 * <AUTHOR>
	 * @Date 2023/11/17
	 * @Param 
	 */
	private void stageSwitchingMsg(HumanObject humanObj, GeneratedMessageV3 msg){
		int msgId = MsgIds.getIdByClass(msg.getClass());

		Log.temp.info("===切图过程中未处理消息msg={}", msgId);
	}
	
	@Override
	protected GeneratedMessageV3 parseFrom(int type, CodedInputStream s) throws IOException {
		return MsgIds.parseFrom(type, s);
	}
	
}