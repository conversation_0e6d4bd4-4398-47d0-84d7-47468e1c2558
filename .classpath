<?xml version="1.0" encoding="UTF-8"?>
<classpath>
	<classpathentry kind="src" path="core"/>
	<classpathentry kind="src" path="connsrv"/>
	<classpathentry kind="src" path="dbsrv"/>
	<classpathentry kind="src" path="gen"/>
	<classpathentry kind="con" path="org.eclipse.jdt.launching.JRE_CONTAINER"/>
	<classpathentry kind="lib" path="../libs/zmq.jar"/>
	<classpathentry kind="lib" path="../libs/mysql-connector-java-5.1.22-bin.jar"/>
	<classpathentry kind="lib" path="../libs/commons-lang3.jar"/>
	<classpathentry kind="lib" path="../libs/cron-utils-9.1.1.jar"/>
	<classpathentry kind="lib" path="../libs/guava-19.0.jar"/>
	<classpathentry kind="lib" path="../libs/picocli-4.3.0.jar"/>
	<classpathentry kind="lib" path="../libs/javassist.jar"/>
	<classpathentry kind="lib" path="../libs/freemarker.jar"/>
	<classpathentry kind="lib" path="../libs/fastjson-1.2.75.jar"/>
	<classpathentry kind="lib" path="../libs/quartz.jar"/>
	<classpathentry kind="lib" path="../libs/quartz-jobs.jar"/>
	<classpathentry kind="lib" path="../libs/log4j-api.jar"/>
	<classpathentry kind="lib" path="../libs/log4j-core.jar"/>
	<classpathentry kind="lib" path="../libs/log4j-slf4j-impl.jar"/>
	<classpathentry kind="lib" path="../libs/slf4j-api.jar"/>
	<classpathentry kind="lib" path="../libs/netty-all-4.1.36.Final.jar"/>
	<classpathentry kind="lib" path="../libs/protobuf.jar"/>
	<classpathentry kind="lib" path="../libs/httpclient.jar"/>
	<classpathentry kind="lib" path="../libs/httpcore.jar"/>
	<classpathentry kind="lib" path="../config"/>
	<classpathentry kind="lib" path="../plant/json"/>
	<classpathentry kind="lib" path="../plant/nameFix"/>
	<classpathentry kind="lib" path="../plant/stageConfig"/>
	<classpathentry kind="output" path="bin"/>
</classpath>
