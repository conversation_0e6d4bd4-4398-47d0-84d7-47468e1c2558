# 合成大西瓜排行榜修复说明

## 问题描述

在`on_act_fruit_merge_info_c2s`方法中，原来使用`getMyRank`只能获取排名，无法获取玩家在排行榜中的分数。这导致无法检测排行榜分数是否与玩家当前最高分一致，可能出现排行榜数据不同步的问题。

## 修复方案

### 1. 替换排名获取方法

**原方法**：
```java
RankManager.inst().getMyRank(confTerm.rank_id, humanObj.getHuman().getServerId(), humanObj.id, ret -> {
    int rank = Utils.intValue(ret.result());
    builder.setRank(rank);
    // ...
});
```

**新方法**：
```java
RankManager.inst().getMyRankAndScore(confTerm.rank_id, humanObj.getHuman().getServerId(), humanObj.id, ret -> {
    int rank = 0;
    long rankScore = 0;
    if (ret.succeeded()) {
        List<String> result = ret.result();
        if (result != null && result.size() >= 2) {
            rank = Integer.parseInt(result.get(0));
            rankScore = Long.parseLong(result.get(1));
        }
    }
    // ...
});
```

### 2. 添加排行榜分数检测

在获取到排行榜分数后，检测是否小于玩家当前最高分：

```java
// 检测排行榜分数是否小于当前最高分，如果是则刷新排行榜
if (rankScore < fruitMergeData.maxScore) {
    Log.activity.info("检测到排行榜分数({})小于当前最高分({}), 刷新排行榜, humanId={}", 
        rankScore, fruitMergeData.maxScore, humanObj.getHumanId());
    RankManager.inst().updateRankWithTime(humanObj, rankKey, confRanktype, fruitMergeData.maxScore);
    // 更新后重新获取排名
    builder.setRank(0); // 先设置为0，后续会重新获取
}
```

### 3. 优化排名重新获取逻辑

修改`getTopScoreList`方法，支持在排行榜刷新后重新获取正确的排名：

```java
private void getTopScoreList(HumanObject humanObj, ConfRanktype confRanktype, String rankKey,
                            MsgAct2.act_fruit_merge_info_s2c.Builder builder, int scoreRankSize) {
    // 如果排名为0，说明需要重新获取排名
    if (builder.getRank() == 0) {
        // 重新获取排名
        RankManager.inst().getMyRank(confRanktype.sn, humanObj.getHuman().getServerId(), humanObj.id, rankRet -> {
            int newRank = 0;
            if (rankRet.succeeded()) {
                newRank = Utils.intValue(rankRet.result());
            }
            builder.setRank(newRank);
            
            // 获取前100名分数列表
            getScoreListAndSend(humanObj, confRanktype, rankKey, builder, Math.min(newRank, 100));
        });
    } else {
        // 直接获取分数列表
        getScoreListAndSend(humanObj, confRanktype, rankKey, builder, scoreRankSize);
    }
}
```

## 修复效果

### 1. 数据同步保障
- 每次获取活动信息时都会检测排行榜数据一致性
- 自动修复排行榜分数与玩家最高分不一致的问题
- 确保排行榜数据的准确性

### 2. 性能优化
- 只在检测到数据不一致时才刷新排行榜
- 避免不必要的排行榜更新操作
- 保持原有的异步处理机制

### 3. 用户体验改善
- 玩家看到的排行榜数据始终是最新的
- 避免因数据不同步导致的排名显示错误
- 提供准确的排行榜信息

## 触发场景

### 1. 数据不一致场景
- 服务器重启后排行榜数据丢失
- 排行榜Redis数据异常
- 玩家数据回档导致的不一致
- 手动修改玩家数据后的同步

### 2. 正常场景
- 玩家正常游戏，排行榜数据一致
- 不会触发额外的排行榜更新
- 保持原有的性能表现

## 日志记录

当检测到排行榜数据不一致时，会记录详细日志：

```
检测到排行榜分数(旧分数)小于当前最高分(新分数), 刷新排行榜, humanId=玩家ID
```

这有助于：
- 监控排行榜数据一致性问题的频率
- 分析数据不一致的原因
- 验证修复机制的有效性

## 兼容性

### 1. 向后兼容
- 保持原有的消息协议不变
- 不影响客户端的处理逻辑
- 保持原有的功能特性

### 2. 扩展性
- 新增的检测机制可以应用到其他活动
- 排行榜修复逻辑可以复用
- 为后续的数据一致性保障提供参考

## 总结

这次修复主要解决了合成大西瓜活动中排行榜数据可能不一致的问题：

1. **问题根源**：只获取排名，无法检测分数一致性
2. **解决方案**：同时获取排名和分数，检测并修复不一致
3. **实现方式**：使用`getMyRankAndScore`替代`getMyRank`
4. **保障机制**：自动检测、自动修复、详细日志
5. **性能考虑**：只在必要时才进行排行榜更新

修复后，玩家每次查询活动信息时都能获得准确的排行榜数据，提升了游戏的数据一致性和用户体验。
