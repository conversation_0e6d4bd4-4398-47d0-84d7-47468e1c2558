/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class org_gof_demo_worldsrv_support_recast_MTPathFinding */

#ifndef _Included_org_gof_demo_worldsrv_support_recast_MTPathFinding
#define _Included_org_gof_demo_worldsrv_support_recast_MTPathFinding
#ifdef __cplusplus
extern "C" {
#endif
/*
 * Class:     org_gof_demo_worldsrv_support_recast_MTPathFinding
 * Method:    createFindPath
 * Signature: (I)J
 */
JNIEXPORT jlong JNICALL Java_org_gof_demo_worldsrv_support_recast_MTPathFinding_createFindPath
  (JNIEnv *, jclass, jint);

/*
 * Class:     org_gof_demo_worldsrv_support_recast_MTPathFinding
 * Method:    freeFindPath
 * Signature: (J)V
 */
JNIEXPORT void JNICALL Java_org_gof_demo_worldsrv_support_recast_MTPathFinding_freeFindPath
  (JNIEnv *, jclass, jlong);

/*
 * Class:     org_gof_demo_worldsrv_support_recast_MTPathFinding
 * Method:    loadNavData
 * Signature: (ILjava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_org_gof_demo_worldsrv_support_recast_MTPathFinding_loadNavData
  (JNIEnv *, jclass, jint, jstring);

/*
 * Class:     org_gof_demo_worldsrv_support_recast_MTPathFinding
 * Method:    findPath
 * Signature: (J[F[FI)[F
 */
JNIEXPORT jfloatArray JNICALL Java_org_gof_demo_worldsrv_support_recast_MTPathFinding_findPath
  (JNIEnv *, jclass, jlong, jfloatArray, jfloatArray, jint);

/*
 * Class:     org_gof_demo_worldsrv_support_recast_MTPathFinding
 * Method:    isPosInBlock
 * Signature: (J[FI)Z
 */
JNIEXPORT jboolean JNICALL Java_org_gof_demo_worldsrv_support_recast_MTPathFinding_isPosInBlock
  (JNIEnv *, jclass, jlong, jfloatArray, jint);

/*
 * Class:     org_gof_demo_worldsrv_support_recast_MTPathFinding
 * Method:    raycast
 * Signature: (J[F[FI)[F
 */
JNIEXPORT jfloatArray JNICALL Java_org_gof_demo_worldsrv_support_recast_MTPathFinding_raycast
  (JNIEnv *, jclass, jlong, jfloatArray, jfloatArray, jint);

/*
 * Class:     org_gof_demo_worldsrv_support_recast_MTPathFinding
 * Method:    posHeight
 * Signature: (J[FI)[F
 */
JNIEXPORT jfloatArray JNICALL Java_org_gof_demo_worldsrv_support_recast_MTPathFinding_posHeight
  (JNIEnv *, jclass, jlong, jfloatArray, jint);

/*
 * Class:     org_gof_demo_worldsrv_support_recast_MTPathFinding
 * Method:    randomPoint
 * Signature: (JII)[F
 */
JNIEXPORT jfloatArray JNICALL Java_org_gof_demo_worldsrv_support_recast_MTPathFinding_randomPoint
  (JNIEnv *, jclass, jlong, jint, jint);

/*
 * Class:     org_gof_demo_worldsrv_support_recast_MTPathFinding
 * Method:    randomAroundPoint
 * Signature: (JI[FFI)[F
 */
JNIEXPORT jfloatArray JNICALL Java_org_gof_demo_worldsrv_support_recast_MTPathFinding_randomAroundPoint
  (JNIEnv *, jclass, jlong, jint, jfloatArray, jfloat, jint);

#ifdef __cplusplus
}
#endif
#endif
