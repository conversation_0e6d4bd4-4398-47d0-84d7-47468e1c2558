package org.gof.core.support;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;


/**
 * 初始化KEY安全的数据存储
 * 不使用ConcurrentHashMap.computeIfAbsent()避免线程争用
 * 不保证VALUE的多线程安全
 * 不懂这个怎么用的就别用！
 */
public class SafeKeyData<K, V> {
    //存储数据
    private final Map<K, V> data = new HashMap<>();

    /**
     * 获取统计信息
     * @param key
     * @param mappingFunction
     * @return
     */
    public V computeIfAbsent(K key, Function<? super K, ? extends V> mappingFunction) {
        V d = data.get(key);
        if(d == null) {
            synchronized (data) {
                d = data.get(key);
                if(d == null) {
                    d = mappingFunction.apply(key);
                    data.put(key, d);
                }
            }
        }

        return d;
    }

    /**
     * 清空
     */
    public void clear() {
        synchronized (data) {
            data.clear();
        }
    }

    public Collection<V> values() {
        return data.values();
    }

    public Set<Map.Entry<K, V>> entrySet() {
        return data.entrySet();
    }

    @Override
    public String toString() {
        return data.toString();
    }
}
