package org.gof.core.support.observer;

import com.google.protobuf.GeneratedMessageV3;

import java.util.HashMap;
import java.util.Map;

public class MsgParamBase {
	private GeneratedMessageV3 msg;
	
	private Map<String, Object> param = new HashMap<>();
	
	public MsgParamBase(GeneratedMessageV3 msg) {
		super();
		this.msg = msg;
	}
	
	@SuppressWarnings("unchecked")
	public <T> T getMsg() {
		return (T) msg;
	}
	
	public void setMsg(GeneratedMessageV3 msg) {
		this.msg = msg;
	}
	
	@SuppressWarnings("unchecked")
	public <T> T get(String key) {
		return (T) param.get(key);
	}
	
	public void put(String key, Object value) {
		param.put(key, value);
	}
}