package org.gof.core.support;

import java.util.HashMap;
import java.util.Map;

public abstract class ManagerBase {
	public static Map<String, ManagerBase> instances = new HashMap<>();
	
	/**
	 * 获取唯一实例
	 * @param clazz
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static <T extends ManagerBase> T inst(Class<?> clazz) {
		Object inst = instances.get(clazz.getName());
		if(inst == null) {
			try {
				inst = clazz.newInstance();
			}catch (InstantiationException | IllegalAccessException e) {
				throw new SysException("创建实例失败，Clazz{}", clazz);
			} 
			
			instances.put(clazz.getName(), (ManagerBase) inst);
		}
		
		return (T)inst;
	}
	
}