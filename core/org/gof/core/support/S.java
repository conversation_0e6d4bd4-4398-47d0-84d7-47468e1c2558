package org.gof.core.support;

public class S {
    //默认非跨服，由跨服启动时赋值
    public static boolean isBridge = false;
    public static boolean gameStartupFinish = false;

    //中心服标记
    public static boolean isAdmin = false;
    //跨服标记
    public static boolean isCross = false;
    //游戏服标记
    public static boolean isGameServer = false;

    public static boolean isGameLeagueOpen = false;
    public static boolean isTestLog = false;
    public static boolean isRedis = true;
    public static boolean isCrossClose = false;
    public static boolean isClose = false;

    public static boolean isSelectLog = false;


    public static boolean isArena1 = false;

    public static boolean isParam1 = false;

    public static boolean isParam2 = false;
	
	public static boolean isParam3 = false;

    public static boolean isServerMerge = false;// 是否合服过


}
