package org.gof.core.support;

import com.google.protobuf.CodedInputStream;
import com.google.protobuf.GeneratedMessageV3;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.gof.core.support.log.LogCore;
import org.slf4j.Logger;

import java.io.IOException;
import java.lang.reflect.Constructor;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public abstract class MsgHandler {
	private static final Map<Class<?>, MsgHandler> instances = new ConcurrentHashMap<>();
	private final Logger log = LogCore.msg;
	
	//发布消息
	protected abstract void fire(GeneratedMessageV3 msg, Param param);
	//通过消息ID获取消息类型
	protected abstract GeneratedMessageV3 parseFrom(int type, CodedInputStream s) throws IOException;

	/**
	 * 获取唯一实例
	 * @param clazz
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static <T extends Msg<PERSON>andler> T getInstance(Class<?> clazz) {
		try {
			Object inst = instances.get(clazz);
			if(inst == null) {
				Constructor<?> constr = clazz.getDeclaredConstructor();
				constr.setAccessible(true);
				inst = constr.newInstance();
			}
			
			return (T)inst;
		} catch (Exception e) {
			throw new SysException(e);
		}
	}

	/**
	 * 消息处理
	 * @param buffer
	 */
	public void handle(byte[] buffer, Object...params) {
		//取出消息头
		@SuppressWarnings("unused")
		int len = Utils.bytesToInt(buffer, 0);		//消息长度
		int msgId = Utils.bytesToInt(buffer, 4);		//消息ID
		
		//记录日志
		if(log.isDebugEnabled() && msgId != 1211 && msgId != 1301) {
			log.info("接收到客户端消息：id={}", msgId);
		}

		//取出消息体
		CodedInputStream in = CodedInputStream.newInstance(buffer, 8, buffer.length - 8);
		
		try {
			//利用反射解析协议
			GeneratedMessageV3 msg = parseFrom(msgId, in);
			if(msg == null) {
				log.error("接收到错误的客户端消息，无法解析：id={}", msgId);
				return;
			} 
			//发送接到消息事件
			fire(msg, new Param(params));
		} catch (Exception e) {
//			throw new SysException(e);
			log.error("异常消息解析 msgId={},len={},e={}", msgId, len, ExceptionUtils.getStackTrace(e));
		}
	}


}
