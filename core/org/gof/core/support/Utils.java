package org.gof.core.support;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.cronutils.model.Cron;
import com.cronutils.model.CronType;
import com.cronutils.model.definition.CronDefinition;
import com.cronutils.model.definition.CronDefinitionBuilder;
import com.cronutils.model.time.ExecutionTime;
import com.cronutils.parser.CronParser;
import com.google.protobuf.GeneratedMessageV3;
import com.google.protobuf.Message;
import com.google.protobuf.Parser;
import io.vertx.core.AsyncResult;
import io.vertx.core.Future;
import io.vertx.core.Handler;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.redis.client.Redis;
import io.vertx.redis.client.RedisAPI;
import io.vertx.redis.client.Request;
import io.vertx.redis.client.Response;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.compressors.gzip.GzipCompressorInputStream;
import org.apache.commons.compress.compressors.gzip.GzipCompressorOutputStream;
import org.apache.commons.compress.compressors.lz4.BlockLZ4CompressorInputStream;
import org.apache.commons.compress.compressors.lz4.BlockLZ4CompressorOutputStream;
import org.apache.commons.compress.compressors.snappy.SnappyCompressorInputStream;
import org.apache.commons.compress.compressors.snappy.SnappyCompressorOutputStream;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.reflect.ConstructorUtils;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.apache.commons.lang3.reflect.MethodUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ClientConnectionManager;
import org.apache.http.conn.scheme.Scheme;
import org.apache.http.conn.scheme.SchemeRegistry;
import org.apache.http.conn.ssl.SSLSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.apache.logging.log4j.message.ParameterizedMessage;
import org.gof.core.Port;
import org.gof.core.dbsrv.redis.AsyncActionResult;
import org.gof.core.dbsrv.redis.AwaitUtil;
import org.gof.core.dbsrv.redis.EntityManager;
import org.gof.core.dbsrv.redis.RedisTools;
import org.gof.core.support.idAllot.IdAllotPoolBase;
import org.gof.demo.worldsrv.common.ServerList;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.guild.GuildParamKey;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.produce.ProduceVo;
import org.gof.demo.worldsrv.support.*;
import org.quartz.CronExpression;
import org.quartz.CronScheduleBuilder;
import org.quartz.CronTrigger;
import org.quartz.TriggerBuilder;
import sun.misc.SharedSecrets;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.beans.BeanInfo;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.io.*;
import java.lang.management.ManagementFactory;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.sql.Timestamp;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.temporal.ChronoField;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.Map.Entry;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.zip.GZIPOutputStream;

import static org.gof.demo.worldsrv.support.Util.getServerIdReal;
import static org.gof.demo.worldsrv.support.Util.getServerTagId;

@SuppressWarnings("deprecation")
public class Utils {

	public static final int SIZE_KB = 1024;
	public static final int SIZE_MB = 1024 * 1024;

	public static final int I100 = 100;
	public static final int I1000 = 1000;
	public static final int I10000 = 10000;
	public static final long L100 = 100L;
	public static final long L1000 = 1000L;
	public static final long L10000 = 10000L;
	public static final float F100 = 100.0F;
	public static final float F1000 = 1000.0F;
	public static final float F10000 = 10000.0F;
	public static final double D100 = 100.0D;
	public static final double D1000 = 1000.0D;
	public static final double D10000 = 10000.0D;

	// cron表达式格式"秒 分 时 日 月 周"
	// 秒（0~59）
	// 分（0~59）
	// 时（0~23）
	// 日（0~31，但是你需要考虑你月的天数）
	// 月（0~11）
	// 周（1~7 1=SUN 或 SUN，MON，TUE，WED，THU，FRI，SAT）
	// * 代表所有可能的值
	// ? 表示不指定值
	// / 指定数值的增量

	/**
	 * MD5加密
	 * @param s	被加密的字符串
	 * @return	加密后的字符串
	 */
	public static String md5_2(String s) {
		if(s == null) s = "";
		try {
			byte[] strTemp = s.getBytes("UTF-8");
			MessageDigest mdTemp = MessageDigest.getInstance("MD5");
			mdTemp.update(strTemp);
			byte[] md = mdTemp.digest();
			// 将结果转换为十六进制字符串
			StringBuilder sb = new StringBuilder();
			for (byte b : md) {
				sb.append(String.format("%02x", b & 0xff));
			}
			// 返回十六进制字符串
			return sb.toString();
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}

	/**
	 * 是否调试模式
	 *
	 * @return
	 */
	public static boolean isDebugMode() {
		boolean debug = false;
		List<String> arguments = ManagementFactory.getRuntimeMXBean().getInputArguments();
		for (String str : arguments) {
			if (str.startsWith("-agentlib")) {
				debug = true;
				break;
			}
		}
		return debug;
//		return Config.DATA_DEBUG;
	}


	/**
	 * MD5加密
	 * @param s	被加密的字符串
	 * @return	加密后的字符串
	 */
	public static String md5(String s) {
		if(s == null) s = "";
		char hexDigits[] = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};
		try {
			byte[] strTemp = s.getBytes("UTF-8");
			MessageDigest mdTemp = MessageDigest.getInstance("MD5");
			mdTemp.update(strTemp);
			byte[] md = mdTemp.digest();
			int j = md.length;
			char str[] = new char[j * 2];
			int k = 0;
			for (int i = 0; i < j; i++) {
				byte byte0 = md[i];
				str[k++] = hexDigits[byte0 & 0xf];
				str[k++] = hexDigits[byte0 >>> 4 & 0xf];
			}
			return new String(str);

		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}
	
	/**
	 * 判断两个对象是否相等
	 * @param objA
	 * @param objB
	 * @return
	 */
	public static boolean isEquals(Object objA, Object objB) {
		return new EqualsBuilder().append(objA, objB).isEquals();
	}
	
	/**
	 * 参数1是否为参数2的子类或接口实现
	 * @param parentCls
	 * @return
	 */
	public static boolean isInstanceof(Class<?> cls, Class<?> parentCls) {
		return parentCls.isAssignableFrom(cls);
	}
	
	/**
	 * 格式化时间戳
	 * @param timestamp
	 * @param pattern
	 * @return
	 */
	public static String formatTime(long timestamp, String pattern) {
		SimpleDateFormat format = new SimpleDateFormat(pattern);

		return format.format(new Date(timestamp));
	}
	
	/**
	 * 将日期字符串转化为毫秒数
	 * @param dateTime
	 * @return
	 */
	public static long formatTimeToLong(String dateTime){
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Date date;
		try {
			date = sdf.parse(dateTime);
			return date.getTime();
		} catch (ParseException e) {
			Log.temp.error("===e={}", e);
		}
		return 0;
	}
	
	/**
	 * 将毫秒数转化为日期(毫秒--->yyyy-MM-dd HH:mm:ss)
	 * @param time (单位:毫秒)
	 * @return
	 */
	public static String formatTimeToDate(long time){
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Date date = new Date();
		date.setTime(time);
		return sdf.format(date);
	}
	
	/**
	 * 将毫秒数转化为日期精确到小时(毫秒--->yyyy-MM-dd HH)
	 * @param time (单位:毫秒)
	 * @return
	 */
	public static String formatTimeToDateHH(long time){
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH");
		Date date = new Date();
		date.setTime(time);
		return sdf.format(date);
	}

	/**
	 * 将毫秒数转化为天数整型的(毫秒--->20210304)
	 * @param dateTime
	 * @return
	 */
	public static int formatTimeToInt(long dateTime, String format){
		SimpleDateFormat sdf = new SimpleDateFormat(format);
		Date date = new Date(dateTime);
		return Integer.parseInt(sdf.format(date));
	}

	public static int formatTimeToInt(long dateTime){
		return formatTimeToInt(dateTime, "yyyyMMdd");
	}
	/**
	 * 根据时分秒配置 获取今天配置时间点
	 * @param dateStr
	 * @param pattern
	 * @return
	 */
	public static long formatDateStr(String dateStr, String pattern) {
		try {
			SimpleDateFormat bartDateFormat = new SimpleDateFormat(pattern);
			
			return bartDateFormat.parse(dateStr).getTime();
		} catch (Exception ex) {
			throw new SysException(ex);
		}
	}


	public static int getDayBetween(long time1, long time2, int hour){
		Calendar cal1 = Calendar.getInstance();
		cal1.clear();// clear一下或set一下data，a.set(Calendar.DATE, 1);
		cal1.setTimeInMillis(time1);
		int nowhour = cal1.get(Calendar.HOUR_OF_DAY);
		if(nowhour<hour){
			cal1.setTimeInMillis(cal1.getTimeInMillis()-hour*3600000);
		}
		Calendar cal2 = Calendar.getInstance();
		cal2.clear();// clear一下或set一下data，a.set(Calendar.DATE, 1);
		cal2.setTimeInMillis(time2);
		nowhour = cal2.get(Calendar.HOUR_OF_DAY);
		if(nowhour<hour){
			cal2.setTimeInMillis(cal2.getTimeInMillis()-hour*3600000);
		}
		return Utils.getDaysBetween(cal1.getTimeInMillis(), cal2.getTimeInMillis());
	}
	
	/**
	 * 两个时间戳相差的天数(时间大小不分先后,最后返回的都是正值)
	 * @param ta
	 * @param tb
	 * @return
	 */
	public static int getDaysBetween(long ta, long tb) {
		Calendar a = Calendar.getInstance();
		a.clear();// clear一下或set一下data，a.set(Calendar.DATE, 1);
		a.setTimeInMillis(ta);
		Calendar b = Calendar.getInstance();
		b.clear();
		b.setTimeInMillis(tb);
		
		if (a.after(b)) {
			Calendar swap = a;
			a = b;
			b = swap;
		}
		
		int days = b.get(Calendar.DAY_OF_YEAR) - a.get(Calendar.DAY_OF_YEAR);
		int y2 = b.get(Calendar.YEAR);
		if (a.get(Calendar.YEAR) != y2) {
			a = (Calendar) a.clone();
			do {
				days += a.getActualMaximum(Calendar.DAY_OF_YEAR);
				a.add(Calendar.YEAR, 1);
			} while (a.get(Calendar.YEAR) != y2);
		}
		return days;
	}

	/** 
	 * 获取两个时间戳时间差小时数 
	 * <AUTHOR>
	 * @Date 2023/5/18
	 * @Param 
	 */
	public static int getHourBetween(long ta, long tb) {
		long hourTime = tb - ta;
		int hour = (int)Math.floor( (double)(hourTime / Time.HOUR));
		return Math.abs(hour);
	}
	
	/**
	 * 是否是同一天
	 * @param ta
	 * @param tb
	 * @return
	 */
	public static boolean isSameDay(long ta, long tb) {
		return Utils.formatTime(ta, "yyyyMMdd").equals(Utils.formatTime(tb, "yyyyMMdd"));
	}

	/**
	 * 是否是同一天
	 */
	public static boolean isSameDay(long time1, long time2, int hour) {
		time1 = time1 - hour * Time.HOUR;
		time2 = time2 - hour * Time.HOUR;
		return isSameDay(time1, time2);
	}
	
	/**
	 * 是否同一周
	 * @param ta
	 * @param tb
	 * @return
	 */
	public static boolean isSameWeek(long ta, long tb) {
		if(getTimeBeginOfWeek(ta) == getTimeBeginOfWeek(tb)) {
			return true;
		}
		return false;
	}

	public static boolean isSameWeek2(long ta, long tb){
		Calendar calendar = Calendar.getInstance();
		calendar.setTimeInMillis(ta);
		calendar.setFirstDayOfWeek(Calendar.MONDAY);
		int weekNumber1 = calendar.get(Calendar.WEEK_OF_YEAR);
        int year1 = calendar.get(Calendar.YEAR);
		Calendar calendar2 = Calendar.getInstance();
		calendar2.setTimeInMillis(tb);
		calendar2.setFirstDayOfWeek(Calendar.MONDAY);
		int weekNumber2 = calendar2.get(Calendar.WEEK_OF_YEAR);
        int year2 = calendar2.get(Calendar.YEAR);
        return year1 == year2 && weekNumber1 == weekNumber2;
    }

	public static long timeToUTC(long localTimeInMillis){
		Calendar calendar= Calendar.getInstance();
		calendar.setTimeInMillis(localTimeInMillis);
		/** 取得时间偏移量 */
		int zoneOffset = calendar.get(java.util.Calendar.ZONE_OFFSET);
		/** 取得夏令时差 */
		int dstOffset = calendar.get(java.util.Calendar.DST_OFFSET);
		/** 从本地时间里扣除这些差量，即可以取得UTC时间*/
		calendar.add(java.util.Calendar.MILLISECOND, -(zoneOffset + dstOffset));
		/** 取得的时间就是UTC标准时间 */
		Date utcDate=new Date(calendar.getTimeInMillis());
		return utcDate.getTime();
	}
	/**
	 * 获取给定时间当前凌晨的时间对象
	 * @param time 取当天凌晨的话传入 System.currentTimeMillis() 即可
	 * @return
	 */
	public static long getTimeBeginOfToday(long time) {
		Calendar ca = Calendar.getInstance();
		ca.clear();// clear一下或set一下data，a.set(Calendar.DATE, 1);
		ca.setTimeInMillis(time);
		ca.set(Calendar.HOUR_OF_DAY, 0);
		ca.set(Calendar.MINUTE, 0);
		ca.set(Calendar.SECOND, 0);
		ca.set(Calendar.MILLISECOND, 0);

		return ca.getTimeInMillis();
	}

	public static long getTime(int year, int month, int day, int hour, int min, int sec) {
		Calendar ca = Calendar.getInstance();
		ca.clear();// clear一下或set一下data，a.set(Calendar.DATE, 1);
		ca.setTimeInMillis(Port.getTime());
		ca.set(Calendar.YEAR, year);
		ca.set(Calendar.MONTH, month);
		ca.set(Calendar.DAY_OF_MONTH, day);
		ca.set(Calendar.HOUR_OF_DAY, hour);
		ca.set(Calendar.MINUTE, min);
		ca.set(Calendar.SECOND, sec);
		ca.set(Calendar.MILLISECOND, 0);
		return ca.getTimeInMillis();
	}

	/**
	 * 获取给定时间当月第一天凌晨的时间对象
	 * @param time
	 * @return
	 */
	public static long getTimeBeginOfMonth(long time) {
		Calendar ca = Calendar.getInstance();
		ca.clear();// clear一下或set一下data，a.set(Calendar.DATE, 1);
		ca.setTimeInMillis(time);
		ca.set(Calendar.DAY_OF_MONTH, 1);//设置为1号,当前日期既为本月第一天
		//将小时至0
		ca.set(Calendar.HOUR_OF_DAY, 0);
		//将分钟至0
		ca.set(Calendar.MINUTE, 0);
		//将秒至0
		ca.set(Calendar.SECOND,0);
		//将毫秒至0
		ca.set(Calendar.MILLISECOND, 0);

		return ca.getTimeInMillis();
	}


	/**
	 * 获取第二天零点时间戳
	 * @return
	 */
	public static long getTomorrowZeroTime(long time) {

		Calendar calendar = Calendar.getInstance();
		calendar.clear();// clear一下或set一下data，a.set(Calendar.DATE, 1);
		calendar.setTimeInMillis(time);
		calendar.add(Calendar.DAY_OF_MONTH, 1);
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		return calendar.getTimeInMillis();


	}



	/**
	 * 获取给定时间本周一的时间对象
	 * @param time 取当天凌晨的话传入 System.currentTimeMillis() 即可
	 * @return
	 */
	public static long getTimeBeginOfWeek(long time) {
		Calendar ca = Calendar.getInstance();
		ca.clear();// clear一下或set一下data，a.set(Calendar.DATE, 1);
		ca.setTimeInMillis(time);
		
		//当今天是星期天的时候，需要特殊处理，因为星期天是按照这个星期第一天算的，而我们不是这么需要的
		long timeCheck = 0;
		if(ca.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY) {
			timeCheck = Time.DAY * 7;
		}

		ca.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
		ca.set(Calendar.HOUR_OF_DAY, 0);
		ca.set(Calendar.MINUTE, 0);
		ca.set(Calendar.SECOND, 0);
		ca.set(Calendar.MILLISECOND, 0);
		
		return ca.getTimeInMillis() - timeCheck;
	}

	/**
	 * 获取给定时间的下个月1号0点0分0秒
	 * <AUTHOR>
	 * @Date 2021/8/9
	 * @Param
	 */
	public static long getTimeBeginOfNextMonthOneDay(long time) {
		Calendar calendar = Calendar.getInstance();
		calendar.clear();// clear一下或set一下data，a.set(Calendar.DATE, 1);
		calendar.setTimeInMillis(time);
		calendar.add(Calendar.MONTH, 1);// 下个月
		calendar.set(Calendar.DAY_OF_MONTH, 1);//设置为1号,当前日期既为本月第一天
		calendar.set(Calendar.HOUR_OF_DAY, 0);//0点时间
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MILLISECOND, 0);

		return calendar.getTimeInMillis();
	}

	/** 
	 * 获取给定时间的当月的1号时间0点0分0秒
	 * <AUTHOR>
	 * @Date 2021/8/10
	 * @Param
	 */
	public static long getTimeBeginOfNowMonthOneDay(long time) {
		Calendar calendar = Calendar.getInstance();
		calendar.clear();// clear一下或set一下data，a.set(Calendar.DATE, 1);
		calendar.setTimeInMillis(time);
		calendar.add(Calendar.MONTH, 0);// 本月
		calendar.set(Calendar.DAY_OF_MONTH, 1);//设置为1号,当前日期既为本月第一天
		calendar.set(Calendar.HOUR_OF_DAY, 0);//0点时间
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MILLISECOND, 0);

		return calendar.getTimeInMillis();
	}
	
	/**
	 * 根据当前时间，获取小时数 24小时格式
	 * @param time 当天的时间 
	 * @return
	 */
	public static int getHourOfTime(long time) {
		Calendar ca = Calendar.getInstance();
		ca.setTimeInMillis(time);
		return ca.get(Calendar.HOUR_OF_DAY);
	}

	/**
	 * 从开始时间到哪一天时间戳
	 * @param fromTime 开始时间
	 * @param toDay 天数
	 * @return
	 */
	public static long toWhichDayTime(long fromTime,int toDay){
		if(fromTime <= 0 || fromTime <= 0){
			return 0;
		}
		Calendar calendar = Calendar.getInstance();
		calendar.clear();// clear一下或set一下data，a.set(Calendar.DATE, 1);
		calendar.setTimeInMillis(fromTime);
		calendar.set(Calendar.DAY_OF_YEAR,calendar.get(Calendar.DAY_OF_YEAR)+ toDay);
		return calendar.getTimeInMillis();
	}


	
	/**
	 * 构造List对象
	 * 
	 * 如果传入的是参数仅仅为一个对象数组(Object[])或原生数组(int[], long[]等)
	 * 那么表现结果表现是不同的，Object[]为[obj[0], obj[1], obj[2]]
	 * 而原生数组则为[[int[0], int[1]，int[2]]]
	 * 多了一层嵌套，需要对原生数组进行特殊处理。
	 * @param <T>
	 * @param ts
	 * @return
	 */
	@SafeVarargs
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static <T> List<T> ofList(T... ts) {
		List result = new ArrayList();
		
		//对Null进行特殊处理
		if(ts == null) {
			result.add(null);
			return result;
		}
		
		//对单独的原始数组类型进行特殊处理
		if(ts.length == 1 && ts[0] != null && OFLIST_ARRAY_CLASS.contains(ts[0].getClass())) {
			if(ts[0] instanceof int[]) {
				int[] val = (int[]) ts[0];
				for(int v : val) {
					result.add(v);
				}
			} else if(ts[0] instanceof long[]) {
				long[] val = (long[]) ts[0];
				for(long v : val) {
					result.add(v);
				}
			} else if(ts[0] instanceof boolean[]) {
				boolean[] val = (boolean[]) ts[0];
				for(boolean v : val) {
					result.add(v);
				}
			} else if(ts[0] instanceof byte[]) {
				byte[] val = (byte[]) ts[0];
				for(byte v : val) {
					result.add(v);
				}
			} else if(ts[0] instanceof double[]) {
				double[] val = (double[]) ts[0];
				for(double v : val) {
					result.add(v);
				}
			}
		} else {	//对象数组
			for(T t : ts) {
				result.add(t);
			}
		}
		
		return result;
	}
	//专供ofList类使用 对于数组类型进行特殊处理
	private static final List<?> OFLIST_ARRAY_CLASS = Utils.ofList(int[].class, long[].class, boolean[].class, byte[].class, double[].class);
	
	/**
	 * 构造Map对象
	 * @param params
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static <K, V> Map<K, V> ofMap(Object...params) {
		LinkedHashMap<K, V> result = new LinkedHashMap<K, V>();
		
		//无参 返回空即可
		if(params == null || params.length == 0) {
			return result;
		}
		
		//处理成对参数
		int len = params.length;
		for (int i = 0; i < len; i += 2) {
			K key = (K) params[i];
			V val = (V) params[i + 1];
			
			result.put(key, val);
		}
		
		return result;
	}
	
	/**
	 * 基于参数创建字符串
	 * #0开始
	 * @param str
	 * @param params
	 * @return
	 */
	public static String createStr(String str, Object...params) {
		return ParameterizedMessage.format(str, params);
	}

	public static void intToBytes(byte[] b,int offset,int v) {
		for(int i = 0; i < 4; ++i) {
			b[offset + i] = (byte)(v >>> (24 - i * 8));
		}
	}
	
	public static int bytesToInt(byte[] b,int offset) {
		int num = 0;
		for(int i = offset; i < offset+4; ++i) {
			num <<= 8;
			num |= (b[i] & 0xff);
		}
		return num;
	}
	
	public static int bytesToInt(Object[] b,int offset) {
		int num = 0;
		for(int i = offset; i < offset+4; ++i) {
			num <<= 8;
			num |= (((byte)b[i]) & 0xff);
		}
		return num;
	}
	
	public static int bytesToLittleEndian32(byte[] b,int offset) {
		return (((int)b[offset] & 0xff)      ) |
		           (((int)b[offset+1] & 0xff) <<  8) |
		           (((int)b[offset+2] & 0xff) << 16) |
		           (((int)b[offset+3] & 0xff) << 24);
	}
	
	public static void LittleEndian32ToBytes(byte[] b,int offset, int value) {
		b[offset+0] = (byte)((value      ) & 0xFF);
		b[offset+1] = (byte)((value >>  8) & 0xFF);
		b[offset+2] = (byte)((value >> 16) & 0xFF);
		b[offset+3] = (byte)((value >> 24) & 0xFF);
	}
	
	public static String toHexString(byte[] byteBuffer, int length) {
		StringBuffer outputBuf = new StringBuffer(length * 4);

		for (int i = 0; i < length; i++) {
			String hexVal = Integer.toHexString(byteBuffer[i] & 0xff);

			if (hexVal.length() == 1) {
				hexVal = "0" + hexVal; //$NON-NLS-1$
			}

			outputBuf.append(hexVal); //$NON-NLS-1$
		}
		return outputBuf.toString();
	}
	
	public static String getClassPath() {
		return Class.class.getResource("/").getPath();
	}
	/**
	 * 获取运行类所在根目录下的指定文件或目录的路径
	 * @param name
	 * @return
	 */
	public static String getClassPath(String name) {
		String ret = "";
		URL url = Thread.currentThread().getContextClassLoader().getResource(name);
		if (url != null) {
			ret = url.getPath();
		}
		return ret;
	}


	/**
	 * String转为int型
	 * 如果出错 则为0
	 * @param value
	 * @return
	 */
	public static int intValue(Object value) {
		if (value == null) {
			return 0;
		}
		if (value instanceof Integer) {
			return (int) value;
		}
		if (value instanceof Long) {
			long tempValue = (long) value;
			return tempValue > Integer.MAX_VALUE ? Integer.MAX_VALUE : (int) tempValue;
		}
		if (value instanceof Number) {
			return ((Number) value).intValue();
		}
		String tempValue = value.toString();
		if (StringUtils.isNotEmpty(tempValue) && NumberUtils.isNumber(tempValue)) {
			try {
				return Double.valueOf(tempValue).intValue();
			} catch (NumberFormatException e) {
				Log.temp.error("{}", e);
			}
		}
		return 0;
	}


	/**
	 * String转为long型
	 * 如果出错 则为0
	 * @param value
	 * @return
	 */
	public static long longValue(Object value) {
		if (value == null) {
			return 0L;
		}

		if (value instanceof Number) {
			return ((Number) value).longValue();
		}

		if (value instanceof String) {
			if(((String) value).isEmpty()){
				return 0L;
			}
			String temp = (String) value;
			if (StringUtils.isNotEmpty(temp) && NumberUtils.isNumber(temp)) {
				try {
					return new BigDecimal(temp).longValue();
				} catch (NumberFormatException e) {
					Log.temp.error("异常已捕获，返回0正常执行，异常信息打印：{}", e);
					return 0L;
				}
			}
		}

		try {
			return Long.parseLong(value.toString());
		} catch (Exception e) {
			Log.temp.error("{}", e);
			return 0L;
		}
	}


	/**
	 * String转为double型
	 * 如果出错 则为0.0
	 * @param value
	 * @return
	 */
	public static double doubleValue(String value) {
		if(StringUtils.isNotEmpty(value) && NumberUtils.isNumber(value))
			return Double.valueOf(value);
		else
			return 0.0D;
	}
	public static double doubleValue(Double value){
		if(null == value)
			return 0.0D;
		else
			return value;
	}

	/**
	 * String转为float型
	 * 如果出错 则为0.0
	 * @param value
	 * @return
	 */
	public static float floatValue(Object value) {
		if (value == null) {
			return 0.0f;
		}

		if (value instanceof Number) {
			return ((Number) value).floatValue();
		}

		if (value instanceof Boolean) {
			return ((Boolean) value) ? 1.0f : 0.0f;
		}

		if (value instanceof String) {
			try {
				if(((String) value).isEmpty()){
					return 0.0f;
				}
				return Float.parseFloat((String) value);
			} catch (NumberFormatException e) {
				// 记录日志或进行其他错误处理
				System.err.println("Invalid float string: " + value);
			}
		}

		return 0.0f;
	}


	/**
	 * String转为boolean型
	 * 如果出错 则为false
	 * @param value
	 * @return
	 */
	public static boolean booleanValue(String value) {
		if("true".equalsIgnoreCase(value) && value != null)
			return true;
		else
			return false;
	}
	
	/**
	 * 从字符串转为JSONObject，主要目的是包装一下异常处理
	 * @param str 
	 * @return 正常返回对象，否则返回长度为0的JSONArray
	 */
	public static JSONObject str2JSONObject(String str) {
		if(StringUtils.isEmpty(str)) {
			str = "{}";
		}
		return JSON.parseObject(str);
	}
	
	/**
	 * 把两个数组组成一个匹配的Json 前面是属性，后面是数值
	 * @param str1
	 * @param str2
	 * @return
	 */
	public static String toJOSNString(String []str1, String []str2) {
		Map<String, String> tempMap = new HashMap<String, String>();
		if(str1 != null && str2 != null && str1.length == str2.length) {
			for(int i = 0 ; i < str1.length ; i++) {
				tempMap.put(str1[i], str2[i]);
			}
		}
		return toJSONString(tempMap);
	}
	
	/**
	 * 把两个数组组成一个匹配的Json 前面是属性，后面是数值
	 * @param str1
	 * @param str2
	 * @return
	 */
	public static String  toJOSNString(String []str1, int []str2) {
		Map<String, Integer> tempMap = new HashMap<String, Integer>();
		if(str1 != null && str2 != null && str1.length == str2.length) {
			for(int i = 0 ; i < str1.length ; i++) {
				tempMap.put(str1[i], str2[i]);
			}
		}
		return toJSONString(tempMap);
	}
	
	public static Map<String, Integer> arrToMap(String []str1, int []str2) {
		Map<String, Integer> tempMap = new HashMap<String, Integer>();
		if(str1 != null && str2 != null && str1.length == str2.length) {
			for(int i = 0 ; i < str1.length ; i++) {
				tempMap.put(str1[i], str2[i]);
			}
		}
		return tempMap;
	}
	
	public static String toJOSNStringWeight(String []str1, int []str2, double weight) {
		Map<String, Integer> tempMap = new HashMap<String, Integer>();
		if(str1 != null && str2 != null && str1.length == str2.length) {
			for(int i = 0 ; i < str1.length ; i++) {
				tempMap.put(str1[i], (int)(str2[i] + weight));
			}
		}
		return toJSONString(tempMap);
	}
	
	public static String toJOSNStringNag(String []str1, int []str2) {
		Map<String, Integer> tempMap = new HashMap<String, Integer>();
		if(str1 != null && str2 != null && str1.length == str2.length) {
			for(int i = 0 ; i < str1.length ; i++) {
				tempMap.put(str1[i], -str2[i]);
			}
		}
		return toJSONString(tempMap);
	}
	
	/**
	 * 把两个数组组成一个匹配的Json 前面是属性，后面是数值
	 * @param str1
	 * @param str2
	 * @return
	 */
	public static String toJOSNString(String []str1, float []str2) {
		Map<String, Float> tempMap = new HashMap<String, Float>();
		if(str1 != null && str2 != null && str1.length == str2.length) {
			for(int i = 0 ; i < str1.length ; i++) {
				tempMap.put(str1[i], str2[i]);
			}
		}
		return toJSONString(tempMap);
	}
	
	/**
	 * 保留一位小数点
	 * 
	 * @param value 如 100/3.0F
	 * @return 返回33.3F
	 */
	public static float round(float value) {
		// return Math.round(value * 10.0D) / 10.0F;
		DecimalFormat df = new DecimalFormat("#.0");
		df.setRoundingMode(RoundingMode.HALF_UP);
		return NumberUtils.toFloat(df.format(value));
	}

	/**
	 * 保留一位小数点
	 * 
	 * @param value 如100/3.0D
	 * @return 返回33.3D
	 */
	public static double round(double value) {
		// return Math.round(value * 10.0D) / 10.0D;
		DecimalFormat df = new DecimalFormat("#.0");
		df.setRoundingMode(RoundingMode.HALF_UP);
		return NumberUtils.toDouble(df.format(value));
	}
	
    /**
	 * 保留两位小数点
	 * 
	 * @param value 如100/3.0F
	 * @return 返回33.33F
	 */
	public static float round2(float value) {
		// return Math.round(value * 100.0D) / 100.0F;
		DecimalFormat df = new DecimalFormat("#.00");
		df.setRoundingMode(RoundingMode.HALF_UP);
		return NumberUtils.toFloat(df.format(value));
	}

	/**
	 * 保留两位小数点
	 * 
	 * @param value 如100/3.0D
	 * @return 返回33.33D
	 */
	public static double round2(double value) {
		// return Math.round(value * 100.0D) / 100.0D;
		DecimalFormat df = new DecimalFormat("#.00");
		df.setRoundingMode(RoundingMode.HALF_UP);
		return NumberUtils.toDouble(df.format(value));
	}
	
	/**
	 * 获取JSON字符串里的指定KEY的值
	 * @param strJSON
	 * @param key
	 * @param defaultValue
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static <T> T getJSONValue(String strJSON, String key, T defaultValue) {
		T value = defaultValue;
		JSONObject jo = Utils.toJSONObject(strJSON);
		if (jo != null && jo.containsKey(key)) {
			value = (T) jo.get(key);
		}
		return value;
	}
	
	/**
	 * 获取JSONObject里的指定KEY的值
	 * @param jo
	 * @param key
	 * @param defaultValue
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static <T> T getJSONValue(JSONObject jo, String key, T defaultValue) {
		T value = defaultValue;
		if (jo.containsKey(key)) {
			value = (T) jo.get(key);
		}
		return value;
	}

	/**
	 * 获取JSON字符串中指定key的value
	 * 
	 * @param json
	 * @param key
	 */
	public static String getJSONValueStr(String json, int key) {
		return getJSONValueStr(json, String.valueOf(key));
	}

	/**
	 * 获取JSON字符串中指定key的value
	 * 
	 * @param json
	 * @param key
	 */
	public static String getJSONValueStr(String json, String key) {
		String ret = null;
		JSONObject jo = toJSONObject(json);
		if (jo != null && !jo.isEmpty() && jo.containsKey(key)) {
			ret = jo.getString(key);
		}
		return ret;
	}

	/**
	 * 获取JSON字符串中指定key的value
	 * 
	 * @param json
	 * @param key
	 */
	public static Integer getJSONValueInt(String json, int key) {
		return getJSONValueInt(json, String.valueOf(key));
	}

	/**
	 * 获取JSON字符串中指定key的value
	 * 
	 * @param json
	 * @param key
	 */
	public static Integer getJSONValueInt(String json, String key) {
		Integer ret = null;
		JSONObject jo = toJSONObject(json);
		if (jo != null && !jo.isEmpty() && jo.containsKey(key)) {
			ret = jo.getInteger(key);
		}
		return ret;
	}

	/**
	 * 获取JSON字符串中指定key的value
	 * 
	 * @param json
	 * @param key
	 */
	public static Long getJSONValueLong(String json, long key) {
		return getJSONValueLong(json, String.valueOf(key));
	}

	/**
	 * 获取JSON字符串中指定key的value
	 * 
	 * @param json
	 * @param key
	 */
	public static Long getJSONValueLong(String json, String key) {
		Long ret = null;
		JSONObject jo = toJSONObject(json);
		if (jo != null && !jo.isEmpty() && jo.containsKey(key)) {
			ret = jo.getLong(key);
		}
		return ret;
	}

	/**
	 * 获取JSON字符串中指定key的value
	 * 
	 * @param json
	 * @param key
	 */
	public static Boolean getJSONValueBoolean(String json, String key) {
		Boolean ret = null;
		JSONObject jo = toJSONObject(json);
		if (jo != null && !jo.isEmpty() && jo.containsKey(key)) {
			ret = jo.getBoolean(key);
		}
		return ret;
	}

	/**
	 * 设置JSON字符串中指定key的value
	 * 
	 * @param json
	 * @param key
	 * @param newValue
	 */
	public static String setJSONValue(String json, int key, Object newValue) {
		return setJSONValue(json, String.valueOf(key), newValue);
	}

	/**
	 * 设置JSON字符串中指定key的value
	 * 
	 * @param json
	 * @param key
	 * @param newValue
	 */
	public static String setJSONValue(String json, String key, Object newValue) {
		String ret = json;
		JSONObject jo = toJSONObject(json);
		if (jo != null) {
			jo.put(key, newValue);
			ret = jo.toJSONString();
		}
		return ret;
	}

	/**
	 * 设置JSON字符串中指定key的value
	 * 
	 * @param json
	 * @param key
	 * @param newValue
	 */
	public static String setJSONValue(String json, String[] key, Object[] newValue) {
		String ret = json;
		if (key != null && newValue != null && key.length == newValue.length) {
			JSONObject jo = toJSONObject(json);
			if (jo != null) {
				for (int i = 0; i < key.length; i++) {
					jo.put(key[i], newValue[i]);
				}
				ret = jo.toJSONString();
			}
		}
		return ret;
	}

	/**
	 * 更新JSON字符串中指定key的value
	 * 
	 * @param json
	 * @param key
	 * @param newValue
	 */
	public static String updateJSONValue(String json, int key, Object newValue) {
		return updateJSONValue(json, String.valueOf(key), newValue);
	}

	/**
	 * 更新JSON字符串中指定key的value
	 * 
	 * @param json
	 * @param key
	 * @param newValue
	 */
	public static String updateJSONValue(String json, String key, Object newValue) {
		String ret = json;
		JSONObject jo = toJSONObject(json);
		if (jo != null && jo.containsKey(key)) {
			jo.put(key, newValue);
			ret = jo.toJSONString();
		}
		return ret;
	}

	/**
	 * 更新JSON字符串中指定key的value
	 * 
	 * @param json
	 * @param key
	 * @param newValue
	 */
	public static String updateJSONValue(String json, String[] key, Object[] newValue) {
		String ret = json;
		if (key != null && newValue != null && key.length == newValue.length) {
			JSONObject jo = toJSONObject(json);
			if (jo != null) {
				for (int i = 0; i < key.length; i++) {
					if (jo.containsKey(key[i])) {
						jo.put(key[i], newValue[i]);
					}
				}
				ret = jo.toJSONString();
			}
		}
		return ret;
	}

	/**
	 * 加入JSON字符串中指定key的value
	 * 
	 * @param json
	 * @param key
	 * @param newValue
	 */
	public static String addJSONValue(String json, int key, Object newValue) {
		return addJSONValue(json, String.valueOf(key), newValue);
	}

	/**
	 * 加入JSON字符串中指定key的value
	 * 
	 * @param json
	 * @param key
	 * @param newValue
	 */
	public static String addJSONValue(String json, String key, Object newValue) {
		String ret = json;
		JSONObject jo = toJSONObject(json);
		if (jo != null) {
			jo.put(key, newValue);
			ret = jo.toJSONString();
		}
		return ret;
	}

	/**
	 * 加入JSON字符串中指定key的value
	 * 
	 * @param json
	 * @param key
	 * @param newValue
	 */
	public static String addJSONValue(String json, String[] key, Object[] newValue) {
		String ret = json;
		if (key != null && newValue != null && key.length == newValue.length) {
			JSONObject jo = toJSONObject(json);
			if (jo != null) {
				for (int i = 0; i < key.length; i++) {
					jo.put(key[i], newValue[i]);
				}
				ret = jo.toJSONString();
			}
		}
		return ret;
	}

	/**
	 * 加入JSON对象指定key的非零value
	 *
	 * @param jsonObject
	 * @param key
	 * @param value
	 */
	public static <T extends Number> void addToJsonIfNotZero(JSONObject jsonObject, String key, T value) {
		if (value.longValue() != 0L) {
			jsonObject.put(key, value);
		}
	}

	/**
	 * (自增n,值为int或long)JSON字符串中指定key的value
	 * 
	 * @param json
	 * @param key
	 * @param numAdd
	 */
	public static String plusJSONValue(String json, int key, int numAdd) {
		return plusJSONValue(json, String.valueOf(key), numAdd);
	}

	/**
	 * (自增1,值为int或long)JSON字符串中指定key的value
	 * 
	 * @param json
	 * @param key
	 */
	public static String plusJSONValue(String json, int key) {
		return plusJSONValue(json, String.valueOf(key), 1);
	}

	/**
	 * (自增1,值为int或long)JSON字符串中指定key的value
	 * 
	 * @param json
	 * @param key
	 * @param numAdd
	 */
	public static String plusJSONValue(String json, String key, int numAdd) {
		String ret = json;
		JSONObject jo = new JSONObject();
		if (isJSONString(json)) {
			jo = toJSONObject(json);
			Object num = jo.get(key);
			if (num != null) {
				if (num instanceof Integer) {
					jo.put(key, (Integer) num + numAdd);
				} else if (num instanceof Long) {
					jo.put(key, (Long) num + numAdd);
				}
			} else {
				jo.put(key, numAdd);
			}
		} else {
			jo.put(key, numAdd);
		}
		ret = jo.toJSONString();
		return ret;
	}

	/**
	 * (自增1,值为int或long)JSON字符串中指定key的value
	 * 
	 * @param json
	 * @param key
	 */
	public static String plusJSONValue(String json, String[] key) {
		String ret = json;
		JSONObject jo = new JSONObject();
		if (isJSONString(json)) {
			jo = toJSONObject(json);
			for (int i = 0; i < key.length; i++) {
				Object num = jo.get(key[i]);
				if (num != null) {
					if (num instanceof Integer) {
						jo.put(key[i], (Integer) num + 1);
					} else if (num instanceof Long) {
						jo.put(key[i], (Long) num + 1);
					}
				} else {
					jo.put(key[i], 1);
				}
			}
		} else {
			for (int i = 0; i < key.length; i++) {
				jo.put(key[i], 1);
			}
		}
		ret = jo.toJSONString();
		return ret;
	}

	/**
	 * Map转JSON字符串，例如:{"1":1,"2":2}
	 */
	public static String mapIntIntToJSON(Map<Integer, Integer> map) {
		JSONObject jo = new JSONObject(true);
		for (Entry<Integer, Integer> entry : map.entrySet()) {
			Integer key = entry.getKey();
			Integer value = entry.getValue();
			if (key != null && value != null) {
				jo.put(key.toString(), value);
			}
		}
		return jo.toJSONString();
	}

	public static String mapIntIntToJSON(int[][] arr) {
		JSONObject jo = new JSONObject(true);
		if(arr == null || arr.length == 0){
			return jo.toJSONString();
		}
		for(int i = 0; i < arr.length; i++){
			int[] intArr = arr[i];
			for(int m = 0; m < intArr.length; m+=2){
				int key = intArr[m];
				int value = intArr[m + 1];
				jo.put(String.valueOf(key), value);
			}
		}
		return jo.toJSONString();
	}

	public static int getServerIdTo(int serverId){
		 return serverId % Integer.parseInt(Config.GAME_SERVER_PREFIX);
	}


	/**
	 * Map转JSON字符串，例如:{1:"1",2:"2"}
	 */
	public static String mapIntStrToJSON(Map<Integer, String> map) {
		JSONObject jo = new JSONObject(true);
		for (Entry<Integer, String> entry : map.entrySet()) {
			Integer key = entry.getKey();
			String value = entry.getValue();
			if (key != null && value != null) {
				jo.put(key.toString(), value);
			}
		}
		return jo.toJSONString();
	}

	/**
	 * Map转JSON字符串，例如:{"1":[1,2],"2":[2,3]}
	 */
	public static String mapIntListIntToJSON(Map<Integer, List<Integer>> map) {
		JSONObject jo = new JSONObject(true);
		for (Entry<Integer, List<Integer>> entry : map.entrySet()) {
			JSONArray joArr = new JSONArray();

			Integer key = entry.getKey();
			for (Integer val : entry.getValue()) {
				joArr.add(val);
			}
			jo.put(key.toString(), joArr);
		}
		return jo.toJSONString();
	}

	public static String mapLongListIntToJSON(Map<Long, List<Integer>> map) {
		JSONObject jo = new JSONObject(true);
		for (Entry<Long, List<Integer>> entry : map.entrySet()) {
			JSONArray joArr = new JSONArray();

			Long key = entry.getKey();
			for (Integer val : entry.getValue()) {
				joArr.add(val);
			}
			jo.put(key.toString(), joArr);
		}
		return jo.toJSONString();
	}

	/**
	 * Map转JSON字符串，例如:{"1":[1,2],"2":[2,3]}
	 */
	public static String mapIntListLongToJSON(Map<Integer, List<Long>> map) {
		JSONObject jo = new JSONObject(true);
		for (Entry<Integer, List<Long>> entry : map.entrySet()) {
			JSONArray joArr = new JSONArray();

			Integer key = entry.getKey();
			for (Long val : entry.getValue()) {
				joArr.add(val);
			}
			jo.put(key.toString(), joArr);
		}
		return jo.toJSONString();
	}

	public static String mapIntSetLongToJSON(Map<Integer, Set<Long>> map) {
		JSONObject jo = new JSONObject(true);
		for (Entry<Integer, Set<Long>> entry : map.entrySet()) {
			JSONArray joArr = new JSONArray();
			Integer key = entry.getKey();
			for (Long val : entry.getValue()) {
				joArr.add(val);
			}
			jo.put(key.toString(), joArr);
		}
		return jo.toJSONString();
	}



	/**
	 * 双重Map转JSON字符串，
	 */
	public static String mapIntMapLongToJSON(Map<Integer, Map<Long, Integer>> map) {
		JSONObject jo = new JSONObject(true);
		for (Entry<Integer, Map<Long, Integer>> entry : map.entrySet()) {
			Integer key = entry.getKey();
			jo.put(key.toString(), mapLongIntToJSON(entry.getValue()));
		}
		return jo.toJSONString();
	}


	public static String mapIntLongMapIntIntToJSON(Map<Integer,Map<Long, Map<Integer, Integer>>> map) {
		JSONObject jo = new JSONObject(true);
		for (Entry<Integer,Map<Long, Map<Integer, Integer>>> entry : map.entrySet()) {
			Integer key = entry.getKey();
			jo.put(key.toString(), mapLongIntIntToJSON(entry.getValue()));
		}
		return jo.toJSONString();
	}

	public static String mapLongIntIntToJSON(Map<Long, Map<Integer, Integer>> map) {
		JSONObject jo = new JSONObject(true);
		for (Entry<Long, Map<Integer, Integer>> entry : map.entrySet()) {
			Long key = entry.getKey();
			Map<Integer, Integer> value = entry.getValue();
			if (key != null && value != null) {
				jo.put(key.toString(), mapIntIntToJSON(value));
			}
		}
		return jo.toJSONString();
	}

	public static String mapIntMapIntLongToJSON(Map<Integer, Map<Integer, Long>> map) {
		JSONObject jo = new JSONObject(true);
		for (Entry<Integer, Map<Integer, Long>> entry : map.entrySet()) {
			Integer key = entry.getKey();
			jo.put(key.toString(), mapIntLongToJSON(entry.getValue()));
		}
		return jo.toJSONString();
	}

	public static String mapIntMapLongLongToJSON(Map<Integer, Map<Long, Long>> map) {
		JSONObject jo = new JSONObject(true);
		for (Entry<Integer, Map<Long, Long>> entry : map.entrySet()) {
			Integer key = entry.getKey();
			jo.put(key.toString(), mapLongLongToJSON(entry.getValue()));
		}
		return jo.toJSONString();
	}

	/**
	 * 双重Map转JSON字符串，
	 */
	public static String mapLongMapIntLongToJSON(Map<Long, Map<Integer, Long>> map) {
		JSONObject jo = new JSONObject(true);
		for (Entry<Long, Map<Integer, Long>> entry : map.entrySet()) {
			Long key = entry.getKey();
			jo.put(key.toString(), mapIntLongToJSON(entry.getValue()));
		}
		return jo.toJSONString();
	}


	/**
	 * Map转JSON字符串，例如:{"1000000000000001":1,"200000000000001":2}
	 */
	public static String mapLongIntToJSON(Map<Long, Integer> map) {
		JSONObject jo = new JSONObject(true);
		for (Entry<Long, Integer> entry : map.entrySet()) {
			Long key = entry.getKey();
			Integer value = entry.getValue();
			if (key != null && value != null) {
				jo.put(key.toString(), value);
			}
		}
		return jo.toJSONString();
	}

	/**
	 * Map转JSON字符串，例如:{"1000000000000001":"1000000000000001","200000000000001":"1000000000000001"}
	 */
	public static String mapLongLongToJSON(Map<Long, Long> map) {
		JSONObject jo = new JSONObject(true);
		for (Entry<Long, Long> entry : map.entrySet()) {
			Long key = entry.getKey();
			Long value = entry.getValue();
			if (key != null && value != null) {
				jo.put(key.toString(), value);
			}
		}
		return jo.toJSONString();
	}

	/**
	 * Map转JSON字符串，例如:{"1":1,"2":2}
	 */
	public static String mapIntLongToJSON(Map<Integer, Long> map) {
		JSONObject jo = new JSONObject(true);
		for (Entry<Integer, Long> entry : map.entrySet()) {
			Integer key = entry.getKey();
			Long value = entry.getValue();
			if (key != null && value != null) {
				jo.put(key.toString(), value);
			}
		}
		return jo.toJSONString();
	}

	/**
	 * Map转JSON字符串，例如:{"1":1,"2":2}
	 */
	public static String mapToJSON(Map<String, Object> map) {
		JSONObject jo = new JSONObject(true);
		jo.putAll(map);
		return jo.toJSONString();
	}

	public static Map<String, Object> jsonToMap(String str) {
		LinkedHashMap<String, Object> jsonMap = new LinkedHashMap<>();
		if (isJSONString(str) && !isEmptyJSONString(str)) {
			jsonMap = JSON.parseObject(str, new TypeReference<LinkedHashMap<String, Object>>() {
			});
		}
		return jsonMap;
	}
	public static String mapStrToJSON(Map<String, String> map) {
		JSONObject jo = new JSONObject(true);
		jo.putAll(map);
		return jo.toJSONString();
	}

	public static Map<String, String> jsonToMapStr(String str) {
		LinkedHashMap<String, String> jsonMap = new LinkedHashMap<>();
		if (isJSONString(str) && !isEmptyJSONString(str)) {
			jsonMap = JSON.parseObject(str, new TypeReference<LinkedHashMap<String, String>>() {
			});
		}
		return jsonMap;
	}

	/**
	 * Map转JSON字符串，例如:{"1":1,"2":2}
	 */
	public static String mapStringIntToJSON(Map<String, Integer> map) {
		JSONObject jo = new JSONObject(true);
		jo.putAll(map);
		return jo.toJSONString();
	}

	/**
	 * JSON字符串转Map，例如:{"1":1,"2":2}
	 */
	public static Map<Integer, Integer> jsonToMapIntInt(String strJSON) {
		LinkedHashMap<Integer, Integer> jsonMap = new LinkedHashMap<>();
		if (isJSONString(strJSON) && !isEmptyJSONString(strJSON)) {
			jsonMap = JSON.parseObject(strJSON, new TypeReference<LinkedHashMap<Integer, Integer>>() {
			});
		}
		return jsonMap;
	}

	/**
	 * JSON字符串转Map，例如:{"1":1,"2":2}
	 */
	public static Map<Integer, List<Integer>> jsonToMapIntListInt(String strJSON) {
		LinkedHashMap<Integer, List<Integer>> jsonMap = new LinkedHashMap<>();
		if (isJSONString(strJSON) && !isEmptyJSONString(strJSON)) {
			jsonMap = JSON.parseObject(strJSON, new TypeReference<LinkedHashMap<Integer, List<Integer>>>() {
			});
		}
		return jsonMap;
	}

	public static Map<Long, List<Integer>> jsonToMapLongListInt(String strJSON) {
		LinkedHashMap<Long, List<Integer>> jsonMap = new LinkedHashMap<>();
		if (isJSONString(strJSON) && !isEmptyJSONString(strJSON)) {
			jsonMap = JSON.parseObject(strJSON, new TypeReference<LinkedHashMap<Long, List<Integer>>>() {
			});
		}
		return jsonMap;
	}

	/**
	 *  JSON字符串转Map，例如:{"1":List</1>,"2":List</1>}
	 * <AUTHOR>
	 * @Date 2021/8/3
	 * @Param
	 */
	public static Map<Integer, List<Long>> jsonToMapIntListLong(String strJSON) {
		LinkedHashMap<Integer, List<Long>> jsonMap = new LinkedHashMap<>();
		if (isJSONString(strJSON) && !isEmptyJSONString(strJSON)) {
			jsonMap = JSON.parseObject(strJSON, new TypeReference<LinkedHashMap<Integer, List<Long>>>() {
			});
		}
		return jsonMap;
	}

	public static Map<Integer, Map<Long, Integer>> jsonToIntMapLongInt(String strJSON) {
		LinkedHashMap<Integer, Map<Long, Integer>> jsonMap = new LinkedHashMap<>();
		if (isJSONString(strJSON) && !isEmptyJSONString(strJSON)) {
			Map<Integer, String> intStrMap = Utils.jsonToMapIntString(strJSON);
			for(Map.Entry<Integer, String> entry : intStrMap.entrySet()){
				jsonMap.put(entry.getKey(), Utils.jsonToMapLongInt(entry.getValue()));
			}
		}
		return jsonMap;
	}

	public static Map<Long, Map<Integer, Integer>> jsonToLongMapIntInt(String strJSON) {
		LinkedHashMap<Long, Map<Integer, Integer>> jsonMap = new LinkedHashMap<>();
		if (isJSONString(strJSON) && !isEmptyJSONString(strJSON)) {
			Map<Long, String> intStrMap = Utils.jsonToMapLongStr(strJSON);
			for(Map.Entry<Long, String> entry : intStrMap.entrySet()){
				jsonMap.put(entry.getKey(), Utils.jsonToMapIntInt(entry.getValue()));
			}
		}
		return jsonMap;
	}


	public static Map<Integer,Map<Long, Map<Integer, Integer>>> jsonToIntLongMapIntInt(String strJSON) {
		LinkedHashMap<Integer,Map<Long, Map<Integer, Integer>>> jsonMap = new LinkedHashMap<>();
		if (isJSONString(strJSON) && !isEmptyJSONString(strJSON)) {
			Map<Integer, String> intStrMap = Utils.jsonToMapIntString(strJSON);
			for(Map.Entry<Integer, String> entry : intStrMap.entrySet()){
				jsonMap.put(entry.getKey(), Utils.jsonToLongMapIntInt(entry.getValue()));
			}
		}
		return jsonMap;
	}

	public static Map<Integer, Map<Integer, Integer>> jsonToIntMapIntInt(String strJSON) {
		LinkedHashMap<Integer, Map<Integer, Integer>> jsonMap = new LinkedHashMap<>();
		if (isJSONString(strJSON) && !isEmptyJSONString(strJSON)) {
			Map<Integer, String> intStrMap = Utils.jsonToMapIntString(strJSON);
			for(Map.Entry<Integer, String> entry : intStrMap.entrySet()){
				jsonMap.put(entry.getKey(), Utils.jsonToMapIntInt(entry.getValue()));
			}
		}
		return jsonMap;
	}

	public static String mapIntMapIntIntToJSON(Map<Integer, Map<Integer, Integer>> map) {
		JSONObject jo = new JSONObject(true);
		for (Entry<Integer, Map<Integer, Integer>> entry : map.entrySet()) {
			Integer key = entry.getKey();
			jo.put(key.toString(), mapIntIntToJSON(entry.getValue()));
		}
		return jo.toJSONString();
	}

	public static Map<Long, Map<Integer, Long>> jsonToLongMapIntLong(String strJSON) {
		LinkedHashMap<Long, Map<Integer, Long>> jsonMap = new LinkedHashMap<>();
		if (isJSONString(strJSON) && !isEmptyJSONString(strJSON)) {
			Map<Long, String> longStrMap = Utils.jsonToMapLongStr(strJSON);
			for(Map.Entry<Long, String> entry : longStrMap.entrySet()){
				jsonMap.put(entry.getKey(), Utils.jsonToMapIntLong(entry.getValue()));
			}
		}
		return jsonMap;
	}

	public static<T> T jsonToMapTObject(String strJSON, TypeReference<T> type) {
		strJSON = (strJSON == null || "".equals(strJSON)) ? "{}" : strJSON;
		return (T) JSON.parseObject(strJSON, type);
	}

	public static Map<Integer, Map<Integer, Long>> jsonToIntegerMapIntLong(String strJSON) {
		LinkedHashMap<Integer, Map<Integer, Long>> jsonMap = new LinkedHashMap<>();
		if (isJSONString(strJSON) && !isEmptyJSONString(strJSON)) {
			Map<Integer, String> longStrMap = Utils.jsonToMapIntString(strJSON);
			for(Map.Entry<Integer, String> entry : longStrMap.entrySet()){
				jsonMap.put(entry.getKey(), Utils.jsonToMapIntLong(entry.getValue()));
			}
		}
		return jsonMap;
	}




	public static Map<Integer, Map<Long, Long>> jsonToIntMapLongLong(String strJSON) {
		LinkedHashMap<Integer, Map<Long, Long>> jsonMap = new LinkedHashMap<>();
		if (isJSONString(strJSON) && !isEmptyJSONString(strJSON)) {
			Map<Integer, String> longStrMap = Utils.jsonToMapIntString(strJSON);
			for(Map.Entry<Integer, String> entry : longStrMap.entrySet()){
				jsonMap.put(entry.getKey(), Utils.jsonToMapLongLong(entry.getValue()));
			}
		}
		return jsonMap;
	}

	public static Map<Long, String> jsonToMapLongStr(String strJSON) {
		LinkedHashMap<Long, String> jsonMap = new LinkedHashMap<>();
		if (isJSONString(strJSON) && !isEmptyJSONString(strJSON)) {
			jsonMap = JSON.parseObject(strJSON, new TypeReference<LinkedHashMap<Long, String>>() {
			});
		}
		return jsonMap;
	}

	/**
	 * JSON字符串转Map，例如:{"1000000000001":1,"2000000000000001":2}
	 */
	public static Map<Long, Integer> jsonToMapLongInt(String strJSON) {
		LinkedHashMap<Long, Integer> jsonMap = new LinkedHashMap<>();
		if (isJSONString(strJSON) && !isEmptyJSONString(strJSON)) {
			jsonMap = JSON.parseObject(strJSON, new TypeReference<LinkedHashMap<Long, Integer>>() {
			});
		}
		return jsonMap;
	}

	/**
	 * JSON字符串转Map，例如:{"1000000000001":"1000000000001","2000000000000001":"1000000000001"}
	 */
	public static Map<Long, Long> jsonToMapLongLong(String strJSON) {
		LinkedHashMap<Long, Long> jsonMap = new LinkedHashMap<>();
		if (isJSONString(strJSON) && !isEmptyJSONString(strJSON)) {
			jsonMap = JSON.parseObject(strJSON, new TypeReference<LinkedHashMap<Long, Long>>() {
			});
		}
		return jsonMap;
	}

	/**
	 * JSON字符串转Map，例如:{"1":1,"2":2}
	 */
	public static Map<Integer, Long> jsonToMapIntLong(String strJSON) {
		LinkedHashMap<Integer, Long> jsonMap = new LinkedHashMap<>();
		if (isJSONString(strJSON) && !isEmptyJSONString(strJSON)) {
			jsonMap = JSON.parseObject(strJSON, new TypeReference<LinkedHashMap<Integer, Long>>() {
			});
		}
		return jsonMap;
	}

	/**
	 * JSON字符串转Map，例如:{1:"1",2:"2"}
	 */
	public static Map<Integer, String> jsonToMapIntString(String strJSON) {
		LinkedHashMap<Integer, String> jsonMap = new LinkedHashMap<>();
		if (isJSONString(strJSON) && !isEmptyJSONString(strJSON)) {
			jsonMap = JSON.parseObject(strJSON, new TypeReference<LinkedHashMap<Integer, String>>() {
			});
		}
		return jsonMap;
	}

	/**
	 * JSON字符串转Map，例如:{"1":1,"2":2}
	 */
	public static Map<String, Integer> jsonToMapStringInt(String strJSON) {
		LinkedHashMap<String, Integer> jsonMap = new LinkedHashMap<>();
		if (isJSONString(strJSON) && !isEmptyJSONString(strJSON)) {
			jsonMap = JSON.parseObject(strJSON, new TypeReference<LinkedHashMap<String, Integer>>() {
			});
		}
		return jsonMap;
	}

	/**
	 * 将JSONObject转换成map对象
	 */
	public static Map<String, Object> jsonToMap(JSONObject obj) {
		Map<String, Object> map = new LinkedHashMap<String, Object>(obj.keySet().size());
		String key;
		Object value;
		for (Entry<String, Object> entry : obj.entrySet()) {
			key = entry.getKey();
			value = entry.getValue();
			if (value instanceof JSONArray) {
				map.put(key, jsonToList((JSONArray) value));
			} else if (value instanceof JSONObject) {
				map.put(key, jsonToMap((JSONObject) value));
			} else {
				map.put(key, value);
			}
		}
		return map;
	}

	/**
	 * 将JSONObject转换成map对象
	 */
	public static Map<String, String> jsonToMapStrStr(JSONObject obj) {
		Map<String, String> map = new LinkedHashMap<String, String>(obj.keySet().size());
		String key;
		Object value;
		for (Entry<String, Object> entry : obj.entrySet()) {
			key = entry.getKey();
			value = entry.getValue();
			map.put(key, String.valueOf(value));
		}
		return map;
	}

	/**
	 * 将JSONArray对象转换成list集合
	 */
	public static List<Object> jsonToList(JSONArray jsonArr) {
		List<Object> list = new ArrayList<Object>();
		for (Object obj : jsonArr) {
			if (obj instanceof JSONArray) {
				list.add(jsonToList((JSONArray) obj));
			} else if (obj instanceof JSONObject) {
				list.add(jsonToMap((JSONObject) obj));
			} else {
				list.add(obj);
			}
		}
		return list;
	}

	/**
	 * 从字符串转为JSONArray，主要目的是包装一下空值处理
	 * 
	 * @param str
	 * @return 正常返回对象，否则返回长度为0的JSONArray
	 */
	public static JSONArray toJSONArray(String str) {
		if (StringUtils.isBlank(str) || !isJSONString(str) || str.equals("{}")) {
			str = "[]";
		}
		// return JSON.parseArray(str);// 无序解析
		return (JSONArray) JSON.parse(str, Feature.OrderedField);// 有序解析
	}

	/**
	 * 从字符串转为JSONObject，主要目的是包装一下空值处理
	 * 
	 * @param str
	 * @return 正常返回对象，否则返回空的JSONObject
	 */
	public static JSONObject toJSONObject(String str) {
		if (StringUtils.isBlank(str) || !isJSONString(str) || str.equals("[]")) {
			str = "{}";
		}
		// return JSON.parseObject(str);// 无序解析
		return JSON.parseObject(str, Feature.OrderedField);// 有序解析
	}

	/**
	 * 将对象转化为JSON字符串
	 * 
	 * @param obj
	 * @return
	 */
	public static String toJSONString(Object obj) {
		return JSON.toJSONString(obj, SerializerFeature.DisableCircularReferenceDetect);
	}

	/**
	 * 将对象转化为JSON字符串
	 * 如果有key对应的value为null则设置value为 ”“ 空字符串
	 *
	 * @param obj
	 * @return
	 */
	public static String toJSONStringIfJSONArrayValueNull(JSONArray obj) {
		//TODO 等东升解决了 dType 的问题再打开
		for(int i = 0; i < obj.size(); i++){
			JSONObject jo = obj.getJSONObject(i);
			for(String key : jo.keySet()){
				jo.putIfAbsent(key, "");
			}
		}
		return JSON.toJSONString(obj, SerializerFeature.DisableCircularReferenceDetect);
	}


	/**
	 * 把两个数组组成一个有序的Json={KEY:VALUE,...}
	 * 
	 * @param keys
	 * @param values
	 * @return
	 */
	public static String toJSONString(String[] keys, String[] values) {
		Map<String, String> tempMap = new LinkedHashMap<String, String>();
		if (keys != null && values != null && keys.length == values.length) {
			for (int i = 0; i < keys.length; i++) {
				tempMap.put(keys[i], values[i]);
			}
		}
		return toJSONString(tempMap);
	}

	/**
	 * 把两个数组组成一个有序的Json={KEY:VALUE,...}
	 * 
	 * @param keys
	 * @param values
	 * @return
	 */
	public static String toJSONString(String[] keys, int[] values) {
		Map<String, Integer> tempMap = new LinkedHashMap<String, Integer>();
		if (keys != null && values != null && keys.length == values.length) {
			for (int i = 0; i < keys.length; i++) {
				tempMap.put(keys[i], values[i]);
			}
		}
		return toJSONString(tempMap);
	}

	/**
	 * 把两个数组组成一个有序的Json={KEY:VALUE,...}
	 * 
	 * @param keys
	 * @param values
	 * @return
	 */
	public static String toJSONString(List<Integer> keys, List<Integer> values) {
		Map<Integer, Integer> tempMap = new LinkedHashMap<Integer, Integer>();
		if (keys != null && values != null && keys.size() == values.size()) {
			for (int i = 0; i < keys.size(); i++) {
				tempMap.put(keys.get(i), values.get(i));
			}
		}
		return toJSONString(tempMap);
	}

	/**
	 * 把两个数组组成一个有序的Json={KEY:VALUE,...}
	 * 
	 * @param keys
	 * @param values
	 * @return
	 */
	public static String toJSONString(int[] keys, int[] values) {
		Map<Integer, Integer> tempMap = new LinkedHashMap<Integer, Integer>();
		if (keys != null && values != null && keys.length == values.length) {
			for (int i = 0; i < keys.length; i++) {
				tempMap.put(keys[i], values[i]);
			}
		}
		return toJSONString(tempMap);
	}

	/**
	 * 把两个数组组成一个有序的Json={KEY:VALUE,...}
	 * 
	 * @param keys
	 * @param value
	 * @return
	 */
	public static String toJSONString(int[] keys, int value) {
		Map<Integer, Integer> tempMap = new LinkedHashMap<Integer, Integer>();
		if (keys != null) {
			for (int i = 0; i < keys.length; i++) {
				tempMap.put(keys[i], value);
			}
		}
		return toJSONString(tempMap);
	}

	/**
	 * 是否为JSON字符串
	 * 
	 * @param str
	 */
	public static boolean isJSONString(String str) {
		boolean ret = false;
		if (str != null && str.length() >= 2) {
			if (str.startsWith("{") && str.endsWith("}") || str.startsWith("[") && str.endsWith("]")
					|| str.startsWith("[{") && str.endsWith("}]")) {
				ret = true;
			}
		}
		return ret;
	}

	/**
	 * 是否为空的JSON字符串
	 * 
	 * @param str
	 */
	public static boolean isEmptyJSONString(String str) {
		boolean ret = false;
		if (str != null) {
			str = str.replace(" ", "");
			if (str.length() == 2 && str.startsWith("{") && str.endsWith("}")
					|| str.length() == 2 && str.startsWith("[") && str.endsWith("]")
					|| str.length() == 4 && str.startsWith("[{") && str.endsWith("}]")) {
				ret = true;
			}
		}
		return ret;
	}

	/**
	 * 是否为空的JSON字符串(包含空串)
	 */
	public static boolean isNullOrEmptyJSONString(String str) {
		boolean ret = false;
		if (str != null) {
			str = str.replace(" ", "");
			if ("".equals(str) || (str.length() == 2 && str.startsWith("{") && str.endsWith("}"))
					|| (str.length() == 2 && str.startsWith("[") && str.endsWith("]"))
					|| (str.length() == 4 && str.startsWith("[{") && str.endsWith("}]"))) {
				ret = true;
			}
		}
		return ret;
	}

	/**
	 * 把两个数组组成一个匹配的Json 前面是属性，后面是数值
	 * 
	 * @param str1
	 * @param str2
	 * @return
	 */
	public static String toJSONString(String[] str1, float[] str2) {
		Map<String, Float> tempMap = new HashMap<String, Float>();
		if (str1 != null && str2 != null && str1.length == str2.length) {
			for (int i = 0; i < str1.length; i++) {
				tempMap.put(str1[i], str2[i]);
			}
		}
		return toJSONString(tempMap);
	}

	public static String toJSONStringWeight(String[] str1, int[] str2, double weight) {
		Map<String, Integer> tempMap = new HashMap<String, Integer>();
		if (str1 != null && str2 != null && str1.length == str2.length) {
			for (int i = 0; i < str1.length; i++) {
				tempMap.put(str1[i], (int) (str2[i] + weight));
			}
		}
		return toJSONString(tempMap);
	}

	public static String toJSONStringNag(String[] str1, int[] str2) {
		Map<String, Integer> tempMap = new HashMap<String, Integer>();
		if (str1 != null && str2 != null && str1.length == str2.length) {
			for (int i = 0; i < str1.length; i++) {
				tempMap.put(str1[i], -str2[i]);
			}
		}
		return toJSONString(tempMap);
	}

	public static Map<String, Integer> toMap(String[] str1, int[] str2) {
		Map<String, Integer> tempMap = new HashMap<String, Integer>();
		if (str1 != null && str2 != null && str1.length == str2.length) {
			for (int i = 0; i < str1.length; i++) {
				tempMap.put(str1[i], str2[i]);
			}
		}
		return tempMap;
	}

	public static Map<String, Float> toMapWeight(String[] str1, float[] str2, float weight) {
		Map<String, Float> tempMap = new HashMap<String, Float>();
		if (str1 != null && str2 != null && str1.length == str2.length) {
			for (int i = 0; i < str1.length; i++) {
				tempMap.put(str1[i], (str2[i] + weight));
			}
		}
		return tempMap;
	}

	/**
	 * 获取Param值 提供参数默认值
	 * 
	 * @param param
	 * @param key
	 * @param defaultValue
	 * @return
	 */
	public static <T> T getParamValue(Param param, String key, T defaultValue) {
		T result = param.get(key);
		if (result == null) {
			result = defaultValue;
		}
		return result;
	}

	/**
	 *
	 * 读取文件，返回一个String
	 * @return
	 */
	public static String readFile(String filePath) {
		BufferedReader reader = null;
		String contents = "";
		try(FileInputStream in = new FileInputStream(filePath)) {
			InputStreamReader inputStreamReader = new InputStreamReader(in, "UTF-8");
			reader = new BufferedReader(inputStreamReader);
			String line = null;
			while((line = reader.readLine()) != null){
				contents += line;
			}
			reader.close();
		} catch (IOException e) {
			Log.temp.error("===e={}", e);
		} finally {
			if (reader != null) {
				try {
					reader.close();
				} catch (IOException e) {
					Log.temp.error("===e={}", e);
				}
			}
		}

		return contents;
	}
    
	/**
	 * 读取配置文件
	 * @return
	 */
	public static Properties readProperties(String name) {
		File file = null;
		URL resource = Thread.currentThread().getContextClassLoader().getResource(name);
		if(resource!=null){
			String filePath = resource.getPath();
			file = new File(filePath);
		}else{
			file = new File(name);
		}
		try(FileInputStream in = new FileInputStream(file)) {
			Properties p = new Properties();
			p.load(new InputStreamReader(in));
			return p;
		} catch (Exception e) {
			throw new SysException(e);
		}
	}
	
	/**
	 * 全反射，不建议使用
	 * 获取对象的属性 
	 * 会先尝试利用getter方法获取 然后再直接访问字段属性
	 * 如果给定的属性不存在 会返回null
	 * @param obj
	 * @param fieldName
	 * @return
	 */
	@Deprecated
	@SuppressWarnings("unchecked")
	public static <T> T fieldRead(Object obj, String fieldName) {
		try {
			//返回值
			Object result = null;
			
			Class<? extends Object> clazz = obj.getClass();
			
			//先通过自省来获取字段的值(getter方法)
			boolean hasGetter = false;
			BeanInfo bi = Introspector.getBeanInfo(clazz);
			PropertyDescriptor[] pds = bi.getPropertyDescriptors();
			for(PropertyDescriptor p : pds) {
				if(!p.getName().equals(fieldName)) continue;
				
				result =  p.getReadMethod().invoke(obj);
				hasGetter = true;
			}
			
			//如果通过getter方法没找到 那么就尝试直接读取字段
			if(!hasGetter) {
				for(Field f : clazz.getFields()) {
					if(!f.getName().equals(fieldName)) continue;
					
					result = f.get(obj);
				}
			}
			
			return (T) result;
		} catch (Exception e) {
			throw new SysException(e);
		}
	}
	
	/**
	 * 获取对象的静态属性
	 * @param clazz
	 * @param fieldName
	 * @return
	 */
	@Deprecated
	@SuppressWarnings("unchecked")
	public static <T> T fieldRead(Class<?> clazz, String fieldName) {
		try {
			Field field = FieldUtils.getDeclaredField(clazz, fieldName);
			return (T) field.get(clazz);
		} catch (Exception e) {
			throw new SysException(e);
		}
	}
	
	/**
	 * 设置对象的属性  不建议使用
	 * 会先尝试利用setter方法修改 然后再直接修改字段属性
	 * 如果给定的属性不存在 会抛出异常
	 * @param obj
	 * @param fieldName
	 * @return
	 */
	@Deprecated
	public static void fieldWrite(Object obj, String fieldName, Object valueNew) {
		try {
			Class<? extends Object> clazz = obj.getClass();
			
			//先通过自省来设置字段的值(setter方法)
			boolean hasSetter = false;
			BeanInfo bi = Introspector.getBeanInfo(clazz);
			PropertyDescriptor[] pds = bi.getPropertyDescriptors();
			for(PropertyDescriptor p : pds) {
				if(!p.getName().equals(fieldName)) continue;
				
				//到这里的话 证明属性能找到（至少有对应的getter）但是没有找到setter
				//可能是setter方法不符合规范 比如非void有返回值等
				//这种情况使用反射再次尝试
				Method wm = p.getWriteMethod();
				if(wm == null) {
					String wmStr = "set" + StringUtils.capitalize(fieldName);
					for(Method m : clazz.getMethods()) {
						if(!m.getName().equals(wmStr)) continue;
						m.invoke(obj, valueNew);
					}
				} else {
					wm.invoke(obj, valueNew);
				}
				
				hasSetter = true;
			}
			
			//如果通过setter方法没找到 那么就尝试直接操作字段
			if(!hasSetter) {
				Field f = clazz.getField(fieldName);
				f.set(obj, valueNew);
			}
		} catch (Exception e) {
			throw new SysException(e);
		}
	}
	
	/**
	 * 通过反射执行函数
	 * @param obj
	 * @param method
	 * @param params
	 */
	@SuppressWarnings("unchecked")
	public static <T> T invokeMethod(Object obj, String method, Object...params) {
		try {
			return (T)MethodUtils.invokeMethod(obj, method, params);
		} catch (Exception e) {
			throw new SysException(e);
		}
	}
	
	/**
	 * 通过反射执行函数
	 * @param cls
	 * @param method
	 * @param params
	 */
	@SuppressWarnings("unchecked")
	public static <T> T invokeStaticMethod(Class<?> cls, String method, Object...params) {
		try {
			return (T)MethodUtils.invokeStaticMethod(cls, method, params);
		} catch (Exception e) {
			throw new SysException(e);
		}
	}
	
	/**
	 * 通过反射执行构造函数
	 * @param cls
	 * @param params
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static <T> T invokeConstructor(Class<?> cls, Object...params) {
		try {
			return (T) ConstructorUtils.invokeConstructor(cls, params);
		} catch (Exception e) {
			throw new SysException(e);
		}
	}
	
	/**
	 * 进行Get请求操作
	 * @return
	 */
	public static String httpGet(String url, Map<String, String> params) {
		try {
			//1 拼接地址
			StringBuilder urlSB = new StringBuilder(url);
			//1.1 有需要拼接的参数
			if(!params.isEmpty()) {
				urlSB.append("?");
			}
			
			//1.2 拼接参数
			for(Entry<String, String> entry : params.entrySet()) {
				Object value = entry.getValue();
				String v = (value == null) ? "" : URLEncoder.encode(entry.getValue(), "UTF-8");
				
				urlSB.append(entry.getKey()).append("=").append(v).append("&");
			}
			
			//1.3 最终地址
			String urlStrFinal = urlSB.toString();
			
			//1.4 去除末尾的&
			if(urlStrFinal.endsWith("&")) {
				urlStrFinal = urlStrFinal.substring(0, urlStrFinal.length() - 1);
			}
			
			//请求地址
			HttpGet get = new HttpGet(urlStrFinal);
			
			//准备环境
			try(CloseableHttpClient http = HttpClients.createDefault();
				CloseableHttpResponse response = http.execute(get);) {

				//返回内容
			    HttpEntity entity = response.getEntity();

			    //主体数据
			    InputStream in = entity.getContent();  
			    BufferedReader reader = new BufferedReader(new InputStreamReader(in));
			    //读取
			    StringBuilder sb = new StringBuilder();
			    String line = null;  
			    while ((line = reader.readLine()) != null) {  
			    	sb.append(line);
			    }
			    
			    reader.close();
			    return sb.toString();
			}
		} catch (Exception e) {
			throw new SysException(e);
		}
	}
	
	/**
	 * 通过HTTPS请求获取json格式的返回
	 * @param urlStr
	 * @param params
	 * @return
	 */
	public static String httpsGet(String urlStr, Map<String, String> params) {
		String html = null;
		try {
			if(params == null) params = new HashMap<String, String>();
			
			StringBuilder sb = new StringBuilder(urlStr);
			
			if(!params.isEmpty()) {
				sb.append("?");
			}
			
			for(Entry<String, String> entry : params.entrySet()) {
				Object value = entry.getValue();
				String v = (value == null) ? "" : URLEncoder.encode(entry.getValue(), "UTF-8");
				
				sb.append(entry.getKey()).append("=").append(v).append("&");
			}
			
			String urlStrFinal = sb.toString();
			
			//去除末尾的&
			if(urlStrFinal.endsWith("&")) {
				urlStrFinal = urlStrFinal.substring(0, urlStrFinal.length() - 1);
			}

			HttpClient httpclient = new DefaultHttpClient();
			httpclient = wrapHttpsClient(httpclient);
			HttpGet httpGet = new HttpGet(urlStrFinal);
			HttpResponse response = httpclient.execute(httpGet);
			HttpEntity httpEntity = response.getEntity();
			if (httpEntity != null) {
				html = EntityUtils.toString(httpEntity);
			}
			return html;
		} catch (Exception e) {
			throw new SysException("返回内容为:" + html,  e);
		}
	}
	
	/**
	 * 构造一个可以接受任意HTTPS协议的client
	 * @param base
	 * @return
	 */
	public static HttpClient wrapHttpsClient(HttpClient base) {
        try {
                SSLContext ctx = SSLContext.getInstance("TLS");
                X509TrustManager tm = new X509TrustManager() {
                        public void checkClientTrusted(X509Certificate[] xcs, String string) throws CertificateException {}
                        public void checkServerTrusted(X509Certificate[] xcs, String string) throws CertificateException {}
                        public X509Certificate[] getAcceptedIssuers() {
                                return null;
                        }
                };
                ctx.init(null, new TrustManager[]{tm}, null);
                SSLSocketFactory ssf = new SSLSocketFactory(ctx);
                ssf.setHostnameVerifier(SSLSocketFactory.ALLOW_ALL_HOSTNAME_VERIFIER);
                ClientConnectionManager ccm = base.getConnectionManager();
                SchemeRegistry sr = ccm.getSchemeRegistry();
                sr.register(new Scheme("https", ssf, 443));
                return new DefaultHttpClient(ccm, base.getParams());
        } catch (Exception ex) {
                ex.printStackTrace();
                return null;
        }
	}
	
	/**
	 * 进行Post请求操作
	 * @return
	 */
	public static String httpPost(String url, Map<String, String> params) {
		try {
			//参数
			List<NameValuePair> nvps = new ArrayList<NameValuePair>();
			for (Entry<String, String> entry : params.entrySet()) {
				Object key = entry.getKey();
				Object val = entry.getValue();
				String valStr = (val == null) ? "" : val.toString();
				
				nvps.add(new BasicNameValuePair(key.toString(), valStr));
			}
			
			//请求地址
			HttpPost post = new HttpPost(url);
			//设置参数
			post.setEntity(new UrlEncodedFormEntity(nvps, "UTF-8"));
			
			//准备环境
			try(CloseableHttpClient http = HttpClients.createDefault();
				CloseableHttpResponse response = http.execute(post);) {

				//返回内容
			    HttpEntity entity = response.getEntity();
			    
			    //主体数据
			    InputStream in = entity.getContent();  
			    BufferedReader reader = new BufferedReader(new InputStreamReader(in));
			    //读取
			    StringBuilder sb = new StringBuilder();
			    String line = null;  
			    while ((line = reader.readLine()) != null) {  
			    	sb.append(line);
			    }
			    
			    reader.close();
			    return sb.toString();
			}
		} catch (Exception e) {
			throw new SysException(e);
		}
	}
	
	/**
	 * 分隔字符串（去除[],{}的字符串）
	 * 
	 * @param str
	 * @param split
	 * @return
	 */
	public static String[] splitStr(String str, String split) {
		String[] ret = new String[] {};
		if (str != null && !str.isEmpty()) {
			if (str.contains("[")) {
				str = str.replaceAll("\\[", "");
			}
			if (str.contains("]")) {
				str = str.replaceAll("\\]", "");
			}
			if (str.contains("{")) {
				str = str.replaceAll("\\{", "");
			}
			if (str.contains("}")) {
				str = str.replaceAll("\\}", "");
			}
			if (str.equals("''")) {
				str = str.replaceAll("\\'\\'", "");
			}
			ret = str.split(split);
		}
		return ret;
	}
	
	/**
	 * 字符串转换成String[]
	 * 
	 * @param str
	 *            字符串
	 * @param split
	 *            分隔符（特殊符号必须加 转义符 \\）：如"\\|"
	 * @return
	 */
	public static String[] strToStrArray(String str, String split) {
		String[] ret = null;
		if (StringUtils.isNotBlank(str)) {
			ret = str.split(split);
		}
		return ret;
	}
	
	/**
	 * 字符串转换成double[]：分隔符=","
	 */
	public static double[] strToDoubleArray(String str) {
		if (str == null) {
			return null;
		}
		if (str.indexOf(",") > 0) {
			return strToDoubleArray(str, "\\,");
		} else {
			return new double[] { doubleValue(str) };
		}
	}
	
	/**
	 * 字符串转换成double[]
	 * 
	 * @param str 字符串
	 * @param split 分隔符（特殊符号必须加转义符\\）：如"\\|"
	 * @return
	 */
	private static double[] strToDoubleArray(String str, String split) {
		double[] ret = null;
		if (str != null && StringUtils.isNotBlank(str)) {
			String[] strAry = splitStr(str, split);
			if (strAry != null) {
				int size = strAry.length;
				ret = new double[size];
				for (int i = 0; i < size; i++) {
					ret[i] = doubleValue(strAry[i]);
				}
			}
		}
		return ret;
	}

	public static float[] strToFloatArray(String str) {
		if (str == null) {
			return null;
		}
		if (str.indexOf(",") > 0) {
			return strToFloatArray(str, "\\,");
		} else if (str.indexOf("|") > 0) {
			return strToFloatArray(str, "\\|");
		} else {
			return new float[] { floatValue(str) };
		}
	}

	public static float[] strToFloatArray(String str, String split) {
		float[] ret = null;
		if (str != null && StringUtils.isNotBlank(str)) {
			String[] strAry = splitStr(str, split);
			if (strAry != null) {
				int size = strAry.length;
				ret = new float[size];
				for (int i = 0; i < size; i++) {
					ret[i] = floatValue(strAry[i]);
				}
			}
		}
		return ret;
	}
	/**
	 * 字符串转换成long[]：分隔符=","
	 */
	public static long[] strToLongArray(String str) {
		if (str == null) {
			return null;
		}
		if (str.indexOf(",") > 0) {
			return strToLongArray(str, "\\,");
		} else {
			return new long[] { longValue(str) };
		}
	}
	
	/**
	 * 字符串转换成long[]
	 * 
	 * @param str 字符串
	 * @param split 分隔符（特殊符号必须加转义符\\）：如"\\|"
	 * @return
	 */
	private static long[] strToLongArray(String str, String split) {
		long[] ret = null;
		if (str != null && StringUtils.isNotBlank(str)) {
			String[] strAry = splitStr(str, split);
			if (strAry != null) {
				int size = strAry.length;
				ret = new long[size];
				for (int i = 0; i < size; i++) {
					ret[i] = longValue(strAry[i]);
				}
			}
		}
		return ret;
	}
	
	/**
	 * 字符串转换成int[]：分隔符=","
	 */
	public static int[] strToIntArray(String str) {
		if (str == null) {
			return null;
		}
		if (str.indexOf(",") > 0) {
			return strToIntArray(str, ",");
		} else if (str.indexOf("|") > 0) {
			return strToIntArray(str, "\\|");
		} else {
			return new int[] { intValue(str) };
		}
	}

	/**
	 * 字符串转换成Map：一级分隔符="|"，二级分隔符=","
	 */
	public static Map<Integer,Integer> strToMapIntInt(String str) {
		Map<Integer,Integer> map = new HashMap<>();
		if (str == null) {
			return map;
		}
		if (str.indexOf("|") > 0) {
			String[] strAry = str.split("\\|");
			for (String s : strAry) {
				String[] kv = s.split(",");
				if(kv.length == 2){
					map.put(intValue(kv[0]), intValue(kv[1]));
				}
			}
		} else {
			String[] kv = str.split(",");
			if(kv.length == 2){
				map.put(intValue(kv[0]), intValue(kv[1]));
			}
		}
		return map;
	}

	/**
	 * 字符串转换成int[]
	 * 
	 * @param str 字符串
	 * @param split 分隔符（特殊符号必须加转义符\\）：如"\\|"
	 * @return
	 */
	public static int[] strToIntArray(String str, String split) {
		int[] ret = null;
		if (str != null && StringUtils.isNotBlank(str)) {
			String[] strAry = splitStr(str, split);
			if (strAry != null) {
				int size = strAry.length;
				ret = new int[size];
				for (int i = 0; i < size; i++) {
					ret[i] = intValue(strAry[i]);
				}
			}
		}
		return ret;
	}
	
	public static List<Integer> strToIntList(String str) {
		if(str == null  || str.isEmpty() || isEmptyJSONString(str)) return new ArrayList<Integer>();

		if(str.startsWith("[") && str.endsWith("]")){
			str = str.substring(1, str.length() - 1);
		}
		
		List<Integer> l = new ArrayList<Integer>();
		String[] o = str.split(",");
		for (String s : o) {
			if(s.isEmpty()) continue;
			l.add(intValue(s.trim()));
		}
		
		return l;
	}
	public static List<Long> strToLongList(String str) {
		if(str == null  || str.isEmpty() || isEmptyJSONString(str)) return new ArrayList<Long>();

		if (str.startsWith("[") && str.endsWith("]")) {
			str = str.substring(1, str.length() - 1);
		}

		List<Long> l = new ArrayList<Long>();
		String[] o = str.split(",");
		for (String s : o) {
			if(s.isEmpty()) continue;
			l.add(Long.parseLong(s));
		}

		return l;
	}
	
	/**
	 * 将String，以“，”分割的字符串，转化为int[]
	 * @param str
	 * @return
	 */
	public static int[] arrayStrToInt(String str) {
		if(StringUtils.isEmpty(str)){
			return new int[0];
		}
		
		int []skillLogicArr = null;
		String skillLogicArrTemp[] = str.split(",");    //逻辑库的数组
		
		skillLogicArr = new int[skillLogicArrTemp.length];
		for (int i = 0; i < skillLogicArrTemp.length; i++) {
			skillLogicArr[i] = Utils.intValue(skillLogicArrTemp[i]);
		}
		
		return skillLogicArr;
	}

	public static long[] arrayStrToLong(String str) {
		if(StringUtils.isEmpty(str)){
			return new long[0];
		}

		long[] skillLogicArr = null;
		String skillLogicArrTemp[] = str.split(",");    //逻辑库的数组

		skillLogicArr = new long[skillLogicArrTemp.length];
		for (int i = 0; i < skillLogicArrTemp.length; i++) {
			skillLogicArr[i] = Utils.longValue(skillLogicArrTemp[i]);
		}

		return skillLogicArr;
	}


	public static int[] arrayStrToInt(String[] strArray){
		int[] intArr = new int[strArray.length];
		for (int i = 0; i<strArray.length;i++){
			intArr[i] = intValue(strArray[i]);
		}
		return intArr;
	}
	/**
	 * 将String[]，转化为String，以“，”分割
	 * @param arr
	 * @return
	 */
	public static String arrayStrToStr(String[] arr) {
		if(arr.length == 0){
			return "";
		}
		
		String result = "";
		for (int i = 0; i < arr.length; i++) {
			result += (arr[i] + ","); 
		}
		
		return result.substring(0, result.length() - 1);
	}
	
	/**
	 * 将int[]，转化为String，以“，”分割
	 * @param arr
	 * @return
	 */
	public static String arrayIntToStr(int[] arr) {
		if (arr == null || arr.length == 0){
			return "";
		}
		
		String result = "";
		for (int i = 0; i < arr.length; i++) {
			result += (arr[i] + ","); 
		}
		
		return result.substring(0, result.length() - 1);
	}
	
	/**
	 * 将int[]，转化为String，以“，”分割
	 * @param arr
	 * @return
	 */
	public static String arrayIntToStr(List<Integer> arr) {
		if(arr.isEmpty()){
			return "";
		}
		
		StringBuilder result = new StringBuilder();
		for(Integer i:arr){
			result.append(i).append(",");
		}
		
		return result.substring(0, result.length() - 1);
	}

    public static String arrayLongToStr(List<Long> arr) {
        if(arr.isEmpty()){
            return "";
        }

        StringBuilder result = new StringBuilder();
        for(Long i:arr){
            result.append(i).append(",");
        }

        return result.substring(0, result.length() - 1);
    }
	
	public static int[] arrayMerge(int [] arr1,int [] arr2) {
		int newArrLen = arr1.length + arr2.length;
		int [] mergeArr = new int [newArrLen];
		System.arraycopy(arr1, 0, mergeArr, 0, arr1.length);  
		System.arraycopy(arr2, 0, mergeArr, arr1.length, arr2.length); 
		return mergeArr;
	}
	
	/**
	 * 创建函数特征码
	 * 类全路径:函数名(参数类型)
	 * @return
	 */
	public static String createMethodKey(Method method) {
		return createMethodKey(method.getDeclaringClass(), method);
	}
	
	/**
	 * 创建函数特征码
	 * 类全路径:函数名(参数类型)
	 * @return
	 */
	public static String createMethodKey(Class<?> cls, Method method) {
		//类全路径
		String clazzName = cls.getName();
		//函数名
		String methodName = method.getName();
		//参数类型字符串
		StringBuilder methodParam = new StringBuilder();
		methodParam.append("(");
		for(Class<?> clazz : method.getParameterTypes()) {
			if(methodParam.length() > 1) methodParam.append(", "); 
			methodParam.append(clazz.getSimpleName());
		}
		methodParam.append(")");
		
		return clazzName + ":" + methodName + methodParam.toString();
	}
	
	/**
	 * 将毫秒转化为秒
	 * @param time
	 * @return
	 */
	public static int parseSecond(long time){
		return (int)time/1000;
	}
	
	/**
	 * 获取某个范围内的随机数(注意:结果包含最大值和最小值)
	 * @param min 最小值
	 * @param max 最大值
	 * @return
	 */
	public static int randomBetween(int min, int max){
		int s = RandomUtils.nextInt(max) % (max - min + 1) + min;
		return s;
	}
	
	/**
	 * 取[0,max)之间的随机值
	 * 
	 * @param max
	 * @return
	 */
	public static int random(int max) {
		return RandomUtils.nextInt(max);
	}

	/** 
	 * 是否在随机范围内
	 * <AUTHOR>
	 * @Date 2022/4/12
	 * @Param 
	 */
	public static boolean isRandomInclude(int probabilityValue, int maxTotal) {
		if(RandomUtils.nextInt(maxTotal) <= probabilityValue){
			return true;
		}
		return false;
	}
	
	/**
	 * 取[min,max)之间的随机值
	 * 
	 * @param min 最小值
	 * @param max 最大值
	 * @return
	 */
	public static int random(int min, int max) {
		if (min < max) {
			return random(max - min) + min;
		} else {
			return min;
		}
	}
	
	/**
	 * 取[min,max]之间的随机值
	 * 
	 * @param min 最小值
	 * @param max 最大值
	 * @return
	 */
	public static int randomMinMax(int min, int max) {
		if (min < max) {
			return random(min, max + 1);
		} else {
			return min;
		}
	}
	
	/**
	 * 随机获得数组中的值
	 * 
	 * @param array
	 * @return
	 */
	public static int random(int[] array) {
		if (array != null && array.length > 0) {
			int index = RandomUtils.nextInt(array.length);
			return array[index];
		} else {
			return 0;
		}
	}

	/**
	 * 随机获得数组中的值排除List
	 * @param array
	 * @param excludeList
	 * @return
	 */
	public static int random(int[] array, List<Integer> excludeList) {
		List<Integer> list = Arrays.stream(array)
				.boxed()
				.collect(Collectors.toList());
		list.removeAll(excludeList);
		return list.isEmpty() ? 0 : list.get(new Random().nextInt(list.size()));
	}
	
	/**
	 * 获取某个时间点的毫秒数
	 * @param hour
	 * @return
	 */
	public static long getHourMillis(int hour){
		
		Calendar to = Calendar.getInstance();
		to.clear();// clear一下或set一下data，a.set(Calendar.DATE, 1);
	    to.set(Calendar.HOUR_OF_DAY, hour);
	    to.set(Calendar.MINUTE, 0);
	    to.set(Calendar.SECOND, 0);
	    to.set(Calendar.MILLISECOND, 0);
	    
	    return to.getTimeInMillis();
	}
	
	/**
	 * 获取N天后的某个时间点的毫秒数
	 * @param hour
	 * @return
	 */
	public static long getHourOffDayMillis(int hour, int offDay){
		
		Calendar to = Calendar.getInstance();
		to.clear();// clear一下或set一下data，a.set(Calendar.DATE, 1);
		to.add(Calendar.DAY_OF_YEAR, offDay);
	    to.set(Calendar.HOUR_OF_DAY, hour);
	    to.set(Calendar.MINUTE, 0);
	    to.set(Calendar.SECOND, 0);
	    to.set(Calendar.MILLISECOND, 0);
	    
	    return to.getTimeInMillis();
	}
	
	/**
	 * 判断现在的时间是否在某个时间段范围内
	 * @param fromHour 小时
	 * @param toHour   小时
	 * @return
	 */
	public static boolean isTimeBetween(int fromHour, int toHour){
	    Calendar from = Calendar.getInstance();
		from.clear();// clear一下或set一下data，a.set(Calendar.DATE, 1);
	    from.set(Calendar.HOUR_OF_DAY, fromHour);
	    from.set(Calendar.MINUTE, 0);
	    from.set(Calendar.SECOND, 0);
	    from.set(Calendar.MILLISECOND, 0);
	    
	    Calendar to = Calendar.getInstance();
		to.clear();// clear一下或set一下data，a.set(Calendar.DATE, 1);
	    to.set(Calendar.HOUR_OF_DAY, toHour);
	    to.set(Calendar.MINUTE, 0);
	    to.set(Calendar.SECOND, 0);
	    to.set(Calendar.MILLISECOND, 0);
	    
	    long now = System.currentTimeMillis();
	    
	    return now >= from.getTimeInMillis() && now < to.getTimeInMillis(); 
	}
	
	
	/**
	 * 判断现在的时间是否在某个时间段范围内
	 * @param fromHour
	 * @param fromMin
	 * @param toHour
	 * @param fromMin
	 * @return
	 */
	public static boolean isTimeBetween(int fromHour, int fromMin, int toHour, int toMin){
	    Calendar from = Calendar.getInstance();
		from.clear();// clear一下或set一下data，a.set(Calendar.DATE, 1);
	    from.set(Calendar.HOUR_OF_DAY, fromHour);
	    from.set(Calendar.MINUTE, fromMin);
	    from.set(Calendar.SECOND, 0);
	    from.set(Calendar.MILLISECOND, 0);
	    
	    Calendar to = Calendar.getInstance();
		to.clear();// clear一下或set一下data，a.set(Calendar.DATE, 1);
	    to.set(Calendar.HOUR_OF_DAY, toHour);
	    to.set(Calendar.MINUTE, toMin);
	    to.set(Calendar.SECOND, 0);
	    to.set(Calendar.MILLISECOND, 0);
	    
	    long now = System.currentTimeMillis();
	    
	    return now >= from.getTimeInMillis() && now < to.getTimeInMillis(); 
	}
	
	/**
	 * 判断是否是一周的星期几
	 * @param day Calendar.MONDAY.... (周日至周六:  1 2 3 4 5 6 7)
	 * @return
	 */
	public static boolean isDayOfWeek(int day){
		Calendar cal = Calendar.getInstance();
		return cal.get(Calendar.DAY_OF_WEEK) == day;
	}

	/**
	 * 获取指定时间是周几 (周日至周六:  1 2 3 4 5 6 7)
	 * <AUTHOR>
	 * @Date 2024/7/15
	 * @Param
	 */
	public static int getDayOfWeek(long time){
		Calendar cal = Calendar.getInstance();
		cal.clear();// clear一下或set一下data，a.set(Calendar.DATE, 1);
		cal.setTimeInMillis(time);
		return cal.get(Calendar.DAY_OF_WEEK);
	}

	/**
	 * 获取指定时间周1的0点
	 * @Param
	 */
	public static long getMondayZero(long time) {
		Calendar cal = Calendar.getInstance();
		cal.setTimeInMillis(time);
		cal.setFirstDayOfWeek(Calendar.MONDAY); // 设置周一为周首日
		cal.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY); // 调整到当前周的周一
		cal.set(Calendar.HOUR_OF_DAY, 0);
		cal.set(Calendar.MINUTE, 0);
		cal.set(Calendar.SECOND, 0);
		cal.set(Calendar.MILLISECOND, 0);

		// 如果当前时间已经是周一 0 点，则返回上一周的周一
		if (cal.getTimeInMillis() > time) {
			cal.add(Calendar.WEEK_OF_YEAR, -1);
		}
		return cal.getTimeInMillis();
	}

	/**
	 * 获取指定时间下个周1的0点
	 * @Param
	 */
	public static long getNextMondayZero(long time) {
		Calendar cal = Calendar.getInstance();
		cal.setTimeInMillis(time);
		cal.setFirstDayOfWeek(Calendar.MONDAY); // 设置周一为周首日
		cal.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY); // 调整到当前周的周一
		cal.set(Calendar.HOUR_OF_DAY, 0);
		cal.set(Calendar.MINUTE, 0);
		cal.set(Calendar.SECOND, 0);
		cal.set(Calendar.MILLISECOND, 0);

		cal.add(Calendar.WEEK_OF_YEAR, 1);
		return cal.getTimeInMillis();
	}

	/**
	 * 获取当前时间是周几
	 * <AUTHOR>
	 * @Date 2021/7/28
	 * @Param (周日至周六:  1 2 3 4 5 6 7)
	 */
	public static int getDayOfWeek(){
		Calendar cal = Calendar.getInstance();
		return cal.get(Calendar.DAY_OF_WEEK);
	}

	/**
	 * 判断是否是一周的某一天
	 * @param millis 毫秒数
	 * @param day (周日至周六:  1 2 3 4 5 6 7)
	 * @return
	 */
	public static boolean isDayOfWeek(long millis, int day){
		Calendar cal = Calendar.getInstance();
		cal.clear();// clear一下或set一下data，a.set(Calendar.DATE, 1);
	    cal.setTimeInMillis(millis);
		return cal.get(Calendar.DAY_OF_WEEK) == day;
	}
	
	
	/**
	 * 是否是一个月的某一天
	 * @param millis
	 * @param day
	 * @return
	 */
	public static boolean isDayOfMonth(long millis, int day){
		Calendar cal = Calendar.getInstance();
		cal.clear();// clear一下或set一下data，a.set(Calendar.DATE, 1);
	    cal.setTimeInMillis(millis);
		return cal.get(Calendar.DAY_OF_MONTH) == day;
	}
	
	/**
	 * 將字符串压缩为 gzip 流
	 * 
	 * @param content
	 * @return
	 */
	public static byte[] gzip(String content) {
		ByteArrayOutputStream baos = null;
		GZIPOutputStream out = null;
		byte[] ret = null;
		try {
			baos = new ByteArrayOutputStream();
			out = new GZIPOutputStream(baos);
			out.write(content.getBytes());
			out.close();
			baos.close();
			ret = baos.toByteArray();
		} catch (FileNotFoundException e) {
			Log.temp.error("===e={}", e);
		} catch (IOException e) {
			Log.temp.error("===e={}", e);
		} finally {
			if (out != null) {
				try {
					baos.close();
				} catch (IOException e) {
					Log.temp.error("===e={}", e);
				}
			}
			if (out != null) {
				try {
					out.close();
				} catch (IOException e) {
					Log.temp.error("===e={}", e);
				}
			}
		}
		return ret;
	}

	public static String compressJson(String json) {
		if (json == null || json.isEmpty()) {
			return null;
		}
		try (ByteArrayOutputStream baos = new ByteArrayOutputStream();
			 GzipCompressorOutputStream gzipOut = new GzipCompressorOutputStream(baos)) {

			gzipOut.write(json.getBytes("UTF-8"));
			gzipOut.finish();
			return Base64.getEncoder().encodeToString(baos.toByteArray());
		} catch (Exception e) {
			Log.game.error("Error compressing JSON", e);
			return null;
		}
	}

	public static String decompressJson(String compressedJson) {
		if (compressedJson == null || compressedJson.isEmpty()) {
			return null;
		}
		try {
			byte[] compressed = Base64.getDecoder().decode(compressedJson);
			try (ByteArrayInputStream bais = new ByteArrayInputStream(compressed);
				 GzipCompressorInputStream gzipIn = new GzipCompressorInputStream(bais);
				 ByteArrayOutputStream baos = new ByteArrayOutputStream()) {

				byte[] buffer = new byte[1024];
				int len;
				while ((len = gzipIn.read(buffer)) != -1) {
					baos.write(buffer, 0, len);
				}
				return new String(baos.toByteArray(), "UTF-8");
			}
		} catch (Exception e) {
			Log.game.error("Error decompressing JSON", e);
			return null;
		}
	}

	/**
	 * LZ4压缩
	 * @param protoMessage
	 * @return
	 */
	public static String compressProtoLZ4(Message protoMessage) {
		if (protoMessage == null) {
			return null;
		}
		try (ByteArrayOutputStream baos = new ByteArrayOutputStream();
			 BlockLZ4CompressorOutputStream lz4Out = new BlockLZ4CompressorOutputStream(baos)) {

			lz4Out.write(protoMessage.toByteArray());
			lz4Out.finish();
			// 使用ISO_8859_1编码，因为它是单字节编码，不会改变原始字节值
			return new String(baos.toByteArray(), StandardCharsets.ISO_8859_1);
		} catch (Exception e) {
			Log.game.error("Error compressing protobuf with LZ4", e);
			return null;
		}
	}

	/**
	 * LZ4解压
	 * @param compressedData
	 * @param parser
	 * @return
	 */
	public static <T extends Message> T decompressProtoLZ4(String compressedData, Parser<T> parser) {
		if (compressedData == null || compressedData.isEmpty()) {
			return null;
		}
		try {
			// 转回字节数组
			byte[] compressed = compressedData.getBytes(StandardCharsets.ISO_8859_1);
			try (ByteArrayInputStream bais = new ByteArrayInputStream(compressed);
				 BlockLZ4CompressorInputStream lz4In = new BlockLZ4CompressorInputStream(bais);
				 ByteArrayOutputStream baos = new ByteArrayOutputStream()) {

				byte[] buffer = new byte[1024];
				int len;
				while ((len = lz4In.read(buffer)) != -1) {
					baos.write(buffer, 0, len);
				}
				return parser.parseFrom(baos.toByteArray());
			}
		} catch (Exception e) {
			Log.game.error("Error decompressing protobuf with LZ4", e);
			return null;
		}
	}

	/**
	 * 通用协议缓冲区列表序列化方法
	 * @param messageList 要序列化的消息列表
	 * @return 包含长度前缀的字节数组
	 */
	public static byte[] serializeProtoList(List<? extends Message> messageList){
		try (ByteArrayOutputStream baos = new ByteArrayOutputStream();
			 DataOutputStream dos = new DataOutputStream(baos)) {

			for (Message message : messageList) {
				byte[] bytes = message.toByteArray();
				dos.writeInt(bytes.length);  // 写入4字节长度前缀
				dos.write(bytes);            // 写入实际数据
			}
			return baos.toByteArray();
		} catch (Exception e) {
			Log.game.error("Error Protocol buffer serialize error", e);
			return null;
		}
	}

	/**
	 * 通用协议缓冲区列表反序列化方法
	 * @param data 包含长度前缀的字节数组
	 * @param parser 协议缓冲区的解析器
	 * @return 反序列化后的消息列表
	 */
	public static <T extends Message> List<T> deserializeProtoList(byte[] data, Parser<T> parser){
		List<T> resultList = new ArrayList<>();
		try (ByteArrayInputStream bais = new ByteArrayInputStream(data);
			 DataInputStream dis = new DataInputStream(bais)) {

			while (dis.available() > 0) {
				int length = dis.readInt();    // 读取长度前缀
				byte[] bytes = new byte[length];
				dis.readFully(bytes);          // 读取指定长度的字节
				try {
					T message = parser.parseFrom(bytes);
					resultList.add(message);
				} catch (Exception e) {
					throw new IOException("Protocol buffer parse error", e);
				}
			}
		} catch (Exception e) {
			Log.game.error("Error Protocol buffer parse error", e);
			return null;
		}
		return resultList;
	}

	/**
	 * LZ4压缩
	 * @param bytes
	 * @return
	 */
	public static String compressBytesLZ4(byte[] bytes, String bytesFlag) {
		if (bytes == null || bytes.length == 0) {
			return bytesFlag;
		}
		String byteStr;
		try (ByteArrayOutputStream baos = new ByteArrayOutputStream();
			 BlockLZ4CompressorOutputStream lz4Out = new BlockLZ4CompressorOutputStream(baos)) {
			lz4Out.write(bytes);
			lz4Out.finish();
			// 使用ISO_8859_1编码，因为它是单字节编码，不会改变原始字节值
			byteStr = Base64.getEncoder().encodeToString(baos.toByteArray());
//			byteStr = new String(baos.toByteArray(), StandardCharsets.ISO_8859_1);
		} catch (Exception e) {
			Log.game.error("Error compressing bytes with LZ4", e);
			throw new SysException("Error compressing bytes with LZ4");
		}
		return bytesFlag+byteStr;
	}

	/**
	 * LZ4解压
	 * @param compressedData
	 * @return
	 */
	public static byte[] decompressBytesLZ4(String compressedData, String bytesFlag) {
		if (compressedData == null || compressedData.isEmpty() || compressedData.length()<bytesFlag.length()) {
			return new byte[0];
		}
		String dataStr = compressedData.substring(bytesFlag.length());
		if (dataStr.isEmpty()) {
			return new byte[0];
		}
		// 转回字节数组
//		byte[] compressed = dataStr.getBytes(StandardCharsets.ISO_8859_1);
		byte[] compressed = Base64.getDecoder().decode(dataStr);
		try (ByteArrayInputStream bais = new ByteArrayInputStream(compressed);
			 BlockLZ4CompressorInputStream lz4In = new BlockLZ4CompressorInputStream(bais);
			 ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
			byte[] buffer = new byte[1024];
			int len;
			while ((len = lz4In.read(buffer)) != -1) {
				baos.write(buffer, 0, len);
			}
			return baos.toByteArray();
		} catch (Exception e) {
			Log.game.error("Error decompressing protobuf with LZ4", e);
			throw new SysException(e);
		}
	}

	/**
	 * LZ4压缩
	 * @param bytes
	 * @return
	 */
	public static String compressBytesLZ4(byte[] bytes) {
		return compressBytesLZ4(bytes,"");
	}

	/**
	 * LZ4解压
	 * @param compressedData
	 * @return
	 */
	public static byte[] decompressBytesLZ4(String compressedData) {
		return decompressBytesLZ4(compressedData,"");
	}


	/**
	 * LZ4压缩
	 * @param bytes
	 * @return
	 */
	public static String compressSnappy(byte[] bytes, String bytesFlag) {
		if (bytes == null || bytes.length == 0) {
			return bytesFlag;
		}
		String byteStr;
		try (ByteArrayOutputStream baos = new ByteArrayOutputStream();
			 SnappyCompressorOutputStream lz4Out = new SnappyCompressorOutputStream(baos, bytes.length)) {
			lz4Out.write(bytes);
			lz4Out.finish();
			// 使用ISO_8859_1编码，因为它是单字节编码，不会改变原始字节值
			byteStr = Base64.getEncoder().encodeToString(baos.toByteArray());
//			byteStr = new String(baos.toByteArray(), StandardCharsets.ISO_8859_1);
		} catch (Exception e) {
			Log.game.error("Error compressing bytes with Snappy", e);
			throw new SysException("Error compressing bytes with Snappy");
		}
		return bytesFlag+byteStr;
	}

	/**
	 * LZ4解压
	 * @param compressedData
	 * @return
	 */
	public static byte[] decompressSnappy(String compressedData, String bytesFlag) {
		if (compressedData == null || compressedData.isEmpty() || compressedData.length()<bytesFlag.length()) {
			return new byte[0];
		}
		String dataStr = compressedData.substring(bytesFlag.length());
		if (dataStr.isEmpty()) {
			return new byte[0];
		}
		// 转回字节数组
//		byte[] compressed = dataStr.getBytes(StandardCharsets.ISO_8859_1);
		byte[] compressed = Base64.getDecoder().decode(dataStr);
		try (ByteArrayInputStream bais = new ByteArrayInputStream(compressed);
			 SnappyCompressorInputStream lz4In = new SnappyCompressorInputStream(bais);
			 ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
			byte[] buffer = new byte[1024];
			int len;
			while ((len = lz4In.read(buffer)) != -1) {
				baos.write(buffer, 0, len);
			}
			return baos.toByteArray();
		} catch (Exception e) {
			Log.game.error("Error decompressing protobuf with Snappy", e);
			throw new SysException(e);
		}
	}

	/**
	 * 格式化字节流大小显示<BR/>
	 * 大于5MB 单位显示MB<BR/>
	 * 大于5KB 单位显示KB<BR/>
	 * 其余直接显示字节B
	 * @return 
	 */
	public static String formatByteSize(long byteSize) {
		return byteSize > 5 * SIZE_MB ? (byteSize / SIZE_MB + "MB") : (byteSize > 5 * SIZE_KB ? (byteSize / SIZE_KB + "KB") : (byteSize + "B"));
	}

	/**
	 * 获取堆外内存信息
	 * @return Map<reserved: 已申请堆外内存, max:最大可申请堆外内存>
	 */
	public static Map<String, Long> memoryDirectInfo() {
		try {
			Class<?> c = Class.forName("java.nio.Bits");
			
			//已申请堆外内存
			Field reserved = c.getDeclaredField("reservedMemory");
			reserved.setAccessible(true);
			Long reservedValue = (Long)reserved.get(null);
			
			//最大可申请堆外内存
			Field max = c.getDeclaredField("maxMemory");
			max.setAccessible(true);
			Long maxValue = (Long)max.get(null);

			return Utils.ofMap("reserved", reservedValue, "max", maxValue);
		} catch(Exception e) {
			throw new SysException(e);
		}
	}
	/**
	 * @param days 1-7  周一~周日
	 * @return 1-7  周日~周一
	 */
	public static int[] getQuartzDayOffWeek(int days[]){		
		int len = days.length;
		int[] result = new int[len];
		for(int i=0; i < len; i++){
			result[i] = days[i] == 7 ? 1 : days[i] + 1;
 		}
		
		return result;
	}
	
	/**
	 * 哈希分布增强
	 * 防止质量较差的哈希函数造成的影响
	 * @param hash
	 * @return
	 */
	public static int hash(int hash) {
		hash ^= (hash >>> 20) ^ (hash >>> 12);
		return hash ^ (hash >>> 7) ^ (hash >>> 4);
	}
	
	/**
	 * 根据参数获取quartz的执行时间格式
	 * @param day 注意:!!!!!!quartz cron中 周日至周六的数字是  1-7
	 * @param hour
	 * @param min
	 * @param second
	 * @return 如: 每周1,3,5 12:30 执行schedule 0 30 12 ? * 2,4,6 *
	 */
	public static String getQuartzCron(int[] day, int hour, int min, int second){
		
		//0 30 12 ? * 2,4,6 *
		StringBuilder cron = new StringBuilder();
		
		if(second !=-1){
			cron.append(second + " ");
		}else{
			cron.append("* ");
		}
		
		if(min !=-1){
			cron.append(min + " ");
		}else{
			cron.append("* ");
		}
		
		if(hour != -1){
			cron.append(hour +" ");
		}else{
			cron.append("* ");
		}
		cron.append("? ");
		cron.append("* ");
		
		if(day != null && day.length > 0 && day[0]!=-1){
			for(int i=0; i< day.length; i++){
				if(i > 0){
					cron.append(",");
				}
				cron.append(day[i]);
			}
			cron.append(" ");
		}else{
			cron.append("* ");
		}
		cron.append("* ");
		return cron.toString();
	}

	/**
	 * 判断是否在某个时间段范围内
	 * @param days
	 * @param startHour
	 * @param startMin
	 * @param endHour
	 * @param endMin
	 * @return
	 */
	public static boolean isInTime(int[] days, int startHour, int startMin, int endHour, int endMin){
		Calendar cal = Calendar.getInstance();
		cal.clear();// clear一下或set一下data，a.set(Calendar.DATE, 1);
		int day = cal.get(Calendar.DAY_OF_WEEK);
		
		boolean flag = true;
		if(days!=null && days.length > 0 && days[0] !=-1){
			flag = false;
			int[] formatDays = getQuartzDayOffWeek(days);
			for(int i=0; i<formatDays.length; i++){
				if(day == formatDays[i]){
					flag = true;
					break;
				}
			}
		}
		
		if(flag){
			if(startHour == -1 && startMin == -1 && endHour == -1 && endMin == -1){
				return true;
			}
			Calendar start = Calendar.getInstance();
			start.clear();// clear一下或set一下data，a.set(Calendar.DATE, 1);
			start.set(Calendar.HOUR_OF_DAY, startHour);
			start.set(Calendar.MINUTE, startMin);
			
			Calendar end = Calendar.getInstance();
			end.clear();// clear一下或set一下data，a.set(Calendar.DATE, 1);
			end.set(Calendar.HOUR_OF_DAY, endHour);
			end.set(Calendar.MINUTE, endMin);
			
			long curr = System.currentTimeMillis();
			if(curr >= start.getTimeInMillis()  && curr < end.getTimeInMillis()){
				return true;
			}
		}
		
		return false;
		
	}
	
	/**
	 * 检查某个字符串中是否有某个字符
	 * @param source 源字符串
	 * @param check 要检查的字符串
	 * @param split 分隔符
	 * @return
	 */
	public static boolean check(String source, String check, String split){
		if(StringUtils.isEmpty(source)){
			return false;
		}
		
		String[] strs = source.split(split);
		for(String str : strs){
			if(str.equals(check)){
				return true;
			}
		}
		
		return false;
	}
	
	/**
	 * 获取当前的小时数
	 * @return
	 */
	public static int getHourOfDay(){
		Calendar cal = Calendar.getInstance();
		return cal.get(Calendar.HOUR_OF_DAY);
	}
	
	/**
	 * 获取指定时间的小时数
	 * @param mills
	 * @return
	 */
	public static int getHourOfDay(long mills){
		Calendar cal = Calendar.getInstance();
		cal.clear();// clear一下或set一下data，a.set(Calendar.DATE, 1);
		cal.setTimeInMillis(mills);
		return cal.get(Calendar.HOUR_OF_DAY);
	}

	/**
	 * 获取当天的指定小时的时间
	 *
	 * @param time
	 *            取当天凌晨的话传入 System.currentTimeMillis() 即可
	 * @return hour 指定小时
	 * @return
	 */
	public static long getTimeHourOfToday(long time, int hour) {
		Calendar ca = Calendar.getInstance();
		ca.setTimeInMillis(time);
		ca.set(Calendar.HOUR_OF_DAY, hour);
		ca.set(Calendar.MINUTE, 0);
		ca.set(Calendar.SECOND, 0);
		ca.set(Calendar.MILLISECOND, 0);

		return ca.getTimeInMillis();
	}
	
	/**
	 * 获取下一个日期的时间点（如:下一个周日的21点）
	 * @param currTime 当前时间(毫秒数)
	 * @param day 星期几
	 * @param hour 小时(0-24)
	 * @return
	 */
	public static long getNextTime(long currTime, int day,int hour){
		Calendar cal = Calendar.getInstance();
		cal.clear();// clear一下或set一下data，a.set(Calendar.DATE, 1);
		cal.setTimeInMillis(currTime);
		cal.add(Calendar.DATE, 7);//向后推移7天
		cal.set(Calendar.DAY_OF_WEEK, day);
		cal.set(Calendar.HOUR_OF_DAY, hour);
		cal.set(Calendar.MINUTE, 0);
		cal.set(Calendar.SECOND, 0);
		
		return cal.getTimeInMillis();
	}

	/**
	 * 获取当前时间的偏移天数
	 * @param currTime 当前时间(毫秒数)
	 * @param dayOff 偏移天数
	 * @param hour 小时(0-24)
	 * @return
	 */
	public static long getOffDayTime(long currTime, int dayOff,int hour){
		Calendar cal = Calendar.getInstance();
		cal.clear();// clear一下或set一下data，a.set(Calendar.DATE, 1);
		cal.setTimeInMillis(currTime);
		cal.add(Calendar.DATE, dayOff);
		cal.set(Calendar.HOUR_OF_DAY, hour);
		cal.set(Calendar.MINUTE, 0);
		cal.set(Calendar.SECOND, 0);
		
		return cal.getTimeInMillis();
	}
	
	/**
	 * 
	 * 判断是否可以重置
	 * @param lastTime 	上一次重置的时间
	 * @param refresh	重置的格式
	 * 类型1,每日固定点重置 ： [1,22] 		-- 每日22点重置
	 * 类型2,每周固定点重置: [2,4,22]		-- 每周周三22点重置	
	 * 类型3,每月固定点重置: [3,12,22]	-- 每月的12号22点重置
	 * 
	 * @return
	 */
	public static boolean canRest(long lastTime,int[] refresh) {
		long timeNow = Port.getCurrent().getTimeCurrent();	
		
		// 现在时间对应的重置时间点
		long thisRestTime = 0;
		// 上一个重置的时间点
		long lastRestTime = 0;
		
		if(refresh == null || refresh.length == 0) {
			return false;
		}
		int type = refresh[0];
		if(lastTime == 0) {
			return true;			
		}
		//每日整点更新
		if(type == 1) {
			if(refresh.length <2) {
				return false;
			}
			//今天的刷新时间点
			Calendar ca = Calendar.getInstance();
			ca.clear();// clear一下或set一下data，a.set(Calendar.DATE, 1);
			ca.setTimeInMillis(timeNow);
			ca.set(Calendar.HOUR_OF_DAY, refresh[1]);
			ca.set(Calendar.MINUTE, 0);
			ca.set(Calendar.SECOND, 0);
			ca.set(Calendar.MILLISECOND, 0);	
			thisRestTime = ca.getTimeInMillis();
			ca.add(Calendar.DAY_OF_MONTH, -1);
			lastRestTime = ca.getTimeInMillis();
	
		} else if (type == 2) {
			if(refresh.length <3) {
				return false;
			}
			
			Calendar ca = Calendar.getInstance();
			ca.clear();// clear一下或set一下data，a.set(Calendar.DATE, 1);
			ca.setTimeInMillis(timeNow);
			ca.set(Calendar.DAY_OF_WEEK,refresh[1]); //周几
			ca.set(Calendar.HOUR_OF_DAY, refresh[2]);//小时
			ca.set(Calendar.MINUTE, 0);
			ca.set(Calendar.SECOND, 0);
			ca.set(Calendar.MILLISECOND, 0);
			thisRestTime = ca.getTimeInMillis();			
			//上一周的重置时间点
			ca.set(Calendar.WEEK_OF_MONTH, -1);
			lastRestTime = ca.getTimeInMillis();
			
			
			//每月几号，几点更新
		}else if(type == 3) {	
			if(refresh.length <3) {
				return false;
			}
			//重置的时间点
			Calendar ca = Calendar.getInstance();
			ca.clear();// clear一下或set一下data，a.set(Calendar.DATE, 1);
			ca.setTimeInMillis(timeNow);
			ca.set(Calendar.DAY_OF_MONTH,refresh[1]);
			ca.set(Calendar.HOUR_OF_DAY, refresh[2]);
			ca.set(Calendar.MINUTE, 0);
			ca.set(Calendar.SECOND, 0);
			ca.set(Calendar.MILLISECOND, 0);			
			thisRestTime = ca.getTimeInMillis();			
			//上个月的重置点
			ca.add(Calendar.MONTH, -1);
			lastRestTime = ca.getTimeInMillis();			
		}
		
		if(lastTime < lastRestTime) {
			return true;
		}
		if( lastTime  <=lastRestTime || (lastTime <thisRestTime && lastRestTime <timeNow && timeNow>=thisRestTime)) {
			return true;
		}		
		return false;
	}
	
	/** 
	 * 获取本月几号几点的时间戳
	 * <AUTHOR>
	 * @Date 2023/1/10
	 * @Param 
	 */
	public static long getMonthDateTime(long timeNow, int dayOfMonth, int hour){
		//重置的时间点
		Calendar ca = Calendar.getInstance();
		ca.clear();// clear一下或set一下data，a.set(Calendar.DATE, 1);
		ca.setTimeInMillis(timeNow);
		ca.set(Calendar.DAY_OF_MONTH, dayOfMonth);
		ca.set(Calendar.HOUR_OF_DAY, hour);
		ca.set(Calendar.MINUTE, 0);
		ca.set(Calendar.SECOND, 0);
		ca.set(Calendar.MILLISECOND, 0);
		return ca.getTimeInMillis();
	}
	/** 
	 * 获取几月几号几点时间戳（monthDifference差月值，-1上个月）
	 * <AUTHOR>
	 * @Date 2023/1/10
	 * @Param 
	 */
	public static long getLastMonthDateTime(long timeNow, int dayOfMonth, int hour){
		//重置的时间点
		return getAmountMonthDateTime(timeNow, dayOfMonth, hour, -1);
	}

	public static long getAmountMonthDateTime(long timeNow, int dayOfMonth, int hour, int amount){
		//重置的时间点
		Calendar ca = Calendar.getInstance();
		ca.clear();// clear一下或set一下data，a.set(Calendar.DATE, 1);
		ca.setTimeInMillis(timeNow);
		ca.set(Calendar.DAY_OF_MONTH, dayOfMonth);
		ca.set(Calendar.HOUR_OF_DAY, hour);
		ca.set(Calendar.MINUTE, 0);
		ca.set(Calendar.SECOND, 0);
		ca.set(Calendar.MILLISECOND, 0);
		//上个月的重置点
		ca.add(Calendar.MONTH, amount);
		return ca.getTimeInMillis();
	}

	public static long getDayTime(long timeNow, int hour, int minute, int sec){
		//重置的时间点
		Calendar ca = Calendar.getInstance();
		ca.clear();// clear一下或set一下data，a.set(Calendar.DATE, 1);
		ca.setTimeInMillis(timeNow);
		ca.set(Calendar.HOUR_OF_DAY, hour);
		ca.set(Calendar.MINUTE, minute);
		ca.set(Calendar.SECOND, sec);
		ca.set(Calendar.MILLISECOND, 0);
		return ca.getTimeInMillis();
	}

	/**
	 * 获取rpc调用处的函数信息，文件名:行号 调用者函数名
	 *
	 * @return
	 */
	public static String getCallerInfo() {
		StackTraceElement e = SharedSecrets.getJavaLangAccess().getStackTraceElement(new Exception(), 3);
		return new StringBuilder().append(e.getFileName()).append(":")
				.append(e.getLineNumber()).append(" ")
				.append(e.getMethodName()).toString();
	}
	/**
	 * 获取rpc调用处的函数信息，文件名:行号 调用者函数名
	 *
	 * @return
	 */
	public static String getCallerInfo(int stackDeep) {
		StringBuilder sb = new StringBuilder();
		Exception throwable = new Exception();
		int stackTraceDepth = SharedSecrets.getJavaLangAccess().getStackTraceDepth(throwable);
		stackTraceDepth = Math.min(stackTraceDepth,stackDeep);
		for(int i=0;i<stackTraceDepth;i++){
			StackTraceElement e = SharedSecrets.getJavaLangAccess().getStackTraceElement(throwable, i);
			sb.append(e.getFileName()).append(":")
					.append(e.getLineNumber()).append(" ")
					.append(e.getMethodName()).append("\n");
		}
		return sb.toString();
	}


	
	/**
	 * 获取rpc调用处的函数信息，文件名:行号 调用者函数名
	 * 
	 * @return
	 */
	public static String getCallerInfo2() {
		StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
		StackTraceElement e = stackTrace[3];
		return new StringBuilder().append(e.getFileName()).append(":")
					.append(e.getLineNumber()).append(" ")
					.append(e.getMethodName()).toString();
	}
	
	/**
	 * 判断一个数组中的所有元素是否都是整数
	 * @param strs
	 * @return
	 */
	public static boolean isDigits(String strs[]){
		if(strs == null || strs.length == 0){
			return false;
		}
		for(String str : strs){
			if(!NumberUtils.isDigits(str)){
				return false;
			}
		}
		return true;
	}

	public static List<Integer> intToIntegerList(int[] arr) {
		List<Integer> intList = new ArrayList<>();
		if(arr != null){
			for (int i = 0; i < arr.length; i++) {
				intList.add(arr[i]);
			}
		}
		return intList;
	}
	/**
	 * 根据权重取值，返回随机到的元素下标
	 *
	 * @param rates
	 * @return
	 */
	public static int getRandRange(List<Integer> rates) {
		Integer baseRate = 0 ;
		for (Integer i : rates) {
			baseRate += i;
		}
		int r = (int) (Math.random() * baseRate);
		int c = 0;
		if (rates != null && !rates.isEmpty()) {
			for (int i = 0; i < rates.size(); i ++) {
				c += rates.get(i);
				if (r < c) {
					return i;
				}
			}
		}
		return -1;
	}

	/**
	 * 根据权重取值，返回随机到的元素下标
	 * @param rates
	 * @param baseRate
	 * @param step
	 * @return
	 */
	public static int getRandRange(int[] rates, int baseRate, int step) {
		int r = (int) (Math.random() * baseRate);
		int c = 0;
		if (rates != null) {
			for (int i = 0; i < rates.length; i += step) {
				c += rates[i];
				if (r < c) {
					return i;
				}
			}
		}
		return -1;
	}
	public static int getRandRange(int[] rates) {
		int baseRate = 0;
		for (int i = 0; i < rates.length; i++) {
			baseRate +=rates[i];
		}
		int r = (int) (Math.random() * baseRate);
		int c = 0;
		if (rates != null) {
			for (int i = 0; i < rates.length; i ++) {
				c += rates[i];
				if (r < c) {
					return i;
				}
			}
		}
		return -1;
	}

	public static int getRandRange(int[][] rates) {
		int baseRate = 0;
		for (int i = 0; i < rates.length; i++) {
			baseRate +=rates[i][1];
		}
		int r = (int) (Math.random() * baseRate);
		int c = 0;
		if (rates != null) {
			for (int i = 0; i < rates.length; i ++) {
				c += rates[i][1];
				if (r < c) {
					return i;
				}
			}
		}
		return -1;
	}

	public static int getRandRangeValue(int[][] rates) {
		int baseRate = 0;
		for (int i = 0; i < rates.length; i++) {
			baseRate +=rates[i][1];
		}
		int r = (int) (Math.random() * baseRate);
		int c = 0;
		if (rates != null) {
			for (int i = 0; i < rates.length; i ++) {
				c += rates[i][1];
				if (r < c) {
					return rates[i][0];
				}
			}
		}
		return -1;
	}

	public static boolean isJSONValid(String string){
		try {
			JSONObject jsonStr= JSONObject.parseObject(string);
			return  true;
		} catch (Exception e) {
			return false;
		}
	}
	/**
	 * string 格式转换为 List<String>
	 * @param str
	 * @return
	 */
	public static List<String> strToStringList(String str) {
		if (str == null || str.isEmpty())
			return new ArrayList<String>();

		List<String> l = new ArrayList<String>();
		String[] o = str.split(",");
		for (String s : o) {
			if (s.isEmpty())
				continue;
			l.add(s.trim());
		}
		return l;
	}

	public static  <T extends  Object>  String listToString(List<T> list){
		StringBuilder sb=new StringBuilder();
		for(T item : list){
			if(sb.length()==0) {
				sb.append(item.toString());
			} else {
				sb.append("," + item.toString());
			}
		}
		return sb.toString();
	}

	public static  <T extends  Object>  String setToString(Set<T> list){
		StringBuilder sb=new StringBuilder();
		for(T item : list){
			if(sb.length()==0) {
				sb.append(item.toString());
			} else {
				sb.append("," + item.toString());
			}
		}
		return sb.toString();
	}

	public static long getPreviousValidDate(String cronStr) {
		if (!CronExpression.isValidExpression(cronStr)) {
			return 0;
		}

		CronDefinition def = CronDefinitionBuilder.instanceDefinitionFor(CronType.QUARTZ);
		CronParser parser = new CronParser(def);

		Cron cron = parser.parse(cronStr);
		ExecutionTime executionTime = ExecutionTime.forCron(cron);
		ZonedDateTime vs = ZonedDateTime.now();
		Optional<ZonedDateTime> lastExecution = executionTime.lastExecution(vs);
		if (lastExecution.isPresent()) {
			Timestamp ts = Timestamp.from(lastExecution.get().toInstant());
			return ts.getTime();
		}
		return  0;
	}

	public static long getPreviousValidDate(long time, String cronStr) {
		if (!CronExpression.isValidExpression(cronStr)) {
			return 0;
		}

		CronDefinition def = CronDefinitionBuilder.instanceDefinitionFor(CronType.QUARTZ);
		CronParser parser = new CronParser(def);

		Cron cron = parser.parse(cronStr);
		ExecutionTime executionTime = ExecutionTime.forCron(cron);
//		ZonedDateTime vs = ZonedDateTime.now();
//		Optional<ZonedDateTime> lastExecution = executionTime.lastExecution(vs);

		ZonedDateTime zdt = ZonedDateTime.ofInstant(Instant.ofEpochMilli(time), ZoneId.systemDefault());
		Optional<ZonedDateTime> lastExecution = executionTime.lastExecution(zdt);

		if (lastExecution.isPresent()) {
			Timestamp ts = Timestamp.from(lastExecution.get().toInstant());
			return ts.getTime();
		}
		return  0;
	}

	/**
	 * 	获取下次执行时间（getFireTimeAfter，也可以下下次...）
	 */
	public static long getNextTriggerTime(String cron){
		if(!CronExpression.isValidExpression(cron)){
			return 0;
		}
		CronTrigger trigger = TriggerBuilder.newTrigger().withIdentity("Caclulate Date").withSchedule(CronScheduleBuilder.cronSchedule(cron)).build();
		Date time0 = trigger.getStartTime();
		Date time1 = trigger.getFireTimeAfter(time0);
		return time1.getTime();
	}

	/**
	 * 获取到设定的间隔时间的开启结束时间点
	 * @param timePoint 这个参数是传入一个时间戳 这个时间戳对于表达式来说就是当前时间
	 * @return
	 */
	public static long getNextTriggerTimeByFixedTime(long timePoint, String cron){
		if(!CronExpression.isValidExpression(cron)){
			return 0;
		}
		CronTrigger trigger = TriggerBuilder.newTrigger().startAt(new Date(timePoint)).withIdentity("Caclulate Date").withSchedule(CronScheduleBuilder.cronSchedule(cron)).build();
		Date time0 = trigger.getStartTime();
		Date time1 = trigger.getFireTimeAfter(time0);
		return time1.getTime();
	}

	public static long cronGetNextTime(String cron){
		if (!CronExpression.isValidExpression(cron)) {
			return 0;
		}
		CronTrigger trigger = TriggerBuilder.newTrigger().withIdentity("Caclulate Date")
				.withSchedule(CronScheduleBuilder.cronSchedule(cron)).build();
		Date time0 = trigger.getStartTime();
		Date time1 = trigger.getFireTimeAfter(time0);
		return time1.getTime();
	}

	public static String utilBaseGenSqlWhere(Map<String, Object> paramsFilter) {
		//SQL语句
		StringBuilder sqlWhere = new StringBuilder(34);
		//无参数 立即返回
		if(paramsFilter.isEmpty()) return sqlWhere.toString();

		//将参数拼装为?占位符的形式
		for(Entry<String, Object> e : paramsFilter.entrySet()) {
			String key = e.getKey();
			Object val = e.getValue();

			//需要增加and分割
			if(sqlWhere.length() > 0) sqlWhere.append(" AND ");

			//属性名
			sqlWhere.append("`").append(key).append("`");

			//属性值 如果是List则判定为in语句
			if(val instanceof Collection) {
				Collection<?> vals = (Collection<?>) val;
				//拼装in
				sqlWhere.append(" in ").append("(");
				for(int i = 0; i < vals.size(); i++) {
					if(i > 0) sqlWhere.append(",");
					sqlWhere.append("?");
				}
				sqlWhere.append(")");
			} else {		//默认为相等
				sqlWhere.append("=").append("?");
			}
		}

		//在头部插入where语句
		sqlWhere.insert(0, " WHERE ");

		return sqlWhere.toString();
	}

	/**
	 * 类型1,每日固定点重置: [1,22] 		-- 每日22点重置
	 * 类型2,每周固定点重置: [2,1,5]		-- 每周周一5点重置
	 * 类型3,每月固定点重置: [3,12,22]		-- 每月的12号22点重置
	 * @return
	 */
	public static List<Long> refreshTime(int[] refresh){
		long timeNow = System.currentTimeMillis();
		// 现在时间对应的重置时间点
		long thisResetTime = 0;
		// 上一个重置的时间点
		long lastResetTime = 0;
		// 下一个重置的时间点
		long nextResetTime = 0;
		//每日整点更新
		List<Long> time = new ArrayList<>();
		int type = refresh[0];
		if(type == 1) {
			if(refresh.length <2) {
				return time;
			}
			//今天的刷新时间点
			Calendar ca = Calendar.getInstance();
			ca.clear();// clear一下或set一下data，a.set(Calendar.DATE, 1);
			ca.setTimeInMillis(timeNow);
			ca.set(Calendar.HOUR_OF_DAY, refresh[1]);
			ca.set(Calendar.MINUTE, 0);
			ca.set(Calendar.SECOND, 0);
			ca.set(Calendar.MILLISECOND, 0);
			thisResetTime = ca.getTimeInMillis();
			ca.add(Calendar.DAY_OF_MONTH, -1);
			lastResetTime = ca.getTimeInMillis();
			ca.add(Calendar.DAY_OF_MONTH, 2);
			nextResetTime = ca.getTimeInMillis();


		} else if (type == 2) {
			if(refresh.length <3) {
				return time;
			}

			Calendar ca = Calendar.getInstance();
			ca.clear();// clear一下或set一下data，a.set(Calendar.DATE, 1);
			ca.setTimeInMillis(timeNow);
			int weekDay = refresh[1] + 1;
			if (weekDay > 7){
				weekDay = 1;
			}
			ca.set(Calendar.DAY_OF_WEEK, weekDay); //周几
			ca.set(Calendar.HOUR_OF_DAY, refresh[2]);//小时
			ca.set(Calendar.MINUTE, 0);
			ca.set(Calendar.SECOND, 0);
			ca.set(Calendar.MILLISECOND, 0);

			thisResetTime = ca.getTimeInMillis();
			//上一周的重置时间点
			ca.add(Calendar.WEEK_OF_MONTH, -1);
			lastResetTime = ca.getTimeInMillis();
			ca.add(Calendar.WEEK_OF_MONTH, 2);
			nextResetTime = ca.getTimeInMillis();

			//每月几号，几点更新
		}else if(type == 3) {
			if(refresh.length <3) {
				return time;
			}
			//重置的时间点
			Calendar ca = Calendar.getInstance();
			ca.clear();// clear一下或set一下data，a.set(Calendar.DATE, 1);
			ca.setTimeInMillis(timeNow);
			ca.set(Calendar.DAY_OF_MONTH,refresh[1]);
			ca.set(Calendar.HOUR_OF_DAY, refresh[2]);
			ca.set(Calendar.MINUTE, 0);
			ca.set(Calendar.SECOND, 0);
			ca.set(Calendar.MILLISECOND, 0);
			thisResetTime = ca.getTimeInMillis();
			//上个月的重置点
			ca.add(Calendar.MONTH, -1);
			lastResetTime = ca.getTimeInMillis();
			ca.add(Calendar.MONTH, 2);
			nextResetTime = ca.getTimeInMillis();
		}
		time.add(lastResetTime);
		time.add(thisResetTime);
		time.add(nextResetTime);
		return time;
	}
	
	/**
	 * 获取指定时间戳的本周几的几点时间戳
	 * 
	 * @param time 给定的时间
	 * @param day 周几[1周一,7周日]
	 * @param hour 小时[0,23]
	 * @return
	 */
	public static long getTimeOfWeek(long time, int day, int hour) {
		ZonedDateTime zdt = ZonedDateTime.ofInstant(Instant.ofEpochMilli(time), ZoneId.systemDefault());
		return zdt.with(ChronoField.DAY_OF_WEEK, day).withHour(hour).withMinute(0).withSecond(0).withNano(0).toInstant()
				.toEpochMilli();
	}

	public static long getTimeOfWeekSec(long time, int week, int sumSec) {
		int hour = sumSec / 3600;
		int min = (sumSec - hour * 3600) / 60;
		int sec = sumSec - hour * 3600 - min * 60;
		ZonedDateTime zdt = ZonedDateTime.ofInstant(Instant.ofEpochMilli(time), ZoneId.systemDefault());
		return zdt.with(ChronoField.DAY_OF_WEEK, week).withHour(hour).withMinute(min).withSecond(sec).withNano(0).toInstant()
				.toEpochMilli();
	}

	
	/**
	 * 取多个不重复的随机获得数组中的值
	 *
	 * @param array
	 * @return
	 */
	public static List<String> randomNonRepeatList(String[] array, int num) {
		List<String> tList = new ArrayList<>();
		int first = random(array.length);
		tList.add(array[first]);
		for (int i = 2; i <= num; i++) {
			String next = randomExclude(array, tList);
			tList.add(next);
		}
		return tList;
	}

	/** 
	 * list集合中随机num个不重复的数据（注意paramList中数据本身就不重复）
	 * <AUTHOR>
	 * @Date 2021/12/2
	 * @Param
	 */
	public static List randomNonRepeatList(List paramList, int num) {
		List<Object> randomList = new ArrayList<>();
		if(paramList == null){
			return randomList;
		}
		int size = paramList.size();
		if(size <= num){
			randomList.addAll(paramList);
			return randomList;
		}
		List<Integer> indexList = new ArrayList<>();
		int index = 0;
		for (int i = 0; i < num; i++) {
			index = random(size);
			if(!indexList.contains(index)){
				indexList.add(index);
				randomList.add(paramList.get(index));
			} else {
				i--;
			}
		}
		return randomList;
	}

	public static List<Integer> randomNonRepeatIntList(int randomMax, int num) {
		List<Integer> randomList = new ArrayList<>();
		if(randomMax < num){
			for(int i = 1; i <= randomMax; i++){
				randomList.add(i);
			}
			return randomList;
		}
		Set<Integer> set = new HashSet<>();
		for (int i = 0; i < num; i++) {
			int value;
			do {
				value = random(randomMax) + 1;
			} while (set.contains(value));
			set.add(value);
		}
		randomList.addAll(set);
		return randomList;
	}


	/**
	 * 取数组下标在[0,max)之间的随机值，排除指定值
	 *
	 * @param array
	 * @param exclude
	 * @return
	 */
	public static String randomExclude(String[] array, List<String> exclude) {
		List<String> tList = new ArrayList<>();
		for (String t : array) {
			if (!exclude.contains(t)) {
				tList.add(t);
			}
		}
		if (!tList.isEmpty()) {
			int index = random(tList.size());
			return tList.get(index);
		} else {
			return "";
		}
	}

	/** 
	 * 随机获取0-max之间num个不重复值的数量的集合(相当于获取的是下标)
	 * <AUTHOR>
	 * @Date 2021/9/9
	 * @Param
	 */
	public static List<Integer> randomNonRepeatList(int max, int num) {
		List<Integer> tList = new ArrayList<>();
		if(max <= num){
			for(int i = 0; i < max; i++){
				tList.add(i);
			}
			return tList;
		}
		tList.add(random(max));
		for (int i = 2; i <= num; i++) {
			int next = randomExclude(max, tList);
			if(!tList.contains(next)){
				tList.add(next);
			}
		}
		return tList;
	}

	/**
	 * 取数组下标在[0,max)之间的随机值，排除指定值
	 * @return
	 */
	public static int randomExclude(int max, List<Integer> tList) {
		int loopNum = 2 * max;
		int random = random(max);
		while (tList.contains(random)){
			random = random(max);
			loopNum --;
			if(loopNum <= 0){
				break;
			}
		}
		if(!tList.contains(random)){
			return random;
		} else {
			return 0;
		}
	}

	public static int randomByWeight(List<Integer> weightList) {
		int sum = weightList.stream().mapToInt(Integer::intValue).sum();
		int random = random(sum);
		for (int i = 0; i < weightList.size(); i++) {
			int weight = weightList.get(i);
			if (random < weight) {
				return i;
			}
			random -= weight;
		}
		return 0;
	}

	/**
	 * 传入权重数组随机获取数组下标
	 * @param
	 * @return index
	 */
	public static int randomByWeight(int[] weight) {
		int sum = 0;
		for (int i = 0; i < weight.length; i++) {
			sum += weight[i];
		}
		int random = random(sum);
		for (int i = 0; i < weight.length; i++) {
			if (random < weight[i]) {
				return i;
			}
			random -= weight[i];
		}
		return 0;
	}

	/**
	 * 传入二维权重数组随机获取
	 * @param index 二维权重索引
	 * @return 一维结果索引
	 */
	public static int randomByWeight2D(int[][] weight,int index) {
		int sum = 0;
		for (int i = 0; i < weight.length; i++) {
			sum += weight[i][index];
		}
		int random = random(sum);
		for (int i = 0; i < weight.length; i++) {
			if (random < weight[i][index]) {
				return i;
			}
			random -= weight[i][index];
		}
		return 0;
	}

	/**
	 * 传入二维权重数组随机获取
	 * @param weight 二维权重数组
	 * @param weightIndex 权重索引
	 * @param resultIndex 返回结果索引
	 * @return 传入二维权重数组随机获取其中一个数组, 根据结果索引返回结果
	 */
	public static int randomByWeight2D(int[][] weight, int weightIndex, int resultIndex) {
		int sum = 0;
		for (int[] ints : weight) {
			sum += ints[weightIndex];
		}
		int random = random(sum);
		for (int i = 0; i < weight.length; i++) {
			if (random < weight[i][weightIndex]) {
				return weight[i][resultIndex];
			}
			random -= weight[i][weightIndex];
		}
		return 0;
	}


	/**
	 * 获取合服前玩家所在服务器id
	 * @param humanId
	 * @return
	 */
	public static int getServerIdOldByHumanId(long humanId) {
		return (int) (humanId % IdAllotPoolBase.PLATFORM_POW / IdAllotPoolBase.SERVER_ID_POW_13) + Utils.intValue(Config.GAME_SERVER_PREFIX);
	}

	/**
	 * 获取玩家旧的服务器id(创建在哪个服id)
	 * <AUTHOR>
	 * @Date 2024/1/9
	 * @Param
	 */
	public static int getOldServerId(long humanId) {
		int serverIdOldByHumanId = getServerIdOldByHumanId(humanId);
		return serverIdOldByHumanId;
	}

	/**
	 * 获取当前玩家所在服务器id，因为合服后玩家所在的服务器id会改变
	 *
	 * @param humanId
	 * @return
	 */
	public static int getServerIdByHumanId(long humanId) {
		int serverIdOldByHumanId = getServerIdOldByHumanId(humanId);
		if (serverIdOldByHumanId == 0) {
			return 0;
		}
		return getServerTagId(serverIdOldByHumanId);
	}


	/**
	 * 获取当前日期零点时间
	 * <AUTHOR>
	 * @param time
	 * @return
	 */
	public static long curDayZeroTime(long time){
		return getTimeBeginOfToday(time);
	}

	/**
	 * 判断时间是否为今日
	 * <AUTHOR>
	 * @param time
	 * @return
	 */
	public static boolean isToday(long time){
		long todayStartTime = curDayZeroTime(Port.getTime());
		long todayEndTime = todayStartTime + Time.DAY;

		if( time >= todayStartTime && time < todayEndTime){
			return true;
		}
		return false;
	}

	public static int intArrayToIndex(int[] ints, int v1){
		for (int i = 0; i < ints.length; i++){
			if (v1 == ints[i]){
				return i;
			}
		}
		return -1;
	}

	/**
	 * TODO 获取本月第几周
	 *@date 2021/8/25 10:57
	 *@param
	 *@return int
	*/
	public static int getWeekByMonth(){
			Calendar calendar = Calendar.getInstance();
			calendar.setTimeInMillis(System.currentTimeMillis());
			calendar.setFirstDayOfWeek(Calendar.MONDAY);
			calendar.setTimeInMillis(System.currentTimeMillis());
			//第几周
			return calendar.get(Calendar.WEEK_OF_MONTH);
	}

	public static int strArrayToIndex(String[] ints, String v1){
		for (int i = 0; i < ints.length; i++){
			if (v1.equals(ints[i])){
				return i;
			}
		}
		return -1;
	}

	/*
	 * 判断是否为整数
	 * @param str 传入的字符串
	 * @return 是整数返回true,否则返回false
	 */
	public static boolean isInteger(String str) {
		Pattern pattern = Pattern.compile("^[-\\+]?[\\d]*$");
		return pattern.matcher(str).matches();
	}

	/**
	 * 获取数组 前多少个之和包含index
	 * @param ints
	 * @param index
	 * @return
	 */
	public static int getArrayIndexAgoNum(int[] ints,int index) {
		int num = 0;
		if(ints == null){
			return num;
		}
		for (Integer i = 0; i < ints.length; i++) {
			if (i > index) {
				break;
			}
			num+=ints[i];
		}
		return num;
	}


	/**
	 * 数组转list
	 * @param ints
	 * @return
	 */
	public static List<Integer> intArrToList(int[] ints) {
		List<Integer> gatherSns = Arrays.stream(ints).boxed().collect(Collectors.toList());
		return gatherSns;
	}

	/** 
	 * 解析字符串成奖励
	 * <AUTHOR>
	 * @Date 2022/5/9
	 * @Param 
	 */
	public static void splitStr(String strValue, String oneSplit, String towSplit, List<ProduceVo> produceVoList){
		String[] arrStr = Utils.splitStr(strValue, oneSplit);
		for(int i = 0; i < arrStr.length; i++){
			int[] arr = Utils.strToIntArray(arrStr[i], towSplit);
			for(int m = 0; m < arr.length; m+=2){
				produceVoList.add(new ProduceVo(arr[m], arr[m+1]));
			}
		}
	}

	public static boolean isMyServer(){
		if(S.isBridge){
			return false;
		}
		if("************".equals(Config.GAME_SERVER_IP)){
			return true;
		}
		return false;
	}

	/**
	 * int相乘先转long
	 * <AUTHOR>
	 * @Date 2023/9/6
	 * @Param
	 */
	public static int intMultiply(int a, int b, int c){
		long value = 1L * a * b / c;
		if(value > Integer.MAX_VALUE){
			Log.temp.error("数值超了，a={}, b={}, c={}, value={}", a, b, c, value);
		}
		return (int)value;
	}

	/** 
	 * 根据服务器id获取游戏服地址（目前用于中心服找游戏服）
	 * <AUTHOR>
	 * @Date 2023/9/13
	 * @Param 
	 */
	public static String getNodeId(int serverId) {
		if(Utils.isDebugMode()){
			return Utils.createStr("{}{}", D.NODE_WORLD_BRIDGE_PREFIX, 2001);
		}

		//寻找合服后的标准id
		return Util.getNodeId(serverId);
	}

	/**
	 * 根据服务器id获取游戏服地址集合（目前用于中心服找游戏服）
	 * <AUTHOR>
	 * @Date 2023/9/13
	 * @Param
	 */
	public static Map<String, String> getServerIdNodeIdMap(){
		return ServerList.getServerIdNodeIdMap();
	}

	public static boolean isCronSpecialSymbol(String str) {
		// cron表达式月，时，分，秒是否包含特殊字符（排除逗号）
		if(str.contains("*") || str.contains("/")|| str.contains("-") || str.contains("?")){
			return true;
		}
		return false;
	}

	/**
	 * 修改cron表达式提前几分钟（注意混沌玩法专用，表达式的修改规则只是特定需求而修改的）
	 * <AUTHOR>
	 * @Date 2023/10/8
	 * @Param
	 */
	public static String cronNew(String cronStr, int earlyMin){
		// Cron表达式空格隔开，每个域的含义是固定的
		String cronNew = "";
		String[] arrStr = splitStr(cronStr," ");
		if(isCronSpecialSymbol(arrStr[1])){
			// TODO 目前还没用到，不处理
			Log.temp.error("===cron表达式处理出错，未实现代码。cronStr={}, earlyMin={}", cronStr, earlyMin);
			return cronNew;
		}
		// 默认不存在逗号。功能设计如此。
		String minStr = arrStr[1];
		if(minStr.contains(",")){
			Log.temp.error("===cron表达式处理出错，配置出问题了，未实现代码。cronStr={}, earlyMin={}", cronStr, earlyMin);
			return cronNew;
		}

		int min = intValue(minStr);
		if(min > earlyMin){
			arrStr[1] = String.valueOf(min-earlyMin);
			return arrStrToStr(arrStr, " ");
		}
		int num = (int)Math.ceil(earlyMin / 60.0d);
		int minNew = 60 * num + min - earlyMin;
		if(minNew >= 60){
			minNew -= 60;
			num--;
		}
		if(minNew < 0){
			minNew += 60;
			num ++;
		}
		// 分钟上数字小于提前的分钟数
		arrStr[1] = String.valueOf(minNew);
		Log.temp.info("===cron={}, earlyMin={}, minNew={} num={}", cronStr, earlyMin, minNew, num);
		if(isCronSpecialSymbol(arrStr[2])){
			// TODO 目前还没用到，不处理
			Log.temp.error("===cron表达式处理出错，未实现代码。cronStr={}, earlyMin={}", cronStr, earlyMin);
			return cronNew;
		}
		int[] hourArr = strToIntArray(arrStr[2],",");
		int[] hourArrNew = new int[hourArr.length];
		for(int i= 0; i< hourArr.length; i++){
			int hour = hourArr[i]-num;
			if(hour < 0){
				hour += 24;
			}
			hourArrNew[i] = hour;
		}
		arrStr[2] = Utils.arrayIntToStr(hourArrNew);
		return arrStrToStr(arrStr, " ");
	}

	/** 
	 * 字符串数组替换特殊符号转字符串
	 * <AUTHOR>
	 * @Date 2023/10/8
	 * @Param 
	 */
	public static String arrStrToStr(String[] arrStr, String symbol){
		String strNew = "";
		for(int i = 0; i < arrStr.length; i++){
			if(i==0){
				strNew = arrStr[i];
				continue;
			}
			strNew = Utils.createStr("{}{}{}", strNew, symbol, arrStr[i]);
		}
		return strNew;
	}

	/** 
	 * 是否是同一组
	 * <AUTHOR>
	 * @Date 2024/1/4
	 * @Param 
	 */
	public static boolean isServerIdGroup(long humanId, int serverId) {
		if(Utils.isDebugMode()){
			return true;
		}
		int serverIdOldByHumanId = getOldServerId(humanId);

		int group1 = Util.getServerIdGroup(serverIdOldByHumanId);
		int group2 = Util.getServerIdGroup(serverId);
		return group1 == group2;
	}

	public static boolean isServerIdZoneCode(long humanId, int serverId) {
		if(Utils.isDebugMode()){
			return true;
		}
		int serverIdOldByHumanId = getOldServerId(humanId);
		int zoneCode1 = Util.getServerIdGroup(serverIdOldByHumanId);
		int zoneCode2 = Util.getServerIdGroup(serverId);
		return zoneCode1 == zoneCode2;
	}

	public static Map<Integer, Integer> intArrToIntMap(int[] arr){
		Map<Integer, Integer> map = new HashMap<>();
		if(arr.length % 2 != 0){
			return map;
		}
		for(int i = 0; i < arr.length; i+=2){
			int key = arr[i];
			int value = arr[i+1];
			map.put(key, map.getOrDefault(key, 0) + value);
		}
		return map;
	}

	public static Map<Integer, Integer> intArrToIntMap(Map<Integer, Integer> map, int[] arr){
		if(arr.length % 2 != 0){
			return map;
		}
		for(int i = 0; i < arr.length; i+=2){
			int key = arr[i];
			int value = arr[i+1];
			map.put(key, map.getOrDefault(key, 0) + value);
		}
		return map;
	}

	public static Map<Integer, Integer> intArrToIntMap(Map<Integer, Integer> map, int[][] arr){
		if(arr == null || arr.length == 0){
			return map;
		}
		for(int i = 0; i < arr.length; i++){
			int[] intArr = arr[i];
			for(int m = 0; m < intArr.length; m+=2){
				int key = intArr[m];
				int value = intArr[m + 1];
				map.put(key, map.getOrDefault(key, 0) + value);
			}
		}
		return map;
	}

	public static Map<Integer, Integer> intArrToIntMap(Map<Integer, Integer> map, int[][] arr, double multiple){
		if(arr == null || arr.length == 0){
			return map;
		}
		for(int i = 0; i < arr.length; i++){
			int[] intArr = arr[i];
			for(int m = 0; m < intArr.length; m+=2){
				int key = intArr[m];
				int value = Utils.intValue(intArr[m + 1] * multiple);
				map.put(key, map.getOrDefault(key, 0) + value);
			}
		}
		return map;
	}

	public static Map<Integer, Integer> intArrToIntMap(int[] arr, int multiple){
		Map<Integer, Integer> map = new HashMap<>();
		if(arr.length % 2 != 0){
			return map;
		}
		for(int i = 0; i < arr.length; i+=2){
			int key = arr[i];
			int value = arr[i+1] * multiple;
			map.put(key, map.getOrDefault(key, 0) + value);
		}
		return map;
	}

	public static int[][] scaleIntArr(int[][] arr, double multiple) {
		// 创建一个新的二维数组来存储修改后的值
		int[][] result = new int[arr.length][];

		for (int i = 0; i < arr.length; i++) {
			// 对每个一维数组进行处理，创建一个新的数组
			result[i] = new int[arr[i].length];

			// 复制原始数组的内容，并在必要时进行缩放
			for (int j = 0; j < arr[i].length; j += 2) {
				int key = arr[i][j];
				result[i][j] = key;  // 直接赋值
				result[i][j + 1] = (int)(arr[i][j + 1] * multiple);  // 缩放第二个元素
			}
		}
		return result;
	}



	public static Map<Integer, Integer> mergeMap(Map<Integer, Integer> mapMerge, Map<Integer, Integer> map){
		if(mapMerge == null){
			return map;
		}
		for(Map.Entry<Integer, Integer> entry : map.entrySet()){
			int key = entry.getKey();
			int value= entry.getValue();
			mapMerge.put(key, mapMerge.getOrDefault(key, 0) + value);
		}
		return mapMerge;
	}

	public static float[] parseFloatArray(String value) {
		if(value == null) value = "";
		if(StringUtils.isEmpty(value)){
			return null;
		}
		String[] elems = value.split("\\|");
		if(value.contains(",")){
			elems = value.split("\\,");
		}
		if(elems != null && elems.length > 0) {
			float []temp = new float[elems.length] ;
			for(int i = 0 ; i < elems.length ; i++) {
				temp[i] = Utils.floatValue(elems[i]);
			}
			return temp;
		}
		return null;
	}


	public static float[][] parseFloatArray2(String value) {
		if(value == null) value = "";
		if(StringUtils.isEmpty(value)){
			return null;
		}

		float[][] elems = null;
		String [] strArr = Utils.splitStr(value, "\\|");
		if(strArr != null && strArr.length > 0){
			for(int i = 0; i < strArr.length; i++){
				float[] floatArr = parseFloatArray(strArr[i]);
				if(elems == null){
					elems = new float[strArr.length][floatArr.length];
				}
				elems[i] = new float[floatArr.length];
				for(int m = 0; m < floatArr.length; m++) {
					elems[i][m]=floatArr[m];
				}
			}
			return elems;
		}
		return null;
	}

	public static int[][] parseIntArray2(String[] values) {
		int[][] elems = new int[values.length][];
		for (int i = 0; i < values.length; i++) {
			elems[i] = Utils.arrayStrToInt(values[i]);
		}
		return elems;
	}

	public static int[][] parseIntArray2(String value) {
		if(value == null) value = "";
		if(StringUtils.isEmpty(value)){
			return null;
		}

		int[][] elems = null;
		String [] strArr = Utils.splitStr(value, "\\|");
		if(strArr != null && strArr.length > 0){
			for(int i = 0; i < strArr.length; i++){
				int[] intArr = Utils.arrayStrToInt(strArr[i]);
				if(elems == null){
					elems = new int[strArr.length][intArr.length];
				}
				elems[i] = new int[intArr.length];
				for(int m = 0; m < intArr.length; m++) {
					elems[i][m]=intArr[m];
				}
			}
			return elems;
		}
		return null;
	}

	public static String[][] parseStrArray2(String value) {
		if(value == null) value = "";
		if(StringUtils.isEmpty(value)){
			return null;
		}

		String[][] elems = null;
		String [] strArr = Utils.splitStr(value, "\\|");
		if(strArr != null && strArr.length > 0){
			for(int i = 0; i < strArr.length; i++){
				String[] intArr = strArr[i].split(",");
				if(elems == null){
					elems = new String[strArr.length][intArr.length];
				}
				elems[i] = new String[intArr.length];
				for(int m = 0; m < intArr.length; m++) {
					elems[i][m]=intArr[m];
				}
			}
			return elems;
		}
		return null;
	}

	public static String intArray2ToStr(int[][] values) {
		StringBuilder sb = new StringBuilder();
		for (int i = 0; i < values.length; i++) {
			if (i != 0) {
				sb.append("|");
			}
			for (int j = 0; j < values[i].length; j++) {
				if (j != 0) {
					sb.append(",");
				}
				sb.append(values[i][j]);
			}
		}
		return sb.toString();
	}

	/**
	 * 根据字符串获取长度（双字节）
	 * <AUTHOR>
	 * @Date 2023/8/21
	 * @Param
	 */
	public static int length(String str){
		double length = 0;
		if(str == null || str.isEmpty()){
			return 0;
		}
		String chinese = "[\u0391-\uFFE5]";// 双字节编码范围
		for(int i = 0; i < str.length(); i++){
			String temp = str.substring(i, i+1);
			if(temp.matches(chinese)){
				length +=1;
			} else {
				length +=0.5;
			}
		}
		return (int)Math.ceil(length);
	}
	/*
	 * 消息转成字符串
	 */
	public static <T extends GeneratedMessageV3> String toProtoString(T message) {
		return Base64.getEncoder().encodeToString(message.toByteArray());
	}

	public static Map<Integer, Map<Long, Long>> battleNumGuildIdMap(List<Long> pairGuildIdList){
		Map<Integer, Map<Long, Long>> battleNumGuildIdMap = new HashMap<>();
		int size = pairGuildIdList.size();
		Map<Long, List<Long>> idIdListMap = new HashMap<>();

		for(int m = 0; m < size; m+=2){
			long guildId1 = pairGuildIdList.get(m);
			long guildId2 = 0;
			if(m + 1 < size){
				guildId2 = pairGuildIdList.get(m + 1);
			}

			List<Long> idListNew = new ArrayList<>();
			idListNew.add(guildId2);
			idIdListMap.put(guildId1, idListNew);

			if(guildId2 > 0){
				List<Long> idListNew2 = new ArrayList<>();
				idListNew2.add(guildId1);
				idIdListMap.put(guildId2, idListNew2);
			}
			Map<Long, Long> mapNew = battleNumGuildIdMap.get(GuildParamKey.battleNum_1);
			if(mapNew == null){
				mapNew = new HashMap<>();
			}
			mapNew.put(guildId1, guildId2);
			battleNumGuildIdMap.put(GuildParamKey.battleNum_1, mapNew);
		}
		for(int i = GuildParamKey.battleNum_2; i <= GuildParamKey.sumBattleNum; i++){
			Map<Long, Long> mapNew = new HashMap<>();
			battleNumGuildIdMap.put(i, mapNew);
			for(int m = 0; m < size; m++){
				long guildId = pairGuildIdList.get(m);
				if(mapNew.containsKey(guildId) || mapNew.containsValue(guildId)){
					continue;
				}
				List<Long> idListNew = new ArrayList<>(pairGuildIdList);
				List<Long> useIdList = idIdListMap.get(guildId);
				idListNew.removeAll(useIdList);
				idListNew.remove(guildId);
				idListNew.removeAll(mapNew.keySet());
				idListNew.removeAll(mapNew.values());

				if(idListNew.isEmpty()){
					mapNew.put(guildId, 0L);
					continue;
				}
				long enemyId = idListNew.get(0);
				mapNew.put(guildId, enemyId);
				useIdList.add(enemyId);
				List<Long> useIdList2 = idIdListMap.get(enemyId);
				useIdList2.add(guildId);
			}
		}
		return battleNumGuildIdMap;
	}

//	public static String encode(String raw) {
//		byte[] digest;
//		try {
//			// MessageDigest#digest not thread safe, so can't be singleton
//			digest = MessageDigest.getInstance("MD5").digest(raw.getBytes("UTF-8"));
//		} catch (UnsupportedEncodingException e) {
//			logger.error(raw, e);
//			throw new IllegalStateException("UTF-8 not supported!");
//		} catch (NoSuchAlgorithmException e) {
//			logger.error(raw, e);
//			throw new IllegalArgumentException("No such algorithm [MD5]");
//		}
//		return new String(Hex.encodeHex(digest));
//	}

	public static String encode(String raw) {
		byte[] digest;
		try {
			// 使用StandardCharsets.UTF_8而不是"UTF-8"字符串，这是一个更好的做法
			digest = MessageDigest.getInstance("MD5").digest(raw.getBytes(StandardCharsets.UTF_8));
		} catch (Exception e) {
			// 这通常不会发生，因为MD5是Java标准提供的算法
			throw new IllegalArgumentException("No such algorithm [MD5]", e);
		}
		// 直接使用Hex.encodeHexString而不是先转换为byte[]再转换为String
		return byteArrayToHex(digest);
	}
	private static final char[] CH_HEX = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};
	private static String byteArrayToHex(byte[] bytes) {
		// 一个字节占8位，一个十六进制字符占4位；十六进制字符数组的长度为字节数组长度的两倍
		char[] chars = new char[bytes.length * 2];
		int index = 0;
		for (byte b : bytes) {
			// 取字节的高4位
			chars[index++] = CH_HEX[b >>> 4 & 0xf];
			// 取字节的低4位
			chars[index++] = CH_HEX[b & 0xf];
		}
	   	return new String(chars);
    }

	/*
	 * 字符串转成消息
	 */
	public static <T extends GeneratedMessageV3> T fromProtoString(String protoString, Class<T> clazz) {
		try {
			byte[] data = Base64.getDecoder().decode(protoString);
			Method method = clazz.getDeclaredMethod("parser");
			Parser<T> parser = (Parser<T>) method.invoke(null);
			return parser.parseFrom(data);
		} catch (Exception e) {
			throw new RuntimeException("Failed to parse protobuf string", e);
		}
	}

	private static void ssstest(Object... objs){

		Object[] newObjs = new Object[objs.length+1];
		newObjs[0] = 1;
		System.arraycopy(objs, 0, newObjs, 1, objs.length);

		System.out.println(Arrays.toString(newObjs));
	}
	public static int getHeroId(long id){
		return (int) (id % IdAllotPoolBase.PLATFORM_POW % IdAllotPoolBase.SERVER_ID_POW_13);
	}
	private  static boolean isSameSkill(Define.p_passive_skill a, Define.p_passive_skill b) {
		return a.getSkillId() == b.getSkillId() && a.getSkillLv() == b.getSkillLv();
	}

	public static void main(String[] args) {
		if(true){
			int multiFinal = 5000;
			for(int i = -30; i < 30; i++){
				int value = (int)Math.ceil(i * multiFinal / Utils.I10000);
				System.out.println("====i="+i+ "====value="+value);
			}
			return;
		}

		if(true){
			List<Define.p_passive_skill> updateList = new ArrayList<>();
			updateList.add(Define.p_passive_skill.newBuilder().setSkillId(1).setSkillLv(1).build());
			updateList.add(Define.p_passive_skill.newBuilder().setSkillId(3).setSkillLv(3).build());

			List<Define.p_passive_skill> delList = new ArrayList<>();
			delList.add(Define.p_passive_skill.newBuilder().setSkillId(1).setSkillLv(1).build());
			delList.add(Define.p_passive_skill.newBuilder().setSkillId(3).setSkillLv(1).build());

//				List<Define.p_passive_skill> removeList = new ArrayList<>();
//				for(Define.p_passive_skill skill : delList){
//					for(Define.p_passive_skill info : updateList){
//						if(skill.getSkillId() == info.getSkillId() && skill.getSkillLv() == info.getSkillLv()){
//							removeList.add(skill);
//						}
//					}
//				}
//				if(!removeList.isEmpty()){
//					delList.removeAll(removeList);
//				}
			if (updateList != null && delList != null && !updateList.isEmpty() && !delList.isEmpty()) {
				Set<Define.p_passive_skill> updateSet = new HashSet<>(updateList);
				delList.removeIf(skill -> updateSet.stream().anyMatch(
						updatedSkill -> isSameSkill(skill, updatedSkill)
				));
			}
			System.out.println("updateList="+updateList);
			System.out.println("delList="+delList);

			return;
		}
		if(true){
			System.out.println(getHeroId(600140000000000002L));
			System.out.println(getHeroId(600140000000000003L));
			LocalDate today = LocalDate.now();
			LocalDate firstMonday = today.with(TemporalAdjusters.firstInMonth(DayOfWeek.MONDAY));
			System.out.println("本月第一个周一的日期是: " + firstMonday);

			String str = "6.3298130974581811e+17";
			long value = longValue(str);
			System.out.println(value);
			return;
		}
		if(true){


			String textWithEmoji = "Ｋ";
			System.out.println(removeEmojis(textWithEmoji));
			List<Integer> serverList = new ArrayList<>();
			serverList.add(1);
			serverList.add(2);
			serverList.add(3);
			serverList.sort((g1, g2) -> {
				int ret = 0;// 0默认相等
				if(g1 == 1){
					return -1;
				}
				if(g2 == 1){
					return -1;
				}
				if (g2 < g1) {
					ret = -1;
				} else if (g2 > g1) {
					ret = 1;
				}
				return ret;
			});
			System.out.println(serverList);

			return;
		}
		if(true){
			for(int m = 0; m < 1; m++){
				int roomNum = 6;
				int size = 276;
				int humanNum = 50;
				List<Long> humanIdList = new ArrayList<>();
				for(int i = 0; i < size; i++){
					humanIdList.add(i+1L);
				}
				for(int i = 0; i < roomNum; i++){
					int min = Math.min(i * humanNum, humanIdList.size());
					int max = Math.min((i + 1) * humanNum, humanIdList.size());
					int room = i + 1;
					if(room == roomNum - 1){
						int addNum = (int)Math.ceil((humanIdList.size() - i * humanNum) / 2);
						List<Long> idList = humanIdList.subList(min, min + addNum);
						System.out.println("==========min="+min+ "=======max="+ min + addNum);
						System.out.println("==========room="+room+ "=======idList="+idList);
					} else if(room == roomNum){
						int addNum = (int)Math.ceil((humanIdList.size() - (i - 1) * humanNum) / 2);
						min = humanNum * (i - 1) + addNum;
						List<Long> idList = humanIdList.subList(min, humanIdList.size());
						System.out.println("==========min="+min+ "=======max="+ humanIdList.size());
						System.out.println("==========room="+room+ "=======idList="+idList);
					} else {
						List<Long> idList = humanIdList.subList(min, max);
						System.out.println("==========min="+min+ "=======max="+ max);
						System.out.println("==========room="+room+ "=======idList="+idList);
					}
				}
			}


			long modinit = 0;
			boolean isInit = checkBitValueLong(modinit, 1);
			System.out.println(isInit);

			modinit = setBitValueLong(modinit, 1, 1);
			System.out.println(modinit);

			isInit = checkBitValueLong(modinit, 1);
			System.out.println(isInit);

			modinit = setBitValueLong(modinit, 2, 1);
			System.out.println(modinit);

			isInit = checkBitValueLong(modinit, 2);
			System.out.println(isInit);

			modinit = setBitValueLong(modinit, 2, 0);
			System.out.println(modinit);

			isInit = checkBitValueLong(modinit, 2);
			System.out.println(isInit);


			modinit = setBitValueLong(modinit, 10, 1);
			System.out.println(modinit);

			isInit = checkBitValueLong(modinit, 10);
			System.out.println(isInit);

			System.out.println("====================================");


			int maxt = 3;
			List<Integer> serverList1 = new ArrayList<>();
			serverList1.add(1);
			serverList1.add(2);
			serverList1.add(3);
			serverList1.add(4);
			if(maxt > serverList1.size()){
				maxt = serverList1.size();
			}
			List<Integer> tempList = serverList1.subList(0, maxt);
			System.out.println(tempList);
			tempList = serverList1.subList(3, 4);
			System.out.println(tempList);
			System.out.println(serverList1);
			if(true){
				return;
			}
			long now = 1722502022000L;
			long timeO = Utils.getOffDayTime(now, 0, 0);
			long timeO1 = Utils.getOffDayTime(now, 1, 0);
			long timeO2 = Utils.getOffDayTime(now, 22, 0);
			long timeO3 = Utils.getOffDayTime(now, 28, 0);
			System.out.println(formatTime(timeO, "yyyy-MM-dd HH:mm:ss")+"======"+
					formatTime(timeO1, "yyyy-MM-dd HH:mm:ss")+"======"
					+formatTime(timeO2, "yyyy-MM-dd HH:mm:ss")+ "----------"+
					formatTime(timeO3, "yyyy-MM-dd HH:mm:ss"));


			int endTime = (int) ((Utils.getTimeOfWeek(Port.getTime(), 7, ParamKey.arenaHour23) + ParamKey.arenaMin30 * Time.MIN) / Time.SEC);
			int endTime2 = (int) ((Utils.getTimeOfWeek(Port.getTime(), 6, ParamKey.arenaHour23) + ParamKey.arenaMin30 * Time.MIN) / Time.SEC);
			System.out.println(endTime2+"=endTime="+endTime);
			long openTime1 = Utils.getDayTime(Port.getTime(), 0, 0, 0);
			int endTime3 = (int) ((Utils.getOffDayTime(openTime1, 6, ParamKey.arenaHour23) + ParamKey.arenaMin30 * Time.MIN) / Time.SEC);
			int endTime4 = (int) ((Utils.getOffDayTime(openTime1, 7, ParamKey.arenaHour23) + ParamKey.arenaMin30 * Time.MIN) / Time.SEC);
			System.out.println(endTime3+"=endTime="+endTime4);
			if(true) {
				return;
			}
			List<Integer> serverList = new ArrayList<>();
			serverList.add(1);
			serverList.add(2);
			serverList.add(3);
			List<Integer> serverIdListNow = new ArrayList<>();
			serverIdListNow.add(1);
			serverIdListNow.add(4);
			serverIdListNow.add(2);

			Collection<Integer> diffAdd = (Collection<Integer>) CollectionUtils.subtract(serverIdListNow, serverList);


			System.out.println(diffAdd);
			System.out.println("==============");

			System.out.println(serverIdListNow);

			System.out.println("=========----------");
			System.out.println(serverList);

			ssstest("1", 2, 4.0);


			BigDecimal equipCombat = new BigDecimal("123.1245652");

			System.out.println("openTime1="+equipCombat.longValue());
			System.out.println("kkkkk="+ Utils.longValue(equipCombat.longValue()));

			String s = "2200000000";
			System.out.println("openTime1="+Utils.longValue(s));
			System.out.println("openTime2="+Utils.intValue(Utils.longValue(s)));
			System.out.println("openTime3="+Utils.intValue(s));

			System.out.println("week="+getDayOfWeek());
			long openTime = Utils.getOffDayTime(Port.getTime(), 1 - 1, 0);
			long closeTime  = Utils.getOffDayTime(Port.getTime(), 7, 0);
			System.out.println("openTime="+openTime);
			System.out.println("closeTime="+closeTime);
			if(true){
				return;
			}


			List<Long> humanIdList = new ArrayList<>();
			for(long i = 1; i < 150; i++){
				humanIdList.add(i);
			}
			int roomNum = 3;
			int humanNum = 50;
			int sec = (int)(3 * Time.DAY / Time.SEC);
			for(int i = 0; i < roomNum; i++){
				int min = i * humanNum;
				int max = (i + 1) * 50;
				if(max > humanIdList.size()){
					max = humanIdList.size();
				}
				int room = i + 1;
				List<Long> idList = humanIdList.subList(min, max);
				System.out.println("==========room="+room+ "=======idList="+idList);
			}

			if(true){
				return;
			}

			boolean isWin = true;
			int rivalScore = 80;
			long rivalId = 1;

			int myScore = 100;
			long humanId = 2;

			int arenaAbsScore = 20;
			int arenaWinAddScore = 24;
			int arenaLoseAddScore = -16;
			int min = -30;
			int max = 30;

			boolean isRobot = true;

			int fillScore = 0;
			if(Math.abs(rivalScore - myScore) >= arenaAbsScore){
				fillScore = (int)((rivalScore - myScore) / arenaAbsScore);
			}
			if(!isWin){
				fillScore = -fillScore;
			}
			int loseScore = arenaLoseAddScore - fillScore;
			if(loseScore < min){
				loseScore = min;
			} else if(loseScore > max){
				loseScore = max;
			}
			int winScore = arenaWinAddScore + fillScore;
			if(winScore < min){
				winScore = min;
			} else if(winScore > max){
				winScore = max;
			}

			String rivalIdStr = String.valueOf(rivalId);
			List<String> keysList = new ArrayList<>();
			keysList.add("key");
			if(isWin){
				// 自己胜利
				keysList.add(String.valueOf(myScore + winScore));
				keysList.add(String.valueOf(humanId));
				if(isRobot){
					// 机器人失败-1分
					keysList.add(String.valueOf(rivalScore-1));
				} else {
					keysList.add(String.valueOf(rivalScore + loseScore));
				}
			} else {
				// 自己失败
				keysList.add(String.valueOf(myScore + loseScore));
				keysList.add(String.valueOf(humanId));
				// 机器人失败-1分
				keysList.add(String.valueOf(rivalScore + winScore));
			}
			keysList.add(rivalIdStr);
			System.out.println("==========key="+keysList);
//			Log.temp.info("===keyList={}", keysList);

//			long timeWeekOne = Utils.getTimeOfWeek(Port.getTime(), 1, 0);
//			String dateStr = Utils.formatTime(timeWeekOne, "yyyy-MM-dd");
//			int day = Utils.getDaysBetween(1718976600000L, 1719409130408L);
//			System.out.println(dateStr);
			return;
		}

		String queryString = "https://dldl-zk-back.weiyouxi.com/glapi/1yuan/api/v1?action=recharge&accountid=5721822_gdfTGK&serverid=30001&roleid=600010000000000225&giftid=1001&rechargeid=6q7intkgcghaqondh3206t640o&sign=038f105861726f33a603d1e7cff2fb9b";

		// 提取查询参数（不包括?）
		String params = queryString.substring(queryString.indexOf('?') + 1);
		params = params.substring(0, params.indexOf("&sign="));
//		params = "accountid=5721822_gdfTGK&action=recharge&giftid=1&rechargeid=q2h7m9c2igg8rp9nal0jpvtj20&roleid=***********&serverid=30001";

		// 分割参数对
		String[] paramPairs = params.split("&");


		// 将参数对存储到Map中
		Map<String, String> paramMap = new HashMap<>();

//		paramMap.put("action", "1");
//		paramMap.put("accountid", "1");
//		paramMap.put("roleid", "1");
//				paramMap.put(	"serverid", "1");
//
//		paramMap.put(	"rechargeid",  "1");
//		paramMap.put(	"giftid", "1");
//		paramMap.put(	"content", "1");
//				paramMap.put(	"giftids", "1");
//						paramMap.put("title","1");
		for (String pair : paramPairs) {
			String[] keyValue = pair.split("=");
			if (keyValue.length == 2) {
				paramMap.put(keyValue[0], keyValue[1]);
			}
		}

		// 使用TreeMap来排序Key-Value对
		Map<String, String> sortedParamMap = new TreeMap<>(paramMap);

		// 构建排序后的查询字符串
		StringBuilder sortedQueryString = new StringBuilder();
		for (Map.Entry<String, String> entry : sortedParamMap.entrySet()) {
			sortedQueryString.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
		}
		System.out.println(sortedQueryString.toString());
		// 移除末尾的&
		if (sortedQueryString.length() > 0) {
			sortedQueryString.deleteCharAt(sortedQueryString.length() - 1);
		}
//		sortedQueryString.append("ucr8hojdnjy0comvn2ocuj4wzl8rurto");
		sortedQueryString.append("glgame");

		System.out.println("原来数据： "+(sortedQueryString.toString()));
		System.out.println("md5： "+ md5(sortedQueryString.toString()));

//		System.out.println(md5("accountid=5721822_gdfTGK&action=recharge&giftid=1&rechargeid=q2h7m9c2igg8rp9nal0jpvtj20&roleid=***********&serverid=30001ucr8hojdnjy0comvn2ocuj4wzl8rurto"));
		System.out.println("他的："+ md5("accountid=5721822_gdfTGK&action=recharge&giftid=1001&rechargeid=6q7intkgcghaqondh3206t640o&roleid=600010000000000225&serverid=30001glgame"));
//		String md5Sign = DigestUtils.md5Hex(signStr);

		System.out.println("新的："+encode(sortedQueryString.toString()));

		String signStr = String.format("%s=%s&%s=%s%s",
				"accountid", 1,
				"action", "getServerRole",
				"glgame"
		);
		System.out.println(signStr);


		long time = getTime(2024, 4,16,0,0,0);
		System.out.println("time="+time);
		String stra = "1002,1_8,100_7001,100|1002,1_8,100_7001,100|1002,1_8,100_7001,100|1002,1_8,100_7001,100|1002,1_8,100_7001,100";
		String strs = "1002,1_8,100_7001,100";
		String[] strArra = Utils.splitStr(strs, "\\_");

		System.out.println(md5_2("gameId=5requestTime=**********appkey=123456"));
		System.out.println(md5("gameId=5&requestTime=**********&appkey=123456"));

		int size = strArra.length;
		int[] itemArr = new int[size*2];
//		1002,1_8,100_7001,100
		for(int i = 0; i < size; i++){
			int[] arr = Utils.strToIntArray(strArra[i], "\\,");
			if(arr[0] == TokenItemType.GuildExp){
				continue;
			}
			itemArr[i*2] = arr[0];
			itemArr[i*2+1] = arr[1];
		}

//		long id = Port.applyId();
		List<String> codeList = new ArrayList<>();
		codeList.add("MG9JQ");
		codeList.add("VZW57");
		codeList.add("F099K");
		codeList.add("4OBND");
		codeList.add("NFD8L");
		codeList.add("VHNNM");
		String str111 = Utils.listToString(codeList);
		System.out.println("===========sss==="+str111);
		System.out.println("===========sss==="+str111.getBytes().length);
//		PropCalc propCalc = new PropCalc();
//		ConfPetlevel_0 confPetLv = ConfPetlevel_0.get(2101, 1);
//		propCalc.plus(confPetLv.ownEffect);
//
//		System.out.println(propCalc.toJSONStr());


		// 初始任务
//		TaskInfo taskInfo = new TaskInfo();
//		taskInfo.setId(id);
//		JSONArray ja = new JSONArray();
//		ConfMainTask conf = ConfMainTask.get(1);
//		MainTaskVO vo = new MainTaskVO(conf);
//		ja.add(vo.toString());
//		taskInfo.setMainTaskJSON(ja.toJSONString());
//		taskInfo.persist();

//		TaskManager.inst()._msg_task_commit_c2s(humanObj, msg.getType(), msg.getTaskId());
		if(true){
			return;
		}

		String tempStr = "buff";
		String topS = tempStr.substring(0,1);
		String wS = tempStr.substring(1,tempStr.length());
		String tempKey = topS.toUpperCase() + wS;
		System.out.println("00000000000000==="+tempKey);
		if(tempStr.startsWith("ff") || tempStr.endsWith("ff")){
			System.out.println("00000000000000==="+tempKey);
		}

		BigDecimal b = new BigDecimal("1234234234242324342343557645345643542536453424234");
		long v = 1234212342123421234L;
		System.out.println(Utils.doubleValue(b.toString()));

		String str = "1,2|1,3,4";
		String [] strArr = Utils.splitStr(str, "\\|");
		int[][] elems = null;
		for(int i = 0; i < strArr.length; i++){
			int[] intArr = Utils.arrayStrToInt(strArr[i]);
			if(elems == null){
				elems = new int[strArr.length][];
			}
			elems[i] = new int[intArr.length];
			for(int m = 0; m < intArr.length; m++) {
				elems[i][m]=intArr[m];
			}
		}
		System.out.println("==========数组+"+Arrays.deepToString(elems));

		int intArr[][] ={{ 1, 2 }, { 3, 4} , { 5, 6}};
		intArr= elems;

		int i1 = intArr.length;
		int i2 = intArr[0].length;

		for(int i = 0; i < i1; i++) {
			System.out.println("==========数组+"+i+"=="+intArr[i][0]);

			for(int m = 0; m < i2; m++) {
				System.out.println("-------数组+"+m+"=="+intArr[i][m]);
			}
		}
		System.out.println("==========数组转字符串=i1="+i1+"==i2="+i2+"==="+Arrays.deepToString(new int[0][0]));

		Object[][] table = new String[2][2];
	}


	/**
	 * 获取加了服id的key
	 */
	public static String getRedisKey(long humanId, String key) {
		int serverId = Utils.getServerIdByHumanId(humanId);
		return Utils.createStr("{}.{}", key, serverId);
	}

	public static int getBridgeServerNo(int zone, int group){
		return 1;// TODO 日方不支持多服，暂时返回1
//		int num = (int) Math.ceil(group / 1000);
//		if(num <= 0){
//			num = 1;
//		}
//		int serverNo = num;// 组装node
//		return serverNo;
	}


	public static String getBridgeNodeIdPvp(int group){
		int num = (int) Math.ceil(group / 1000);
		String nodeId = D.NODE_BRIDGE_PREFIX + 10000 +num;// 组装node
		return nodeId;
	}

	public static int getRedisRank(String key, String id){
		Long myRank = AwaitUtil.awaitResult(handler->{
			RedisTools.getMyRank(EntityManager.redisClient, key, id, f->{
				if(f.succeeded()){
					handler.handle(Future.succeededFuture(f.result()));
				} else {
					handler.handle(Future.failedFuture(f.cause()));
				}
			});
		});
		try {
			return Utils.intValue(myRank);
		} catch (Exception e) {
			Log.temp.error("===e = {}", e);
		}
		return -1;
	}

	public static void getRedisRank(String key, String id, Handler<AsyncResult<Long>> handler) {
		RedisTools.getMyRank(EntityManager.redisClient, key, id, f -> {
			if (f.succeeded()) {
				handler.handle(Future.succeededFuture(f.result()));
			} else {
				handler.handle(Future.failedFuture(f.cause()));
			}
		});
	}

	public static Response getRedisZaddValue(List<String> keyList){
		Response response = AwaitUtil.awaitResult(handler->{
			EntityManager.redisClient.zrangebyscore(keyList, r->{
				if(r.succeeded()){
					handler.handle(Future.succeededFuture(r.result()));
				} else {
					handler.handle(Future.failedFuture(r.cause()));
				}
			});
		});
		return response;
	}

	public static List<String> getRedisZrangebyscore(String redisKey, String min, String max){
		List<String> keyList = new ArrayList<>();
		keyList.add(redisKey);
		keyList.add(min);
		keyList.add(max);
		return getRedisZrangebyscore(keyList);
	}

	public static List<String> getRedisZrangebyscore(List<String> keyList){
		Response response = AwaitUtil.awaitResult(handler->{
			EntityManager.redisClient.zrangebyscore(keyList, r->{
				if(r.succeeded()){
					handler.handle(Future.succeededFuture(r.result()));
				} else {
					handler.handle(Future.failedFuture(r.cause()));
				}
			});
		});

		List<String> strValueList = new ArrayList<>();
		try {
			Iterator itrs = response.iterator();
			while(itrs.hasNext()) {
				String key = String.valueOf(itrs.next());
				strValueList.add(key);
			}
			return strValueList;
		} catch (Exception e) {
			Log.temp.error("===e= {}", e);
		}
		return strValueList;
	}


	public static List<String> getRedisZaddStrValueList(String key, int min, int max, boolean isScore){
		JsonArray json = AwaitUtil.awaitResult(handler -> {
			RedisTools.getRankListByIndex(EntityManager.redisClient, key, min, max, isScore, f -> {
				if (f.succeeded()) {
					handler.handle(Future.succeededFuture(f.result()));
				} else {
					handler.handle(Future.failedFuture(f.cause()));
				}
			});
		});
		List<String> strValueList = new ArrayList<>();
		try{
			if (isScore) {
//				// vertx-redis-client 3.9.13会返回[key,value,key2,value2]的格式
//				int size = json.getList().size();
//				if(size % 2 != 0){
//					Log.temp.error("===数据不对，未被2整除json={}", json);
//				}
				if (!json.getList().isEmpty()){
					for (Object arr : json.getList()) {
						if (arr instanceof JsonArray) {
							JsonArray subList = (JsonArray) arr;
							strValueList.addAll(subList.getList());
						} else {
							strValueList.add(arr.toString());
						}
					}
				}
			} else {
				strValueList.addAll(json.getList());
			}
			return strValueList;
		} catch (Exception e) {
			Log.temp.error("===e={}", e);
		}
		return strValueList;
	}

	public static String[] getMyRankAndScore(String key,long id){
		String[] strArr = new String[2];
		strArr[0] = Integer.toString(getRedisRank(key, String.valueOf(id)));
		strArr[1] = "0";
		Double value = AwaitUtil.awaitResult(handler -> {
			RedisTools.getMyScore(EntityManager.redisClient, key, id, r -> {
				if (r.succeeded()) {
					handler.handle(Future.succeededFuture(r.result()));
				} else {
					handler.handle(Future.failedFuture(r.cause()));
				}
			});
		});
		strArr[1] = Double.toString(value);
		return strArr;
	}

	public static void getRedisStrValue(String key, Handler<AsyncResult<String>> handler) {
		RedisTools.get(EntityManager.redisClient, key, f -> {
			if (f.succeeded()) {
				handler.handle(Future.succeededFuture(f.result()));
			} else {
				handler.handle(Future.failedFuture(f.cause()));
			}
		});
	}

	public static String getRedisStrValue(String key){
		String strValue = AwaitUtil.awaitResult(handler->{
			RedisTools.get(EntityManager.redisClient, key, f->{
				if(f.succeeded()){
					handler.handle(Future.succeededFuture(f.result()));
				} else {
					handler.handle(Future.failedFuture(f.cause()));
				}
			});
		});
		return strValue;
	}

	public static List<String> getRedisSet(String key){
		JsonArray json = AwaitUtil.awaitResult(handler -> {
			RedisTools.getFullSet(EntityManager.redisClient, key, f -> {
				if (f.succeeded()) {
					handler.handle(Future.succeededFuture(f.result()));
				} else {
					handler.handle(Future.failedFuture(f.cause()));
				}
			});
		});
		List<String> strValueList = new ArrayList<>();
		if (json == null) {
			return strValueList;
		}
		strValueList.addAll(json.getList());
		return strValueList;
	}

	public static void getRedisSet(String key, Handler<AsyncResult<List<String>>> handler) {
		RedisTools.getFullSet(EntityManager.redisClient, key, f -> {
			if (f.succeeded()) {
				JsonArray json = f.result();
				List<String> strValueList = new ArrayList<>();
				if (json != null) {
					strValueList.addAll(json.getList());
				}
				handler.handle(Future.succeededFuture(strValueList));
			} else {
				handler.handle(Future.failedFuture(f.cause()));
			}
		});
	}

	public static List<String> getRedisRandSet(String key, int num){
		JsonArray json = AwaitUtil.awaitResult(handler->{
			RedisTools.getRandSet(EntityManager.redisClient, key, String.valueOf(num), f->{
				if(f.succeeded()){
					handler.handle(Future.succeededFuture(f.result()));
				} else {
					handler.handle(Future.failedFuture(f.cause()));
				}
			});
		});
		List<String> strValueList = new ArrayList<>();
		if (json == null) {
			return strValueList;
		}
		strValueList.addAll(json.getList());
		return strValueList;
	}

	public static List<String> getRedisList(String key){
		JsonArray json = AwaitUtil.awaitResult(handler -> {
			RedisTools.getListRange(EntityManager.redisClient, key, 0, -1, f -> {
				if (f.succeeded()) {
					handler.handle(Future.succeededFuture(f.result()));
				} else {
					handler.handle(Future.failedFuture(f.cause()));
				}
			});
		});
		List<String> strValueList = new ArrayList<>();
		if (json == null) {
			return strValueList;
		}
		strValueList.addAll(json.getList());
		return strValueList;
	}

	public static String redisLRange(String key) {
		JsonArray result = AwaitUtil.awaitResult(handler -> {
			RedisTools.getListRange(EntityManager.redisClient, key, 0, 0, f -> {
				if (f.succeeded()) {
					handler.handle(Future.succeededFuture(f.result()));
				} else {
					handler.handle(Future.failedFuture(f.cause()));
				}
			});
		});
		if (result != null && !result.isEmpty()) {
			return result.getString(0);
		}
		return null;
	}

	public static long getRedisListSize(String key){
		Long len = AwaitUtil.awaitResult(handler->{
			RedisTools.getLen(EntityManager.redisClient, key, f->{
				if(f.succeeded()){
					handler.handle(Future.succeededFuture(f.result()));
				} else {
					handler.handle(Future.failedFuture(f.cause()));
				}
			});
		});
		return Utils.intValue(len);
	}

	public static long removeRedisList(String key, String value){
		Long delResult = AwaitUtil.awaitResult(handler->{
			RedisTools.delToList(EntityManager.redisClient, key, value, f->{
				if(f.succeeded()){
					handler.handle(Future.succeededFuture(f.result()));
				} else {
					handler.handle(Future.failedFuture(f.cause()));
				}
			});
		});
		return delResult;
	}

	public static boolean isExitRedisKey(String key){
		Boolean exists = AwaitUtil.awaitResult(handler->{
			RedisTools.exists(EntityManager.getRedisClient(), key, h -> {
				if (h.succeeded()) {
					handler.handle(Future.succeededFuture(h.result()));
				} else {
					handler.handle(Future.failedFuture(h.cause()));
				}
			});
		});
		return exists;
	}

	public static String getRedisLpop(String key){
		String item = AwaitUtil.awaitResult(handler->{
			RedisTools.lpop(EntityManager.redisClient, key, f->{
				if(f.succeeded()){
					handler.handle(Future.succeededFuture(f.result()));
				} else {
					handler.handle(Future.failedFuture(f.cause()));
				}
			});
		});
		return item;
	}

	public static Double getRedisScore(String key, String id){
		Port port = Port.getCurrent();
		Double score = AwaitUtil.awaitResult(handler->{
			EntityManager.redisClient.zscore(key, String.valueOf(id), res -> {
				if (res.succeeded()) {
					double result = 0;
					Response response = res.result();
					if (response != null) {
						result = Double.parseDouble(response.toString());
					}
					AsyncActionResult.success(port,handler,result);
				} else {
					AsyncActionResult.fail(port,handler,res.cause());
				}
			});
		});
		return score;
	}

	public static BigDecimal getBigDecimal(BigDecimal bd1, BigDecimal bd2) {
		// 检查输入参数
		if (bd1 == null) {
			return BigDecimal.ZERO; // 如果bd1为null，直接返回0
		}

		// 初始化值为bd1
		BigDecimal value = bd1;

		// 如果bd2不为null，进行计算
		if (bd2 != null) {
			try {
				value = value.multiply(bd2.divide(BigDecimal.valueOf(10000)).add(BigDecimal.ONE));
			} catch (ArithmeticException e) {
				Log.temp.error("分母不能为零，bd2={}", bd2, e);
				return value;
			}
		}

		return value;
	}

	public static BigDecimal getBigDecimal(BigDecimal bd1, BigDecimal bd2, BigDecimal bd3) {
		// 属性面板=1005（暴伤）*（2008（暴伤加成）/10000+1）*（1+2009（全局暴伤）/10000）

		// 初始化值为bd1，如果bd1为null则返回0
		if (bd1 == null) {
			return BigDecimal.ZERO;
		}

		// 计算暴伤加成和全局暴伤的系数
		BigDecimal value1 = BigDecimal.ONE;
		if (bd3 != null) {
			value1 = value1.add(bd3.divide(BigDecimal.valueOf(10000), 2,RoundingMode.HALF_UP));
		}

		BigDecimal value2 = BigDecimal.ONE;
		if (bd2 != null) {
			value2 = value2.add(bd2.divide(BigDecimal.valueOf(10000),2, RoundingMode.HALF_UP));
		}

		// 计算并返回最终值
		return bd1.multiply(value1).multiply(value2);
	}

	public static BigDecimal getBigDecimal2(BigDecimal bd1, BigDecimal bd2){
		// 属性面板=（1012（回复）*（2019（生命回复加成）/10000+1）
		if(bd1 == null){
			return BigDecimal.ZERO;
		}
		BigDecimal value = bd1;
		if(bd2 != null){
			BigDecimal factor = bd2.divide(BigDecimal.valueOf(10000)).add(BigDecimal.ONE);
			value = value.multiply(factor);
		}
		return value;
	}

	public static BigDecimal getBigDecimal3(BigDecimal bd1, BigDecimal bd2){
		// 2001(基础攻击加成） + 2002（全局攻击) + 2001(基础攻击加成）* 2002（全局攻击）/10000
		BigDecimal value = BigDecimal.ZERO;
		if(bd1 != null){
			value = bd1;
		}
		if(bd2 != null){
			value = value.add(bd2).add(bd1 != null ? bd1.multiply(bd2).divide(BigDecimal.valueOf(10000), 2,RoundingMode.HALF_UP) : BigDecimal.ZERO);
		}
		return value;
	}

	public static <K> List<K> notContainedElements(Set<K> set, Collection<K> list) {
		List<K> diff = new ArrayList<K>();
		if(set.isEmpty()){
			diff.addAll(list);
			return diff;
		}
		for (K item : list) {
			if (!set.contains(item)) {
				diff.add(item);
			}
		}
		return diff;
	}

	/** 
	 * 当前时间秒
	 * <AUTHOR>
	 * @Date 2024/7/15
	 * @Param 
	 */
	public static int getTimeSec(){
		return (int)(Port.getTime() / Time.SEC);
	}

	/** 
	 * 获取开服排行组
	 * <AUTHOR>
	 * @Date 2024/7/15
	 * @Param 
	 */
	public static int getBridgeRankGroup(int serverId){
		return 1;// TODO 永远等于1，目前日方不支持多个app
//		long timeWeekOne = Utils.getTimeOfWeek(Port.getTime(), 1, 0);
//		String dateStr = Utils.formatTime(timeWeekOne, "yyyy-MM-dd");
//		int groupIndex = Utils.intValue(Utils.getRedisStrValue(RedisKeys.admin_server_group + dateStr + serverId));
//		if(groupIndex == 0){
//			groupIndex =1;
//		}
//		return groupIndex;
	}

	public static String listToSqlString(List<Long> list) {
		String sqlGuildIdStr = list.stream()
				.map(String::valueOf)
				.collect(Collectors.joining(","));
		return sqlGuildIdStr;
	}


	public static int getRankSumNum(String redisKey) {
		Port port = Port.getCurrent();
		Integer card = AwaitUtil.awaitResult(handler -> {
			EntityManager.redisClient.zcard(redisKey, res -> {
				if (res.failed()) {
					Log.game.error("获取redisKey的数量失败，redisKey={}", redisKey);
					AsyncActionResult.fail(port,handler,res.cause());
				} else {
					AsyncActionResult.success(port,handler,res.result().toInteger());
				}
			});
		});
		return Utils.intValue(card);
	}

	/**
	 * 判断是否在概率范围内(抽中概率)
	 *
	 * @param oddr
	 *            传入概率
	 * @return
	 */
	public static boolean isRandRangeInner(int oddr) {
		int rand = (int) (Math.random() * Utils.I10000);// 获取随机数
		if (rand <= oddr) {
			return true;
		}
		return false;
	}
	public static String getNumString(float t) {
		return getNumString(t, false, 1);
	}

	public static String getNumString(float t, boolean e, int r) { //= false, int r = 1
		float n = t;
		String i = "";
		if (t >= 1e11) {
			n = (float)(t / 1e9);
			i = "B";
		}
		else if (t >= 1e8)
		{
			n = (float)(t / 1e6);
			i = "M";
		}
		else
		{
			if (!(t >= 1e5))
				return Float.toString(t);
			n = (float)(t / 1e3);
			i = "K";
		}
		if (e)
			i = i.toLowerCase();
		return i.isEmpty() ? Float.toString(n) + i : String.format("%." + r + "f", n) + i;
	}

	public static double getStringSizeInMB(String str) {
		// 获取字符串的字节数组，使用UTF-8编码
		byte[] bytes = str.getBytes();
		// 计算字节数并转换为MB
		return bytes.length / (1024.0 * 1024.0);
	}

	public static void putIfNonZero(JSONObject jo, String key, int value) {
		if (value != 0) {
			jo.put(key, value);
		}
	}
	public static void putIfNonZero(JSONObject jo, String key, long value) {
		if (value != 0) {
			jo.put(key, value);
		}
	}

	public static void putIfNotEmpty(JSONObject jo, String key, String value) {
		if (value != null && !value.isEmpty()) {
			jo.put(key, value);
		}
	}

	public static void putIfNotNull(JSONObject jo, String key, Object value) {
		if (value != null) {
			jo.put(key, value);
		}
	}

	public static <K> List<K> diffElement(List<K> list1, List<K> list2) {
		List<K> diff = new ArrayList<K>();
		Map<K, Integer> map = new HashMap<>(list1.size() + list2.size());
		List<K> maxList = list1;
		List<K> minList = list2;
		if (list2.size() > list1.size()) {
			maxList = list2;
			minList = list1;
		}
		for (K item : maxList) {
			map.put(item, 1);
		}
		for (K item : minList) {
			Integer count = map.get(item);
			if (count != null) {
				map.put(item, ++count);
				continue;
			}
			map.put(item, 1);
		}
		for (Map.Entry<K, Integer> entry : map.entrySet()) {
			if (entry.getValue() == 1) {
				diff.add(entry.getKey());
			}
		}
		return diff;
	}

	public static void redisZrem(String redisKey){
		RedisTools.getRankLen(EntityManager.getRedisClient(), redisKey, rest -> {
			if (rest.failed()) {
				Log.friend.error("获取排行榜长度失败，i={}", redisKey);
				return;
			}
			long total = rest.result();
			if(total > GlobalConfVal.arenaHistoryNum){
				List<String> strList = Utils.getRedisZaddStrValueList(redisKey, 0, -1, false);
				if(strList.size() > GlobalConfVal.arenaHistoryNum){
					List<String> removeList  = strList.subList(GlobalConfVal.arenaHistoryNum, strList.size());
					if(!removeList.isEmpty()){
						removeList.add(0, redisKey);
						RedisTools.removeFromList(EntityManager.redisClient, removeList);
					}
				}
			}
		});
	}

	/**
	 * 将运算数指定位置的值置为指定值<br>
	 * 例: 0000 1011 需要更新为 0000 1111, 即第 2 位的值需要置为 1<br>
	 *
	 * @param source
	 *            需要运算的数
	 * @param pos
	 *            指定位置
	 * @param value
	 *            只能取值为 0, 或 1, 所有大于0的值作为1处理, 所有小于0的值作为0处理
	 *
	 * @return 运算后的结果数
	 */
	public static long setBitValueLong(long source, int pos, int value) {
		if(pos < 0 || pos > 63){
			throw new IllegalArgumentException("pos参数必须在0-63之间");
		}
		if (value != 0 && value != 1) {
			throw new IllegalArgumentException("value参数必须是0或1");
		}
		long mask = (1L << pos);
		if (value > 0) {
			source |= mask;
		} else {
			source &= (~mask);
		}
		return source;
	}

	/**
	 * 检查运算数的指定位置是否为1<br>
	 *
	 * @param source
	 *            需要运算的数
	 * @param pos
	 *            指定位置
	 * @return true 表示指定位置值为1, false 表示指定位置值为 0
	 */
	public static boolean checkBitValueLong(long source, int pos) {
		if (pos < 0 || pos >= 64) {
			throw new IllegalArgumentException("pos参数必须在0-63之间");
		}
		source = (source >>> pos);

		return (source & 1) == 1;
	}

	/**
	 * 解析通过port.applyId()生成的ID中serverID
	 * @param serverApplyId
	 * @return
	 */
	public static int getServerId(long serverApplyId) {
		int len = 5;
		if(Config.GAME_SERVER_PREFIX_VALUE>=100000){
			len=6;
		}
		return Integer.parseInt(String.valueOf(serverApplyId-C.GAME_PLATFORM_ID*IdAllotPoolBase.PLATFORM_POW).substring(0,len));
	}

	// 添加removeEmojis方法
	public static String removeEmojis(String input) {
		if (input == null) {
			return null;
		}
		String newStr = input.replaceAll("[\uD83C-\uDBFF\uDC00-\uDFFF]", "");
		return newStr.isEmpty() ? input : newStr;
	}

	/**
	 * 检测文本是否包含emoji表情包
	 */
	public static boolean containEmoji(String text) {
		for (int i = 0; i < text.length(); i++) {
			int codePoint = text.codePointAt(i);
			if (isEmoji(codePoint)) {
				return true;
			}
		}
		return false;
	}

	public static boolean isEmoji(int codePoint) {
		return (codePoint >= 0x1F600 && codePoint <= 0x1F64F)  // 基本的表情符号范围（比如笑脸等常见表情）
				|| (codePoint >= 0x1F300 && codePoint <= 0x1F5FF)  // 各种图标、符号等相关范围
				|| (codePoint >= 0x1F680 && codePoint <= 0x1F6FF)  // 运输和地图符号相关Emoji范围
				|| (codePoint >= 0x1F1E0 && codePoint <= 0x1F1FF)  // 区域指示符号Emoji范围
				|| (codePoint >= 0x2600 && codePoint <= 0x26FF)  // 杂项符号范围，部分也用于Emoji
				|| (codePoint >= 0x2700 && codePoint <= 0x27BF)  // 装饰符号范围，有一些Emoji在内
				|| (codePoint >= 0xFE00 && codePoint <= 0xFE0F)  // 变体选择器范围，常和Emoji配合使用
				|| (codePoint >= 0x1F900 && codePoint <= 0x1F9FF)  // 补充的表情符号等范围
				|| (codePoint >= 0x1F004 && codePoint <= 0x1F0CF);  // 扑克牌等相关Emoji范围
	}

	public static List<String> getRedisZaddKeyList(String key, Map<?, ?> map) {
		List<String> list = new ArrayList<>();
		list.add(key);
		for (Map.Entry<?, ?> pair : ((Map<?, ?>) map).entrySet()) {
			list.add(String.valueOf(pair.getValue()));
			list.add(String.valueOf(pair.getKey()));
		}
		return list;
	}


	public static String redisGetKey(RedisAPI client, String key){
		String strValue = AwaitUtil.awaitResult(handler->{
			RedisTools.get(client, key, f->{
				if(f.succeeded()){
					handler.handle(Future.succeededFuture(f.result()));
				} else {
					handler.handle(Future.failedFuture(f.cause()));
				}
			});
		});
		return strValue;
	}

	/**
	 * 获取有序排行（zadd）中指定索引范围的元素列表
	 * @param client  adminRedis,crossRedis,redisClient
	 * @param key
	 * @param min
	 * @param max
	 * @param isScore 是否带分数
	 * @return
	 */
	public static JsonArray redisRankList(RedisAPI client, String key, int min, int max, boolean isScore){
		JsonArray jsonArrary = AwaitUtil.awaitResult(handler->{
			RedisTools.getRankListByIndex(client, key, min, max, isScore, f->{
				if(f.succeeded()){
					handler.handle(Future.succeededFuture(f.result()));
				} else {
					handler.handle(Future.failedFuture(f.cause()));
				}
			});
		});
		return jsonArrary;
	}

	/**
	 * 批量执行redis命令
	 * @param redis  adminRedis,crossRedis,redisClient
	 * @param reqList
	 * @return
	 */
	public static List<Response> redisDoBatch(Redis redis, List<Request> reqList){
		List<Response> result = AwaitUtil.awaitResult(handler->{
			RedisTools.doBatch(redis, reqList, f->{
				if(f.succeeded()){
					handler.handle(Future.succeededFuture(f.result()));
				} else {
					handler.handle(Future.failedFuture(f.cause()));
				}
			});
		});
		return result;
	}

	/**
	 * 批量执行redis命令
	 * @param redis  adminRedis,crossRedis,redisClient
	 * @param reqList
	 * @return
	 */
	public static List<JsonObject> redisBatch(Redis redis, List<Request> reqList){
		List<JsonObject> result = AwaitUtil.awaitResult(handler->{
			RedisTools.batch(redis, reqList, f->{
				if(f.succeeded()){
					handler.handle(Future.succeededFuture(f.result()));
				} else {
					handler.handle(Future.failedFuture(f.cause()));
				}
			});
		});
		return result;
	}


}


