<#include "EntityCommonVisit.ftl">
package ${packageName};

import org.gof.core.db.DBConsts;
import io.vertx.core.json.JsonObject;
import org.gof.core.Port;
import org.gof.core.Record;
import ${superClassPackage}.${superClassName};
import ${annotationPack};
import org.gof.core.support.S;
import org.gof.core.OutputStream;
import java.io.IOException;
import org.gof.core.InputStream;

${annotation}
public abstract class ${entityName} extends ${superClassName} {
	public ${entityName}() {
		super();
	<#list fields as field>
		<#if field.defaults??>
			<#if field.type == "String">
		set${field.name?cap_first}("${field.defaults}");
			<#elseif field.type == "byte[]">
        set${field.name?cap_first}(new byte[1]);
			<#else>
		set${field.name?cap_first}(${field.defaults});
			</#if>
		<#else>
			<#if field.type == "String">
		set${field.name?cap_first}("");
			<#elseif field.type == "byte[]">
        set${field.name?cap_first}(new byte[1]);
			</#if>
		</#if>
	</#list>
	}

	public ${entityName}(Record record) {
		super(record);
	<#list fields as field>
		<#if field.isTransient>
		<#if field.defaults??>
			<#if field.type == "String">
		set${field.name?cap_first}("${field.defaults}");
			<#else>
		set${field.name?cap_first}(${field.defaults});
			</#if>
		<#else>
			<#if field.type == "String">
		set${field.name?cap_first}("");
			</#if>
		</#if>
		</#if>
		
	</#list>
	}
	
	
	/**
	 * 属性关键字
	 */
	public static class SuperK {
		<#list fields as field>
		public static final String ${field.name} = "${field.name}";	//${field.comment}
		</#list>
	}

	<#-- get和set方法 -->
	<@getAndSetField fields=fields fieldKeyHolder="SuperK" />
	
	public JsonObject getAllObjNew(){
		JsonObject obj = new JsonObject();
		<#list fields as field>
			<#if field.type == "boolean">
		obj.put(SuperK.${field.name}, is${field.name?cap_first}());
			<#else>
		obj.put(SuperK.${field.name}, get${field.name?cap_first}());
			</#if>
		</#list>
		return obj;
	}
	
	@Override
	public void writeTo(OutputStream out) throws IOException {
		super.writeTo(out);
<#--		<#list fields as field>-->
<#--		out.write(${field.name});-->
<#--		</#list>-->
		 
	}

	@Override
	public void readFrom(InputStream in) throws IOException {
		super.readFrom(in);
<#--		<#list fields as field>-->
<#--		${field.name} = in.read();-->
<#--		</#list>-->
	}

}