package org.gof.core.gen.entity;

import org.apache.commons.lang3.StringUtils;
import org.gof.core.support.Config;
import org.gof.core.support.S;
import picocli.CommandLine;

import java.sql.Connection;

@CommandLine.Command(name = "grades", mixinStandardHelpOptions = true, version = "grades 1.0")
public class Grades implements Runnable {

    @CommandLine.Command
    void genDB(@CommandLine.Option(names = "-source") String sourceDir,
             @CommandLine.Option(names = "-jarfile", defaultValue = "") String jarfile,
             @CommandLine.Option(names = "-isCross",defaultValue = "false") boolean isCross,
            @CommandLine.Option(names = "-isAdmin",defaultValue = "false") boolean isAdmin) throws Exception {

        boolean isJar = false;
        String jarFile = jarfile;
        if(!StringUtils.isEmpty(jarfile)){
            isJar = true;
        }
        if(isCross){
            S.isBridge = true;
            S.isCross = true;
        }
        if(isAdmin){
            S.isAdmin = true;
        }

        GenDB genDB = new GenDB(sourceDir,isJar,jarFile);
        String dbSchema = Config.DB_SCHEMA;
        String dbUrl = Config.DB_URL;
        String dbUser = Config.DB_USER;
        String dbPwd = Config.DB_PWD;

        //设置log4j2配置文件所需的环境变量，作用是gen的时候
        //不会报配置没找到的错误，同时有gen.log的日志记录
        System.setProperty("logFileName", "genDB");
        System.out.println(dbUrl+"==="+sourceDir);
        Connection conn = genDB.getDBConnection("com.mysql.jdbc.Driver", dbUrl, dbUser, dbPwd);
        genDB.genDB(conn);
        System.out.println("执行完毕，如果没有输出则说明无需建表或者无需更新表结构。");
    }

    @Override
    public void run() {

    }

    public static void main(String[] args) {
        int exitCode = new CommandLine(new Grades()).execute(args);
        assert exitCode == 0;
        System.exit(exitCode);
    }
}
