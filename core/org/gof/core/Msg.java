package org.gof.core;

import com.google.protobuf.Message;
import org.gof.core.support.Utils;

import java.util.HashMap;
import java.util.Map;

public class Msg {

    private static final Map<Integer, Class<? extends Message>> idToClass = new HashMap<>();

    public static int getMsgId(byte[] bytes) {
        int msgId = Utils.bytesToInt(bytes, 4);	//消息ID
        return msgId;
    }

    public static String getNameById(Integer msgId) {
        return String.valueOf(msgId);
    }
}
