package org.gof.core;

import java.util.concurrent.*;

/**
 * 公用线程池
 *
 */
public class ServerExecutors {

    /** 初始化线程数量 */
    public static int core_size=0;
    /** 线程池大小 */
    public static int max_pool_size=Math.max(1, Runtime.getRuntime().availableProcessors()/2);
    /** 线程缓存时间（s） */
    public static int time_out_sec=180;

    // 任务执行队列
    private static LinkedBlockingQueue<Runnable> normalTaskQueue = new LinkedBlockingQueue<Runnable>();
    // 线程池
    private static ExecutorService taskExecutor;

    /**
     * 初始化公共线程池
     *
     * @param coreSize
     *            核心启动数
     * @param maxPoolSize
     *            最大数
     * @param keepAliveTime
     *            线程缓存时间(seconds)
     */
    public static void init(int coreSize, int maxPoolSize, long keepAliveTime) {
        ThreadPoolExecutor executor = new ThreadPoolExecutor(coreSize, maxPoolSize, keepAliveTime, TimeUnit.SECONDS, normalTaskQueue);
        executor.allowCoreThreadTimeOut(true);
        taskExecutor = executor;
    }

    /**
     * 获取可缓存的线程池，<b>任务中不能执行基于ThreadContext的回调处理</b>
     *
     * @return
     */
    public static ExecutorService getTaskExecutor() {
        if(taskExecutor ==null){
            synchronized (normalTaskQueue) {
                if(taskExecutor ==null){
                    init(core_size,max_pool_size,time_out_sec);
                }
            }
        }
        return taskExecutor;
    }

    public static int getNormalTaskQueueSize() {
        return normalTaskQueue.size();
    }

    /**
     * 提交异步任务，注意：不能在任务中直接执行rpc调用
     * @param task
     * @return
     */
    public static Future<?> submit(Runnable task){
        return getTaskExecutor().submit(task);
    }
}
