package org.gof.core.statistics;

import org.gof.core.Port;
import org.gof.core.Service;
import org.gof.core.gen.proxy.DistrClass;
import org.gof.core.support.Config;
import org.gof.core.support.Distr;
import org.gof.core.support.TickTimer;
import org.gof.core.support.Time;

/**
 * 执行信息统计
 * 生产环境中不建议开启，会影响系统性能。
 */
@DistrClass(
	servId = Distr.SERV_STATISTICS
)
public class StatisticsService extends Service {
	//打印统计数据
	private TickTimer timer = new TickTimer(Config.STATISTICS_RESULT_TIME * Time.SEC);
	
	public StatisticsService(Port port) {
		super(port);
	}

	@Override
	public Object getId() {
		return Distr.SERV_DEFAULT;
	}

	@Override
	public void pulseOverride() {
		if(!Config.STATISTICS_ENABLE){
			return;
		}

		long now = port.getTimeCurrent();
		if (!timer.isPeriod(now)) {
			return;
		}

		//打印统计信息
		StatisticsMSG.showResult();
		StatisticsOB.showResult();
		StatisticsDB.showResult();
		StatisticsRPC.showResult();
	}
	
//	/**
//	 * 格式化纳秒显示
//	 * @return
//	 */
//	public static String formatTime(long nano) {
//		return nano > 5 * SIZE_S ? (nano / SIZE_S + "s") : (nano > 10 * SIZE_MS ? (nano / SIZE_MS + "ms") : (String.format("%.2f", 1.0 * nano / SIZE_MS) + "ms"));
//	}
//	private static final long SIZE_MS = 1_000_000L;
//	private static final long SIZE_S = 1_000_000_000L;
}
