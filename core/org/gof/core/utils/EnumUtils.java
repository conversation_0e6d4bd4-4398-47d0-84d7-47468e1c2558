package org.gof.core.utils;

import org.gof.core.support.SysException;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

public class EnumUtils {
    public static <K,T> Map<K,T> toMap(T[] values, String keyName, Class<K> keyCls) {
        if(values==null){
            return null;
        }
        if(values.length==0){
            return new HashMap<>();
        }
        try {
            Method method = values[0].getClass().getDeclaredMethod("get"+StrUtil.captureName(keyName));
            Map<K, T> map = new HashMap<>(values.length);
            for (T t : values) {
                K k = (K) method.invoke(t);
                map.put(k, t);
            }
            return map;
        }catch (Exception ex){
            throw new SysException("枚举转换map出错",ex);
        }
    }
}
