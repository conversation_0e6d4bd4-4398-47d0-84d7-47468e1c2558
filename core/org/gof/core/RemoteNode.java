package org.gof.core;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.gof.core.interfaces.ISerilizable;
import org.gof.core.support.*;
import org.gof.core.support.log.LogCore;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.Util;
import org.gof.demo.worldsrv.support.observer.Event;
import org.slf4j.Logger;
import org.zeromq.ZContext;
import org.zeromq.ZMQ;

import java.io.IOException;

public class RemoteNode {
	public static final long INTERVAL_PING = 6 * Time.SEC;		//连接检测时间间隔 6秒
	public static final long INTERVAL_LOST = 20 * Time.SEC;		//连接丢失时间间隔 20秒
	
	//定时器
	private final TickTimer pingTimer = new TickTimer(INTERVAL_PING);
	private final TickTimer disconnTimer = new TickTimer(INTERVAL_PING);
		
	//日志远程访问
	private final Logger logRemote = LogCore.remote;
		
	private final String remoteId;				//远程Node名称
	private final String remoteAddr;			//远程Node地址
	private final Node localNode;				//本地Node名称
	private final String localAlias;			//本地Node别名
	private final int nodeType;					//节点类型
	
	private final ZContext zmqContext;			//ZMQ上下文
	private final ZMQ.Socket zmqPush;			//ZMQ连接
	
	//连接创建时间
	private long createTime;
	//最后连接检查反馈时间
	private long pongTime;
	//是否连接上
	private boolean connected;					
	
	//是否为主动连接（主动连接超时关闭后会重新建立连接，被动则不会）
	private boolean main;
	
	/**
	 * 创建主动连接
	 * @param localNode
	 * @param localAlias
	 * @param remoteName
	 * @param remoteAddr
	 * @return
	 */
	public static RemoteNode createActive(int nodeType, Node localNode ,String remoteName, String remoteAddr) {
		String localAlias = localNode.getLocalAlias(nodeType);
		RemoteNode r = new RemoteNode(nodeType, localNode, localAlias, remoteName, remoteAddr);
		r.main = true;
		return r;
	}
	
	/**
	 * 创建被动连接
	 * @param localNode
	 * @param localAlias
	 * @param remoteName
	 * @param remoteAddr
	 * @return
	 */
	public static RemoteNode createInactive(int nodeType, Node localNode, String localAlias ,String remoteName, String remoteAddr) {
		RemoteNode r = new RemoteNode(nodeType, localNode, localAlias, remoteName, remoteAddr);
		r.main = false;
		
		return r;
	}
	
	/**
	 * 构造函数
	 * @param localNode
	 * @param remoteName
	 * @param remoteAddr
	 */
	private RemoteNode(int nodeType, Node localNode, String localAlias , String remoteName, String remoteAddr) {
		this.nodeType = nodeType;
		this.localNode = localNode;
		this.localAlias = localAlias;
		this.remoteId = remoteName;
		this.remoteAddr = remoteAddr;

		this.createTime = System.currentTimeMillis();
		this.pongTime = createTime;
		
		this.zmqContext = new ZContext();

		this.zmqPush = zmqContext.createSocket(ZMQ.PUSH);
		this.zmqPush.setSndHWM(0);		//发送队列无限大，不会堵塞。
		this.zmqPush.setLinger(3000);
		this.zmqPush.setReconnectIVL(2000);
		this.zmqPush.setReconnectIVLMax(5000);
		this.zmqPush.setSendTimeOut(15000);//15s,避免send夯住导致系统功能完全无法使用
		this.zmqPush.connect(remoteAddr);

//		Log.temp.error("===this={}", this);
	}
	
	/**
	 * 心跳操作
	 */
	public void pulse() {
		//当前时间
		long now = localNode.getTimeCurrent();
		
		//到达间隔时间后 进行连接检测
		pulsePing(now);
		
		//活跃状态下 长时间没收到心跳检测 那么就认为连接已丢失
		pulseDisconn(now);
		
	}
	
	/**
	 * 进行连接测试
	 */
	public void pulsePing(long now) {
		if(!pingTimer.isPeriod(now)) return;
		//创建并发送测试请求
		pulsePing();
	}
	
	/**
	 *	不管定时器，直接来一个 
	 */
	private void pulsePing() {
		//创建并发送测试请求
		Call call = new Call();
		call.type = Call.TYPE_PING;
		call.fromNodeId = localAlias;
		call.to.nodeId = remoteId;
		call.methodParam = new Object[] { localAlias, localNode.getAddr(), main, localNode.getNodeType()};
		call.immutable = true;
		sendCall(call);
	}

	/**
	 *	发送注册请求
	 */
	public void sendRegCall() {
		//创建并发送测试请求
		Call call = new Call();
		call.type = Call.TYPE_NODE_REG;
		call.fromNodeId = localAlias;
		call.to.nodeId = remoteId;
		call.methodParam = new Object[] { localAlias, localNode.getAddr(), localNode.getNodeType()};
		call.immutable = true;
		sendCall(call);
	}

	/**
	 *	发送注册请求
	 */
	public void sendRegCallResult() {
		//创建并发送测试请求
		Call call = new Call();
		call.type = Call.TYPE_NODE_REG_RESULT;
		call.fromNodeId = localAlias;
		call.to.nodeId = remoteId;
		call.methodParam = new Object[0];
		call.immutable = true;
		sendCall(call);
	}
	
	
	/**
	 * 活跃状态下 长时间没收到心跳检测 那么就认为连接已丢失
	 * @param now
	 */
	private void pulseDisconn(long now) {
		if(!isActive()) return;
		if(!disconnTimer.isPeriod(now)) return;
		if((now - pongTime) < INTERVAL_LOST) return;

		connected = false;
		String connectInfo;
		if(this.isMain()){
			connectInfo= Util.createStr("[{}]==>[{}] 远程Node连接已断开,createTime={}, pongTime={}",localNode.getId(), this.getRemoteId(),createTime, pongTime);
		}else{
			connectInfo= Util.createStr("[{}]==>[{}] 远程Node连接已断开,createTime={}, pongTime={}",this.getRemoteId(),localNode.getId(),createTime, pongTime);
		}
		logRemote.error(connectInfo);
		Port port = localNode.getPort(Distr.PORT_DEFAULT);
		port.addQueue(new PortPulseQueue() {
			@Override
			public void execute(Port port) {
				Event.fire(NodeEventKey.NODE_UNREGISTER,"nodeType",nodeType, "remoteId",remoteId, "remoteAddr",remoteAddr,"active",main);
			}
		});
	}
	
	/**
	 * 处理连接测试请求
	 */
	public void pingHandle() {
		//反馈请求
		Call call = new Call();
		call.type = Call.TYPE_PONG;
		call.fromNodeId = localAlias;
		call.to = new CallPoint(remoteId, null, null);
		
		sendCall(call);
		
		Log.temp.debug("===处理连接测试 pingHandle, call={}", call);
	}
	
	/**
	 * 处理连接测试反馈请求
	 */
	public void pongHandle() {
		//非活跃的情况下收到连接测试
		if(!isActive()) {
			markActive();
			String connectInfo;
			if(this.isMain()){
				connectInfo= Util.createStr("[{}]==>[{}] 远程Node连接成功,createTime={}, pongTime={}",localNode.getId(), this.getRemoteId(),createTime, pongTime);
			}else{
				connectInfo= Util.createStr("[{}]==>[{}] 远程Node连接成功,createTime={}, pongTime={}",this.getRemoteId(),localNode.getId(),createTime, pongTime);
			}
			logRemote.info(connectInfo);
//			Port port = localNode.getPort(Distr.PORT_DEFAULT);
//			port.addQueue(new PortPulseQueue() {
//				@Override
//				public void execute(Port port) {
//					Event.fire(NodeEventKey.NODE_REGISTER,"nodeType",nodeType, "remoteId",remoteId, "remoteAddr",remoteAddr,"active",main);
//				}
//			});
		}
		//设置最后心跳检查反馈时间
		pongTime = localNode.getTimeCurrent();
		Log.temp.debug("===处理连接测试 pongHandle, pongTime={}, remoteId={}", pongTime, remoteId);
	}
	
	public String getRemoteId() {
		return remoteId;
	}

	public int getNodeType(){
		return nodeType;
	}

	public String getRemoteAddr() {
		return remoteAddr;
	}

	public long getPongTime() {
		return pongTime;
	}
	
	public String getLocalId() {
		return localNode.getId();
	}

	/**
	 * 本地Node别名
	 * @return
	 */
	public String getLocalAlias() {
		return localAlias;
	}



	/**
	 * 是否为活跃状态
	 * @return
	 */
	public boolean isActive() {
		return connected;
	}

	public void markActive(){
		this.connected = true;
		pongTime = localNode.getTimeCurrent();
	}
	
	public boolean isMain() {
		return main;
	}

	/**
	 * 关闭
	 */
	public void close() {
		synchronized (zmqPush) {
			zmqContext.destroy();
		}
	}
	
	/**
	 * 发送调用请求
	 * @param call
	 */
	public void sendCall(Call call) {
		//输出流
		OutputStream2 out = null;
		try {
			//创建输出流并写入
			out = new OutputStream2();
			out.write(call);
			
			//发送消息
			sendCall(out.getBuffer(), out.getLength());
			
		} finally {
			//关闭回收
			if(out!=null){
				out.close();
				out = null;
			}
		}
	}
	
	/**
	 * 发送调用请求
	 * zmq内部不是线程安全的，必须做同步发送。
	 * @param call
	 */
	public void sendCall(byte[] buf, int size) {
		synchronized (zmqPush) {
			zmqPush.send(buf, 0, size, 0);
		}
	}
	
	@Override
	public String toString() {
		return new ToStringBuilder(this)
				.append("remoteId", remoteId)
				.append("nodeType", NodeType.of(nodeType))
				.append("remoteAddr", remoteAddr)
				.append("localAlias", localAlias)
				.append("connected", connected)
				.append("main", main)
				.append("createTime", Utils.formatTime(createTime, "yyyy-MM-dd HH:mm:ss"))
				.append("rogerTime", Utils.formatTime(pongTime, "yyyy-MM-dd HH:mm:ss"))
				.toString();
	}
}
