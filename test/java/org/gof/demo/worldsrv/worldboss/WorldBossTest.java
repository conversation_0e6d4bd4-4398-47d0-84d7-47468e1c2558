package org.gof.demo.worldsrv.worldBoss;

import io.vertx.core.json.JsonArray;
import org.gof.core.Port;
import org.gof.core.dbsrv.redis.EntityManager;
import org.gof.core.dbsrv.redis.RedisTools;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.config.ConfRanktype;
import org.gof.demo.worldsrv.rank.RankParamKey;
import org.gof.demo.worldsrv.redis.RedisKeys;
import org.gof.test.TestBase;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

public class WorldBossTest extends TestBase {

    @Test
    public void testWorldBossRedisDataLoad() {
        initLoadRedis(30006);
    }

    public void initLoadRedis(int serverId) {
        String dateStr = Utils.formatTime(Port.getTime(), "yyyy-MM-dd");
        String redisKey = RedisKeys.worldBoss + serverId + dateStr;
        ConfRanktype confRanktype = ConfRanktype.get(RankParamKey.rankTypeWorldBoss);
        System.out.println(redisKey);
        CompletableFuture<Boolean> future = new CompletableFuture<>();
        RedisTools.getRankListByIndex(EntityManager.redisClient, redisKey, 0, confRanktype.show_num, false, ret -> {
            JsonArray json = ret.result();
            List list = json.getList();
            int size = list.size();
            for (int i = 0; i < size; i += 2) {
                Object value = list.get(i);
                long humanId = Utils.longValue(value);
                long hurt = Utils.longValue(list.get(i + 1));
            }
            future.complete(true);
        });
        try {
            future.get();
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }
    }
}
