package org.gof.test;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.util.Iterator;

public class JsonFormatter {
    /**
     * 格式化JSON字符串
     */
    public static String formatJson(String jsonStr) {
        try {
            JSONObject json = JSONObject.parseObject(jsonStr);
            return formatJsonObject(json, 0);
        } catch (Exception e) {
            return jsonStr;
        }
    }

    /**
     * 递归格式化JSONObject
     */
    private static String formatJsonObject(JSONObject json, int indent) {
        StringBuilder sb = new StringBuilder();
        String indentStr = getIndentString(indent);

        sb.append("{\n");

        for (Iterator<String> it = json.keySet().iterator(); it.hasNext();) {
            String key = it.next();
            Object value = json.get(key);
            sb.append(indentStr).append("    \"").append(key).append("\": ");

            // 处理值
            if (value instanceof String) {
                String strValue = (String) value;
                // 尝试解析字符串值是否为JSON
                try {
                    if (strValue.startsWith("{")) {
                        JSONObject subJson = JSONObject.parseObject(strValue);
                        sb.append(formatJsonObject(subJson, indent + 1));
                    } else if (strValue.startsWith("[")) {
                        JSONArray subArray = JSONArray.parseArray(strValue);
                        sb.append(formatJsonArray(subArray, indent + 1));
                    } else {
                        sb.append("\"").append(strValue).append("\"");
                    }
                } catch (Exception e) {
                    sb.append("\"").append(strValue).append("\"");
                }
            } else if (value instanceof JSONObject) {
                sb.append(formatJsonObject((JSONObject) value, indent + 1));
            } else if (value instanceof JSONArray) {
                sb.append(formatJsonArray((JSONArray) value, indent + 1));
            } else {
                sb.append(value);
            }

            if (it.hasNext()) {
                sb.append(",");
            }
            sb.append("\n");
        }

        sb.append(indentStr).append("}");
        return sb.toString();
    }

    /**
     * 格式化JSONArray
     */
    private static String formatJsonArray(JSONArray array, int indent) {
        StringBuilder sb = new StringBuilder();
        String indentStr = getIndentString(indent);

        sb.append("[\n");

        for (int i = 0; i < array.size(); i++) {
            Object value = array.get(i);
            sb.append(indentStr).append("    ");

            if (value instanceof JSONObject) {
                sb.append(formatJsonObject((JSONObject) value, indent + 1));
            } else if (value instanceof JSONArray) {
                sb.append(formatJsonArray((JSONArray) value, indent + 1));
            } else if (value instanceof String) {
                sb.append("\"").append(value).append("\"");
            } else {
                sb.append(value);
            }

            if (i < array.size() - 1) {
                sb.append(",");
            }
            sb.append("\n");
        }

        sb.append(indentStr).append("]");
        return sb.toString();
    }

    /**
     * 获取缩进字符串
     */
    private static String getIndentString(int indent) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < indent; i++) {
            sb.append("    ");
        }
        return sb.toString();
    }
}