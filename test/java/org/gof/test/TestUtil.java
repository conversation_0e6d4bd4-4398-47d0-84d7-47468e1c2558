package org.gof.test;

import java.lang.reflect.Method;

public class TestUtil {
    public static Object invokeMethod(Class<?> cls, String methodName, Class<?>[] paramTypes, Object...params) {
        try{
            Method method = cls.getDeclaredMethod(methodName, paramTypes);
            method.setAccessible(true);
            Object result = method.invoke(null, params);
            return result;
        }catch (Exception ex){
            throw new RuntimeException(ex);
        }
    }

    public static Object invokeMethod(Object target, String methodName, Class<?>[] paramTypes, Object...params) {
        try{
            Method method = target.getClass().getDeclaredMethod(methodName, paramTypes);
            method.setAccessible(true);
            Object result = method.invoke(target, params);
            return result;
        }catch (Exception ex){
            throw new RuntimeException(ex);
        }
    }
}
