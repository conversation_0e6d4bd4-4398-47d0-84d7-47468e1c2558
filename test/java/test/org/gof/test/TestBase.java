package test.org.gof.test;

import org.apache.logging.log4j.LogManager;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.TestInstance;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class TestBase {

    public static Logger logger ;

    @BeforeAll
    public static void startTestEvn() {
        System.setProperty("logFileName", "game-test-log");
        System.setProperty("vertx.logger-delegate-factory-class-name", "io.vertx.core.logging.Log4j2LogDelegateFactory");
        logger = LoggerFactory.getLogger("TEST");
    }

    @AfterAll
    public void stopTestEvn(){
        LogManager.shutdown();
    }
}
