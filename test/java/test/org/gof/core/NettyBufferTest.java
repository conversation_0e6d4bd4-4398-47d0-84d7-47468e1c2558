package test.org.gof.core;

import org.gof.core.NettyBuffer;
import org.gof.core.NettyBufferPool;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import test.org.gof.test.TestBase;

public class NettyBufferTest extends TestBase {

    @Test
    public void testWriteNumber(){
        NettyBuffer buffer = NettyBufferPool.allocate();
        //int
        assertReadIntOk(buffer,100);
        assertReadIntOk(buffer,1);
        assertReadIntOk(buffer,-1);
        assertReadIntOk(buffer,0);
        assertReadIntOk(buffer,Integer.MAX_VALUE);
        assertReadIntOk(buffer,Integer.MIN_VALUE);
        //long
        assertReadLongOk(buffer,100L);
        assertReadLongOk(buffer,1L);
        assertReadLongOk(buffer,-1L);
        assertReadLongOk(buffer,0L);
        assertReadLongOk(buffer,Long.MAX_VALUE);
        assertReadLongOk(buffer,Long.MIN_VALUE);

    }

    private void assertReadIntOk(NettyBuffer buffer, int value){
        buffer.writeInt32NoTag(value);
        int ret = buffer.readInt32();
        Assertions.assertEquals(value,ret);
    }

    private void assertReadLongOk(NettyBuffer buffer, long value){
        buffer.writeInt64NoTag(value);
        long ret = buffer.readInt64();
        Assertions.assertEquals(value,ret);
    }


}
