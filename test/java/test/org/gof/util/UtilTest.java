package test.org.gof.util;


import org.gof.core.dbsrv.redis.RedisTools;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.msg.Define;

import java.util.*;


public class UtilTest {

    public static void main(String[] args) {
        //600060000162400001L
        //399010000038200001L
        int serverId = Utils.getServerId(600060000162400001L);
        System.out.println(serverId);
    }

    private static void testCompress(){
        Define.p_battle_role.Builder dInfo = Define.p_battle_role.newBuilder();
        dInfo.setId(1234);
        dInfo.setName("test1234");
        dInfo.setLev(100);
        dInfo.setJob(1);
        for(int i=0;i<10;i++) {
            dInfo.addPetList(Define.p_role_pet.newBuilder().setPetId(1+i).setPetLev(10).setPetPos(7).build());
        }
        for(int i=0;i<10;i++) {
            dInfo.addAttrObjList(Define.p_attr_obj_list.newBuilder().addAttrList(Define.p_key_value.newBuilder().setK(10+i).setV(10000+i).build()).build());
        }
        byte[] byteArray = dInfo.build().toByteArray();
        System.out.println("byte:"+byteArray.length+",runTimes:"+100000);
        calBase64(byteArray);
        calSnappy(byteArray);
        calLz4(byteArray);
        String base64Str = Base64.getEncoder().encodeToString(byteArray);
        String lz4Str = Utils.compressBytesLZ4(byteArray, RedisTools.BYTE_STR_FLAG);
        String snappyStr = Utils.compressSnappy(byteArray, RedisTools.BYTE_STR_FLAG);
        System.out.println(Utils.createStr("base64Str len={}",base64Str.length()));
        System.out.println(Utils.createStr("lz4Str len={}",lz4Str.length()));
        System.out.println(Utils.createStr("snappyStr len={}",snappyStr.length()));
    }

    private static void calBase64(byte[] byteArray){
        List<String> result = new ArrayList<>();
        long time = System.currentTimeMillis();
        for (int i = 0; i < 100000; i++) {
//            result.add(Utils.compressSnappy(byteArray, RedisTools.BYTE_STR_FLAG));
            result.add(Base64.getEncoder().encodeToString(byteArray));
//            result.add(Utils.compressBytesLZ4(byteArray, RedisTools.BYTE_STR_FLAG));
        }
        long time2 = System.currentTimeMillis();
        long timeCost = time2-time;
        for (int i = 0; i < 100000; i++) {
//            Utils.decompressSnappy(result.get(i), RedisTools.BYTE_STR_FLAG);
            Base64.getDecoder().decode(result.get(i));
//            Utils.decompressBytesLZ4(result.get(i), RedisTools.BYTE_STR_FLAG);
        }
        long time3 = System.currentTimeMillis();
        long timeCost2 = time3-time2;
        System.out.println(Utils.createStr("base64 compressTimeCost={}, decompressTimeCost={}",timeCost,timeCost2));
    }
    private static void calSnappy(byte[] byteArray){
        List<String> result = new ArrayList<>();
        long time = System.currentTimeMillis();
        for (int i = 0; i < 100000; i++) {
            result.add(Utils.compressSnappy(byteArray, RedisTools.BYTE_STR_FLAG));
        }
        long time2 = System.currentTimeMillis();
        long timeCost = time2-time;
        for (int i = 0; i < 100000; i++) {
            Utils.decompressSnappy(result.get(i), RedisTools.BYTE_STR_FLAG);
        }
        long time3 = System.currentTimeMillis();
        long timeCost2 = time3-time2;
        System.out.println(Utils.createStr("snappy compressTimeCost={}, decompressTimeCost={}",timeCost,timeCost2));
    }

    private static void calLz4(byte[] byteArray){
        List<String> result = new ArrayList<>();
        long time = System.currentTimeMillis();
        for (int i = 0; i < 100000; i++) {
            result.add(Utils.compressBytesLZ4(byteArray, RedisTools.BYTE_STR_FLAG));
        }
        long time2 = System.currentTimeMillis();
        long timeCost = time2-time;
        for (int i = 0; i < 100000; i++) {
            Utils.decompressBytesLZ4(result.get(i), RedisTools.BYTE_STR_FLAG);
        }
        long time3 = System.currentTimeMillis();
        long timeCost2 = time3-time2;
        System.out.println(Utils.createStr("lz4 compressTimeCost={}, decompressTimeCost={}",timeCost,timeCost2));
    }


    public static void compareListContain(){
        List<String> list1 = new ArrayList<String>();
        List<String> list2 = new ArrayList<String>();
        for (int i = 0; i < 30000; i++) {
            list1.add("test" + i);
        }
        for (int i = 0; i < 80000; i++) {
            list2.add("test" + i * 2);
        }
        getDiffrent1(list1, list2);
        getDiffrent2(list1, list2);
        getDiffrent3(list1, list2);
        getDiffrent4(list1, list2);
    }

    // 方法1，两层遍历查找，遍历次数为list1.size()*list2.size()，有点蠢
    private static List<String> getDiffrent1(List<String> list1, List<String> list2) {
        // diff 存放不同的元素
        List<String> diff = new ArrayList<String>();
        // 开始查找的时间，用于计时
        long start = System.currentTimeMillis();
        for (String str : list1) {
            if (!list2.contains(str)) {
                diff.add(str);
            }
        }
        // 计时
        System.out.println("方法1 耗时：" + (System.currentTimeMillis() - start) + " 毫秒");
        return diff;
    }

    // 方法2，两层遍历查找，用retainAll()方法查找，也很蠢，方法底层依旧是两层遍历
    private static List<String> getDiffrent2(List<String> list1, List<String> list2) {
        long start = System.currentTimeMillis();
        list1.retainAll(list2);// 返回值是boolean
        System.out.println("方法2 耗时：" + (System.currentTimeMillis() - start) + " 毫秒");
        return list1;
    }

    // 方法3，用Map存放List1和List2的元素作为key，value为其在List1和List2中出现的次数
    // 出现次数为1的即为不同元素，查找次数为list1.size() + list2.size()，较方法1和2，是极大简化
    private static List<String> getDiffrent3(List<String> list1, List<String> list2) {
        List<String> diff = new ArrayList<String>();
        long start = System.currentTimeMillis();
        Map<String, Integer> map = new HashMap<String, Integer>(list1.size() + list2.size());
        // 将List1元素放入Map，计数1
        for (String string : list1) {
            map.put(string, 1);
        }
        // 遍历List2，在Map中查找List2的元素，找到则计数+1；未找到则放入map，计数1
        for (String string : list2) {
            Integer count = map.get(string);
            if (count != null) {
                map.put(string, ++count);// 此处可优化，减少put次数，即为方法4
                continue;
            }
            map.put(string, 1);
        }
        for (Map.Entry<String, Integer> entry : map.entrySet()) {
            if (entry.getValue() == 1) {
                diff.add(entry.getKey());
            }
        }
        System.out.println("方法3 耗时：" + (System.currentTimeMillis() - start) + " 毫秒");
        return diff;
    }

    // 优化方法3，先遍历大的list，再遍历小的list，减少put次数
    private static List<String> getDiffrent4(List<String> list1, List<String> list2) {
        List<String> diff = new ArrayList<String>();
        long start = System.currentTimeMillis();
        Map<String, Integer> map = new HashMap<String, Integer>(list1.size() + list2.size());
        List<String> maxList = list1;
        List<String> minList = list2;
        if (list2.size() > list1.size()) {
            maxList = list2;
            minList = list1;
        }
        for (String string : maxList) {
            map.put(string, 1);
        }
        for (String string : minList) {
            Integer count = map.get(string);
            if (count != null) {
                map.put(string, ++count);
                continue;
            }
            map.put(string, 1);
        }
        for (Map.Entry<String, Integer> entry : map.entrySet()) {
            if (entry.getValue() == 1) {
                diff.add(entry.getKey());
            }
        }
        System.out.println("方法4 耗时：" + (System.currentTimeMillis() - start) + " 毫秒");
        return diff;
    }

}
