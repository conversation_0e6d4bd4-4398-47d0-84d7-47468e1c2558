package org.gof.demo.distr.cross;

import io.vertx.core.AsyncResult;
import io.vertx.core.Future;
import io.vertx.core.Handler;
import org.gof.core.Port;
import org.gof.core.support.Distr;
import org.gof.core.support.Param;
import org.gof.core.support.function.GofFunction3;
import org.gof.demo.distr.admin.AdminCenterManager;
import org.gof.demo.distr.admin.AdminCenterServiceProxy;
import org.gof.demo.distr.cross.domain.CrossPoint;
import org.gof.demo.distr.cross.domain.CrossType;
import org.gof.demo.distr.world.WorldLocalServiceProxy;
import org.gof.demo.worldsrv.support.D;
import org.gof.demo.worldsrv.support.Log;
import org.slf4j.Logger;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class CrossManager {

    private static Logger log = Log.game;
    private static CrossManager instance = new CrossManager();
    public static CrossManager getInstance() {
        return instance;
    }

    // key-区服id，合并到的nodeId
    public Map<Integer, String> mergeServerNodes = new ConcurrentHashMap<>();

    public Map<String, List<Integer>> nodeIdServerIds = new HashMap<>();

    private Map<Integer,Map<CrossType,CrossPoint>> cachedCrossPoints = new ConcurrentHashMap<>();

    /**
     * 获取区服当前的nodeId
     * @param serverId
     * @return
     */
    public String getLocalServerNodeId(int serverId) {
        String nodeId = mergeServerNodes.get(serverId);
        return nodeId;
    }

    /**
     * gof-rpc方式回调
     * @param crossType
     * @param serverId
     */
    public void callCrossFunc(CrossType crossType, int serverId, GofFunction3<Boolean, Param, Param> method, Object...context) {
        AdminCenterServiceProxy proxy = AdminCenterManager.createAdminProxy();
        proxy.getCrossServer(crossType.getType(), serverId);
        Param userContext = new Param(context);
        Param callContext = new Param();
        callContext.put("asyncRetId", Port.getCurrent().createReturnAsync());
        callContext.put("crossType",crossType);
        callContext.put("serverId",serverId);
        callContext.put("userContext",userContext);
        callContext.put("gofCallBackMethod",method);
        proxy.listenResult(this::_resultOfGetCrossServer,callContext);
    }

    public void callCrossFunc(CrossType crossType, int serverId, GofFunction3<Boolean, Param, Param> method, Param context) {
        AdminCenterServiceProxy proxy = AdminCenterManager.createAdminProxy();
        proxy.getCrossServer(crossType.getType(), serverId);
        Param callContext = new Param();
        callContext.put("crossType",crossType);
        callContext.put("serverId",serverId);
        callContext.put("userContext",context);
        callContext.put("gofCallBackMethod",method);
        proxy.listenResult(this::_resultOfGetCrossServer,callContext);
    }

    /**
     * 回调方式处理
     * @param crossType
     * @param serverId
     * @param crossBack
     */
    public void callCrossFunc(CrossType crossType, int serverId, Handler<AsyncResult<CrossPoint>> crossBack){
        Map<CrossType, CrossPoint> typePoints = cachedCrossPoints.get(serverId);
        if(typePoints !=null){
            CrossPoint crossPoint = typePoints.get(crossType);
            if(crossPoint!=null){
                crossBack.handle(Future.succeededFuture(crossPoint));
                return;
            }
        }
        AdminCenterServiceProxy proxy = AdminCenterManager.createAdminProxy();
        proxy.getCrossServer(crossType.getType(), serverId);
        Param context = new Param("crossBack",crossBack);
        context.put("crossType",crossType);
        context.put("serverId",serverId);
        proxy.listenResult(this::_resultOfGetCrossServer,context);
    }

    public void _resultOfGetCrossServer(boolean isTimeout, Param returns, Param context){
        Handler<AsyncResult<CrossPoint>> crossBack = context.get("crossBack");
        if(isTimeout){
            log.error("获取跨服分组超时！crossType={},serverId={}",context.get("crossType"),context.get("serverId"));
            if(crossBack!=null) {
                crossBack.handle(Future.failedFuture("获取跨服分组超时！"));
            }else{
                GofFunction3<Boolean, Param, Param> method = context.get("gofCallBackMethod");
                method.apply(true,new Param(),context.get("userContext"));
            }
            return;
        }
        String groupId = returns.get("groupId");
        if(groupId==null){//未找到配置
            log.error("获取跨服分组时未找到对应group配置！crossType={},serverId={}",context.get("crossType"),context.get("serverId"));
            if(crossBack!=null) {
                crossBack.handle(Future.failedFuture("获取跨服分组超时！"));
            }
            return;
        }
        CrossPoint point = new CrossPoint();
        point.setGroupId(groupId);
        String servRegin = returns.get("servRegion");
        point.setServRegion(servRegin);
        String crossNodeId = returns.get("crossNodeId");
        point.setNodeId(crossNodeId);
        int serverId = context.get("serverId");
        CrossType crossType = context.get("crossType");
        Map<CrossType, CrossPoint> typePoints = cachedCrossPoints.putIfAbsent(serverId,new ConcurrentHashMap<>());
        if(typePoints==null){
            typePoints = cachedCrossPoints.get(serverId);
        }
        typePoints.put(crossType,point);
        if(crossBack!=null) {
            crossBack.handle(Future.succeededFuture(point));
        }else{
            GofFunction3<Boolean, Param, Param> method = context.get("gofCallBackMethod");
            method.apply(false,new Param(point),context.get("userContext"));
        }
    }

    public void resetCachedCrossPoints(){
        cachedCrossPoints.clear();
    }
}
