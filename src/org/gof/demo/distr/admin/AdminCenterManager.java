package org.gof.demo.distr.admin;

import org.gof.core.Node;
import org.gof.core.support.*;
import org.gof.core.support.observer.Listener;
import org.gof.demo.distr.DistrKit;
import org.gof.demo.worldsrv.arena.ArenaCrossServiceProxy;
import org.gof.demo.worldsrv.support.D;
import org.gof.demo.worldsrv.support.observer.EventKey;

import java.util.ArrayList;
import java.util.List;

public class AdminCenterManager extends ManagerBase {

    //增加缓存处理，避免服务器还未启动完成时，已经收到远程节点的连接了
    private boolean serverStartOk = false;
    private List<KeyValue<Integer,Param>> delayNodeRegEvents = new ArrayList<>();

    @Listener(EventKey.GAME_STARTUP_FINISH)
    public void onServerStartFinish(Param param){
        serverStartOk=true;
        for(KeyValue<Integer,Param> event : delayNodeRegEvents){
            AdminCenterServiceProxy proxy = createAdminProxy();
            Param eventParam = event.getValue();
            if(event.getKey()==NodeEventKey.NODE_REGISTER){
                proxy.onNodeRegister(eventParam);
            }else if(event.getKey()==NodeEventKey.NODE_UNREGISTER) {
                proxy.onNodeUnregister(eventParam);
            }
        }
        delayNodeRegEvents.clear();
        delayNodeRegEvents=null;
    }

    @Listener(NodeEventKey.NODE_REGISTER)
    public void onNodeRegister(Param params) {
        Node node = Node.getInstance();
        if(NodeType.ADMIN.isMatch(node.getNodeType())){
            if(serverStartOk){
                AdminCenterServiceProxy proxy = createAdminProxy();
                proxy.onNodeRegister(params);

                if(S.isCross){
                    ArenaCrossServiceProxy proxyCrossArena = ArenaCrossServiceProxy.newInstance();
                    if(proxyCrossArena != null){
                        proxyCrossArena.onNodeRegister();
                    }
                }
            }else{
                KeyValue<Integer,Param> event = new KeyValue<>();
                event.setKey(NodeEventKey.NODE_REGISTER);
                event.setValue(params);
                delayNodeRegEvents.add(event);
            }
        }
    }

    @Listener(NodeEventKey.NODE_UNREGISTER)
    public void onNodeUnregister(Param params) {
        Node node = Node.getInstance();
        if(NodeType.ADMIN.isMatch(node.getNodeType())){
            if(serverStartOk){
                AdminCenterServiceProxy proxy = createAdminProxy();
                proxy.onNodeUnregister(params);
            }else{
                KeyValue<Integer,Param> event = new KeyValue<>();
                event.setKey(NodeEventKey.NODE_UNREGISTER);
                event.setValue(params);
                delayNodeRegEvents.add(event);
            }
        }
    }

    public static AdminCenterServiceProxy createAdminProxy(){
        return AdminCenterServiceProxy.newInstance(Distr.NODE_ADMIN_SERVER);
    }

}
