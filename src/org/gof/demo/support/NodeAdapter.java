package org.gof.demo.support;

import org.gof.core.support.Distr;
import org.gof.core.support.S;
import org.gof.core.support.function.GofReturnFunction0;
import org.gof.core.support.function.GofReturnFunction1;
import org.gof.demo.worldsrv.support.D;

/**
 * 节点适配器
 * <AUTHOR>
 */
public class NodeAdapter {

    public static String world(int serverId){
        return D.NODE_WORLD_BRIDGE_PREFIX + serverId;
    }

    // 接管函数
    public static GofReturnFunction1<String,String> remoteNodeIdFunc = (servId) -> S.isBridge ? S.isGameLeagueOpen ? D.NODE_BRIDGE_LEAGUE: D.NODE_BRIDGE_DEFAULT : Distr.NODE_DEFAULT;



    /**
     * 获取跨服NodeId
     * 从服务器访问跨服时使用
     * @return
     */
    public static String bridge(){
        return bridge(false);
    }

    public static String bridge(boolean isLeague){
        if(isLeague){
            return Distr.NODE_BRIDGE_LEAGUE;
        }
        return Distr.NODE_BRIDGE_DEFAULT;
    }
}
