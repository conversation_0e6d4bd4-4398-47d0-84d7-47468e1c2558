package org.gof.demo.support.statistics;

import com.google.common.collect.MapMaker;
import org.apache.commons.collections4.CollectionUtils;
import org.gof.core.Node;
import org.gof.core.support.Config;
import org.gof.core.support.SafeKeyData;
import org.gof.core.support.Utils;
import org.gof.core.support.log.LogCore;
import org.gof.core.support.prettytable.FlipTableConverters;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.stage.StageObjectService;
import org.gof.demo.worldsrv.stage.StagePort;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import static org.gof.core.statistics.StatisticsUtils.formatShowCount;
import static org.gof.core.statistics.StatisticsUtils.runningTime;


/**
 * 玩家信息统计
 */
public class StatisticsHuman {
	//监控开始时间
	private static long timeStart = System.currentTimeMillis();

	//<数据>
	private static DATA data = new DATA();

	//实例弱引用，监控是否有对象无法回收<PortId, 玩家引用>
	private static SafeKeyData<String, Set<HumanObject>> instances = new SafeKeyData<>();

	/**
	 * 统计数据
	 */
	private static class DATA {
		//登陆次数
		public final AtomicLong login = new AtomicLong();
		//登出次数
		public final AtomicLong logout = new AtomicLong();
	}

	/**
	 * 统计登陆事件
	 */
	public static void login(HumanObject human) {
		data.login.incrementAndGet();

		//监控对象实例
		Set<HumanObject> inst = instances.computeIfAbsent(human.getPortId(), k -> Collections.newSetFromMap(new MapMaker().weakKeys().makeMap()));
		inst.add(human);
	}

	/**
	 * 统计登陆事件
	 */
	public static void logout() {
		data.logout.incrementAndGet();
	}

	/**
	 * 生成统计结果
	 * @return
	 */
	public static void showResult() {
		//活跃的地图
		List<Long> inst = findInstance();
		List<Long> active = findActive();

		//差值
		Collection<Long> diff = CollectionUtils.subtract(inst, active);

		//返回信息模板
		String rst = "\n========================\n" +
				"==  玩家 - {}  ==\n" +
				"========================\n" +
//				"实例数\t活跃数\t登陆数\t登出数\n" +
//				"{}\t{}\t{}\t{}\n\n" +
				"{}" +
				"不活跃但仍未清理的十个随机ID：\n{}\n\n" +
				"实例数(InstanceCount)：JVM中存在的HuamnObject对象，登出后第一次YGC时会回收，如果已经入老年代，则FGC后才会回收。\n" +
				"活跃数(ActivityCount)：地图管理中的HumanObject对象，如果与上面的实例数相差太大，则有可能是对象被引用无法被收回，需要仔细查看。\n"
				;

		String[] headers = {"InstanceNum","ActivityCount","LoginCount","LogoutCount"};
		int titleLen = headers.length;
		int dataLen = 1;
		Object[][] table = new String[dataLen][titleLen];

		//地图创建销毁总数
		String timeDiff = runningTime(System.currentTimeMillis() - timeStart);

		table[0] = new String[]{
				formatShowCount(inst.size()),
				formatShowCount(active.size()),
				formatShowCount(data.login.get()),
				formatShowCount(data.logout.get()),
		};
		String tableStr = FlipTableConverters.fromObjects(headers, table);
		//输出结果
		rst = Utils.createStr(
				rst,
				timeDiff,
				tableStr,
				diff.stream().limit(10).collect(Collectors.toList())
		);
		if(Config.STATISTICS_ENABLE){
			LogCore.statistics.info(rst);
		}
	}

	/**
	 * 获取当前实际仍然活跃的对象ID
	 * 只是通过地图进行反向查找，不能完全确保玩家不被错误的引用造成没有释放
	 * @return
	 */
	private static List<Long> findActive() {
		Node node = Node.getInstance();
		return node.getPorts().values().stream()
				.filter(p -> p instanceof StagePort)
				.flatMap(p -> p.getServices().stream())
				.filter(s -> s instanceof StageObjectService)
				.flatMap(s -> ((StageObjectService) s).getStageObj().getHumanObjs().values().stream())
				.map(h -> h.id)
				.collect(Collectors.toList());
	}

	/**
	 * 获取当前内存中的对象ID
	 * 这里与findActive()返回的数量不应该差别太大，否则可能是有内存泄露
	 * @return
	 */
	private static List<Long> findInstance() {
		return instances.values().stream()
                    .flatMap(s -> s.stream())
                    .map(h -> h.id)
                    .collect(Collectors.toList());
	}
}
