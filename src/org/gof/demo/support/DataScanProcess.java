package org.gof.demo.support;

import org.gof.core.support.Config;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.NameFix;

import java.io.File;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 数据扫描
 * 
 * <AUTHOR>
 *
 */
public class DataScanProcess {

	private static DataScanProcess instance = new DataScanProcess();

	public static DataScanProcess getInstance() {
		return instance;
	}

	/**
	 * 写死一个定位文件好了
	 */
	private static final String LOCA = "ConfGlobal.json";
	private static final String PACAKGE = "org.gof.demo.worldsrv.config";

	private File basePackage = null;
	private Map<String, String> confMapping = new HashMap<String, String>();
	private Map<String, String> nameMapping = new HashMap<String, String>();
	private List<String> changedFile = new LinkedList<String>();
	private Scanner scanner = null;
	private ScheduledExecutorService service = null;

	public void init() throws Exception {
		try {
			service = Executors.newScheduledThreadPool(1);
			
			if(!initPackage())
				return;
			
			// 增加基础扫描数据
			scanner = new Scanner();
			scanner.scanConf();
			scanner.scanTxt();

			// 5分钟扫描一次
			service.scheduleAtFixedRate(new Task(), 10, Config.DATA_DEBUG ? 5
					: 5 * 60, TimeUnit.SECONDS);

		} catch (Exception e1) {
			Log.game.error("定位文件异常 {}", e1.toString());
		}

	}
	
	public boolean initPackage(){
		String loca = Thread.currentThread().getContextClassLoader()
				.getResource(LOCA).getPath();

		int idx = loca.lastIndexOf("/");
		if (idx == -1) {
			Log.game.error("定位文件目录错误");
			return false;
		}

		loca = loca.substring(0, idx);
		basePackage = new File(loca);
		if (basePackage.isFile()) {
			Log.game.error("定位文件目录错误");
			return false;
		}
		
		return true;
	}

	public void loadAllData() {
		String fileName;
		String clazzName;
		File[] files = basePackage.listFiles();
		if (files != null) {
			for (File file : files) {
				fileName = file.getName();
				if (file.isFile() && fileName.endsWith(".json")) {
					clazzName = PACAKGE + "." + fileName.substring(0, fileName.lastIndexOf("."));
					//这里要先判断这个class是否存在
					DataReloadManager.inst().loadConf(clazzName);
				}
			}
		}

		try {
			//重新加载全局缓存
			GlobalConfVal.reloadConfVal();
		}catch (Exception e){
			Log.game.error("===加载全局缓存错误 e={}", e);
		}
	}

	class Task implements Runnable {
		@Override
		public void run() {
			try {
				if (scanner.isChanged()) {
					Log.game.error("[热更新]策划数据文件改变,重新加载.");
					scanner.reloadConfData();
					Log.game.error("[热更新]策划数据加载完毕..");
				}
				// 文本文件的扫描
				scanner.scanAndReloadTxt();
			} catch (Exception e) {
				Log.temp.error("[热更新]策划数据出错，e={}", e);
			}
		}

	}

	class Scanner {

		public Scanner() {

		}

		public void reloadConfData() throws Exception {
			String clazzName;
			for (String name : changedFile) {
				clazzName = PACAKGE + "."
						+ name.substring(0, name.lastIndexOf("."));
				DataReloadManager.inst().reLoadConf(clazzName);
			}
			try {
				//重新加载全局缓存
				GlobalConfVal.reloadConfVal();
			}catch (Exception e){
				Log.game.error("[热更新]加载全局缓存错误，e={}", e);
			}
			changedFile.clear();
		}

		public void scanConf() {
			if (basePackage == null)
				return;

			if (!confMapping.isEmpty()) {
				return;
			}
			File[] files = basePackage.listFiles();
			if (files != null) {
				for (File file : files) {
					if (file.isFile() && file.getName().endsWith(".json")) {
						String multi_key = getValue(file);
						confMapping.put(file.getName(), multi_key);
					}
				}
			}
		}

		public void scanTxt() {
			if (!nameMapping.isEmpty()) {
				return;
			}

			scanSingleTxtFile(NameFix.SHIELD_FILE_NAME);
			scanSingleTxtFile(NameFix.FIRST_NAME_MALE_FILE_NAME);
			scanSingleTxtFile(NameFix.FIRST_NAME_FEMALE_FILE_NAME);
			scanSingleTxtFile(NameFix.FAMILY_NAME_FILE_NAME);
			scanSingleTxtFile(NameFix.NAME_CHAR);
			scanSingleTxtFile(NameFix.SHIELD_NAME);
			scanSingleTxtFile(NameFix.Content_CHAR);
			scanSingleTxtFile(NameFix.accountTxt);

		}

		private void scanSingleTxtFile(String fName) {
			String path = Thread.currentThread().getContextClassLoader()
					.getResource(fName).getPath();
			File file = new File(path);
			String multi_key = getValue(file);
			nameMapping.put(fName, multi_key);
		}

		private String getValue(File source) {
			String contentLength = String.valueOf((source.length()));
			String lastModified = String.valueOf((source.lastModified()));
			return new StringBuilder(contentLength).append(lastModified)
					.toString();
		}

		public boolean isChanged() {
			if (basePackage == null)
				return false;

			boolean isChanged = false;
			String fileName;
			File[] files = basePackage.listFiles();
			if (files != null) {
				for (File file : files) {
					fileName = file.getName();
					if (file.isFile() && fileName.endsWith(".json")) {
						String value = confMapping.get(fileName);
						String multi_key = getValue(file);
						if (!multi_key.equals(value)) {
							isChanged = true;
							confMapping.put(fileName, multi_key);
							changedFile.add(fileName);
						}
					}
				}
			}
			return isChanged;
		}

		public void scanAndReloadTxt() {
			if (scanAndReloadSingleTxt(NameFix.SHIELD_FILE_NAME)) {
				NameFix.reloadShield();
			}
			if(scanAndReloadSingleTxt(NameFix.SHIELD_NAME)){
				NameFix.reloadNameShield();
			}

			if (scanAndReloadSingleTxt(NameFix.FIRST_NAME_MALE_FILE_NAME)
					|| scanAndReloadSingleTxt(NameFix.FIRST_NAME_FEMALE_FILE_NAME)
					|| scanAndReloadSingleTxt(NameFix.FAMILY_NAME_FILE_NAME)) {
				NameFix.reloadName();
			}
			if(scanAndReloadSingleTxt(NameFix.NAME_CHAR)){
				NameFix.reloadNameChar();
			}
			if(scanAndReloadSingleTxt(NameFix.Content_CHAR)){
				NameFix.reloadcontentChar();
			}
			if(scanAndReloadSingleTxt(NameFix.accountTxt)){
				NameFix.reloadAccount();
			}
		}

		private boolean scanAndReloadSingleTxt(String fileName) {
			String path = Thread.currentThread().getContextClassLoader()
					.getResource(fileName).getPath();
			File file = new File(path);
			String multi_key = getValue(file);
			String value = nameMapping.get(fileName);
			if (!multi_key.equals(value)) {
				nameMapping.put(fileName, multi_key);
				return true;
			}
			return false;
		}
	}

	public void destroy() throws Exception {
		if (service != null) {
			service.shutdownNow();
		}
	}
}
