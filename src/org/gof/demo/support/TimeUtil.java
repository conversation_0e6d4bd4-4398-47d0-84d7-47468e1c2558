package org.gof.demo.support;


import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.temporal.ChronoField;
import java.time.temporal.TemporalAccessor;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.TimeZone;

import static java.time.temporal.ChronoField.*;

/**
 * 时间工具类
 */
public class TimeUtil {

	public static final int SECOND_TIME = 1000;
	public static final int MINUTE_TIME = 60 * SECOND_TIME;
	public static final int HOUR_TIME = 60 * MINUTE_TIME;
	public static final long DAY_TIME = 24 * HOUR_TIME;
	public static final int MINUTE_SEC = 60;
//	/** 今日0点时间 */
//	private static int today0ClockTime=0;
	// 北京时间跟GMT的时差
	private static int timeOffset = 8 * HOUR_TIME;

	static{
		timeOffset = TimeZone.getDefault().getRawOffset();
	}
	
	/**
	 * 判断两个时间是否在同一天
	 * 
	 * @param time
	 *            (毫秒ms)
	 * @param otherTime
	 *            (毫秒ms)
	 * @return
	 */
	public static boolean isSameDay(long time, long otherTime) {
		long distance = time-otherTime;
		if (distance>=DAY_TIME || distance<=-DAY_TIME)
			return false;
		return getBetweenDays(time, otherTime)==0;
	}

	/**
	 * 判断是否进行5点刷新
	 * @param time
	 * @param otherTime
	 * @return
	 */
	public static boolean is5ClockDayDuration(long time, long otherTime) {
		long distance = time-otherTime;
		if (distance>=DAY_TIME || distance<=-DAY_TIME)
			return false;
		return getBettwenDaysOf5Clock(time, otherTime)==0;
	}
	
	/**
	 * 判断两个时间戳是否在同一周 
	 * @param time
	 * @param otherTime
	 * @return
	 */
	public static boolean isSameWeek(long time, long otherTime) {
		//8小时时差+三天便宜（1970.1.1为星期四）
		return (time+timeOffset+DAY_TIME*3) / DAY_TIME / 7 - (otherTime+timeOffset+DAY_TIME*3) / DAY_TIME / 7 == 0;
	}
	
	/**
	 * 判断两个时间戳是否在同一周（以周一凌晨五点做跨越点） 
	 * @param time
	 * @param otherTime
	 * @return
	 */
	public static boolean isSameWeekOfFive(long time, long otherTime) {
		//8小时时差+三天便宜（1970.1.1为星期四）
		return (time+3*HOUR_TIME+DAY_TIME*3) / DAY_TIME / 7 - (otherTime+3*HOUR_TIME+DAY_TIME*3) / DAY_TIME / 7 == 0;
	}
	
	/**
	 * 判断进行5点刷新了多少次
	 */
	public static int fiveClockDayDurationNum(long time, long otherTime) {
		int bettwenDays = (int) ((time+3*HOUR_TIME) / DAY_TIME - (otherTime+3*HOUR_TIME) / DAY_TIME);
		return bettwenDays;
	}
	
	/**
	 * 获取以5点为跨天时间点的两个时间点的间隔天数 @note：需判断返回值的正负
	 * @param time
	 * @param otherTime
	 * @return
	 */
	public static int getBettwenDaysOf5Clock(long time, long otherTime){
		return (int) ((time+3*HOUR_TIME) / DAY_TIME - (otherTime+3*HOUR_TIME) / DAY_TIME);
	}
	
	/**
	 * 获取相隔天数
	 * 
	 * @param time
	 *            (毫秒ms)
	 * @param otherTime
	 *            (毫秒ms)
	 * @return 大于0表示 time在otherTime之后的天数，否则相反
	 */
	public static int getBetweenDays(long time, long otherTime) {
		return (int) ((time+timeOffset) / DAY_TIME - (otherTime+timeOffset) / DAY_TIME);
	}
	/**时差**/
	private static final ZoneOffset zoneOffSet = ZoneOffset.ofHours(8);

	public static final String DateFormatter="yyyy-MM-dd";
	public static final String DateTimeFormatter="yyyy-MM-dd HH:mm:ss";
	private static final DateTimeFormatter f1;//yyyymmdd
	private static final DateTimeFormatter f2;//yyyymmddhh
	private static final DateTimeFormatter f3;//yyyymmddhhmm
	private static final DateTimeFormatter f4;//yyyymmddhhmmss
	static {
		f1 = new DateTimeFormatterBuilder().parseLenient()
	            .appendValue(YEAR, 4)
	            .appendValue(MONTH_OF_YEAR, 2)
	            .appendValue(DAY_OF_MONTH, 2).toFormatter();
		f2 = new DateTimeFormatterBuilder().parseLenient()
				.appendValue(YEAR, 4)
				.appendValue(MONTH_OF_YEAR, 2)
				.appendValue(DAY_OF_MONTH, 2)
				.appendValue(HOUR_OF_DAY,2)
				.toFormatter();
		f3 = new DateTimeFormatterBuilder().parseLenient()
				.appendValue(YEAR, 4)
				.appendValue(MONTH_OF_YEAR, 2)
				.appendValue(DAY_OF_MONTH, 2)
				.appendValue(HOUR_OF_DAY,2)
				.appendValue(MINUTE_OF_HOUR,2)
				.toFormatter();
		f4 = new DateTimeFormatterBuilder().parseLenient()
				.appendValue(YEAR, 4)
				.appendValue(MONTH_OF_YEAR, 2)
				.appendValue(DAY_OF_MONTH, 2)
				.appendValue(HOUR_OF_DAY,2)
				.appendValue(MINUTE_OF_HOUR,2)
				.appendValue(SECOND_OF_MINUTE,2)
				.toFormatter();
	}
	
	/**
	 * 解析时间格式字符串为epochmils
	 * {@link #f1}
	 * {@link #f2}
	 * {@link #f3}
	 * {@link #f4}
	 * @param dateFormatStr
	 * @return
	 */
	public static long parseLocalTime(String dateFormatStr) {
		try {
			TemporalAccessor time = null;
			switch (dateFormatStr.length()) {
			case 8: {
				time = f1.parse(dateFormatStr);
				return time.getLong(ChronoField.EPOCH_DAY)*86400-28800;
				}
			case 10: time = f2.parse(dateFormatStr);break;
			case 12: time = f3.parse(dateFormatStr);break;
			case 14: time = f4.parse(dateFormatStr);break;
			}
			if (time == null) {
				throw new IllegalArgumentException("解析配置时间字符串出错！！！{"+dateFormatStr+"}，解析格式不存在！！！");
			}
			return (time.getLong(ChronoField.EPOCH_DAY)*86400+time.getLong(ChronoField.SECOND_OF_DAY)-zoneOffSet.getTotalSeconds())*1000;
		} catch (Exception e) {
			throw new IllegalArgumentException("解析配置时间字符串出错！！！{"+dateFormatStr+"}，格式有误！！！", e);
		}
	}
	
	/**
	 * 转存epochsecs为{@link #f4}格式字符串
	 * @param epochSecs
	 * @return
	 */
	public static String formatLocalTime(int epochSecs) {
		LocalDateTime dt = LocalDateTime.ofEpochSecond(epochSecs, 0, zoneOffSet);
		return dt.format(f4);
	}
	
	/**
	 * @return 当前是否为每月一号凌晨五点零分
	 */
	public static boolean monthlyFirstDay5Clock() {
		LocalTime lt = LocalTime.now();
		if (lt.getMinute() != 0)
			return false;
		if (lt.getHour() != 5)
			return false;
		LocalDate ld = LocalDate.now();
		return ld.getDayOfMonth() == 1;
	}
	
	/**
	 * 解析时间
	 * 
	 * @param dateFormatStr
	 *            格式：yyyy-MM-dd HH:mm:ss
	 * @return
	 */
	public static long parseTime(String dateFormatStr) {
		try {
			return DateFormat.getDefaultFormat().parse(dateFormatStr).getTime();
		} catch (ParseException e) {
			throw new RuntimeException("解析时间出错：格式不正确。dateFormatStr=" + dateFormatStr, e);
		}
	}

	/**
	 * 解析时间
	 *
	 * @param dateStr
	 * @param format
	 *
	 * @return
	 */
	public static long parseTime(String dateStr, String format) {
		try {
			return new SimpleDateFormat(format).parse(dateStr).getTime();
		} catch (ParseException e) {
			throw new RuntimeException("解析时间出错：格式不正确。dateFormatStr=" + dateStr, e);
		}
	}
	
	public static String formatTime(long time){
		return DateFormat.getDefaultFormat().format(new Date(time));
	}
	
	public static String formatTime(long time, String formater){
		return new SimpleDateFormat(formater).format(new Date(time));
	}
	
	public static String formatDate(Date date){
		return DateFormat.getDefaultFormat().format(date);
	}
	
	public static String formatDate(Date date, String formater){
		return new SimpleDateFormat(formater).format(date);
	}
	
	/**
	 * 获取开始时间
	 * 
	 * <pre>
	 * 自定义时间格式说明：
	 * 类型说明：1-起始时间 2-根据配置每天执行 3-按周计算 4-以某时间点开始，每隔一定时间执行一次
	 * 类型	时间格式												说明
	 * 1 	1 2015-12-18 23:00:00,2016-1-12 10:00:00			2015年12月18日 23点  至 2016年1月12日 10点
	 * 2 	2 * * * 1000-1030									每天10点到10点30分
	 * 2 	2 2015-2016 12-1 18-12 2300-1000					2015年12月18日 至 2016年1月12日 每天23点到10点 
	 * 2 	2 2015-2016 12-1 18-12 2300,1000					2015年12月18日 至 2016年1月12日 每天23点,10点 各执行一次
	 * 2 	2 2015-2016 12-1 18-12 2300-1000,1130-1200			2015年12月18日 至 2016年1月12日 每天23点到10点 ，11点30到12点
	 * 3	3 * * * 2,4,6 1000-1200								每周2,4,6的10点到12点
	 * 3	3 2015 * 1-10 2,4,6 1000-1200						2015年第一周到第十周	每周2,4,6的10点到12点
	 * 4	4 2015-8-1 12:00:00 3600							2015年8月1日12点起 每隔1小时执行一次
	 * 5	5 1-7 1200											开服后第1-7天，12点执行一次
	 * 5	5 1-7 1200-1300										开服后第1-7天，12点到13点持续
	 * 5	5 1-7 *												开服后第1-7天持续
	 * 6	6 1-7 1200											合服后第1-7天，12点执行一次
	 * 6	6 1-7 1200-1300										合服后第1-7天，12点到13点持续
	 * 6	6 1-7 *												合服后第1-7天持续
	 * </pre>
	 * 
	 * @param customFormatTimeStr
	 * @return
	 * @throws ParseException
	 */
	public static long getBeginTime(String customFormatTimeStr) throws ParseException {
		int type = Integer.parseInt(customFormatTimeStr.substring(0, 1));
		String timeStr = customFormatTimeStr.substring(2, customFormatTimeStr.length());
		if (type == 1 || type==4) {			
			timeStr = timeStr.split(Symbol.DOUHAO_REG)[0];
			return DateFormat.getDefaultFormat().parse(timeStr).getTime();
		} else if (type == 2) {
			Calendar cal = Calendar.getInstance();
			String[] span = timeStr.split(" ");
			if(!Symbol.XINGHAO.equals(span[0])){
				int year = Integer.parseInt(span[0].substring(0, 4));
				cal.set(Calendar.YEAR, year);
			}
			if(!Symbol.XINGHAO.equals(span[1])){
				int target = 1;
				if(Character.isDigit(span[1].charAt(1))){
					target = 2;
				}
				int month = Integer.parseInt(span[1].substring(0,target));
				cal.set(Calendar.MONTH, month);
			}
			if(!Symbol.XINGHAO.equals(span[2])){
				int target = 1;
				if(Character.isDigit(span[2].charAt(1))){
					target = 2;
				}
				int day = Integer.parseInt(span[2].substring(0,target));
				cal.set(Calendar.DAY_OF_MONTH, day);
			}
			if(!Symbol.XINGHAO.equals(span[3])){
				int time = Integer.parseInt(span[3].substring(0,4));
				cal.set(Calendar.HOUR_OF_DAY, time/100);
				cal.set(Calendar.MINUTE, time%100);
			}
			cal.set(Calendar.SECOND,0);
			cal.set(Calendar.MILLISECOND, 0);
			return cal.getTimeInMillis();
		}else if(type==3){
			Calendar cal = Calendar.getInstance();
			String[] span = timeStr.split(" ");
			if(!Symbol.XINGHAO.equals(span[0])){
				int year = Integer.parseInt(span[0].substring(0, 4));
				cal.set(Calendar.YEAR, year);
			}
			if(!Symbol.XINGHAO.equals(span[1])){
				int target = 1;
				if(Character.isDigit(span[1].charAt(1))){
					target = 2;
				}
				int month = Integer.parseInt(span[1].substring(0,target));
				cal.set(Calendar.MONTH, month);
			}
			if(!Symbol.XINGHAO.equals(span[2])){
				int target = 1;
				if(Character.isDigit(span[2].charAt(1))){
					target = 2;
				}
				int weekOfYear = Integer.parseInt(span[2].substring(0,target));
				cal.set(Calendar.WEEK_OF_YEAR, weekOfYear);
			}
			if(!Symbol.XINGHAO.equals(span[3])){
				int weekday = Integer.parseInt(span[3].substring(0,1));
				cal.set(Calendar.DAY_OF_WEEK, weekday+1);
			}
			if(!Symbol.XINGHAO.equals(span[4])){
				int time = Integer.parseInt(span[4].substring(0,4));
				cal.set(Calendar.HOUR_OF_DAY, time/100);
				cal.set(Calendar.MINUTE, time%100);
			}
			cal.set(Calendar.SECOND,0);
			cal.set(Calendar.MILLISECOND, 0);
			return cal.getTimeInMillis();
		}
		return 0;
	}

	/**
	 * 获取结束时间
	 * 
	 * @param customFormatTimeStr
	 * @return
	 * @throws ParseException
	 */
	public static long getEndTime(String customFormatTimeStr) {
		try {
			int type = Integer.parseInt(customFormatTimeStr.substring(0, 1));
			if (type == 1) {
				String timeStr = customFormatTimeStr.substring(2, customFormatTimeStr.length());
				timeStr = timeStr.split(Symbol.DOUHAO_REG)[1];
				Date date = DateFormat.getDefaultFormat().parse(timeStr);
				return date.getTime();
			} else if (type == 2) {
				// TODO:will@获取结束时间
			}
		} catch (Exception ex) {
			throw new RuntimeException("时间解析出错！customFormatTimeStr="+customFormatTimeStr,ex);
		}
		return 0;
	}
	
	
	
	/**
	 * 是否在设定的时间范围内
	 * @param customFormatTimeStr
	 * @return
	 * @throws ParseException 
	 */
	public static boolean isBetweenTime(long nowTime, String customFormatTimeStr) throws ParseException{
		int type = Integer.parseInt(customFormatTimeStr.substring(0, 1));
		String timeStr = customFormatTimeStr.substring(2, customFormatTimeStr.length());
		if(type==1){
			String[] expressArray = timeStr.split(Symbol.DOUHAO_REG);
			long beginTime = DateFormat.getDefaultFormat().parse(expressArray[0]).getTime();
			long endTime = DateFormat.getDefaultFormat().parse(expressArray[1]).getTime();
			if (nowTime >= beginTime && nowTime <= endTime) {
				return true;
			}
		}else{
			TimeExpression exp = null;
//			Object expObj = GameCacheManager.getDefaultCache().get(customFormatTimeStr);
//			if(expObj!=null){
//				exp = (TimeExpression)expObj;
//			}else{
				exp = TimeExpression.build(customFormatTimeStr);
//				GameCacheManager.getDefaultCache().put(customFormatTimeStr, exp);
//			}
			TimeExpressionParse expParser = new TimeExpressionParse(nowTime);
			return expParser.isBetween(exp,0);
		}
		return false;
	}
	
	/**
	 * 获取周一0点毫秒数
	 * 
	 * @param time
	 * @return
	 */
	public static long getMonday0clock(long time) {
		Calendar cal = Calendar.getInstance();
		cal.setTimeInMillis(time);
		cal.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY); 
		cal.set(Calendar.HOUR_OF_DAY, 0);
		cal.set(Calendar.MINUTE, 0);
		cal.set(Calendar.SECOND, 0);
		cal.set(Calendar.MILLISECOND, 0);
		return cal.getTimeInMillis();
	}
	public static long getWed0clock(long time) {
		Calendar cal = Calendar.getInstance();
		cal.setTimeInMillis(time);
		cal.set(Calendar.DAY_OF_WEEK, Calendar.WEDNESDAY); 
		cal.set(Calendar.HOUR_OF_DAY, 0);
		cal.set(Calendar.MINUTE, 0);
		cal.set(Calendar.SECOND, 0);
		cal.set(Calendar.MILLISECOND, 0);
		return cal.getTimeInMillis();
	}
	public static long getSat0clock(long time) {
		Calendar cal = Calendar.getInstance();
		cal.setTimeInMillis(time);
		cal.set(Calendar.DAY_OF_WEEK, Calendar.SATURDAY); 
		cal.set(Calendar.HOUR_OF_DAY, 0);
		cal.set(Calendar.MINUTE, 0);
		cal.set(Calendar.SECOND, 0);
		cal.set(Calendar.MILLISECOND, 0);
		return cal.getTimeInMillis();
	}
	/**
	 * @param time
	 * @return 传入时间当日0点整毫秒数
	 */
	public static long get0clockTime(long time) {
		Calendar cal = Calendar.getInstance();
		cal.setTimeInMillis(time);
		cal.set(Calendar.HOUR_OF_DAY, 0);
		cal.set(Calendar.MINUTE, 0);
		cal.set(Calendar.SECOND, 0);
		cal.set(Calendar.MILLISECOND, 0);
		return cal.getTimeInMillis();
	}
	
	/**
	 * @param time
	 * @return 传入时间当日24点整毫秒数
	 */
	public static long get24clockTime(long time) {
		return get0clockTime(time)+TimeUtil.DAY_TIME;
	}
	
	public static int getDays(long time) {
		return (int) (time/DAY_TIME);
	}
	
	/**
	 * 获取当前时间戳
	 * 
	 * @return
	 */
	public static int getSecond() {
		return (int)(Calendar.getInstance().getTimeInMillis() * 0.001);
	}

	/**
	 * 获取星期[[周日为1->周六为7]]
	 * 
	 * @return
	 */
	public static int getWeek() {
		Calendar c = Calendar.getInstance();
		return c.get(Calendar.DAY_OF_WEEK);
	}
	
//	public static int getMonthDay() {
//		int[] now = getTimes(new Date());
//		return now[4];
//	}
//	
	public static int[] getTimes(Date date) {
		int[] now = new int[7];
		GregorianCalendar gc = new GregorianCalendar();
		gc.setTime(date);
		now[6] = gc.get(Calendar.YEAR); // 年
		now[5] = gc.get(Calendar.DAY_OF_WEEK); // 周[周日为1->周六为7]
		now[4] = gc.get(Calendar.MONTH) + 1; // 月
		now[3] = gc.get(Calendar.DAY_OF_MONTH); // 日
		now[2] = gc.get(Calendar.HOUR_OF_DAY); // 时
		now[1] = gc.get(Calendar.MINUTE); // 分
		now[0] = gc.get(Calendar.SECOND); // 秒
		return now;
	}
	
	/**
	 * 获取时间戳[精确到毫秒]
	 * 
	 * @param calendar
	 * @return
	 */
	public static long getMilli(Calendar calendar) {
		return calendar.getTimeInMillis();
	}

	/**
	 * 获取时间戳[精确到秒]
	 * 
	 * @param calendar
	 * @return
	 */
	public static long getSecondmilli(Calendar calendar) {
		calendar.set(Calendar.MILLISECOND, 0);
		return calendar.getTimeInMillis();
	}

	/**
	 * 获取当前秒时间戳
	 * 
	 * @return
	 */
	public static long getSecondmilli() {
		return getSecondmilli(Calendar.getInstance());
	}

	/**
	 * 获取时间戳[精确到分秒]
	 * 
	 * @param calendar
	 * @return
	 */
	public static long getMinutemilli(Calendar calendar) {
		calendar.set(Calendar.MILLISECOND, 0);
		calendar.set(Calendar.SECOND, 0);
		return calendar.getTimeInMillis();
	}

	/**
	 * 获取当前分时间戳
	 * 
	 * @return
	 */
	public static long getMinutemilli() {
		return getMinutemilli(Calendar.getInstance());
	}

	/**
	 * 获取时间戳[精确到小时]
	 * 
	 * @param calendar
	 * @return
	 */
	public static long getHourmilli(Calendar calendar) {
		calendar.set(Calendar.MILLISECOND, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MINUTE, 0);
		return calendar.getTimeInMillis();
	}

	/**
	 * 获取当前小时时间戳
	 * 
	 * @return
	 */
	public static long getHourmilli() {
		return getHourmilli(Calendar.getInstance());
	}

	/**
	 * 获取时间戳[精确到天]
	 * 
	 * @param calendar
	 * @return
	 */
	public static long getDaymilli(Calendar calendar) {
		calendar.set(Calendar.MILLISECOND, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		return calendar.getTimeInMillis();
	}
	/**
	 * 获取当天5点时间戳[精确到天]
	 * 
	 * @param calendar
	 * @return
	 */
	public static long getOf5Clockmilli(Calendar calendar) {
		calendar.set(Calendar.MILLISECOND, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.HOUR_OF_DAY, 5);
		return calendar.getTimeInMillis();
	}

	/**
	 * 获取当天零点时间戳
	 * 
	 * @return
	 */
	public static long getDaymilli() {
		return getDaymilli(Calendar.getInstance());
	}
	/**
	 * 获取当天凌晨5点时间戳
	 * 
	 * @return
	 */
	public static long getDaysOf5Clockmilli() {
		return getOf5Clockmilli(Calendar.getInstance());
	}

	/**
	 * 获取时间戳[精确到月]
	 * 
	 * @param calendar
	 * @return
	 */
	public static long getMonthmilli(Calendar calendar) {
		calendar.set(Calendar.MILLISECOND, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.DATE, Calendar.YEAR);
		return calendar.getTimeInMillis();
	}

	/**
	 * 获取当月一号零点时间戳
	 * 
	 * @return
	 */
	public static long getMonthmilli() {
		return getMonthmilli(Calendar.getInstance());
	}

	/**
	 * 获取时间戳[精确到年]
	 * 
	 * @param calendar
	 * @return
	 */
	public static long getYearmilli(Calendar calendar) {
		calendar.set(Calendar.MILLISECOND, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.DATE, Calendar.YEAR);
		calendar.set(Calendar.MONTH, 0);
		return calendar.getTimeInMillis();
	}

	/**
	 * 获取当年第一天零点时间戳
	 * 
	 * @return
	 */
	public static long getYearmilli() {
		return getYearmilli(Calendar.getInstance());
	}

	/**
	 * 获取时间戳[周一零点][周日开始]
	 * 
	 * @param calendar
	 * @return
	 */
	public static long getWeekmilli(Calendar calendar) {
		calendar.set(Calendar.MILLISECOND, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.DAY_OF_WEEK, 2);
		return calendar.getTimeInMillis();
	}
	
	/**
	 * 获取下个月1号0点时间戳
	 * 
	 * @param nowTime
	 * @return
	 */
	public static long getNextMonth1st(long nowTime) {
		Calendar calendar = Calendar.getInstance();
		calendar.set(Calendar.MILLISECOND, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.DAY_OF_MONTH, 1);
		calendar.add(Calendar.MONTH, 1);
		return calendar.getTimeInMillis();
	}
	/**
	 * @param time
	 * @param monthes
	 * @return @param time 往后（前）几个天0点
	 */
	public static long getFurtherDaysStartTime(long time, int day) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTimeInMillis(time);
		calendar.set(Calendar.MILLISECOND, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.add(Calendar.DAY_OF_MONTH, day);
		return calendar.getTimeInMillis();
	}

	/**
	 * @param time
	 * @param monthes
	 * @return @param time 往后（前）几个月月初时间
	 */
	public static long getFurtherMonthsStartTime(long time, int monthes) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTimeInMillis(time);
		calendar.set(Calendar.MILLISECOND, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.DAY_OF_MONTH, 1);
		calendar.add(Calendar.MONTH, monthes);
		return calendar.getTimeInMillis();
	}
	
	/**
	 * @param time
	 * @param years
	 * @return @param time 往后（前）几年年初时间
	 */
	public static long getFurtherYearsStartTime(long time, int years) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTimeInMillis(time);
		calendar.set(Calendar.MILLISECOND, 0);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MINUTE, 0);
		calendar.set(Calendar.HOUR_OF_DAY, 0);
		calendar.set(Calendar.DAY_OF_MONTH, 1);
		calendar.set(Calendar.MONTH, 0);
		calendar.add(Calendar.YEAR, years);
		return calendar.getTimeInMillis();
	}
	
	/**
     * 将HH:mm:ss格式转化为秒
     * @param hms
     * @return
     */
    public static int hms2second(String hms) {
    	String[] arr = hms.split(":");
    	int len = arr.length;
    	int second = 0;
    	if (len > 0) second += Integer.parseInt(arr[0])*3600;
    	if (len > 1) second += Integer.parseInt(arr[1])*60;
    	if (len > 2) second += Integer.parseInt(arr[2]);
    	return second;
    }
    
    public static String cronMinsBefore(String timeStr, int notifyMinsBefore) {
		String[] startStrs = timeStr.split(" ");
		int refreshMins = Integer.valueOf(startStrs[1]);
		int refreshHours  = -1;
		int refreshHours2 = -1;
		String[] tmps = startStrs[2].split("/");
		String[] hours = tmps[0].split("-");
		refreshHours = Integer.valueOf(hours[0]);
		if (hours.length > 1) {
			refreshHours2 = Integer.valueOf(hours[1]);
		}
		
		int notifyMins,notifyHours;
		int notifyHours2 = -1;
		notifyMins = refreshMins-notifyMinsBefore;
		if (notifyMins < 0) {
			notifyMins = notifyMins + 60;
			notifyHours = refreshHours - 1;
			if (refreshHours2 >= 0) {
				notifyHours2 = refreshHours2 - 1;
			}
		} else {
			notifyHours = refreshHours;
			notifyHours2 = refreshHours2;
		}
		startStrs[1] = notifyMins+"";
		String tmp = (notifyHours<0? notifyHours+24:notifyHours)+"";
		if (refreshHours2 >= 0) {
			tmp += "-"+(notifyHours2<0? notifyHours2+24:notifyHours2)+"";
		}
		if (tmps.length > 1) {
			tmp += "/"+tmps[1];
		}
		startStrs[2] = tmp;
		
		return String.join(" ", startStrs);
	}    

    
    /**
     * @return 明年开始时间戳
     */
    public static long getNextYearStart() {
    	Calendar calendar = Calendar.getInstance();
    	calendar.set(Calendar.MILLISECOND, 0);
    	calendar.set(Calendar.SECOND, 0);
    	calendar.set(Calendar.MINUTE, 0);
    	calendar.set(Calendar.HOUR_OF_DAY, 0);
    	calendar.set(Calendar.DAY_OF_MONTH, 1);
    	calendar.set(Calendar.MONTH, 0);
    	calendar.add(Calendar.YEAR, 1);
    	return calendar.getTimeInMillis();
    }
    
    /**
     * 获取年月日
     * 
     * @param time
     * @return
     */
    public static int[] getYearMonthDay(long time) {
    	Calendar calendar = Calendar.getInstance();
    	calendar.setTimeInMillis(time);
    	return new int[]{calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), calendar.get(Calendar.DAY_OF_MONTH)};
    }

	/**
	 * 将毫秒转换成时分秒的格式数据（00小时00分00秒）
	 *
	 * @param time long类型的数据
	 * @return HH:mm:ss
	 */
	public static String msecToTime(long time) {
		String timeStr = null;
		long hour = 0;
		long minute = 0;
		long second = 0;
		if (time <= 0)
			return "00小时00分00秒";
		else {
			second = time / 1000;
			minute = second / 60;
			if (second < 60) {
				timeStr = "00小时00分" + unitFormat(second) + "秒";
			} else if (minute < 60) {
				second = second % 60;
				timeStr = "00小时" + unitFormat(minute) + "分" + unitFormat(second) + "秒";
			} else {// 数字>=3600 000的时候
				hour = minute / 60;
				minute = minute % 60;
				second = second - hour * 3600 - minute * 60;
				timeStr = unitFormat(hour) + "小时" + unitFormat(minute) + "分" + unitFormat(second) + "秒";
			}
		}
		return timeStr;
	}

	public static String unitFormat(long i) {// 时分秒的格式转换
		String retStr = null;
		if (i >= 0 && i < 10)
			retStr = "0" + Long.toString(i);
		else
			retStr = "" + i;
		return retStr;
	}

	/**
	 * 当前日期零点时间
	 *
	 * @param time
	 * @return
	 */
	public static long currZeroTime(long time) {
		return (time + TimeZone.getDefault().getRawOffset()) / (1000 * 3600 * 24) * (1000 * 3600 * 24) - TimeZone.getDefault().getRawOffset();//当天零点零分零秒的毫秒数
	}
}
