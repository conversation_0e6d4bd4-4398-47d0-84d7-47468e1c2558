package org.gof.demo.worldsrv.botHelper;

import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.interfaces.ISerilizable;

import java.io.IOException;

public class DebugBuffVo implements ISerilizable {
    public long buffId;
    public long timeLeft;


    @Override
    public void writeTo(OutputStream out) throws IOException {
        out.write(buffId);
        out.write(timeLeft);
    }

    @Override
    public void readFrom(InputStream in) throws IOException {
        buffId = in.read();
        timeLeft = in.read();
    }
}
