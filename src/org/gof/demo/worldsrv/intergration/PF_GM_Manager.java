package org.gof.demo.worldsrv.intergration;

import com.alibaba.fastjson.JSONObject;
import io.vertx.core.json.JsonArray;
import org.apache.commons.lang3.StringUtils;
import org.gof.core.Node;
import org.gof.core.Port;
import org.gof.core.RecordTransient;
import org.gof.core.Service;
import org.gof.core.db.DBKey;
import org.gof.core.dbsrv.DB;
import org.gof.core.dbsrv.redis.EntityManager;
import org.gof.core.dbsrv.redis.RedisTools;
import org.gof.core.dbsrv.redis.Tool;
import org.gof.core.support.S;
import org.gof.core.support.*;
import org.gof.core.support.log.LogCore;
import org.gof.core.support.observer.Listener;
import org.gof.demo.distr.admin.AdminCenterManager;
import org.gof.demo.distr.admin.AdminCenterServiceProxy;
import org.gof.demo.distr.cross.CrossManager;
import org.gof.demo.distr.cross.domain.CrossPoint;
import org.gof.demo.distr.cross.domain.CrossType;
import org.gof.demo.seam.account.AccountLoginService;
import org.gof.demo.seam.account.AccountPort;
import org.gof.demo.seam.account.AccountService;
import org.gof.demo.worldsrv.allot.AllotServerServiceProxy;
import org.gof.demo.worldsrv.arena.ArenaCrossServiceProxy;
import org.gof.demo.worldsrv.arena.ArenaManager;
import org.gof.demo.worldsrv.arena.ArenaRankedServiceProxy;
import org.gof.demo.worldsrv.character.HumanObjectServiceProxy;
import org.gof.demo.worldsrv.check.CheckAdminService;
import org.gof.demo.worldsrv.config.ConfGoods;
import org.gof.demo.worldsrv.config.ConfRanktype;
import org.gof.demo.worldsrv.entity.Currency;
import org.gof.demo.worldsrv.entity.*;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.gm.GMGameServiceProxy;
import org.gof.demo.worldsrv.gm.GameDebugManager;
import org.gof.demo.worldsrv.guild.league.GuildLeagueServiceProxy;
import org.gof.demo.worldsrv.guild.league.GuildLeagueWarmUpServiceProxy;
import org.gof.demo.worldsrv.human.HumanGlobalInfo;
import org.gof.demo.worldsrv.human.HumanGlobalServiceProxy;
import org.gof.demo.worldsrv.human.HumanPort;
import org.gof.demo.worldsrv.human.HumanService;
import org.gof.demo.worldsrv.inform.ChatServiceProxy;
import org.gof.demo.worldsrv.inform.ErrorTip;
import org.gof.demo.worldsrv.inform.Inform;
import org.gof.demo.worldsrv.kungFuRace.KungFuRaceServiceProxy;
import org.gof.demo.worldsrv.kungFuRace.KungFuRaceUtils;
import org.gof.demo.worldsrv.mail.FillMailServiceProxy;
import org.gof.demo.worldsrv.mail.MailManager;
import org.gof.demo.worldsrv.pocketLine.PocketLineEventSubKey;
import org.gof.demo.worldsrv.rank.RankManager;
import org.gof.demo.worldsrv.redis.RedisKeys;
import org.gof.demo.worldsrv.support.*;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;
import org.gof.demo.worldsrv.support.observer.EventKey;
import org.gof.platform.ConstPf;
import org.gof.platform.LogPF;
import org.gof.platform.http.HttpAsyncSendServiceProxy;
import org.gof.platform.http.HttpServerHandler;

import java.util.*;

public class PF_GM_Manager extends ManagerBase {

	private static final String SPLIT = ",";
	/**
	 * 一些限制
	 */
	public static final int All_Maill_Gold = 30000;  //群发邮件元宝上限
    public static final int All_Maill_BindGold = 30000;//群发邮件绑定元宝上限
    public static final int Maill_Gold = 30000;  //个人邮件元宝上限
    public static final int Maill_BindGold = 30000;//个人邮件绑定元宝上限
    public static final int CHARGE_Gs = 3240;//gs人民币上限
	public static PF_GM_Manager inst() {
		return inst(PF_GM_Manager.class);
	}

	/**
	 * 发送补偿邮件
	 * http://************:8181/GM?cmd=sendFillMail&num=2&startTime=*************&detail=11&sn=1&endTime=*************&accounts=&title=群发1
	 * 自己：http://************:8018/GM?cmd=sendFillMail&num=2&startTime=*************&detail=11&sn=1&endTime=*************&accounts=&title=%E7%BE%A4%E5%8F%911
	 * @param param
	 */
	@Listener(value = EventKey.GM, subStr = { "sendFillMail" })
	public void sendFillMail(Param param) {
		JSONObject jo = param.get();
		String itemSn = jo.getString("sn");
		String itemNum = jo.getString("num");
		if(itemSn.indexOf("，")>=0 || itemNum.indexOf("，")>=0){
			//分隔符不能是中文逗号
			Port.getCurrent().returnsAsync(Port.getCurrent().createReturnAsync(), "success",false, "reason", "分隔符不能是中文逗号");
			return;
		}
		if(!StringUtils.isEmpty(itemSn) || !StringUtils.isEmpty(itemNum)){
			String [] ids = itemSn.split(",");
			String [] ns = itemNum.split(",");

			if(ids.length != ns.length){
				//类型和数量不匹配
				Port.getCurrent().returnsAsync(Port.getCurrent().createReturnAsync(), "success",false, "reason", "资源类型和数量个数不匹配");
				return;
			}

			if(ids.length > 0 || ns.length > 0){
				if(!Utils.isDigits(ids) || !Utils.isDigits(ns)){
					//附件格式不正确
					Port.getCurrent().returnsAsync(Port.getCurrent().createReturnAsync(), "success",false, "reason", "附件格式不正确");
					return;
				}
			}
		}
		if(isMoneyLimit(itemSn, itemNum)){
			sendMailToAll(jo.toJSONString());
		}else{
			//元宝或绑定元宝超出上限
			Port.getCurrent().returnsAsync(Port.getCurrent().createReturnAsync(), "success",false, "reason", "元宝或绑定元宝超出上限");
		}

	}

	/**
	 * 删除补偿邮件
	 * @param param
	 */
	@Listener(value = EventKey.GM, subStr = { "unSendFillMail" })
	public void deleteFillMail(Param param) {
		JSONObject jo = param.get();
		String eventKey = jo.getString("mailKey");
		long pid = Port.getCurrent().createReturnAsync();
		FillMailServiceProxy prx = FillMailServiceProxy.newInstance();
		prx.deleteMail(eventKey);
		prx.listenResult(this::_result_deleteFillMail, "pid", pid);
	}
	public void _result_deleteFillMail(Param results, Param context){
		boolean result = results.get("success");
		String reason = results.get("reason");
		long pid = context.get("pid");
		Port.getCurrent().returnsAsync(pid, "success", result, "reason", reason);
	}

	/**
	 * 设置最大在线玩家(针对登录排队)
	 * @param param
	 */
	@Listener(value = EventKey.GM, subStr = { "setMaxOnline" })
	public void setMaxOnline(Param param) {
		JSONObject jo = param.get();
		setMaxOnline(jo);
	}


	/** -------------sendMail start-------------- */
	/**
	 * 给玩家发送邮件 注意:一个发送完成后，发送下一个 names:玩家名称或玩家Id
	 *
	 * @param param
	 */
	@Listener(value = EventKey.GM, subStr = { "sendMail" })
	public void sendMail(Param param) {
		JSONObject jo = param.get();
		// 要发送给的人名称集合
		String names = jo.getString("names");
		String itemSnStr = jo.getString("sn");
		String itemNumStr = jo.getString("num");
		int[] itemSN = null;
		int[] itemNum = null;
		if(itemSnStr != null && itemNumStr != null){
			if(itemSnStr.contains("，") || itemNumStr.contains("，")){
				//分隔符不能是中文逗号
				Port.getCurrent().returns("success",false, "reason", "分隔符不能是中文逗号");
				return;
			}
			itemSN = Utils.strToIntArray(itemSnStr);
			itemNum = Utils.strToIntArray(itemNumStr);

			if(!StringUtils.isEmpty(itemSnStr) || !StringUtils.isEmpty(itemNumStr)){
				if(itemSN == null ||  itemNum == null || itemSN.length != itemNum.length){
					//类型和数量不匹配
					Port.getCurrent().returnsAsync(Port.getCurrent().createReturnAsync(), "success",false, "reason", "资源类型和数量个数不匹配");
					return;
				}
				for (int i = 0; i < itemSN.length; i++) {
					if(Utils.intValue(itemSN[i]) == TokenItemType.Money999){
						if(itemNum[i] > 10000000){
							Port.getCurrent().returns("success", false, "reason", "闪钻超出上限");
							return ;
						}
					}else if(Utils.intValue(itemSN[i]) == TokenItemType.GOLD){
						if(itemNum[i] > 10000000){
							Port.getCurrent().returns("success", false, "reason", "元宝超出上限");
							return ;
						}
					}
				}
			}
		}

		try {
			List<Long> humanIdList = Utils.strToLongList(names);
			if(humanIdList.isEmpty()){
				Port.getCurrent().returns("success", false, "reason", "格式不正确, 没有数量");
				return;
			}

			String title = jo.getString("title");
			String detail = jo.getString("detail");
			String mailSn = jo.getString("mailSn");
			Param params = new Param();
			params.put(Mail.K.backstage, true);
			String reason = Utils.createStr("操作者eventKey:{}", jo.getString("eventKey"));
			for (Long humanId : humanIdList) {
				Human human = (Human) EntityManager.getEntity(Human.class, humanId);
				if (human == null) {
					reason += "玩家 " + humanId + " 不存在<br/>";
					continue;
				}
				// 不是这个服的玩家不处理
				int serverId = Util.getServerIdByHumanId(human.getId());
				if(human.getServerId() != Config.SERVER_ID && serverId != Config.SERVER_ID || serverId == 0){
					Log.temp.info("===不是这个服的玩家不处理， humanId={}, serverId={}, game_serverId={}, realServerId={}",
							human.getId(), human.getServerId(), Config.SERVER_ID, serverId);
					continue;
				}
				MailManager.inst().sendMail(humanId, MailManager.SYS_SENDER,
						mailSn != null && !mailSn.isEmpty() ? Integer.parseInt(mailSn) : 0, title, detail, itemSN, itemNum, params);
			}
			Port.getCurrent().returns("success", true, "reason", reason + "发送成功！");
		} catch (Exception e) {
			Log.temp.error(e.toString());
			Port.getCurrent().returns("success", false, "reason", "格式不正确");

		}


//		sendMail(names,jo);
	}

	/**
	 * 给所有玩家发送邮件
	 * @param msg
	 */
	public void sendMailToAll(String msg){
		FillMailServiceProxy.newInstance().sendMail(msg);

		Port.getCurrent().returnsAsync(Port.getCurrent().createReturnAsync(), "success",true, "reason", "发送成功！");
	}
	/**
	 * 判断群发邮件货币上限
	 * @param itemIdStr
	 * @param numStr
	 * @return
	 */
	public boolean isMoneyLimit(String itemIdStr, String numStr){
		int[] itemIds = new int[]{};
		int[] nums = new int[]{};
		if(StringUtils.isNotEmpty(itemIdStr)){
			String [] ids = itemIdStr.split(",");
			String [] ns = numStr.split(",");
			if(ids.length != ns.length){
				//类型和数量不匹配
				return false;
			}
			int len = ids.length;

			itemIds = new int[len];
			nums = new int[len];
			for(int i=0; i<len; i++){
				itemIds[i] = Integer.parseInt(ids[i]);
				nums[i] = Integer.parseInt(ns[i]);
				if(itemIds[i] == TokenItemType.Money999){
					if(nums[i] > All_Maill_BindGold){
						return false;
					}
				}else if(itemIds[i] == TokenItemType.GOLD){
					if(nums[i] > All_Maill_Gold){
						return false;
					}
				}
			}
			return true;
		}
		return true;
	}

	/**
	 * 设置最大在线人数(针对登录排队)
	 * @param jo
	 */
	public void setMaxOnline(JSONObject jo){

		int maxOnlineNum = jo.getIntValue("maxOnlineNum");
		AccountService serv = Port.getCurrent().getServices(Distr.SERV_GATE);
		if(serv != null){
			serv.setLoginMaxOnline(maxOnlineNum);
			Port.getCurrent().returnsAsync(Port.getCurrent().createReturnAsync(), "success",true, "reason", "发送成功！");
		}else{
			Port.getCurrent().returnsAsync(Port.getCurrent().createReturnAsync(), "success",false, "reason", "发送失败！");
		}

	}


	/**
	 * 给指定人员发送邮件
	 * @param names
	 * @param jo
	 */
	public void sendMail(String names, JSONObject jo){
		String[] nameArray = names.split(SPLIT);
		List<String> nameList = new LinkedList<>();
		try {
			for (String name : nameArray) {
				if (name.isEmpty()) {
					continue;
				}
				if (name.length() >= 18) {
					// 如果length>=18,检查id的合法性
					Long.parseLong(name);
				}
				nameList.add(name);
			}
		} catch (Exception e) {
			Port.getCurrent().returns("success", false, "reason", "格式不正确");
			return;
		}

		jo.remove("names");

		// 添加的物品list
		int itemSn[] = null;
		int itemNum[] = null;
		String sn = jo.getString("sn");
		String num = jo.getString("num");
		if (sn != null && !"".equals(sn) && !"null".equals(sn)) {
			String[] sns = sn.split(",");
			String[] nums = num.split(",");

			itemSn = new int[sns.length];
			itemNum = new int[num.length()];

			for (int i = 0; i < sns.length; i++) {
				itemSn[i] = Integer.parseInt(sns[i]);
				itemNum[i] = Integer.parseInt(nums[i]);
				if(itemSn[i] == TokenItemType.Money999){
					if(itemNum[i] > 10000000){
						Port.getCurrent().returns("success", false, "reason", "绑定元宝超出上限");
						return ;
					}
				}else if(itemSn[i] == TokenItemType.GOLD){
					if(itemNum[i] > 10000000){
						Port.getCurrent().returns("success", false, "reason", "元宝超出上限");
						return ;
					}
				}
			}
		}

		jo.put("pid", Port.getCurrent().createReturnAsync());
		// 查询的列（id）
		List<String> coList = new ArrayList<>();
		coList.add(Human.K.id);

		send(jo, nameList, coList, itemSn, itemNum, "");
	}

	public void send(JSONObject jo, List<String> nameList, List<String> coList, int[] itemSn, int[] itemNum, String reason) {
		if (nameList.size() == 0) {
			boolean flag = false;
			if ("".equals(reason)) {
				reason = "操作成功";
				flag = true;
			}
			// 循环出口
			Port.getCurrent().returnsAsync(jo.getLongValue("pid"), "success", flag, "reason", reason);
			return;
		}
		String name = nameList.remove(0);
		// 如果是ID
		String whereSql = Utils.createStr(" where name = '{}'", name);
		if (name.length() >= 18) {
			whereSql = Utils.createStr(" where id = '{}'", name);
		}
		DB db = DB.newInstance(Human.tableName);
		db.findByQuery(false, whereSql, DBKey.COLUMN, coList);
		db.listenResult(this::_result_sendMail, "jo", jo, "nameList",
				nameList, "coList", coList, "itemSn", itemSn, "itemNum",
				itemNum, "reason", reason, "name", name);
	}

	private void _result_sendMail(Param results, Param context) {
		List<RecordTransient> list = results.get();
		JSONObject jo = context.get("jo");
		List<String> nameList = context.get("nameList");
		List<String> coList = context.get("coList");

		int len = 0;

		// 物品
		int[] itemSn = context.get("itemSn");
		int[] itemNum = context.get("itemNum");

		if (itemSn != null) {
			len += itemSn.length;
		}

		int[] targetItem = new int[len];
		int[] targetNum = new int[len];

		int i = 0;
		if (itemSn != null) {
			int index = 0;
			for (; i < len; i++) {
				targetItem[i] = itemSn[index];
				targetNum[i] = itemNum[index];
				index++;
			}
		}

		String reason = context.getString("reason");
		long receiverId;
		if (list.size() > 0) {
			receiverId = list.get(0).get("id");
		} else {
			reason += "玩家 " + context.getString("name") + " 不存在<br/>";
			send(jo, nameList, coList, itemSn, itemNum, reason);
			return;
		}
		// 发送一个玩家的邮件
		if (targetItem.length > 0 && targetNum.length > 0) {
			MailManager.inst().sendMail(receiverId, MailManager.SYS_SENDER, MailManager.MAIL_SN_ATTACHMENT, jo.getString("title"), jo.getString("detail"), targetItem, targetNum);
		} else {
			MailManager.inst().sendMail(receiverId, MailManager.SYS_SENDER, MailManager.MAIL_SN_COMMON, jo.getString("title"), jo.getString("detail"), targetItem, targetNum);
		}


		// 发送其他玩家的邮件
		send(jo, nameList, coList, itemSn, itemNum, reason);
	}

	/** -------------sendMail end-------------- */

	/** -------------sendGiftCode start-------------- */
	/**
	 * 给玩家发送邮件 注意:一个发送完成后，发送下一个 names:玩家名称或玩家Id
	 *
	 * @param param
	 */
	@Listener(value = EventKey.GM, subStr = { "sendGiftCode" })
	public void sendGiftCode(Param param) {
		JSONObject jo = param.get();
		// 要发送给的人名称集合
		String names = jo.getString("names");
		String[] nameArray = names.split(SPLIT);
		List<String> nameList = new LinkedList<>();
		try {
			for (String name : nameArray) {
				if (name.isEmpty()) {
					continue;
				}
				if (name.length() >= 18) {
					// 如果length>=18,检查id的合法性
					Long.parseLong(name);
				}
				nameList.add(name);
			}
		} catch (Exception e) {
			Port.getCurrent().returns("success", false, "reason", "格式不正确");
			return;
		}

		jo.remove("names");

		// 添加的物品list
		String code = jo.getString("code");

		jo.put("pid", Port.getCurrent().createReturnAsync());
		// 查询的列（id）
		List<String> coList = new ArrayList<>();
		coList.add(Human.K.id);

		sendGiftCode(jo, nameList, coList, code, "");
	}

	public void sendGiftCode(JSONObject jo, List<String> nameList,
			List<String> coList, String code, String reason) {
		if (nameList.size() == 0) {
			boolean flag = false;
			if ("".equals(reason)) {
				reason = "操作成功";
				flag = true;
			}
			// 循环出口
			Port.getCurrent().returnsAsync(jo.getLongValue("pid"), "success",
					flag, "reason", reason);
			return;
		}
		String name = nameList.remove(0);
		// 如果是ID
		String whereSql = Utils.createStr(" where name = '{}'", name);
		if (name.length() >= 18) {
			whereSql = Utils.createStr(" where id = '{}'", name);
		}
		DB db = DB.newInstance(Human.tableName);
		db.findByQuery(false, whereSql, DBKey.COLUMN, coList);
		db.listenResult(this::_result_sendGiftCode, "jo", jo, "nameList",
				nameList, "coList", coList, "code", code, "reason", reason,
				"name", name);
	}

	private void _result_sendGiftCode(Param results, Param context) {
		List<RecordTransient> list = results.get();
		JSONObject jo = context.get("jo");
		List<String> nameList = context.get("nameList");
		List<String> coList = context.get("coList");

		String code = context.get("code");

		String reason = context.getString("reason");
		long receiverId;
		if (list.size() > 0) {
			receiverId = list.get(0).get("id");
		} else {
			reason += "玩家 " + context.getString("name") + " 不存在<br/>";
			sendGiftCode(jo, nameList, coList, code, reason);
			return;
		}

		// 发送其他玩家的邮件
		sendGiftCode(jo, nameList, coList, code, reason);
	}

	/** -------------sendGiftCode end-------------- */

	/**
	 * 走马灯
	 *
	 * @param param
	 */
	@Listener(value = EventKey.GM, subStr = { "sendNotice" })
	public void sendNotice(Param param) {
		JSONObject jo = param.get();
		String notice = jo.getString("content");
		int split = jo.getIntValue("split");
		int count = jo.getIntValue("count");
		long pid = Port.getCurrent().createReturnAsync();
		long time = jo.getLongValue("timestamp");
		if ("".equals(notice) || "null".equals(notice)) {
			Port.getCurrent().returnsAsync(pid, "success", false, "reason", Inform.getServerData(106));
			return;
		}

//		String fix = NameFix.shield(notice);
//		if (!StringUtils.isEmpty(fix)) {
//			Port.getCurrent().returnsAsync(pid, "success", false, "reason",
//					"通告中不能包含屏蔽字。");
//			return;
//		}
		ChatServiceProxy prx = ChatServiceProxy.newInstance();
		prx.GMNotice(time, split, count, notice);
//		Inform.all(Inform.通告滚动, notice, null, null, null);
		Port.getCurrent().returnsAsync(pid, "success", true, "reason","发送通告成功");
	}

	/**
	 * 跑马灯撤回
	 * @param param
	 */
	@Listener(value = EventKey.GM, subStr = { "recallNotice" })
	public void recallNotice(Param param) {
		long pid = Port.getCurrent().createReturnAsync();
		JSONObject jo = param.get();
		String content = jo.getString("content");
		ChatServiceProxy prx = ChatServiceProxy.newInstance();
		prx.recallNotice(content);
		Port.getCurrent().returnsAsync(pid, "success", true, "reason","撤回成功");
	}


	/**
	 * TODO 黑市广播
	 *@date 2021/8/12 15:12
	 *@param param
	 *@return void
	*/
	@Listener(value = EventKey.GM, subStr = { "blackMarketNotice" })
	public void blackMarketNotice(Param param) {
		JSONObject jo = param.get();
		// 要发送给的人名称集合
		String notice = jo.getString("notice");

		Port.getCurrent().returns("success", true, "reason", "");
	}


	/**
	 * 解除禁言，封号
	 *
	 * @param param
	 */
	@Listener(value = EventKey.GM, subStr = { "unSeal" })
	public void unSeal(Param param) {
		JSONObject jo = param.get();
		// 要发送给的人名称集合
		String names = jo.getString("names");

		String[] nameArray = names.split(SPLIT);
		List<String> nameList = new LinkedList<>();
		try {
			for (String name : nameArray) {
				if (name.isEmpty()) {
					continue;
				}
				if (name.length() >= 18) {
					// 如果length>=18,检查id的合法性
					Long.parseLong(name);
				}
				nameList.add(name);
			}
		} catch (Exception e) {
			Port.getCurrent().returns("success", false, "reason", "格式不正确");
			return;
		}

		jo.remove("names");
		jo.put("endDateStr", 0);
		jo.put("pid", Port.getCurrent().createReturnAsync());

		List<String> coList = new ArrayList<>();
		coList.add(Human.K.id);

		doSeal(jo, nameList, coList, "");
	}
	/**
	 * 禁言，封号, 禁言喇叭
	 *http://************:8181/GM?cmd=seal&type=2&endDateStr=1730108746000&names=3100110000000700001
	 * @param param
	 */
	@Listener(value = EventKey.GM, subStr = { "seal" })
	public void seal(Param param) {
		JSONObject jo = param.get();
		// 要发送给的人名称集合
		String names = jo.getString("names");

		String[] nameArray = names.split(SPLIT);
		int type = jo.getIntValue("type");
		long endTime = jo.getLong("endDateStr");
		try {
			for (String name : nameArray) {
				if (name.isEmpty()) {
					continue;
				}
				if (name.length() >= 18) {
					// 如果length>=18,检查id的合法性
					Long.parseLong(name);
				}
				HumanGlobalServiceProxy.newInstance().sealAccount(Utils.longValue(name), type, endTime);
			}
		} catch (Exception e) {
			Port.getCurrent().returns("success", false, "reason", "格式不正确");
		}
		Port.getCurrent().returns("success", true, "reason", "收到在处理中");
	}

	/**
	 * 禁言
	 *
	 * @param jo
	 * @param nameList
	 * @param coList
	 * @param reason
	 */
	public void doSeal(JSONObject jo, List<String> nameList,
			List<String> coList, String reason) {
		if (nameList.size() == 0) {
			boolean flag = false;
			if ("".equals(reason)) {
				reason = "操作成功";
				flag = true;
			}
			// 循环出口
			Port.getCurrent().returnsAsync(jo.getLongValue("pid"), "success",
					flag, "reason", reason);
			return;
		}

		String name = nameList.remove(0);
		// 如果是ID
		String whereSql = Utils.createStr(" where name = '{}'", name);
		if (name.length() >= 18) {
			whereSql = Utils.createStr(" where id = '{}'", name);
		}

		DB db = DB.newInstance(Human.tableName);
		db.findByQuery(false, whereSql, DBKey.COLUMN, coList);
		db.listenResult(this::_result_seal, "jo", jo, "nameList", nameList,
				"coList", coList, "reason", reason, "name", name);

	}

	private void _result_seal(Param results, Param context) {
		List<RecordTransient> list = results.get();
		JSONObject jo = context.get("jo");
		List<String> nameList = context.get("nameList");
		List<String> coList = context.get("coList");
		String reason = context.getString("reason");

		long receiverId;
		if (list.size() > 0) {
			receiverId = list.get(0).get("id");
		} else {
			reason += "玩家 " + context.getString("name") + " 不存在<br/>";
			doSeal(jo, nameList, coList, reason);
			return;
		}

		int type = jo.getIntValue("type");

		long endTime = jo.getLong("endDateStr");

//		HumanGlobalServiceProxy.newInstance().sealAccount(receiverId, type, endTime);

		doSeal(jo, nameList, coList, reason);

	}

	/**
	 * 开启运营活动
	 * @param param
	 */
	@Listener(value = EventKey.GM, subStr = { "openOperateActivity" })
	public void openOperateActivity(Param param) {
		Port.getCurrent().returns("success", false, "reason", "功能未实现");
	}
	/**
	 * 关闭运营活动
	 * @param param
	 */
	@Listener(value = EventKey.GM, subStr = { "closeOperateActivity" })
	public void closeOperateActivity(Param param) {
		Port.getCurrent().returns("success", false, "reason", "功能未实现");
	}

	/**
	 * 充值礼包新增
	 * <AUTHOR>
	 * @Date 2023/8/23
	 * @Param
	 */
	//"{data={\"id\":\"ggggg-44444-jjhjhj-rrrr-dafdadfeda\",\"giftCode\":\"1000006\",\"vipPoints\":180,\"amountToDiamond\":180,\"price\":180,\"priceValueRatio\":211,\"duration\":86400,\"purchaseLimitAmount\":1,\"isAISupported\":false,\"items\":{\"111\":2,\"2\":2,\"205\":3},\"platformCreative\":{\"enabled\":false,\"background\":\"\"},\"ctwid\":\"G20Q52KXL\",\"humanId\":\"600040000178800001\",\"currency\":\"JPY\",\"currencyPrice\":180,\"currencyTag\":\"¥\",\"templateId\":\"1\",\"Server\":\"30004\",\"Result\":false,\"GameReturn\":\"\"}, cmd=sendGiftInfo, serverIds=[30004]}"
	@Listener(value = EventKey.GM, subStr = { "sendGiftInfo" })
	public void sendGiftInfo(Param param) {
		long pid = Port.getCurrent().createReturnAsync();
		JSONObject jsonObject = param.get();
		String data = jsonObject.getString("data");
		JSONObject jo = Utils.toJSONObject(data);
		long humanId = Utils.longValue(jo.getString("humanId"));//玩家id
		if(S.isAdmin){
			int serverId = Utils.getServerIdByHumanId(humanId);
			if(serverId == 0){
				Human human = (Human)EntityManager.getEntity(Human.class, humanId);
				if(human == null){
					Log.temp.error("===玩家不存在，jsonObj={}", jsonObject);
					Port.getCurrent().returns("success", false, "reason", "玩家不存在");
					return;
				}
				serverId = human.getServerId();
			}
			int serverTag = Util.getServerTagId(serverId);
			String url = CheckAdminService.remoteIdHttpAddrMap.get(String.valueOf(serverTag));
			if(url == null || url.isEmpty()){
				Port.getCurrent().returns("success", false, "reason", Utils.createStr("找不到{}服务器的http地址链接", serverId));
				return;
			}
			
			String portHttpAsync2 = ConstPf.PORT_HTTP_ASYNC_PREFIX + new Random().nextInt(ConstPf.PORT_STARTUP_NUM_CHAT);

			HttpAsyncSendServiceProxy asyncSendProxy = HttpAsyncSendServiceProxy.newInstance(Distr.NODE_ADMIN_SERVER, portHttpAsync2, ConstPf.SERV_HTTP_SEND);
			asyncSendProxy.httpPostJsonAsync(Utils.createStr("http://{}{}", url, HttpServerHandler.GM_CMD_NEED_RESULT), jsonObject, true);
			asyncSendProxy.listenResult(this::_result_httpPostJsonAsync_sendGiftInfo, "pid", pid, "serverId", serverId, "json", data);
			return;
		}

		Human human = (Human)EntityManager.getEntity(Human.class, humanId);
		if(human == null){
			Log.temp.error("===玩家不存在，jsonObj={}", jsonObject);
			Port.getCurrent().returns("success", false, "reason", Utils.createStr("此服务器没有这个玩家roleId={}", humanId));
			return;
		}
		int serverTag = Util.getServerTagId(human.getServerId());
		if(serverTag != C.GAME_SERVER_ID){
			Log.temp.error("===服务器id不匹配， serverTag={}，jsonObj={}", serverTag, jsonObject);
			Port.getCurrent().returns("success", false, "reason", Utils.createStr("此服务器没有这个玩家humanId={}， serverId={}， serverTag={}", humanId, human.getServerId(), serverTag));
			return;
		}

		String id = jo.getString("id");//礼包的唯一id
		int vipPoints = jo.getIntValue("vipPoints");//购买礼包后可获得的vip点数
		int amountToDiamond = jo.getIntValue("amountToDiamond");//此礼包付费但是购买失败后给玩家补单的钻石数量
		int price = jo.getIntValue("price");//礼包的定价
		int priceValueRatio = jo.getIntValue("priceValueRatio");//礼包性价比
		int duration = jo.getIntValue("duration");//弹出时长 （秒）
		int purchaseLimitAmount = jo.getIntValue("purchaseLimitAmount");
		boolean isAISupported = jo.getBooleanValue("isAISupported");
		String currency = jo.getString("currency");
		double currencyPrice = jo.getDouble("currencyPrice");
		String currencyTag = jo.getString("currencyTag");
		String templateId = jo.getString("templateId");
		String giftCode = jo.getString("giftCode");//礼包的标识sn

		String itemJSON = jo.getString("items");
		Map<Integer, Integer> itemMap = Utils.jsonToMapIntInt(itemJSON);// 礼包内包含的物品 {item id: item quantity}
		for(Map.Entry<Integer, Integer> entry : itemMap.entrySet()){
			ConfGoods confItem = ConfGoods.get(entry.getKey());
			if(confItem == null){
				Log.temp.error("===ConfItemData 配表错误。not find sn={}", entry.getKey());
				Port.getCurrent().returns("success", false, "reason", Utils.createStr("{}物品不存在", entry.getKey()));
				return;
			}
		}
		JSONObject joTemp = Utils.toJSONObject(jo.getString("platformCreative"));// 背景图片
		boolean enabled = joTemp.getBooleanValue("enabled");
		String background = joTemp.getString("background");

		PayGift payGift = new PayGift();
		payGift.setHumanId(humanId);
		payGift.setPayId(id);
		payGift.setVipPoints(vipPoints);
		payGift.setAmountToDiamond(amountToDiamond);
		payGift.setPrice(price);
		payGift.setPriceValueRatio(priceValueRatio);
		payGift.setDuration(duration);
		payGift.setPurchaseLimitAmount(purchaseLimitAmount);
		payGift.setItems(itemJSON);
		payGift.setEnabled(enabled);
		payGift.setBackground(background);
		payGift.setCurrency(currency);
		payGift.setCurrencyPrice(currencyPrice);
		payGift.setCurrencyTag(currencyTag);
		payGift.setTemplateId(convertTemplateId(templateId));
		payGift.setGiftCode(giftCode);

		payGift.setCreateTime(Port.getTime());
		payGift.setEndTime(payGift.getCreateTime() + duration * Time.SEC);
		// 先不保存，也不设置id
		HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance();
		proxy.getInfo(humanId);
		proxy.listenResult(this::_result_getInfo_payGift, "pid", pid,"payGift", payGift, "json", data);

	}

	/**
	 * AI礼包模板id转换
	 * @param templateId
	 * @return
	 */
	public static String convertTemplateId(String templateId){
		int giftTemplateId = Utils.intValue(templateId);
		if(giftTemplateId == 1){
			return ParamKey.PAYGIFT_TYPE_1;
		}
		if(giftTemplateId == 2){
			return ParamKey.PAYGIFT_TYPE_5;
		}
		if(giftTemplateId == 3){
			return ParamKey.PAYGIFT_TYPE_6;
		}
		if(giftTemplateId == 101){
			return ParamKey.PAYGIFT_TYPE_4;
		}
		if(giftTemplateId >= 100 && giftTemplateId <= 199){
			return ParamKey.PAYGIFT_TYPE_2;
		}
		if(giftTemplateId >= 200 && giftTemplateId <= 299){
			return ParamKey.PAYGIFT_TYPE_3;
		}
		// 默认为1
		return ParamKey.PAYGIFT_TYPE_1;
	}

	private void _result_httpPostJsonAsync_sendGiftInfo(Param results, Param context){
		long pid = context.get("pid");
		JSONObject joTemp = new JSONObject();
		if(!results.containsKey("success")){
			Port.getCurrent().returnsAsync( pid,"success", false, "reason", "未知的结果");
			return;
		}
		joTemp.put("success", results.getBoolean("success"));
		joTemp.put("reason", results.getString("reason"));

		Port.getCurrent().returnsAsync(pid, "success", results.getBoolean("success"), "reason", joTemp.toJSONString());
	}

	private void _result_getInfo_payGift(Param results, Param context){
		long pid = context.get("pid");
		HumanGlobalInfo info = results.get();
		String json = context.get("json");
		if(info == null){
			JSONObject jo = Utils.toJSONObject(json);
			long humanId = Utils.longValue(jo.getString("humanId"));//玩家id
			Log.temp.error("===_result_getInfo_payGift 玩家不在线, 进待办 isAdmin={}, sId={} ,Context={}", S.isAdmin, C.GAME_SERVER_ID, context);
			Param param = new Param();
			param.put("humanId", humanId);
			param.put("json", json);
			param.put("time", Port.getTime());
			HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance();
			proxy.pockLineAdd(humanId, PocketLineEventSubKey.PAY_GIFT_CREATE, param.toJsonString());
			Port.getCurrent().returnsAsync(pid, "success", true, "reason", "玩家不在线进待办");
			return;
		}
		PayGift payGift = context.get("payGift");
		HumanObjectServiceProxy proxy = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
		proxy.payGiftNew(payGift, json);
		proxy.listenResult(this::_result_payGift, "pid", pid, "payGift", payGift, "json", json);
	}

	private void _result_payGift(boolean timeout, Param results, Param context){
		long pid = context.get("pid");
		boolean result = Utils.getParamValue(results, "result", false);
		String reason = Utils.getParamValue(results, "reason", "未知原因");
		if(!result || timeout){
			Log.temp.error("===_result_payGift 充值礼包，result={}, timeout={}，jsonObj={}", result, timeout, context);
		}
		Port.getCurrent().returnsAsync(pid, "success", result, "reason", reason);
	}

	
	@Listener(value = EventKey.GM, subStr = { "pullData" })
	public void pullData(Param param) {
		JSONObject jo = param.get();
		String data = jo.getString("data");
		long humanId = Utils.longValue(Utils.toJSONObject(data).getString("humanId"));

		GMGameServiceProxy proxy = GMGameServiceProxy.newInstance();
//		proxy.gm_loadData(humanId);

		Port.getCurrent().returnsAsync(Port.getCurrent().createReturnAsync(), "success", true, "reason", "");
	}

	@Listener(value = EventKey.GM, subStr = { "pullDataCreate" })
	public void pullDataCreate(Param param) {
		JSONObject jo = param.get();
		String data = jo.getString("data");
		GMGameServiceProxy proxy = GMGameServiceProxy.newInstance();
//		proxy.gm_createData(data);
	}

	//http://127.0.0.1:8018/GM?cmd=innerPay&money=6&humanId=110010000328300014
	@Listener(value = EventKey.GM, subStr = { "innerPay" })
	public void chargeGs(Param param) {
		JSONObject jo = param.get();
		int money = jo.getIntValue("money");
		long humanId = jo.getLongValue("humanId");

		GuildLeagueWarmUpServiceProxy proxy = GuildLeagueWarmUpServiceProxy.newInstance();
		proxy.update1();

//		resetAccountPort(money);

//		ConfPayMall conf = ConfPayMall.get(money);
//		String rmb=String.valueOf(conf.price / 100);
//		HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance();
//		prx.getInfo(humanId);
//		prx.listenResult(this::_result_chargeGs,"pid", Port.getCurrent().createReturnAsync(), "money", money, "humanId", humanId, "rmb",rmb);
	}

	public void resetAccountPort(int index){
		// 重启accountPort0
		Log.temp.info("===重启accountPort{}开始", index);
		Node node = Port.getCurrent().getNode();
		Port port = node.getPort(D.PORT_ACCOUNT + index);
		node.delPort(port);

		AccountPort accountPort = new AccountPort(D.PORT_ACCOUNT + index);
		accountPort.startup(node);
		// 启动并加入服务
		AccountLoginService servLogin = new AccountLoginService(accountPort, node.getId());
		servLogin.startup();
		accountPort.addService(servLogin);
	}

	private void _result_chargeGs(Param results, Param context){
		long humanId = context.getLong("humanId");
		long pid = context.getLong("pid");
		int money = context.getInt("money");
		String rmb= context.getString("rmb");
		HumanGlobalInfo info = results.get();

		if(info != null){
			HumanObjectServiceProxy prx = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
			prx.chargeGs(money);
			prx.listenResult(this::_result_chargeGs2, context);
			Port.getCurrent().returnsAsync(pid, "success", true, "param", rmb);
		}else{
			List<String> columns = new ArrayList<>();
			columns.add(Human.K.id);
			String whereSql = Utils.createStr(" where {}={}", Human.K.id, humanId);
			DB db = DB.newInstance(Human.tableName);
			db.findByQuery(false, whereSql, DBKey.COLUMN, columns);
			db.listenResult(this::_result_chargeGs1, context);
		}
	}

	private void _result_chargeGs1(Param results, Param context){
		long pid = context.getLong("pid");
		int money = context.getInt("money");
		String rmb= context.getString("rmb");
		List<RecordTransient> list = results.get();
		if(list.size() == 0){
			Port.getCurrent().returnsAsync(pid, "success", false, "reason", "玩家不存在");
			return;
		}

//		ConfPayCharge confPayCharge=ConfPayCharge.get(money);
//		String rmb=String.valueOf(confPayCharge.rmb);

		long humanId = context.getLong("humanId");
//		Pocket.add(humanId, PocketLineKey.Charge_Gs, String.valueOf(money));
//		Port.getCurrent().returnsAsync(pid, "success", true,"param", rmb);
	}
	private void _result_chargeGs2(Param results, Param context){
		long pid = context.getLong("pid");
		boolean result = results.get("success");
		String reason = results.get("reason");
		if(result){
			Port.getCurrent().returnsAsync(pid, "success", true);
		}else{
			Port.getCurrent().returnsAsync(pid, "success", false, "reason", reason);
		}
	}


	/**
	 * 扣除道具
	 * //http://127.0.0.1:8018/GM?cmd=test05&sn=2&num=100&id=110010000328300014
	 * @param param
	 */
	@Listener(value = EventKey.GM, subStr = { "test05" })
	public void test05 (Param param){
		Log.game.info("后台删道具");
		long pid = Port.getCurrent().createReturnAsync();
		JSONObject jo = param.get();
		long roleId=jo.getLong("id");
		int sn = jo.getInteger("sn");
		int num = jo.getInteger("num");
		if (num == 0){
			Port.getCurrent().returnsAsync(pid, "success", false, "reason", "数量不能为0");
			return;
		}
		HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance();
		prx.getInfo(roleId);
		prx.listenResult(this::_result_test05, "num", num, "pid", pid,"sn",sn,"id",roleId);

	}

	private void _result_test05(Param results, Param context) {
		long pid = context.getLong("pid");
		int sn = context.getInt("sn");
		int num = context.getInt("num");
		long id = context.getLong("id");
		HumanGlobalInfo info = results.get();
		if (info != null) {
			HumanObjectServiceProxy proxy = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
			proxy.removeItem(sn, num);
			Port.getCurrent().returnsAsync(pid, "success", true, "reason", "操作成功");
		} else {
			Port.getCurrent().returnsAsync(pid, "success", false, "reason", "玩家不在线");
		}
	}

	@Listener(value = EventKey.GM, subStr = { "test06" })
	public void test06 (Param param){
		Log.game.info("后台减货币");
		long pid = Port.getCurrent().createReturnAsync();
		JSONObject jo = param.get();
		long roleId=jo.getLong("id");
		int sn = jo.getInteger("sn");
		int num = jo.getInteger("num");
		if (num == 0){
			Port.getCurrent().returnsAsync(pid, "success", false, "reason", "数量不能为0");
			return;
		}
		HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance();
		prx.getInfo(roleId);
		prx.listenResult(this::_result_test06, "num", num, "pid", pid,"sn",sn,"id",roleId);
	}

	private void _result_test06(Param results, Param context) {
		long pid = context.getLong("pid");
		long humanId = context.getLong("id");
		int sn = context.getInt("sn");
		int num = context.getInt("num");
		HumanGlobalInfo info = results.get();
		if (info != null) {
			HumanObjectServiceProxy proxy = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
			proxy.removeMoney(sn, num);
		} else {
			Currency currency = (Currency) EntityManager.getEntity(Currency.class, humanId);
			if (currency == null){
				Port.getCurrent().returnsAsync(pid, "success", false, "reason", "操作失败，玩家不存在");
				return;
			}
			switch (sn){
				case TokenItemType.COIN:// 1金币
				currency.setCoin(currency.getCoin() - num);
				break;
				case TokenItemType.GOLD:// 2钻石
				currency.setGold(currency.getGold() - num);
				break;
				case TokenItemType.Money999:	// 999星钻
				currency.setMoney_999(currency.getMoney_999() - num);
				break;
				case TokenItemType.Money1001:	// 1001神灯
				currency.setMoney_1001(currency.getMoney_1001() - num);
				break;
			}
			currency.update();
		}
		Port.getCurrent().returnsAsync(pid, "success", true, "reason", "操作成功");
	}

	/**
	 * 移除humanport
	 * @param param
	 */
	@Listener(value = EventKey.GM, subStr = { "pullHumanToCommon" })
	public void resetHumanPort(Param param) {
		long pid = Port.getCurrent().createReturnAsync();
		JSONObject jo = param.get();
		if(!jo.containsKey("humanId")){
			Port.getCurrent().returnsAsync(pid,"success", false);
			return;
		}
		long num = jo.getLong("humanId");
		Node node = Port.getCurrent().getNode();
		Port port = node.getPort(D.PORT_HUMAN + num);
		try {
			for (Service service : port.getServices()) {
				if (service instanceof HumanService) {
					HumanService humanService = (HumanService) service;
					Set<Long> humanIdSet = humanService.getHumanIdSet();
					for (long id : humanIdSet) {
						HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance();
						proxy.kick(id, ErrorTip.AccountIpLock);
					}
				}
			}
		} catch (Exception e){
			LogCore.core.error("===resetHumanPort 重置玩家端口踢人失败, D.PORT_HUMAN{}, e={}", num, e);
		}
		node.delPort(port);
		HumanPort humanPort = new HumanPort(D.PORT_HUMAN + num);
		humanPort.startup(node);
		// 启动并加入服务
		HumanService servHuman = new HumanService(humanPort, node.getId());
		servHuman.startup();
		humanPort.addService(servHuman);
		Log.human.error("===resetHumanPort 重置玩家端口成功, D.PORT_HUMAN{}", num);
		Port.getCurrent().returnsAsync(pid,"success", true);
	}

	/**
	 * 删除排行榜数据
	 * @param param
	 */
	@Listener(value = EventKey.GM, subStr = { "updateAnnoucement" })
	public void updateAnnoucement(Param param) {
		JSONObject jo = param.get();
		long humanId = jo.getLongValue("humanId");
		RankManager.inst().removeRank(humanId);
		Port.getCurrent().returnsAsync(Port.getCurrent().createReturnAsync(), "success",true, "reason", "已删除");
	}

	/**
	 * curl "http://************:8181/GM?cmd=kick&humanId=3100110000000700001&account=g1"
	 * @param param
	 */
	@Listener(value = EventKey.GM, subStr = { "kick" })
	public void kickHuman(Param param) {
		JSONObject jo = param.get();
		long humanId = jo.getLongValue("humanId");
		String account = jo.getString("account");
		if(humanId > 0){
			HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance();
			proxy.kick(humanId, ErrorTip.AccountLoginOther);
		}

		if(account == null || account.isEmpty()){
			return;
		}
		EntityManager.getEntityListAsync(Account.class, account, res-> {
			if (res.failed()) {
				Log.temp.error("accountManager.loadRedis failed: account={}, serverId={}", account, C.GAME_SERVER_ID);
				return;
			}
			List<Account> accountList = res.result();
			for (Account accountEntity : accountList) {
				HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance();
				proxy.kick(accountEntity.getId(), ErrorTip.AccountLoginOther);
			}
		});
	}


	/**
	 * 服务器统一gm指令接口
	 * @param param
	 * 例子注意id和端口：http://************:8181/GM?cmd=bossEliteCreate&bossSn={"type":"setCrossClose","value":1}
	 */
	@Listener(value = EventKey.GM, subStr = { "bossEliteCreate" })
	public void bossEliteCreate(Param param) {
		JSONObject jo = param.get();
		if(jo == null){
			return;
		}
		// TODO 后续gm指令是通过这些获取
		String json = jo.getString("bossSn");
		Log.human.info("这是gm指令{}", json);
		JSONObject jsonObject= Utils.toJSONObject(json);
		optionalExecute(new Param(jsonObject));

		Port.getCurrent().returns("success", true, "param", "收到请求成功反馈，请等待服务器处理");
	}

	@Listener(value = EventKey.GM, subStr = {"addGoods"})
	public void addGoods(Param param) {
		JSONObject jo = param.get();
		String cmdParam = jo.getString("param");
		String[] split = cmdParam.split("_");
		String account = split[0];
		int itemSn = Integer.parseInt(split[1]);
		int itemNum = Integer.parseInt(split[2]);
		String redisKey = "Account." + account;
		JsonArray fields = new JsonArray();
		fields.add("id");
		RedisTools.getHashJsonObject(EntityManager.redisClient, redisKey, res -> {
			List<Account> modelList = EntityManager.toModelList(res.result(), Account.class, redisKey, false);
			if(modelList.isEmpty()){
				return;
			}
			Account accEntity = modelList.get(0);
			long humanId = accEntity.getId();
			HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance();
			proxy.getInfo(humanId);
			proxy.listenResult((returns, context) -> {
				HumanGlobalInfo humanInfo = returns.get();
				if (humanInfo != null) {
					HumanObjectServiceProxy humanPrx = HumanObjectServiceProxy
							.newInstance(humanInfo.nodeId, humanInfo.portId,
									humanInfo.id);
					humanPrx.produceAdd(itemSn, itemNum, MoneyItemLogKey.GM后台操作);
				}
			}, new Param());
		});
	}

	// http://************:8181/GM?cmd=subCmd&type=setCrossClose&value=1
	//curl -G "http://192.168.0.80:31072/GM" --data-urlencode "cmd=bossEliteCreate" --data-urlencode "bossSn={\"type\":\"setCrossClose\",\"value\":1}"
	//线上 curl -G "http://127.0.0.1:31001/GM" --data-urlencode "cmd=subCmd" --data-urlencode "type=setCrossClose" --data-urlencode "value=1"
	@Listener(value = EventKey.GM, subStr = { "subCmd" })
	public void optionalExecute(Param param){
		JSONObject jo = param.get();
		String type = jo.getString("type");
		int value = jo.getIntValue("value");
		switch (type) {
			case "accountPort":
				Log.temp.info("===执行gm指令, 重启accountPort{}开始", value);
				resetAccountPort(value);
				Log.temp.info("===执行gm指令,type={}, value={}", type, value);
				break;
			case "arenaReset":
				if(value == 1){
					Log.temp.info("===执行gm指令, arenaReset开始,生成本周一");
					long timeWeekOne = Utils.getTimeOfWeek(Port.getTime() + Time.DAY, 1, 0);
					String dateStr = Utils.formatTime(timeWeekOne, "yyyy-MM-dd");
					int zone = GlobalConfVal.getZone(C.GAME_SERVER_ID);
					ArenaManager.inst().initRobot(ArenaManager.inst().getArenaRankKey(C.GAME_SERVER_ID, S.isBridge), zone, C.GAME_SERVER_ID, dateStr);
				} else if(value == 2){
					Log.temp.info("===执行gm指令, arenaReset开始，生成下周一");
					long timeWeekOne = Utils.getTimeOfWeek(Port.getTime(), 1, 0) + Time.WEEK;
					String dateStr = Utils.formatTime(timeWeekOne, "yyyy-MM-dd");
					ArenaManager.inst().getArenaRankKey(C.GAME_SERVER_ID, S.isBridge, true, res->{
						String key = res.result();
						int zone = GlobalConfVal.getZone(C.GAME_SERVER_ID);
						ArenaManager.inst().initRobot(key, zone, C.GAME_SERVER_ID, dateStr, true, true);
						Log.temp.info("===初始化下周机器人，serverId={}, key={}, zone={}", C.GAME_SERVER_ID, key, zone);
					});
				}
				Log.temp.info("===执行gm指令,type={}, value={}", type, value);
				break;
			case "delRank":
				long humanId = jo.getLongValue("humanId");
				if(value == 0){
					RankManager.inst().removeRank(humanId);
				} else {
                    RankManager.inst().removeRank(humanId, value);
				}
				Log.temp.info("===执行gm指令,type={}, value={}", type, value);
				break;
			case "dailyActRefresh":
				param = new Param();
				param.put("type",type);
				param.put("humanId", jo.getLongValue("humanId"));
				param.put("actType", jo.getIntValue("actType"));
				HumanGlobalServiceProxy prx1 = HumanGlobalServiceProxy.newInstance();
				prx1.gmCallHumanServiceMethod(param);
				Log.temp.info("===执行gm指令,type={}, value={}", type, value);
				break;

			case "kickAll":
				Log.temp.info("===执行gm指令, 踢所有人开始");
				HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance();
				prx.kickAll();
				Log.temp.info("===执行gm指令,type={}, value={}", type, value);
				break;

			case "league":
				GuildLeagueWarmUpServiceProxy proxy = GuildLeagueWarmUpServiceProxy.newInstance();
				proxy.update1();
				Log.temp.info("===执行gm指令,type={}, value={}", type, value);
				break;

			case "clearRank": {
				String redisKey = Utils.createStr("{}_{}_{}", RedisKeys.rankSn_list, value, C.GAME_SERVER_ID);
				RedisTools.del(EntityManager.redisClient, redisKey);
				Log.temp.info("===执行gm指令,type={}, value={}", type, value);
			}
				break;
			case "leagueGM":
				GuildLeagueWarmUpServiceProxy proxyGm = GuildLeagueWarmUpServiceProxy.newInstance();
				proxyGm.gm(value, jo.getString("str"));
				Log.temp.info("===执行gm指令,type={}, value={}", type, value);
				break;
			case "setCrossClose":
				S.isCrossClose = value == 1;
				Log.temp.info("===执行gm指令,type={}, value={}", type, value);
				break;
			case "arenaRankedRoom":
				AllotServerServiceProxy pxy = AllotServerServiceProxy.newInstance();
				pxy.gm(14);
				Log.temp.info("===执行gm指令,type={}, value={}", type, value);
				break;
			case "leagueSeasonTime":
				GuildLeagueWarmUpServiceProxy proxyTime = GuildLeagueWarmUpServiceProxy.newInstance();
				proxyTime.update2(new Param());
				Log.temp.info("===执行gm指令,type={}, value={}", type, value);
				break;
			case "crossLeague":
				try {
					CrossManager.getInstance().callCrossFunc(CrossType.cross_chaos_battle, Config.SERVER_ID, res -> {
						try {
							if(res.failed()){
								Log.temp.error("==跨服获取数据出问题 {}", res.cause());
								return;
							}
							CrossPoint result = res.result();
							GuildLeagueServiceProxy proxyCross = GuildLeagueServiceProxy.newInstance(result.getNodeId(), D.SERV_GUILD_LEAGUE);
							proxyCross.gmUpdate();
							Log.temp.info("==执行gm指令，跨服，result={}", result);
						} catch (Exception e){
							Log.temp.error("==跨服链接出问题 ", e);
						}
					});
				} catch (Exception e){
					Log.temp.error("====跨服获取数据出问题", e);
				}
				Log.temp.info("===执行gm指令,type={}, value={}", type, value);
				break;
			case "redisRemove":
				String key = jo.getString("delkey");
				RedisTools.del(EntityManager.redisClient, key);
				Log.temp.error("===删除rediskey={}", key);
				break;
			case "redisRemoveAll":
				String delkey = jo.getString("delkey");
				List<String> keyList = new ArrayList<>();
				String[] keys = Utils.strToStrArray(delkey, "\\|");
				for (String k : keys) {
					if(k.isEmpty()){
						continue;
					}
					keyList.add(k + "*");
				}
				if(keyList.isEmpty()){
					Log.temp.error("===清除rediskey失败，delkey为空 {}", delkey);
					return;
				}
				EntityManager.redisClient.del(keyList, h -> {
					if (h.succeeded()) {
						Log.temp.error("===清除。keyList={}", keyList);
					}
				});
				Log.temp.info("===执行gm指令,type={}, value={}", type, value);
				break;
			case "delRedisKey":
				delRedisKey();
				Log.temp.info("===执行gm指令,type={}, value={}", type, value);
				break;
			case "regenActServerData": {
				String json = jo.getString("param");
				GameDebugManager.inst().gmActivity(null, json.split(","));
				Log.temp.info("===执行gm指令,type={}, value={}", type, value);
			} 	break;
			case "gmResetActivity":
				String[] arr = new String[]{"5", Config.SERVER_ID+"", value+""};
				org.gof.demo.worldsrv.activity.ActivityControlServiceProxy proxyGmActivty = org.gof.demo.worldsrv.activity.ActivityControlServiceProxy.newInstance();
				proxyGmActivty.gmActivity(arr);
				Log.temp.info("===执行gm指令,type={}, value={}", type, value);
				break;
			case "gmActivitySet":
				String[] arr2 = new String[]{"6", Config.SERVER_ID+"", value+""};
				org.gof.demo.worldsrv.activity.ActivityControlServiceProxy proxySet = org.gof.demo.worldsrv.activity.ActivityControlServiceProxy.newInstance();
				proxySet.gmActivity(arr2);
				Log.temp.info("===执行gm指令,type={}, value={}", type, value);
				break;
			case "gmActivityType":
				int gmType = jo.getIntValue("gmType");
				List<String> strList = new ArrayList<>();
				strList.add(String.valueOf(gmType));
				strList.add(String.valueOf(Config.SERVER_ID));
				strList.add(String.valueOf(value));
				if (jo.containsKey("valueList")) {
					strList.addAll(Arrays.asList(jo.getString("valueList").split(",")));
				}
				String[] arrStr = strList.toArray(new String[0]);
				org.gof.demo.worldsrv.activity.ActivityControlServiceProxy proxySet2 = org.gof.demo.worldsrv.activity.ActivityControlServiceProxy.newInstance();
				proxySet2.gmActivity(arrStr);
				Log.temp.info("===执行gm指令,type={}, value={}, 实际参数={}", type, value, Utils.listToString(Arrays.asList(arrStr)));
				break;
			case "gmCrossArena":
				gmCrossArena(jo);
				Log.temp.info("===执行gm指令,type={}, value={}", type, value);
				break;
			case "gmAdmin":
				AdminCenterServiceProxy proxyAdmin = AdminCenterManager.createAdminProxy();
				proxyAdmin.gm(value);
				Log.temp.info("===执行gm指令,type={}, value={}", type, value);
				break;
			case "gmCross":
				crossServerGm(value, jo);
				Log.temp.info("===执行gm指令,type={}, value={}", type, value);
				break;
			case "gmSelectLog":
				S.isSelectLog = value == 1;
				Log.temp.info("===执行gm指令,type={}, value={}", type, value);
				break;
			case "gmIsArena1":
				S.isArena1 = value == 1;
				Log.temp.info("===执行gm指令,type={}, value={}", type, value);
				break;
			case "gmLoadCarPark":
				org.gof.demo.worldsrv.carPark.CarParkServiceProxy proxyCarPark = org.gof.demo.worldsrv.carPark.CarParkServiceProxy.newInstance();
				proxyCarPark.update1(Integer.toString(value));
				Log.temp.info("===执行gm指令,type={}, value={}", type, value);
				break;
			case "gmKungFuRace":
				String json = jo.getString("param");
				try{
					Log.temp.info("武道会通用gm");
					KungFuRaceUtils.getCrossKungFuRaceServiceProxy(null, res -> {
						if(res.failed()){
							return;
						}
						String[] params = json.split(",");
						// 转为 List
						List<String> list = new ArrayList<>(Arrays.asList(params));
						// 在头部添加元素
						list.add(0, "101");
						KungFuRaceServiceProxy proxyKungFuRace = res.result();
						Param kungFuRaceparam = new Param();
						kungFuRaceparam.put("serverId", Config.SERVER_ID);
						kungFuRaceparam.put("params", list.toArray(new String[0]));
						proxyKungFuRace.gmCommand("common", kungFuRaceparam);
					});
					Log.temp.info("===执行gm指令,type={}, value={}", type, value);
				}
				catch(Exception e){
					Log.temp.error("===执行gm指令,type={}, value={}失败!", type, value);
				}
				break;
			case "gmDelRedisEntityByTTL":
				{
					int cursor = jo.containsKey("cursor") ? jo.getIntValue("cursor") : 0;
					int pageSize = jo.containsKey("pageSize") ? jo.getIntValue("pageSize") : 500;
					GameDebugManager.inst().delRedisEntityByTTL(null, new String[]
					{
							String.valueOf(value),
							String.valueOf(cursor),
							String.valueOf(pageSize)
					});
				}
				break;
			case "gmDelRedisEntityByLevel":
				{
					int ttl = jo.containsKey("ttl") ? jo.getIntValue("ttl") : 6 * Tool.HOUR;
					int pageSize = jo.containsKey("pageSize") ? jo.getIntValue("pageSize") : 500;
					GameDebugManager.inst().delRedisEntityByLevel(null, new String[]
					{
							String.valueOf(value),
							String.valueOf(ttl),
							String.valueOf(pageSize)
					});
				}
				break;
			case "gmRefreshRedisEntityTTL":
				{
					int cursor = jo.containsKey("cursor") ? jo.getIntValue("cursor") : 0;
					int pageSize = jo.containsKey("pageSize") ? jo.getIntValue("pageSize") : 500;
					GameDebugManager.inst().refreshRedisEntityTTL(null, new String[]
					{
							String.valueOf(value),
							String.valueOf(cursor),
							String.valueOf(pageSize)
					});
				}
				break;
			case "gmStopRedisWork":
				{
					GameDebugManager.inst().stopRedisWork(null, null);
				}
				break;
			default:
				Log.temp.error("===执行gm指令, 未知指令{}", type);
				break;
		}
		Log.temp.info("===执行gm指令,type={}, value={} 结束", type, value);
		Port.getCurrent().returns("success", true);
	}

	private void gmCrossArena(JSONObject jo){
		try {
			CrossManager.getInstance().callCrossFunc(CrossType.cross_arena_rank, Config.SERVER_ID, res -> {
				try {
					if(res.failed()){
						Log.temp.error("==跨服获取数据出问题 {}", res.cause());
						return;
					}
					CrossPoint result = res.result();
					int value = jo.getIntValue("value");
					ArenaCrossServiceProxy proxy = ArenaCrossServiceProxy.newInstance(result.getNodeId(), D.SERV_ARENA_CROSS);
					proxy.update1(value);
				} catch (Exception e){
					Log.temp.error("==跨服链接出问题 ", e);
				}
			});
		} catch (Exception e){
			Log.temp.error("==跨服链接出问题 ", e);
		}
	}

	private void delRedisKey(){
		// TODO 删除没用的rediskey，用于减少redis占用
		List<String> keys = new ArrayList<>();
		keys.add(RedisKeys.bridge_pvp_season_group_list+Config.SERVER_ID);
		keys.add("GuildLeagueHistoryWarmUp."+Config.SERVER_ID);
		keys.add("entity.init.GuildLeagueHistoryWarmUp."+Config.SERVER_ID);
		keys.add(RedisKeys.humanCombat+"*");

		EntityManager.redisClient.del(keys, h -> {
			if (h.succeeded()) {
				Log.temp.error("===清除rediskey成功，keys={}", keys);
			}
		});

	}

	/**
	 * 查看玩家是否在线
	 */
	@Listener(value = EventKey.GM, subStr = { "online" })
	public void online(Param param) {
		JSONObject jo = param.get();
		long id = jo.getLongValue("id");
		LogPF.platform.info("查询玩家是否在线，id={}", id);
		Port port = Port.getCurrent();
		long pid = port.createReturnAsync();
		HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance();
		prx.getInfo(id);
		prx.listenResult((results, context) -> {
			port.returnsAsync(pid, "success", results.get() != null);
		});
	}

	private void crossServerGm(int type, JSONObject jo){
		try {
			Log.temp.info("==执行gm指令, 跨服类型{}, 服务器id{}, jo={}", type, Config.SERVER_ID, jo);
			CrossManager.getInstance().callCrossFunc(CrossType.valueOf(type), Config.SERVER_ID, res -> {
				try {
					if(res.failed()){
						Log.temp.error("==跨服获取数据出问题 {}", res.cause());
						return;
					}
					CrossPoint result = res.result();
					String[] strArr = Utils.splitStr(jo.getString("paramValue"), "\\|");

					CrossType crossType = CrossType.valueOf(type);
					if(crossType == CrossType.cross_arena_rank) {
						ArenaCrossServiceProxy proxy = ArenaCrossServiceProxy.newInstance(result.getNodeId());
						proxy.gm(strArr);
						Log.temp.info("==跨服gm指令类型 {}", crossType);
					} else if(crossType ==  CrossType.cross_arena_region_rank) {
						ArenaRankedServiceProxy proxyRanked = ArenaRankedServiceProxy.newInstance(result.getNodeId());
						proxyRanked.gm(Utils.intValue(strArr[0]), strArr);
						Log.temp.info("==跨服gm指令类型 {}", crossType);
					} else if(crossType ==  CrossType.cross_chaos_battle){
						GuildLeagueServiceProxy proxyGvg = GuildLeagueServiceProxy.newInstance(result.getNodeId());
						proxyGvg.gm(Utils.intValue(strArr[0]), strArr[1]);
						Log.temp.info("==跨服gm指令类型 {}", crossType);
					}else{
						Log.temp.error("==跨服gm指令未知类型 {}", crossType);
					}
				} catch (Exception e){
					Log.temp.error("==跨服链接出问题 ", e);
				}
			});
		}catch (Exception e){
			Log.temp.error("==跨服链接出问题 ", e);
		}
	}

	@Listener(value = EventKey.GM, subStr = { "test" })
	public void test (Param param){
		Port port = Port.getCurrent();
		port.returns( "success", true);
	}
}
