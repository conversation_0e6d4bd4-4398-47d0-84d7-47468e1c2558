package org.gof.demo.worldsrv.privilege;

import org.gof.core.support.observer.MsgReceiver;
import org.gof.demo.seam.msg.MsgParam;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.msg.MsgAd;
import org.gof.demo.worldsrv.msg.MsgPrivilege;

public class PrivilegeMsgHandler {

    /**
     * 特权信息C2S消息
     */
    @MsgReceiver(MsgPrivilege.privilege_info_c2s.class)
    public void privilege_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        PrivilegeManager.inst().on_privilege_info_c2s(humanObj);
    }

    /**
     * 特权卡信息C2S消息
     */
    @MsgReceiver(MsgPrivilege.privilege_card_info_c2s.class)
    public void privilege_card_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        PrivilegeManager.inst().on_privilege_card_info_c2s(humanObj);
    }

    /**
     * 特权卡奖励C2S消息
     * @param id 特权卡ID
     */
    @MsgReceiver(MsgPrivilege.privilege_card_reward_c2s.class)
    public void privilege_card_reward_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgPrivilege.privilege_card_reward_c2s msg = param.getMsg();
        PrivilegeManager.inst().on_privilege_card_reward_c2s(humanObj, msg.getId());
    }

    /**
     * 获取全部特权卡C2S消息
     */
    @MsgReceiver(MsgPrivilege.privilege_card_get_all_c2s.class)
    public void privilege_card_get_all_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        PrivilegeManager.inst().on_privilege_card_get_all_c2s(humanObj);
    }

    /**
     * 广告奖励C2S消息
     */
    @MsgReceiver(MsgAd.ad_reward_c2s.class)
    public void ad_reward_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAd.ad_reward_c2s msg = param.getMsg();
        PrivilegeManager.inst().on_ad_reward_c2s(humanObj, msg.getConfigId(), msg.getExtList(), msg.getIsFree());
    }


}
