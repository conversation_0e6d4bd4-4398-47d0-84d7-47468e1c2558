
package org.gof.demo.worldsrv.callback;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.protobuf.InvalidProtocolBufferException;
import io.vertx.core.*;
import io.vertx.core.json.JsonObject;
import org.gof.core.*;
import org.gof.core.db.DBKey;
import org.gof.core.db.FieldTable;
import org.gof.core.dbsrv.DB;
import org.gof.core.dbsrv.redis.AsyncActionResult;
import org.gof.core.dbsrv.redis.BaseModel;
import org.gof.core.dbsrv.redis.EntityManager;
import org.gof.core.interfaces.ISerilizable;
import org.gof.core.support.Param;
import org.gof.core.support.ParamKey;
import org.gof.core.support.Utils;
import org.gof.demo.battlesrv.msgHandler.SkillManager;
import org.gof.demo.battlesrv.support.PropCalc;
import org.gof.demo.worldsrv.entity.*;
import org.gof.demo.worldsrv.equip.EquipManager;
import org.gof.demo.worldsrv.human.HumanData;
import org.gof.demo.worldsrv.human.HumanManager;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.pet.PetManager;
import org.gof.demo.worldsrv.support.Log;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;

import static org.gof.demo.worldsrv.equip.EquipInfo.face;
import static org.gof.demo.worldsrv.equip.EquipInfo.weapon;

/**
 *
 * <AUTHOR>
 * @Date 2024/10/31
 * @Param
 */
public class MemberCallback implements ISerilizable {

    private Param param;
    public MemberCallback(){

    }
    public MemberCallback(Param param, Handler<AsyncResult<Param>> handler){
        handlerTeamMemberLoadOk = handler;
        this.param = param;
    }
    private Handler<AsyncResult<Param>> handlerTeamMemberLoadOk = res->{

    };
    public static void getMemberAsync(List<Long> humanIds, MemberCallback callback){
        HumanData.getList(humanIds, HumanManager.inst().humanClasses, res->{
            if(res.succeeded()){
                List<HumanData> humanDataList = res.result();
                // 在此处处理成功的成员列表
                callback.handlerTeamMemberLoadOk.handle(Future.succeededFuture(callback.param.put("humanDataList", humanDataList)));
            }else{
                Log.human.error("MemberCallback getMemberAsync error", res.cause());
                callback.handlerTeamMemberLoadOk.handle(Future.failedFuture(res.cause()));
            }
        });
    }

    @Override
    public void writeTo(OutputStream out) throws IOException {

    }

    @Override
    public void readFrom(InputStream in) throws IOException {

    }


    public static void loadTableFieldAsync(List<EntityTableSelectVo> voList, Handler<AsyncResult<JSONArray>> handler) {
        List<Future> futures = new ArrayList<>();
        Port port = Port.getCurrent();
        voList.forEach(vo -> {
            Promise<JSONObject> promise = Promise.promise();
            futures.add(promise.future());
            if(vo.isRedis){
                if(ParamKey.redisSelectType_hgetall.equals(vo.selectType)){
                    EntityManager.redisClient.hgetall(vo.redisKey, res -> {
                        if(res.failed() || res.result() == null || res.result().size() != vo.fields.size()){
                            port.doAction(()->{
                                loadtTableFieldDB(promise, vo);
                            });
                        } else {
                            JSONObject jo = new JSONObject();
                            jo.put(ParamKey.loadTableFieldName, vo.tableName);
                            jo.put(ParamKey.loadTableRedisType, vo.selectType);
                            jo.put(ParamKey.loadTableFieldResult, res.result());
//                            jo.put(ParamKey.loadTableEntity, toEntity(vo.clazz, RedisTools.toJsonObject(res.result())));
                            promise.complete(jo);
                        }
                    });
                } else if(ParamKey.redisSelectType_hmget.equals(vo.selectType)){
                    EntityManager.redisClient.hmget(vo.redisKeys, res -> {
                        if(res.failed() || res.result() == null || res.result().size() != vo.fields.size()){
                            port.doAction(()->{
                                loadtTableFieldDB(promise, vo);
                            });
                        } else {
                            JSONObject jo = new JSONObject();
                            jo.put(ParamKey.loadTableFieldName, vo.tableName);
                            JSONObject fieldJo = new JSONObject();
                            boolean isAllNull = true;
                            for(int i=0;i<vo.fields.size();i++){
                                fieldJo.put(vo.fields.get(i), res.result().get(i));
                                if(res.result().get(i) != null){
                                    isAllNull = false;
                                }
                            }
                            if(isAllNull){
                                port.doAction(()->{
                                    loadtTableFieldDB(promise, vo);
                                });
                            } else {
                                jo.put(ParamKey.loadTableRedisType, vo.selectType);
                                jo.put(ParamKey.loadTableFieldResult, fieldJo);
                                promise.complete(jo);
                            }

                        }
                    });
                }

            } else {
                loadtTableFieldDB(promise, vo);
            }
        });

        CompositeFuture.all(futures).onComplete(ret -> {
            if (ret.failed()) {
                handler.handle(Future.failedFuture(ret.cause()));
                return;
            }
            JSONArray result = new JSONArray();
            for (Future<JSONObject> fut : futures) {
                if(fut.failed()){
                    continue;
                }

                JSONObject jo = fut.result();
                result.add(jo);
            }
            AsyncActionResult.success(port, handler, result);
        });
    }

    private static <E extends BaseModel> E toEntity(Class<E> cls, JsonObject jsonObj){
        E entity = jsonObj.mapTo(cls);
        return entity;
    }


    public static void loadtTableFieldDB( Promise<JSONObject> promise, EntityTableSelectVo vo){
        DB db = DB.newInstance(vo.tableName);
        if(ParamKey.redisSelectType_hgetall.equals(vo.selectType)){
//            db.findBy(false, vo.sqlWhereStr);
            db.findBy(false, vo.paramObjs);
        } else if(ParamKey.redisSelectType_hmget.equals(vo.selectType)){
            db.findByQuery(false, vo.sqlWhereStr, DBKey.COLUMN, vo.fields);
        }
        db.listenResult((timeout,returns,context)->{
            if(timeout){
                String errMsg = Utils.createStr("db find list callback timeout, tableName={}, listKeyField={}, whereStr={}, selectType={}",
                        vo.tableName, vo.fields, vo.sqlWhereStr, vo.selectType);
                Log.game.error(errMsg);
                promise.fail(errMsg);
                return;
            }

            if(returns.get() instanceof Record){
                Record record = returns.get();
                if (record == null) {
                    //Log.game.info("loadListFromDB array is empty, sql = {} sqlParams = {}", sql, sqlParams);
                    promise.complete(new JSONObject());
                    return;
                }
                JSONObject jo = new JSONObject();
                jo.put(ParamKey.loadTableFieldName, vo.tableName);
                JSONObject fieldJo = new JSONObject();
                if(vo.fields != null){
                    for(String field : vo.fields){
                        fieldJo.put(field, record.get(field));
                    }
                }
                jo.put(ParamKey.loadTableRedisType, vo.selectType);
                jo.put(ParamKey.loadTableFieldResult, fieldJo);
                promise.complete(jo);
            } else if(returns.get() instanceof List){
                List<?> list = returns.get();
                if (list == null || list.isEmpty()) {
                    //Log.game.info("loadListFromDB array is empty, sql = {} sqlParams = {}", sql, sqlParams);
                    promise.complete(new JSONObject());
                    return;
                }

                JSONObject jo = new JSONObject();
                jo.put(ParamKey.loadTableFieldName, vo.tableName);
                JSONObject fieldJo = new JSONObject();

                if(list.get(0) instanceof Record){
                    Record record = (Record)list.get(0);
                    if (record == null) {
                        //Log.game.info("loadListFromDB array is empty, sql = {} sqlParams = {}", sql, sqlParams);
                        promise.complete(new JSONObject());
                        return;
                    }
                    if(vo.fields != null){
                        for(String field : vo.fields){
                            fieldJo.put(field, record.get(field));
                        }
                    }
                    jo.put(ParamKey.loadTableRedisType, vo.selectType);
                    jo.put(ParamKey.loadTableFieldResult, fieldJo);
                    promise.complete(jo);
                } else if(list.get(0) instanceof RecordTransient){
                    RecordTransient recordT = (RecordTransient)list.get(0);
                    if(recordT != null && vo.fields != null){
                        for(String field : vo.fields){
                            fieldJo.put(field, recordT.get(field));
                        }
                    }
                    jo.put(ParamKey.loadTableRedisType, vo.selectType);
                    jo.put(ParamKey.loadTableFieldResult, fieldJo);
                    promise.complete(jo);
                }

            } else {
                Log.game.error("===未实现代码， loadTableFieldDB返回类型错误=== tableName={}, selectType={}, field={}", vo.tableName, vo.selectType, vo.fields);
            }

        });
    }


    public void p_role_figure(long humanId, Handler<AsyncResult<Define.p_role_figure>> handler) {
        List<EntityTableSelectVo> voList = new ArrayList<>();
        voList.add(new EntityTableSelectVo(Human.tableName, "Human." + humanId, Arrays.asList(Human.K.name, Human.K.level, Human.K.headSn,
                Human.K.jobModel, Human.K.currentTitleSn), Utils.createStr(" where `{}` ={}", Human.K.id, humanId), true));

        voList.add(new EntityTableSelectVo(Human2.tableName, "Human2." + humanId, Arrays.asList(Human2.K.equipFigureMap,
                Human2.K.wingUse, Human2.K.hairColor, Human2.K.mountUse, Human2.K.artifactUse, Human2.K.currentSkin),
                Utils.createStr(" where `{}` = {}", Human.K.id, humanId), false));

        MemberCallback.loadTableFieldAsync(voList, r -> {
            if (r.failed()) {
                handler.handle(Future.failedFuture(r.cause()));
                return;
            }
            JSONArray ja = r.result();
            Define.p_role_figure.Builder dFigur = Define.p_role_figure.newBuilder();
            for (int i = 0; i < ja.size(); i++) {
                JSONObject jo = ja.getJSONObject(i);
                EntityTableSelectVo vo = voList.get(i);
                JSONObject joField = jo.getJSONObject(ParamKey.loadTableFieldResult);

                if(vo.fields.contains(Human.K.jobModel)){
                    dFigur.setHairFigure(joField.getIntValue(Human.K.jobModel));// 当前职业外观
                }
                if(vo.fields.contains(Human.K.currentTitleSn)){
                    dFigur.setCurrentTitle(joField.getIntValue(Human.K.currentTitleSn));// 头衔
                }
                if(vo.fields.contains(Human2.K.equipFigureMap)){
                    Map<Integer,Integer> figureMap = Utils.jsonToMapIntInt(joField.getString(Human2.K.equipFigureMap));
                    //遍历weapon到face的部位不存在或者value=0就去默认取
                    for(int m = weapon; m <= face; ++m){
                        int sn = figureMap.getOrDefault(m, EquipManager.FIGURE_DEFAULT);
                        dFigur.addEquipList(HumanManager.inst().to_p_key_value(m, sn));
                    }
                }
                if(vo.fields.contains(Human2.K.wingUse)){
                    dFigur.addEquipList(HumanManager.inst().to_p_key_value(HumanManager.wing, joField.getIntValue(Human2.K.wingUse))); // 翅膀
                }
                if(vo.fields.contains(Human2.K.hairColor)){
                    dFigur.setHairFigure(joField.getIntValue(Human2.K.hairColor));// 发色
                }
                if(vo.fields.contains(Human2.K.mountUse)){
                    dFigur.setMountFigure(joField.getIntValue(Human2.K.mountUse));// 坐骑
                }
                if(vo.fields.contains(Human2.K.artifactUse)){
                    dFigur.setArtifactFigure(joField.getIntValue(Human2.K.artifactUse)); // 神器sn
                }
                if(vo.fields.contains(Human2.K.currentSkin)){
                    dFigur.addSkinList(HumanManager.inst().to_p_key_value(2,Utils.intValue(joField.get(Human2.K.currentSkin))));  // 皮肤
                }
                dFigur.setGender(1);// 无性别 默认1
            }
            handler.handle(Future.succeededFuture(dFigur.build()));
        });
    }

    public static void p_head(long humanId, Handler<AsyncResult<Define.p_head>> handler){
        List<EntityTableSelectVo> voList = new ArrayList<>();
        voList.add(new EntityTableSelectVo(Human.tableName, "Human." + humanId, Arrays.asList(Human.K.name, Human.K.headSn,
                Human.K.currentHeadFrameSn), Utils.createStr(" where `{}` ={}", Human.K.id, humanId), true));

        Port port = Port.getCurrent();
        MemberCallback.loadTableFieldAsync(voList, r -> {
            if (r.failed()) {
                AsyncActionResult.fail(port, handler, r.cause());
                return;
            }
            JSONArray ja = r.result();
            Define.p_head.Builder head = Define.p_head.newBuilder();
            for (int i = 0; i < ja.size(); i++) {
                JSONObject jo = ja.getJSONObject(i);
                EntityTableSelectVo vo = voList.get(i);
                JSONObject joField = jo.getJSONObject(ParamKey.loadTableFieldResult);
                if (vo.fields.contains(Human.K.headSn)) {
                    head.setId(Utils.intValue(joField.get(Human.K.headSn)));
                }
                if (vo.fields.contains(Human.K.currentHeadFrameSn)) {
                    head.setFrameId(Utils.intValue(joField.get(Human.K.currentHeadFrameSn)));
                }
            }
            head.setUrl("");
            AsyncActionResult.success(port, handler, head.build());
        });
    }


    public void p_role_info(long humanId, Handler<AsyncResult<JSONObject>> handler) {
        List<Future> futures = new ArrayList<>();

        Promise<JSONObject> promise = Promise.promise();
        futures.add(promise.future());
        JSONObject jo =new JSONObject();
        Port port = Port.getCurrent();
        p_role_figure(humanId, res -> {
            if(res.failed()){
                promise.fail(res.cause());
                return;
            }
            jo.put("figure", res.result());
            promise.complete(jo);
        });

        Promise<JSONObject> promise2 = Promise.promise();
        futures.add(promise2.future());
        p_head(humanId, res -> {
            if(res.failed()){
                promise2.fail(res.cause());
                return;
            }
            jo.put("head", res.result());
            promise2.complete(jo);
        });

        CompositeFuture.all(futures).onComplete(ret -> {
            if(ret.failed()){
                AsyncActionResult.fail(port, handler, ret.cause());
                return;
            }
            AsyncActionResult.success(port, handler, jo);
        });
    }


    public static void p_role_figureToJSONObject(long humanId, Handler<AsyncResult<JSONObject>> handler) {
        List<EntityTableSelectVo> voList = new ArrayList<>();
        voList.add(new EntityTableSelectVo(Human.tableName, "Human." + humanId, Arrays.asList(Human.K.id, Human.K.name, Human.K.level, Human.K.headSn,
                Human.K.jobModel, Human.K.currentTitleSn, Human.K.currentHeadFrameSn, Human.K.combat), Utils.createStr(" where `{}` ={}", Human.K.id, humanId), true));

        voList.add(new EntityTableSelectVo(Human2.tableName, "Human2." + humanId, Arrays.asList(Human2.K.id,Human2.K.equipFigureMap,
                Human2.K.wingUse, Human2.K.hairColor, Human2.K.mountUse, Human2.K.artifactUse, Human2.K.currentSkin),
                Utils.createStr(" where `{}` = {}", Human.K.id, humanId), false));

        Port port = Port.getCurrent();
        MemberCallback.loadTableFieldAsync(voList, r -> {
            if (r.failed()) {
//                handler.handle(Future.failedFuture(r.cause()));
//                AsyncActionResult.futureSuccess(port, handler.handle(Future.failedFuture(r.cause())));
                AsyncActionResult.fail(port, handler, r.cause());
                return;
            }
            JSONArray ja = r.result();
            Define.p_role_figure.Builder dFigur = Define.p_role_figure.newBuilder();
            Define.p_head.Builder head = Define.p_head.newBuilder();
            head.setUrl("");

            JSONObject joResult = new JSONObject();

            for (int i = 0; i < ja.size(); i++) {
                JSONObject jo = ja.getJSONObject(i);
                EntityTableSelectVo vo = voList.get(i);
                JSONObject joField = jo.getJSONObject(ParamKey.loadTableFieldResult);

                if(vo.fields.contains(Human.K.jobModel)){
                    dFigur.setHairFigure(Utils.intValue(joField.get(Human.K.jobModel)));// 当前职业外观
                }
                if(vo.fields.contains(Human.K.currentTitleSn)){
                    dFigur.setCurrentTitle(Utils.intValue(joField.get(Human.K.currentTitleSn)));// 头衔
                }
                if(vo.fields.contains(Human.K.headSn)){
                    head.setId(Utils.intValue(joField.get(Human.K.headSn)));
                }
                if(vo.fields.contains(Human.K.currentHeadFrameSn)){
                    head.setFrameId(Utils.intValue(joField.get(Human.K.currentHeadFrameSn)));
                }
                if(vo.fields.contains(Human.K.name)){
                    joResult.put(Human.K.name, joField.getString(Human.K.name));
                }
                if(vo.fields.contains(Human.K.level)){
                    joResult.put(Human.K.level, Utils.intValue(joField.get(Human.K.level)));
                }
                if(vo.fields.contains(Human.K.combat)){
                    joResult.put(Human.K.combat, new BigDecimal(joField.getString(Human.K.combat)).longValue());
                }

                if(vo.fields.contains(Human2.K.equipFigureMap)){
                    Map<Integer,Integer> figureMap = Utils.jsonToMapIntInt(joField.getString(Human2.K.equipFigureMap));
                    //遍历weapon到face的部位不存在或者value=0就去默认取
                    for(int m = weapon; m <= face; ++m){
                        int sn = figureMap.getOrDefault(m, EquipManager.FIGURE_DEFAULT);
                        dFigur.addEquipList(HumanManager.inst().to_p_key_value(m, sn));
                    }
                }
                if(vo.fields.contains(Human2.K.wingUse)){
                    dFigur.addEquipList(HumanManager.inst().to_p_key_value(HumanManager.wing, Utils.intValue(joField.get(Human2.K.wingUse)))); // 翅膀
                }
                if(vo.fields.contains(Human2.K.hairColor)){
                    dFigur.setHairFigure(Utils.intValue(joField.get(Human2.K.hairColor)));// 发色
                }
                if(vo.fields.contains(Human2.K.mountUse)){
                    dFigur.setMountFigure(Utils.intValue(joField.get(Human2.K.mountUse)));// 坐骑
                }
                if(vo.fields.contains(Human2.K.artifactUse)){
                    dFigur.setArtifactFigure(Utils.intValue(joField.get(Human2.K.artifactUse))); // 神器sn
                }
                if(vo.fields.contains(Human2.K.currentSkin)){
                    dFigur.addSkinList(HumanManager.inst().to_p_key_value(2,Utils.intValue(joField.get(Human2.K.currentSkin))));  // 皮肤
                }
                dFigur.setGender(1);// 无性别 默认1
            }

            joResult.put("figure", dFigur.build());
            joResult.put("head", head.build());
            AsyncActionResult.success(port, handler, joResult);
        });
    }


    public static List<EntityTableSelectVo> getVoList(long humanId){
        List<EntityTableSelectVo> voList = new ArrayList<>();
        voList.add(new EntityTableSelectVo(Human.tableName, Human.getRedisKeyStr(humanId), Arrays.asList(Human.K.name, Human.K.level,
                Human.K.headSn, Human.K.jobModel, Human.K.currentTitleSn, Human.K.currentHeadFrameSn, Human.K.combat)
                , new Object[]{Human.K.id, humanId}, true));

        voList.add(new EntityTableSelectVo(Human2.tableName, ParamKey.redisSelectType_hgetall, Human2.getRedisKeyStr(humanId),
                new ArrayList<>(FieldTable.get(Human2.tableName).getFieldNames()),
                new Object[]{Human.K.id, humanId}, false));

        voList.add(new EntityTableSelectVo(Profession.tableName, Profession.getRedisKeyStr(humanId), Arrays.asList(Profession.K.jobSn,
                Profession.K.jobLv, Profession.K.jobSkillSn, Profession.K.jobSkillLv, Profession.K.passiveSkillMap),
                new Object[]{Profession.K.id, humanId}, false));

        voList.add(new EntityTableSelectVo(Artifact.tableName, Artifact.getRedisKeyStr(humanId), Arrays.asList(Artifact.K.level),
                new Object[]{Artifact.K.id, humanId}, false));

        voList.add(new EntityTableSelectVo(Mount.tableName, Mount.getRedisKeyStr(humanId), Arrays.asList(Mount.K.level),
                new Object[]{Mount.K.id, humanId}, false));

        voList.add(new EntityTableSelectVo(Relic.tableName, Relic.getRedisKeyStr(humanId), Arrays.asList(Relic.K.tabCur, Relic.K.relicMap, Relic.K.tabMap),
                new Object[]{Relic.K.id, humanId}, false));

        voList.add(new EntityTableSelectVo(UnitPropPlus.tableName, ParamKey.redisSelectType_hgetall, UnitPropPlus.getRedisKeyStr(humanId),
                new ArrayList<>(FieldTable.get(UnitPropPlus.tableName).getFieldNames()),
                new Object[]{UnitPropPlus.K.id, humanId}, false));

        return voList;
    }


    public static void p_base_fighter(long humanId, Handler<AsyncResult<Define.p_base_fighter>> handler) {
        Port port = Port.getCurrent();
        List<EntityTableSelectVo> voList = getVoList(humanId);
        MemberCallback.loadTableFieldAsync(voList, r -> {
            if (r.failed()) {
                AsyncActionResult.fail(port, handler, r.cause());
                return;
            }
            JSONArray ja = r.result();
            if(ja == null){
                Log.temp.error("===humanId={}, 查询结果为空", humanId);
                AsyncActionResult.fail(port, handler, r.cause());
                return;
            }
            Define.p_role_figure.Builder dFigur = Define.p_role_figure.newBuilder();
            dFigur.setGender(1);// 无性别 默认1

            Define.p_head.Builder head = Define.p_head.newBuilder();
            head.setUrl("");

            Define.p_base_fighter.Builder dInfo = Define.p_base_fighter.newBuilder();
            dInfo.setId(humanId);


            JSONObject joResult = new JSONObject();

            for (int i = 0; i < ja.size(); i++) {
                JSONObject jo = ja.getJSONObject(i);
                EntityTableSelectVo vo = voList.get(i);
                JSONObject joField = jo.getJSONObject(ParamKey.loadTableFieldResult);
                switch (vo.tableName){
                    case Human.tableName: {
                        if (vo.fields.contains(Human.K.jobModel)) {
                            dFigur.setHairFigure(Utils.intValue(joField.get(Human.K.jobModel)));// 当前职业外观
                        }
                        if (vo.fields.contains(Human.K.currentTitleSn)) {
                            dFigur.setCurrentTitle(Utils.intValue(joField.get(Human.K.currentTitleSn)));// 头衔
                        }
                        if (vo.fields.contains(Human.K.headSn)) {
                            head.setId(Utils.intValue(joField.get(Human.K.headSn)));
                        }
                        if (vo.fields.contains(Human.K.currentHeadFrameSn)) {
                            head.setFrameId(Utils.intValue(joField.get(Human.K.currentHeadFrameSn)));
                        }
                        if (vo.fields.contains(Human.K.name)) {
                            dInfo.setName(joField.getString(Human.K.name));
                        }
                        if (vo.fields.contains(Human.K.level)) {
                            dInfo.setLev(Utils.intValue(joField.get(Human.K.level)));
                        }
                        if (vo.fields.contains(Human.K.combat)) {
                            dInfo.setPower(new BigDecimal(joField.getString(Human.K.combat)).longValue());
                        }
                    }   break;

                    case Human2.tableName:{
                        if(vo.selectType == ParamKey.loadTableRedisType){
                            // TODO
                            jo.put(Human2.tableName, joField);
                            break;
                        }
                        if(vo.fields.contains(Human2.K.equipFigureMap)){
                            Map<Integer,Integer> figureMap = Utils.jsonToMapIntInt(joField.getString(Human2.K.equipFigureMap));
                            //遍历weapon到face的部位不存在或者value=0就去默认取
                            for(int m = weapon; m <= face; ++m){
                                int sn = figureMap.getOrDefault(m, EquipManager.FIGURE_DEFAULT);
                                dFigur.addEquipList(HumanManager.inst().to_p_key_value(m, sn));
                            }
                        }
                        if(vo.fields.contains(Human2.K.wingUse)){
                            dFigur.addEquipList(HumanManager.inst().to_p_key_value(HumanManager.wing, Utils.intValue(joField.get(Human2.K.wingUse)))); // 翅膀
                        }
                        if(vo.fields.contains(Human2.K.hairColor)){
                            dFigur.setHairFigure(Utils.intValue(joField.get(Human2.K.hairColor)));// 发色
                        }
                        if(vo.fields.contains(Human2.K.mountUse)){
                            dFigur.setMountFigure(Utils.intValue(joField.get(Human2.K.mountUse)));// 坐骑
                        }
                        if(vo.fields.contains(Human2.K.artifactUse)){
                            dFigur.setArtifactFigure(Utils.intValue(joField.get(Human2.K.artifactUse))); // 神器sn
                        }
                        if(vo.fields.contains(Human2.K.currentSkin)){
                            dFigur.addSkinList(HumanManager.inst().to_p_key_value(2, Utils.intValue(joField.get(Human2.K.currentSkin))));  // 皮肤
                        }
                    }   break;

                    case Profession.tableName:{
                        if(vo.fields.contains(Profession.K.jobSn)){
                            dInfo.setJob(Utils.intValue(joField.get(Profession.K.jobSn)));
                        }
                        if (vo.fields.contains(Profession.K.jobLv)) {
                            joResult.put(Profession.K.jobLv, Utils.intValue(joField.get(Profession.K.jobLv)));
                        }
                        if (vo.fields.contains(Profession.K.jobSkillSn)) {
                            joResult.put(Profession.K.jobSkillSn, Utils.intValue(joField.get(Profession.K.jobSkillSn)));
                        }
                        if (vo.fields.contains(Profession.K.jobSkillLv)) {
                            joResult.put(Profession.K.jobSkillLv, Utils.intValue(joField.get(Profession.K.jobSkillLv)));
                        }
                        if (vo.fields.contains(Profession.K.passiveSkillMap)){
                            joResult.put(Profession.K.passiveSkillMap, joField.getString(Profession.K.passiveSkillMap));
                        }
                    }   break;

                    case Artifact.tableName:{
                        if(vo.fields.contains(Artifact.K.level)){
                            joResult.put(Artifact.tableName+Artifact.K.level, Utils.intValue(joField.get(Artifact.K.level)));
                        }
                    }   break;
                    case Mount.tableName:{
                        if(vo.fields.contains(Mount.K.level)){
                            joResult.put(Mount.tableName+Mount.K.level, Utils.intValue(joField.get(Mount.K.level)));
                        }
                    }   break;
                    case Relic.tableName:{
                        if(vo.fields.contains(Relic.K.tabCur)){
                            joResult.put(Relic.K.tabCur, Utils.intValue(joField.get(Relic.K.tabCur)));
                        }
                        if(vo.fields.contains(Relic.K.relicMap)){
                            joResult.put(Relic.K.relicMap, joField.getString(Relic.K.relicMap));
                        }
                        if(vo.fields.contains(Relic.K.tabMap)){
                            joResult.put(Relic.K.tabMap, joField.getString(Relic.K.tabMap));
                        }
                    }   break;
                    case UnitPropPlus.tableName:{
                        jo.put(UnitPropPlus.tableName, joField);
                    }   break;
                    default:
                        Log.human.error("====查询了表格和字段 但未对查询结果做处理， tableName={}, humanId={}, fields={}", vo.tableName, humanId, vo.fields);
                        break;
                }
            }

            List<Define.p_active_skill> activeSkillList = new ArrayList<>();
            activeSkills(activeSkillList, joResult);
            List<Define.p_passive_skill> passiveSkillList = SkillManager.inst().getAllPassiveSkill(joResult);
            Define.p_role_skill.Builder dSkill = Define.p_role_skill.newBuilder();
            dSkill.addAllActiveSkill(activeSkillList);
            dSkill.addAllPassiveSkill(passiveSkillList);
            dInfo.setRoleSkill(dSkill);

            List<Define.p_role_pet> petList = PetManager.inst().getP_role_petList(joResult.getString(Human2.K.petSnLvMap), joResult.getString(Human2.K.petLineupJSON),
                    joResult.getIntValue(Human2.K.usePetLineup), joResult.getString(Human2.K.petSkinLineupJSON), joResult.getString(Human2.K.petSkinSnLvMap));
            dInfo.addAllPetList(petList);

            UnitPropPlus unitPropPlus = (UnitPropPlus)joResult.get(UnitPropPlus.tableName);
            PropCalc propCalc = HumanManager.inst().getPropPlus(unitPropPlus, new ArrayList<>());
            for (Map.Entry<Integer, BigDecimal> entry : propCalc.getDatas().entrySet()) {
                int key = entry.getKey();
                // 1001、1002、1003、1024转换成最终属性1、2、3、24
                int newKey = HumanManager.inst().getType(key);
                long value = HumanManager.inst().getCalculationValue(key, propCalc.getDatas());
                if(value == -1){
                    // 1001、1002、1003、1024转换成最终属性1、2、3、24
                    value = propCalc.getBigDecimal(newKey).longValue();
                }
                Define.p_key_value.Builder dattr = HumanManager.inst().to_p_key_value(key, value);
                dInfo.addAttrList(dattr);
            }

            List<Integer> activeSnList = new ArrayList<>();
            for(Define.p_active_skill dActive : activeSkillList){
                activeSnList.add(dActive.getSkillId());
            }
            List<Integer> petSnList = new ArrayList<>();
            for(Define.p_role_pet dPet : petList){
                petSnList.add(dPet.getPetId());
            }
            List<Define.p_attr_obj_list> p_attr_objListAll = SkillManager.inst().to_p_attr_obj_list(passiveSkillList,
                    petSnList, activeSnList, HumanManager.inst().petAttList(propCalc, petSnList));
            dInfo.addAllAttrObjList(p_attr_objListAll);
            AsyncActionResult.success(port, handler, dInfo.build());
        });
    }

    /**
     * 主动技能
     * @param activeSkillList
     * @param joResult
     */
    private static void activeSkills(List<Define.p_active_skill> activeSkillList, JSONObject joResult){
        int lineup = joResult.getIntValue(Human2.K.useSkillLineup);
        Map<Integer, List<Integer>> lineupMap = Utils.jsonToMapIntListInt(joResult.getString(Human2.K.skillLineupJSON));
        List<Integer> skillSnList = lineupMap.get(lineup);
        Map<Integer, Integer> skillSnLvMap = Utils.jsonToMapIntInt(joResult.getString(Human2.K.skillSnLvMap));

        int jobSkillSn = joResult.getIntValue(Profession.K.jobSkillSn);
        int jobSkillLv = joResult.getIntValue(Profession.K.jobSkillLv);
        Map<Integer, Map<Integer, Long>> tabDelayMap =  Utils.jsonToIntegerMapIntLong(joResult.getString(Human2.K.skillDelayJSON));
        if(tabDelayMap == null || tabDelayMap.isEmpty()){
            for(int i = 1; i <= SkillManager.lineupMax; i++){
                if(tabDelayMap.containsKey(i)){
                    continue;
                }
                Map<Integer, Long> posTimeMap = new HashMap<>();
                for(int m = 1; m <= 6; m++){
                    posTimeMap.put(m, 0L);
                }
                tabDelayMap.put(i, posTimeMap);
            }
        }
        Map<Integer, Long> posTimeMap = tabDelayMap.get(lineup);
        if(posTimeMap == null){
            posTimeMap = new HashMap<>();
            tabDelayMap.put(lineup, posTimeMap);
        }
        int delayTime = Utils.intValue(posTimeMap.get(1));
        // 主动技能
        activeSkillList.add(SkillManager.inst().to_p_active_skill(jobSkillSn, jobSkillLv, 1, delayTime).build());

        int skillPosIndex = SkillManager.inst().skillPosIndex;
        if(skillSnList != null){
            int repSn = joResult.getIntValue(Human2.K.repSn);
            for (int i = 0; i < skillSnList.size(); i++) {
                int skillSn = skillSnList.get(i);
                if (!SkillManager.inst().isOpenSkillPos(repSn, i + skillPosIndex)) {
                    continue;
                }
                int lv = Utils.intValue(skillSnLvMap.get(skillSn));
                activeSkillList.add(SkillManager.inst().to_p_active_skill(skillSn, lv, i + skillPosIndex,
                        Utils.intValue(posTimeMap.get(i + skillPosIndex))).build());
            }
        }
    }

    public static void p_battle_role(long humanId, Handler<AsyncResult<Define.p_battle_role>> handler) {
        Port port = Port.getCurrent();
        List<EntityTableSelectVo> voList = getVoList(humanId);
        MemberCallback.loadTableFieldAsync(voList, r -> {
            if (r.failed()) {
                AsyncActionResult.fail(port, handler, r.cause());
                return;
            }
            JSONArray ja = r.result();
            Define.p_role_figure.Builder dFigur = Define.p_role_figure.newBuilder();
            Define.p_head.Builder head = Define.p_head.newBuilder();
            head.setUrl("");

            JSONObject joResult = new JSONObject();

            Define.p_battle_role.Builder dInfo = Define.p_battle_role.newBuilder();

            dInfo.setId(humanId);
            dInfo.setManualOperator(Utils.getTimeSec());
            dInfo.setLeftHp(100);
            for (int i = 0; i < ja.size(); i++) {
                JSONObject jo = ja.getJSONObject(i);
                EntityTableSelectVo vo = voList.get(i);
                JSONObject joField = jo.getJSONObject(ParamKey.loadTableFieldResult);

                switch (vo.tableName){
                    case Human.tableName: {
                        if (vo.fields.contains(Human.K.jobModel)) {
                            dFigur.setHairFigure(Utils.intValue(joField.get(Human.K.jobModel)));// 当前职业外观
                        }
                        if (vo.fields.contains(Human.K.currentTitleSn)) {
                            dFigur.setCurrentTitle(Utils.intValue(joField.get(Human.K.currentTitleSn)));// 头衔
                        }
                        if (vo.fields.contains(Human.K.headSn)) {
                            head.setId(Utils.intValue(joField.get(Human.K.headSn)));
                        }
                        if (vo.fields.contains(Human.K.currentHeadFrameSn)) {
                            head.setFrameId(Utils.intValue(joField.get(Human.K.currentHeadFrameSn)));
                        }
                        if (vo.fields.contains(Human.K.name)) {
                            dInfo.setName(joField.getString(Human.K.name) == null ? "" : joField.getString(Human.K.name));
                        }
                        if (vo.fields.contains(Human.K.level)) {
                            dInfo.setLev(Utils.intValue(joField.get(Human.K.level)));
                        }
                    }   break;

                    case Human2.tableName:{
                        if(vo.fields.contains(Human2.K.equipFigureMap)){
                            Map<Integer,Integer> figureMap = Utils.jsonToMapIntInt(joField.getString(Human2.K.equipFigureMap));
                            //遍历weapon到face的部位不存在或者value=0就去默认取
                            for(int m = weapon; m <= face; ++m){
                                int sn = figureMap.getOrDefault(m, EquipManager.FIGURE_DEFAULT);
                                dFigur.addEquipList(HumanManager.inst().to_p_key_value(m, sn));
                            }
                        }
                        if(vo.fields.contains(Human2.K.wingUse)){
                            dFigur.addEquipList(HumanManager.inst().to_p_key_value(HumanManager.wing, Utils.intValue(joField.get(Human2.K.wingUse)))); // 翅膀
                        }
                        if(vo.fields.contains(Human2.K.hairColor)){
                            dFigur.setHairFigure(Utils.intValue(joField.get(Human2.K.hairColor)));// 发色
                        }
                        if(vo.fields.contains(Human2.K.mountUse)){
                            dFigur.setMountFigure(Utils.intValue(joField.get(Human2.K.mountUse)));// 坐骑
                        }
                        if(vo.fields.contains(Human2.K.artifactUse)){
                            dFigur.setArtifactFigure(Utils.intValue(joField.get(Human2.K.artifactUse))); // 神器sn
                        }
                        if(vo.fields.contains(Human2.K.currentSkin)){
                            dFigur.addSkinList(HumanManager.inst().to_p_key_value(2,Utils.intValue(joField.get(Human2.K.currentSkin))));  // 皮肤
                        }

                    }   break;

                    case Profession.tableName:{
                        if(vo.fields.contains(Profession.K.jobSn)){
                            dInfo.setJob(Utils.intValue(joField.get(Profession.K.jobSn)));
                        }
                        if (vo.fields.contains(Profession.K.jobLv)) {
                            joResult.put(Profession.K.jobLv, Utils.intValue(joField.get(Profession.K.jobLv)));
                        }
                        if (vo.fields.contains(Profession.K.jobSkillSn)) {
                            joResult.put(Profession.K.jobSkillSn, Utils.intValue(joField.get(Profession.K.jobSkillSn)));
                        }
                        if (vo.fields.contains(Profession.K.jobSkillLv)) {
                            joResult.put(Profession.K.jobSkillLv, Utils.intValue(joField.get(Profession.K.jobSkillLv)));
                        }
                        if (vo.fields.contains(Profession.K.passiveSkillMap)){
                            joResult.put(Profession.K.passiveSkillMap, joField.getString(Profession.K.passiveSkillMap));
                        }
                    }   break;

                    case Artifact.tableName:{
                        if(vo.fields.contains(Artifact.K.level)){
                            joResult.put(Artifact.tableName+Artifact.K.level, Utils.intValue(joField.get(Artifact.K.level)));
                        }
                    }   break;
                    case Mount.tableName:{
                        if(vo.fields.contains(Mount.K.level)){
                            joResult.put(Mount.tableName+Mount.K.level, Utils.intValue(joField.get(Mount.K.level)));
                        }
                    }   break;
                    case Relic.tableName:{
                        if(vo.fields.contains(Relic.K.tabCur)){
                            joResult.put(Relic.K.tabCur, Utils.intValue(joField.get(Relic.K.tabCur)));
                        }
                        if(vo.fields.contains(Relic.K.relicMap)){
                            joResult.put(Relic.K.relicMap, joField.getString(Relic.K.relicMap));
                        }
                        if(vo.fields.contains(Relic.K.tabMap)){
                            joResult.put(Relic.K.tabMap, joField.getString(Relic.K.tabMap));
                        }
                    }   break;
                    case UnitPropPlus.tableName:{
                        jo.put(UnitPropPlus.tableName, joField);
                    }   break;
                    default:
                        Log.human.error("====查询了表格和字段 但未对查询结果做处理， tableName={}, humanId={}, fields={}", vo.tableName, humanId, vo.fields);
                        break;
                }
            }
            List<Define.p_active_skill> activeSkillList = new ArrayList<>();
            activeSkills(activeSkillList, joResult);
            List<Define.p_passive_skill> passiveSkillList = SkillManager.inst().getAllPassiveSkill(joResult);
            Define.p_role_skill.Builder dSkill = Define.p_role_skill.newBuilder();
            dSkill.addAllActiveSkill(activeSkillList);
            dSkill.addAllPassiveSkill(passiveSkillList);
            dInfo.setRoleSkill(dSkill);

            List<Define.p_role_pet> petList = PetManager.inst().getP_role_petList(joResult.getString(Human2.K.petSnLvMap), joResult.getString(Human2.K.petLineupJSON),
                    joResult.getIntValue(Human2.K.usePetLineup), joResult.getString(Human2.K.petSkinLineupJSON), joResult.getString(Human2.K.petSkinSnLvMap));
            dInfo.addAllPetList(petList);

            UnitPropPlus unitPropPlus = (UnitPropPlus)joResult.get(UnitPropPlus.tableName);
            PropCalc propCalc = HumanManager.inst().getPropPlus(unitPropPlus, new ArrayList<>());
            for (Map.Entry<Integer, BigDecimal> entry : propCalc.getDatas().entrySet()) {
                int key = entry.getKey();
                // 1001、1002、1003、1024转换成最终属性1、2、3、24
                int newKey = HumanManager.inst().getType(key);
                long value = HumanManager.inst().getCalculationValue(key, propCalc.getDatas());
                if(value == -1){
                    // 1001、1002、1003、1024转换成最终属性1、2、3、24
                    value = propCalc.getBigDecimal(newKey).longValue();
                }
                Define.p_key_value.Builder dattr = HumanManager.inst().to_p_key_value(key, value);
                dInfo.addAttrList(dattr);
            }

            List<Integer> activeSnList = new ArrayList<>();
            for(Define.p_active_skill dActive : activeSkillList){
                activeSnList.add(dActive.getSkillId());
            }
            List<Integer> petSnList = new ArrayList<>();
            for(Define.p_role_pet dPet : petList){
                petSnList.add(dPet.getPetId());
            }
            List<Define.p_attr_obj_list> p_attr_objListAll = SkillManager.inst().to_p_attr_obj_list(passiveSkillList,
                    petSnList, activeSnList, HumanManager.inst().petAttList(propCalc, petSnList));
            dInfo.addAllAttrObjList(p_attr_objListAll);


            AsyncActionResult.success(port, handler, dInfo.build());
        });
    }

    private void fieldsToJSON(JSONObject jo,  EntityTableSelectVo vo, JSONObject joField){
        if(vo.selectType == ParamKey.loadTableRedisType){
            jo.put(vo.tableName, joField);
        } else {
            if(vo.fields == null){
                jo.put(vo.tableName, joField);
                return;
            }
            for(String field : vo.fields){
                jo.put(field, Utils.intValue(joField.get(field)));
            }
        }
    }
}
