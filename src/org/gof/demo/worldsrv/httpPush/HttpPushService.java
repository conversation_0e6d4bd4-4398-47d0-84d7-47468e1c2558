package org.gof.demo.worldsrv.httpPush;

import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.sqlclient.Row;
import io.vertx.sqlclient.RowSet;
import org.gof.core.Port;
import org.gof.core.dbsrv.redis.EntityManager;
import org.gof.core.gen.proxy.DistrClass;
import org.gof.core.gen.proxy.DistrMethod;
import org.gof.core.support.*;
import org.gof.demo.worldsrv.common.GamePort;
import org.gof.demo.worldsrv.common.GameServiceBase;
import org.gof.demo.worldsrv.config.ConfPush;
import org.gof.demo.worldsrv.config.ConfPushInfo;
import org.gof.demo.worldsrv.entity.Human;
import org.gof.demo.worldsrv.entity.Human2;
import org.gof.demo.worldsrv.redis.RedisKeys;
import org.gof.demo.worldsrv.support.C;
import org.gof.demo.worldsrv.support.D;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.Util;
import org.gof.demo.worldsrv.support.enumKey.LanguageTypeKey;
import org.gof.platform.ConstPf;
import org.gof.platform.http.HttpAsyncSendServiceProxy;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@DistrClass(servId = D.SERV_HTTP_PUSH, importClass = {List.class})
public class HttpPushService extends GameServiceBase {
    private TickTimer checkTime = new TickTimer(60 * Time.SEC);
    private static Map<Integer,Long> snNextPushTimeMap = new HashMap<>();
    // 服务器ID对应下一个要通知的玩家信息
    private static Map<Integer, String> serverNextPushHumanMap = new HashMap<>();

    /**
     * 构造函数
     *
     * @param port
     */
    public HttpPushService(GamePort port) {
        super(port);
    }

    @Override
    protected void init() {
//        Collection<ConfPush> confPushList = ConfPush.findAll();
//        for (ConfPush confPush : confPushList) {
//            if(confPush.pushType == HttpPushType.TIME_TYPE_1){
//                snNextPushTimeMap.put(confPush.sn, Utils.getNextTriggerTime(confPush.pushTime));
//            }
//        }
//
//        // 初始化离线玩家通知
//        initOfflinePlayerNotification();
    }

    @Override
    public void pulseOverride() {
        super.pulseOverride();
        long timeNow = Port.getTime();
        if (checkTime.isPeriod(timeNow)) {
            // TODO 先注释掉
//            checkAndPush(timeNow);
//            checkAndPushOfflinePlayers(timeNow);
        }
    }

    private void checkAndPush(long timeNow) {
        for (Map.Entry<Integer, Long> entry : snNextPushTimeMap.entrySet()){
            if (timeNow <= entry.getValue()){
                continue;
            }
            ConfPush confPush = ConfPush.get(entry.getKey());
            if(confPush == null){
                continue;
            }
            CompletableFuture.runAsync(() -> push(confPush));
            long nextPushTime = Utils.getNextTriggerTime(confPush.pushTime);
            snNextPushTimeMap.put(confPush.sn, nextPushTime);
        }
    }

    private void push(ConfPush confPush){
//        List<Integer> serverIdList = Util.getServerTagList(C.GAME_SERVER_ID);
//        int pageSize = 2000; // 每页数据量
//        int offset = 0; // 初始偏移量
//
//        String portHttpAsync = ConstPf.PORT_HTTP_ASYNC_PREFIX + new Random().nextInt(ConstPf.PORT_STARTUP_NUM_CHAT);
//        HttpAsyncSendServiceProxy proxy = HttpAsyncSendServiceProxy.newInstance(ConstPf.NODE_ID, portHttpAsync, ConstPf.SERV_HTTP_SEND);
//
//        String serverIds = "(" + String.join(",", serverIdList.stream().map(String::valueOf).collect(Collectors.toList())) + ")";
//
//        int loopNum = 1000;
//        while (true) {
//            String sql = Utils.createStr("select `{}`,`{}`,`{}` from {} where serverId in {} limit {} offset {}",
//                    Human.K.id, Human.K.account, Human.K.language, Human.tableName, serverIds, pageSize, offset);
//
//            loopNum--;
//            if(loopNum < 0){
//                Log.game.error("查询玩家数据失败太多次，退出循环");
//                break;
//            }
//            RowSet<Row> result;
//            try {
//                result = EntityManager.executeSql(sql);
//            } catch (Exception e) {
//                Log.game.error("执行SQL失败: {}", e.getMessage());
//                break; // 在出现错误时退出循环
//            }
//
//            if (result == null || result.size() == 0) {
//                break; // 没有更多数据，退出循环
//            }
//
//            // 处理当前页的数据
//            for (Row row : result) {
//                long id = row.getLong(0);
//                String account = row.getString(1);
//                int language = row.getInteger(2);
//
//                // 准备推送消息
//                Map<String, String> params = new HashMap<>();
//                params.put("app_id", Config.APP_ID);
//                params.put("ctw_id", account);
//                params.put("external_user_id", Long.toString(id));
//                params.put("language", LanguageTypeKey.getEnumByType(language).getName());
//
//                ConfPushInfo confPushInfo = ConfPushInfo.get(confPush.sn, language);
//                if(confPushInfo == null){
//                    confPushInfo = ConfPushInfo.get(confPush.sn, LanguageTypeKey.en.getType());
//                    if(confPushInfo == null){
//                        Log.game.error("ConfPushInfo is null, sn={}, language={}", confPush.sn, language);
//                        continue;
//                    }
//                }
//
//                JsonArray messagesArr = new JsonArray();
//                addMessageToArray(messagesArr, confPushInfo.text, "text");
//                addMessageToArray(messagesArr, confPushInfo.image, "image");
//                addMessageToArray(messagesArr, confPushInfo.video, "video");
//
//                params.put("messages", messagesArr.toString());
//                sendPostMsg(proxy, params);
//            }
//            offset += pageSize; // 更新偏移量，准备获取下一页数据
//        }
    }

    private void addMessageToArray(JsonArray messagesArr, String content, String contentType) {
        if (content != null && !content.isEmpty()) {
            JsonObject messageJson = new JsonObject();
            messageJson.put("content", content);
            messageJson.put("content_type", contentType);
            messagesArr.add(messageJson);
        }
    }



    private void sendPostMsg(HttpAsyncSendServiceProxy proxy, Map<String, String> params) {
        String url = Config.PUSH_URL;
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
//        headers.put("Authorization", "Basic " + getEncodedToken());
//        headers.put("x-idempotency-key", generateUUID());
        proxy.httpPushAsync(url, headers, params, false);
//        proxy.listenResult(this::_result_sendPostMsg,"params",params);
    }

    private void _result_sendPostMsg(Param results, Param context) {
        String result = results.getString();
        if (result == null || result.equals("")) {
            // 处理发送失败的逻辑
            Log.game.error("发送失败");
        } else {
            // 处理发送成功的逻辑
            Log.game.error("发送成功，返回{}", result);

        }
    }

    private String getEncodedToken() {
        // 从配置或环境变量中获取token
        String token = Config.PUSH_TOKEN;
        return token;
    }


    private void initOfflinePlayerNotification() {
        List<Integer> serverIdList = Util.getServerTagList(C.GAME_SERVER_ID);
        for (int serverId : serverIdList) {
            String redisKey = getRedisKey(serverId);
            String nextPlayerInfo = Utils.redisLRange(redisKey);
            if(nextPlayerInfo != null && !nextPlayerInfo.isEmpty()){
                serverNextPushHumanMap.put(serverId, nextPlayerInfo);
            }
        }
    }
    private String getRedisKey(int serverId){
        return Utils.createStr("{}.{}",RedisKeys.offlinePushList,serverId);
    }

    private void checkAndPushOfflinePlayers(long timeNow) {
        for (Map.Entry<Integer, String> entry : serverNextPushHumanMap.entrySet()) {
            int serverId = entry.getKey();
            String playerInfo = entry.getValue();
            
            if (playerInfo != null && !playerInfo.isEmpty()) {
                String[] info = playerInfo.split(",");
                long id = Utils.longValue(info[0]);
                long logoutTime = Utils.longValue(info[1]);
                
                if (logoutTime < timeNow - 8 * Time.HOUR) {
                    pushToPlayer(id);
                    // 通知后删除该玩家信息
                    removePlayerFromRedis(serverId, playerInfo);
                    serverNextPushHumanMap.put(serverId, getNextPlayerInfo(serverId));
                }
            }
        }
    }

    private void removePlayerFromRedis(int serverId, String playerInfo) {
        String redisKey = getRedisKey(serverId);
        Utils.removeRedisList(redisKey, playerInfo);
    }

    private String getNextPlayerInfo(int serverId) {
        String redisKey = getRedisKey(serverId);
        return Utils.redisLRange(redisKey);
    }

    private void pushToPlayer(long id) {
//        String portHttpAsync = ConstPf.PORT_HTTP_ASYNC_PREFIX + new Random().nextInt(ConstPf.PORT_STARTUP_NUM_CHAT);
//        HttpAsyncSendServiceProxy proxy = HttpAsyncSendServiceProxy.newInstance(ConstPf.NODE_ID, portHttpAsync, ConstPf.SERV_HTTP_SEND);
//
//        Human2 human = (Human2) EntityManager.getEntity(Human2.class, id);
//        if(human == null){
//            return;
//        }
//        ConfPushInfo confPushInfo = ConfPushInfo.get(HttpPushType.OFFLINE_PUSH_SN, human.getLanguage());
//        if(confPushInfo == null){
//            return;
//        }
//        Map<String, String> params = new HashMap<>();
//        params.put("app_id", Config.APP_ID);
//        params.put("ctw_id", human.getAccount());
//        params.put("external_user_id", Long.toString(id));
////        params.put("event", "game_push");
//        params.put("language", LanguageTypeKey.getEnumByType(human.getLanguage()).getName());
//        JsonArray messagesArr = new JsonArray();
//        if (confPushInfo.text != null && !confPushInfo.text.isEmpty()){
//            JsonObject contextJson = new JsonObject();
//            contextJson.put("content", confPushInfo.text);
//            contextJson.put("content_type", confPushInfo.text);
//            messagesArr.add(contextJson);
//        }
//        if (confPushInfo.image != null && !confPushInfo.image.isEmpty()) {
//            JsonObject imageJson = new JsonObject();
//            imageJson.put("content", confPushInfo.image);
//            imageJson.put("content_type", "image");
//            messagesArr.add(imageJson);
//        }
//        if (confPushInfo.video != null && !confPushInfo.video.isEmpty()) {
//            JsonObject videoJson = new JsonObject();
//            videoJson.put("content", confPushInfo.video);
//            videoJson.put("content_type", "video");
//            messagesArr.add(videoJson);
//        }
//        params.put("messages", messagesArr.toString());
//
//        sendPostMsg(proxy, params);
    }

    @DistrMethod
    public void humanLogin(long humanId, int serverID, long lastLogoutTime) {
        // 玩家登录时，从Redis中移除该玩家的离线信息
        String key = getRedisKey(serverID);
        String playerInfo = Utils.createStr("{},{}", humanId, lastLogoutTime);
        Utils.removeRedisList(key, playerInfo);
        
        // 如果是下一个要通知的玩家，更新serverNextNotifyPlayerMap
        if (serverNextPushHumanMap.get(serverID) != null &&
            serverNextPushHumanMap.get(serverID).startsWith(Long.toString(humanId))) {
            serverNextPushHumanMap.put(serverID, getNextPlayerInfo(serverID));
        }
    }

    @DistrMethod
    public void humanLogout(long humanId, int serverID, Long logoutTime) {
        // 玩家登出时，将玩家信息存入Redis
        String key = getRedisKey(serverID);
        String playerInfo = Utils.createStr("{},{}", humanId, logoutTime);
        Utils.removeRedisList(key, playerInfo);
        
        // 如果是该服务器的第一个离线玩家，更新serverNextNotifyPlayerMap
        if (serverNextPushHumanMap.get(serverID) == null || serverNextPushHumanMap.get(serverID).isEmpty()) {
            serverNextPushHumanMap.put(serverID, playerInfo);
        }
    }

    @DistrMethod
    public void setSnNextPushTimeMap(int sn, int nowAddTime){
        if(snNextPushTimeMap.get(sn) == null){
            Log.game.error("snNextPushTimeMap不存在sn={}的记录，无法再次添加", sn);
            return;
        }
        snNextPushTimeMap.put(sn, Port.getTime() + nowAddTime*Time.SEC);
    }
}