package org.gof.demo.worldsrv.family32.data;

import com.alibaba.fastjson.JSONObject;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.bridgeEntity.Family32Guild;
import org.gof.demo.worldsrv.entity.HumanBrief;
import org.gof.demo.worldsrv.family32.Family32Const;
import org.gof.demo.worldsrv.guild.GuildManager;
import org.gof.demo.worldsrv.human.HumanManager;
import org.gof.demo.worldsrv.logic.LogicUtils;
import org.gof.demo.worldsrv.logic.LogicZone;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgFamily32;
import org.gof.demo.worldsrv.support.Log;

import java.util.*;
import java.util.stream.Collectors;

public class Family32BattleFieldData {
    // 战场序号
    private final int m_battleIndex;
    // 公会信息
    private final Family32GuildData m_guildData;
    // 公会成员的战斗行装
    private final Map<Long, Integer> m_battlePlanMap;
    // 公会成员的战斗数据
    private final Map<Long, Define.p_battle_role.Builder> m_battleRoleMap;
	// 各分路的数据 <road, LinkedHashSet<humanId>>
	private final Map<Integer, LinkedHashSet<Long>> m_roadInfoMap;

	public Family32BattleFieldData(int battleIndex, Family32GuildData guildData, Define.p_family32_battle_field_info battleFieldInfo) {
        m_battleIndex = battleIndex;
        m_guildData = guildData;
        m_battlePlanMap = battleFieldInfo.getBattlePlanListList().stream()
                .collect(Collectors.toMap(
                        (Define.p_key_value::getK),
                        (v->Utils.intValue(v.getV())),
                        (existing, replacement) -> existing  // 处理重复key的情况，保留已存在的
                ));
        m_battleRoleMap = battleFieldInfo.getBattleRoleListList().stream()
                .collect(Collectors.toMap(
                        (Define.p_battle_role::getId),
                        (Define.p_battle_role::toBuilder),
                        (existing, replacement) -> existing  // 处理重复key的情况，保留已存在的
                ));
        m_roadInfoMap = battleFieldInfo.getRoadInfoListList().stream()
                .collect(Collectors.toMap(
                        (Define.p_family32_road_info::getRoad),
                        (v->new LinkedHashSet<>(v.getHumanIdListList())),
                        (existing, replacement) -> existing  // 处理重复key的情况，保留已存在的
                ));
    }

    public Define.p_family32_battle_field_info.Builder to_p_family32_battle_field_info(){
        Define.p_family32_battle_field_info.Builder info = Define.p_family32_battle_field_info.newBuilder();
        info.addAllBattlePlanList(getBattlePlanList());
        info.addAllBattleRoleList(getBattleRoleList());
        info.addAllRoadInfoList(getRoadInfoList());
        return info;
    }

    public List<Define.p_key_value> getBattlePlanList(){
        return m_battlePlanMap.entrySet().stream()
                .map(info->Define.p_key_value.newBuilder().setK(info.getKey()).setV(info.getValue()).build())
                .collect(Collectors.toList());
    }

    public List<Define.p_battle_role> getBattleRoleList(){
        return m_battleRoleMap.values().stream().map(Define.p_battle_role.Builder::build).collect(Collectors.toList());
    }

    public List<Define.p_family32_road_info> getRoadInfoList(){
        return m_roadInfoMap.entrySet().stream()
                .map(info->Define.p_family32_road_info.newBuilder().setRoad(info.getKey()).addAllHumanIdList(info.getValue()).build())
                .collect(Collectors.toList());
    }

    /**
     * 获取各分路的数据
     * @return
     */
    public Map<Integer, LinkedHashSet<Long>> getRoadInfoMap(){
        return m_roadInfoMap;
    }

    /**
     * 获取某线路的战斗数据
     * @return
     */
    public LinkedList<Define.p_battle_role.Builder> getBattleRoleList(int road){
        LinkedHashSet<Long> roadInfo = m_roadInfoMap.getOrDefault(road, new LinkedHashSet<>());
        return roadInfo.stream()
                .map(m_battleRoleMap::get)
                .collect(Collectors.toCollection(LinkedList::new));
    }

    /**
     * 获取某线路的humanBrief
     * @return
     */
    public List<HumanBrief> getHumanBriefList(int road){
        LinkedHashSet<Long> roadInfo = m_roadInfoMap.getOrDefault(road, new LinkedHashSet<>());
        return roadInfo.stream()
                .map(m_guildData::getHumanBrief)
                .collect(Collectors.toList());
    }

    @Override
    public String toString(){
        JSONObject jo = Utils.toJSONObject(super.toString());
        jo.put("guildId", m_guildData.getGuildId());
        jo.put("battlePlanMap", m_battlePlanMap);
        jo.put("battleRoleMap", m_battleRoleMap.size());
        jo.put("roadInfoMap", m_roadInfoMap);
        return jo.toJSONString();
    }

    /**
     * 所有公会成员分配到所有分路
     */
    public void addAllMembersToAllRoads(){
        // 1. 过滤已存在的玩家ID
        Set<Long> existingHumans = new HashSet<>();
        for (LinkedHashSet<Long> roadInfo : m_roadInfoMap.values()) {
            existingHumans.addAll(roadInfo);
        }
        Set<Long> newHumans = m_guildData.getMemberIdSet().stream()
                .filter(id -> !existingHumans.contains(id))
                .collect(Collectors.toSet());
        // 2. 计算各路线当前人数
        int road1Size = m_roadInfoMap.getOrDefault(Family32Const.FAMILY32_ROAD_1, new LinkedHashSet<>()).size();
        int road2Size = m_roadInfoMap.getOrDefault(Family32Const.FAMILY32_ROAD_2, new LinkedHashSet<>()).size();
        int road3Size = m_roadInfoMap.getOrDefault(Family32Const.FAMILY32_ROAD_3, new LinkedHashSet<>()).size();
        // 3. 轮询分配新玩家
        for (Long humanId : newHumans) {
            // 找出当前人数最少的路线
            int targetRoad = 1;
            int minSize = road1Size;
            if (road2Size < minSize) {
                minSize = road2Size;
                targetRoad = 2;
            }
            if (road3Size < minSize) {
                targetRoad = 3;
            }
            // 分配到对应路线并更新计数
            m_roadInfoMap.computeIfAbsent(targetRoad, k -> new LinkedHashSet<>()).add(humanId);
            switch (targetRoad) {
                case 1: road1Size++; break;
                case 2: road2Size++; break;
                case 3: road3Size++; break;
            }
        }
        // 4. 验证人数差（调试用）
        int max = Math.max(road1Size, Math.max(road2Size, road3Size));
        int min = Math.min(road1Size, Math.min(road2Size, road3Size));
        if (max - min > 1) {
            throw new IllegalStateException("分配后人数差超过1");
        }
        // 保存战场数据
        save();
    }

    /**
     * 更新所有成员的战斗数据
     */
    public void updateAllBattleRoles(){
        m_guildData.getHumanBriefVOMap().forEach((humanId,vo)->{
            int planId = m_battlePlanMap.getOrDefault(humanId, vo.getPlanId(HumanManager.PLAN_TYPE_GVG));
            try{
                Define.p_battle_role.Builder battleRole = Define.p_battle_role.parseFrom(vo.getBattleRole(planId)).toBuilder();
                m_battleRoleMap.put(humanId, battleRole);
            } catch (Exception e) {
                Log.family32.error("====解析玩家战斗数据出错，humanId={}", humanId);
            }
        });
        // 保存战场数据
        save();
    }

    /**
     * 保存战场数据
     */
    public void save(){
        Define.p_family32_battle_field_info.Builder info = to_p_family32_battle_field_info();
        Family32Guild guild = m_guildData.getGuild();
        switch(m_battleIndex){
            case Family32Const.FAMILY32_BATTLE_FIELD_1:
                guild.setBattleFieldInfo1(info.build().toByteArray());
                break;
            case Family32Const.FAMILY32_BATTLE_FIELD_2:
                guild.setBattleFieldInfo2(info.build().toByteArray());
                break;
            case Family32Const.FAMILY32_BATTLE_FIELD_3:
                guild.setBattleFieldInfo3(info.build().toByteArray());
                break;
            default:
                break;
        }
    }

    /**
     * 获取线路信息
     * @param humanId
     * @param road
     */
    public void getRoadInfo(long humanId, int road) {
        MsgFamily32.family32_road_info_s2c.Builder msg = MsgFamily32.family32_road_info_s2c.newBuilder();
        msg.setBattleIndex(m_battleIndex);
        msg.setRoad(road);
        List<Define.p_gvg_player> playerList = getPlayerList(road);
        msg.addAllPlayerList(playerList);
        LogicUtils.sendMsg(humanId, msg.build());
        Log.family32.info("===获取线路信息成功，humanId={} battleIndex={} road={} playerList={}", humanId, m_battleIndex, road, playerList.size());
    }

    public List<Define.p_gvg_player> getPlayerList(int road){
        List<Define.p_gvg_player> playerList = new LinkedList<>();
        List<HumanBrief> humanBriefList = getHumanBriefList(road);
        for (int index=0;index<humanBriefList.size();index++) {
            HumanBrief humanBrief = humanBriefList.get(index);
            Define.p_gvg_player gvgPlayer = GuildManager.inst().to_p_gvg_player(humanBrief, index);
            if(gvgPlayer != null){
                playerList.add(gvgPlayer);
            }
        }
        return playerList;
    }

    /**
     * 选择线路
     * @param humanId
     * @param road
     */
    public boolean selectRoad(long humanId, int road) {
        // 1.从分路数据中删除当前玩家
        for(LinkedHashSet<Long> roadInfo : m_roadInfoMap.values()){
            roadInfo.remove(humanId);
        }
        // 2.把该玩家加到选择的分路末尾
        m_roadInfoMap.computeIfAbsent(road, k -> new LinkedHashSet<>()).add(humanId);
        // 3.保存战场数据
        save();
        MsgFamily32.family32_select_road_s2c.Builder msg = MsgFamily32.family32_select_road_s2c.newBuilder();
        msg.setBattleIndex(m_battleIndex);
        msg.setRoad(road);
        msg.addAllPlayerList(getPlayerList(road));
        LogicUtils.sendMsg(humanId, msg.build());
        Log.family32.info("===选择线路成功，humanId={} battleIndex={} road={}", humanId, m_battleIndex, road);
        return true;
    }

    /**
     * 保存分路
     * @param humanId
     * @param road
     * @param roadInfoList
     */
    public boolean saveRoad(long humanId, int road, List<Define.p_key_value> roadInfoList) {
        LinkedHashSet<Long> newRoadInfo = roadInfoList.stream().map(Define.p_key_value::getK).collect(Collectors.toCollection(LinkedHashSet::new));
        LinkedHashSet<Long> roadInfo = m_roadInfoMap.getOrDefault(road, new LinkedHashSet<>());
        if(!new HashSet<>(roadInfo).equals(new HashSet<>(newRoadInfo))){
            Log.family32.info("===保存分路失败，分路数据已被修改 humanId={} guildId={} battleIndex={} road={} roadInfoList={}",
                    humanId, m_guildData.getGuildId(), m_battleIndex, road, roadInfoList);
            return false;
        }
        // 1.客户端传的是分路的全量数据，直接覆盖
        m_roadInfoMap.put(road, newRoadInfo);
        // 2.保存战场数据
        save();
        MsgFamily32.family32_road_change_s2c.Builder msg = MsgFamily32.family32_road_change_s2c.newBuilder();
        msg.setBattleIndex(m_battleIndex);
        msg.setRoad(road);
        msg.addAllPlayerList(getPlayerList(road));
        LogicUtils.sendMsg(humanId, msg.build());
        Log.family32.info("===保存分路成功，humanId={} battleIndex={} road={} roadInfoList={}", humanId, m_battleIndex, road, roadInfoList);
        return true;
    }

    /**
     * 切换分路
     * @param humanId
     * @param road1
     * @param road2
     */
    public boolean switchRoad(long humanId, int road1, int road2) {
        // 1.直接交换两路数据
        LinkedHashSet<Long> roadInfo1 = m_roadInfoMap.getOrDefault(road1, new LinkedHashSet<>());
        LinkedHashSet<Long> roadInfo2 = m_roadInfoMap.getOrDefault(road2, new LinkedHashSet<>());
        m_roadInfoMap.put(road1, roadInfo2);
        m_roadInfoMap.put(road2, roadInfo1);
        // 2.保存战场数据
        save();
        MsgFamily32.family32_road_change_all_s2c.Builder msg = MsgFamily32.family32_road_change_all_s2c.newBuilder();
        msg.setBattleIndex(m_battleIndex);
        m_roadInfoMap.keySet().forEach(road->{
            Define.p_gvg_road_info.Builder roadInfo = Define.p_gvg_road_info.newBuilder();
            roadInfo.setRoad(road);
            List<Define.p_gvg_player> playerList = getPlayerList(road);
            roadInfo.setNum(playerList.size());
            roadInfo.addAllPlayerList(playerList);
            msg.addRoadInfoList(roadInfo);
            if(playerList.stream().anyMatch(v->v.getRoleId() == humanId)){
                // 路线变化后，重新发送给自己一次选择路线
                MsgFamily32.family32_select_road_s2c.Builder msgSelect = MsgFamily32.family32_select_road_s2c.newBuilder();
                msgSelect.setBattleIndex(m_battleIndex);
                msgSelect.setRoad(road);
                msgSelect.addAllPlayerList(playerList);
                LogicUtils.sendMsg(humanId, msgSelect.build());
            }
        });
        LogicUtils.sendMsg(humanId, msg.build());
        Log.family32.info("===切换分路成功，humanId={} battleIndex={} roads=[{},{}]", humanId, m_battleIndex, road1, road2);
        return true;
    }

}