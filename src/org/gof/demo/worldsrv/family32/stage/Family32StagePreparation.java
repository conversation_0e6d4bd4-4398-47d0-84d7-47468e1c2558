package org.gof.demo.worldsrv.family32.stage;

import io.vertx.core.AsyncResult;
import io.vertx.core.Handler;
import org.gof.core.Port;
import org.gof.core.RecordTransient;
import org.gof.core.db.DBKey;
import org.gof.core.dbsrv.DB;
import org.gof.core.dbsrv.redis.AsyncActionResult;
import org.gof.core.dbsrv.redis.CrossRedis;
import org.gof.core.dbsrv.redis.EntityManager;
import org.gof.core.dbsrv.redis.RedisTools;
import org.gof.core.support.Config;
import org.gof.core.support.Param;
import org.gof.core.support.ParamKey;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.bridgeEntity.Family32Guild;
import org.gof.demo.worldsrv.config.ConfFamiliyBrawl;
import org.gof.demo.worldsrv.config.ConfFamily32Stage;
import org.gof.demo.worldsrv.config.ConfGlobal;
import org.gof.demo.worldsrv.entity.Guild;
import org.gof.demo.worldsrv.family32.Family32Const;
import org.gof.demo.worldsrv.family32.vo.Family32GuildVO;
import org.gof.demo.worldsrv.family32.Family32Utils;
import org.gof.demo.worldsrv.family32.Family32Zone;
import org.gof.demo.worldsrv.family32.data.Family32GuildData;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.enumKey.ConfGlobalKey;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

public class Family32StagePreparation extends Family32Stage {
    // 原始的32强公会集合
    private final Set<Long> m_originalTop32Set = new HashSet<>();

	public Family32StagePreparation(Family32Zone zone, ConfFamily32Stage conf) {
		super(zone, conf);
	}

	@Override
	public void onEnd(Handler<AsyncResult<Void>> onComplete) {
        // 获取32强的信息
        Family32Utils.getTop32Info(getZoneId(), res->{
            if (res.failed()) {
                AsyncActionResult.fail(port, onComplete, res.cause());
                return;
            }
            LinkedHashSet<Long> guildSet = new LinkedHashSet<>(res.result());
            int guildNum = guildSet.size();
            if(guildNum > 0){
                if(guildNum == Family32Const.FAMILY32_BATTLE_FLAG_32){
                    Log.family32.info("==={} [{}][{}]入选公会数据正常 guildNum={}", getZoneName(), m_conf.Locname, m_conf.sn, guildNum);
                    // 初始化32强公会
                    initTop32Guild(guildSet.iterator(), onComplete);
                    return;
                }
                Log.family32.error("=={} [{}][{}]入选公会数据异常 guildNum={} != {}",
                        getZoneName(), m_conf.Locname, m_conf.sn, guildNum, Family32Const.FAMILY32_BATTLE_FLAG_32);
                AsyncActionResult.fail(port, onComplete, new Exception("入选公会数据异常"));
                return;
            }
            // 公会数为0，说明未生成入选32强的公会名单，则开始生成
            generateTop32Info(res2->{
                if (res2.failed()) {
                    AsyncActionResult.fail(port, onComplete, res2.cause());
                    return;
                }
                LinkedHashSet<Long> top32GuildSet = res2.result();
                if(top32GuildSet.size() != Family32Const.FAMILY32_BATTLE_FLAG_32){
                    Log.family32.error("{} top32 guild set has incorrect size = {}", getZoneName(), top32GuildSet.size());
                    AsyncActionResult.fail(port, onComplete, new Exception("32强公会不足"));
                    return;
                }
                Log.family32.info("==={} [{}][{}]生成入选公会 top32GuildSet={}", getZoneName(), m_conf.Locname, m_conf.sn, top32GuildSet);
                List<Long> guildIdList = new ArrayList<>(top32GuildSet);
                // 保存入选的32强名单
                saveTop32Guild(guildIdList, res3->{
                    if (res3.failed()) {
                        AsyncActionResult.fail(port, onComplete, res3.cause());
                        return;
                    }
                    // 发送资格取消和顺延邮件
                    sendCancelMail(top32GuildSet);
                    // 初始化32强公会
                    initTop32Guild(guildIdList.iterator(), onComplete);
                });
            });
        });
	}

    /**
     * 生成32强的信息
     * @param onComplete
     */
    public void generateTop32Info(Handler<AsyncResult<LinkedHashSet<Long>>> onComplete){
        Port port = Port.getCurrent();
        m_originalTop32Set.clear();
        if(!Config.DATA_DEBUG){
            String whereSql = Utils.createStr(" WHERE removeTime = 0 ORDER BY `{}` DESC LIMIT {}", Guild.K.level, 100);
            DB db = DB.newInstance(Guild.tableName);
            List<String> coList = new ArrayList<>();
            coList.add(Guild.K.id);
            coList.add(Guild.K.name);
            db.findByQuery(false, whereSql, DBKey.COLUMN, coList);
            db.listenResult((results, context) -> {
                List<RecordTransient> list = results.get();
                if (list == null || list.isEmpty()) {
                    Log.family32.info("sqlRank: 没有找到数据, {}", context);
                    AsyncActionResult.fail(port, onComplete, new Exception("没有找到数据"));
                    return;
                }
                LinkedHashSet<Long> allGuildSet = new LinkedHashSet<>();
                for(RecordTransient record : list){
                    long humanId = record.get(Guild.K.id);
                    if(humanId > 0){
                        allGuildSet.add(humanId);
                    }
                }
                int topN = 32;
                LinkedHashSet<Long> topNSet = new LinkedHashSet<>();
                // 添加原始的32强公会
                m_originalTop32Set.addAll(new ArrayList<>(allGuildSet).subList(0, Math.min(topN, allGuildSet.size())));
                getGuildInfoSerial(allGuildSet.iterator(), topN, topNSet, onComplete);
            });
            return;
        }
        Map<Integer, LinkedHashSet<Long>> top32GuildMap = new HashMap<>();
        Map<Integer, Integer> top32GroupInfo = m_zone.getTop32GroupInfo();
        AtomicInteger loadNum = new AtomicInteger(0);
        top32GroupInfo.forEach((groupId,topN)->{
            loadNum.incrementAndGet();
            getTopNSetFromGroup(groupId, topN, res->{
                if (res.failed()) {
                    AsyncActionResult.fail(port, onComplete, res.cause());
                    return;
                }
                LinkedHashSet<Long> topNSet = res.result();
                Log.family32.info("==={} 生成乱斗分组={}的前{}名公会，topNSet={}", getZoneName(), groupId, topN, topNSet);
                top32GuildMap.put(groupId, topNSet);
                loadNum.decrementAndGet();
                if(loadNum.get() <= 0){
                    // 按照乱斗分组，给32强公会排序
                    LinkedHashSet<Long> top32GuildSet = Family32Utils.sortTop32Guild(top32GuildMap);
                    AsyncActionResult.success(port, onComplete, top32GuildSet);
                }
            });
        });
    }

    /**
     * 获取乱斗分组的前N名公会
     * @param groupId
     * @param topN
     * @param onComplete
     */
    public void getTopNSetFromGroup(int groupId, int topN, Handler<AsyncResult<LinkedHashSet<Long>>> onComplete){
        Port port = Port.getCurrent();
        String redisKey = Family32Utils.getGvgSeasonRankKey(Family32Utils.getCurrentSeason(), groupId);
        int maxNum = GlobalConfVal.getLeagueRankMaxNum(ConfFamiliyBrawl.get(ParamKey.familiyBrawlSn).family_rank_cross_reward);
        CrossRedis.getRankListByIndex(redisKey, 0, maxNum, false, res -> {
            if (res.failed()) {
                Log.family32.warn("===获取乱斗排行数据失败， redisKey={}", redisKey);
                AsyncActionResult.fail(port, onComplete, new Exception("获取乱斗排行数据失败"));
                return;
            }
            List<String> idList = res.result().getList();
            List<Long> guildIdList = idList.stream().map(Utils::longValue).collect(Collectors.toList());
            LinkedHashSet<Long> topNSet = new LinkedHashSet<>();
            getGuildInfoSerial(guildIdList.iterator(), topN, topNSet, res2->{
                if (res2.failed()) {
                    AsyncActionResult.fail(port, onComplete, res2.cause());
                    return;
                }
                // 添加原始的32强公会
                m_originalTop32Set.addAll(guildIdList.subList(0, Math.min(topN, guildIdList.size())));
                AsyncActionResult.success(port, onComplete, topNSet);
            });
        });
    }

    public void getGuildInfoSerial(Iterator<Long> iterator, int topN, LinkedHashSet<Long> topNSet, Handler<AsyncResult<LinkedHashSet<Long>>> onComplete) {
        Port port = Port.getCurrent();
        if(!iterator.hasNext() || topNSet.size() == topN){
            AsyncActionResult.success(port, onComplete, topNSet);
            return;
        }
        Long guildId = iterator.next();
        Family32Utils.fetchGuildInfoFromGameServer(guildId, res -> {
            if (res.failed()) {
                if(Config.DATA_DEBUG){
                    // dev找不到服务器，直接跳过
                    getGuildInfoSerial(iterator, topN, topNSet, onComplete); // 递归处理下一个
                    return;
                }
                AsyncActionResult.fail(port, onComplete, res.cause());
                return;
            }
            Family32GuildVO guildVO = res.result();
            ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.family32_signup_condition);
            int minJoinMemberNum = confGlobal != null ? confGlobal.intArray[0] : 20;
            if(guildVO.memberIds.size() < minJoinMemberNum){
                // 公会人数不足，顺延取排行榜下一个公会
                Log.family32.warn("==={} 公会[{},{}] 人数={}不足{}人，无法参与乱斗32强",
                        getZoneName(), guildVO.guildName, guildId, guildVO.memberIds.size(), minJoinMemberNum);
            }
            else{
                topNSet.add(guildVO.guildId);
//                Log.family32.info("==={} 公会[{},{}] 人数={}，入选乱斗32强", getZoneName(), guildVO.guildName, guildVO.guildId, guildVO.memberIds.size());
            }
            getGuildInfoSerial(iterator, topN, topNSet, onComplete); // 递归处理下一个
        });
    }

    /**
     * 保存入选的32强名单
     * @param guildIdList
     * @param onComplete
     */
    public void saveTop32Guild(List<Long> guildIdList, Handler<AsyncResult<Void>> onComplete){
        String redisKey = Family32Utils.getQualifyingRankKey(getZoneId());
        AtomicInteger loadNum = new AtomicInteger(0);
        for(int index = 0; index < guildIdList.size(); index++){
            long guildId = guildIdList.get(index);
            // score越大，排名越高，用100-排名当score
            loadNum.incrementAndGet();
            RedisTools.addRank(EntityManager.redisClient, redisKey, guildId, 100-index, res3->{
                if(res3.failed()){
                    AsyncActionResult.fail(port, onComplete, res3.cause());
                    return;
                }
                RedisTools.expire(EntityManager.redisClient, redisKey, Family32Const.FAMILY32_REDIS_KEY_EXPIRE_TIME);
                loadNum.decrementAndGet();
                if(loadNum.get() <= 0){
                    Log.family32.info("{} 保存入选的32强名单完成!", getZoneName());
                    AsyncActionResult.success(port, onComplete, null);
                }
            });
        }
    }

    /**
     * 发送取消资格和顺延邮件
     * @param top32GuildSet
     */
    public void sendCancelMail(LinkedHashSet<Long> top32GuildSet) {
        Set<Long> notifyCancelSet = new HashSet<>(m_originalTop32Set);
        notifyCancelSet.removeAll(top32GuildSet);
        Set<Long> notifyReplacementSet = new HashSet<>(top32GuildSet);
        notifyReplacementSet.removeAll(m_originalTop32Set);
        for(long guildId : notifyCancelSet){
            Family32Utils.sendMailToGuild(guildId, Family32Const.FAMILY32_CANCEL_GUILD_MAIL_SN, "", "", "", new Param());
        }
        for(long guildId : notifyReplacementSet){
            Family32Utils.sendMailToGuild(guildId, Family32Const.FAMILY32_REPLACEMENT_GUILD_MAIL_SN, "", "", "", new Param());
        }
        Log.family32.info("==={} 发送取消资格和顺延邮件 m_originalTop32Set={} notifyCancelSet={} notifyReplacementSet={}",
                getZoneName(), m_originalTop32Set.size(), notifyCancelSet, notifyReplacementSet);
    }

    /**
     * 初始化32强公会
     * @param iterator
     * @param onComplete
     */
    private void initTop32Guild(Iterator<Long> iterator, Handler<AsyncResult<Void>> onComplete) {
        if (!iterator.hasNext()) {
            Log.family32.info("==={} 初始化完成 guildNum={}", getZoneName(), m_zone.getAllGuild().size());
            AsyncActionResult.success(port, onComplete, null);
            return;
        }
        Long guildId = iterator.next();
        initGuild(guildId, res -> {
            if (res.failed()) {
                AsyncActionResult.fail(port, onComplete, res.cause());
                return;
            }
            initTop32Guild(iterator, onComplete); // 递归处理下一个
        });
    }

    /**
     * 初始化公会
     * @param guildId
     * @param onComplete
     */
    public void initGuild(long guildId, Handler<AsyncResult<Void>> onComplete) {
        Family32Utils.fetchGuildInfoFromGameServer(guildId, res -> {
            if (res.failed()) {
                AsyncActionResult.fail(port, onComplete, res.cause());
                return;
            }
            Family32GuildVO guildVO = res.result();
            Family32GuildData guildData = m_zone.getGuild(guildId);
            if(guildData == null){
                Family32Guild guild = new Family32Guild();
                long id = Port.applyId();
                guild.setId(id);
                guild.setSeasonName(m_zone.getCurrentSeasonName());
                guild.setGuildId(guildVO.guildId);
                // 只在初始化时设定本次参赛的成员
                guild.setMemberIdList(Utils.arrayLongToStr(guildVO.memberIds));
                guild.persist();
                guildData = new Family32GuildData(m_zone, guild);
                // 添加公会
                m_zone.addGuild(guildData);
                Log.family32.info("===创建公会，guildData={}", guildData);
            }
            guildData.updateGuildInfo(guildVO, onComplete);
        });
    }

}

