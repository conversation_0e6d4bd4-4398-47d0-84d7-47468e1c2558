package org.gof.demo.worldsrv.family32.stage;

import io.vertx.core.AsyncResult;
import io.vertx.core.Handler;
import org.gof.core.Port;
import org.gof.core.dbsrv.redis.AsyncActionResult;
import org.gof.core.support.TickTimer;
import org.gof.core.support.Time;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.bridgeEntity.Family32Bet;
import org.gof.demo.worldsrv.bridgeEntity.Family32Competition;
import org.gof.demo.worldsrv.config.ConfFamily32Stage;
import org.gof.demo.worldsrv.family32.Family32Const;
import org.gof.demo.worldsrv.family32.vo.Family32GuildVO;
import org.gof.demo.worldsrv.family32.Family32Utils;
import org.gof.demo.worldsrv.family32.Family32Zone;
import org.gof.demo.worldsrv.family32.data.Family32CompetitionData;
import org.gof.demo.worldsrv.family32.data.Family32GuildData;
import org.gof.demo.worldsrv.support.Log;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

public abstract class Family32StageCompetition extends Family32Stage {
	// 竞猜开始时间戳
	protected long m_betStartTime = 0;
	// 竞猜结束时间戳
	protected long m_betEndTime = 0;
	// 是否广播竞猜结束
	private boolean m_isBroadcastBetEnd = false;
    // 上阵期开始时间戳
    protected long m_joinStartTime = 0;
    // 调整期开始时间戳
    protected long m_adjustStartTime = 0;
    // 等待期开始时间戳
    protected long m_waitStartTime = 0;
    // 调整期开始计时器
    protected final TickTimer m_ttAdjust = new TickTimer();
    // 调整期逻辑是否完成
    private boolean m_isAdjustDone = false;
    // 等待期开始计时器
    protected final TickTimer m_ttWait = new TickTimer();
    // 等待期逻辑是否完成
    private boolean m_isWaitDone = false;
    // 战斗开始时间戳
    protected long m_battleStartTime = 0;
    // 当前轮次战斗计时器
    protected final TickTimer m_ttCurBattle = new TickTimer();
    // 战斗轮次
    protected int m_battleRound = 0;
    // 分路战斗计时器
    protected final TickTimer m_ttRoadBattle = new TickTimer();
    // 战斗期逻辑是否完成
    private boolean m_isBattleDone = false;
    // 结算期开始时间戳
    protected long m_settleStartTime = 0;
    // 结算期开始计时器
    protected final TickTimer m_ttSettle = new TickTimer();

	public Family32StageCompetition(Family32Zone zone, ConfFamily32Stage conf) {
		super(zone, conf);
	}

	/**
	 * 初始化
	 * @param timeOpen
	 */
	@Override
	public void init(long timeOpen) {
		super.init(timeOpen);
		if(m_conf.bet_start != null && m_conf.bet_start.length == 3) {
			m_betStartTime = timeOpen + Time.HOUR * m_conf.bet_start[0] + Time.MIN * m_conf.bet_start[1] + Time.SEC * m_conf.bet_start[2];
		}
		if(m_conf.bet_end != null && m_conf.bet_end.length == 3) {
			m_betEndTime = timeOpen + Time.HOUR * m_conf.bet_end[0] + Time.MIN * m_conf.bet_end[1] + Time.SEC * m_conf.bet_end[2];
		}
        if(m_conf.join_start != null && m_conf.join_start.length == 3) {
            m_joinStartTime = timeOpen + Time.HOUR * m_conf.join_start[0] + Time.MIN * m_conf.join_start[1] + Time.SEC * m_conf.join_start[2];
        }
        if(m_conf.adjust_start != null && m_conf.adjust_start.length == 3) {
            m_adjustStartTime = timeOpen + Time.HOUR * m_conf.adjust_start[0] + Time.MIN * m_conf.adjust_start[1] + Time.SEC * m_conf.adjust_start[2];
            // 调整期开始计时器
            m_ttAdjust.start(m_adjustStartTime, Family32Const.FAMILY32_STEP_RETRY_INTERVAL, true);
        }
        if(m_conf.await_start != null && m_conf.await_start.length == 3) {
            m_waitStartTime = timeOpen + Time.HOUR * m_conf.await_start[0] + Time.MIN * m_conf.await_start[1] + Time.SEC * m_conf.await_start[2];
            // 等待期开始计时器
            m_ttWait.start(m_waitStartTime, Family32Const.FAMILY32_STEP_RETRY_INTERVAL, true);
        }
        if(m_conf.battle_start != null && m_conf.battle_start.length == 3) {
            m_battleStartTime = timeOpen + Time.HOUR * m_conf.battle_start[0] + Time.MIN * m_conf.battle_start[1] + Time.SEC * m_conf.battle_start[2];
            m_ttCurBattle.start(m_battleStartTime, Time.DAY, true);
        }
        if(m_conf.settle_start != null && m_conf.settle_start.length == 3) {
            m_settleStartTime = timeOpen + Time.HOUR * m_conf.settle_start[0] + Time.MIN * m_conf.settle_start[1] + Time.SEC * m_conf.settle_start[2];
            // 结算期开始计时器
            m_ttSettle.start(m_settleStartTime, Family32Const.FAMILY32_STEP_RETRY_INTERVAL, true);
        }
	}

	/**
	 * 获取战斗最大轮次
	 * @return
	 */
	abstract public int getMaxBattleRound();

	@Override
	public long getBetEndTime() {
		return m_betEndTime;
	}

	/**
	 * 是否可以竞猜
	 * @return
	 */
	@Override
	public boolean canBetCompetition(){
		long timeNow = Port.getTime();
		// 比赛阶段，竞猜时间必须配置才可竞猜
		return timeNow >= m_betStartTime && timeNow < m_betEndTime;
	}

    /**
     * 是否在上阵期
     * @return
     */
    @Override
    public boolean isInJoinPeriod(){
        long timeNow = Port.getTime();
        return timeNow >= m_joinStartTime && timeNow < m_adjustStartTime;
    }

    /**
     * 是否在调整期
     * @return
     */
    @Override
    public boolean isInAdjustPeriod(){
        long timeNow = Port.getTime();
        return timeNow >= m_adjustStartTime && timeNow < m_waitStartTime;
    }

    /**
     * 能否观战
     * @return
     */
    @Override
    public boolean canWatchBattle(){
        long timeNow = Port.getTime();
        // 比赛阶段，比赛开始时间未配置代表无限制，都可以观战
        return m_battleStartTime == 0 || timeNow >= m_battleStartTime;
    }

    /**
     * 是否在结算期
     * @return
     */
    @Override
    public boolean isInSettlePeriod(){
        long timeNow = Port.getTime();
        // 比赛阶段，结算时间未配置代表无限制，都处在结算期
        return m_settleStartTime == 0 || timeNow >= m_settleStartTime;
    }

	@Override
	public String toString() {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		return Utils.createStr("{} [{}][{}] m_ttBetStartTime={} m_ttBetEndTime={} m_joinStartTime={} m_adjustStartTime={} " +
                        "m_waitStartTime={} m_settleStartTime={} m_battleRound={} m_ttCurBattle={}",
				super.toString(), m_conf.Locdesc, m_conf.battle_flag, sdf.format(new Date(m_betStartTime)), sdf.format(new Date(m_betEndTime)),
                sdf.format(new Date(m_joinStartTime)), sdf.format(new Date(m_adjustStartTime)), sdf.format(new Date(m_waitStartTime)),
                sdf.format(new Date(m_settleStartTime)), m_battleRound, m_ttCurBattle);
	}

	@Override
	public void onStart(Handler<AsyncResult<Void>> onComplete) {
		// 初始化比赛对阵数据
		initCompetitionSchedule(onComplete);
	}

	/**
	 * 初始化比赛对阵数据
	 * @return
	 */
	protected void initCompetitionSchedule(Handler<AsyncResult<Void>> onComplete){
		int battleFlag = m_conf.battle_flag;
		LinkedHashSet<Family32CompetitionData> competitionPool = m_zone.getCompetitionPool(battleFlag);
		int competitionNum = competitionPool.size();
		// 获取比赛对阵总场次
		int totalNum = getCompetitionScheduleTotalNum();
		if(competitionNum > 0){
			if(competitionNum == totalNum){
				Log.family32.info("==={} [{}][{}]比赛数据正常 battleFlag={} competitionNum={}",
						getZoneName(), m_conf.Locname, m_conf.sn, battleFlag, competitionNum);
				AsyncActionResult.success(port, onComplete, null);
				return;
			}
			Log.family32.error("=={} [{}][{}]比赛数据异常 battleFlag={} competitionNum={} != totalNum={}",
					getZoneName(), m_conf.Locname, m_conf.sn, battleFlag, competitionNum, totalNum);
			AsyncActionResult.fail(port, onComplete, new Exception("比赛数据异常"));
			return;
		}
		// 比赛场次为0，说明未生成比赛对阵，则开始生成
		// 获取本阶段的所有队伍数据
		getAllGuildInfo(res->{
			if (res.failed()) {
				AsyncActionResult.fail(port, onComplete, res.cause());
				return;
			}
			LinkedHashSet<Long> allGuildInfo = res.result();
			// 生成循环赛对阵表
			LinkedHashSet<Family32Competition> generateSet = generateCompetitionSchedule(allGuildInfo);
			int generateNum = generateSet.size();
			if(generateNum != totalNum){
				Log.family32.error("==={} [{}][{}]生成比赛对阵表失败! generateNum={} != totalNum={}",
						getZoneName(), m_conf.Locname, m_conf.sn, generateNum, totalNum);
				AsyncActionResult.fail(port, onComplete, new Exception("生成比赛对阵表失败"));
				return;
			}
			for(Family32Competition comp : generateSet){
				comp.persist();
				// 创建 Family32CompetitionData 并存储到 service 中
				Family32CompetitionData compData = new Family32CompetitionData(m_zone, comp);
				m_zone.addCompetition(compData);
			}
			Log.family32.info("==={} [{}][{}]生成比赛对阵表成功! generateNum={}", getZoneName(), m_conf.Locname, m_conf.sn, generateNum);
			AsyncActionResult.success(port, onComplete, null);
		});
	}

	/**
	 * 获取比赛对阵总场次
	 * @return
	 */
	abstract protected int getCompetitionScheduleTotalNum();

	/**
	 * 获取本阶段的所有公会数据
	 * @param onComplete
	 */
	abstract protected void getAllGuildInfo(Handler<AsyncResult<LinkedHashSet<Long>>> onComplete);

	/**
	 * 公共的回调处理函数，处理获取到的 Top 信息
	 * @param guildSet
	 * @param onComplete
	 */
	protected void handleTopInfoResponse(LinkedHashSet<Long> guildSet, Handler<AsyncResult<LinkedHashSet<Long>>> onComplete) {
		if (guildSet.size() != m_conf.battle_flag) {
			Log.family32.error("guild set has incorrect size = {}", guildSet.size());
			AsyncActionResult.fail(port, onComplete, new Exception(Utils.createStr("{}强公会不足", m_conf.battle_flag)));
			return;
		}
		AsyncActionResult.success(port, onComplete, guildSet);
	}

	/**
	 * 生成比赛对阵数据
	 * @param allGuildInfo
	 */
	abstract protected LinkedHashSet<Family32Competition> generateCompetitionSchedule(LinkedHashSet<Long> allGuildInfo);

	/**
	 * 逻辑tick
	 * @return
	 */
	@Override
	public void tickLogic() {
		if(!m_isLogicDataReady){
			return;
		}
		long timeNow = Port.getTime();
		if(!m_isBroadcastBetEnd && m_betEndTime > 0 && timeNow > m_betEndTime){
			m_isBroadcastBetEnd = true;
			// 竞猜结束，广播阶段变化
			m_zone.broadcastStageChange();
		}
		if(m_conf.battle_start == null) {
			// 没战斗，设置本阶段逻辑已全部执行
			m_logicFullyExecuted = true;
			return;
		}
        if (m_ttAdjust.isOnce(timeNow)) {
            adjustStart();
        }
        if (m_isAdjustDone && m_ttWait.isOnce(timeNow)) {
            waitStart();
        }
		if (m_isWaitDone && m_ttCurBattle.isOnce(timeNow)) {
			if(m_battleRound > 0){
				// 本轮战斗结束
				battleEnd();
			}
			if(m_battleRound++ >= getMaxBattleRound()) {
				stopBattleTickTimer();
                allBattleEnd();
				return;
			}
			// 下轮战斗开始
			battleStart();
		}
        if(m_ttRoadBattle.isPeriod(timeNow, true)){
            // 进行分路战斗
            onRoadBattle();
        }
        if (m_isBattleDone && m_ttSettle.isOnce(timeNow)) {
            settleStart();
        }
	}

    /**
     * 获取本阶段比赛所有的公会
     * @return
     */
    public Set<Long> getAllGuilds(){
        int battleFlag = getBattleFlag();
        LinkedHashSet<Family32CompetitionData> competitionPool = m_zone.getCompetitionPool(battleFlag);
        Set<Long> allGuilds = new HashSet<>();
        for(Family32CompetitionData compData : competitionPool){
            long guildId1 = compData.getGuildId1();
            if(guildId1 > 0){
                allGuilds.add(guildId1);
            }
            long guildId2 = compData.getGuildId2();
            if(guildId2 > 0){
                allGuilds.add(guildId2);
            }
        }
        return allGuilds;
    }

    /**
     * 调整期开始
     */
    public void adjustStart() {
        Log.logic.info("==={} 阶段[{}][{}]调整期开始!!!", getZoneName(), getStageName(), getStageSN());
        Set<Long> allGuilds = getAllGuilds();
        if(allGuilds.isEmpty()){
            return;
        }
        for(long guildId : allGuilds){
            Family32GuildData guildData = m_zone.getGuild(guildId);
            if(guildData != null){
                // 把所有公会成员分配到所有战场
                guildData.addAllMembersToAllBattleFields();
            }
        }
        // 调整期逻辑完成
        m_isAdjustDone = true;
    }

    /**
     * 等待期开始
     */
    public void waitStart(){
        Log.logic.info("==={} 阶段[{}][{}]等待期开始!!!", getZoneName(), getStageName(), getStageSN());
        loadBattleData(res->{
            if(res.failed()){
                Log.logic.error("==={} 阶段[{}][{}]加载战斗数据失败!!! e={}", getZoneName(), getStageName(), getStageSN(), res.cause());
                m_ttWait.reStart();
                return;
            }
            // 加载战斗数据成功，等待期逻辑完成
            m_isWaitDone = true;
            Log.logic.info("==={} 阶段[{}][{}]加载战斗数据成功!!!", getZoneName(), getStageName(), getStageSN());
        });
    }

    /**
     * 加载战斗数据
     * @param onComplete
     */
    public void loadBattleData(Handler<AsyncResult<Void>> onComplete){
        Set<Long> allGuilds = getAllGuilds();
        if(allGuilds.isEmpty()){
            AsyncActionResult.success(port, onComplete, null);
            return;
        }
        AtomicInteger loadNum = new AtomicInteger(0);
        for(long guildId : allGuilds){
            loadNum.incrementAndGet();
            updateGuildInfo(guildId, res->{
                if(res.failed()){
                    AsyncActionResult.fail(port, onComplete, res.cause());
                    return;
                }
                loadNum.decrementAndGet();
                if(loadNum.get() <= 0){
                    AsyncActionResult.success(port, onComplete, null);
                }
            });
        }
    }

    /**
     * 更新公会信息
     * @param guildId
     * @param onComplete
     */
    public void updateGuildInfo(long guildId, Handler<AsyncResult<Void>> onComplete) {
        Family32Utils.fetchGuildInfoFromGameServer(guildId, res -> {
            if (res.failed()) {
                AsyncActionResult.fail(port, onComplete, res.cause());
                return;
            }
            Family32GuildVO guildVO = res.result();
            Family32GuildData guildData = m_zone.getGuild(guildId);
            if(guildData == null){
                Log.family32.info("===更新公会信息失败，公会不存在，guildId={}", guildId);
                AsyncActionResult.fail(port, onComplete, new Exception("更新公会信息失败，公会不存在"));
                return;
            }
            guildData.updateGuildInfo(guildVO, onComplete);
        });
    }

	/**
	 * 战斗开始
	 */
	public void battleStart() {
		Log.family32.info("{} 战斗[{}][{}]第{}轮开始!!!", getZoneName(), m_conf.Locname, m_conf.sn, m_battleRound);
        m_ttRoadBattle.start(Family32Const.FAMILY32_ROAD_BATTLE_INTERVAL, true);
	}

    /**
     * 进行分路战斗
     */
    public void onRoadBattle() {
        Log.family32.info("{} 战斗[{}][{}]第{}轮 进行分路战斗!!!", getZoneName(), m_conf.Locname, m_conf.sn, m_battleRound);
    }

	/**
	 * 战斗结束
	 */
	public void battleEnd() {
		Log.family32.info("{} 战斗[{}][{}]第{}轮结束!!!", getZoneName(), m_conf.Locname, m_conf.sn, m_battleRound);
	}

    /**
     * 获取下一场战斗的时间戳
     * @return
     */
    @Override
    public long getNextBattleTime(){
        return m_ttRoadBattle.isStarted() ? m_ttRoadBattle.getNextTime() : 0;
    }

	/**
	 * 停止战斗计时器
	 */
	private void stopBattleTickTimer() {
		m_battleRound = 0;
		m_ttCurBattle.stop();
	}

    /**
     * 所有战斗结束
     */
    public void allBattleEnd() {
        int battleFlag = getBattleFlag();
        LinkedHashSet<Family32CompetitionData> competitionPool = m_zone.getCompetitionPool(battleFlag);
        int battleNotDoneNum = 0;
        for(Family32CompetitionData compData : competitionPool){
            if(!compData.isBattleAlreadyDone()){
                battleNotDoneNum++;
            }
        }
        if(battleNotDoneNum > 0){
            Log.family32.info("{} [{}][{}]有战斗={}场未结束!!!", getZoneName(), m_conf.Locname, m_conf.sn, battleNotDoneNum);
            return;
        }
        Log.family32.info("{} 所有战斗[{}][{}]结束!!!", getZoneName(), m_conf.Locname, m_conf.sn);
        // 战斗期逻辑完成
        m_isBattleDone = true;
    }

	/**
	 * 结算期开始
	 */
	public void settleStart() {
        Log.logic.info("==={} 阶段[{}][{}]结算期开始!!!", getZoneName(), getStageName(), getStageSN());
		// 结算竞猜
		settleBet();
		// 设置本阶段逻辑已全部执行
        m_logicFullyExecuted = true;
	}

	/**
	 * 结算竞猜
	 */
	abstract public void settleBet();

	/**
	 * 结算单个公会的所有竞猜
	 * @param guildId
	 * @param isSuccess
	 */
	public void settleGuildAllBets(long guildId, boolean isSuccess){
		int battleFlag = getBattleFlag();
		Set<Family32Bet> allBets = m_zone.getGuildAllBets(battleFlag, guildId);
		Log.family32.info("==={} settleGuildAllBets battleFlag={} guildId={} betSize={} isSuccess={}", getZoneName(), battleFlag, guildId, allBets.size(), isSuccess);
		if(allBets.isEmpty()){
			return;
		}
		for(Family32Bet bet : allBets){
			// 结算单个竞猜
			m_zone.settleSingleBet(bet, isSuccess);
		}
	}
}

