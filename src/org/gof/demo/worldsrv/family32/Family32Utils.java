package org.gof.demo.worldsrv.family32;

import io.vertx.core.AsyncResult;
import io.vertx.core.Handler;
import io.vertx.core.json.JsonArray;
import org.gof.core.Port;
import org.gof.core.RemoteNode;
import org.gof.core.dbsrv.redis.AsyncActionResult;
import org.gof.core.dbsrv.redis.EntityManager;
import org.gof.core.dbsrv.redis.RedisTools;
import org.gof.core.support.*;
import org.gof.demo.battlesrv.battle.BattleDataFill;
import org.gof.demo.battlesrv.battle.enumVo.AttribDefine;
import org.gof.demo.distr.DistrKit;
import org.gof.demo.distr.cross.CrossManager;
import org.gof.demo.distr.cross.domain.CrossPoint;
import org.gof.demo.distr.cross.domain.CrossType;
import org.gof.demo.support.TimeUtil;
import org.gof.demo.worldsrv.arena.ArenaManager;
import org.gof.demo.worldsrv.bridgeEntity.Family32ChampionInfo;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.common.ServerList;

import org.gof.demo.worldsrv.config.ConfFamily32Reward;
import org.gof.demo.worldsrv.config.ConfGlobal;
import org.gof.demo.worldsrv.entity.HumanBrief;
import org.gof.demo.worldsrv.family32.vo.Family32GuildVO;
import org.gof.demo.worldsrv.guild.GuildManager;
import org.gof.demo.worldsrv.guild.GuildServiceProxy;
import org.gof.demo.worldsrv.guild.HumanBriefVO;
import org.gof.demo.worldsrv.human.HumanBriefLoadType;
import org.gof.demo.worldsrv.inform.ErrorTip;
import org.gof.demo.worldsrv.inform.Inform;
import org.gof.demo.worldsrv.logic.LogicUtils;
import org.gof.demo.worldsrv.mail.MailManager;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.redis.RedisKeys;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.enumKey.ConfGlobalKey;

import java.util.*;
import java.util.stream.Collectors;

public class Family32Utils {
    public static void getCrossFamily32ServiceProxy(HumanObject humanObj, Handler<AsyncResult<Family32ServiceProxy>> onComplete){
        Port port = Port.getCurrent();
        // 跨服功能检测关闭
        if(ArenaManager.inst().isCrossClose(humanObj)){
            Inform.sendMsg_error(humanObj, ErrorTip.CrossServerIsClosed);
            AsyncActionResult.fail(port, onComplete, "cross server is closed");
            return;
        }
        CrossManager.getInstance().callCrossFunc(CrossType.family32, humanObj.getHuman().getServerId(), res -> {
            try {
                if(res.failed()){
                    Log.family32.error("==跨服获取数据出问题", res.cause());
                    AsyncActionResult.fail(port, onComplete, res.cause());
                    return;
                }
                CrossPoint result = res.result();
                Family32ServiceProxy proxy = Family32ServiceProxy.newInstance(result.getNodeId());
                RemoteNode rn = Port.getCurrent().getNode().getRemoteNode(result.getNodeId());
                if(rn == null) {
                    Inform.sendMsg_error(humanObj, ErrorTip.CrossServerIsClosed);
                    AsyncActionResult.fail(port, onComplete, res.cause());
                    return;
                }
                AsyncActionResult.success(port, onComplete, proxy);
            } catch (Exception e){
                Inform.sendMsg_error(humanObj, ErrorTip.CrossServerIsClosed);
                Log.family32.error("==跨服链接出问题 ", e);
                AsyncActionResult.fail(port, onComplete, e);
            }
        });
    }

    /**
     * 获取乱斗32强开启时间
     *
     * @param isNextSeason 月份偏移
     * @return 某月第一个周一的时间戳
     */
    public static long getOpenTime(boolean isNextSeason) {
        int season = getCurrentSeason() + (isNextSeason ? 1 : 0);
        return getOpenTime(season);
    }

    /**
     * 获取乱斗32强开启时间
     * @return
     */
    public static long getOpenTime(int season){
        ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.familybattle_initial_time.SN);
        long initialTime = Utils.formatTimeToLong(confGlobal.strValue);
        // 乱斗32强开启时间，为下赛季乱斗的开启时间
        long openTime = initialTime + season * 4 * Time.WEEK + Time.DAY;
        return openTime + Family32Const.FAMILY32_STEP_TIME_OFFSET;//+ 19 * Time.DAY;
    }

    /**
     * 获取乱斗32强当前赛季
     * @return
     */
    public static int getCurrentSeason(){
        ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.familybattle_initial_time.SN);
        long initialTime = Utils.formatTimeToLong(confGlobal.strValue);
        int sumWeek = (int)Math.ceil((Utils.getDaysBetween(Port.getTime(), initialTime)) * 1.D / 7);
        // 乱斗32强赛季 = 当前乱斗赛季 - 1
        return (int)Math.ceil(sumWeek / 4.D) - 1;
    }

    /**
     * 获取乱斗32强当前赛季指定赛区的名称
     * @param zoneId
     * @return
     */
    public static String getCurrentSeasonName(int zoneId){
        return Utils.createStr("{}-{}", zoneId, getCurrentSeason());
    }

    public static String getSeasonName(int zoneId, int season){
        return Utils.createStr("{}-{}", zoneId, season);
    }

    /**
     * 获取普通乱斗赛季排行榜的key
     * @param season
     * @param groupId
     * @return
     */
    public static String getGvgSeasonRankKey(int season, int groupId){
        return RedisKeys.bridge_pvp_group_guild_rank + Utils.createStr("{}-{}", season, groupId);
    }

    /**
     * 获取参赛资格排行榜的key
     * @param zoneId
     * @return
     */
    public static String getQualifyingRankKey(int zoneId){
        return RedisKeys.family32_qualifying_rank + getCurrentSeasonName(zoneId);
    }

    /**
     * 获取最终排名的key
     * @param zoneId
     * @return
     */
    public static String getFinalRankKey(int zoneId){
        return RedisKeys.family32_final_rank + getCurrentSeasonName(zoneId);
    }

    /**
     * 获取已发结算奖励的key
     * @param zoneId
     * @return
     */
    public static String getSettledRewardKey(int zoneId){
        return RedisKeys.family32_settled_reward + getCurrentSeasonName(zoneId);
    }

    /**
     * 获取乱斗32强比赛录像id
     * @param road
     * @param round
     * @return
     */
    public static long getVideoId(int road, int round) {
        return road * 100L + round;
    }

    /**
     * 获取冠军公会信息
     * @param championInfo
     * @param onComplete
     * @return
     */
    public static void getChampionGuildInfo(Family32ChampionInfo championInfo, Handler<AsyncResult<Define.p_family32_guild>> onComplete){
        Port port = Port.getCurrent();
        Define.p_family32_guild.Builder info = Define.p_family32_guild.newBuilder();
        info.setGuildId(championInfo.getGuildId());
        info.setGuildName(championInfo.getGuildName());
        info.setServerId(championInfo.getServerId());
        info.addAllGuildFlag(GuildManager.inst().to_p_guild_flag(championInfo.getGuildFlagJson()));
        info.setTotalCombat(championInfo.getTotalCombat());
        info.addAllZoneInfo(Arrays.asList(championInfo.getZoneMinServerId(), championInfo.getZoneMaxServerId()));
        LogicUtils.loadHumanBrief(championInfo.getLeaderId(), HumanBriefLoadType.SHOW_INFO, res -> {
            if (res.failed()) {
                AsyncActionResult.fail(port, onComplete, res.cause());
                return;
            }
            HumanBrief humanBrief = res.result();
            HumanBriefVO vo = new HumanBriefVO(humanBrief);
            Define.p_common_role2.Builder leaderInfo = vo.to_p_common_role2(vo.roleFigure);
            if(leaderInfo != null){
                info.setLeader(leaderInfo);
            }
            AsyncActionResult.success(port, onComplete, info.build());
        });
    }

    /**
     * 获取乱斗32强排名
     * @param battleFlag
     * @return
     */
    public static int getCompetitionRank(int battleFlag) {
        List<ConfFamily32Reward> list = new ArrayList<>(ConfFamily32Reward.findAll());
        for(ConfFamily32Reward conf : list){
            if(conf.rank == battleFlag){
                return conf.sn;
            }
        }
        return list.isEmpty() ? 0 : list.get(list.size() - 1).sn;
    }

    /**
     * 是否循环赛阶段
     * @param stageGroup
     * @return
     */
    public static boolean isLoopStage(int stageGroup) {
        return stageGroup == Family32Const.FAMILY32_STEP_LOOP || stageGroup == Family32Const.FAMILY32_STEP_SEMI_FINAL || stageGroup == Family32Const.FAMILY32_STEP_FINAL;
    }

    /**
     * 是否发送淘汰赛信息
     * @param stageGroup
     * @return
     */
    public static boolean isSendKnockoutInfo(int stageGroup) {
        return stageGroup == Family32Const.FAMILY32_STEP_SEMI_FINAL || stageGroup == Family32Const.FAMILY32_STEP_FINAL;
    }

    /**
     * 获取开启乱斗32强的最大服务器id
     * @return
     */
    public static int getMaxOpenServerId() {
        // 先用最大开服id-10，保证乱斗组里有10个以上的服，再开启
        return (ServerList.getMaxServerId() - 10) % Config.GAME_SERVER_PREFIX_VALUE;
    }

    /**
     * 按照乱斗分组，给32强公会排序
     * @param top32GuildMap
     * @return
     */
    public static LinkedHashSet<Long> sortTop32Guild(Map<Integer, LinkedHashSet<Long>> top32GuildMap){
        LinkedHashSet<Long> top32GuildSet = new LinkedHashSet<>();
        // 按groupId升序获取迭代器列表
        List<Iterator<Long>> iterators = top32GuildMap.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .map(entry -> entry.getValue().iterator())
                .collect(Collectors.toList());
        // 轮询所有迭代器直到全部耗尽
        while (iterators.stream().anyMatch(Iterator::hasNext)) {
            for (Iterator<Long> it : iterators) {
                if (it.hasNext()) {
                    top32GuildSet.add(it.next());
                }
            }
        }
        return top32GuildSet;
    }

    /**
     * 获取32强的信息
     * @param zoneId
     * @param onComplete
     */
    public static void getTop32Info(int zoneId, Handler<AsyncResult<LinkedHashSet<Long>>> onComplete){
        Port port = Port.getCurrent();
        String redisKey = getQualifyingRankKey(zoneId);
        int minRank = 0;
        int maxRank = 31;
        RedisTools.getRankListByIndex(EntityManager.redisClient, redisKey, minRank, maxRank, false, res -> {
            if (res.failed()) {
                Log.temp.error("getRankListByIndex fail, redisKey={}", redisKey);
                AsyncActionResult.fail(port, onComplete, res.cause());
                return;
            }
            JsonArray json = res.result();
            LinkedHashSet<Long> top32GuildSet = ((List<?>) json.getList()).stream() // 将 List 转换为 List<?>
                    .map(Utils::longValue) // 将 String 转换为 Long
                    .collect(Collectors.toCollection(LinkedHashSet::new)); // 收集到 LinkedHashSet
            AsyncActionResult.success(port, onComplete, top32GuildSet);
        });
    }

    /**
     * 从游服拉取公会信息
     * @param guildId
     * @param onComplete
     */
    public static void fetchGuildInfoFromGameServer(long guildId, Handler<AsyncResult<Family32GuildVO>> onComplete) {
        Port port = Port.getCurrent();
        int serverId = Utils.getServerIdByHumanId(guildId);
        String worldNodeId = DistrKit.getWorldNodeID(serverId);
        RemoteNode rn = port.getNode().getRemoteNode(worldNodeId);
        if(rn == null) {
            // 游戏服务器没连上跨服，直接报错
            if(!Config.DATA_DEBUG){
                Log.family32.warn("游戏服={}未连接 guildId={}", serverId, guildId);
            }
            AsyncActionResult.fail(port, onComplete, new Exception("游戏服未连接"));
            return;
        }
        GuildServiceProxy proxy = GuildServiceProxy.newInstance(worldNodeId);
        proxy.getFamily32GuildInfo(guildId);
        proxy.listenResult((results, context) ->{
            boolean bResult = Utils.getParamValue(results, "result", false);
            if (!bResult) {
                Log.family32.warn("乱斗32强获取公会信息失败 guildId={}", guildId);
                AsyncActionResult.fail(port, onComplete, new Exception("乱斗32强获取公会信息失败"));
                return;
            }
            Family32GuildVO guildVO = Utils.getParamValue(results, "guildVO", null);
            if(guildVO == null){
                Log.family32.warn("乱斗32强获取公会信息为空 guildId={}", guildId);
                AsyncActionResult.fail(port, onComplete, new Exception("乱斗32强获取公会信息为空"));
                return;
            }
            AsyncActionResult.success(port, onComplete, guildVO);
        });
    }

    public static Define.p_family32_battle_report_data.Builder to_p_family32_battle_report_data(Define.p_battle_role.Builder battleRole){
        Define.p_family32_battle_report_data.Builder data = Define.p_family32_battle_report_data.newBuilder();
        data.setId(battleRole.getId());
        data.setName(battleRole.getName());
        data.setLev(battleRole.getLev());
        data.setJob(battleRole.getJob());
        data.setFigure(battleRole.getFigure());
        data.setHead(battleRole.getHead());
        data.setGvgCurHp(BattleDataFill.getAttrib(ParamKey.extPosType_4, battleRole.getExtList()));
        data.setGvgMaxHp(BattleDataFill.getAttrib(AttribDefine.hp.getValue(), battleRole.getAttrListList()));
        data.setGvgBuffId(Utils.intValue(BattleDataFill.getAttrib(ParamKey.extPosType_5, battleRole.getExtList())));
        return data;
    }

    /**
     * 给公会发送邮件
     * @param guildId
     * @param mailSn
     * @param title
     * @param content
     * @param itemJSON
     * @param params
     */
    public static void sendMailToGuild(long guildId, int mailSn, String title, String content, String itemJSON, Param params){
        int serverId = Utils.getServerIdByHumanId(guildId);
        String worldNodeId = DistrKit.getWorldNodeID(serverId);
        RemoteNode rn = Port.getCurrent().getNode().getRemoteNode(worldNodeId);
        if(rn == null) {
            if(!Config.DATA_DEBUG){
                Log.family32.warn("游戏服={}未连接 guildId={}", serverId, guildId);
            }
            return;
        }
        GuildServiceProxy proxy = GuildServiceProxy.newInstance(worldNodeId);
        proxy.sendMailToGuild(guildId, false, MailManager.SYS_SENDER, mailSn, title, content, itemJSON, params);
    }
}
