
package org.gof.demo.worldsrv.pet;

import org.gof.core.support.ManagerBase;
import org.gof.core.support.Param;
import org.gof.core.support.ParamKey;
import org.gof.core.support.Utils;
import org.gof.core.support.observer.Listener;
import org.gof.demo.battlesrv.manager.CombatChangeLog;
import org.gof.demo.battlesrv.manager.PropManager;
import org.gof.demo.battlesrv.msgHandler.SkillManager;
import org.gof.demo.battlesrv.support.PropCalc;
import org.gof.demo.worldsrv.activity.ActivityManager;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.charm.CharmManager;
import org.gof.demo.worldsrv.common.GameServiceProxy;
import org.gof.demo.worldsrv.config.*;
import org.gof.demo.worldsrv.entity.Human2;
import org.gof.demo.worldsrv.entity.UnitPropPlus;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.human.EModule;
import org.gof.demo.worldsrv.human.HumanManager;
import org.gof.demo.worldsrv.human.PlanVo;
import org.gof.demo.worldsrv.human.RoleInfoKey;
import org.gof.demo.worldsrv.inform.Inform;
import org.gof.demo.worldsrv.jobs.JobsManager;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgPet.*;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.ReasonResult;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;
import org.gof.demo.worldsrv.support.enumKey.NewsConditionTypeKey;
import org.gof.demo.worldsrv.support.observer.Event;
import org.gof.demo.worldsrv.support.observer.EventKey;
import org.gof.demo.worldsrv.task.TaskConditionTypeKey;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 同伴
 *
 * <AUTHOR>
 * @Date 2024/3/7
 * @Param
 */
public class PetManager extends ManagerBase {
    public static final int lineupMax = 5;
    public static final int PosMax = 6;// 改为6，这个常量少用，因为不同职业能使用的同伴数量不一样，改用getPetPosMax()来获得玩家的同伴数量
    public static final int pet_update_type_0 = 0;// 获得0
    public static final int pet_update_type_2 = 2;// 升级2

    public static PetManager inst() {
        return inst(PetManager.class);
    }

    public void _msg_pet_list_c2s(HumanObject humanObj) {
        sendMsg_pet_list_s2c(humanObj);
    }

    public void sendMsg_pet_list_s2c(HumanObject humanObj) {
        pet_list_s2c.Builder msg = pet_list_s2c.newBuilder();
        int lineup = humanObj.getHuman2().getUsePetLineup();
        List<Integer> petSnList = humanObj.operation.petData.lineupMap.get(lineup);
        if (petSnList == null && (lineup > lineup || lineup <= 0)) {
            humanObj.getHuman2().setUsePetLineup(1);
            petSnList = humanObj.operation.petData.lineupMap.get(1);
        }
        for (int petSn : humanObj.operation.petData.petSnLvMap.keySet()) {
            if(petSn <= 0){
                continue;
            }
            msg.addPetList(to_p_pet(humanObj, petSn));
        }
        boolean isUp = false;
        if (petSnList != null) {
            for (int i = 0; i < petSnList.size(); i++) {
                if (!isOpenPetPos(i + 1, humanObj.getHuman2().getRepSn(), humanObj.getProfession())) {
                    if (petSnList.get(i) > 0) {
                        petSnList.set(i, 0);
                        isUp = true;
                    }
                    continue;
                }
                msg.addPosList(HumanManager.inst().to_p_key_value(i+1, petSnList.get(i)));
            }
        }
        if(isUp){
            humanObj.operation.petData.saveData(humanObj);
        }
        humanObj.sendMsg(msg);
    }

    /**
     * 一键强化升级伙伴
     *
     * <AUTHOR>
     * @Date 2024/4/16
     * @Param
     */
    private void petAddLvOnekey(HumanObject humanObj) {
        List<Integer> snList = new ArrayList<>();
        for (int petSn : humanObj.operation.petData.petSnLvMap.keySet()) {
            if (isAddLvOnekey(humanObj, petSn)) {
                snList.add(petSn);
            }
        }
        if (!snList.isEmpty()) {
            sendMsg_pet_update_s2c(humanObj, pet_update_type_2, snList);
            updatePetPropCalc(humanObj);
            humanObj.operation.petData.saveData(humanObj);
        }
    }



    private boolean isAddLvOnekey(HumanObject humanObj, int petSn) {
        int level = humanObj.operation.petData.petSnLvMap.getOrDefault(petSn, 0);
        ConfPetlevel_0 confPetLv = ConfPetlevel_0.get(petSn, level);
        if (confPetLv == null) {
            Log.temp.error("===ConfPetlevel_0 配表错误, petSn={}, level ={}", petSn, level);
            return false;
        }

        ConfPetlevel_0 confLvNext = ConfPetlevel_0.get(petSn, level + 1);
        if (confLvNext == null) {
            Log.temp.error("===ConfPetlevel_0 配表错误, petSn={}, level ={}", petSn, level + 1);
            return false;
        }

        ReasonResult result = ProduceManager.inst().canCostIntArr(humanObj, confLvNext.expend);
        if (!result.success) {
            return false;
        }
        int lvOld = level;
        while (result.success) {
            ProduceManager.inst().costIntArr(humanObj, confLvNext.expend, MoneyItemLogKey.同伴升级);
            level++;
            confLvNext = ConfPetlevel_0.get(petSn, level + 1);
            if (confLvNext == null) {
                Log.temp.error("===ConfPetlevel_0 配表错误, petSn={}, level ={}", petSn, level + 1);
                break;
            }
            result = ProduceManager.inst().canCostIntArr(humanObj, confLvNext.expend);
        }
        humanObj.operation.petData.petSnLvMap.put(petSn, level);
        if(!humanObj.getHuman3().isAddLvPet()){
            humanObj.getHuman3().setAddLvPet(true);
        }
        Event.fire(EventKey.TASK_CONDITION_TYPE, "humanObj", humanObj, "type", TaskConditionTypeKey.TASK_主线,
                "taskType", TaskConditionTypeKey.TASK_TYPE_14, "paramType", TaskConditionTypeKey.PARAM_TYPE_1, "addValue", level - lvOld);
        return true;
    }

    private boolean isAddPetLv(HumanObject humanObj, int petSn){
        if (!humanObj.operation.petData.petSnLvMap.containsKey(petSn)) {
            return false;
        }
        int level = humanObj.operation.petData.petSnLvMap.getOrDefault(petSn, 0);
        ConfPetlevel_0 confPetLv = ConfPetlevel_0.get(petSn, level);
        if (confPetLv == null) {
            Log.temp.error("===ConfPetlevel_0 配表错误, petSn={}, level ={}", petSn, level);
            return false;
        }
        ConfPetlevel_0 confLvNext = ConfPetlevel_0.get(petSn, level + 1);
        if (confLvNext == null) {
            Log.temp.error("===ConfPetlevel_0 配表错误, petSn={}, level ={}", petSn, level + 1);
            return false;
        }

        ReasonResult result = ProduceManager.inst().canCostIntArr(humanObj, confLvNext.expend);
        if (!result.success) {
            return false;
        }
        ProduceManager.inst().costIntArr(humanObj, confLvNext.expend, MoneyItemLogKey.同伴升级);
        level++;
        humanObj.operation.petData.petSnLvMap.put(petSn, level);
        if(!humanObj.getHuman3().isAddLvPet()){
            humanObj.getHuman3().setAddLvPet(true);
        }
        Event.fire(EventKey.TASK_CONDITION_TYPE, "humanObj", humanObj, "type", TaskConditionTypeKey.TASK_主线,
                "taskType", TaskConditionTypeKey.TASK_TYPE_14, "paramType", TaskConditionTypeKey.PARAM_TYPE_1, "addValue", 1);
        return true;
    }

    /**
     * 同伴升级
     *
     * <AUTHOR>
     * @Date 2024/3/7
     * @Param
     */
    public void _msg_pet_levup_c2s(HumanObject humanObj, int petSn) {
        if (petSn == 0) {
            // 一键
            petAddLvOnekey(humanObj);
            return;
        }
        if(!isAddPetLv(humanObj, petSn)){
            sendMsg_pet_update_s2c(humanObj, petSn);
            return;
        }

        // TODO 属性
        updatePetPropCalc(humanObj);

        sendMsg_pet_update_s2c(humanObj, petSn);
        humanObj.operation.petData.saveData(humanObj);
    }

    private void sendMsg_pet_update_part_s2c(HumanObject humanObj, int petSn) {
        pet_update_part_s2c.Builder msg = pet_update_part_s2c.newBuilder();
        msg.setPetId(petSn);
        // TODO
        int level = humanObj.operation.petData.petSnLvMap.getOrDefault(petSn, 0);
        msg.addUpdateList(HumanManager.inst().to_p_key_value(RoleInfoKey.ROLE_ATTR_LVL.getKey(), level));

        humanObj.sendMsg(msg);
    }

    public void updatePetPropCalc(HumanObject humanObj) {
        HumanManager.inst().updateIllustratedPropCalc(humanObj);

        PropCalc propCalc = new PropCalc();
        int power = 0;
        for (Map.Entry<Integer, Integer> entry : humanObj.operation.petData.petSnLvMap.entrySet()) {
            int petSn = entry.getKey();
            int petLv = entry.getValue();
            ConfPetlevel_0 confPetLv = ConfPetlevel_0.get(petSn, petLv);
            if (confPetLv == null) {
                Log.temp.error("===ConfPetlevel_0 配表错误, petSn={}, level ={}", petSn, petLv);
                continue;
            }
            propCalc.plus(confPetLv.ownEffect);
        }
        // 算上皮肤拥有加成
        for (Map.Entry<Integer, Integer> entry : humanObj.operation.petData.petSkinLvMap.entrySet()) {
            int skinSn = entry.getKey();
            int skinLv = entry.getValue();
            ConfPetSkinlevel conf = ConfPetSkinlevel.get(skinSn, skinLv);
            propCalc.plus(conf.ownEffect);
            power += conf.power;
        }

        int lineup = humanObj.getHuman2().getUsePetLineup();
        List<Integer> petSnList = humanObj.operation.petData.lineupMap.get(lineup);
        Map<Integer, Integer> petSkinMap = humanObj.operation.petData.petSkinLineupMap.getOrDefault(lineup, new HashMap<>(0));
        Map<Integer, Integer> petSkinLvMap = humanObj.operation.petData.petSkinLvMap;
        Map<Integer, Integer> petLvMap = humanObj.operation.petData.petSnLvMap;
        Map<Integer, Integer> typeEffectMap = new HashMap<>();
        int effectType = JobsManager.inst().getPetSpecialEffectType(humanObj.getProfession());
        for (int petSn : petSnList) {
            if (petSn <= 0) {
                continue;
            }
            int petLv = petLvMap.getOrDefault(petSn, 0);
            int skinSn = petSkinMap.getOrDefault(petSn, 0);
            int skinLv = petSkinLvMap.getOrDefault(skinSn, 0);
            int[][] equipEffect = null;
            if (skinSn > 0 && skinLv > 0) {
                // 皮肤装备效果直接替换掉伙伴的装备效果
                ConfPetSkinlevel conf = ConfPetSkinlevel.get(skinSn, skinLv);
                equipEffect = effectType != 0 ? conf.equipEffect1 : conf.equipEffect;
            }
            if (equipEffect == null) {
                ConfPetlevel_0 confPetLv = ConfPetlevel_0.get(petSn, petLv);
                equipEffect = effectType != 0 ? confPetLv.equipEffect1 : confPetLv.equipEffect;
            }
            if (equipEffect == null) {
                Log.game.error("伙伴装备效果为空, petSn={}, petLv={}, petSkinSn={}, petSkinLv={}", petSn, petLv, skinSn, skinLv);
                continue;
            }
            for (int[] ints : equipEffect) {
                int effectSn = ints[0];
                int effectLv = ints[1];
                typeEffectMap.put(effectSn, Math.max(effectLv, typeEffectMap.getOrDefault(effectSn, 0)));
            }
        }
        for (Map.Entry<Integer, Integer> entry : typeEffectMap.entrySet()) {
            ConfSkillLevel_0 confSkillLv = ConfSkillLevel_0.get(entry.getKey(), entry.getValue());
            if (confSkillLv == null) {
                continue;
            }
            if (confSkillLv.attrType == null || confSkillLv.attrType[0] != 1) {
                propCalc.plus(confSkillLv.ownEffect);
            }
        }
        humanObj.dataPers.unitPropPlus.setFieldRead(UnitPropPlus.K.pet, propCalc.toJSONStr());
        HumanManager.inst().updatePowerPar(humanObj, EModule.petSkin, power);
        PropManager.inst().propCalc(humanObj, CombatChangeLog.同伴);
        HumanManager.inst().sendMsg_role_total_sp_update_s2c(humanObj);
    }

    public List<Define.p_passive_skill> getPetPassiveSkillList(HumanObject humanObj, int lineup) {
        // 被动技能
        Map<Integer, Integer> petSnLvMap = humanObj.operation.petData.petSnLvMap;
        Map<Integer, List<Integer>> lineupMap = humanObj.operation.petData.lineupMap;
        Map<Integer, Map<Integer, Integer>> petSkinLineupMap = humanObj.operation.petData.petSkinLineupMap;
        Map<Integer, Integer> petSkinLvMap = humanObj.operation.petData.petSkinLvMap;
        int effectType = JobsManager.inst().getPetSpecialEffectType(humanObj.getProfession());
        return getPetPassiveSkillList(lineup, petSnLvMap, lineupMap, petSkinLineupMap, petSkinLvMap, effectType);
    }


    public List<Define.p_passive_skill> getPetPassiveSkillList(int lineup, Map<Integer, Integer> petSnLvMap, Map<Integer, List<Integer>> lineupMap,
                                                               Map<Integer, Map<Integer, Integer>> petSkinLineupMap, Map<Integer, Integer> petSkinLvMap, int effectType) {
        if (petSnLvMap == null || lineupMap == null || !lineupMap.containsKey(lineup)) {
            return null;
        }
        // 被动技能
        List<Integer> petSnList = lineupMap.get(lineup);
        if (petSnList == null) {
            return null;
        }
        Map<Integer, Integer> petSkinSnMap = petSkinLineupMap.getOrDefault(lineup, new HashMap<>(0));
        return SkillManager.inst().pet_passive_skill_list(petSnLvMap, petSnList, petSkinSnMap, petSkinLvMap, effectType);
    }

    public void _msg_pet_set_pos_c2s(HumanObject humanObj, int petSn, int pos) {
        int lineup = humanObj.getHuman2().getUsePetLineup();
        List<Integer> petSnList = humanObj.operation.petData.lineupMap.get(lineup);
        List<Integer> oldPetSnList = new ArrayList<>(petSnList);
        Map<Integer, Integer> petSnLvMap = humanObj.operation.petData.petSnLvMap;
        if (pos != 0 && !isOpenPetPos(pos, humanObj.getHuman2().getRepSn(), humanObj.getProfession())) {
            return;
        }
        int effectType = JobsManager.inst().getPetSpecialEffectType(humanObj.getProfession());
        Map<Integer, Integer> petSnSkinMap = humanObj.operation.petData.petSkinLineupMap.getOrDefault(lineup, new HashMap<>(0));
        Map<Integer, Integer> petSkinLvMap = humanObj.operation.petData.petSkinLvMap;
        if (petSn == 0) {
            if (pos >= 1 && pos <= getPetPosMax(humanObj)) {
                int delPetSn;
                if (pos - 1 >= petSnList.size()) {
                    delPetSn = 0;
                    petSnList.add(0);
                } else {
                    delPetSn = petSnList.get(pos - 1);
                    petSnList.set(pos - 1, 0);
                }
                sendMsg_pet_update_s2c(humanObj, delPetSn);
                int skinSn = petSnSkinMap.getOrDefault(delPetSn, 0);
                int skinLv = petSkinLvMap.getOrDefault(skinSn, 0);
                List<Define.p_passive_skill> delList = SkillManager.inst().to_p_passive_skill(delPetSn, petSnLvMap.get(delPetSn), skinSn, skinLv, effectType);
                SkillManager.inst().sendMsg_skill_passive_update_s2c(humanObj, null, delList);
                updatePetPropCalc(humanObj);
            }
            sendMsg_pet_tab_info_update_s2c(humanObj, lineup, HumanManager.inst().to_p_key_value(pos, 0).build());
            return;
        }
        if (!humanObj.operation.petData.petSnLvMap.containsKey(petSn)) {
            sendMsg_pet_set_pos_s2c(humanObj, petSnList);
            return;
        }
        List<Define.p_passive_skill> delList = new ArrayList<>();
        List<Define.p_passive_skill> updateList = new ArrayList<>();
        int index = -1;
        if (pos == 0) {
            index = petSnList.indexOf(petSn);
            if (index < 0) {
                sendMsg_pet_set_pos_s2c(humanObj, petSnList);
                updatePetPropCalc(humanObj);
                return;
            }
            petSnList.set(index, 0);
            int level = petSnLvMap.get(petSn);
            int skinSn = petSnSkinMap.getOrDefault(petSn, 0);
            int skinLv = petSkinLvMap.getOrDefault(skinSn, 0);
            delList = SkillManager.inst().to_p_passive_skill(petSn, level, skinSn, skinLv, effectType);
        } else {
            if (pos >= 1 && pos <= getPetPosMax(humanObj)) {
                int delPetSn;
                if (pos - 1 >= petSnList.size()) {
                    delPetSn = 0;
                    petSnList.add(petSn);
                } else {
                    delPetSn = petSnList.get(pos - 1);
                    petSnList.set(pos - 1, petSn);
                }
                index = pos - 1;
                if (delPetSn > 0) {
                    int skinSn = petSnSkinMap.getOrDefault(delPetSn, 0);
                    int skinLv = petSkinLvMap.getOrDefault(skinSn, 0);
                    delList = SkillManager.inst().to_p_passive_skill(delPetSn, petSnLvMap.get(delPetSn), skinSn, skinLv, effectType);
                }
                int skinSn = petSnSkinMap.getOrDefault(petSn, 0);
                int skinLv = petSkinLvMap.getOrDefault(skinSn, 0);
                updateList = SkillManager.inst().to_p_passive_skill(petSn, petSnLvMap.get(petSn), skinSn, skinLv, effectType);
                sendMsg_pet_update_s2c(humanObj, delPetSn);
            }
        }
        if (index < 0) {
            sendMsg_pet_set_pos_s2c(humanObj, petSnList);
            humanObj.operation.petData.saveData(humanObj);
            updatePetPropCalc(humanObj);
            return;
        }
        sendMsg_pet_update_s2c(humanObj, petSn);
        SkillManager.inst().sendMsg_skill_passive_update_s2c(humanObj);
        sendMsg_pet_tab_info_update_s2c(humanObj, lineup);
        sendMsg_pet_set_pos_s2c(humanObj, HumanManager.inst().to_p_key_value(pos, petSnList.get(index)));

        updatePetPropCalc(humanObj);

        Map<Integer, Integer> jobPetUpdateSkillMap = getJobPetPassSkillMap(humanObj, petSnList);
        if (jobPetUpdateSkillMap != null && !jobPetUpdateSkillMap.isEmpty()) {
            for (Map.Entry<Integer, Integer> entry : jobPetUpdateSkillMap.entrySet()) {
                updateList.add(SkillManager.inst().to_p_passive_skill(entry.getKey(), entry.getValue()));
            }
        }
        Map<Integer, Integer> jobPetDeleteSkillMap = getJobPetPassSkillMap(humanObj, oldPetSnList);
        if (jobPetDeleteSkillMap != null && !jobPetDeleteSkillMap.isEmpty()) {
            for (Map.Entry<Integer, Integer> entry : jobPetDeleteSkillMap.entrySet()) {
                if (!jobPetUpdateSkillMap.containsKey(entry.getKey())) {
                    delList.add(SkillManager.inst().to_p_passive_skill(entry.getKey(), entry.getValue()));
                }
            }
        }
        if (jobPetUpdateSkillMap.size() > 0 || jobPetDeleteSkillMap.size() > 0) {
            JobsManager.inst().updatePorpCalc(humanObj);// 处理职业技能被动技能变动
        }
        SkillManager.inst().sendMsg_skill_passive_update_s2c(humanObj, updateList, delList);
        humanObj.operation.petData.saveData(humanObj);
        ActivityManager.inst().addActivityProgress(humanObj, TaskConditionTypeKey.TASK_TYPE_64, 0);
    }

    private void sendMsg_pet_set_pos_s2c(HumanObject humanObj, List<Integer> snList) {
        pet_set_pos_s2c.Builder msg = pet_set_pos_s2c.newBuilder();
        // TODO
        for (int i = 0; i < snList.size(); i++) {
            Define.p_key_value.Builder dInfo = Define.p_key_value.newBuilder();
            dInfo.setK(i + 1);
            dInfo.setV(snList.get(i));
            msg.addPosList(dInfo);
        }
        humanObj.sendMsg(msg);
    }

    private void sendMsg_pet_set_pos_s2c(HumanObject humanObj,  Define.p_key_value.Builder dInfo) {
        // 可不发
        pet_set_pos_s2c.Builder msg = pet_set_pos_s2c.newBuilder();
        msg.addPosList(dInfo);
        humanObj.sendMsg(msg);
    }

    public void _msg_pet_set_all_pos_c2s(HumanObject humanObj, List<Define.p_key_value> posListList) {
        Human2 human = humanObj.getHuman2();
        int tab = human.getUsePetLineup();
        PetData petData = humanObj.operation.petData;
        Map<Integer, Integer> petSnLvMap = petData.petSnLvMap;
        Map<Integer, Integer> petSkinSnMap = petData.petSkinLineupMap.getOrDefault(tab, new HashMap<>(0));
        Map<Integer, Integer> petSkinLvMap = petData.petSkinLvMap;
        List<Integer> snList = new ArrayList<>();
        for (int m = 0; m < getPetPosMax(humanObj); m++) {
            snList.add(0);
        }
        List<Integer> useSnList = new ArrayList<>();
        for (Define.p_key_value dInfo : posListList) {
            int index = (int) dInfo.getK() - 1;
            int sn = (int) dInfo.getV();

            if (sn <= 0) {
                snList.set(index, sn);
                continue;
            }
            if (!isOpenPetPos((int)dInfo.getK(), humanObj.getHuman2().getRepSn(), humanObj.getProfession())) {
                return;
            }
            if (useSnList.contains(sn)) {
                return;
            }
            if (!petSnLvMap.containsKey(sn)) {
                return;
            }
            useSnList.add(sn);
            snList.set(index, sn);
        }
        int effectType = JobsManager.inst().getPetSpecialEffectType(humanObj.getProfession());
        List<Integer> oldList = petData.lineupMap.get(tab);
        useSnList.removeAll(oldList);
        List<Define.p_passive_skill> updateList = SkillManager.inst().pet_passive_skill_list(petSnLvMap, useSnList, petSkinSnMap, petSkinLvMap, effectType);
        // 处理更换同伴后，职业技能被动技能变动
        Map<Integer, Integer> jobPetUpdateSkillMap = getJobPetPassSkillMap(humanObj, snList);
        if (jobPetUpdateSkillMap != null && !jobPetUpdateSkillMap.isEmpty()) {
            for (Map.Entry<Integer, Integer> entry : jobPetUpdateSkillMap.entrySet()) {
                updateList.add(SkillManager.inst().to_p_passive_skill(entry.getKey(), entry.getValue()));
            }
        }

        Map<Integer, Integer> jobPetDeleteSkillMap = getJobPetPassSkillMap(humanObj, oldList);// 先把旧同伴引起的职业被动技能记录下来
        oldList.removeAll(snList);
        List<Define.p_passive_skill> delList = SkillManager.inst().pet_passive_skill_list(petSnLvMap, oldList, petSkinSnMap, petSkinLvMap, effectType);
        if (jobPetDeleteSkillMap != null && !jobPetDeleteSkillMap.isEmpty()) {
            for (Map.Entry<Integer, Integer> entry : jobPetDeleteSkillMap.entrySet()) {
                if (!jobPetUpdateSkillMap.containsKey(entry.getKey())) {
                    delList.add(SkillManager.inst().to_p_passive_skill(entry.getKey(), entry.getValue()));
                }
            }
        }

        if (jobPetUpdateSkillMap.size() > 0 || jobPetDeleteSkillMap.size() > 0) {
            JobsManager.inst().updatePorpCalc(humanObj);
        }

        petData.lineupMap.put(tab, snList);
        human.setPetLineupJSON(Utils.mapIntListIntToJSON(petData.lineupMap));
        sendMsg_pet_tab_info_s2c(humanObj, tab);
        sendMsg_pet_tab_info_update_s2c(humanObj, tab);
        sendMsg_pet_update_s2c(humanObj, pet_update_type_0, oldList);
        sendMsg_pet_update_s2c(humanObj, pet_update_type_0, snList);

        SkillManager.inst().sendMsg_skill_passive_update_s2c(humanObj, updateList, delList);

        updatePetPropCalc(humanObj);
        humanObj.operation.petData.saveData(humanObj);
        ActivityManager.inst().addActivityProgress(humanObj, TaskConditionTypeKey.TASK_TYPE_64, 0);
    }

    public void _msg_pet_tab_info_c2s(HumanObject humanObj) {
        sendMsg_pet_tab_info_s2c(humanObj, humanObj.getHuman2().getUsePetLineup());
    }

    public void _msg_pet_choose_tab_c2s(HumanObject humanObj, int tab) {
        if (tab > PetManager.lineupMax) {
            return;
        }
        int oldTab = humanObj.getHuman2().getUsePetLineup();
        humanObj.getHuman2().setUsePetLineup(tab);
        PetData petData = humanObj.operation.petData;
        if (!petData.lineupMap.containsKey(tab)) {
            List<Integer> snList = new ArrayList<>();
            for (int m = 0; m < getPetPosMax(humanObj); m++) {
                snList.add(0);
            }
            petData.lineupMap.put(tab, snList);
        }
        HumanManager.inst().setPlanTab(humanObj, PlanVo.TAB_PET, tab);
        sendMsg_pet_choose_tab_s2c(humanObj);
        sendMsg_pet_tab_info_s2c(humanObj, tab);
        sendMsg_pet_tab_info_update_s2c(humanObj, tab);
        if(oldTab != tab){
            List<Integer> oldSnList = petData.lineupMap.get(oldTab);
            sendMsg_pet_update_s2c(humanObj, pet_update_type_0, oldSnList);

            List<Integer> snListNew = petData.lineupMap.get(tab);
            sendMsg_pet_update_s2c(humanObj, pet_update_type_0, snListNew);

            int effectType = JobsManager.inst().getPetSpecialEffectType(humanObj.getProfession());
            Map<Integer, Integer> petSkinSnMap = petData.petSkinLineupMap.getOrDefault(tab, new HashMap<>(0));
            Map<Integer, Integer> petSkinLvMap = petData.petSkinLvMap;
            List<Define.p_passive_skill> delList = SkillManager.inst().pet_passive_skill_list(petData.petSnLvMap, oldSnList, petSkinSnMap, petSkinLvMap, effectType);
            List<Define.p_passive_skill> updateList = SkillManager.inst().pet_passive_skill_list(petData.petSnLvMap, snListNew, petSkinSnMap, petSkinLvMap, effectType);
            SkillManager.inst().sendMsg_skill_passive_update_s2c(humanObj, updateList, delList);

            humanObj.operation.petData.saveData(humanObj);
            ActivityManager.inst().addActivityProgress(humanObj, TaskConditionTypeKey.TASK_TYPE_64, 0);
            updatePetPropCalc(humanObj);
        }
    }

    public void sendMsg_pet_choose_tab_s2c(HumanObject humanObj) {
        pet_choose_tab_s2c.Builder msg = pet_choose_tab_s2c.newBuilder();
        msg.setNewTab(humanObj.getHuman2().getUsePetLineup());
        humanObj.sendMsg(msg);
    }

    private void sendMsg_pet_tab_info_s2c(HumanObject humanObj, int tab) {
        pet_tab_info_s2c.Builder msg = pet_tab_info_s2c.newBuilder();
        msg.setTab(tab);
        for (int tempSn : humanObj.operation.petData.lineupMap.keySet()) {
            msg.addTabList(to_p_pet_tab_info(humanObj, tempSn));
        }
        humanObj.sendMsg(msg);
    }

    public Define.p_pet_tab_info to_p_pet_tab_info(HumanObject humanObj, int tab) {
        String petTabName = humanObj.getHuman3().getPetTabNameJSON();
        Map<Integer, String> tabNameMap = Utils.jsonToMapIntString(petTabName);
        List<Integer> petSnList = humanObj.operation.petData.lineupMap.get(tab);
        Define.p_pet_tab_info.Builder dInfo = Define.p_pet_tab_info.newBuilder();
        dInfo.setTab(tab);
        if (tabNameMap != null && tabNameMap.containsKey(tab)) {
            dInfo.setName(tabNameMap.get(tab));
        } else {
            dInfo.setName("");
        }
        for (int i = 0; i < petSnList.size(); i++) {
            dInfo.addPosInfo(HumanManager.inst().to_p_key_value(i + 1, petSnList.get(i)));
        }
        return dInfo.build();
    }

    public void _msg_pet_change_tab_name_c2s(HumanObject humanObj, int tab, String name) {
        String petTabName = humanObj.getHuman3().getPetTabNameJSON();
        Map<Integer, String> tabNameMap = Utils.jsonToMapIntString(petTabName);
        tabNameMap.put(tab, name);
        humanObj.getHuman3().setPetTabNameJSON(Utils.mapIntStrToJSON(tabNameMap));
        sendMsg_pet_change_tab_name_s2c(humanObj, tab, name);

        humanObj.getHuman3().update();
    }

    private void sendMsg_pet_change_tab_name_s2c(HumanObject humanObj, int tab, String name) {
        pet_change_tab_name_s2c.Builder msg = pet_change_tab_name_s2c.newBuilder();
        msg.setTab(tab);
        msg.setName(name);
        humanObj.sendMsg(msg);
    }

    /**
     * 图鉴激活
     *
     * <AUTHOR>
     * @Date 2024/3/14
     * @Param
     */
    public boolean petIllustrated(HumanObject humanObj, int sn, int level) {
        ConfIllustrated_0 conf = ConfIllustrated_0.get(sn, level);
        if (conf == null) {
            Log.temp.error("===ConfIllustrated_0配表错误，sn={}, level={}", sn, level);
            return false;
        }
        ConfIllustrated_0 confNext = ConfIllustrated_0.get(sn, level + 1);
        if (confNext == null) {
            Log.temp.error("===ConfIllustrated_0配表错误，sn={}, level={}", sn, level+1);
            return false;
        }
        boolean result = HumanManager.inst().isMeetIllustrated(humanObj, conf.condition);
        if (!result) {
            return false;
        }
        int nowLv = humanObj.operation.petData.petIllustratedSnLvMap.getOrDefault(sn, 0);
        if(nowLv != level){
            return true;
        }
        humanObj.operation.petData.petIllustratedSnLvMap.put(sn, nowLv + 1);
        return true;
    }

    public void sendMsg_pet_update_s2c(HumanObject humanObj, int type, List<Integer> petSnList) {
        pet_update_s2c.Builder msg = pet_update_s2c.newBuilder();
        msg.setUpdateState(type);
        for (int petSn : petSnList) {
            if (petSn <= 0) {
                continue;
            }
            msg.addPetList(to_p_pet(humanObj, petSn));
        }
        humanObj.sendMsg(msg);
    }


    public void sendMsg_pet_update_s2c(HumanObject humanObj, int petSn) {
        if (petSn <= 0) {
            return;
        }
        pet_update_s2c.Builder msg = pet_update_s2c.newBuilder();
        msg.setUpdateState(0);
        msg.addPetList(to_p_pet(humanObj, petSn));
        humanObj.sendMsg(msg);
    }

    public Define.p_pet to_p_pet(HumanObject humanObj, int petSn) {
        if (petSn <= 0) {
            return null;
        }
        int tab = humanObj.getHuman2().getUsePetLineup();
        List<Integer> petSnList = humanObj.operation.petData.lineupMap.get(tab);
        int pos = petSnList != null && petSnList.contains(petSn) ? petSnList.indexOf(petSn) + 1 : 0;
        int level = humanObj.operation.petData.petSnLvMap.get(petSn);
        Define.p_pet.Builder dInfo = Define.p_pet.newBuilder();
        dInfo.setPetId(petSn);
        dInfo.setPower(0);
        dInfo.setLevel(level);
        dInfo.setPos(pos);
        ConfPetlevel_0 conf = ConfPetlevel_0.get(petSn, level);
        for (int i = 0; i < conf.ownEffect.length; i++) {
            dInfo.addAttrList(HumanManager.inst().to_p_key_value(conf.ownEffect[i][0], conf.ownEffect[i][1]));
        }

        // 设置解锁的皮肤和皮肤属性加成
        List<Integer> skinList = GlobalConfVal.petSnSkinMap.getOrDefault(petSn, new ArrayList<>(0));
        for (Integer skinSn : skinList) {
            if (!humanObj.operation.petData.petSkinLvMap.containsKey(skinSn)) {
                continue;
            }
            int skinLv = humanObj.operation.petData.petSkinLvMap.get(skinSn);
            dInfo.addSkinList(HumanManager.inst().to_p_key_value(skinSn, skinLv));
        }
        // 设置穿戴的皮肤
        Map<Integer, Integer> petSkinSnMap = humanObj.operation.petData.petSkinLineupMap.getOrDefault(tab, new HashMap<>(0));
        dInfo.setSkinSn(petSkinSnMap.getOrDefault(petSn, 0));
        return dInfo.build();
    }

    public void sendMsg_pet_tab_info_update_s2c(HumanObject humanObj, int tab, Define.p_key_value dInfo) {
        pet_tab_info_update_s2c.Builder msg = pet_tab_info_update_s2c.newBuilder();
        msg.setTab(tab);
        msg.addPosInfo(dInfo);
        humanObj.sendMsg(msg);
    }

    public void sendMsg_pet_tab_info_update_s2c(HumanObject humanObj, int tab, List<Define.p_key_value> dInfoList) {
        pet_tab_info_update_s2c.Builder msg = pet_tab_info_update_s2c.newBuilder();
        msg.setTab(tab);
        msg.addAllPosInfo(dInfoList);
        humanObj.sendMsg(msg);
    }

    public void sendMsg_pet_tab_info_update_s2c(HumanObject humanObj, int tab) {
        pet_tab_info_update_s2c.Builder msg = pet_tab_info_update_s2c.newBuilder();
        msg.setTab(tab);
        int posMax = getPetPosMax(humanObj);
        List<Integer> snList = humanObj.operation.petData.lineupMap.get(tab);
        for (int i= 0; i < snList.size() && i < posMax; i++) {
            msg.addPosInfo(HumanManager.inst().to_p_key_value(i + 1, snList.get(i)).build());
        }
        humanObj.sendMsg(msg);
    }


    public void sendMsg_pet_unlock_s2c(HumanObject humanObj, int petSn){
        pet_unlock_s2c.Builder msg =pet_unlock_s2c.newBuilder();
        msg.setPetId(petSn);
        humanObj.sendMsg(msg);
    }

    public void sendMsg_pet_unlock_pos_s2c(HumanObject humanObj, List<Define.p_key_value> dInfoList){
        pet_unlock_pos_s2c.Builder msg = pet_unlock_pos_s2c.newBuilder();
        msg.addAllPosList(dInfoList);
        humanObj.sendMsg(msg);
    }

    public Define.p_role_pet to_p_role_pet(HumanObject humanObj, int petSn, int tab) {
        if (petSn <= 0) {
            return null;
        }
        List<Integer> petSnList = humanObj.operation.petData.lineupMap.get(tab);
        int pos = petSnList!= null && petSnList.contains(petSn)? petSnList.indexOf(petSn) + 1 : 0;
        int skinSn = humanObj.operation.petData.petSkinLineupMap.getOrDefault(tab, new HashMap<>(0)).getOrDefault(petSn, 0);
        int skinLv = humanObj.operation.petData.petSkinLvMap.getOrDefault(skinSn, 0);
        Define.p_role_pet.Builder dInfo = Define.p_role_pet.newBuilder();
        dInfo.setPetId(petSn);
        dInfo.setPetLev(humanObj.operation.petData.petSnLvMap.get(petSn));
        dInfo.setPetPos(pos);
        dInfo.setSkinId(skinSn);
        dInfo.setSkinLev(skinLv);
        return dInfo.build();
    }

    /**
     * 机器人的同伴消息构造
     */
    public Define.p_role_pet to_p_role_pet(int pos, int petSn, int petLev, int skinSn, int skinLv) {
        Define.p_role_pet.Builder dInfo = Define.p_role_pet.newBuilder();
        dInfo.setPetPos(pos);
        dInfo.setPetId(petSn);
        dInfo.setPetLev(petLev);
        dInfo.setSkinId(skinSn);
        dInfo.setSkinLev(skinLv);
        return dInfo.build();
    }
    public List<Define.p_role_pet> getP_role_petList(HumanObject humanObj) {
        return getP_role_petList(humanObj, humanObj.getHuman2().getUsePetLineup());
    }

    public List<Define.p_role_pet> getP_role_petList(HumanObject humanObj, int lineup) {
        List<Integer> petSnList = humanObj.operation.petData.lineupMap.get(lineup);
        if (petSnList == null) {
            humanObj.getHuman2().setUsePetLineup(1);
            petSnList = humanObj.operation.petData.lineupMap.getOrDefault(1, new ArrayList<>());
            while (petSnList.size() < getPetPosMax(humanObj)){
                petSnList.add(0);
            }
        }
        List<Define.p_role_pet> petList = new ArrayList<>();
        for (int petSn : petSnList) {
            if (petSn <= 0) {
                continue;
            }
            petList.add(to_p_role_pet(humanObj, petSn, lineup));
        }
        return petList;
    }

    public List<Define.p_role_pet> getP_role_petList(String petSnLvJSON, String lineupJSON, int useLineup, String petSkinLineupJSON, String petSkinLvJSON) {
        Map<Integer, Integer> petSnLvMap = Utils.jsonToMapIntInt(petSnLvJSON);// 同伴等级映射
        Map<Integer, List<Integer>> lineupMap = Utils.jsonToMapIntListInt(lineupJSON);// 方案同伴sn映射
        Map<Integer, Map<Integer, Integer>> petSkinLineupMap = Utils.jsonToIntMapIntInt(petSkinLineupJSON);// 方案同伴皮肤装备映射
        Map<Integer, Integer> petSkinLvMap = Utils.jsonToMapIntInt(petSkinLvJSON);// 同伴皮肤等级映射
        Map<Integer, Integer> petSkinSnMap = petSkinLineupMap.getOrDefault(useLineup, new HashMap<>(0));
        List<Define.p_role_pet> petList = new ArrayList<>();
        List<Integer> useList = lineupMap.getOrDefault(useLineup, Collections.nCopies(PetManager.PosMax, 0));
        for (int i = 0; i < useList.size(); i++) {
            int sn = useList.get(i);
            if (sn == 0) {
                continue;
            }
            int petLv = petSnLvMap.getOrDefault(sn, 0);
            int pos = i + 1;
            int skinSn = petSkinSnMap.getOrDefault(sn, 0);
            int skinLv = petSkinLvMap.getOrDefault(skinSn, 0);
            petList.add(to_p_role_pet(pos, sn, petLv, skinSn, skinLv));
        }
        return petList;
    }

    @Listener(EventKey.HUMAN_LOGIN_FINISH)
    public void _on_HUMAN_LOGIN_FINISH(Param param) {
        HumanObject humanObj = param.get("humanObj");
        checkPetPos(humanObj);
    }

    public void checkPetPos(HumanObject humanObj) {
        boolean isUp = false;
        int posMax = getPetPosMax(humanObj);
        for (int i = 1; i <= posMax; i++) {
            if (humanObj.petOpenPosList.contains(i)) {
                continue;
            }
            if (isOpenPetPos(i, humanObj.getHuman2().getRepSn(), humanObj.getProfession())) {
                isUp = true;
                humanObj.petOpenPosList.add(i);
            }
        }
        if (isUp) {
            List<Define.p_key_value> dInfoList = new ArrayList<>();
            for (int i :humanObj.petOpenPosList) {
                dInfoList.add(HumanManager.inst().to_p_key_value(i, 0).build());
            }
            sendMsg_pet_unlock_pos_s2c(humanObj, dInfoList);
        }
    }

    public boolean isOpenPetPos(int pos, int repSn, int jobSn) {
        ConfPetPos conf = ConfPetPos.get(pos);
        if (conf == null) {
            return false;
        }
        if (conf.condition == null) {
            return true;
        }
        int conditionType = conf.condition[0];
        if (conditionType == ParamKey.PetConditionType3) {
            int value = conf.condition[1];
            if (repSn > value) {
                return true;
            }
        } else if (conditionType == ParamKey.PetConditionType4) {
            ConfJobs confJob = ConfJobs.get(jobSn);
            for (int i = 1; i < conf.condition.length; i++) {
                if (confJob.type == conf.condition[i]) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 同伴皮肤解锁或升级
     */
    public void _msg_pet_skin_up_c2s(HumanObject humanObj, int petSn, int skinSn) {
        int skinLv = humanObj.operation.petData.petSkinLvMap.getOrDefault(skinSn, 0);
        ConfPetSkinlevel conf = ConfPetSkinlevel.get(skinSn, skinLv);
        if (conf == null) {
            Log.game.error("同伴皮肤配置不存在, skinSn={}, skinLv=0", skinSn);
            return;
        }
        if (conf.expend == null) {
            return;// 同伴皮肤已达最高级
        }
        ReasonResult rr = ProduceManager.inst().checkAndCostItem(humanObj, conf.expend[0], conf.expend[1], MoneyItemLogKey.同伴升级);
        if (!rr.success) {
            Inform.sendMsg_error(humanObj, rr.code);
            return;
        }
        humanObj.operation.petData.petSkinLvMap.put(skinSn, skinLv + 1);
        humanObj.operation.petData.savePetSkinLvMap(humanObj);
        PetManager.inst().updatePetPropCalc(humanObj);
        // 重新计算美观值
        CharmManager.reCalcCharmValue(humanObj, true);
        sendMsg_pet_update_s2c(humanObj, petSn);
    }

    /**
     * 同伴皮肤穿戴
     */
    public void _msg_pet_skin_equip_c2s(HumanObject humanObj, int petSn, int skinSn) {
        List<Define.p_passive_skill> updateList = new ArrayList<>(1);
        List<Define.p_passive_skill> deleteList = new ArrayList<>(1);
        int petTab = humanObj.getHuman2().getUsePetLineup();
        int petLevel = humanObj.operation.petData.petSnLvMap.getOrDefault(petSn, 0);// 同伴等级
        boolean isFight = humanObj.operation.petData.lineupMap.getOrDefault(petTab, new ArrayList<>(0)).contains(petSn);// 判断是否出战
        int effectType = JobsManager.inst().getPetSpecialEffectType(humanObj.getProfession());
        if (skinSn == 0) {
            // 脱下皮肤
            Map<Integer, Integer> petSkinMap = humanObj.operation.petData.petSkinLineupMap.get(petTab);
            if (petSkinMap != null) {
                if (isFight) {
                    // 出战还要考虑技能更新
                    updateList = SkillManager.inst().to_p_passive_skill(petSn, petLevel, 0, 0, effectType);// 新技能就是原来同伴的上阵技能
                    if (petSkinMap.containsKey(petSn)) {
                        int oldSkinSn = petSkinMap.get(petSn);// 原来的皮肤
                        int skinLevel = humanObj.operation.petData.petSkinLvMap.getOrDefault(oldSkinSn, 0);
                        deleteList = SkillManager.inst().to_p_passive_skill(petSn, petLevel, oldSkinSn, skinLevel, effectType);// 删除的技能就是原来的皮肤装备技能
                    }
                }
                petSkinMap.remove(petSn);// 移除皮肤穿戴
            }
        } else {
            // 穿戴皮肤
            if (!humanObj.operation.petData.petSkinLvMap.containsKey(skinSn)) {
                Log.game.error("玩家未解锁同伴皮肤, humanId={}, skinSn={}", humanObj.getHumanId(), skinSn);
                return;
            }
            ConfPetSkin confPetSkin = ConfPetSkin.get(skinSn);
            if (confPetSkin.pet != petSn) {
                Log.game.error("玩家选择皮肤不是该伙伴的皮肤, humanId={}, petSn={}, skinSn={}", humanObj.getHumanId(), petSn, skinSn);
                return;
            }
            Map<Integer, Integer> petSkinMap = humanObj.operation.petData.petSkinLineupMap.computeIfAbsent(petTab, k -> new HashMap<>());
            if (isFight) {
                // 出战还要考虑技能更新
                if (petSkinMap.containsKey(petSn)) {
                    int oldSkinSn = petSkinMap.get(petSn);
                    int skinLevel = humanObj.operation.petData.petSkinLvMap.getOrDefault(oldSkinSn, 0);
                    deleteList = SkillManager.inst().to_p_passive_skill(petSn, petLevel, oldSkinSn, skinLevel, effectType);
                } else {
                    deleteList = SkillManager.inst().to_p_passive_skill(petSn, petLevel, 0, 0, effectType);
                }
                int skinLevel = humanObj.operation.petData.petSkinLvMap.getOrDefault(skinSn, 0);
                updateList = SkillManager.inst().to_p_passive_skill(petSn, petLevel, skinSn, skinLevel, effectType);// 新技能就是原来同伴的上阵技能
            }
            petSkinMap.put(petSn, skinSn);
        }
        humanObj.operation.petData.savePetSkinLineupMap(humanObj);
        if (!updateList.isEmpty() || !deleteList.isEmpty()) {
            SkillManager.inst().sendMsg_skill_passive_update_s2c(humanObj, updateList, deleteList);
        }
        PetManager.inst().updatePetPropCalc(humanObj);
        sendMsg_pet_update_s2c(humanObj, petSn);
    }

    /**
     * 返回玩家的最大同伴数量
     */
    public int getPetPosMax(HumanObject humanObj) {
        int normalMax = PosMax - 1;
        if (humanObj == null) {
            return normalMax;
        }
        ConfJobs confJobs = ConfJobs.get(humanObj.getProfession());
        if (confJobs == null) {
            return normalMax;
        }
        if (isOpenPetPos(PosMax, humanObj.getHuman2().getRepSn(), humanObj.getProfession())) {
            return PosMax;
        }
        return normalMax;
    }

    /**
     * 处理职业重置, 清除掉最后一个同伴
     */
    public void handleJobReset(HumanObject humanObj) {
        Set<Integer> lineupSet = humanObj.operation.petData.lineupMap.keySet();
        // 职业重置，那就清除掉最后一个同伴，因为其他职业只有5个同伴
        for (Integer tab : lineupSet) {
            List<Integer> petSnList = humanObj.operation.petData.lineupMap.get(tab);
            while (petSnList.size() >= PosMax) {
                petSnList.remove(PosMax - 1);
            }
            sendMsg_pet_tab_info_s2c(humanObj, tab);
        }
        humanObj.operation.petData.saveData(humanObj);
        updatePetPropCalc(humanObj);
        sendMsg_pet_list_s2c(humanObj);
        checkPetPos(humanObj);
    }

    /**
     * 职业4的特殊处理，根据技能sn以及同伴上阵情况，获得对应的技能sn
     */
    public Map<Integer, Integer> getJobPetPassSkillMap(int skill, List<Integer> petSnList) {
        // 说明技能是第四职业的被动技能，那就要取同伴的种类来做技能转换
        List<Integer> skillList = GlobalConfVal.summonerSkillMap.get(skill);
        if (skillList == null || skillList.isEmpty()) {
            return null;
        }
        if (petSnList == null || petSnList.isEmpty()) {
            return null;
        }
        List<Integer> allPetRaceList = new ArrayList<>();
        Map<Integer, Integer> skillMap = new HashMap<>(0);
        for (Integer skillId : skillList) {
            ConfSummonerPassive conf = ConfSummonerPassive.get(skillId);
            if (conf.petrace == null) {
                continue;
            }
            List<Integer> petRaceList = Arrays.stream(conf.petrace).boxed().collect(Collectors.toList());
            allPetRaceList.addAll(petRaceList);
            int num = 0;
            // 计算前n个同伴的种类，决定是否触发技能
            for (int i = 0; i < petSnList.size() && i < conf.num; i++) {
                int petSn = petSnList.get(i);
                if (petSn <= 0) {
                    continue;
                }
                ConfPet confPet = ConfPet.get(petSn);
                for (int type : confPet.type) {
                    if (petRaceList.contains(type)) {
                        num++;
                        break;
                    }
                }
            }
            if (num == 0) {
                continue;
            }
            // 根据种类的数量决定触发被动
            for (int[] passSkills : conf.passive_skill) {
                if (passSkills[0] == num) {
                    for (int j = 1; j < passSkills.length; j += 2) {
                        int skillSn = passSkills[j];
                        int skillLv = skillMap.getOrDefault(skillSn, 0);
                        skillMap.put(skillSn, Math.max(passSkills[j + 1], skillLv));// 取最高的技能等级
                    }
                }
            }
        }
        if (skillMap.isEmpty()) {
            // 说明前面的技能都没生效，那就取无种族要求的技能，是配置在最后一个
            int id = skillList.get(skillList.size() - 1);
            ConfSummonerPassive conf = ConfSummonerPassive.get(id);
            int num = 0;
            // 计算前n个同伴的种类，决定是否触发技能
            for (int i = 0; i < petSnList.size() && i < conf.num; i++) {
                int petSn = petSnList.get(i);
                if (petSn <= 0) {
                    continue;
                }
                ConfPet confPet = ConfPet.get(petSn);
                for (int type : confPet.type) {
                    if (allPetRaceList.contains(type)) {
                        break;// 如果是前面技能的同伴种族那就跳过
                    }
                }
                num++;
            }
            if (num != 0) {
                for (int[] passSkills : conf.passive_skill) {
                    if (passSkills[0] == num) {
                        for (int j = 1; j < passSkills.length; j += 2) {
                            int skillSn = passSkills[j];
                            int skillLv = skillMap.getOrDefault(skillSn, 0);
                            skillMap.put(skillSn, Math.max(passSkills[j + 1], skillLv));// 取最高的技能等级
                        }
                    }
                }
            }
        }
        return skillMap;
    }

    /**
     * 职业4的特殊处理，根据技能sn以及同伴上阵情况，获得对应的技能sn
     */
    public Map<Integer, Integer> getJobPetPassSkillMap(HumanObject humanObj, List<Integer> petSnList) {
        Map<Integer, Integer> resultMap = new HashMap<>(0);
        Map<Integer, Integer> passiveSkillSnMap = Utils.jsonToMapIntInt(humanObj.operation.profession.getPassiveSkillMap());
        for (Map.Entry<Integer, Integer> entry : passiveSkillSnMap.entrySet()) {
            int skillSn = entry.getKey();
            if (GlobalConfVal.summonerSkillMap.containsKey(skillSn)) {
                Map<Integer, Integer> jobPetPassSkillMap = getJobPetPassSkillMap(skillSn, petSnList);
                for (Map.Entry<Integer, Integer> entry1 : jobPetPassSkillMap.entrySet()) {
                    int skillSn1 = entry1.getKey();
                    int skillLv = resultMap.getOrDefault(skillSn1, 0);
                    resultMap.put(skillSn1, Math.max(entry1.getValue(), skillLv));
                }
            }
        }
        return resultMap;
    }
}