package org.gof.demo.worldsrv.support;

import org.gof.demo.battlesrv.support.Vector2D;
import org.gof.demo.battlesrv.support.Vector3D;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.produce.RandomUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;


public class MapUtil {

	private static float ZERO = 0.00001f;
	
    /**
     * 向量转换参数
     */
    public static int VECTOR_EXPAND = 100;
    
	/**
	 * 向量值转换
	 * @param value
	 * @return
	 */
	public static int convertVectorVal(float value){
		return (int)(value*VECTOR_EXPAND);
	}
	
	/**
	 * 向量值转换
	 * @param value
	 * @return
	 */
	public static float convertVectorVal(int value){
		return (value*1.0f)/VECTOR_EXPAND;
	}
	
	/**
	 * 获取两个点的方向
	 * @param fromPos
	 * @param endPos
	 * @return
	 */
	public static Vector2D calDirection(Vector3D fromPos, Vector3D endPos){
		float diffX = endPos.getX()-fromPos.getX();
		float diffZ = endPos.getZ()-fromPos.getZ();
		return new Vector2D(diffX, diffZ);
	}
	
	public static Vector2D verticalPos(Vector2D vector2D) {
		return new Vector2D(-vector2D.y, vector2D.x);
	}
	
	public static Vector2D verticalNeg(Vector2D vector2D) {
		return new Vector2D(vector2D.y, -vector2D.x);
	}
	
	/**
	 * 计算两点间的距离
	 * 
	 * @param from
	 * @param end
	 * @return
	 */
	public static double distance(Vector3D from, Vector3D end){
		double xpow = Math.pow((end.getX()-from.getX()),2);
		double ypow = Math.pow((end.getY()-from.getY()),2);
		double zpow = Math.pow((end.getZ()-from.getZ()),2);
		return Math.sqrt((xpow+ypow+zpow));
	}
	

	
	/**
	 * 计算两点间的平面距离的平方
	 * 
	 * @param fromX
	 * @param fromZ
	 * @param toX
	 * @param toZ
	 * @return
	 */
	public static double flatDistanceSq(float fromX, float fromZ, float toX, float toZ){
		double xpow = Math.pow((toX-fromX),2);
		double zpow = Math.pow((toZ-fromZ),2);
		return xpow+zpow;
	}
	
	/**
	 * 计算两点间的平面距离
	 * 
	 * @param from
	 * @param toX
	 * @param toZ
	 * @return
	 */
	public static double flatDistance(Vector2D from, float toX, float toZ){
		double xpow = Math.pow((toX-from.x),2);
		double zpow = Math.pow((toZ-from.y),2);
		return Math.sqrt((xpow+zpow));
	}
	
	/**
	 * 计算两点间的平面距离
	 * 
	 * @param from
	 * @param end
	 * @return
	 */
	public static double flatDistance(Vector3D from, Vector3D end){
		double xpow = Math.pow((end.getX()-from.getX()),2);
		double zpow = Math.pow((end.getZ()-from.getZ()),2);
		return Math.sqrt((xpow+zpow));
	}
	
	/**
	 * 计算两点间的平面距离的平方
	 * 
	 * @param from
	 * @param end
	 * @return
	 */
	public static double flatDistanceSq(Vector3D from, Vector3D end){
		double xpow = Math.pow((end.getX()-from.getX()),2);
		double zpow = Math.pow((end.getZ()-from.getZ()),2);
		return xpow+zpow;
	}
	
		
	/**
	 * 获得旋转度数之后的位置
	 * @param center 圆心点
	 * @param from 旋转起始点 
	 * @param rotate 旋转度数
	 * @return
	 */
	public static Vector3D rotate(Vector3D center, Vector3D from, double rotate){
		Vector3D pos = new Vector3D();
		double radians = Math.PI*rotate/180;
		double sin = Math.sin(radians);
		double cos = Math.cos(radians);
		float diffX = (float)(from.x-center.x);
		float diffZ = (float)(from.z-center.z);
		float x = (float)(diffX*cos - diffZ*sin+center.x);
		float z = (float)(diffX*sin + diffZ*cos+center.z);
		pos.setX(x);
		pos.setY(center.getY());
		pos.setZ(z);
		return pos;
	}

	public static Vector3D rotate(Vector3D center, Vector2D dir, float dist, double rotate){
		Vector3D pos = new Vector3D();
		double radians = Math.PI*rotate/180;
		double sin = Math.sin(radians);
		double cos = Math.cos(radians);
		float diffX = (float)(dir.x * dist);
		float diffZ = (float)(dir.y * dist);
		float x = (float)(diffX*cos - diffZ*sin+center.x);
		float z = (float)(diffX*sin + diffZ*cos+center.z);
		pos.setX(x);
		pos.setY(center.getY());
		pos.setZ(z);
		return pos;
	}
	
	/**
	 * 根据二维向量坐标计算角度值
	 * @param X
	 * @param Y
	 * @return
	 */
	public static double calRotation(float X, float Y){
		double ratate = 0;		
		if(Y==0){
			ratate = X>=0 ? 0 : 180;
		}else if(X==0){
			ratate = Y>0 ? 90 : 270;
		} else {
			double cos = 0;
			if (X == 0) {
				cos = 0;
			} else {
				cos = X / Math.sqrt(Math.pow(X, 2) + Math.pow(Y, 2));
			}
			double radians = Math.acos(cos);
			ratate = radians * 180 / Math.PI;
			if (Y < 0) {
				ratate = 180+ratate;
			}
		}
		return ratate;
	}
	
	/**
	 * 获得通过方向和距离计算的新坐标
	 * @param from 起始点
	 * @param dir 方向向量
	 * @param distance 距离
	 * @return
	 */
	public static Vector3D transfer(Vector3D from, Vector2D dir, double distance){
		float dirY = (float)dir.y;
		if (dir.x==0&&dir.y ==0) {
			dirY = 1;
		}
		double len = Math.sqrt(Math.pow(dir.x, 2) + Math.pow(dirY, 2));
		double sin = dir.x / len;
		double cos = dirY / len;
		double difX = from.getX() + sin * distance;
		double difZ = from.getZ() + cos * distance;
		Vector3D insert = new Vector3D();
		insert.setX((float) difX);
		insert.setY(from.getY());
		insert.setZ((float) difZ);
		return insert;
	}

	public static Vector3D transfer(Vector3D from, int dirAngle, double distance){
		Vector2D dir = new Vector2D();
		setDirByAngle(dirAngle, dir);
		return transfer(from, dir, distance);
	}
	
	/**
	 * 获取通过方向和距离计算的新坐标
	 * @param from 起始点
	 * @param next 朝向目标点
	 * @param distance 距离
	 * @return
	 */
	public static Vector3D transferTo(Vector3D from, Vector3D next, double distance){
		float diffX = next.getX() - from.getX();
		float diffY = next.getY() - from.getY();
		if (diffX==0&&diffY==0) {
			diffY = 1;
		}
		double len = Math.sqrt(Math.pow(diffX, 2) + Math.pow(diffY, 2));
		double sin = diffX / len;
		double cos = diffY / len;
		double difX = from.getX() + sin * distance;
		double difY = from.getY() + cos * distance;
		Vector3D insert = new Vector3D();
		insert.setX((float) difX);
		insert.setY((float) difY);
		insert.setZ(from.getZ());
		return insert;
	}	
	
	/**
	 * 获取随机方向
	 * 
	 * @return
	 */
	public static Vector2D getRandomDir() {
		Vector2D direction = new Vector2D(RandomUtil.random(200)-100, RandomUtil.random(200)-100);
		return direction;
	}
	
	/**
	 * 获取方向在坐标轴的角度[以X轴正方向做起始方向顺时针旋转]
	 * 
	 * @param x
	 * @param y
	 * @return
	 */
	public static float getAngle(float x, float y) {
		float x0 = 0, y0 = 0; // 0点坐标为基准
		float a = x - x0;
		float b = y - y0;
		double z = Math.sqrt(a * a + b * b);
		float value = (float)(Math.asin(b / z) / Math.PI * 180);
		// X轴
		if (value == 0) {
			value = x >= 0 ? 0 : 180;
		} else if (value > 0) {
			// 第一象限
			if (x > 0) {
				value = 360 - Math.abs(value);
			}
			// 第二象限
			else if (x < 0) {
				value = Math.abs(value) + 180;
			}
			// Y轴
			else {
				value = 270;
			}
		} else {
			// 第四象限
			if (x > 0) {
				value = Math.abs(value);
			}
			// 第三象限
			else if (x < 0) {
				value = 180 - Math.abs(value);
			}
			// Y轴
			else {
				value = 90;
			}
		}
		return value;
	}
	
	/**
	 * 获取两个角度的夹角[通过a旋转至270度进行比较][0~180=正面，180~360位背面]
	 * 
	 * @param a 
	 * @param b
	 * @return
	 */
	public static float getIncludedAngle(float a, float b) {
		// b旋转后的值
		float value = b + 270 - a;
		if (value >= 360) {
			value = value % 360;
		} else if (value < 0){
			value = 360 + value;
		}
		return value;
	}
	
	/**
	 * 获取两个点的角度[以点pos1为准][0~180=正面，180~360位背面]
	 * 
	 * @param pos1
	 * @param pos2
	 * @return
	 */
	public static float getIncludedAngle(Vector2D pos1, Vector2D pos2) {
		float angle1 = getAngle((float)pos1.x, (float)pos1.y);
		float angle2 = getAngle((float)pos2.x, (float)pos2.y);
		return getIncludedAngle(angle1, angle2);
	}

	/**
	 * 获取选择之后方向
	 * 
	 * @param pos 方向
	 * @param angle 顺时针角度
	 * @return
	 */
	public static Vector2D rotate(Vector2D pos, double angle) {
		float x0 = 0, y0 = 0;
		float x = (float)pos.x, y = (float)pos.y;
		double angle_ =angle;
		x = (float)((x - x0) * Math.cos(angle_) + (y - y0) * Math.sin(angle_) + x0);
		y = (float)(-(x - x0) * Math.sin(angle_) + (y - y0) * Math.cos(angle_) + y0);
		return new Vector2D(x, y);
	}

	public static Vector2D rotate2(Vector2D pos, double angle) {
		double x = pos.x;
		double y = pos.y;
		//[x*cosA-y*sinA  x*sinA+y*cosA]
		angle = Math.toRadians(angle);
		double rx = x * Math.cos(angle) - y * Math.sin(angle);
		double ry = x * Math.sin(angle) + y * Math.cos(angle);
		return  new Vector2D(rx, ry);
	}

	/**
	 * 顺时针
	 * @param pos
	 * @param angle
	 * @return
	 */
	public static Vector2D rotate3(Vector2D pos, double angle) {
		double x = pos.x;
		double y = pos.y;
		//[x*cosA-y*sinA  x*sinA+y*cosA]
		double rx = x * Math.cos(angle) + y * Math.sin(angle);
		double ry = -x * Math.sin(angle) + y * Math.cos(angle);
		return  new Vector2D(rx, ry);
	}
	/**
	 * 获取选择之后方向
	 * 
	 * @param angle 基础角度
	 * @param angle 顺时针旋转角度
	 * @return
	 */
	public static int rotate(int angle, int delta) {
		return (angle+delta) % 360;
	}
	// 通过方向向量获取角度
	// (0~360)坐标系中逆时针方向
	public static int getAngleCCW(float x, float y) {
		if (x<0 && y>=0) {
			return 90-(int)(Math.atan2(y, x)*180.0/Math.PI)+360;
		} else {
			return 90-(int)(Math.atan2(y, x)*180.0/Math.PI);
		}
	}
	// 通过角度设置方向向量
	public static void setDirByAngle(int angle, Vector2D v) {
		Angle.setDirByAngle(angle, v);
	}
	public static boolean isZero(Vector2D v) {
		return (float)v.x>= -ZERO && (float)v.x <= ZERO && 
				(float)v.y >= -ZERO && (float)v.y <= ZERO;
	}
	public static boolean isZero(float v) {
		return v >= -ZERO && v <= ZERO;
	}

	public static List<Define.p_key_value> toKeyValueMsg(Map<Long, Long> map) {
		List<Define.p_key_value> result = new ArrayList<>();
		for (Map.Entry<Long, Long> entry : map.entrySet()) {
			Define.p_key_value.Builder msg = Define.p_key_value.newBuilder();
			msg.setK(entry.getKey());
			msg.setV(entry.getValue());
			result.add(msg.build());
		}
		return result;
	}

	public static List<Define.p_key_string> toKeyStringMsg(Map<Long, String> map) {
		List<Define.p_key_string> result = new ArrayList<>();
		for (Map.Entry<Long, String> entry : map.entrySet()) {
			Define.p_key_string.Builder msg = Define.p_key_string.newBuilder();
			msg.setK(entry.getKey());
			msg.setS(entry.getValue());
			result.add(msg.build());
		}
		return result;
	}
}
