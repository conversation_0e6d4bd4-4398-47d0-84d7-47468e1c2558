package org.gof.demo.worldsrv.support;

import org.gof.core.support.S;
import org.gof.core.support.Utils;

import java.util.Properties;

/**
 * 系统参数
 * 本类都是简单的参数配置，如果需要判断系统环境，
 * 比如是手游还是页游，则使用S.java判断
 */
public class C {
    public static final int BRIDGE_SERVER_ID;
	public static final int ADMIN_SERVER_ID;
	public static final int LEAGUE_SERVER_ID;
    //配置文件名称
	private static final String CONFIG_NAME =  "gofConfig.properties";
	private static final String CROSS_CONFIG_NAME = "crossConfig.properties";
	private static final String ADMIN_CONFIG_NAME = "adminConfig.properties";

	public static final int GAME_PLATFORM_ID;				//运营平台ID 最大91
	public static final int GAME_SERVER_ID;				//游戏区ID 最大9999
	public static final int CROSS_SERVER_ID_START;		//跨服开始ID
	public static final String GAME_I18N_KEY;				//游戏语言
	
	public static final boolean IS_OPENGM;				//启动GM命令
	
	public static final boolean DEBUG_ENABLE;				//启动DEBUG
	public static final int CLIENT_VERSION;				//客户端版本号 临时方案
	public static final String GAME_PLATFORM_NAME;		//平台名称

	static {
		//获取配置
		Properties prop;
		String configDir = System.getProperty("gofConfigDir");
		if(configDir!=null && !configDir.isEmpty()){
			if(!configDir.endsWith("/")){
				configDir=configDir+"/";
			}
		}else{
			configDir="";
		}
		if(org.gof.core.support.S.isAdmin){
			prop = Utils.readProperties(configDir+ADMIN_CONFIG_NAME);
		} else if(S.isCross){
			prop = Utils.readProperties(configDir+CROSS_CONFIG_NAME);
		} else {
			prop = Utils.readProperties(configDir+CONFIG_NAME);
		}
		GAME_PLATFORM_ID = Utils.intValue(prop.getProperty("game.platform.id"));
		int server_id_prefix = Utils.intValue(prop.getProperty("game.server.prefix"));
		GAME_SERVER_ID = Utils.intValue(prop.getProperty("game.server.id")) + server_id_prefix;
		CROSS_SERVER_ID_START = Utils.intValue(prop.getProperty("cross.serverId.start","9901"))+server_id_prefix;
		GAME_I18N_KEY = prop.getProperty("game.i18n.key");
		DEBUG_ENABLE = Boolean.parseBoolean(prop.getProperty("debug.enable"));
		CLIENT_VERSION = Utils.intValue(prop.getProperty("client.version"));
		IS_OPENGM = Boolean.parseBoolean(prop.getProperty("debug.is_opengm"));
		GAME_PLATFORM_NAME = prop.getProperty("game.platform.name");

		BRIDGE_SERVER_ID = Utils.intValue(prop.getProperty("bridge.server.id","0"));

		ADMIN_SERVER_ID = Utils.intValue(prop.getProperty("admin.server.id","0"));

		LEAGUE_SERVER_ID = Utils.intValue(prop.getProperty("league.server.id","0"));
	}
}