package org.gof.demo.worldsrv.support.enumKey;

/**
 * 资源道具界面类型
 * <AUTHOR>
 *
 */
public enum ResItemKey {

	buy(1),  //购买
	use(2);   //使用
		
	private ResItemKey(int type){
		this.type = type;
	} 
	
	private int type;
	
	public int getType() {
		return type;
	}
	
	public static ResItemKey getEnumByType(int type) {
		for(ResItemKey k : values()) {
			if(k.type == type)
				return k;
		}
		return null;
	}	
	
}
