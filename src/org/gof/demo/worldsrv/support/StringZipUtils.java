package org.gof.demo.worldsrv.support;


import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.lang.instrument.Instrumentation;
import java.util.zip.GZIPInputStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;

public class StringZipUtils {
    private static Instrumentation instrumentation;
//    /**
//     * 使用gzip进行压缩
//     */
//    public static String gzip(String primStr) {
//        if (primStr == null || primStr.length() == 0) {
//            return primStr;
//        }
//
//        ByteArrayOutputStream out = new ByteArrayOutputStream();
//
//        GZIPOutputStream gzip = null;
//        try {
//            gzip = new GZIPOutputStream(out);
//            gzip.write(primStr.getBytes());
//        } catch (IOException e) {
//            Log.temp.error("===e={}", e);
//        } finally {
//            if (gzip != null) {
//                try {
//                    gzip.close();
//                } catch (IOException e) {
//                    Log.temp.error("===e={}", e);
//                }
//            }
//        }
//
////return "";
//        return new sun.misc.BASE64Encoder().encode(out.toByteArray());
//    }

    /**
     * <p>Description:使用gzip进行解压缩</p>
     *
     * @param compressedStr
     * @return
     */
    public static String gunzip(String compressedStr) {
        if (compressedStr == null||compressedStr.equals("{}")) {
            return null;
        }

        ByteArrayOutputStream out = new ByteArrayOutputStream();
        ByteArrayInputStream in = null;
        GZIPInputStream ginzip = null;
        byte[] compressed = null;
        String decompressed = null;
        try {
            compressed = new sun.misc.BASE64Decoder().decodeBuffer(compressedStr);
            in = new ByteArrayInputStream(compressed);
            ginzip = new GZIPInputStream(in);

            byte[] buffer = new byte[1024];
            int offset = -1;
            while ((offset = ginzip.read(buffer)) != -1) {
                out.write(buffer, 0, offset);
            }
            decompressed = out.toString();
        } catch (IOException e) {
            Log.temp.error("===e={}", e);
        } finally {
            if (ginzip != null) {
                try {
                    ginzip.close();
                } catch (IOException e) {
                }
            }
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                }
            }
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                }
            }
        }

        return decompressed;
    }

    /**
     * 使用zip进行压缩
     *
     * @param str 压缩前的文本
     * @return 返回压缩后的文本
     */
    public static final String zip(String str) {
        if (str == null)
            return null;
        byte[] compressed;
        ByteArrayOutputStream out = null;
        ZipOutputStream zout = null;
        String compressedStr = null;
        try {
            out = new ByteArrayOutputStream();
            zout = new ZipOutputStream(out);
            zout.putNextEntry(new ZipEntry("0"));
            zout.write(str.getBytes());
            zout.closeEntry();
            compressed = out.toByteArray();
            compressedStr = new sun.misc.BASE64Encoder().encodeBuffer(compressed);
        } catch (IOException e) {
            compressed = null;
        } finally {
            if (zout != null) {
                try {
                    zout.close();
                } catch (IOException e) {
                }
            }
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                }
            }
        }
        return compressedStr;
    }

    /**
     * 使用zip进行解压缩
     *
     * @param compressedStr 压缩后的文本
     * @return 解压后的字符串
     */
    public static final String unzip(String compressedStr) {
        if (compressedStr == null) {
            return null;
        }
        if ((compressedStr.contains("{") && compressedStr.contains("}"))
                || (compressedStr.contains("[") && compressedStr.contains("]")) ){
            return compressedStr;
        }
        ByteArrayOutputStream out = null;
        ByteArrayInputStream in = null;
        ZipInputStream zin = null;
        String decompressed = null;
        try {
            byte[] compressed = new sun.misc.BASE64Decoder().decodeBuffer(compressedStr);
            out = new ByteArrayOutputStream();
            in = new ByteArrayInputStream(compressed);
            zin = new ZipInputStream(in);
            zin.getNextEntry();
            byte[] buffer = new byte[1024];
            int offset = -1;
            while ((offset = zin.read(buffer)) != -1) {
                out.write(buffer, 0, offset);
            }
            decompressed = out.toString();
        } catch (IOException e) {
            decompressed = null;
        } finally {
            if (zin != null) {
                try {
                    zin.close();
                } catch (IOException e) {
                }
            }
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                }
            }
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                }
            }
        }
        return decompressed;
    }

    public static void main(String[] args) {
        String str2 = "Hello, World!";
        System.out.println("String占用的内存: " + getObjectSize(str2) + " bytes");
        if(true){
            return;
        }

//        String str = "UEsDBBQACAgIABBBElkAAAAAAAAAAAAAAAABAAAAMKVXS3LbMAy9i9ZYkOA/Z8j0AI2ycFy7cerE\n" +
//                "ruVk2mZy9z5KskjJFutMswkFQCAePk/we7X6dbytbqq7+4qq/eKweMbDe12tN3V1I6mulo84CBwW\n" +
//                "6/7w3OBQV3WF8/fe6inKpBDxvH2LZx/f3T0/LI7xKSjh47uPTe/k9bBNXpbHXvqyaIX77eL36qC+\n" +
//                "yK9sWoM/O8i1ECbE+9f9pZtvONgoFfhjbW37P5qcgn5aZ3E9ducYRiv+AOTVz9fN/nbTHGMOgPut\n" +
//                "f/FHa0ATCZ9J1JnEfMRULo5dWnulEe7kFIEO78hgzi8LQiRbO4g166DP4pBSi/NYZHIQLgiVGIQx\n" +
//                "ZUmegpBipOA5hZpT+DlFCoj9IGeUaEY+d4M2My8kudX+JE455+TCZJUwrAd5FotSMoSkuJx4mUcZ\n" +
//                "VJLmt+axZzGOQQmbRZTLXfLEPolTllVuHS6KpbgszquepHMllxnUTGqzrKfuU90wLI+bt1U/Dvtd\n" +
//                "0zfjcdOPzfbUnu1Eo7RdRJ0ln1mKk6XIzNR1ZrpkFmNtfmxebi+QAbfali6aXr3dnYAsHvpTh8Sd\n" +
//                "XLKJYS3bs0L+uWOih47m4KOOl9QxLjRZHW+qM44YtFLzSN016KBW6OSxnkd6bls116MuLbEeziNR\n" +
//                "E1c+mrYZbPFyEa9yA14NtPN4TRmvUyW8unU7xjuLZ+pqgkddwGMHPNpl9VPAI+fqp4t4Yl7m8bB3\n" +
//                "1+OZ1IfVGI8u4lFywMNCF+qjyvWRxfowG1PGMwrZXAjZDyEHzkpgSi0liyEbWxwhxeHqEoTxNIH7\n" +
//                "RnhscUS8zkbElvDY/xoRI67GM63WZERcGY/I8LgSHl/GU6S4T428tUU8vjzyIus3X6JsUx75UByR\n" +
//                "8ImRD5NS6zGeUB55n+EJJTz/oDBRxsOfoDBRpLB2C5kCMsN6EEwCFO+ZB8RFQNOBnwCy0y4qAJIT\n" +
//                "QmhNP7qfVE3T7z7xcf0al4s7SUyKNBmy5MhTIHjAp0UygVmlJsBFCTFlIEIZCDsr0stMrAh7Khti\n" +
//                "S+wIqyBQIJ3gdQWXivB1UIaUJXxxUHk0IfpZS0IuNG7UhK7FJkIaFviphO8bvmuEPRkLCBgDHGqY\n" +
//                "sLVgoUByCcwAGjKOMLz4ZFtBVpJlsoqw5lnEb8k6sp5sICcITY05BjU5Tc6QAzxHzpMLBJ7wkjyT\n" +
//                "VwQO9Ia8JQ/0nnwgUGqQBK5Hr+NnDmocdxYdIxV8jzyujl0OP/4CUEsHCMWDYCuYAwAAsw4AAA==\n";

        String str = "UEsDBBQACAgIALJdDFkAAAAAAAAAAAAAAAABAAAAMHVUTXMaMQz9Lz6/gy1Z/si5x1x7KjksBJpN\n" +
                "IVB2ybTN5L9XNrthIRtmGORnfTw9ybyZ9Z/+3tz9eIA5NMdmZ+7M28Js2oW5c1iY1ZMaVo1mMxi7\n" +
                "To2FWRi1fw5ezwVz1hZ7+1psLrH73bLp9STeSQ4SRILCT92Q6XTcXlKt+gF9aSp42DZ/10f7zdL3\n" +
                "WB2Wp6HYv70abK3EctptBrh9LLArsJ18qs/YxPNmwvPpbBdGFX43MOvfp/Zw33a9KvJmXkuU+VXu\n" +
                "MDnR1YmvTvKuQjZ9lbTCwiRRsvh0zmTJDwHkk/JjJTMtUVHJ0V2VotJK1u9VSTemzDcA2wEIY4hi\n" +
                "Y4UqzwjSHMhzYJoD8wfrAVOibgaby+hlxnHEbmS7iDZGyxcyTutPldSLeTHdhV0OI+KnriMoM03Y\n" +
                "MIB8gfInyNnP0Nw4HNf1WfXt67ou0GHf1an2bV2w7XnG7WPVr2aoHnTlYc8e9uOa56+1VPerfbm/\n" +
                "3nUqeH0HXb3Y7s8JmmWtXSnwwAFmVX5Z5SftCGbZjMn40747Gz+NiJkv09G6x4/46Sweinvh4b/g\n" +
                "YQciKootGzglQuGWSAy3PJy3Mzwe3uufYtedp6GHzamq5UBgeAgCIhIylIG27whKyXk4gQtwES7B\n" +
                "ZejSksYQiEEeJFBSFEEJlMEW7MCaksEeLOAAjlAFOcNbeAdP8FrRw+ut9ufVwyqmXspcH4nPEAtx\n" +
                "EIIwRPMoJxHoX65ESIJkBIvgEAiBETyC8g8IESEhZEQLfSuREBnRIwpUqBgRE2JGskgOiZAYySMJ\n" +
                "UkDS7hNSRrbIDpmQGdkjC8qb9oWppaLiuq+C/gdQSwcIYy488XECAABvBgAA\n";


        System.out.println(unzip(str));
    }




    public static void premain(String args, Instrumentation inst) {
        instrumentation = inst;
    }

    public static long getObjectSize(Object object) {
        return instrumentation.getObjectSize(object);
    }

}
