package org.gof.demo.worldsrv.support;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class Log {
	public static Logger game = LoggerFactory.getLogger("GAME");
	public static Logger temp = LoggerFactory.getLogger("TEMP");
	public static Logger common = LoggerFactory.getLogger("COMMON");
	public static Logger fight = LoggerFactory.getLogger("FIGHT");
	public static Logger human = LoggerFactory.getLogger("HUMAN");
	public static Logger coreMsg = LoggerFactory.getLogger("CORE_MSG");
	public static Logger stageCommon = LoggerFactory.getLogger("STAGE_COMMON");
	public static Logger stageMove = LoggerFactory.getLogger("STAGE_MOVE");
	public static Logger general = LoggerFactory.getLogger("GENERAL");
	public static Logger inform = LoggerFactory.getLogger("INFORM");
	public static Logger chat = LoggerFactory.getLogger("CHAT");
	
	public static Logger guild = LoggerFactory.getLogger("GUILD");
	public static Logger task = LoggerFactory.getLogger("TASK");
	
	public static Logger farm = LoggerFactory.getLogger("FARM");
	
	public static Logger item = LoggerFactory.getLogger("ITEM");
	
	public static Logger buff = LoggerFactory.getLogger("BUFF");
	
	public static Logger instance = LoggerFactory.getLogger("INSTANCE");
	
	public static Logger rank = LoggerFactory.getLogger("RANK");
	
	public static Logger capture = LoggerFactory.getLogger("CAPTURE");
	public static Logger shop = LoggerFactory.getLogger("SHOP");

	public static Logger treasure = LoggerFactory.getLogger("TREASURE");
	
	public static Logger liveness = LoggerFactory.getLogger("LIVENESS");
	
	public static Logger team = LoggerFactory.getLogger("TEAM");
	public static Logger friend = LoggerFactory.getLogger("FRIEND");
	public static Logger skill = LoggerFactory.getLogger("SKILL");
	public static Logger activity = LoggerFactory.getLogger("ACTIVITY");
	public static Logger battlefield = LoggerFactory.getLogger("BATTLEFIELD");

	public static Logger battle = LoggerFactory.getLogger("BATTLE");
	public static Logger carPark = LoggerFactory.getLogger("CAR_PARK");
	public static Logger crossWar = LoggerFactory.getLogger("CROSS_WAR");
	public static Logger kungFuRace = LoggerFactory.getLogger("KUNG_FU_RACE");
	public static Logger back = LoggerFactory.getLogger("BACK");
	public static Logger accumulatedRecharge = LoggerFactory.getLogger("ACCUMULATED_RECHARGE");
	public static Logger backLamp = LoggerFactory.getLogger("BACK_LAMP");
	public static Logger charm = LoggerFactory.getLogger("CHARM");
	public static Logger family32 = LoggerFactory.getLogger("FAMILY_32");
    public static Logger logic = LoggerFactory.getLogger("LOGIC");
	public static Logger fish = LoggerFactory.getLogger("FISH");
	public static Logger god = LoggerFactory.getLogger("GOD");
}
