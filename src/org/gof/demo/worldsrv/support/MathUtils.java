package org.gof.demo.worldsrv.support;

import org.gof.core.support.RandomUtils;
import org.gof.core.support.SysException;
import org.gof.demo.battlesrv.support.Vector2D;
import org.gof.demo.worldsrv.stage.StageObject;
import org.gof.demo.worldsrv.support.pathFinding.HeightFinding;

import java.util.List;

public class MathUtils {

	public static double EPSILON = 1.0e-6;
	public static double COLLSION_ANGLE = 42.5;
	
	/**
	 * 计算 从起始点 到目标点是否产生碰撞
	 * @param originPos 起始点
	 * @param originDir 起始方向
	 * @param targetPos 目标点
	 * @param radius 碰撞体积
	 * @param angleArr
	 * @return
	 */
	public static boolean collisionDetect(Vector2D originPos, Vector2D originDir, Vector2D targetPos, double radius, List<Double> angleArr) {
		boolean result = false;
		//如果 AB 距离小于距离 并且角度
		double dis = originPos.distance(targetPos);
		if(dis < radius) {
			double angle = MathUtils.getRotateAngle(targetPos.x - originPos.x, targetPos.y - originPos.y,
					originDir.x, originDir.y
					);
			if(angle < COLLSION_ANGLE && angle >=0) {
				if(angleArr != null) {
					angleArr.add(angle);
				}
				return true;
			} else if(angle > (360 - COLLSION_ANGLE) && angle <=360) {
				if(angleArr != null) {
					angleArr.add(angle);
				}
				return true;
			}
		}
		
		return result;
	}
	
	/**
	 * 计算 从起始点 到目标点是否产生碰撞
	 * @param originPos 起始点
	 * @param originDir 起始方向
	 * @param targetPos 目标点
	 * @param radius 碰撞体积
	 * @param angleArr 返回起点到目标的角度
	 * @return
	 */
	public static boolean collisionDetectAll(Vector2D originPos, Vector2D originDir, Vector2D targetPos, double radius, List<Double> angleArr) {
		boolean result = false;
		//如果 AB 距离小于距离 并且角度
		double dis = originPos.distance(targetPos);
		if(dis < radius) {
			double angle = MathUtils.getRotateAngle(targetPos.x - originPos.x, targetPos.y - originPos.y,
					originDir.x, originDir.y
					);
			
			if(angle < COLLSION_ANGLE && angle >= 0) {
				result =  true;
			} else if(angle > (360 - COLLSION_ANGLE) && angle <=360) {
				result = true;
			}
			
			if(angleArr != null) {
				if(angle > 180) {
					angle = angle - 360;
				}
				angleArr.add(angle);
			}
		}
		
		return result;
	}
	
	
	
	public static Vector2D getDir(Vector2D ori, Vector2D tar) {
		return new Vector2D(tar.x - ori.x, tar.y - ori.y);
	}
	/**
	 * 获取向量p2到p1的逆时针旋转角，其中向量p1表示为x1，y1；向量p2表示为x2，y2
	 * 
	 * @param x1
	 * @param y1
	 * @param x2
	 * @param y2
	 * @return
	 */
	public static double getRotateAngle(double x1, double y1, double x2, double y2) {
		double dist, dot, degree, angle;
		if (x1 == x2 && y1 == y2) {
			return 0;
		}

		// normalize，标准化
		dist = Math.sqrt(x1 * x1 + y1 * y1);
		x1 /= dist;
		y1 /= dist;

		dist = Math.sqrt(x2 * x2 + y2 * y2);
		x2 /= dist;
		y2 /= dist;

		// dot product 向量点积
		dot = x1 * x2 + y1 * y2;
		if (Math.abs(dot - 1.0) <= EPSILON) {
			angle = 0.0;
		} else if (Math.abs(dot + 10) <= EPSILON) {
			angle = Math.PI;
		} else {
			double cross;
			angle = Math.acos(dot);

			// cross product 向量x乘
			cross = x1 * y2 - x2 * y1;

			// vector p2 is clockwise from vector p1
			// with respect to the origin (0.0)
			if (cross > 0) {
				angle = 2 * Math.PI - angle;
			}
		}

		degree = angle * 180.0 / Math.PI;
		return degree;
	}


	/**
	 *根据角度获取一个距离的数值。强制处理1米远 
	 * @param pos
	 * @param angle
	 * @return
	 */
	public static Vector2D getDir(Vector2D pos, float angle){
		return getDir(pos, new Vector2D(pos.x, pos.y+1), angle);
	}

	public static Vector2D getDir(Vector2D start,Vector2D end, float angle, double distance){
		Vector2D v = getDir(start, end, angle);
		Vector2D r = Vector2D.lookAtDis(start, v,start,distance);
		return r;
	}


	/**
	 *根据角度获取一个距离的数值.
	 * @param pos
	 * @param angle
	 * @return
	 */
	public static Vector2D getDir(Vector2D origin, Vector2D pos,  float angle){
		double tmpAngle = - Math.PI * angle / 180;
		double x = origin.x;
		double y = origin.y;
		double ax = pos.x;
		double ay = pos.y;
		ax = x + (ax-x)* Math.cos(tmpAngle) - (ay-y) * Math.sin(tmpAngle);
		ay = y + (ax-x)* Math.sin(tmpAngle) + (ay-y) * Math.cos(tmpAngle);
		return new Vector2D(ax, ay);
	}
	
	/**
	 * 根据圆心和起始半径，在圆环内随机一个点
	 * @param pos
	 * @param radiusMin
	 * @return
	 */
	public static Vector2D randomPosInCircle(Vector2D pos, double radiusMin, double radiusMax) {
		if(radiusMin > radiusMax) {
			throw new SysException("在圆环内随机点出错，小圆半径大于大圆半径");
		}
		//随机位置
		//角度
		int angle = RandomUtils.nextInt(360);
		//半径
		double r = radiusMin + RandomUtils.nextDouble() * (radiusMax - radiusMin);
		//计算位置
		return new Vector2D(pos.x + r * Math.sin(Math.PI * angle/180), pos.y + r * Math.cos(Math.PI * angle/180));
	}
	
	public static Vector2D randomPosInCircle(StageObject stageObj, Vector2D pos, double radiusMin, double radiusMax) {
		if(radiusMin > radiusMax) {
			throw new SysException("在圆环内随机点出错，小圆半径大于大圆半径");
		}
		//随机位置
		//角度
		int angle = stageObj.randUtils.nextInt(360);
		//半径
		double r = radiusMin + stageObj.randUtils.nextDouble() * (radiusMax - radiusMin);
		//计算位置
		return new Vector2D(pos.x + r * Math.sin(Math.PI * angle/180), pos.y + r * Math.cos(Math.PI * angle/180));
	}

	public static Vector2D randomPosInCircle(StageObject stageObj, Vector2D pos, double radiusMin, double radiusMax, int range) {
		if(radiusMin > radiusMax) {
			throw new SysException("在圆环内随机点出错，小圆半径大于大圆半径");
		}
		//随机位置
		//角度
		int angle = stageObj.randUtils.nextInt(range);
		//半径
		double r = radiusMin + stageObj.randUtils.nextDouble() * (radiusMax - radiusMin);
		//计算位置
		return new Vector2D(pos.x + r * Math.sin(Math.PI * angle/180), pos.y + r * Math.cos(Math.PI * angle/180));
	}
	
	
	public static Vector2D randomPosForGen(StageObject stageObj, Vector2D pos, double attDis, int idx, int max, int randomAngle){
		if(max<=0)
			return pos;
		
		int tmpR = stageObj.randUtils.nextInt(30) - 15;
		
		int angle = randomAngle + (idx-1) * 360 / max + tmpR;
		if(angle>360)
			angle -= 360;
		if(angle<0)
			angle +=360;

		//计算位置
		Vector2D r = new Vector2D(pos.x + attDis * Math.sin(Math.PI * angle/180), pos.y + attDis * Math.cos(Math.PI * angle/180));
		if(HeightFinding.posHeight(stageObj.sn, r).z<=0){
			r = randomPosInCircle(stageObj, pos, 0, attDis);
			
			if(HeightFinding.posHeight(stageObj.sn, r).z<=0)
				r = pos;	
		}

		return r;
	}

}
