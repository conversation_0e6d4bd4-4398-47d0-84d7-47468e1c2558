package org.gof.demo.worldsrv.support;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.interfaces.ISerilizable;
import org.gof.core.support.Param;
import org.gof.demo.worldsrv.inform.Inform;

import java.io.IOException;

/**
 * 需要错误原因的返回结果
 */
public class ReasonResult implements ISerilizable {
	public boolean success;
	public int code;
	public String reason = "";
	public Param param = new Param();
	
	public ReasonResult() {
	}
	
	public ReasonResult(boolean success) {
		super();
		this.success = success;
	}
	
	public ReasonResult(boolean success, String reason) {
		super();
		this.success = success;
		this.reason = reason;
	}

	public ReasonResult(boolean success, int code) {
		super();
		this.success = success;
		this.code = code;
	}
	
	@Override
	public void writeTo(OutputStream out) throws IOException {
		out.write(success);
		out.write(code);
		out.write(reason);
		out.write(param);
	}
	
	@Override
	public void readFrom(InputStream in) throws IOException {
		success = in.read();
		code = in.read();
		reason = in.read();
		param = in.read();
	}

	@Override
	public String toString() {
		return new ToStringBuilder(this)
				.append("success", success)
				.append("code", code)
				.append("reason", reason)
				.toString();
	}
	
	/**
	 * 设置参数(注意！！！这个方法每次都会新建对象，是覆盖的方式)
	 */
	public void setParam(Object...params){
		param = new Param(params);
	}
}
