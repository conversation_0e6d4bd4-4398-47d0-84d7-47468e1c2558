package org.gof.demo.worldsrv.support.recast;

//public class SharePathFinding {
//	private static ThreadLocal<HashMap<Integer, FindPathHandle>> M = new ThreadLocal<HashMap<Integer, FindPathHandle>>() {
//        @Override protected HashMap<Integer, FindPathHandle> initialValue() {
//            return new HashMap<Integer, FindPathHandle>();
//        }
//    };
//    private static FindPathHandle getHandle(int stageSn) {
//    	HashMap<Integer, FindPathHandle> m = M.get();
//		FindPathHandle h = m.get(stageSn);
//		if (h == null) {
//			h = new FindPathHandle(stageSn);
//			m.put(stageSn, h);
//		}
//		return h;
//    }
//
//    public static Vector3D posHeight(int stageSn,Vector2D vector2D) {
//		FindPathHandle h = getHandle(stageSn);
//		Vector3D v3d = new Vector3D();
//		v3d.x = vector2D.x;
//		v3d.y = vector2D.y;
//		v3d.z = MTPathFinding.posHeight(h, (float)vector2D.x, (float)vector2D.y, (float)1.5);
//		return v3d;
//	}
//
//	public static float posHeight(int stageSn, float x, float y, float z) {
//		FindPathHandle h = getHandle(stageSn);
//		return MTPathFinding.posHeight(h, x, y, z);
//	}
//	public static boolean autoHeight(int stageSn, Vector3D position) {
//		FindPathHandle h = getHandle(stageSn);
//		return MTPathFinding.autoHeight(h, position);
//	}
//}