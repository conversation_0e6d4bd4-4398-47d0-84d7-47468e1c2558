package org.gof.demo.worldsrv.support;

import com.google.protobuf.Message;
import org.gof.core.support.NodeAdapter;
import org.gof.core.support.S;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.human.HumanGlobalServiceProxy;

import java.util.List;

public class MessageUtils {
    public static void multicastMsg(List<Long> humanIdList, Message msg) {
        if (S.isBridge) {
            for (long humanId : humanIdList) {
                int serverId = Utils.getServerIdByHumanId(humanId);
                if (serverId == 0) {
                    return;
                }

                String nodeId = NodeAdapter.world(serverId);
                HumanGlobalServiceProxy.newInstance(nodeId).sendMsg(humanId, msg);
            }
        } else {
            for (long humanId : humanIdList) {
                HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance();
                prx.sendMsg(humanId, msg);
            }
        }
    }

    public static void sendMsg(long humanId, Message msg) {
        if (S.isBridge) {
            int serverId = Utils.getServerIdByHumanId(humanId);
            if (serverId == 0) {
                return;
            }

            String nodeId = NodeAdapter.world(serverId);
            HumanGlobalServiceProxy.newInstance(nodeId).sendMsg(humanId, msg);
        } else {
            HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance();
            prx.sendMsg(humanId, msg);
        }
    }

}
