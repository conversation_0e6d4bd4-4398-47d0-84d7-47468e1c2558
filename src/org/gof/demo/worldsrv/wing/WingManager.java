package org.gof.demo.worldsrv.wing;

import org.gof.core.support.ManagerBase;
import org.gof.core.support.Param;
import org.gof.core.support.Utils;
import org.gof.core.support.observer.Listener;
import org.gof.demo.battlesrv.manager.CombatChangeLog;
import org.gof.demo.battlesrv.manager.PropManager;
import org.gof.demo.battlesrv.msgHandler.SkillManager;
import org.gof.demo.battlesrv.support.PropCalc;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.charm.CharmManager;
import org.gof.demo.worldsrv.common.GameServiceProxy;
import org.gof.demo.worldsrv.config.*;
import org.gof.demo.worldsrv.entity.*;
import org.gof.demo.worldsrv.human.EModule;
import org.gof.demo.worldsrv.human.FuncOpenType;
import org.gof.demo.worldsrv.human.HumanManager;
import org.gof.demo.worldsrv.human.PlanVo;
import org.gof.demo.worldsrv.item.ItemManager;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgWing;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.ReasonResult;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;
import org.gof.demo.worldsrv.support.enumKey.NewsConditionTypeKey;
import org.gof.demo.worldsrv.support.observer.Event;
import org.gof.demo.worldsrv.support.observer.EventKey;
import org.gof.demo.worldsrv.task.TaskConditionTypeKey;

import java.math.BigDecimal;
import java.util.*;

public class WingManager extends ManagerBase {
    /**
     * 获取实例
     *
     * @return
     */
    public static WingManager inst() {
        return inst(WingManager.class);
    }
    public static final int TALENT_COST_SN = 1085;

    /**
     * 翅膀信息
     */
    public void handleWingInfoC2S(HumanObject humanObj) {
        Wing wing = humanObj.operation.wing;
        if(wing == null){
            humanObj.sendMsg(MsgWing.wing_info_s2c.newBuilder().setUse(0).setSkillUse(0));
            return;
        }

        MsgWing.wing_info_s2c.Builder msg = MsgWing.wing_info_s2c.newBuilder();
        msg.setUse(humanObj.getHuman2().getWingUse());
        msg.setSkillUse(getWingSn(humanObj.getHuman2().getWingSkillSnLv()));
        msg.setTab(wing.getCurrentTab());

        Map<Integer, List<Integer>> featherMap = Utils.jsonToMapIntListInt(wing.getFeatherMap());
        for (Map.Entry<Integer, List<Integer>> entry : featherMap.entrySet()) {
            Define.p_wing_feather.Builder dP = Define.p_wing_feather.newBuilder();
            dP.setCfgId(entry.getKey());
            dP.setLevel(entry.getValue().get(0));
            dP.setExp(entry.getValue().get(1));
            msg.addFeatherList(dP);
        }

        Map<Integer, Integer> skinLvMap = Utils.jsonToMapIntInt(wing.getSkinLvMap());
        for (Map.Entry<Integer, Integer> entry : skinLvMap.entrySet()) {
            Define.p_key_value.Builder dP = Define.p_key_value.newBuilder();
            dP.setK(entry.getKey());
            dP.setV(entry.getValue());
            msg.addSkinList(dP);
        }

        // 从二进制数据中解析天赋方案列表
        byte[] talentTabsData = wing.getTalentTabInfo();
        if (talentTabsData != null && talentTabsData.length > 0) {
            // 解析所有天赋方案
            List<Define.p_talent_tab_info> tabInfoList = Utils.deserializeProtoList(talentTabsData, Define.p_talent_tab_info.parser());
            if(tabInfoList != null){
                msg.addAllTabList(tabInfoList);
            }
            // 添加当前选中方案的天赋数据到talent_type_list
            int currentTab = wing.getCurrentTab();
            for (Define.p_talent_tab_info tabInfo : tabInfoList) {
                if (tabInfo.getTab() == currentTab) {
                    msg.addAllTalentTypeList(tabInfo.getTalentTypeListList());
                    break;
                }
            }
        } else {
            HumanManager.inst().initPlanTab(humanObj, PlanVo.TAB_WING_TALENT);
            // 兼容旧数据：将旧格式转换为新格式
            Map<Integer, Map<Integer, Integer>> talentMap = Utils.jsonToIntMapIntInt(wing.getTalentLvMap());

            // 创建默认方案
            Define.p_talent_tab_info.Builder defaultTab = Define.p_talent_tab_info.newBuilder();
            defaultTab.setTab(1);
            defaultTab.setName("");

            // 转换旧数据格式到新格式
            int totalCost = 0;
            for (Map.Entry<Integer, Map<Integer, Integer>> entry : talentMap.entrySet()) {
                Define.p_wing_talent.Builder talentType = Define.p_wing_talent.newBuilder();
                talentType.setType(entry.getKey());

                for (Map.Entry<Integer, Integer> entry1 : entry.getValue().entrySet()) {
                    Define.p_key_value.Builder talent = Define.p_key_value.newBuilder();
                    talent.setK(entry1.getKey());
                    talent.setV(entry1.getValue());
                    talentType.addTalentList(talent);
                    //从1级开始到当前等级,需要的材料累加到totalCost
                    for (int level = 1; level <= entry1.getValue(); level++) {
                        ConfBackTalent_0 confBackTalent_0 = ConfBackTalent_0.get(entry1.getKey(), level);
                        if (confBackTalent_0 != null) {
                            totalCost += confBackTalent_0.cost[1];
                        }
                    }
                }

                defaultTab.addTalentTypeList(talentType);
                msg.addTalentTypeList(talentType);
            }
            defaultTab.setCount(totalCost);
            int itemNum = ItemManager.inst().getItemNum(humanObj, TALENT_COST_SN);
            ProduceManager.inst().produceAdd(humanObj, TALENT_COST_SN, totalCost, MoneyItemLogKey.未设置);
            Log.game.error("===翅膀天赋重建，humanId={}, talentPointItemCount={}, talentLvMap={}, totalCost={}", humanObj.id, itemNum, wing.getTalentLvMap(), totalCost);
            wing.setTalentLvMap("");
            // 添加默认方案到方案列表
            msg.addTabList(defaultTab);

            // 保存新格式数据
            wing.setTalentTabInfo(Utils.serializeProtoList(Collections.singletonList(defaultTab.build())));
            wing.setCurrentTab(1);
            msg.setTab(1);
            updatePorpCalcPower(humanObj);
            updateTalentPropCalcPower(humanObj, defaultTab.build());
        }

        msg.addAllCollectionList(Utils.strToIntList(wing.getCollectionList()));
        humanObj.sendMsg(msg);
    }

    /**
     * 翅膀羽毛升级
     * @param cfgId 配置ID1
     * @param type 类型1:普通升级 2:一键升级
     */
    public void handleWingFeatherLevelUpC2S(HumanObject humanObj, int cfgId, int type) {
        Wing wing = humanObj.operation.wing;
        if(wing == null){
            return;
        }

        Map<Integer, List<Integer>> featherMap = Utils.jsonToMapIntListInt(wing.getFeatherMap());
        List<Integer> featherList = featherMap.get(cfgId);
        if(featherList == null){
            Log.game.error("翅膀羽毛升级配置错误，cfgId={}", cfgId);
            return;
        }


        int level = featherList.get(0);
        int oldLv = level;
        int exp = featherList.get(1);

        //获取配置,其中多少经验对应多少级
        ConfBackLevel_0 confBackLevel_0_next = ConfBackLevel_0.get(cfgId, level+1);
        if(confBackLevel_0_next == null){
            Log.game.error("翅膀羽毛升级配置错误，cfgId={},level={}", cfgId, level);
            return;
        }

        if(type == 1){
            ReasonResult result = ProduceManager.inst().checkAndCostItem(humanObj, confBackLevel_0_next.expend_goods[0], 1, MoneyItemLogKey.翅膀羽毛升级);
            if(result.success == false){
                return;
            }
            exp += 1;
            if(exp >= confBackLevel_0_next.expend_exp){
                level++;
            }
        }else {
            int itemNum = ItemManager.inst().getItemNum(humanObj, confBackLevel_0_next.expend_goods[0]);
            if(itemNum == 0){
                return;
            }
            int needExp = confBackLevel_0_next.expend_exp - exp;
            itemNum = Math.min(itemNum, needExp);
            ReasonResult result = ProduceManager.inst().checkAndCostItem(humanObj, confBackLevel_0_next.expend_goods[0], itemNum, MoneyItemLogKey.翅膀羽毛升级);
            if(!result.success){
                return;
            }
            exp += itemNum;
            if(exp >= confBackLevel_0_next.expend_exp){
                level++;
            }
        }
        List<Integer> list = featherMap.get(cfgId);
        list.set(0, level);
        list.set(1, exp);
        featherMap.put(cfgId, list);

        wing.setFeatherMap(Utils.mapIntListIntToJSON(featherMap));

        if(oldLv != level){
            Map<Integer,Integer> skinLvMap = Utils.jsonToMapIntInt(wing.getSkinLvMap());
            if(activeSkin(skinLvMap, featherMap)){
                wing.setSkinLvMap(Utils.mapIntIntToJSON(skinLvMap));
                // 重新计算美观值
                CharmManager.reCalcCharmValue(humanObj, true);
            }
            updatePorpCalcPower(humanObj);
        }
        wing.update();

        //发送消息
        MsgWing.wing_feather_level_up_s2c.Builder msg = MsgWing.wing_feather_level_up_s2c.newBuilder();
        Define.p_wing_feather.Builder dP = Define.p_wing_feather.newBuilder();
        dP.setCfgId(cfgId);
        dP.setLevel(level);
        dP.setExp(exp);
        msg.setFeather(dP);
        humanObj.sendMsg(msg);
    }

    public void updatePorpCalcPower(HumanObject humanObj){
        PropCalc propCalc = new PropCalc();
        int power = 0;
        Wing wing = humanObj.operation.wing;
        if(wing == null){
            return;
        }
        //羽毛属性
        int addLv = 0;//图鉴加成等级
        List<Integer> collectionList = Utils.strToIntList(wing.getCollectionList());
        for (Integer cfgId : collectionList) {
            ConfBackBook confBackBook = ConfBackBook.get(cfgId);
            if(confBackBook == null){
                continue;
            }
            addLv += confBackBook.level;
        }

        Map<Integer, List<Integer>> featherMap = Utils.jsonToMapIntListInt(wing.getFeatherMap());
        for (Map.Entry<Integer, List<Integer>> entry : featherMap.entrySet()) {
            ConfBackLevel_0 confBackLevel_0 = ConfBackLevel_0.get(entry.getKey(), entry.getValue().get(0)+addLv);
            if(confBackLevel_0 == null || confBackLevel_0.attr == null || confBackLevel_0.attr.length < 2){
                continue;
            }
            propCalc.plus(confBackLevel_0.attr[0], BigDecimal.valueOf(confBackLevel_0.attr[1]));
            power += confBackLevel_0.power;
        }
        //皮肤属性
        Map<Integer, Integer> skinLvMap = Utils.jsonToMapIntInt(wing.getSkinLvMap());
        for (Map.Entry<Integer, Integer> entry : skinLvMap.entrySet()) {
            ConfBackSkin_0 confBackSkin_0 = ConfBackSkin_0.get(entry.getKey(), entry.getValue());
            if(confBackSkin_0 == null || confBackSkin_0.attr == null || confBackSkin_0.attr.length < 2){
                continue;
            }
            propCalc.plus(confBackSkin_0.attr[0], BigDecimal.valueOf(confBackSkin_0.attr[1]));
            power += confBackSkin_0.power;
        }

        int snLv = humanObj.getHuman2().getWingSkillSnLv();
        ConfBackSkin_0 confBackSkin_0 = ConfBackSkin_0.get(getWingSn(snLv), getWingLv(snLv));
        if(confBackSkin_0 != null && confBackSkin_0.skin_skill != null){
            for (int i = 0; i < confBackSkin_0.skin_skill.length; i++) {
                ConfSkillLevel_0 confSkillLevel_0 = ConfSkillLevel_0.get(confBackSkin_0.skin_skill[i][0], confBackSkin_0.skin_skill[i][1]);
                if(confSkillLevel_0 == null){
                    continue;
                }
                if(confSkillLevel_0.attrType == null || confSkillLevel_0.attrType[0] != 1){
                    propCalc.plus(confSkillLevel_0.ownEffect);
                }
            }
        }

        humanObj.dataPers.unitPropPlus.setFieldRead(UnitPropPlus.K.wing, propCalc.toJSONStr());
        HumanManager.inst().updatePowerPar(humanObj, EModule.WING, power);
        PropManager.inst().propCalc(humanObj, CombatChangeLog.翅膀);
    }

    public void updateTalentPropCalcPower(HumanObject humanObj, Define.p_talent_tab_info currentTabInfo) {
        Wing wing = humanObj.operation.wing;
        if(wing == null){
            return;
        }
        // 获取当前方案信息
        if (currentTabInfo == null) {
            return;
        }
        PropCalc propCalc = new PropCalc();
        int power = 0;
        // 遍历所有天赋类型
        for (Define.p_wing_talent talentType : currentTabInfo.getTalentTypeListList()) {
            // 遍历每个类型下的所有天赋
            for (Define.p_key_value talent : talentType.getTalentListList()) {
                int cfgId = (int)talent.getK();
                int level = (int)talent.getV();

                // 获取天赋配置
                ConfBackTalent_0 confBackTalent_0 = ConfBackTalent_0.get(cfgId, level);
                if (confBackTalent_0 == null) {
                    continue;
                }

                // 累加战力
                power += confBackTalent_0.power;

                // 累加属性
                if (confBackTalent_0.attr == null || confBackTalent_0.attr.length < 2) {
                    continue;
                }
                propCalc.plus(confBackTalent_0.attr[0], BigDecimal.valueOf(confBackTalent_0.attr[1]));
            }
        }
        humanObj.dataPers.unitPropPlus.setFieldRead(UnitPropPlus.K.wingTalent, propCalc.toJSONStr());
        HumanManager.inst().updatePowerPar(humanObj, EModule.wingTalent, power);
        PropManager.inst().propCalc(humanObj, CombatChangeLog.翅膀天赋);
    }

    /**
     * 翅膀皮肤使用
     * @param cfgId 配置ID
     */
    public void handleWingUseC2S(HumanObject humanObj, int cfgId) {
        Wing wing = humanObj.operation.wing;
        if(wing == null){
            return;
        }
        Human2 human = humanObj.getHuman2();
        if(cfgId == 0){
            human.setWingUse(0);
            human.update();
            humanObj.sendMsg(MsgWing.wing_use_s2c.newBuilder().setUse(0));
            return;
        }
        Map<Integer, Integer> skinLvMap = Utils.jsonToMapIntInt(wing.getSkinLvMap());
        if(skinLvMap.get(cfgId) == null){

            Log.game.error("翅膀皮肤使用配置错误，cfgId={}", cfgId);
            return;
        }

        human.setWingUse(cfgId);
        human.update();
        humanObj.sendMsg(MsgWing.wing_use_s2c.newBuilder().setUse(cfgId));
        HumanManager.inst().checkCrossSyns(humanObj, HumanBrief.K.roleFigure);
    }

    private static boolean checkOtherColorTalentEnd(Map<Integer, Integer> talentSnMap, ConfBackTalent_0 confBackTalent_next) {
        //<颜色,是否达高级>
        Map<Integer,Boolean> colorEndNodeMap = new HashMap<>();
        for (Map.Entry<Integer, Integer> entry : talentSnMap.entrySet()) {
            ConfBackTalent_0 confBackTalent_0_1 = ConfBackTalent_0.get(entry.getKey(), 1);
            if(confBackTalent_0_1 == null){
                continue;
            }
            //存在相同颜色满足条件
            if(confBackTalent_0_1.color_type == confBackTalent_next.color_type){
                return true;
            }
            if(confBackTalent_0_1.connect_id==null || confBackTalent_0_1.connect_id.length==0){
                if(entry.getValue()>=1){
                    colorEndNodeMap.put(confBackTalent_0_1.color_type, true);
                }
            }else if(!colorEndNodeMap.containsKey(confBackTalent_0_1.color_type)){
                colorEndNodeMap.put(confBackTalent_0_1.color_type, false);
            }
        }

        //不存在要升级的颜色，其他颜色必须达到最高级
        for (Map.Entry<Integer, Boolean> entry : colorEndNodeMap.entrySet()) {
            if(!entry.getValue()){
                return false;
            }
        }
        return true;
    }

    /**
     * 翅膀技能使用
     * @param cfgId 配置ID
     */
    public void handleWingSkillUseC2S(HumanObject humanObj, int cfgId) {
        changeWingSkillUse(humanObj, cfgId);
    }

    public boolean changeWingSkillUse(HumanObject humanObj, int cfgId){
        Wing wing = humanObj.operation.wing;
        if(wing == null){
            return false;
        }
        Human2 human2 = humanObj.getHuman2();
        int oldCfgId = getWingSn(human2.getWingSkillSnLv());
        if(oldCfgId == cfgId){
            return false;
        }

        int wingSn = WingManager.inst().getWingSn(human2.getWingSkillSnLv());
        int wingLv = WingManager.inst().getWingLv(human2.getWingSkillSnLv());
        List<Define.p_passive_skill> delete = getPassSkill(wingSn, wingLv);
        Map<Integer, Integer> skinLvMap = Utils.jsonToMapIntInt(wing.getSkinLvMap());
        if(cfgId == 0){
            human2.setWingSkillSnLv(0);
            humanObj.sendMsg(MsgWing.wing_skill_use_s2c.newBuilder().setSkillUse(0));
            HumanManager.inst().setMountAtfWingTabSkill(humanObj, PlanVo.WING_INDEX,0);
            if(delete != null){
                SkillManager.inst().sendMsg_skill_passive_update_s2c(humanObj, null, delete);
            }
            updatePorpCalcPower(humanObj);
            return true;
        }

        if(skinLvMap.get(cfgId) == null){
            return false;
        }
        human2.setWingSkillSnLv(getWingSnLv(cfgId, skinLvMap.get(cfgId)));
        List<Define.p_passive_skill> update = getPassSkill(cfgId, skinLvMap.get(cfgId));
        List<Define.p_passive_skill> deleteList = new ArrayList<>();
        List<Define.p_passive_skill> updateList = new ArrayList<>();
        if(delete != null){
            deleteList.addAll(delete);
        }
        if(update != null){
            updateList.addAll(update);
        }
        SkillManager.inst().sendMsg_skill_passive_update_s2c(humanObj, updateList, deleteList);
        humanObj.sendMsg(MsgWing.wing_skill_use_s2c.newBuilder().setSkillUse(cfgId));
        HumanManager.inst().setPlanTab(humanObj, PlanVo.TAB_WING, cfgId);
        updatePorpCalcPower(humanObj);
        return true;
    }

    public int getWingSnLv(int sn, int lv){
        	return sn * 100 + lv;
    }

    public int getWingSn(int snLv){
        return snLv / 100;
    }

    public int getWingLv(int snLv){
        return snLv % 100;
    }

    public List<Define.p_passive_skill> getPassSkill(int sn, int lv) {
        List<Define.p_passive_skill> list = new ArrayList<>();
        ConfBackSkin_0 confBackSkin_0 = ConfBackSkin_0.get(sn, lv);
        if(confBackSkin_0 == null||confBackSkin_0.skin_skill == null){
            return list;
        }
        for (int i = 0; i < confBackSkin_0.skin_skill.length; i++) {
            Define.p_passive_skill.Builder dP = Define.p_passive_skill.newBuilder();
            dP.setSkillId(confBackSkin_0.skin_skill[i][0]);
            dP.setSkillLv(confBackSkin_0.skin_skill[i][1]);
            list.add(dP.build());
        }
        return list;
    }

    public List<Define.p_passive_skill> getTalenPassSkill(String json) {
        List<Define.p_passive_skill> list = new ArrayList<>();
        if(json == null){
            return list;
        }
        Map<Integer, Integer> map = Utils.jsonToMapIntInt(json);
        for (Map.Entry<Integer, Integer> entry : map.entrySet()) {
            Define.p_passive_skill.Builder dP = Define.p_passive_skill.newBuilder();
            dP.setSkillId(entry.getKey());
            dP.setSkillLv(entry.getValue());
            list.add(dP.build());
        }
        return list;
    }

    public List<Define.p_passive_skill> getTalenPassSkill(Wing wing, int tab) {
        List<Define.p_passive_skill> list = new ArrayList<>();
        if(wing == null){
            return list;
        }
        byte[] talentTabsData = wing.getTalentTabInfo();
        if (talentTabsData != null && talentTabsData.length > 0) {
            // 解析所有天赋方案
            List<Define.p_talent_tab_info> tabInfoList = Utils.deserializeProtoList(talentTabsData, Define.p_talent_tab_info.parser());
            for (Define.p_talent_tab_info tabInfo : tabInfoList) {
                if (tabInfo.getTab() == tab) {
                    for (Define.p_wing_talent talentType : tabInfo.getTalentTypeListList()) {
                        for (Define.p_key_value talent : talentType.getTalentListList()) {
                            int talentSn = (int)talent.getK();
                            int talentLv = (int)talent.getV();
                            ConfBackTalent_0 confBackTalent_0 = ConfBackTalent_0.get(talentSn, talentLv);
                            if (confBackTalent_0 == null || confBackTalent_0.skill == null || confBackTalent_0.skill.length < 2) {
                                continue;
                            }
                            Define.p_passive_skill.Builder dP = Define.p_passive_skill.newBuilder();
                            dP.setSkillId(confBackTalent_0.skill[0]);
                            dP.setSkillLv(confBackTalent_0.skill[1]);
                            list.add(dP.build());
                        }
                    }
                    break;
                }
            }
        }
        return list;
    }

    /**
     * 翅膀皮肤升级
     * @param cfgId 配置ID
     */
    public void handleWingSkinLevelUpC2S(HumanObject humanObj, int cfgId) {
        Wing wing = humanObj.operation.wing;
        if(wing == null){
            createWing(humanObj);
        }

        Map<Integer, Integer> skinLvMap = Utils.jsonToMapIntInt(wing.getSkinLvMap());
        Integer level = skinLvMap.getOrDefault(cfgId, 0);
        ConfBackSkin_0 confBackSkin_0 = ConfBackSkin_0.get(cfgId, level);
        if(confBackSkin_0 == null || confBackSkin_0.expend == null || confBackSkin_0.expend.length == 0){
            return;
        }

        ReasonResult result = ProduceManager.inst().checkAndCostItem(humanObj, confBackSkin_0.expend[0], confBackSkin_0.expend[1], MoneyItemLogKey.翅膀皮肤升级);
        if(result.success == false){
            return;
        }
        level++;
        skinLvMap.put(cfgId, level);
        wing.setSkinLvMap(Utils.mapIntIntToJSON(skinLvMap));
        wing.update();
        updatePorpCalcPower(humanObj);
        // 重新计算美观值
        CharmManager.reCalcCharmValue(humanObj, true);
        if(level == 1){
            humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_成就任务, TaskConditionTypeKey.TASK_TYPE_121);
            Event.fire(EventKey.UPDATE_NEWS, "humanObj", humanObj, "newsCondition", NewsConditionTypeKey.WingUnlock, "value", cfgId);
        }
        MsgWing.wing_skin_level_up_s2c.Builder msg = MsgWing.wing_skin_level_up_s2c.newBuilder();
        Define.p_key_value.Builder dP = Define.p_key_value.newBuilder();
        dP.setK(cfgId);
        dP.setV(level);
        msg.setSkin(dP);
        humanObj.sendMsg(msg);

        ConfBackSkin_0 confNew = ConfBackSkin_0.get(cfgId, level);

        if(getWingSn(humanObj.getHuman2().getWingSkillSnLv()) == cfgId && confNew.skin_skill != null){

            Map<Integer, Integer> delSkillMap = new HashMap<>();
            List<Define.p_passive_skill> deleteList = new ArrayList<>();
            for (int i = 0; i < confBackSkin_0.skin_skill.length; i++) {
                delSkillMap.put(confBackSkin_0.skin_skill[i][0], confBackSkin_0.skin_skill[i][1]);
            }

            humanObj.getHuman2().setWingSkillSnLv(getWingSnLv(cfgId, level));
            humanObj.getHuman().update();
            List<Define.p_passive_skill> update = new ArrayList<>();
            for (int i = 0; i < confNew.skin_skill.length; i++) {
                Define.p_passive_skill.Builder builder = Define.p_passive_skill.newBuilder();
                int skillId = confNew.skin_skill[i][0];
                builder.setSkillId(skillId);
                builder.setSkillLv(confNew.skin_skill[i][1]);
                update.add(builder.build());
                if(delSkillMap.containsKey(skillId)){
                    delSkillMap.remove(skillId);
                }
            }
            for(Map.Entry<Integer, Integer> entry : delSkillMap.entrySet()){
                Define.p_passive_skill.Builder builder = Define.p_passive_skill.newBuilder();
                builder.setSkillId(entry.getKey());
                builder.setSkillLv(entry.getValue());
                deleteList.add(builder.build());
            }
            if(update.isEmpty() && deleteList.isEmpty()){
                return;
            }
            SkillManager.inst().sendMsg_skill_passive_update_s2c(humanObj, update, deleteList);
            updatePorpCalcPower(humanObj);
        }
    }

    private boolean activeSkin(Map<Integer,Integer> skinMap, Map<Integer,List<Integer>> featherMap){
        //获得羽毛的最小等级
        int minLv = Integer.MAX_VALUE;
        for (Map.Entry<Integer, List<Integer>> entry : featherMap.entrySet()) {
            if(entry.getValue().get(0) < minLv){
                minLv = entry.getValue().get(0);
            }
        }

        ConfGlobal confGlobal = ConfGlobal.get(501);
        boolean isChange = false;
        for(int i=0;i<confGlobal.strArray.length;i++){
            String[] strArray = confGlobal.strArray[i].split(",");
            int cfgId = Utils.intValue(strArray[0]);
            int limitLv = Utils.intValue(strArray[1]);
            if(minLv >= limitLv && skinMap.get(cfgId) == null){
                skinMap.put(cfgId, 0);
                isChange = true;
            }
        }
        return isChange;
    }

    /**
     * 翅膀图鉴激活
     * @param cfgId 配置ID
     */
    public void handleWingCollectionActiveC2S(HumanObject humanObj, int cfgId) {
        Wing wing = humanObj.operation.wing;
        if(wing == null){
            return;
        }

        Map<Integer, Integer> skinLvMap = Utils.jsonToMapIntInt(wing.getSkinLvMap());
        if(skinLvMap.get(cfgId) != null){
            return;
        }

        ConfBackBook confBackBook = ConfBackBook.get(cfgId);
        if(confBackBook == null || confBackBook.condition == null || confBackBook.condition.length == 0){
            Log.game.error("翅膀图鉴激活配置错误，cfgId={}", cfgId);
            return;
        }
        //skinLvMap是否包含图鉴所有元素
        for (int i = 0; i < confBackBook.condition.length; i++) {
            if(skinLvMap.get(confBackBook.condition[i]) == null){
                return;
            }
        }
        List<Integer> collectionList = Utils.strToIntList(wing.getCollectionList());
        collectionList.add(cfgId);
        wing.setCollectionList(Utils.listToString(collectionList));
        wing.update();

        updatePorpCalcPower(humanObj);

        humanObj.sendMsg(MsgWing.wing_collection_active_s2c.newBuilder().setCfgId(cfgId));
    }

    @Listener(EventKey.FUNCTION_OPEN)
    public void unlock(Param param) {
        HumanObject humanObj = param.get("humanObj");
        List<Integer> snList = Utils.getParamValue(param, "openList", new ArrayList<>());
        if (!snList.contains(FuncOpenType.FUNC_WING)) {
            return;
        }
        if (humanObj.operation.wing != null) {
            return;
        }
//        if (!humanObj.isModUnlock(FuncOpenType.FUNC_WING)) {
//            return;
//        }
        createWing(humanObj);
        WingManager.inst().handleWingInfoC2S(humanObj);
    }

    @Listener(EventKey.FUNCTION_OPEN_LOGIN)
    public void _listener_FUNC_OPEN_LOGIN(Param param) {
        HumanObject humanObj = param.get("humanObj");
        int sn = Utils.getParamValue(param, "sn", 0);
        if (sn != FuncOpenType.FUNC_ARTIFACT) {
            return;
        }
        if (humanObj.operation.wing != null) {
            return;
        }
        if (!humanObj.isModUnlock(FuncOpenType.FUNC_ARTIFACT)) {
            return;
        }
        createWing(humanObj);
    }

    private void createWing(HumanObject humanObj) {
        Wing wing = new Wing();
        wing.setId(humanObj.id);
        humanObj.operation.wing = wing;
        Map<Integer, List<Integer>> featherMap = new HashMap<>();
        //羽毛的id聚合
        int[] featherIds = ConfGlobal.get(501).intArray;
        for (int i = 0; i < featherIds.length; i++) {
            List<Integer> list = new java.util.ArrayList<>();
            list.add(1);
            list.add(0);
            featherMap.put(featherIds[i], list);
        }
        wing.setFeatherMap(Utils.mapIntListIntToJSON(featherMap));
        updatePorpCalcPower(humanObj);

        wing.persist();
    }

    /**
     * 获取当前选中方案的天赋数据
     */
    private Define.p_talent_tab_info getTabTalentInfo(Wing wing, int tab) {
        Define.p_talent_tab_info result = null;

        byte[] talentTabsData = wing.getTalentTabInfo();
        if (talentTabsData != null && talentTabsData.length > 0) {
            List<Define.p_talent_tab_info> tabInfoList = Utils.deserializeProtoList(talentTabsData, Define.p_talent_tab_info.parser());

            for (Define.p_talent_tab_info tabInfo : tabInfoList) {
                if (tabInfo.getTab() == tab) {
                    // 找到当前选中的方案
                    result = tabInfo;
                    break;
                }
            }
        }
        return result;
    }

    private Define.p_talent_tab_info.Builder createTabTalentInfo(int tab) {
        // 创建方案
        Define.p_talent_tab_info.Builder tabInfoBuilder = Define.p_talent_tab_info.newBuilder();
        tabInfoBuilder.setTab(tab);
        tabInfoBuilder.setName("");
        return tabInfoBuilder;
    }


    /**
     * 更新当前选中方案的天赋数据
     */
    private void updateCurrentTabTalentInfo(Wing wing, Define.p_talent_tab_info updatedTabInfo, int tab) {
        byte[] talentTabsData = wing.getTalentTabInfo();
        List<Define.p_talent_tab_info> tabInfoList;
        if (talentTabsData != null && talentTabsData.length > 0) {
            tabInfoList = Utils.deserializeProtoList(talentTabsData, Define.p_talent_tab_info.parser());
        } else {
            tabInfoList = new ArrayList<>();
        }

        boolean found = false;
        List<Define.p_talent_tab_info> updatedTabInfoList = new ArrayList<>();

        // 遍历现有tab，替换或保留
        for (Define.p_talent_tab_info tabInfo : tabInfoList) {
            if (tabInfo.getTab() == tab) {
                updatedTabInfoList.add(updatedTabInfo); // 替换为新的
                found = true;
            } else {
                updatedTabInfoList.add(tabInfo); // 保留其他tab
            }
        }

        // 如果是新tab则添加
        if (!found) {
            updatedTabInfoList.add(updatedTabInfo);
        }

        wing.setTalentTabInfo(Utils.serializeProtoList(updatedTabInfoList));
    }
    /**
     * 翅膀天赋升级
     */
    public void handleWingTalentLevUpC2S(HumanObject humanObj, int tab, int type, int cfgId) {
        Wing wing = humanObj.operation.wing;
        if (wing == null) {
            return;
        }
        if (checkTab(tab)) {
            return;
        }
        // 获取当前方案信息
        Define.p_talent_tab_info tabInfo = getTabTalentInfo(wing, tab);
        Define.p_talent_tab_info.Builder tabInfoBuilder;
        if(tabInfo != null){
            tabInfoBuilder = tabInfo.toBuilder();
        }else {
            tabInfoBuilder = createTabTalentInfo(tab);
        }

        Map<Integer, Integer> talentTypeMap = getCurrentTabTalentTypeMap(tabInfoBuilder, type);

        int level = talentTypeMap.getOrDefault(cfgId, 0);
        ConfBackTalent_0 confBackTalent_0 = ConfBackTalent_0.get(cfgId, level + 1);
        if (confBackTalent_0 == null || confBackTalent_0.job_type != type) {
            Log.game.error("翅膀天赋升级配置错误，cfgId={},level={}, type={}", cfgId, level, type);
            return;
        }

        int totalCount = ItemManager.inst().getItemNum(humanObj, TALENT_COST_SN);
        int currentCount = totalCount - tabInfoBuilder.getCount();
        if(currentCount < confBackTalent_0.cost[1]){
            Log.game.error("翅膀天赋升级点数不足，cfgId={},level={}", cfgId, level);
            return;
        }

        if(!checkOtherColorTalentEnd(talentTypeMap, confBackTalent_0)){
            Log.game.error("翅膀天赋升级其他颜色天赋未满级，cfgId={},level={}", cfgId, level);
            return;
        }
        ConfBackTalent_0 confBackTalent_0_1 = ConfBackTalent_0.get(cfgId, 1);
        if(confBackTalent_0_1.connect_id == null || confBackTalent_0_1.connect_id.length == 0) {
            // 最多只能升级4个节点
            int endCount = getTabEndTalentCount(tabInfoBuilder);
            if (endCount >= ConfGlobal.get("BackTalent_max").value) {
                Log.game.error("翅膀天赋升级已达上限，cfgId={},level={}", cfgId, level);
                return;
            }
        }
        if(confBackTalent_0.condition_1 != null && confBackTalent_0.condition_1.length > 0) {
            for (int i = 0; i < confBackTalent_0.condition_1.length; i++) {
                if (talentTypeMap.get(confBackTalent_0.condition_1[i][0]) < confBackTalent_0.condition_1[i][1]) {
                    Log.game.error("翅膀天赋升级前置条件未满足，cfgId={},level={}", cfgId, level);
                    return;
                }
            }
        }

        tabInfoBuilder.setCount(tabInfoBuilder.getCount() + confBackTalent_0.cost[1]);

        level += 1;
        talentTypeMap.put(cfgId, level);
        //遍历currentTabInfoBuilder移出指定type的天赋
        int index = -1;
        for (int i = 0; i < tabInfoBuilder.getTalentTypeListCount(); i++) {
            if (tabInfoBuilder.getTalentTypeList(i).getType() == type) {
                index = i;
                break;
            }
        }
        if (index != -1) {
            tabInfoBuilder.removeTalentTypeList(index);
        }
        Define.p_wing_talent.Builder talentTypeBuilder = Define.p_wing_talent.newBuilder();
        talentTypeBuilder.setType(type);
        for (Map.Entry<Integer, Integer> entry : talentTypeMap.entrySet()) {
            Define.p_key_value.Builder talentInfo = Define.p_key_value.newBuilder();
            talentInfo.setK(entry.getKey());
            talentInfo.setV(entry.getValue());
            talentTypeBuilder.addTalentList(talentInfo);
        }
        tabInfoBuilder.addTalentTypeList(talentTypeBuilder.build());

        // 更新当前方案的天赋数据

        tabInfo = tabInfoBuilder.build();
        updateCurrentTabTalentInfo(wing, tabInfo, tab);

        // 更新属性
        if (wing.getCurrentTab() == tab) {
            updateTalentPropCalcPower(humanObj, tabInfo);
            // 处理天赋技能更新
            Map<Integer, Integer> oldSkillMap = Utils.jsonToMapIntInt(humanObj.getHuman2().getWingPassiveMap());
            Map<Integer, Integer> newSkillMap = new HashMap<>();
            // 遍历所有天赋类型获取新的技能
            for (Define.p_wing_talent talentType : tabInfo.getTalentTypeListList()) {
                for (Define.p_key_value talent : talentType.getTalentListList()) {
                    ConfBackTalent_0 conf = ConfBackTalent_0.get((int)talent.getK(), (int)talent.getV());
                    if (conf != null && conf.skill != null && conf.skill.length >= 2) {
                        newSkillMap.put(conf.skill[0], conf.skill[1]);
                    }
                }
            }
            updateTalentSkills(humanObj, oldSkillMap, newSkillMap);
        }

        // 发送消息
        MsgWing.wing_talent_lev_up_s2c.Builder msg = MsgWing.wing_talent_lev_up_s2c.newBuilder();
        msg.setTab(tab);
        msg.setCount(tabInfoBuilder.getCount());
        msg.setType(type);

        Define.p_key_value.Builder dP = Define.p_key_value.newBuilder();
        dP.setK(cfgId);
        dP.setV(level);
        msg.setTalent(dP);

        humanObj.sendMsg(msg);
    }

    private boolean checkTab(int tab){
        return tab < 1 || tab > ConfGlobal.get("back_talent_tab_num").value;
    }

    private Map<Integer, Integer> getCurrentTabTalentTypeMap(Define.p_talent_tab_info.Builder currentTabInfo, int type) {
        Map<Integer, Integer> talentTypeMap = new HashMap<>();
        for (Define.p_wing_talent talent : currentTabInfo.getTalentTypeListList()) {
            if (talent.getType() == type) {
                for (Define.p_key_value talentInfo : talent.getTalentListList()) {
                    talentTypeMap.put((int)talentInfo.getK(), (int)talentInfo.getV());
                }
                return talentTypeMap;
            }
        }
        return talentTypeMap;
    }

    private int getTabEndTalentCount(Define.p_talent_tab_info.Builder tabInfoBuilder) {
        int endCount = 0;
        for (Define.p_wing_talent talentType : tabInfoBuilder.getTalentTypeListList()) {
            for (Define.p_key_value talent : talentType.getTalentListList()) {
                ConfBackTalent_0 confBackTalent_0 = ConfBackTalent_0.get((int)talent.getK(), 1);
                if (confBackTalent_0 != null && confBackTalent_0.connect_id == null) {
                    endCount++;
                }
            }
        }
        return endCount;
    }

    /**
     * 翅膀天赋重置
     */
    public void handleWingTalentResetC2S(HumanObject humanObj, int tab, int type) {
        Wing wing = humanObj.operation.wing;
        if (wing == null) {
            return;
        }

        if (checkTab(tab)) {
            return;
        }

        Define.p_talent_tab_info tabInfo = getTabTalentInfo(wing, tab);
        Define.p_talent_tab_info.Builder tabInfoBuilder;
        if (tabInfo != null) {
            tabInfoBuilder = tabInfo.toBuilder();
        } else {
            return;
        }

        Map<Integer, Integer> talentTypeMap = getCurrentTabTalentTypeMap(tabInfoBuilder, type);

        if (talentTypeMap == null || talentTypeMap.isEmpty()) {
            return;
        }

        // 计算该type使用的总点数
        if (tabInfoBuilder.getTalentTypeListCount() == 1 && tabInfoBuilder.getTalentTypeList(0).getType() == type) {
            tabInfoBuilder.setCount(0);
        } else {
            int usedPoints = 0;
            for (Map.Entry<Integer, Integer> entry : talentTypeMap.entrySet()) {
                int talentId = entry.getKey();
                int level = entry.getValue();
                // 累加从1级到当前等级所需的所有点数
                for (int i = 1; i <= level; i++) {
                    ConfBackTalent_0 conf = ConfBackTalent_0.get(talentId, i);
                    if (conf != null) {
                        usedPoints += conf.cost[1];
                    }
                }
            }

            // 返还该type使用的点数
            tabInfoBuilder.setCount(tabInfoBuilder.getCount() - usedPoints);
        }

        // 清空该类型的天赋
        for (int i = 0; i < tabInfoBuilder.getTalentTypeListCount(); i++) {
            Define.p_wing_talent talentType = tabInfoBuilder.getTalentTypeList(i);
            if (talentType.getType() == type) {
                // 找到要清空的类型，从列表中移除
                tabInfoBuilder.removeTalentTypeList(i);
                break;
            }
        }

        // 更新当前方案的天赋数据
        Define.p_talent_tab_info currentTabInfo = tabInfoBuilder.build();
        updateCurrentTabTalentInfo(wing, currentTabInfo, tab);

        // 更新属性
        if (wing.getCurrentTab() == tab) {
            updateTalentPropCalcPower(humanObj, currentTabInfo);
            // 处理天赋技能更新
            Map<Integer, Integer> oldSkillMap = Utils.jsonToMapIntInt(humanObj.getHuman2().getWingPassiveMap());
            Map<Integer, Integer> newSkillMap = new HashMap<>();
            // 遍历剩余天赋类型获取新的技能
            for (Define.p_wing_talent talentType : currentTabInfo.getTalentTypeListList()) {
                for (Define.p_key_value talent : talentType.getTalentListList()) {
                    ConfBackTalent_0 conf = ConfBackTalent_0.get((int) talent.getK(), (int) talent.getV());
                    if (conf != null && conf.skill != null && conf.skill.length >= 2) {
                        newSkillMap.put(conf.skill[0], conf.skill[1]);
                    }
                }
            }
            updateTalentSkills(humanObj, oldSkillMap, newSkillMap);
        }

        // 发送消息
        MsgWing.wing_talent_reset_s2c.Builder msg = MsgWing.wing_talent_reset_s2c.newBuilder();
        msg.setTab(tab);
        msg.setType(type);
        msg.setCount(tabInfoBuilder.getCount());
        humanObj.sendMsg(msg);
    }

    private void validateTalentData(Wing wing) {
        byte[] talentTabsData = wing.getTalentTabInfo();
        if (talentTabsData != null && talentTabsData.length > 0) {
            List<Define.p_talent_tab_info> tabInfoList = Utils.deserializeProtoList(talentTabsData, Define.p_talent_tab_info.parser());
            Set<Integer> tabSet = new HashSet<>();

            for (Define.p_talent_tab_info tabInfo : tabInfoList) {
                if (tabSet.contains(tabInfo.getTab())) {
                    Log.game.error("发现重复tab数据: {}", tabInfo.getTab());
                }
                tabSet.add(tabInfo.getTab());

                Set<Integer> typeSet = new HashSet<>();
                for (Define.p_wing_talent talentType : tabInfo.getTalentTypeListList()) {
                    if (typeSet.contains(talentType.getType())) {
                        Log.game.error("tab {} 发现重复type数据: {}", tabInfo.getTab(), talentType.getType());
                    }
                    typeSet.add(talentType.getType());
                }
            }
        }
    }

    /**
     * 翅膀天赋选择方案
     */
    public void handleWingTalentChooseTabC2S(HumanObject humanObj, int tab) {
        Wing wing = humanObj.operation.wing;
        if (wing == null) {
            return;
        }

        if (tab == wing.getCurrentTab()) {
            return;
        }

        if (checkTab(tab)) {
            return;
        }

        Define.p_talent_tab_info tabInfo = getTabTalentInfo(wing, tab);

        // 如果方案不存，创建新方案
        if (tabInfo == null) {
            // 创建新方案
            Define.p_talent_tab_info.Builder newTabBuilder = createTabTalentInfo(tab);
            tabInfo = newTabBuilder.build();
            updateCurrentTabTalentInfo(wing, tabInfo, tab);
        }
        wing.setCurrentTab(tab);
        // 处理天赋技能更新
        Map<Integer, Integer> oldSkillMap = Utils.jsonToMapIntInt(humanObj.getHuman2().getWingPassiveMap());
        Map<Integer, Integer> newSkillMap = new HashMap<>();
        // 遍历所有天赋类型获取新的技能
        for (Define.p_wing_talent talentType : tabInfo.getTalentTypeListList()) {
            for (Define.p_key_value talent : talentType.getTalentListList()) {
                ConfBackTalent_0 conf = ConfBackTalent_0.get((int)talent.getK(), (int)talent.getV());
                if (conf != null && conf.skill != null && conf.skill.length >= 2) {
                    newSkillMap.put(conf.skill[0], conf.skill[1]);
                }
            }
        }
        updateTalentSkills(humanObj, oldSkillMap, newSkillMap);
        HumanManager.inst().setPlanTab(humanObj, PlanVo.TAB_WING_TALENT, tab);
        updateTalentPropCalcPower(humanObj, tabInfo);

        // 发送消息
        MsgWing.wing_talent_choose_tab_s2c.Builder msg = MsgWing.wing_talent_choose_tab_s2c.newBuilder();
        msg.setNewTab(tab);
        humanObj.sendMsg(msg);
    }

    public void handleWingTalentChangeTabNameC2S(HumanObject humanObj, int tab, String name) {
        Wing wing = humanObj.operation.wing;
        if (wing == null) {
            return;
        }

        if (checkTab(tab)) {
            return;
        }
        Define.p_talent_tab_info tabInfo = getTabTalentInfo(wing, tab);
        Define.p_talent_tab_info.Builder tabInfoBuilder;
        if (tabInfo == null) {
            tabInfoBuilder = createTabTalentInfo(tab);
        }else {
            tabInfoBuilder = tabInfo.toBuilder();
        }

        tabInfoBuilder.setName(name);
        updateCurrentTabTalentInfo(wing, tabInfoBuilder.build(), tab);

        // 发送消息
        MsgWing.wing_talent_change_tab_name_s2c.Builder msg = MsgWing.wing_talent_change_tab_name_s2c.newBuilder();
        msg.setTab(tab);
        msg.setName(name);
        humanObj.sendMsg(msg);
    }

    public void handleWingTalentOneClickLevUpC2S(HumanObject humanObj, int tab, int type, int cfgId, int choose) {
        Wing wing = humanObj.operation.wing;
        if (wing == null) {
            return;
        }

        if (checkTab(tab)) {
            return;
        }

        // 获取当前方案信息

        Define.p_talent_tab_info tabInfo = getTabTalentInfo(wing, tab);
        Define.p_talent_tab_info.Builder tabInfoBuilder;
        if(tabInfo != null){
            tabInfoBuilder = tabInfo.toBuilder();
        }else {
            tabInfoBuilder = createTabTalentInfo(tab);
        }
        Map<Integer, Integer> talentTypeMap = getCurrentTabTalentTypeMap(tabInfoBuilder, type);

        // 当前天赋等级
        int currentLevel = talentTypeMap.getOrDefault(cfgId, 0);
        if (currentLevel != 0) {
            return;
        }

        // 获取下一级配置
        ConfBackTalent_0 nextLevelConf = ConfBackTalent_0.get(cfgId, 1);
        if (nextLevelConf == null || nextLevelConf.job_type != type) {
            Log.game.error("WingManager.handleWingTalentOneClickLevUpC2S, nextLevelConf is null, cfgId={}, level={}, type={}", cfgId, 1, type);
            return; // 找不到下一级配置
        }

        if(!checkOtherColorTalentEnd(talentTypeMap, nextLevelConf)){
            Log.game.error("翅膀天赋升级其他颜色天赋未满级，cfgId={},level={}", cfgId, 1);
            return;
        }

        ConfBackTalent_0 confBackTalent_0_1 = ConfBackTalent_0.get(cfgId, 1);
        if(confBackTalent_0_1.connect_id == null || confBackTalent_0_1.connect_id.length == 0) {
            // 最多只能升级4个节点
            int endCount = getTabEndTalentCount(tabInfoBuilder);
            if (endCount >= ConfGlobal.get("BackTalent_max").value) {
                Log.game.error("翅膀天赋升级已达上限，cfgId={},level={}", cfgId, 1);
                return;
            }
        }

        Map<Integer, Integer> targetLevelMap = new HashMap<>(talentTypeMap); // 目标等级表
        // 设置目标节点的目标等级
        targetLevelMap.put(cfgId, currentLevel + 1);

        // 递归处理所有前置条件
        Set<Integer> processedTalents = new HashSet<>();
        if (!processPrerequisites(cfgId, targetLevelMap, processedTalents, choose)) {
            return; // 前置条件无法满足
        }

        // 计算总消耗
        int totalCost = 0;
        for (Map.Entry<Integer, Integer> entry : targetLevelMap.entrySet()) {
            int talentId = entry.getKey();
            int targetLevel = entry.getValue();
            int originalLevel = talentTypeMap.getOrDefault(talentId, 0);

            for (int level = originalLevel + 1; level <= targetLevel; level++) {
                ConfBackTalent_0 conf = ConfBackTalent_0.get(talentId, level);
                if (conf != null) {
                    totalCost += conf.cost[1];
                }
            }
        }

        // 检查点数是否足够
        int totalCount = ItemManager.inst().getItemNum(humanObj, TALENT_COST_SN);
        int currentCount = totalCount - tabInfoBuilder.getCount();
        if (currentCount < totalCost) {
            Log.game.error("已经升级天赋点数不足, totalCount={}, currentCount={}, totalCost={}", totalCount, currentCount, totalCost);
            return; // 点数不足
        }

        // 扣除点数
        tabInfoBuilder.setCount(tabInfoBuilder.getCount() + totalCost);

        // 更新天赋数据
        List<Define.p_key_value.Builder> updatedTalents = new ArrayList<>();
        for (Map.Entry<Integer, Integer> entry : targetLevelMap.entrySet()) {
            Define.p_key_value.Builder talentInfo = Define.p_key_value.newBuilder();
            talentInfo.setK(entry.getKey());
            talentInfo.setV(entry.getValue());
            updatedTalents.add(talentInfo);
        }

        // 清除旧数据并添加新数据
        boolean foundType = false;
        for (int i = 0; i < tabInfoBuilder.getTalentTypeListCount(); i++) {
            Define.p_wing_talent talentType = tabInfoBuilder.getTalentTypeList(i);
            if (talentType.getType() == type) {
                foundType = true;
                Define.p_wing_talent.Builder talentTypeBuilder = Define.p_wing_talent.newBuilder();
                talentTypeBuilder.setType(type);
                for (Define.p_key_value.Builder talent : updatedTalents) {
                    talentTypeBuilder.addTalentList(talent.build());
                }
                tabInfoBuilder.setTalentTypeList(i, talentTypeBuilder.build());
                break;
            }
        }

        if (!foundType) {
            Define.p_wing_talent.Builder talentTypeBuilder = Define.p_wing_talent.newBuilder();
            talentTypeBuilder.setType(type);
            for (Define.p_key_value.Builder talent : updatedTalents) {
                talentTypeBuilder.addTalentList(talent.build());
            }
            tabInfoBuilder.addTalentTypeList(talentTypeBuilder.build());
        }

        // 更新当前方案的天赋数据
        Define.p_talent_tab_info currentTabInfo = tabInfoBuilder.build();
        updateCurrentTabTalentInfo(wing, currentTabInfo, tab);

        // 更新属性
        if (wing.getCurrentTab() == tab) {
            updateTalentPropCalcPower(humanObj, currentTabInfo);
            // 处理天赋技能更新
            Map<Integer, Integer> oldSkillMap = Utils.jsonToMapIntInt(humanObj.getHuman2().getWingPassiveMap());
            Map<Integer, Integer> newSkillMap = new HashMap<>();
            // 遍历所有天赋类型获取新的技能
            for (Define.p_wing_talent talentType : currentTabInfo.getTalentTypeListList()) {
                for (Define.p_key_value talent : talentType.getTalentListList()) {
                    ConfBackTalent_0 conf = ConfBackTalent_0.get((int)talent.getK(), (int)talent.getV());
                    if (conf != null && conf.skill != null && conf.skill.length >= 2) {
                        newSkillMap.put(conf.skill[0], conf.skill[1]);
                    }
                }
            }
            updateTalentSkills(humanObj, oldSkillMap, newSkillMap);
        }

        // 发送消息
        MsgWing.wing_talent_one_click_lev_up_s2c.Builder msg = MsgWing.wing_talent_one_click_lev_up_s2c.newBuilder();
        msg.setTab(tab);
        msg.setCount(tabInfoBuilder.getCount());
        msg.setType(type);

        for (Define.p_key_value.Builder talent : updatedTalents) {
            msg.addTalent(talent.build());
        }

        humanObj.sendMsg(msg);
    }

    /**
     * 递归处理所有前置条件
     * @param talentId 当前处理的天赋ID
     * @param targetLevelMap 目标等级表
     * @param processedTalents 已处理过的天赋ID集合，避免循环依赖
     * @param choose 选择模式：0-最低要求等级，1-满级
     * @return 是否所有前置条件都能满足
     */
    private boolean processPrerequisites(int talentId, Map<Integer, Integer> targetLevelMap, Set<Integer> processedTalents, int choose) {
        if (processedTalents.contains(talentId)) {
            return true; // 已处理过，避免循环依赖
        }

        processedTalents.add(talentId);

        // 获取当前等级的配置
        ConfBackTalent_0 currentLevelConf = ConfBackTalent_0.get(talentId, 1);
        if (currentLevelConf == null) {
            Log.game.error("WingManager.processPrerequisites, currentLevelConf is null, talentId={}, targetLevel={}", talentId, 1);
            return false; // 配置不存在
        }

        // 处理前置条件
        if (currentLevelConf.condition_1 != null && currentLevelConf.condition_1.length > 0) {
            for (int[] condition : currentLevelConf.condition_1) {
                int conditionCfgId = condition[0];
                int requiredLevel = condition[1];

                // 如果choose=2，则前置条件需要满级
                if (choose == 2) {
                    int maxLevel = 0;
                    for (int i = 1; ; i++) {
                        if (ConfBackTalent_0.get(conditionCfgId, i) == null) {
                            maxLevel = i - 1;
                            break;
                        }
                    }
                    requiredLevel = maxLevel;
                }

                // 更新目标等级表
                int currentTargetLevel = targetLevelMap.getOrDefault(conditionCfgId, 0);
                if (currentTargetLevel < requiredLevel) {
                    targetLevelMap.put(conditionCfgId, requiredLevel);
                }

                // 递归处理前置条件的前置条件
                if (!processPrerequisites(conditionCfgId, targetLevelMap, processedTalents, choose)) {
                    return false;
                }
            }
        }

        return true;
    }

    // 辅助方法:处理天赋技能更新
    private void updateTalentSkills(HumanObject humanObj, Map<Integer, Integer> oldSkillMap, Map<Integer, Integer> newSkillMap) {
        if (!oldSkillMap.equals(newSkillMap)) {
            humanObj.getHuman2().setWingPassiveMap(Utils.mapIntIntToJSON(newSkillMap));

            List<Define.p_passive_skill> delete = new ArrayList<>();
            List<Define.p_passive_skill> update = new ArrayList<>();

            // 处理需要删除的技能
            for (Map.Entry<Integer, Integer> entry : oldSkillMap.entrySet()) {
                if (!newSkillMap.containsKey(entry.getKey())) {
                    Define.p_passive_skill.Builder builder = Define.p_passive_skill.newBuilder();
                    builder.setSkillId(entry.getKey());
                    builder.setSkillLv(entry.getValue());
                    delete.add(builder.build());
                }
            }

            // 处理需要更新的技能
            for (Map.Entry<Integer, Integer> entry : newSkillMap.entrySet()) {
                if (!oldSkillMap.containsKey(entry.getKey()) || !oldSkillMap.get(entry.getKey()).equals(entry.getValue())) {
                    Define.p_passive_skill.Builder builder = Define.p_passive_skill.newBuilder();
                    builder.setSkillId(entry.getKey());
                    builder.setSkillLv(entry.getValue());
                    update.add(builder.build());
                }
            }

            // 发送技能更新消息
            SkillManager.inst().sendMsg_skill_passive_update_s2c(humanObj, update, delete);
        }
    }

   public PropCalc getTalentPropCalc(HumanObject humanObj, int tab) {
        PropCalc propCalc = new PropCalc();
        Wing wing = humanObj.operation.wing;
        if(wing == null){
            return propCalc;
        }

        if(tab == wing.getCurrentTab()){
            // 当前选中方案
            propCalc.plus(humanObj.dataPers.unitPropPlus.dataMap.get(UnitPropPlus.K.wingTalent));
            return propCalc;
        }

        Define.p_talent_tab_info tabInfo = getTabTalentInfo(wing, tab);
        if(tabInfo == null){
            return propCalc;
        }

        // 遍历所有天赋类型
        for (Define.p_wing_talent talentType : tabInfo.getTalentTypeListList()) {
            // 遍历每个类型下的所有天赋
            for (Define.p_key_value talent : talentType.getTalentListList()) {
                int cfgId = (int)talent.getK();
                int level = (int)talent.getV();

                // 获取天赋配置
                ConfBackTalent_0 confBackTalent_0 = ConfBackTalent_0.get(cfgId, level);
                if (confBackTalent_0 == null || confBackTalent_0.attr == null || confBackTalent_0.attr.length < 2) {
                    continue;
                }

                // 累加属性
                propCalc.plus(confBackTalent_0.attr[0], BigDecimal.valueOf(confBackTalent_0.attr[1]));
            }
        }
        return propCalc;
    }
}
