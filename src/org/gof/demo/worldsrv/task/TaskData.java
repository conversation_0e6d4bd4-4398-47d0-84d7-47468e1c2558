package org.gof.demo.worldsrv.task;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.Port;
import org.gof.core.interfaces.ISerilizable;
import org.gof.core.support.S;
import org.gof.core.support.Utils;
import org.gof.demo.battlesrv.manager.CombatChangeLog;
import org.gof.demo.battlesrv.manager.PropManager;
import org.gof.demo.battlesrv.support.PropCalc;
import org.gof.demo.worldsrv.activity.ActivityControlTypeFactory;
import org.gof.demo.worldsrv.activity.ActivityManager;
import org.gof.demo.worldsrv.activity.EActivityType;
import org.gof.demo.worldsrv.activity.calculator.ActivityControlWarToken;
import org.gof.demo.worldsrv.activity.data.ActivityControlObjectData;
import org.gof.demo.worldsrv.activity.data.controldata.ControlWarTokenData;
import org.gof.demo.worldsrv.character.HumanDailyResetInfo;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.*;
import org.gof.demo.worldsrv.entity.*;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.home.Fish.FishData;
import org.gof.demo.worldsrv.instance.InstanceConstants;
import org.gof.demo.worldsrv.instance.InstanceManager;
import org.gof.demo.worldsrv.kungFuRace.KungFuRaceUtils;
import org.gof.demo.worldsrv.msg.*;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.StringZipUtils;
import org.gof.demo.worldsrv.support.enumKey.DailyResetTypeKey;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;
import org.gof.demo.worldsrv.support.observer.Event;
import org.gof.demo.worldsrv.support.observer.EventKey;
import org.gof.demo.worldsrv.task.type.ITaskTypeData;
import org.gof.demo.worldsrv.task.type.TaskTypeDataFactory;
import org.gof.demo.worldsrv.task.type.TaskVO;
import org.gof.demo.worldsrv.task.type.achievementData.AchievementTaskVO;
import org.gof.demo.worldsrv.task.type.activityData.ActivityTaskVO;
import org.gof.demo.worldsrv.task.type.dailydata.DailyTaskVO;
import org.gof.demo.worldsrv.task.type.flyAchieveData.FlyAchieveTaskVO;
import org.gof.demo.worldsrv.task.type.maindata.MainTaskVO;
import java.io.IOException;
import java.util.*;

public class TaskData implements ISerilizable {

	private TaskInfo taskInfo;
	private TaskFinish taskFinish;
	// 主线任务sn, 任务信息
	public Map<Integer, MainTaskVO> mainTaskMap = new HashMap<>();
	// 每日任务
	public Map<Integer, DailyTaskVO> dailyTaskMap = new HashMap<>();
	// 冒险任务
	public Map<Integer, TaskVO> adventureTaskMap = new HashMap<>();
	public List<Integer> finishMainTaskSnList = new ArrayList<>();
	// 成就任务
	public Map<Integer, AchievementTaskVO> achievementTaskMap = new HashMap<>();
	// 武道会任务
	public Map<Integer, TaskVO> kungFuRaceTaskMap = new HashMap<>();
	// 回归任务
	public Map<Integer, TaskVO> backTaskMap = new HashMap<>();

	/**
	 * 构造函数
	 */
	public TaskData() {
	}



	public boolean isNull(){
		return taskInfo == null;
	}

	public void load(TaskInfo taskInfo){
		this.taskInfo = taskInfo;
		if(taskInfo != null){
			initMainTask(taskInfo.getMainTaskJSON());
			initDailyTask(taskInfo.getDailyTaskJSON());
			initAdventureTask(taskInfo.getTitleTaskJSON());
			initAchievementTask(taskInfo.getAchievementTaskJSON());
			initFlyAchieveTask(taskInfo.getFlyAchieveTaskJSON());
			initKungFuRaceTask(taskInfo.getKungFuRaceTaskJSON());
			initBackTask(taskInfo.getBackTaskJSON());
		}
	}

	public void load(TaskFinish taskFinish){
		this.taskFinish = taskFinish;
		if(taskFinish != null){
			finishMainTaskSnList = Utils.strToIntList(StringZipUtils.unzip(taskFinish.getMainSnList()));
		}
	}

	@Override
	public void writeTo(OutputStream out) throws IOException {
		out.write(taskInfo);
		out.write(mainTaskMap);
		out.write(adventureTaskMap);
		out.write(finishMainTaskSnList);
		out.write(taskFinish);
		out.write(achievementTaskMap);
		out.write(flyAchieveMap);
		out.write(kungFuRaceTaskMap);
		out.write(backTaskMap);
	}
	
	@Override
	public void readFrom(InputStream in) throws IOException {
		taskInfo = in.read();
		mainTaskMap.putAll(in.<Map<Integer, MainTaskVO>>read());
		adventureTaskMap.putAll(in.<Map<Integer, TaskVO>>read());
		finishMainTaskSnList = in.read();
		taskFinish = in.read();
		achievementTaskMap = in.read();
		flyAchieveMap = in.read();
		kungFuRaceTaskMap = in.read();
		backTaskMap = in.read();
	}
	// ==============================TODO 主线任务开始=============================

	private void initMainTask(String json){
		JSONArray ja = Utils.toJSONArray(json);
		for(int i = 0; i < ja.size(); i++){
			JSONObject jo = Utils.toJSONObject(ja.getString(i));
			MainTaskVO vo = new MainTaskVO(jo);
			addMainTask(vo, false);
		}
	}

	public void addMainTask(MainTaskVO vo, boolean isSave){
		mainTaskMap.put(vo.taskSn, vo);
		if(isSave){
			saveMainTask();
		}
	}
	public void saveMainTask(){
		saveMainTask(true);
	}
	public void saveMainTask(boolean isUp){
		JSONArray ja = new JSONArray();
		for(MainTaskVO vo : mainTaskMap.values()){
			ja.add(vo.toString());
		}
		taskInfo.setMainTaskJSON(ja.toJSONString());
		if(isUp){
			taskInfo.update();
		}
	}

	public boolean isFinishMainTaskSn(int taskId){
		return finishMainTaskSnList.contains(taskId);
	}

	/** 
	 * 接主线任务
	 * <AUTHOR>
	 * @Date 2024/3/5
	 * @Param 
	 */
	public TaskVO acceptMainTask(HumanObject humanObj, int taskId) {
		TaskVO vo = null;
		ConfMainTask conf = ConfMainTask.get(taskId);
		if(conf == null){
			Log.task.error("===ConfMainTask配表错误， not find sn={}", taskId);
			return vo;
		}
		if(mainTaskMap.containsKey(taskId)){
			MainTaskVO mtVO = mainTaskMap.get(taskId);
			if(mtVO.getStatus() == TaskConditionTypeKey.TASK_STATUS_已领取奖励){
				removeMainTask(taskId);
			} else {
				return mtVO;
			}
		}
		try {
			MainTaskVO voNew = new MainTaskVO(conf);
			mainTaskMap.put(taskId, voNew);
			ITaskTypeData idata = TaskTypeDataFactory.getTaskTypeData(voNew.getConditionType());
			if(idata != null){
				idata.checkPlan(humanObj, voNew, 0);
			}

//			saveMainTask();
			if(S.isTestLog) {
				Log.task.info("===接收任务，taskSn={}", taskId);
			}
			return voNew;
		} catch (Exception e) {
			Log.task.error(e.getMessage());
		}
		return vo;
	}

	public void removeMainTask(int taskId){
		mainTaskMap.remove(taskId);
//		saveMainTask();
		// TODO 删除任务
	}

	public void submitMainTask(HumanObject humanObj, int taskId){
		MainTaskVO vo = mainTaskMap.get(taskId);
		if(vo == null){
			return;
		}
		if(vo.getStatus() != TaskConditionTypeKey.TASK_STATUS_已完成){
			vo.checkStatus();
		}
		if(vo.getStatus() != TaskConditionTypeKey.TASK_STATUS_已完成){
			return;
		}
		if(S.isTestLog) {
			Log.task.info("====提交任务， sn={}", taskId);
		}
		vo.drawAwards(humanObj, MoneyItemLogKey.主线任务);
		TaskManager.inst().sendTaskInfo(humanObj, vo);

		TaskVO voNew = acceptMainTask(humanObj, vo.getNextSn());
		removeMainTask(taskId);
		saveMainTask();

		if(voNew != null){
			TaskManager.inst().sendTaskInfo(humanObj, voNew);
			TaskManager.inst().sendMsg_task_commit_s2c(humanObj, TaskConditionTypeKey.TASK_主线, voNew.taskSn);
		}
		addFinishTaskSn(humanObj.id, taskId);

		Event.fire(EventKey.FINISH_TASK, "humanObj", humanObj, "taskSn", taskId);
		Event.fire(EventKey.FINISH_MAIN_TASK, "humanObj", humanObj, "taskSn", taskId);
	}

	public void addFinishTaskSn(long humanId, int taskId){
		if(!finishMainTaskSnList.contains(taskId)){
			finishMainTaskSnList.add(taskId);
			initFinishTask(humanId);
//			taskFinish.setMainSnList(StringZipUtils.zip(Utils.listToString(finishMainTaskSnList)));
//			taskFinish.update();
		}
	}


	public void initFinishTask(long humanId){
		if(taskFinish != null){
			return;
		}
		taskFinish = new TaskFinish();
		taskFinish.setId(humanId);
		taskFinish.setMainSnList(StringZipUtils.zip(Utils.listToString(finishMainTaskSnList)));
		taskFinish.persist();
	}

	/** 
	 * 检测/增加任务进度 
	 * <AUTHOR>
	 * @Date 2024/3/13
	 * @Param 
	 */
	public void checkTaskPlan(HumanObject humanObj, int type, int taskType, Object... objs){
		switch (type) {
			case TaskConditionTypeKey.TASK_主线:
				checkTaskMainPlan(humanObj, taskType, objs);
				break;
			case TaskConditionTypeKey.TASK_每日:
				checkTaskDailyPlan(humanObj, taskType, objs);
				break;
			case TaskConditionTypeKey.TASK_冒险任务:
				checkTaskAdventurePlan(humanObj, taskType, objs);
				break;
			case TaskConditionTypeKey.TASK_功能预告:
				checkTaskPreFunPlan(humanObj, taskType, objs);
				break;
			case TaskConditionTypeKey.TASK_战令:
				checkTaskWarToken(humanObj, taskType, objs);
				break;
			case TaskConditionTypeKey.TASK_活动:
				checkTaskActivityPlan(humanObj, taskType, objs);
				break;
			case TaskConditionTypeKey.TASK_成就任务:
				checkTaskAchievementPlan(humanObj, taskType, objs);
				break;
			case TaskConditionTypeKey.TASK_飞宠成就:
				checkFlyAchievePlan(humanObj, taskType, objs);
				break;
			case TaskConditionTypeKey.TASK_武道会任务:
				checkTaskKungFuRacePlan(humanObj, taskType, objs);
				break;
			case TaskConditionTypeKey.TASK_回归任务:
				checkTaskBackPlan(humanObj, taskType, objs);
				break;
			case TaskConditionTypeKey.TASK_钓鱼每日任务:
				checkFishDailyTask(humanObj, taskType, objs);
				break;
			case TaskConditionTypeKey.TASK_渔场解锁:
				checkFishGroundTask(humanObj, taskType, objs);
				break;
			default:
				Log.task.error("===未实现代码,type={}, taskType={}", type, taskType);
				break;
		}
	}

	private void checkTaskMainPlan(HumanObject humanObj, int conditionType, Object... objs){
		for(MainTaskVO vo : mainTaskMap.values()){
			if(vo.getConditionType() != conditionType){
				continue;
			}
			ITaskTypeData idata = TaskTypeDataFactory.getTaskTypeData(conditionType);
			if(idata == null){
				Log.task.error("===ITaskTypeData=null, humanId={}, conditionType={} ", humanObj.id, conditionType);
				continue;
			}
			idata.checkPlan(humanObj, vo, objs);
		}
	}

	// ==============================TODO 主线任务结束=================================

	// ==============================TODO 每日任务开始=================================
	private void initDailyTask(String json){
		JSONArray ja = Utils.toJSONArray(json);
		for(int i = 0; i < ja.size(); i++){
			JSONObject jo = Utils.toJSONObject(ja.getString(i));
			DailyTaskVO vo = new DailyTaskVO(jo);
			addDailyTask(vo, false);
		}
	}

	public void addDailyTask(DailyTaskVO vo, boolean isSave){
		dailyTaskMap.put(vo.taskSn, vo);
		if(isSave){
			saveDailyTask();
		}
	}
	public void saveDailyTask(){
		saveDailyTask(true);
	}
	public void saveDailyTask(boolean isUp){
		JSONArray ja = new JSONArray();
		for(DailyTaskVO vo : dailyTaskMap.values()){
			ja.add(vo.toString());
		}
		taskInfo.setDailyTaskJSON(ja.toJSONString());
		if(isUp){
			taskInfo.update();
		}
	}

	public void checkDailyTask(HumanObject humanObj){
		boolean isUp = false;
		for(ConfDailyTask confDailyTask : ConfDailyTask.findAll()){
			if(dailyTaskMap.containsKey(confDailyTask.sn)){
				continue;
			}
			DailyTaskVO vo = new DailyTaskVO(confDailyTask);
			dailyTaskMap.put(vo.taskSn, vo);
			isUp = true;
		}
		if(isUp){
			saveDailyTask();
		}
	}


	public void resetDailyTask(){
		for(ConfDailyTask confDailyTask : ConfDailyTask.findAll()){
			DailyTaskVO vo = new DailyTaskVO(confDailyTask);
			dailyTaskMap.put(vo.taskSn, vo);
		}
		taskInfo.setDailyScore(0);
		taskInfo.setDailyBoxSnList("");
		saveDailyTask();
	}

	public void submitDailyTask(HumanObject humanObj, int taskId){
		boolean hasCompletedTask = false;
		int totalLiveness = 0;
		// 遍历所有每日任务
		MsgTask.task_update_s2c.Builder msg = MsgTask.task_update_s2c.newBuilder();
		Map<Integer, Integer> rewards = new HashMap<>();
		for (DailyTaskVO vo : dailyTaskMap.values()) {
			// 检查任务状态
			if (vo.getStatus() != TaskConditionTypeKey.TASK_STATUS_已完成) {
				vo.checkStatus();
			}
			// 如果任务已完成，领取奖励
			if (vo.getStatus() == TaskConditionTypeKey.TASK_STATUS_已完成) {
				vo.setStatus(TaskConditionTypeKey.TASK_STATUS_已领取奖励);
				Utils.mergeMap(rewards, vo.getAwards());
				totalLiveness += vo.liveness;
				msg.addTaskList(TaskManager.inst().to_p_task(vo));
				// 触发任务完成事件
				Event.fire(EventKey.FINISH_TASK, "humanObj", humanObj, "taskSn", vo.taskSn);
				hasCompletedTask = true;
			}
		}

		// 如果有完成的任务，更新活跃度并保存
		if (hasCompletedTask) {
			// 更新活跃度
			HumanDailyResetInfo info = humanObj.getDailyResetInfo(DailyResetTypeKey.dailyPoint.getType());
			info.setValue(info.getValue() + totalLiveness);
			humanObj.saveDailyResetRecord();

			// 保存任务数据
			saveDailyTask();
			ProduceManager.inst().produceAdd(humanObj, rewards, MoneyItemLogKey.每日任务);
			InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, InstanceConstants.showType_0, rewards);
			// 发送活跃度更新消息
			TaskManager.inst().sendMsg_task_daily_point_s2c(humanObj);
			humanObj.sendMsg(msg);

			if (S.isTestLog) {
				Log.task.info("====一键领取每日任务奖励，humanId={}, 增加活跃度={}", humanObj.id, totalLiveness);
			}
		}
	}

	private void checkTaskDailyPlan(HumanObject humanObj, int conditionType, Object... objs){
		for(DailyTaskVO vo : dailyTaskMap.values()){
			if(vo.getConditionType() != conditionType){
				continue;
			}
			ITaskTypeData idata = TaskTypeDataFactory.getTaskTypeData(conditionType);
			if(idata == null){
				Log.task.error("===ITaskTypeData=null, humanId={}, conditionType={} ", humanObj.id, conditionType);
				continue;
			}
			idata.checkPlan(humanObj, vo, objs);
		}
	}

	// ==============================TODO 每日任务结束=================================

	// ==============================冒险任务开始=================================

	public void checkAdventureTask(HumanObject humanObj){
		ConfAdventureLevel confAdvLv = ConfAdventureLevel.get(humanObj.getHumanExtInfo().getAdventureTitleLv());
		if(confAdvLv != null && confAdvLv.taskGroup != null && confAdvLv.taskGroup.length > 0){
			int[] taskGroup = confAdvLv.taskGroup;
			for(int taskId : taskGroup){
				if(adventureTaskMap.keySet().contains(taskId)){
					return;
				}
			}
			acceptAdventureTask(humanObj, confAdvLv.taskGroup);
		}
	}

	private void initAdventureTask(String json){
		JSONArray ja = Utils.toJSONArray(json);
		for(int i = 0; i < ja.size(); i++){
			TaskVO vo = new TaskVO(ja.getString(i));
			ConfAdventureTask conf = ConfAdventureTask.get(vo.taskSn);
			if(conf == null){
				Log.task.error("===ConfAdventureTask配表错误， not find sn={}", vo.taskSn);
				continue;
			}
			vo.setConditionType(conf.condition[0]);
			vo.setParam1(conf.condition[1]);
			vo.setSumPlan(conf.condition[2]);
			vo.setAwards(Utils.intArrToIntMap(conf.reward));
			addAdventureTask(vo, false);
		}
	}

	public void acceptAdventureTask(HumanObject humanObj, int[] taskGroup) {
		adventureTaskMap.clear();
		for(int sn : taskGroup){
			ConfAdventureTask conf = ConfAdventureTask.get(sn);
			if(conf == null){
				Log.task.error("===ConfAdventureTask配表错误， not find sn={}", sn);
				continue;
			}
			TaskVO vo = new TaskVO(conf.sn, TaskConditionTypeKey.TASK_冒险任务, conf.condition, Utils.intArrToIntMap(conf.reward), 0);
			ITaskTypeData idata = TaskTypeDataFactory.getTaskTypeData(vo.getConditionType());
			if(idata != null){
				idata.checkPlan(humanObj, vo, 0);
			}
			adventureTaskMap.put(sn, vo);
		}
		saveAdventureTask();
	}

	public void addAdventureTask(TaskVO vo, boolean isSave){
		adventureTaskMap.put(vo.taskSn, vo);
		if(isSave){
			saveAdventureTask();
		}
	}

	public void saveAdventureTask(){
		saveAdventureTask(true);
	}

	public void saveAdventureTask(boolean isUp){
		JSONArray ja = new JSONArray();
		for(TaskVO vo : adventureTaskMap.values()){
			ja.add(vo.toString());
		}
		taskInfo.setTitleTaskJSON(ja.toJSONString());
		if(isUp){
			taskInfo.update();
		}

	}

	private void checkTaskAdventurePlan(HumanObject humanObj, int conditionType, Object... objs){
		for(TaskVO vo : adventureTaskMap.values()){
			if(vo.getConditionType() != conditionType){
				continue;
			}
			ITaskTypeData idata = TaskTypeDataFactory.getTaskTypeData(conditionType);
			if(idata == null){
				Log.task.error("===ITaskTypeData=null, humanId={}, conditionType={} ", humanObj.id, conditionType);
				continue;
			}
			idata.checkPlan(humanObj, vo, objs);
		}
	}

	public boolean submitAllAdventureTask(HumanObject humanObj){
		for(TaskVO vo : adventureTaskMap.values()){
			if(vo.getStatus() != TaskConditionTypeKey.TASK_STATUS_已完成){
				vo.checkStatus();
			}
			if(vo.getStatus() != TaskConditionTypeKey.TASK_STATUS_已完成 && vo.getStatus() != TaskConditionTypeKey.TASK_STATUS_已领取奖励){
				return false;
			}
		}
		for (TaskVO vo : adventureTaskMap.values()) {
			vo.drawAwards(humanObj, MoneyItemLogKey.冒险任务);
		}
		return true;
	}


	public void submitAdventureTask(HumanObject humanObj, int taskId, boolean isSave){
		TaskVO vo = adventureTaskMap.get(taskId);
		if(vo == null){
			return;
		}
		if(vo.getStatus() != TaskConditionTypeKey.TASK_STATUS_已完成){
			vo.checkStatus();
		}
		if(vo.getStatus() != TaskConditionTypeKey.TASK_STATUS_已完成){
			return;
		}
		vo.drawAwards(humanObj, MoneyItemLogKey.冒险任务);
		TaskManager.inst().sendTaskInfo(humanObj, vo);
		if(isSave){
			saveAdventureTask();
		}
	}

	public void updatePorpCalc(HumanObject humanObj){
		PropCalc propCalc = new PropCalc();
		ConfAdventureLevel conf = ConfAdventureLevel.get(humanObj.getHumanExtInfo().getAdventureTitleLv());
		if(conf == null||conf.effect.length == 0){
			Log.task.error("===ConfAdventureLevel配表错误， not find sn={}", humanObj.getHumanExtInfo().getAdventureTitleLv());
			return;
		}
		propCalc.plus(conf.effect);
		humanObj.dataPers.unitPropPlus.setFieldRead(UnitPropPlus.K.adventureTask, propCalc.toJSONStr());
		PropManager.inst().propCalc(humanObj, CombatChangeLog.头衔任务);
	}
	// ==============================冒险任务结束=================================

	// ==============================功能预告任务开始=================================
	public List<Integer> getFinishPreFuncList(){
		return Utils.strToIntList(taskInfo.getPreFuncTaskList());
	}

	public void setFinishPreFuncTasks(List<Integer> list){
		taskInfo.setPreFuncTaskList(Utils.listToString(list));
	}

	public boolean canRewardPreFuncTask(HumanObject humanObj, int[] condition){
		int type = condition[0];
		if(type == TaskConditionTypeKey.TASK_TYPE_1){
			return humanObj.getHuman().getLevel() >= condition[2];
		}else if(type == TaskConditionTypeKey.TASK_TYPE_8){
			return humanObj.getHuman2().getRepSn() > condition[1];
		}else if(type == TaskConditionTypeKey.TASK_TYPE_29){
			return humanObj.operation.taskData.finishMainTaskSnList.contains(condition[1]);
		}
		return false;
	}

	public void  checkTaskPreFunPlan(HumanObject humanObj, int taskType, Object...objs){
		List<Integer> list = Utils.strToIntList(taskInfo.getPreFuncTaskList());
		for(ConfPreFunc conf : ConfPreFunc.findAll()){
			if(conf.condition[0] != taskType){
				continue;
			}
			if(list.contains(conf.sn)){
				continue;
			}
			if(taskType == TaskConditionTypeKey.TASK_TYPE_1) {
				if (humanObj.getHuman().getLevel() == conf.condition[2]) {
					sendRole_preview_reward_s2c(humanObj, conf.sn);
				}
			}else if(taskType == TaskConditionTypeKey.TASK_TYPE_8) {
				if (humanObj.getHuman2().getRepSn() - 1 == conf.condition[1]) {
					sendRole_preview_reward_s2c(humanObj, conf.sn);
				}
			}else if(taskType == TaskConditionTypeKey.TASK_TYPE_29){
				int taskSn = objs!=null && objs.length > 0 ? (int)objs[0] : 0;
				if(taskSn == conf.condition[1]){
					sendRole_preview_reward_s2c(humanObj, conf.sn);
				}
			}
		}
	}

	private void sendRole_preview_reward_s2c(HumanObject humanObj, int sn) {
		MsgRole.role_preview_reward_s2c.Builder msg = MsgRole.role_preview_reward_s2c.newBuilder();
		Define.p_preview.Builder builder = Define.p_preview.newBuilder();
		builder.setConfigId(sn);
		builder.setState(TaskConditionTypeKey.PREVIEW_STATUS_可领奖);
		msg.addPreviewInfo(builder);
		humanObj.sendMsg(msg);
	}
	public void sendRole_preview_s2c(HumanObject humanObj) {
		MsgRole.role_preview_s2c.Builder msg = MsgRole.role_preview_s2c.newBuilder();
		//已经领奖list
		List<Integer> list = Utils.strToIntList(taskInfo.getPreFuncTaskList());
		for(ConfPreFunc conf : ConfPreFunc.findAll()){
			Define.p_preview.Builder builder = Define.p_preview.newBuilder();
			builder.setConfigId(conf.sn);
			if(list.contains(conf.sn)){
				builder.setState(TaskConditionTypeKey.PREVIEW_STATUS_已领奖);
			}else if(canRewardPreFuncTask(humanObj, conf.condition)){
				builder.setState(TaskConditionTypeKey.PREVIEW_STATUS_可领奖);
			}else {
				builder.setState(TaskConditionTypeKey.PREVIEW_STATUS_未完成);
			}
			msg.addPreviewList(builder);
		}
		humanObj.sendMsg(msg);
	}

	// ==============================功能预告任务结束=================================

	// ==============================成就任务开始=================================
	public void checkAchievementTask(HumanObject humanObj){
		boolean isSave = false;
		for(ConfAchievement confAchievement : ConfAchievement.findAll()){
			if(achievementTaskMap.containsKey(confAchievement.group)){
				continue;
			}
			AchievementTaskVO vo = new AchievementTaskVO(confAchievement);
			achievementTaskMap.put(vo.getGroup(), vo);
			ITaskTypeData idata = TaskTypeDataFactory.getTaskTypeData(vo.getConditionType());
			if(idata != null){
				idata.checkPlan(humanObj, vo, 0);
			}
			isSave = true;
		}
		for(AchievementTaskVO vo : achievementTaskMap.values()){
			// 如果成就已领取奖励，但有下一个任务，则接受下一个成就任务
			if(vo.getStatus() == TaskConditionTypeKey.TASK_STATUS_已领取奖励 && vo.getNextSn() > 0){
				acceptAchievementNextTask(humanObj, vo, false);
				isSave = true;
			}
		}
		if(isSave){
			saveAchievementTask();
		}
	}

	private void initAchievementTask(String json){
		JSONArray ja = Utils.toJSONArray(json);
		for(int i = 0; i < ja.size(); i++){
			JSONObject jo = Utils.toJSONObject(ja.getString(i));
			AchievementTaskVO vo = new AchievementTaskVO(jo);
			addAchievementTask(vo, false);
		}
	}

	public void addAchievementTask(AchievementTaskVO vo, boolean isSave){
		achievementTaskMap.put(vo.getGroup(), vo);
		if(isSave){
			saveAchievementTask();
		}
	}

	public void saveAchievementTask(){
		saveAchievementTask(true);
	}

	public void saveAchievementTask(boolean isUp){
		JSONArray ja = new JSONArray();
		for(AchievementTaskVO vo : achievementTaskMap.values()){
			ja.add(vo.toString());
		}
		taskInfo.setAchievementTaskJSON(ja.toJSONString());
		if(isUp){
			taskInfo.update();
		}
	}

	private void checkTaskAchievementPlan(HumanObject humanObj, int conditionType, Object... objs){
		for(AchievementTaskVO vo : achievementTaskMap.values()){
			if(vo.getConditionType() != conditionType){
				continue;
			}
			ITaskTypeData idata = TaskTypeDataFactory.getTaskTypeData(conditionType);
			if(idata == null){
				Log.task.error("===ITaskTypeData=null, humanId={}, conditionType={} ", humanObj.id, conditionType);
				continue;
			}
			idata.checkPlan(humanObj, vo, objs);
		}
	}

	public void submitAchievementTask(HumanObject humanObj, int taskId){
		ConfAchievement conf = ConfAchievement.get(taskId);
		if(conf == null){
			return;
		}
		AchievementTaskVO vo = achievementTaskMap.get(conf.group);
		if(vo == null){
			return;
		}
		if(vo.getStatus() != TaskConditionTypeKey.TASK_STATUS_已完成){
			vo.checkStatus();
		}
		if(vo.getStatus() != TaskConditionTypeKey.TASK_STATUS_已完成){
			return;
		}
		vo.drawAwards(humanObj, MoneyItemLogKey.成就任务);
		// 发送已领取的成就任务信息
		TaskManager.inst().sendTaskInfo(humanObj, vo);
		// 领取成就奖励时，通知更新成就统计信息
		TaskManager.inst().sendMsg_task_achievement_s2c(humanObj);
		// 接受下一个成就任务
		acceptAchievementNextTask(humanObj, vo, true);
	}

	/**
	 * 接受下一个成就任务
	 * @param humanObj
	 * @param curTask
	 * @param isSave
	 */
	public void acceptAchievementNextTask(HumanObject humanObj, AchievementTaskVO curTask, boolean isSave) {
		int nextSn = curTask.getNextSn();
		ConfAchievement conf = ConfAchievement.get(nextSn);
		if(conf == null){
			return;
		}
		AchievementTaskVO nextTask = new AchievementTaskVO(conf, curTask);
//		Log.task.info("acceptAchievementNextTask nextSn={} group={}", nextSn, conf.group);
		ITaskTypeData idata = TaskTypeDataFactory.getTaskTypeData(nextTask.getConditionType());
		if(idata != null){
			idata.checkPlan(humanObj, nextTask, 0);
		}
		achievementTaskMap.put(nextTask.getGroup(), nextTask);
		if(isSave){
			saveAchievementTask();
		}
	}

	/**
	 * 构造成就统计信息
	 */
	public MsgTask.task_achievement_s2c.Builder to_task_achievement_s2c() {
		MsgTask.task_achievement_s2c.Builder msg = MsgTask.task_achievement_s2c.newBuilder();
		int receivedId = taskInfo.getAchievementTotalReceivedId();
		msg.setGetId(receivedId);
		msg.setNowId(ConfAchievementTotal.get(receivedId+1) != null ? receivedId+1:receivedId);
		msg.setProgress(getAchievementTotalProgress());
		return msg;
	}

	private int getAchievementTotalProgress(){
		int achievementTotalProgress = 0;
		for(AchievementTaskVO vo : achievementTaskMap.values()){
			achievementTotalProgress += vo.getAccumulatedReceivedAwardNum();
		}
		return achievementTotalProgress;
	}

	/**
	 * 领取成就统计奖励
	 * @param humanObj
	 * @return
	 */
	public int[] receiveAchievementTotalRewards(HumanObject humanObj){
		int receivedId = taskInfo.getAchievementTotalReceivedId();
		ConfAchievementTotal confNow = ConfAchievementTotal.get(receivedId+1);
		if(confNow == null){
			Log.task.warn("成就统计奖励不可重复领取! humanId={} receivedId={}", humanObj.id, receivedId);
			return null;
		}
		int progress = getAchievementTotalProgress();
		if(progress < confNow.number){
			Log.task.warn("成就统计奖励进度未完成! humanId={} progress={} confNumber={}", humanObj.id, progress, confNow.number);
			return null;
		}
		taskInfo.setAchievementTotalReceivedId(receivedId+1);
		// 领取成就统计奖励后，通知更新成就统计信息
		TaskManager.inst().sendMsg_task_achievement_s2c(humanObj);
		return confNow.reward;
	}
	// ==============================成就任务结束=================================

	// ============================== 活动任务开始 =================================
	public void checkTaskWarToken(HumanObject humanObj, int taskType, Object...objs){
		List<Integer> actTypeList = ActivityControlTypeFactory.getTypeList(ActivityControlWarToken.class);
		for (int actType : actTypeList) {
			ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(actType);
			if(data == null){
				continue;
			}
			ConfActivityControl conf = ConfActivityControl.get(data.getActControlData().getActivitySn());
			boolean isUnlock = humanObj.isModUnlock(conf.newfunctionID);
			ControlWarTokenData warTokenData = (ControlWarTokenData)data.getControlData();
			if(warTokenData == null){
				Log.game.error("===ControlWarTokenData=null, humanId={}, actType={} ", humanObj.id, actType);
				continue;
			}
			boolean isChange = false;
			for(TaskVO vo : warTokenData.getTaskList()){
				if(vo.getConditionType() != taskType){
					continue;
				}
				ITaskTypeData idata = TaskTypeDataFactory.getTaskTypeData(taskType);
				if(idata == null){
					Log.task.error("===ITaskTypeData=null, humanId={}, conditionType={} ", humanObj.id, taskType);
					continue;
				}
				int state = vo.getStatus();
				int plan = vo.getPlan();
				idata.checkPlan(humanObj, vo, objs);
				if(state != vo.getStatus() || plan != vo.getPlan()){
					isChange = true;

					ActivityTaskVO activityTaskVO = (ActivityTaskVO) vo;
					ActivityControlWarToken controlWarToken = (ActivityControlWarToken)ActivityControlTypeFactory.getTypeData(((ActivityTaskVO) vo).getActivityType());
					if(controlWarToken == null){
						Log.task.error("===controlWarToken=null, activityType={}", activityTaskVO.getActivityType());
						return;
					}
					if(isUnlock){
						controlWarToken.sendWarTokenTaskInfo(humanObj, activityTaskVO, warTokenData.getTokenSn());
					}
				}
			}
			if(isChange){
				data.updateControlData();
			}
		}
	}

	public boolean submitTaskWarToken(TaskVO vo){
		if(vo.getStatus() != TaskConditionTypeKey.TASK_STATUS_已完成){
			vo.checkStatus();
		}
		if(vo.getStatus() != TaskConditionTypeKey.TASK_STATUS_已完成){
			return false;
		}
		vo.setStatus(TaskConditionTypeKey.TASK_STATUS_已领取奖励);
		return true;
	}

	// ============================== 活动任务结束 =================================


	public void saveTask(){
		saveMainTask(false);;
		saveDailyTask(false);
		saveAdventureTask(false);
		saveAchievementTask(false);
		saveFlyAchieveTask();
		saveKungFuRaceTask();
		saveBackTask();

		taskInfo.update(true);
		if(taskFinish != null){
			taskFinish.setMainSnList(StringZipUtils.zip(Utils.listToString(finishMainTaskSnList)));
			taskFinish.update(true);
		}
	}

	// ===============================================活动任务===========================================================
	private void checkTaskActivityPlan(HumanObject humanObj, int conditionType, Object... objs){
		int activityType = Utils.intValue(objs[0]);
		ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(activityType);
		if(data == null){
			return;
		}

		Map<Integer, ActivityTaskVO> snTaskMap = data.getActivityTask();
		if(snTaskMap.isEmpty()){
			return;
		}
		Object[] newObjs = Arrays.copyOfRange(objs, 1, objs.length);
		Set<Integer> changGroupSet = new HashSet<>();
		MsgAct.act_task_update_s2c.Builder msg = MsgAct.act_task_update_s2c.newBuilder();
		msg.setType(activityType);
		Map<Integer,Integer> noFinishGroupNumMap = new HashMap<>();
		boolean isChange = false;
		for(ActivityTaskVO vo : snTaskMap.values()){
			if(vo.isFinish()){
				continue;
			}
			noFinishGroupNumMap.put(vo.getGroupSn(), noFinishGroupNumMap.getOrDefault(vo.getGroupSn(), 0) + 1);
			if(vo.getConditionType() != conditionType){
				continue;
			}
			ITaskTypeData idata = TaskTypeDataFactory.getTaskTypeData(conditionType);
			if(idata == null){
				Log.task.error("===ITaskTypeData=null, humanId={}, conditionType={} ", humanObj.id, conditionType);
				continue;
			}
			int oldPlan = vo.getPlan();
			idata.checkPlan(humanObj, vo, newObjs);
			isChange = isChange || (oldPlan != vo.getPlan());
			if(vo.isFinish()){
				msg.addTaskList(ActivityManager.inst().to_p_act_task(vo));
				changGroupSet.add(vo.getGroupSn());
				noFinishGroupNumMap.put(vo.getGroupSn(), noFinishGroupNumMap.get(vo.getGroupSn()) - 1);
				isChange = true;
			}else if(isChange){
				msg.addTaskList(ActivityManager.inst().to_p_act_task(vo));
			}
		}
		Map<Integer,Integer> perfectMap = Utils.jsonToMapIntInt(data.getActControlData().getPerfectMap());
		for (int group : changGroupSet) {
			if (noFinishGroupNumMap.getOrDefault(group,0) == 0 ){
				perfectMap.put(group, EActivityType.TASK_CAN_GET);
			}
			ConfActivityTaskGroup confGroup = ConfActivityTaskGroup.get(group);
			if(confGroup==null||confGroup.reward == null||confGroup.reward.length == 0){
				continue;
			}
			msg.addTaskList(ActivityManager.inst().to_p_act_task_group(group, perfectMap));
		}
		if(isChange){
			data.getActControlData().setPerfectMap(Utils.mapIntIntToJSON(perfectMap));
			data.saveActivityTask();
		}
		if(msg.getTaskListCount() > 0){
			humanObj.sendMsg(msg);
		}
	}

	// =================================================活动任务============================================================

	// region 飞宠成就
	// =================================================飞宠成就============================================================
	public Map<Integer, FlyAchieveTaskVO> flyAchieveMap = new HashMap<>();

	public void initFlyAchieveTask(String json) {
		if (Utils.isNullOrEmptyJSONString(json)) {
			return;
		}
		JSONArray ja = Utils.toJSONArray(json);
		for (int i = 0; i < ja.size(); i++) {
			FlyAchieveTaskVO taskVO = new FlyAchieveTaskVO(ja.getString(i));
			ConfFlyAchievement conf = ConfFlyAchievement.get(taskVO.getTaskSn());
			flyAchieveMap.put(conf.group, taskVO);
		}
	}

	/**
	 * 飞宠成就进度更新
	 */
	public void checkFlyAchievePlan(HumanObject humanObj, int conditionType, Object... objArray) {
		// 根据条件类型找到所有组别
		List<Integer> groupList = GlobalConfVal.flyAchieveCondTypeGroup.get(conditionType);
		if (groupList.isEmpty()) {
			return;
		}
		ITaskTypeData data = TaskTypeDataFactory.getTaskTypeData(conditionType);
		if (data == null){
			Log.task.error("===ITaskTypeData=null, humanId={}, conditionType={} ", humanObj.id, conditionType);
			return;
		}
		// 遍历组别更新任务
		boolean isSave = false;
		for (Integer group : groupList) {
			boolean isNew = false;
			FlyAchieveTaskVO vo = flyAchieveMap.get(group);
			if (vo == null) {
				int startSn = GlobalConfVal.flyAchieveGroupStartMap.get(group);
				if (startSn == 0) {
					Log.game.error("飞宠成就组别取不到初始成就任务, humanId={}, group={}", humanObj.id, group);
					continue;
				}
				ConfFlyAchievement conf = ConfFlyAchievement.get(startSn);
				vo = new FlyAchieveTaskVO(conf);
				vo.initPlan(humanObj);
				flyAchieveMap.put(group, vo);
				isNew = true;
			}
			// 有可能任务条件类型不一样，这里想支持同一个组别内不同的任务可以配置不同的任务条件
			if (vo.getConditionType() != conditionType) {
				continue;
			}
			int oldPlan = vo.getPlan();
			int oldState = vo.getStatus();
			data.checkPlan(humanObj, vo, objArray);
			// 检测数值是否有变动，有变动就准备保存
			if (vo.getPlan() != oldPlan || vo.getStatus() != oldState || isNew) {
				isSave = true;
				TaskManager.inst().sendTaskInfo(humanObj, vo);
			}
		}
		if (isSave) {
			saveFlyAchieveTask();
		}
	}

	public void submitFlyAchieveTask(HumanObject humanObj, int sn) {
		ConfFlyAchievement conf = ConfFlyAchievement.get(sn);
		if (conf == null) {
			return;
		}
		FlyAchieveTaskVO vo = flyAchieveMap.get(conf.group);
		if (vo == null) {
			return;
		}
		if (vo.getStatus() != TaskConditionTypeKey.TASK_STATUS_已完成) {
			vo.checkStatus();
		}
		if (vo.getStatus() != TaskConditionTypeKey.TASK_STATUS_已完成) {
			return;
		}
		vo.drawAwards(humanObj, MoneyItemLogKey.飞宠成就);
		// 发送已领取的成就任务信息
		TaskManager.inst().sendTaskInfo(humanObj, vo);
		// 领取成就奖励时，通知更新成就统计信息
		TaskManager.inst()._msg_task_fly_achievement_c2s(humanObj);
		// 接受下一个成就任务
		acceptNextFlyAchieveTask(humanObj, vo);
	}

	public void acceptNextFlyAchieveTask(HumanObject humanObj, FlyAchieveTaskVO vo) {
		ConfFlyAchievement conf = ConfFlyAchievement.get(vo.taskSn);
		if (conf.next_id == 0) {
			return;
		}
		conf = ConfFlyAchievement.get(conf.next_id);
		FlyAchieveTaskVO newVO = new FlyAchieveTaskVO(conf);
		newVO.initPlan(humanObj);
		flyAchieveMap.put(conf.group, newVO);
		saveFlyAchieveTask();
		TaskManager.inst().sendTaskInfo(humanObj, newVO);
	}

	public void saveFlyAchieveTask() {
		JSONArray ja = new JSONArray();
		for (FlyAchieveTaskVO vo : flyAchieveMap.values()) {
			ja.add(vo.toString());
		}
		taskInfo.setFlyAchieveTaskJSON(ja.toJSONString());
	}

	public int getFlyAchieveFinishNum() {
		int num = 0;
		for (Map.Entry<Integer, FlyAchieveTaskVO> entry : flyAchieveMap.entrySet()) {
			int group = entry.getKey();
			FlyAchieveTaskVO vo = entry.getValue();
			int sn = GlobalConfVal.flyAchieveGroupStartMap.get(group);
			ConfFlyAchievement conf;
			while ((conf = ConfFlyAchievement.get(sn)) != null) {
				if (sn == vo.getTaskSn()) {
					if (vo.getStatus() == TaskConditionTypeKey.TASK_STATUS_已领取奖励) {
						num++;
					}
					break;
				}
				num++;
				sn = conf.next_id;
			}
		}
		return num;
	}

	public List<Integer> getFlyTotalAchieveRecvIdList() {
		return Utils.strToIntList(taskInfo.getFlyAchieveReceiveIdList());
	}

	public void addFlyTotalAchieveRecvId(int sn) {
		List<Integer> recvList = Utils.strToIntList(taskInfo.getFlyAchieveReceiveIdList());
		recvList.add(sn);
		taskInfo.setFlyAchieveReceiveIdList(Utils.listToString(recvList));
	}
	// =================================================飞宠成就============================================================
	// endregion 飞宠成就

	// region 武道会任务
	// =================================================武道会任务============================================================

	private void initKungFuRaceTask(String json){
		JSONArray ja = Utils.toJSONArray(json);
		for(int i = 0; i < ja.size(); i++){
			TaskVO vo = new TaskVO(ja.getString(i));
			ConfBattleCompetitionTask conf = ConfBattleCompetitionTask.get(vo.taskSn);
			if(conf == null){
				Log.task.error("===ConfBattleCompetitionTask 配表错误， not find sn={}", vo.taskSn);
				continue;
			}
			vo.setConditionType(conf.condition[0]);
			vo.setParam1(conf.condition[1]);
			vo.setSumPlan(conf.condition[2]);
			vo.setAwards(Utils.intArrToIntMap(new HashMap<>(), conf.reward));
			addKungFuRaceTask(vo, false);
		}
	}

	/**
	 * 检查武道会任务
	 * @param humanObj
	 */
	public void checkKungFuRaceTask(HumanObject humanObj){
		int currentSeason = KungFuRaceUtils.getCurrentSeason();
		int mySeason = taskInfo.getKungFuRaceTaskSeason();
		humanObj.kungFuRaceLoginSeason = mySeason;
		if(mySeason != currentSeason){
			// 接取的任务赛季 != 当前赛季，重置武道会任务
			kungFuRaceTaskMap.clear();
			taskInfo.setKungFuRaceTaskSeason(currentSeason);
		}
		acceptKungFuRaceTask(humanObj);
	}

	/**
	 * 接取武道会任务
	 * @param humanObj
	 */
	public void acceptKungFuRaceTask(HumanObject humanObj){
		boolean isUp = false;
		for(ConfBattleCompetitionTask conf : ConfBattleCompetitionTask.findAll()){
			if(kungFuRaceTaskMap.containsKey(conf.sn)){
				continue;
			}
			// 接取配表新增的武道会任务
			TaskVO vo = new TaskVO(conf.sn, TaskConditionTypeKey.TASK_武道会任务, conf.condition, Utils.intArrToIntMap(new HashMap<>(), conf.reward), 0);
			ITaskTypeData iData = TaskTypeDataFactory.getTaskTypeData(vo.getConditionType());
			if(iData != null){
				iData.checkPlan(humanObj, vo, 0);
			}
			kungFuRaceTaskMap.put(vo.taskSn, vo);
			isUp = true;
		}
		if(isUp){
			saveKungFuRaceTask();
		}
	}

	public void addKungFuRaceTask(TaskVO vo, boolean isSave){
		kungFuRaceTaskMap.put(vo.taskSn, vo);
		if(isSave){
			saveKungFuRaceTask();
		}
	}

	public void saveKungFuRaceTask(){
		JSONArray ja = new JSONArray();
		for(TaskVO vo : kungFuRaceTaskMap.values()){
			ja.add(vo.toString());
		}
		taskInfo.setKungFuRaceTaskJSON(ja.toJSONString());
	}

	private void checkTaskKungFuRacePlan(HumanObject humanObj, int conditionType, Object... objs){
		for(TaskVO vo : kungFuRaceTaskMap.values()){
			if(vo.getConditionType() != conditionType){
				continue;
			}
			ITaskTypeData iData = TaskTypeDataFactory.getTaskTypeData(conditionType);
			if(iData == null){
				Log.task.error("===ITaskTypeData=null, humanId={}, conditionType={} ", humanObj.id, conditionType);
				continue;
			}
			iData.checkPlan(humanObj, vo, objs);
		}
	}

	public void submitKungFuRaceTask(HumanObject humanObj, int taskId){
		TaskVO vo = kungFuRaceTaskMap.get(taskId);
		if(vo == null){
			return;
		}
		if(vo.getStatus() != TaskConditionTypeKey.TASK_STATUS_已完成){
			vo.checkStatus();
		}
		if(vo.getStatus() != TaskConditionTypeKey.TASK_STATUS_已完成){
			return;
		}
		vo.drawAwards(humanObj, MoneyItemLogKey.武道会任务);
		TaskManager.inst().sendTaskInfo(humanObj, vo);
		saveKungFuRaceTask();
	}
	// =================================================武道会任务============================================================
	// endregion 武道会任务

	// region 回归任务
	// =================================================回归任务============================================================

	private void initBackTask(String json){
		JSONArray ja = Utils.toJSONArray(json);
		for(int i = 0; i < ja.size(); i++){
			TaskVO vo = new TaskVO(ja.getString(i));
			ConfBackTask conf = ConfBackTask.get(vo.taskSn);
			if(conf == null){
				Log.task.error("===ConfBackTask 配表错误， not find sn={}", vo.taskSn);
				continue;
			}
			vo.setConditionType(conf.condition[0]);
			vo.setParam1(conf.condition[1]);
			vo.setSumPlan(conf.condition[2]);
			vo.setAwards(Utils.intArrToIntMap(new HashMap<>(), conf.reward));
			addBackTask(vo, false);
		}
	}

	/**
	 * 检查回归任务
	 * @param humanObj
	 * @param bReset
	 */
	public void checkBackTask(HumanObject humanObj, boolean bReset){
		if(bReset){
			// 重置回归任务
			backTaskMap.clear();
		}
		acceptBackTask(humanObj);
	}

	/**
	 * 接取回归任务
	 * @param humanObj
	 */
	public void acceptBackTask(HumanObject humanObj){
		boolean isUp = false;
		Human2 human2 = humanObj.getHuman2();
		long backStartTime = human2.getBackStartTime();
		int openDay = human2.getBackOpenDay();
		int lossDay = human2.getBackLossDay();
		int day = Utils.getDaysBetween(backStartTime, Port.getTime()) + 1;
		for(ConfBackTask conf : ConfBackTask.findAll()){
			if(backTaskMap.containsKey(conf.sn)){
				continue;
			}
			if(day < conf.day){
				continue;
			}
			if(conf.open_day == null || conf.open_day.length != 2 || openDay < conf.open_day[0] || openDay > conf.open_day[1]){
				continue;
			}
			if(conf.lost_day == null || conf.lost_day.length != 2 || lossDay < conf.lost_day[0] || lossDay > conf.lost_day[1]){
				continue;
			}
			// 接取配表新增的回归任务
			TaskVO vo = new TaskVO(conf.sn, TaskConditionTypeKey.TASK_回归任务, conf.condition, Utils.intArrToIntMap(new HashMap<>(), conf.reward), 0);
			ITaskTypeData iData = TaskTypeDataFactory.getTaskTypeData(vo.getConditionType());
			if(iData != null){
				iData.checkPlan(humanObj, vo, 0);
			}
			backTaskMap.put(vo.taskSn, vo);
			isUp = true;
		}
		if(isUp){
			saveBackTask();
		}
	}

	public void addBackTask(TaskVO vo, boolean isSave){
		backTaskMap.put(vo.taskSn, vo);
		if(isSave){
			saveBackTask();
		}
	}

	public void saveBackTask(){
		JSONArray ja = new JSONArray();
		for(TaskVO vo : backTaskMap.values()){
			ja.add(vo.toString());
		}
		taskInfo.setBackTaskJSON(ja.toJSONString());
	}

	private void checkTaskBackPlan(HumanObject humanObj, int conditionType, Object... objs){
		for(TaskVO vo : backTaskMap.values()){
			if(vo.getConditionType() != conditionType){
				continue;
			}
			ITaskTypeData iData = TaskTypeDataFactory.getTaskTypeData(conditionType);
			if(iData == null){
				Log.task.error("===ITaskTypeData=null, humanId={}, conditionType={} ", humanObj.id, conditionType);
				continue;
			}
			iData.checkPlan(humanObj, vo, objs);
		}
	}

	public void submitBackTask(HumanObject humanObj, int taskId){
		TaskVO vo = backTaskMap.get(taskId);
		if(vo == null){
			return;
		}
		if(vo.getStatus() != TaskConditionTypeKey.TASK_STATUS_已完成){
			vo.checkStatus();
		}
		if(vo.getStatus() != TaskConditionTypeKey.TASK_STATUS_已完成){
			return;
		}
		vo.drawAwards(humanObj, MoneyItemLogKey.回归任务);
		TaskManager.inst().sendTaskInfo(humanObj, vo);
		saveBackTask();
	}
	// =================================================回归任务============================================================
	// endregion 回归任务

	// region 钓鱼任务
	// =================================================钓鱼任务============================================================

	public void checkFishDailyTask(HumanObject humanObj, int conditionType, Object... objs) {
		FishData fishData = humanObj.operation.fishData;
		if(fishData == null){
			return;
		}
		HomeFish homeFish = fishData.getHomeFish();
		if(homeFish == null){
			return;
		}
		Map<Integer, TaskVO> taskMap = humanObj.operation.fishData.getDailyTasks();
		boolean isChange = checkTaskMapAndBuildMsg(humanObj, taskMap, conditionType, objs);
		if (isChange) {
			fishData.saveDailyTasks();
		}
	}

	public void checkFishGroundTask(HumanObject humanObj, int conditionType, Object... objs) {
		FishData fishData = humanObj.operation.fishData;
		if(fishData == null){
			return;
		}
		HomeFish homeFish = fishData.getHomeFish();
		if(homeFish == null){
			return;
		}
		Map<Integer, TaskVO> taskMap = humanObj.operation.fishData.getFishGroundTasks();
		boolean isChange = checkTaskMapAndBuildMsg(humanObj, taskMap, conditionType, objs);
		if (isChange) {
			JSONArray ja = new JSONArray();
			for(TaskVO vo : taskMap.values()){
				ja.add(vo.toString());
			}
			fishData.saveFishGroundTasks();
		}
	}

	/**
	 * 检查任务Map并发送变更消息
	 * @param humanObj 玩家对象
	 * @param taskMap 任务Map
	 * @return 是否有任务状态变更
	 */
	private boolean checkTaskMapAndBuildMsg(HumanObject humanObj, Map<Integer, TaskVO> taskMap, int conditionType, Object... objs) {
		boolean isChange = false;

		// 创建消息构建器
		MsgTask.task_update_s2c.Builder msg = MsgTask.task_update_s2c.newBuilder();

		for (TaskVO vo : taskMap.values()) {
			if(vo.getConditionType() != conditionType){
				continue;
			}
			if (vo.getStatus() == TaskConditionTypeKey.TASK_STATUS_已完成) {
				continue;
			}

			ITaskTypeData iData = TaskTypeDataFactory.getTaskTypeData(vo.getConditionType());
			if (iData == null) {
				Log.task.error("===ITaskTypeData=null, humanId={}, conditionType={}", humanObj.id, vo.getConditionType());
				continue;
			}

			// 保存旧状态用于比较
			int oldStatus = vo.getStatus();
			int oldPlan = vo.getPlan();
			iData.checkPlan(humanObj, vo, objs);
			if(oldPlan != vo.getPlan() || oldStatus != vo.getStatus()){
				isChange = true;
				msg.addTaskList(TaskManager.inst().to_p_task(vo));
			}
		}
		// 发送消息给客户端
		if (msg.getTaskListCount() > 0) {
			humanObj.sendMsg(msg);
		}
		return isChange;
	}
	// =================================================钓鱼任务============================================================
	// endregion 钓鱼任务
}
