package org.gof.demo.worldsrv.task;

public class TaskConditionTypeKey {

	/* 任务分类*/
	public static final int TASK_活动 = 0;				  //活动任务
	public static final int TASK_主线 = 1;					//主线
	public static final int TASK_每日 = 2;					//每日
	public static final int TASK_冒险任务 = 3;				//冒险任务
	public static final int TASK_战令 = 7;				  //战令任务
	public static final int TASK_功能预告 = 8;			  //功能预告
	public static final int TASK_成就任务 = 9;				//成就任务
	public static final int TASK_飞宠成就 = 10;				// 飞宠成就
	public static final int TASK_武道会任务 = 100;			//武道会任务
	public static final int TASK_回归任务 = 101;				//回归任务
	public static final int TASK_钓鱼每日任务 = 102;			//钓鱼每日任务
	public static final int TASK_渔场解锁 = 103;				//鱼饵任务

	public static final int PARAM_TYPE_1 = 1;				//参数1类型:1个值
	public static final int PARAM_TYPE_2 = 2;				//参数2类型:object数组

	/* 任务状态*/
	public static final int TASK_STATUS_0 = 0;				//任务状态0
	public static final int TASK_STATUS_进行中 = 1;			//任务状态：进行中
	public static final int TASK_STATUS_已完成 = 2;			//任务状态：已完成
	public static final int TASK_STATUS_已领取奖励 = 3;		//任务状态：已领取奖励

	/* 功能预告任务状态*/
	public static final int PREVIEW_STATUS_未完成 = 0;
	public static final int PREVIEW_STATUS_可领奖 = 1;
	public static final int PREVIEW_STATUS_已领奖 = 2;



	/* 每日任务进度奖励状态*/
	public static final int TASK_BOX_NORMAL = 1;			//宝箱状态：未领取
	public static final int TASK_BOX_RECEIVED = 2;			//宝箱状态：已领取

	// 任务类型包含任务分类
	public static final int[] type4Arr = new int[]{TASK_主线, TASK_每日};

	/* 任务重置类型*/
	public static final int TASK_RESET_1 = 1;			//每日重置

	// 任务条件类型
	public static final int TASK_TYPE_0 = 0;				// 未设置
	public static final int TASK_TYPE_1 = 1;				// 人物等级:[1,0,需要的等级]
	public static final int TASK_TYPE_2 = 2;				// 抽卡次数:[2,卡池ID,抽卡次数]
	public static final int TASK_TYPE_3 = 3;				// 累积抽卡次数:[3,卡池ID,抽卡次数]
	public static final int TASK_TYPE_4 = 4;				// 开箱次数:[4,0,开箱次数]
	public static final int TASK_TYPE_5 = 5;				// 累积开箱次数:[5,0,开箱次数]
	public static final int TASK_TYPE_6 = 6;				// 使用道具:[6,道具id,使用个数]
	public static final int TASK_TYPE_7 = 7;				// 穿戴装备:[7,装备品质,装备数量]
	public static final int TASK_TYPE_8 = 8;				// 通过关卡:[8,关卡id,1]
	public static final int TASK_TYPE_9 = 9;				// 出售装备:[9,0,数量]
	public static final int TASK_TYPE_10 = 10;				// 获得道具:[10,道具id,道具数量]
	public static final int TASK_TYPE_11 = 11;				// 通过副本:[11,副本类型,次数]
	public static final int TASK_TYPE_12 = 12;				// 累计通过副本:[12,副本类型,次数]
	public static final int TASK_TYPE_13 = 13;				// 强化技能:[13,0,次数]
	public static final int TASK_TYPE_14 = 14;				// 强化同伴:[14,0,次数]
	public static final int TASK_TYPE_15 = 15;				// 强化遗物:[15,0,次数]
	public static final int TASK_TYPE_16 = 16;				// 击杀敌人:[16,0,数量]
	public static final int TASK_TYPE_17 = 17;				// 收看广告:[17,0,数量]
	public static final int TASK_TYPE_18 = 18;				// 登录游戏:[18,0,天数]
	public static final int TASK_TYPE_19 = 19;				// 竞技场:[19,0,次数]
	public static final int TASK_TYPE_20 = 20;				// 宝箱等级:[20,0,等级]
	public static final int TASK_TYPE_21 = 21;				// 通过副本难度:[21,副本类型,难度等级]
	public static final int TASK_TYPE_22 = 22;				// 选择性别:[22,0,是否拥有性别]
	public static final int TASK_TYPE_23 = 23;				// 分享游戏:[23,0,次数]
	public static final int TASK_TYPE_24 = 24;				// 非历史值的充值天数(仅在限时消费活动中使用）:[24,充值时间,天数]
	public static final int TASK_TYPE_25 = 25;				// 非历史值的充值金额:[25,0,金额]
	public static final int TASK_TYPE_26 = 26;				// 坐骑等级:[26,0,坐骑等级]
	public static final int TASK_TYPE_27 = 27;				// 领取预告奖励:[27,0,是否领取预告奖励]
	public static final int TASK_TYPE_28 = 28;				// 累计雕像乞灵次数:[28,0,次数]
	public static final int TASK_TYPE_29 = 29;				// 完成任务:[29,任务id,1]
	public static final int TASK_TYPE_30 = 30;				// 武魂消耗:[30,0,数量]
	public static final int TASK_TYPE_31 = 31;				// 种植次数:[31,0,数量]
	public static final int TASK_TYPE_32 = 32;				// 收获次数:[32,0,数量]
	public static final int TASK_TYPE_33 = 33;				// 施肥次数:[33,0,数量]
	public static final int TASK_TYPE_34 = 34;				// 偷菜次数:[34,0,数量]
	public static final int TASK_TYPE_35 = 35;				// 赠送鲜花次数:[35,0,数量]
	public static final int TASK_TYPE_36 = 36;				// 累计相思值:[36,0,数量]
	public static final int TASK_TYPE_37 = 37;				// 累计送过我花的玩家人数:[37,0,数量]
	public static final int TASK_TYPE_38 = 38;				// 累计我送过花的玩家人数:[38,0,数量]
	public static final int TASK_TYPE_39 = 39;				// 鲜花数保持为0的天数:[39,0,天数]
	public static final int TASK_TYPE_40 = 40;				// 集齐卡牌种类数:[40,0,种类数]
	public static final int TASK_TYPE_41 = 41;				// 跨服战个人最高积分:[41,0,积分]
	public static final int TASK_TYPE_42 = 42;				// 击杀敌人:[42,对象类型,数量]
	public static final int TASK_TYPE_43 = 43;				// 入侵次数:[43,0,次数]
	public static final int TASK_TYPE_44 = 44;				// 邀请好友数:[44,通关数,邀请好友数]
	public static final int TASK_TYPE_45 = 45;				// 战力达到数值:[45,0,战力数值]
	public static final int TASK_TYPE_46 = 46;				// 完成第n次转职:[46,0,第N次转职]
	public static final int TASK_TYPE_47 = 47;				// 觉醒第n个印记:[47,0,印记数]
	public static final int TASK_TYPE_48 = 48;				// 三消星数达成:[48,关卡,星数]
	public static final int TASK_TYPE_49 = 49;				// 完成其他任务（列表中其他任务都完成时此任务完成）:[49,{其他任务id}]
	public static final int TASK_TYPE_50 = 50;				// 矿山深度:[50,0,到达矿山深度]
	public static final int TASK_TYPE_51 = 51;				// 累计家族捐献次数:[51,0,累计家族捐献次数]
	public static final int TASK_TYPE_52 = 52;				// 累计完成科技数:[52,科技树类型,累计完成的科技数量]
	public static final int TASK_TYPE_53 = 53;				// 庄园等级:[53,0,庄园等级]
	public static final int TASK_TYPE_54 = 54;				// 雕像等级:[54,0,雕像等级]
	public static final int TASK_TYPE_55 = 55;				// 雕像词条:[55,词条品质,词条数量]
	public static final int TASK_TYPE_56 = 56;				// 遗物数量（累计搜寻遗物次数）:[56,0,累计搜寻遗物次数]
	public static final int TASK_TYPE_57 = 57;				// 最高遗物等级:[57,0,最高遗物等级]
	public static final int TASK_TYPE_58 = 58;				// 是否完成跨服排位赛定级赛:[58,0,是否完成跨服排位赛定级赛]
	public static final int TASK_TYPE_59 = 59;				// 盛唐打地鼠玩法生命剩余大于等于n通关关卡X:[59,关卡x,剩余Hp]
	public static final int TASK_TYPE_60 = 60;				// 盛唐打地鼠玩法挑战关卡得分达到x分:[60,关卡x,得分x]
	public static final int TASK_TYPE_61 = 61;				// 盛唐打地鼠玩法击败X敌人N次:[61,敌人id,敌人数目]
	public static final int TASK_TYPE_62 = 62;				// 盛唐打地鼠玩法开启X次大招:[62,0,次数]
	public static final int TASK_TYPE_63 = 63;				// 七日试炼弹射玩法消耗步数:[63,关卡,步数]
	public static final int TASK_TYPE_64 = 64;				// 上阵同伴:[64,上阵同伴品质,该品质以上的同伴数量]
	public static final int TASK_TYPE_65 = 65;				// 装配技能:[65,装配技能品质,该品质以上的技能数量]
	public static final int TASK_TYPE_66 = 66;				// 神器等级:[66,0,神器等级]
	public static final int TASK_TYPE_67 = 67;				// 逆转之战关卡获得星级:[67,关卡,星数]
	public static final int TASK_TYPE_68 = 68;				// 接水管通过关卡:[68,关卡,1]
	public static final int TASK_TYPE_69 = 69;				// 分享等级:[69,需要的等级,需要的人数]
	public static final int TASK_TYPE_70 = 70;				// 分享关卡:[70,需要的关卡,需要的人数]
	public static final int TASK_TYPE_71 = 71;				// 分享抽卡:[71,需要的次数,需要的人数]
	public static final int TASK_TYPE_72 = 72;				// 年兽通过关卡:[72,关卡,1]
	public static final int TASK_TYPE_73 = 73;				// 射箭星数达成:[73,关卡,星数]
	public static final int TASK_TYPE_74 = 74;				// 完成录屏:[74,0,是否点击录屏按钮]
	public static final int TASK_TYPE_75 = 75;				// 电玩室游戏局数:[75,游戏ID,局数]
	public static final int TASK_TYPE_76 = 76;				// 电玩室获得积分:[76,游戏ID,分数]
	public static final int TASK_TYPE_77 = 77;				// 集卡活动集卡种类:[77,活动类型,集卡种类]
	public static final int TASK_TYPE_78 = 78;				// 本周累计提高亲密度:[78,0,亲密度值]
	public static final int TASK_TYPE_79 = 79;				// 本周互赠金币次数:[79,0,次数]
	public static final int TASK_TYPE_80 = 80;				// 本周互相帮忙施肥次数:[80,0,次数]
	public static final int TASK_TYPE_81 = 81;				// 等待活动结束:[81,0,活动类型]
	public static final int TASK_TYPE_94 = 94;				// 进行X次停车:[94,0,次数]
	public static final int TASK_TYPE_95 = 95;				// 抢夺X名跟班:[95,0,个数]
	public static final int TASK_TYPE_96 = 96;				// 聊天频道发言X次:[96,0,次数]

	public static final int TASK_TYPE_98 = 98;				// 武道会挑战X次:[98,0,次数]
	public static final int TASK_TYPE_99 = 99;				// 武道会积分达到X分:[99,0,积分]
	public static final int TASK_TYPE_100 = 100;			// 竞技场击败其他阵营玩家:[100,0,次数]
	public static final int TASK_TYPE_101 = 101;			// 竞技场名次达到第X名:[101,0,名次]
	public static final int TASK_TYPE_102 = 102;			// 竞技场获胜X次:[102,0,次数]
	public static final int TASK_TYPE_103 = 103;			// 获取对应时装:[103,时装id,等级]
	public static final int TASK_TYPE_104 = 104;			// 累计赠送金币数量:[104,0,金币数量]
	public static final int TASK_TYPE_105 = 105;			// 累计帮好友施肥:[105,0,次数]
	public static final int TASK_TYPE_106 = 106;			// X名好友亲密度达到Y级:[106,好感度等级,人数]
	public static final int TASK_TYPE_107 = 107;			// 家族帮助Y次:[107,0,次数]
	public static final int TASK_TYPE_108 = 108;			// 驱赶小偷X次:[108,0,次数]
	public static final int TASK_TYPE_109 = 109;			// 答题获得家族前3名 X次:[109,0,次数]
	public static final int TASK_TYPE_110 = 110;			// 家族乱斗累计战胜X名对手:[110,0,人数]
	public static final int TASK_TYPE_111 = 111;			// 跨服车位驻守战胜X名对手:[111,0,人数]
	public static final int TASK_TYPE_112 = 112;			// 跨服车位抢夺战胜X名对手:[112,0,人数]
	public static final int TASK_TYPE_113 = 113;			// 跨服战玩家累计积分:[113,0,积分]
	public static final int TASK_TYPE_114 = 114;			// 累计发放X个红包:[114,0,次数]
	public static final int TASK_TYPE_115 = 115;			// 跨服排名赛达到过XX大段位:[115,段位,1]
	public static final int TASK_TYPE_116 = 116;			// 红包手气最好X次:[116,0,次数]
	public static final int TASK_TYPE_117 = 117;			// 马里奥玩法获得金币大于等于n通关关卡X:[117,关卡x,金币]
	public static final int TASK_TYPE_118 = 118;			// 激活X件时装:[118,0,数量]
	public static final int TASK_TYPE_119 = 119;			// 激活X件神器:[119,0,数量]
	public static final int TASK_TYPE_120 = 120;			// 激活X件高级坐骑:[120,0,数量]
	public static final int TASK_TYPE_121 = 121;			// 激活X件背饰化形:[121,0,数量]
	public static final int TASK_TYPE_122 = 122;			// 通过夺回神灯关卡:[122,0,数量]

	public static final int TASK_TYPE_1013 = 1013;			// 加好友:[1013,0,数量]

	public static final int TASK_TYPE_1101 = 1101;			// 累计解锁飞宠:[1101,0,解锁数量]
	public static final int TASK_TYPE_1102 = 1102;			// 累计培育飞宠:[1102,0,培育次数]
	public static final int TASK_TYPE_1103 = 1103;			// 累计飞宠被借用次数:[1103,0,被借用次数]
	public static final int TASK_TYPE_1104 = 1104;			// 累计借用他人飞宠次数:[1104,0,借用次数]
	public static final int TASK_TYPE_1105 = 1105;			// 获得一只x个词条都是x色词条的飞宠:[1105,词条品质,词条个数]
	public static final int TASK_TYPE_1106 = 1106;			// 获得一只成长值大于等于x的飞宠:[1106,0,成长率]
	public static final int TASK_TYPE_1107 = 1107;			// 获得一只培育代数大于等于x的飞宠:[1107,0,培育代数]
	public static final int TASK_TYPE_1108 = 1108;			// 累计使用同一个飞宠的培育次数大于等于x:[1108,0,培育次数]
	public static final int TASK_TYPE_1109 = 1109;			// 同一只飞宠累计被借用的次数大于等于x:[1109,0,被借用的次数]
	public static final int TASK_TYPE_1110 = 1110;			// 提升一只飞宠的等级到x级:[1110,0,飞宠等级]


	public static final int TASK_TYPE_2000 = 2000;			// 神灯等级和星级:[2000,等级,星级]
	public static final int TASK_TYPE_2001 = 2001;			// 魔王军关卡:[2001,关卡,1]
	public static final int TASK_TYPE_2002 = 2002;			// 魔王军战力:[2002,0,战力]
	public static final int TASK_TYPE_2004 = 2004;			// 吃豆总数:[2004,0,数量]
	public static final int TASK_TYPE_2005 = 2005;			// 史莱姆战力:[2005,0,战力]
	public static final int TASK_TYPE_2006 = 2006;			// 史莱姆体力消耗:[2006,0,体力消耗]
	public static final int TASK_TYPE_2007 = 2007;			// 活动数值:[2007,0,值]
	public static final int TASK_TYPE_2008 = 2008;			// 活动累计数值:[2008,0,累加值]
	public static final int TASK_TYPE_2010 = 2010;			// 转剑关卡:[2010,0,关卡]
	public static final int TASK_TYPE_2011 = 2011;			// 转剑战力:[2011,0,战力]
	public static final int TASK_TYPE_2012 = 2012;			// 转剑体力消耗:[2012,0,体力消耗]

	// 钓鱼相关任务条件类型
	public static final int TASK_TYPE_2014 = 2014;			// 钓鱼等级达到:[2014,0,钓鱼等级]
	public static final int TASK_TYPE_2015 = 2015;			// 渔具总强化等级达到:[2015,0,渔具总等级]
	public static final int TASK_TYPE_2016 = 2016;			// 获得指定长度级别的鱼:[2016,鱼长度级别,数量]
	public static final int TASK_TYPE_2017 = 2017;			// 渔册总评分:[2017,0,渔册总评分]
	public static final int TASK_TYPE_2018 = 2018;			// 获得指定类型的鱼:[2018,鱼类型,数量]
	public static final int TASK_TYPE_2019 = 2019;			// 获得指定品质的鱼:[2019,鱼品质,数量]
	public static final int TASK_TYPE_2021 = 2021;			// 成功钓鱼次数:[2021,0,数量]
	public static final int TASK_TYPE_2022 = 2022;			// 从商店购买鱼饵:[2022,0,数量]
	public static final int TASK_TYPE_2020 = 2020;			// 完成x次钓鱼：[2020,0,次数]

	public static final int TASK_TYPE_2023 = 2023;			// 空投礼包奖励:[2023,0,积分]
	public static final int TASK_TYPE_2024 = 2024;			// 合成大西瓜合成指定类型的:[2024,类型,数量]
}
