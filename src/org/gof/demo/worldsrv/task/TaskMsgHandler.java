package org.gof.demo.worldsrv.task;

import org.gof.core.support.observer.MsgReceiver;
import org.gof.demo.seam.msg.MsgParam;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.msg.MsgTask;

/** 
 * 任务 
 * <AUTHOR>
 * @Date 2024/3/7
 * @Param 
 */
public class TaskMsgHandler {
	
	/** 
	 * 提交任务
	 * <AUTHOR>
	 * @Date 2024/3/7
	 * @Param 
	 */
	@MsgReceiver(MsgTask.task_commit_c2s.class)
	public void _msg_task_commit_c2s(MsgParam param){
		HumanObject humanObj = param.getHumanObject();
		MsgTask.task_commit_c2s msg = param.getMsg();
		TaskManager.inst()._msg_task_commit_c2s(humanObj, msg.getType(), msg.getTaskId());
	}


	@MsgReceiver(MsgTask.task_commit_all_c2s.class)
	public void _msg_task_commit_all_c2s(MsgParam param){
		HumanObject humanObj = param.getHumanObject();
		MsgTask.task_commit_all_c2s msg = param.getMsg();
		TaskManager.inst()._msg_task_commit_all_c2s(humanObj, msg.getType());
	}

	@MsgReceiver(MsgTask.task_req_daily_box_c2s.class)
	public void _msg_task_req_daily_box_c2s(MsgParam param){
		HumanObject humanObj = param.getHumanObject();
		MsgTask.task_req_daily_box_c2s msg = param.getMsg();
		TaskManager.inst()._msg_task_req_daily_box_c2s(humanObj);
	}


	@MsgReceiver(MsgTask.task_action_finish_channel_c2s.class)
	public void _msg_task_action_finish_channel_c2s(MsgParam param){
		HumanObject humanObj = param.getHumanObject();
		MsgTask.task_action_finish_channel_c2s msg = param.getMsg();
		TaskManager.inst()._msg_task_action_finish_channel_c2s(humanObj, msg.getChannel());
	}

	@MsgReceiver(MsgTask.task_achievement_c2s.class)
	public void _msg_task_achievement_c2s(MsgParam param){
		TaskManager.inst()._msg_task_achievement_c2s(param.getHumanObject());
	}

	@MsgReceiver(MsgTask.task_achievement_reward_c2s.class)
	public void _msg_task_achievement_reward_c2s(MsgParam param){
		TaskManager.inst()._msg_task_achievement_reward_c2s(param.getHumanObject());
	}

	@MsgReceiver(MsgTask.task_fly_achievement_c2s.class)
	public void _msg_task_fly_achievement_c2s(MsgParam param) {
		HumanObject humanObj = param.getHumanObject();
		MsgTask.task_fly_achievement_c2s msg = param.getMsg();
		TaskManager.inst()._msg_task_fly_achievement_c2s(humanObj);
	}

	@MsgReceiver(MsgTask.task_fly_achievement_reward_c2s.class)
	public void _msg_task_fly_achievement_reward_c2s(MsgParam param) {
		HumanObject humanObj = param.getHumanObject();
		MsgTask.task_fly_achievement_reward_c2s msg = param.getMsg();
		TaskManager.inst()._msg_task_fly_achievement_reward_c2s(humanObj);
	}
}
