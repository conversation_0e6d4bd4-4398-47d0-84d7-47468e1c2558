package org.gof.demo.worldsrv.jobs;

import org.gof.core.support.ManagerBase;
import org.gof.core.support.Utils;
import org.gof.demo.battlesrv.manager.CombatChangeLog;
import org.gof.demo.battlesrv.manager.PropManager;
import org.gof.demo.battlesrv.msgHandler.SkillManager;
import org.gof.demo.battlesrv.support.PropCalc;
import org.gof.demo.worldsrv.activity.ActivityManager;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.*;
import org.gof.demo.worldsrv.entity.HumanBrief;
import org.gof.demo.worldsrv.entity.Profession;
import org.gof.demo.worldsrv.entity.UnitPropPlus;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.human.EModule;
import org.gof.demo.worldsrv.human.HumanManager;
import org.gof.demo.worldsrv.human.RoleInfoKey;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgJobsWakeup;
import org.gof.demo.worldsrv.msg.MsgRole;
import org.gof.demo.worldsrv.msg.MsgSkill;
import org.gof.demo.worldsrv.pet.PetManager;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.ReasonResult;
import org.gof.demo.worldsrv.support.Util;
import org.gof.demo.worldsrv.support.enumKey.ConfGlobalKey;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;
import org.gof.demo.worldsrv.task.TaskConditionTypeKey;
import org.gof.demo.worldsrv.task.TaskManager;

import java.util.*;
import java.util.stream.Collectors;


public class JobsManager extends ManagerBase {

	/** 
	 * 获取实例
	 * <AUTHOR>
	 * @Date 2024/3/4
	 * @Param 
	 */
	public static JobsManager inst() {
		return inst(JobsManager.class);
	}

	public void _msg_jobs_wakeup_info_c2s(HumanObject humanObj){
		MsgJobsWakeup.jobs_wakeup_info_s2c.Builder msg = MsgJobsWakeup.jobs_wakeup_info_s2c.newBuilder();
		Profession profession = humanObj.operation.profession;
		// TODO 觉醒方案还没做，协议过来，先这么处理
		Define.p_jobs_wakeup_plan.Builder pjwpb = Define.p_jobs_wakeup_plan.newBuilder();
		pjwpb.setPlanId(0);
		Map<Integer, Integer> posLvMap = Utils.jsonToMapIntInt(profession.getPosLvMap());
		for(Map.Entry<Integer, Integer> entry : posLvMap.entrySet()){
			pjwpb.addWakeupList(to_p_jobs_wakeup(entry.getKey(), entry.getValue(), 0));
		}
		msg.setUsePlan(0);
		msg.addWakeupPlanList(pjwpb);
		humanObj.sendMsg(msg);
	}
	

	public void _msg_role_change_job_c2s(HumanObject humanObj, int id) {
		Profession profession = humanObj.operation.profession;
		int jobSn = ConfGlobal.get(ConfGlobalKey.默认职业.SN).value;
		if (id == jobSn) {
			_msg_reset_c2s(humanObj);
			return;
		}
		int level = humanObj.getHuman().getLevel();
		if (profession.getJobSn() == id) {
			ConfJobs confJobsNew = ConfJobs.get(id);
			Map<Integer, Integer> skillSnMap = new HashMap<>();
			for (int i = 0; i < confJobsNew.passive_skill.length; i++) {
				int needLv = confJobsNew.passive_skill[i][0];
				if(level < needLv){
					continue;
				}
				skillSnMap.put(confJobsNew.passive_skill[i][1], confJobsNew.passive_skill[i][2]);
			}
			// 被动更改
			profession.setPassiveSkillMap(Utils.mapIntIntToJSON(skillSnMap));
		} else {
			ConfJobs confJobs = ConfJobs.get(profession.getJobSn());
			ConfJobs confJobsNew = ConfJobs.get(id);
			if(confJobsNew == null){
				return;
			}
			List<Integer> snList = Utils.intArrToList(confJobs.job_change);
			if(!snList.contains(id)){
				return;
			}
			profession.setJobSn(confJobsNew.sn);
			profession.setJobLv(confJobsNew.skill[0]);
			profession.setJobSkillSn(confJobsNew.skill[1]);
			profession.setJobSkillLv(confJobsNew.skill[2]);
			humanObj.getHuman().setJobModel(id);
			humanObj.getHuman2().setHairColor(confJobsNew.skin);

			Map<Integer, Integer> skillSnLvMap = new HashMap<>();
			for (int i = 0; i < confJobsNew.passive_skill.length; i++) {
				// 需求玩家等级
				int needLv = confJobsNew.passive_skill[i][0];
				if (level < needLv) {
					continue;
				}
				// 技能sn和技能等级
				skillSnLvMap.put(confJobsNew.passive_skill[i][1], confJobsNew.passive_skill[i][2]);
			}
			// 被动更改
			profession.setPassiveSkillMap(Utils.mapIntIntToJSON(skillSnLvMap));
			SkillManager.inst().sendMsg_skill_active_update_s2c(humanObj);
			profession.setJobChange(profession.getJobChange()+1);
			HumanManager.inst().checkCrossSyns(humanObj, HumanBrief.K.jobSn, HumanBrief.K.roleFigure, HumanBrief.K.battleRole);

			// 处理觉醒印记变动
			if (confJobsNew.passive_imprint != null) {
				Map<Integer, Integer> posLvMap = Utils.jsonToMapIntInt(profession.getPosLvMap());
				if (!posLvMap.isEmpty()) {
					// 取上次处理觉醒印记变动的配表
					ConfJobs confJobsLastWake = ConfJobs.get(profession.getWakeJobSn());
					if (confJobsLastWake != null && confJobsLastWake.passive_imprint != null) {
						Map<Integer, Integer> indexLevelMap = new HashMap<>();// Map<觉醒印记sn对应的下标, 等级>
						List<Integer> impritList = Arrays.stream(confJobsLastWake.passive_imprint).boxed().collect(Collectors.toList());
						List<Integer> wakeSnList = new ArrayList<>();
						for (Integer wakeSn : posLvMap.keySet()) {
							int index = impritList.indexOf(wakeSn);
							if (index == -1) {
								wakeSnList.add(wakeSn);
								// 做一下容错，因为最早一批的号可以觉醒其他职业的印记，如果不过滤index=-1的情况，下面的代码会报错，然后不转成职业对应的印记
								continue;
							}
							indexLevelMap.put(index, posLvMap.get(wakeSn));
						}
						if (!wakeSnList.isEmpty()) {
							// 上面容错要纠正错误的数据，否则还是会出错
							wakeSnList.sort(Comparator.naturalOrder());
							for(int i = 0; i < confJobsLastWake.passive_imprint.length && !wakeSnList.isEmpty(); i++){
								if (!indexLevelMap.containsKey(i)) {
									int wakeSn = wakeSnList.remove(0);
									indexLevelMap.put(i, posLvMap.getOrDefault(wakeSn, 0));
								}
							}
						}
						posLvMap.clear();
						for (Integer index : indexLevelMap.keySet()) {
							posLvMap.put(confJobsNew.passive_imprint[index], indexLevelMap.get(index));
						}
						profession.setPosLvMap(Utils.mapIntIntToJSON(posLvMap));
						profession.setWakeJobSn(confJobsNew.sn);
						_msg_jobs_wakeup_info_c2s(humanObj);
					} else {
						Log.game.error("上次觉醒印记变更sn配表为空或者配表的觉醒印记为空, sn={}", profession.getWakeJobSn());
					}
				}
			}
		}
		SkillManager.inst().sendMsg_skill_passive_update_s2c(humanObj);
		profession.update();
		updatePorpCalc(humanObj);
		sendMsg_role_info_change_s2c(humanObj);
		ActivityManager.inst().addActivityProgress(humanObj, TaskConditionTypeKey.TASK_TYPE_46, 0);
		humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_冒险任务, TaskConditionTypeKey.TASK_TYPE_46);
		PetManager.inst().checkPetPos(humanObj);// 处理转职成新职业后，同伴可上阵位置+1
	}


	public void _msg_jobs_wakeup_upgrade_c2s(HumanObject humanObj, int id, int lv) {
		Profession profession = humanObj.operation.profession;
		Map<Integer, Integer> posLvMap = Utils.jsonToMapIntInt(profession.getPosLvMap());
		int level = posLvMap.get(id);
		if(!posLvMap.containsKey(id) || level <= 0){
			return;
		}
		int addLv = 0;
		Map<Integer, Integer> costItemNumMap = Utils.jsonToMapIntInt(profession.getCostAddLvMap());
		for (int i = 0; i < lv; i++) {
			ConfJobsWakeup_0 conf = ConfJobsWakeup_0.get(id, level + i);
			if (conf == null) {
				continue;
			}
			ConfJobsWakeup_0 confNext = ConfJobsWakeup_0.get(id, level + i + 1);
			if (confNext == null) {
				continue;
			}
			ReasonResult result = ProduceManager.inst().canCostIntArr(humanObj, conf.cost);
			if (!result.success) {
				break;
			}
			addLv++;
			ProduceManager.inst().costIntArr(humanObj, conf.cost, MoneyItemLogKey.职业觉醒升级);
			costItemNumMap = Utils.intArrToIntMap(costItemNumMap, conf.cost);
		}
		if (addLv <= 0) {
			return;
		}
		posLvMap.put(id, addLv + level);
		profession.setPosLvMap(Utils.mapIntIntToJSON(posLvMap));
		profession.setCostAddLvMap(Utils.mapIntIntToJSON(costItemNumMap));
		// TODO 通知
		sendMsg_jobs_wakeup_upgrade_s2c(humanObj, id, addLv + level);
		updatePorpCalc(humanObj);
		humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_冒险任务, TaskConditionTypeKey.TASK_TYPE_47);
	}

	/**
	 * 更新职业觉醒属性加成
	 */
	public void updatePorpCalc(HumanObject humanObj) {
		Profession profession = humanObj.operation.profession;
		SkillManager.inst().updatePower(humanObj);
		Map<Integer, Integer> passiveSkillSnMap = Utils.jsonToMapIntInt(profession.getPassiveSkillMap());
		PropCalc propCalc = new PropCalc();
		for (Map.Entry<Integer, Integer> entry : passiveSkillSnMap.entrySet()) {
			ConfSkillLevel_0 confSkillLevel0  = ConfSkillLevel_0.get(entry.getKey(), entry.getValue());
			if (confSkillLevel0 == null) {
				Log.game.error("找不到ConfSkillLevel_0配表，id={}，lv={}", entry.getKey(), entry.getValue());
				continue;
			}
			if(confSkillLevel0.attrType == null || confSkillLevel0.attrType[0] != 1){
				propCalc.plus(confSkillLevel0.ownEffect);
			}
			if (GlobalConfVal.summonerSkillMap.containsKey(entry.getKey())) {
				List<Integer> petSnList = humanObj.operation.petData.lineupMap.get(humanObj.getHuman2().getUsePetLineup());
				Map<Integer, Integer> jobPetPassSkillMap = PetManager.inst().getJobPetPassSkillMap(entry.getKey(), petSnList);
				if (jobPetPassSkillMap != null) {
					for (Map.Entry<Integer, Integer> entry2 : jobPetPassSkillMap.entrySet()) {
						confSkillLevel0  = ConfSkillLevel_0.get(entry2.getKey(), entry2.getValue());
						if (confSkillLevel0 == null) {
							Log.game.error("找不到ConfSkillLevel_0配表，id={}，lv={}", entry2.getKey(), entry2.getValue());
							continue;
						}
						if (confSkillLevel0.attrType == null || confSkillLevel0.attrType[0] != 1) {
							propCalc.plus(confSkillLevel0.ownEffect);
						}
					}
				}
			}
		}
		Map<Integer, Integer> posLvMap = Utils.jsonToMapIntInt(profession.getPosLvMap());
		long power = 0;
		for (Map.Entry<Integer, Integer> entry : posLvMap.entrySet()) {
			int id = entry.getKey();
			int lv = entry.getValue();
			ConfJobsWakeup_0 conf = ConfJobsWakeup_0.get(id, lv);
			if (conf == null) {
				Log.game.error("找不到ConfJobsWakeup_0配表，id={}，lv={}", id, lv);
				continue;
			}
			propCalc.plus(conf.value_plus);
			power += conf.power;
		}
		HumanManager.inst().updatePowerPar(humanObj, EModule.jobsWakeup, power);
		humanObj.dataPers.unitPropPlus.setFieldRead(UnitPropPlus.K.jobAwake, propCalc.toJSONStr());
		PropManager.inst().propCalc(humanObj, CombatChangeLog.职业觉醒);
	}

	private void sendMsg_jobs_wakeup_upgrade_s2c(HumanObject humanObj, int id, int lv){
		MsgJobsWakeup.jobs_wakeup_upgrade_s2c.Builder msg = MsgJobsWakeup.jobs_wakeup_upgrade_s2c.newBuilder();
		msg.setWakeupImprint(to_p_jobs_wakeup(id, lv, 0));
		humanObj.sendMsg(msg);
	}

	public Define.p_jobs_wakeup to_p_jobs_wakeup(int id, int lv, int cfgId) {
		Define.p_jobs_wakeup.Builder dInfo = Define.p_jobs_wakeup.newBuilder();
		dInfo.setPos(id);
		dInfo.setLevel(lv);
		dInfo.setCfgId(cfgId);
		return dInfo.build();
	}

	public void _msg_reset_c2s(HumanObject humanObj) {
		Profession profession = humanObj.operation.profession;
		ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.reset_job.SN);
		// 策划要求写死，只有一个数据
		ReasonResult result = ProduceManager.inst().canCostProduce(humanObj, confGlobal.intArray[0], confGlobal.intArray[1]);
		if(result.success){
			ProduceManager.inst().costItem(humanObj, confGlobal.intArray[0], confGlobal.intArray[1], MoneyItemLogKey.职业重置消耗);
		} else {
			ConfGoods confGoods = ConfGoods.get( confGlobal.intArray[0]);
			if(confGoods == null){
				Log.temp.error("找不到ConfGoods，id={}", confGlobal.intArray[0]);
				return;
			}
			ReasonResult result2 = ProduceManager.inst().canCostProduce(humanObj, confGoods.price[0], confGoods.price[1]);
			if(!result2.success){
				return;
			}
			ProduceManager.inst().costItem(humanObj, confGoods.price[0], confGoods.price[1], MoneyItemLogKey.职业重置消耗);
		}

		int jobOldSn = profession.getJobSn();
		int jobSn = ConfGlobal.get(ConfGlobalKey.默认职业.SN).value;
		ConfJobs confJobs = ConfJobs.get(jobSn);
		profession.setJobSn(jobSn);
		humanObj.getHuman().setJobModel(jobSn);
		profession.setJobLv(confJobs.skill[0]);
		profession.setJobSkillSn(confJobs.skill[1]);
		profession.setJobSkillLv(confJobs.skill[2]);
		humanObj.getHuman2().setHairColor(confJobs.skin);

		MsgSkill.skill_passive_update_s2c.Builder msg = MsgSkill.skill_passive_update_s2c.newBuilder();
		Map<Integer, Integer> passiveSkillSnMap = Utils.jsonToMapIntInt(profession.getPassiveSkillMap());
		for (Map.Entry<Integer, Integer> entry : passiveSkillSnMap.entrySet()) {
			Define.p_passive_skill.Builder dPassive = Define.p_passive_skill.newBuilder();
			dPassive.setSkillId(entry.getKey());
			dPassive.setSkillLv(entry.getValue());
			msg.addDeleteList(dPassive);
			if (GlobalConfVal.summonerSkillMap.containsKey(entry.getKey())) {
				List<Integer> petSnList = humanObj.operation.petData.lineupMap.get(humanObj.getHuman2().getUsePetLineup());
				Map<Integer, Integer> jobPetPassSkillMap = PetManager.inst().getJobPetPassSkillMap(entry.getKey(), petSnList);
				if (jobPetPassSkillMap!= null) {
					for (Map.Entry<Integer, Integer> entry2 : jobPetPassSkillMap.entrySet()) {
						dPassive = Define.p_passive_skill.newBuilder();
						dPassive.setSkillId(entry2.getKey());
						dPassive.setSkillLv(entry2.getValue());
						msg.addDeleteList(dPassive);
					}
				}
			}
		}
		humanObj.sendMsg(msg);

		profession.setPassiveSkillMap("");

		// 觉醒卷返还
		Map<Integer, Integer> costItemNumMap = Utils.jsonToMapIntInt(profession.getCostAddLvMap());
		profession.setCostAddLvMap("");
		Map<Integer, Integer> posLvMap = Utils.jsonToMapIntInt(profession.getPosLvMap());
		for(int id : posLvMap.keySet()){
			posLvMap.put(id, 1);
		}
		profession.setPosLvMap(Utils.mapIntIntToJSON(posLvMap));
		int wakeJobSn = profession.getWakeJobSn();
		if (profession.getWakeJobSn() != 0) {
			ConfJobs confJobdOldLastWake = ConfJobs.get(profession.getWakeJobSn());
			ConfJobs confJobdOld = ConfJobs.get(jobOldSn);
			if (confJobdOldLastWake.change_times <= confJobdOld.change_times) {
				wakeJobSn = jobOldSn;
			}
		} else {
			wakeJobSn = jobOldSn;
		}
		profession.setWakeJobSn(wakeJobSn);
		ProduceManager.inst().produceAll(humanObj, costItemNumMap, MoneyItemLogKey.职业重置返还);

		humanObj.getHuman().setJobModel(jobSn);
		updatePorpCalc(humanObj);
		sendMsg_role_info_change_s2c(humanObj);
		_msg_jobs_wakeup_info_c2s(humanObj);
		SkillManager.inst().sendMsg_skill_active_update_s2c(humanObj);
		SkillManager.inst().sendMsg_skill_passive_update_s2c(humanObj);
		HumanManager.inst().checkCrossSyns(humanObj, HumanBrief.K.jobSn, HumanBrief.K.roleFigure, HumanBrief.K.battleRole);
		PetManager.inst().handleJobReset(humanObj);
	}

	public void sendMsg_role_info_change_s2c(HumanObject humanObj){
		Profession profession = humanObj.operation.profession;
		MsgRole.role_info_change_s2c.Builder msg = MsgRole.role_info_change_s2c.newBuilder();
		Define.p_role_change.Builder dChange = Define.p_role_change.newBuilder();
		dChange.addKv(HumanManager.inst().to_p_key_value(RoleInfoKey.ROLE_ATTR_CUR_JOB.getKey(), profession.getJobSn()));
		dChange.addKv(HumanManager.inst().to_p_key_value(RoleInfoKey.ROLE_ATTR_STYLE.getKey(), profession.getJobSn()));
		msg.setChangeList(dChange);
		humanObj.sendMsg(msg);
	}

	// 随机激活
	public void _msg_jobs_wakeup_wakeup_c2s(HumanObject humanObj) {
		ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.jobs_wakeup_cost.SN);
		int[][] costs = Utils.parseIntArray2(confGlobal.strValue);
		ReasonResult result = ProduceManager.inst().canCostIntArr(humanObj, costs);
		if (!result.success) {
			return;
		}
		Profession profession = humanObj.operation.profession;
		Map<Integer, Integer> posLvMap = Utils.jsonToMapIntInt(profession.getPosLvMap());
		ConfJobs confJobs = ConfJobs.get(profession.getJobSn());
		List<Integer> imprintList = Arrays.stream(confJobs.passive_imprint).boxed().collect(Collectors.toList());
		imprintList.removeAll(posLvMap.keySet());
		if (imprintList.size() == 0) {
			return;
		}
		int index = Utils.random(imprintList.size());
		int jobWakeId = imprintList.get(index);
		ConfJobsWakeup_0 conf = ConfJobsWakeup_0.get(jobWakeId, 1);
		if (conf == null) {
			return;
		}
		ProduceManager.inst().costIntArr(humanObj, costs, MoneyItemLogKey.职业觉醒);
		posLvMap.put(conf.id, 1);
		profession.setPosLvMap(Utils.mapIntIntToJSON(posLvMap));
		sendMsg_jobs_wakeup_wakeup_s2c(humanObj, conf.id, 1, conf.id);
		updatePorpCalc(humanObj);
		humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_冒险任务, TaskConditionTypeKey.TASK_TYPE_47);
	}


	private void sendMsg_jobs_wakeup_wakeup_s2c(HumanObject humanObj, int id, int lv, int cfgId){
		MsgJobsWakeup.jobs_wakeup_wakeup_s2c.Builder msg = MsgJobsWakeup.jobs_wakeup_wakeup_s2c.newBuilder();
		msg.setWakeupImprint(to_p_jobs_wakeup(id, lv, cfgId));
		humanObj.sendMsg(msg);
	}

	public void _msg_jobs_wakeup_item_transform_c2s(HumanObject humanObj, int num) {
		ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.jobs_wakeup_cost.SN);
		int[][] costs = Utils.parseIntArray2(confGlobal.strValue);
		for (int i = 0; i < costs.length; i++) {
			costs[i][1] *= num;
		}
		ReasonResult result = ProduceManager.inst().checkAndCostItem(humanObj, costs, MoneyItemLogKey.职业觉醒道具转换);
		if (!result.success) {
			return;
		}
		ConfGlobal confConvert = ConfGlobal.get(ConfGlobalKey.jobs_wakeup_props_convert.SN);
		int[][] adds = Utils.parseIntArray2(confConvert.strValue);
		for (int i = 0; i < adds.length; i++) {
			adds[i][1] *= num;
		}
		ProduceManager.inst().produceAdd(humanObj, adds, MoneyItemLogKey.职业觉醒道具转换);
	}

	/**
	 * 根据职业决定同伴上阵使用的字段是equipEffect还是equipEffect1
	 */
	public int getPetSpecialEffectType(int jobSn) {
		ConfJobs confJobs = ConfJobs.get(jobSn);
		if (confJobs == null) {
			Log.game.error("找不到ConfJobs, sn={}", jobSn);
			return 0;
		}
		ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.summoner_passive1_jobtype);
		if (confGlobal == null) {
			Log.game.error("找不到ConfGlobal, sn={}", ConfGlobalKey.summoner_passive1_jobtype);
			return 0;
		}
		if (confGlobal.value == confJobs.type) {
			return 1;
		}
		return 0;
	}
}
