package org.gof.demo.worldsrv.captureSlave;

import com.alibaba.fastjson.JSONObject;
import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.interfaces.ISerilizable;
import org.gof.core.support.Utils;

import java.io.IOException;
import java.util.Map;

public class SlaveVo implements ISerilizable {
    public String slaveName = "";
    public int entTime;


    public SlaveVo() {
    }
    public SlaveVo(String jsonString) {
        JSONObject json = Utils.toJSONObject(jsonString);
        if(json.isEmpty()){
            return;
        }
        slaveName = json.getString("n");
        entTime = json.getIntValue("et");

    }
    public SlaveVo(String slaveName, int entTime) {
        this.slaveName = slaveName;
        this.entTime = entTime;
    }

    public String toJsonString() {
        JSONObject json = new JSONObject();
        json.put("n", slaveName);
        json.put("et", entTime);
        return json.toJSONString();
    }

    @Override
    public void writeTo(OutputStream out) throws IOException {
        out.write(slaveName);
        out.write(entTime);
    }

    @Override
    public void readFrom(InputStream in) throws IOException {
        slaveName = in.read();
        entTime = in.read();
    }

    public static Map<Long,SlaveVo> mapFromJsonString(String jsonStr) {
        JSONObject jsonObject = Utils.toJSONObject(jsonStr);
        java.util.HashMap<Long, SlaveVo> map = new java.util.HashMap<>();
        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
            SlaveVo vo = new SlaveVo(entry.getValue().toString());
            map.put(Long.parseLong(entry.getKey()), vo);
        }
        return map;
    }

    public static String mapToJsonString(Map<Long, SlaveVo> map) {
        JSONObject jsonObject = new JSONObject();
        for (Map.Entry<Long, SlaveVo> entry : map.entrySet()) {
            jsonObject.put(entry.getKey().toString(), entry.getValue().toJsonString());
        }
        return jsonObject.toJSONString();
    }
}
