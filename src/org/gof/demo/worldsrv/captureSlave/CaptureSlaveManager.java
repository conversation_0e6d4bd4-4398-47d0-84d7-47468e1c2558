package org.gof.demo.worldsrv.captureSlave;

import io.vertx.core.Future;
import io.vertx.core.json.JsonArray;
import io.vertx.redis.client.Response;
import org.apache.commons.lang3.StringUtils;
import org.gof.core.Port;
import org.gof.core.dbsrv.redis.AwaitUtil;
import org.gof.core.dbsrv.redis.EntityManager;
import org.gof.core.dbsrv.redis.RedisTools;
import org.gof.core.support.*;
import org.gof.core.support.observer.Listener;
import org.gof.demo.worldsrv.arena.ArenaManager;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.ConfGlobal;
import org.gof.demo.worldsrv.entity.*;
import org.gof.demo.worldsrv.friend.FriendManager;
import org.gof.demo.worldsrv.guild.HumanBriefVO;
import org.gof.demo.worldsrv.human.*;
import org.gof.demo.worldsrv.instance.InstanceConstants;
import org.gof.demo.worldsrv.instance.InstanceManager;
import org.gof.demo.worldsrv.msg.*;
import org.gof.demo.worldsrv.placingReward.Calculator.AbstractCalculator;
import org.gof.demo.worldsrv.placingReward.Calculator.RewardCalculatorTypeFactory;
import org.gof.demo.worldsrv.placingReward.PlacingRewardType;
import org.gof.demo.worldsrv.placingReward.PlacingRewardVo;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.rank.RankManager;
import org.gof.demo.worldsrv.rank.RankParamKey;
import org.gof.demo.worldsrv.redis.RedisKeys;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.StringZipUtils;
import org.gof.demo.worldsrv.support.enumKey.ConfGlobalKey;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;
import org.gof.demo.worldsrv.support.observer.EventKey;
import org.gof.demo.worldsrv.task.TaskConditionTypeKey;
import org.gof.demo.worldsrv.team.TeamMember;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

public class CaptureSlaveManager extends ManagerBase {
    private static final int MAX_NAME_LEN = 8;
    private static final int COMBAT_CAPTURE = 1;//抓捕
    private static final int COMBAT_HELP = 2;//帮助
    private static final int COMBAT_REBEL = 3;//反叛

    private static final int LIST_SEARCH_AND_ENEMY = 0;
    private static final int LIST_FRIEND = 1;
    private static final int LIST_RANK = 2;

    /**
     * 获取实例
     *
     * @return
     */
    public static CaptureSlaveManager inst() {
        return inst(CaptureSlaveManager.class);
    }

    /**
     * 抓捕奴隶信息C2S消息
     * @param roleId 角色ID
     */
    public void on_capture_slave_info_c2s(HumanObject humanObj, long roleId) {
        if (!humanObj.isModUnlock(CaptureSlaveType.FUNCTION_ID)) {
            humanObj.sendMsg(MsgError.error_info_s2c.newBuilder().setCode(93));
            return;
        }
        roleId = roleId == 0 ? humanObj.id : roleId;

        Capture capture = humanObj.operation.capture;
        if (capture == null || capture.getId() != roleId) {
            // 异步获取Capture实体
            long finalRoleId = roleId;
            EntityManager.getEntityAsync(Capture.class, roleId, captureRes -> {
                if (captureRes.failed()) {
                    Log.game.error("无法获取Capture数据，roleId={}", finalRoleId);
                    return;
                }
                AtomicReference<Capture> fetchedCapture = new AtomicReference<>(captureRes.result());
                if (fetchedCapture.get() == null) {
                    String createLockKey = Utils.createStr("{}.{}", RedisKeys.captureCreateLock, finalRoleId);
                    RedisTools.lockAsync(EntityManager.redisClient, createLockKey, Long.toString(finalRoleId), lockRes -> {
                        if (lockRes.failed() || !lockRes.result()) {
                            return;
                        }
                        fetchedCapture.set(new Capture());
                        fetchedCapture.get().setId(finalRoleId);
                        fetchedCapture.get().persist();
                        if (finalRoleId == humanObj.id) {
                            humanObj.operation.capture = fetchedCapture.get();
                        }
                        proceedWithCaptureInfo(humanObj, finalRoleId, fetchedCapture.get());
                    });
                } else {
                    if (finalRoleId == humanObj.id) {
                        humanObj.operation.capture = fetchedCapture.get();
                    }
                    proceedWithCaptureInfo(humanObj, finalRoleId, fetchedCapture.get());
                }
            });
        } else {
            proceedWithCaptureInfo(humanObj, roleId, capture);
        }
    }

    private void proceedWithCaptureInfo(HumanObject humanObj, long roleId, Capture capture) {
        HumanData.getHumanDataAsync(roleId, HumanManager.inst().humanClasses2, humanDataRes -> {
            if (humanDataRes.failed()) {
                Log.game.error("无法获取HumanData，roleId={}", roleId);
                return;
            }
            HumanData humanData = humanDataRes.result();
            if (humanData == null) {
                Log.game.error("on_capture_slave_info_c2s:找不到角色信息HumanData，roleId = {}", roleId);
                return;
            }

            Human human = humanData.human;
            Human2 human2 = humanData.human2;
            Capture captureMaster = humanObj.operation.capture;

            // 如果这个人是奴隶，检查是否过期或主人信息不匹配
            if (human.getMasterId() != 0) {
                // 获取奴隶锁
                String lockKey = Utils.createStr("{}.{}", RedisKeys.captureLock, roleId);
                RedisTools.lockWithExpireTimeAsync(EntityManager.redisClient, lockKey, Long.toString(humanObj.id), 5, lockRes -> {
                    if (lockRes.failed() || !lockRes.result()) {
                        // 获取锁失败，继续处理消息
                        continueWithMessage(humanObj, roleId, capture, human, human2);
                        return;
                    }

                    int now = (int) (Port.getTime() / Time.SEC);
                    boolean needCleanup = false;
                    
                    // 检查是否过期
                    if (human.getCaptureTime() + ConfGlobal.get(ConfGlobalKey.capture_slave_free_time).value * Time.HOUR / Time.SEC < now) {
                        needCleanup = true;
                    } 
                    // 检查主人是否匹配（如果主人是当前玩家，但不在奴隶列表中）
                    else if (human.getMasterId() == humanObj.id) {
                        Map<Long, SlaveVo> slaveInfoMap = SlaveVo.mapFromJsonString(captureMaster.getSlaveInfoMap());
                        if (!slaveInfoMap.containsKey(roleId)) {
                            needCleanup = true;
                        }
                    }

                    if (needCleanup) {
                        // 清理奴隶状态
                        human.setMasterId(0);
                        human.setCaptureTime(0);
                        human.update();
                        
                        // 异步获取主人的Capture数据并清理
                        EntityManager.getEntityAsync(Capture.class, human.getMasterId(), masterCaptureRes -> {
                            if (!masterCaptureRes.failed() && masterCaptureRes.result() != null) {
                                Capture masterCapture = masterCaptureRes.result();
                                freeSlave(masterCapture, roleId);
                            }
                            
                            // 释放锁并继续处理消息
                            RedisTools.unLock(EntityManager.redisClient, lockKey);
                            continueWithMessage(humanObj, roleId, capture, human, human2);
                        });
                    } else {
                        // 未过期且主人信息匹配，释放锁并继续处理消息
                        RedisTools.unLock(EntityManager.redisClient, lockKey);
                        continueWithMessage(humanObj, roleId, capture, human, human2);
                    }
                });
            } else {
                // 不是奴隶，直接处理消息
                continueWithMessage(humanObj, roleId, capture, human, human2);
            }
        });
    }

    // 抽取消息处理逻辑为单独的方法
    private void continueWithMessage(HumanObject humanObj, long roleId, Capture capture, Human human, Human2 human2) {
        MsgCaptureSlave.capture_slave_info_s2c.Builder msg = MsgCaptureSlave.capture_slave_info_s2c.newBuilder();
        msg.setRoleInfo(to_p_common_role(human, human2, 0, 0, 0, null));

        // 先处理雇主信息
        long masterId = human.getMasterId();
        if(masterId != 0) {
            HumanData.getHumanDataAsync(masterId, HumanManager.inst().humanClasses2, masterDataRes -> {
                if (masterDataRes.failed()) {
                    Log.game.error("无法获取雇主HumanData，masterId={}", masterId);
                    continueWithSlaveInfo(humanObj, roleId, capture, msg);
                    return;
                }
                HumanData humanMasterData = masterDataRes.result();
                if (humanMasterData == null) {
                    Log.game.error("on_capture_slave_info_c2s:找不到雇主信息HumanData，masterId = {}", masterId);
                    continueWithSlaveInfo(humanObj, roleId, capture, msg);
                    return;
                }

            Human masterHuman = humanMasterData.human;
            Human2 masterHuman2 = humanMasterData.human2;
            if(masterHuman != null && masterHuman2 != null) {
                msg.setMasterInfo(to_p_common_role(masterHuman, masterHuman2, 0, 0, 0, null));
            }
            continueWithSlaveInfo(humanObj, roleId, capture, msg);
            });
        } else {
            continueWithSlaveInfo(humanObj, roleId, capture, msg);
        }
    }

    private void continueWithSlaveInfo(HumanObject humanObj, long roleId, Capture capture,
                                       MsgCaptureSlave.capture_slave_info_s2c.Builder msg) {
        int freeCd = (int) (ConfGlobal.get(ConfGlobalKey.capture_slave_free_time).value * Time.HOUR / Time.SEC);
        int now = (int) (Port.getTime() / Time.SEC);

        Map<Long, SlaveVo> slaveInfoMap = SlaveVo.mapFromJsonString(capture.getSlaveInfoMap());
        if(slaveInfoMap.isEmpty()) {
            humanObj.sendMsg(msg);
            return;
        }
        List<Map.Entry<Long, SlaveVo>> slaveList = new ArrayList<>(slaveInfoMap.entrySet());
        slaveList.sort((o1, o2) -> o2.getValue().entTime - o1.getValue().entTime);

        List<Long> slaveIds = new ArrayList<>();
        Map<Long, Integer> freeTimeMap = getCleanedFreeTimeMap(capture);

        Iterator<Map.Entry<Long, SlaveVo>> iterator = slaveList.iterator();
        while (iterator.hasNext()) {
            Map.Entry<Long, SlaveVo> entry = iterator.next();
            long slaveId = entry.getKey();
            SlaveVo slaveVo = entry.getValue();

            // 检查奴隶是否已经过期
            if (slaveVo.entTime < now) {
                iterator.remove();
                slaveInfoMap.remove(slaveId);
                if (slaveVo.entTime + freeCd < now) {
                    freeTimeMap.put(slaveId, slaveVo.entTime + freeCd);
                }
                continue;
            }
            slaveIds.add(slaveId);
        }
        capture.setFreeTimeMap(Utils.mapLongIntToJSON(freeTimeMap));
        capture.setSlaveInfoMap(SlaveVo.mapToJsonString(slaveInfoMap));
        capture.update();
        // 批量获取奴隶数据
        HumanData.getList(slaveIds, HumanManager.inst().humanClasses2, slaveDataListRes -> {
            if (slaveDataListRes.failed()) {
                Log.game.error("无法获取奴隶数据，roleId={}", roleId);
                capture.update();
                return;
            }
            List<HumanData> slaveDataList = slaveDataListRes.result();
            if(slaveDataList == null) {
                Log.game.error("无法获取奴隶数据，roleIds={}", roleId);
                capture.update();
                return;
            }

            for(int i = 0; i < slaveDataList.size(); i++) {
                HumanData slaveData = slaveDataList.get(i);
                long slaveId = slaveIds.get(i);
                if (slaveData == null) {
                    Log.game.error("无法获取奴隶数据，slaveIds={}", slaveId);
                    slaveInfoMap.remove(slaveId);
                    continue;
                }
                Human slaveHuman = slaveData.human;
                Human2 slaveHuman2 = slaveData.human2;
                SlaveVo slaveVo = slaveInfoMap.get(slaveId);

                if (slaveHuman != null && slaveHuman2 != null && slaveVo != null) {
                    msg.addSlavesInfo(to_p_common_role(slaveHuman, slaveHuman2, slaveVo.entTime, 0, 0, slaveVo.slaveName));
                }
            }

            humanObj.sendMsg(msg);

            // 更新奴隶信息
            capture.setSlaveInfoMap(SlaveVo.mapToJsonString(slaveInfoMap));
            capture.update();
        });
    }

    public Define.p_common_role.Builder to_p_common_role(Human human, Human2 human2, int capturetime, int cdTime, int rank, String slaveName){
        Define.p_common_role.Builder dInfo = Define.p_common_role.newBuilder();
        dInfo.setRoleId(human.getId());
        Define.p_role_change.Builder p_role_change = Define.p_role_change.newBuilder();
        p_role_change.addKs(Define.p_key_string.newBuilder().setK(RoleInfoKey.ROLE_ATTR_NAME.getKey()).setS(human.getName()));
        p_role_change.addKv(Define.p_key_value.newBuilder().setK(RoleInfoKey.ROLE_ATTR_POWER_SHOW.getKey()).setV(new BigDecimal(human.getCombat()).longValue()));
        p_role_change.addKv(Define.p_key_value.newBuilder().setK(RoleInfoKey.ROLE_ATTR_HEAD_ID.getKey()).setV(human.getHeadSn()));
        p_role_change.addKs(Define.p_key_string.newBuilder().setK(RoleInfoKey.ROLE_ATTR_HEAD_URL.getKey()).setS(""));
        p_role_change.addKv(Define.p_key_value.newBuilder().setK(RoleInfoKey.ROLE_ATTR_HEAD_FRAME_ID.getKey()).setV(human.getCurrentHeadFrameSn()));
        if(slaveName != null){
            p_role_change.addKs(Define.p_key_string.newBuilder().setK(RoleInfoKey.ROLE_ATTR_SLAVE_NAME.getKey()).setS(slaveName));
        }
        dInfo.setInfoList(p_role_change);

        dInfo.addExtList(Define.p_key_value.newBuilder().setK(1).setV(human2.getRepSn()-1));
        dInfo.addExtList(Define.p_key_value.newBuilder().setK(2).setV(capturetime));
        dInfo.addExtList(Define.p_key_value.newBuilder().setK(3).setV(rank));
        dInfo.addExtList(Define.p_key_value.newBuilder().setK(4).setV(cdTime));

        dInfo.setFigure(HumanManager.inst().to_p_role_figure(human,human2));

        return dInfo;
    }


    /**
     * 搜索奴隶C2S消息
     * @param type 搜索类型
     */
    public void on_capture_slave_search_c2s(HumanObject humanObj, int type, int page) {
        if(type == LIST_SEARCH_AND_ENEMY){
            sendSearchList(humanObj);
        }else if(type == LIST_FRIEND){
            sendFriendList(humanObj);
        }else if(type == LIST_RANK){
            sendRankList(humanObj,page);
        }

    }

    private void sendSearchList(HumanObject humanObj) {
        Capture capture = humanObj.operation.capture;
        if (capture == null) {
            return;
        }
        Map<Long, Integer> freeTimeMap = Utils.jsonToMapLongInt(capture.getFreeTimeMap());
        List<Long> searchList = Utils.strToLongList(capture.getNullList());
        int searchSize = ConfGlobal.get(ConfGlobalKey.capture_slave_research).intArray[1];

        if (searchList.size() < searchSize) {
            FriendManager.inst().getRandFriendList(humanObj, searchSize, res -> {
                if (res.succeeded()) {
                    List<Long> searchTempList = res.result();
                    if(searchTempList != null){
                        capture.setNullList(Utils.listToString(searchTempList));
                        capture.update();
                    }
                    processSearchList(humanObj, capture, searchTempList, freeTimeMap);
                }
            });
        } else {
            processSearchList(humanObj, capture, searchList, freeTimeMap);
        }
    }

    private void processSearchList(HumanObject humanObj, Capture capture, List<Long> searchList, Map<Long, Integer> freeTimeMap) {
        MsgCaptureSlave.capture_slave_search_s2c.Builder msg = MsgCaptureSlave.capture_slave_search_s2c.newBuilder();
        msg.setType(LIST_SEARCH_AND_ENEMY);
        msg.setNextTime((int) (capture.getNullListTime() / Time.SEC));

        // 异步获取搜索列表中的Human和Human2数据
        HumanData.getList(searchList, new Class[]{Human.class, Human2.class}, result -> {
            if (result.failed()) {
                Log.capture.error("无法获取搜索列表中的角色数据");
                return;
            }

            List<HumanData> humanDataList = result.result();
            for (HumanData humanData : humanDataList) {
                if (humanData == null || humanData.human == null) continue;

                long id = humanData.human.getId();
                int endTime = getAndClearFreeTimeMap(capture, freeTimeMap, id);
                msg.addNullList(to_p_common_role(humanData.human, humanData.human2, 0, endTime, 0, null));
            }

            // 处理仇恨列表
            List<Long> hateList = Utils.strToLongList(capture.getHateList());
            if(hateList.isEmpty()){
                humanObj.sendMsg(msg);
                return;
            }

            HumanData.getList(hateList, new Class[]{Human.class, Human2.class}, hateResult -> {
                if (hateResult.failed()) {
                    Log.capture.error("无法获取仇恨列表中的角色数据");
                    return;
                }

                List<HumanData> hateDataList = hateResult.result();
                for (HumanData humanData : hateDataList) {
                    if (humanData == null) continue;

                    msg.addHateList(to_p_common_role(humanData.human, humanData.human2, 0, 0, 0, null));
                }

                humanObj.sendMsg(msg);
            });
        });
    }

    private void sendFriendList(HumanObject humanObj) {
        Capture capture = humanObj.operation.capture;
        Friend friend = humanObj.operation.friend;
        if (capture == null || friend == null) {
            return;
        }

        List<Long> friendList = Utils.strToLongList(friend.getFriendList());
        MsgCaptureSlave.capture_slave_search_s2c.Builder msg = MsgCaptureSlave.capture_slave_search_s2c.newBuilder();
        msg.setType(LIST_FRIEND);
        msg.setNextTime((int) (capture.getNullListTime() / Time.SEC));

        // 异步查询好友的Human和Human2数据
        HumanData.getList(friendList, new Class[]{Human.class, Human2.class}, result -> {
            if (result.failed()) {
                Log.capture.error("无法获取好友数据");
                return;
            }

            List<HumanData> humanDataList = result.result();
            Map<Long, Integer> freeTimeMap = Utils.jsonToMapLongInt(capture.getFreeTimeMap());

            for (HumanData humanData : humanDataList) {
                if (humanData == null) continue;

                long id = humanData.human.getId();
                int endTime = getAndClearFreeTimeMap(capture, freeTimeMap, id);
                msg.addNullList(to_p_common_role(humanData.human, humanData.human2, 0, endTime, 0, null));
            }

            humanObj.sendMsg(msg);
        });
    }

    private void sendRankList(HumanObject humanObj, int page) {
        MsgCaptureSlave.capture_slave_search_s2c.Builder msg = MsgCaptureSlave.capture_slave_search_s2c.newBuilder();
        msg.setType(LIST_RANK);

        String rankKey = RankManager.inst().getRedisRankTypeKey(RankParamKey.rankTypeRep_1003, humanObj.getHuman().getServerId());

        int pageSize = 50; // 每页显示50条数据
        int maxCount = 500; // 最多先显示500条数据
        if (page > maxCount / pageSize) {
            return;
        }
        int startIndex = (page - 1) * pageSize;
        int endIndex = startIndex + pageSize - 1;

        // 异步获取排名列表
        RedisTools.getRankListByIndex(EntityManager.redisClient, rankKey, startIndex, endIndex, true, ret -> {
            if (!ret.succeeded()) {
                Log.rank.error("获取排行榜失败, rankKey={}", rankKey);
                return;
            }

            JsonArray jsonArray = ret.result();
            int totalCount = Math.min(maxCount, Utils.getRankSumNum(rankKey));
            int baseRank = (page - 1) * pageSize;

            List<Long> humanIds = new ArrayList<>();
            for (int i = 0; i < jsonArray.size(); i += 2) {
                long humanId = Utils.longValue(jsonArray.getString(i));
                humanIds.add(humanId);
            }

            // 异步查询Human和Human2数据
            HumanData.getList(humanIds, new Class[]{Human.class, Human2.class}, result -> {
                if (result.failed()) {
                    Log.rank.error("无法获取玩家数据");
                    return;
                }

                List<HumanData> humanDataList = result.result();
                if(humanDataList == null){
                    return;
                }
                for (int i = 0; i < humanDataList.size(); i++) {
                    HumanData humanData = humanDataList.get(i);
                    if (humanData == null) continue;

                    int rank = baseRank + i + 1;
                    msg.addNullList(to_p_common_role(humanData.human, humanData.human2, 0, 0, rank, null));
                }

                msg.setNextTime(0);
                msg.setPage(page);
                msg.setMaxNum(totalCount);
                humanObj.sendMsg(msg);
            });
        });
    }

    private int getAndClearFreeTimeMap(Capture capture, Map<Long,Integer> freeTimeMap, long roleId){
        //如果roleId在freeTimeMap中，过期了就清理，返回剩余时间
        if(freeTimeMap.containsKey(roleId)){
            int endTime = freeTimeMap.get(roleId);
            if(endTime < Port.getTime()/Time.SEC){
                freeTimeMap.remove(roleId);
                capture.setFreeTimeMap(Utils.mapLongIntToJSON(freeTimeMap));
                return 0;
            }
            return endTime;
        }
        return 0;
    }

    public List<Long> getRandCaptureList(long humanId, int num){
        List<Long> randList = new ArrayList<>();
        int total;
        try {
            int serverId = Utils.getServerIdByHumanId(humanId);
            String redisKey = RankManager.inst().getRedisRankTypeKey(RankParamKey.rankTypeLevel_1001, serverId);
            AwaitUtil.awaitResult(handler -> {
                EntityManager.redisClient.zcard(redisKey, res -> {
                    if (res.failed()) {
                        Log.capture.error("获取玩家等级排行数量失败，id={}", humanId);
                    } else {
                        handler.handle(Future.succeededFuture(res.result().toInteger()));
                    }
                });
            });
            total = AwaitUtil.awaitResult(handler -> {
                EntityManager.redisClient.zcard(redisKey, res -> {
                    if (res.failed()) {
                        Log.capture.error("获取玩家等级排行数量失败，id={}", humanId);
                        handler.handle(Future.failedFuture(res.cause()));
                    } else {
                        handler.handle(Future.succeededFuture(res.result().toInteger()));
                    }
                });
            });
            num = num > total ? total : num;
        } catch (Exception e){
            Log.capture.error("获取玩家登录时间失败，id={}", humanId);
            return randList;
        }

        int index = 0;
        for (int i = 0; i < num; i++) {
            if(randList.size() >= total-1){
                break;
            }
            String rand = "0";
            if(total < 10){
                rand = Integer.toString(index);
                ++index;
            }else {
                rand = Integer.toString(Utils.random(0, total - 1));
            }
            try {
                int serverId = Utils.getServerIdByHumanId(humanId);
                String redisKey = RankManager.inst().getRedisRankTypeKey(RankParamKey.rankTypeLevel_1001, serverId);

                List<String> args = Arrays.asList(redisKey, rand, rand);
                Response response = AwaitUtil.awaitResult(handler -> {
                    EntityManager.redisClient.zrevrange(args, res -> {
                        if (res.failed()) {
                            Log.capture.error("随机好友获取玩家等级失败，id={}", humanId);
                            handler.handle(Future.failedFuture(res.cause()));
                        } else {
                            handler.handle(Future.succeededFuture(res.result()));
                        }
                    });
                });
                if(response == null){
                    return randList;
                }
                long id = Utils.strToLongList(response.toString()).get(0);
                if (id != humanId && !randList.contains(id)) {
                    randList.add(id);
                }else {
                    i--;
                }
            } catch (Exception e) {
                Log.capture.error("获取玩家等级时间失败，id={}", humanId);
            }
        }
        return randList;
    }

    /**
     * 获取奴隶视频列表C2S消息
     */
    public void on_capture_slave_video_c2s(HumanObject humanObj) {
        MsgCaptureSlave.capture_slave_video_s2c.Builder msg = MsgCaptureSlave.capture_slave_video_s2c.newBuilder();
        String redisKey = Utils.createStr("{}{}", RedisKeys.captureBattleHistoryProtoList, humanObj.id);
        RedisTools.getListRange(EntityManager.getRedisClient(), redisKey, 0, -1, res -> {
            if(res.failed()){
                return;
            }
            JsonArray jsonArray = res.result();
            if(jsonArray == null || jsonArray.isEmpty()){
                humanObj.sendMsg(msg);
                return;
            }

            long currentTime = Port.getTime() / Time.SEC;
            int validCount = jsonArray.size();

            // 处理每条战报数据
            for (int i = 0; i < jsonArray.size(); i++) {
                try {
                    String protoBuf = jsonArray.getString(i);
                    Define.p_battle_video report = Define.p_battle_video.parseFrom(
                            protoBuf.getBytes(StandardCharsets.ISO_8859_1));

                    // 检查是否过期
                    if (currentTime - report.getTime() > ParamKey.historyExpireTime) {
                        validCount = i;
                        break;
                    }
                    msg.addVideoList(report);
                } catch (Exception e) {
                    Log.temp.error("解析战报数据失败, index={}: {}", i, e.getMessage());
                }
            }

            // 如果有过期数据,清理Redis中的过期数据
            if (validCount < jsonArray.size()) {
                if (validCount > 0) {
                    // 保留有效的数据
                    RedisTools.ltrim(EntityManager.getRedisClient(), redisKey, 0, validCount-1);
                } else {
                    // 如果所有数据都过期了，清空整个列表
                    RedisTools.del(EntityManager.getRedisClient(), redisKey);
                }
            }

            humanObj.sendMsg(msg);
        });
        HumanManager.inst().updateSystemModuleRedNum(humanObj, RedPointConstants.SystemSlave, RedPointConstants.ModuleSlaveCapture, 0);
    }

    /**
     * 奴隶奖励C2S消息
     * @param type 奖励类型
     *
     */
    public void on_capture_slave_reward_c2s(HumanObject humanObj, int type) {
        int placingType = 0;
        if(type == 0){
            placingType = PlacingRewardType.OFFLINE_REWARD_2;
        }else if(type == 1){
            placingType = PlacingRewardType.ONLINE_REWARD_2;
        }
        AbstractCalculator calculator = RewardCalculatorTypeFactory.getTypeData(humanObj, placingType);
        if (calculator != null) {
            PlacingRewardVo placingRewardVo = calculator.getPlacingRewardVo();
            calculator.handleReward2Async(placingRewardVo, Utils.getTimeSec(), true, res -> {
                // 在异步回调中构建并发送消息
                MsgCaptureSlave.capture_slave_reward_s2c.Builder msg = MsgCaptureSlave.capture_slave_reward_s2c.newBuilder();
                msg.setType(type);
                msg.setSumSecond(placingRewardVo.placingTime);
                msg.setCoinSpeed(placingRewardVo.coinSpeed);
                msg.addAllRewardList(placingRewardVo.itemToBuilder());
                msg.addAllRewardList(placingRewardVo.resToBuilder());
                humanObj.sendMsg(msg);
            });
        }

    }

    /**
     * 释放奴隶C2S消息
     * @param roleId 奴隶角色ID
     */
    public void on_capture_slave_free_c2s(HumanObject humanObj, long roleId) {
        Capture capture = humanObj.operation.capture;
        if(capture == null){
            return;
        }
        //获取Redis奴隶锁
        String lockKey = Utils.createStr("{}.{}", RedisKeys.captureLock, roleId);
        RedisTools.lockAsync(EntityManager.redisClient,lockKey,Long.toString(humanObj.id),res->{
            if(res.failed()){
                return;
            }
            Boolean lock = res.result();
            if(lock == null || !lock){
                return;
            }
            freeSlave(capture, roleId);

            // 异步获取Human实体
            EntityManager.getEntityAsync(Human.class, roleId, humanRes -> {
                if (humanRes.failed()) {
                    RedisTools.unLock(EntityManager.redisClient, lockKey);
                    Log.capture.error("无法获取Human数据，roleId={}", roleId);
                    return;
                }
                Human humanBasic = humanRes.result();
                if (humanBasic == null || humanBasic.getMasterId() != humanObj.id) {
                    RedisTools.unLock(EntityManager.redisClient, lockKey);
                    return;
                }
                humanBasic.setMasterId(0);
                humanBasic.setCaptureTime(0);
                humanBasic.update();
                RedisTools.unLock(EntityManager.redisClient, lockKey);

                MsgCaptureSlave.capture_slave_free_s2c.Builder msg = MsgCaptureSlave.capture_slave_free_s2c.newBuilder();
                msg.setCode(0);
                msg.setRoleId(roleId);
                humanObj.sendMsg(msg);
            });
        });
    }

    /**
     * 获取奴隶奖励C2S消息
     * @param type 奖励类型
     */
    public void on_capture_slave_reward_get_c2s(HumanObject humanObj, int type) {
        int placingType = 0;
        if(type == 0){
            placingType = PlacingRewardType.OFFLINE_REWARD_2;
        }else if(type == 1){
            placingType = PlacingRewardType.ONLINE_REWARD_2;
        }else {
            return;
        }
        AbstractCalculator calculator = RewardCalculatorTypeFactory.getTypeData(humanObj, placingType);
        PlacingRewardVo placingRewardVo = calculator.getPlacingRewardVo();
        calculator.handleReward2Async(placingRewardVo, Utils.getTimeSec(), true, res -> {
            Map<Integer, Integer> rewardMap = new HashMap<>();
            if (!placingRewardVo.rewardResMap.isEmpty()) {
                rewardMap.putAll(placingRewardVo.rewardResMap);
                ProduceManager.inst().produceAdd(humanObj, placingRewardVo.rewardResMap, MoneyItemLogKey.放置奖励);
                placingRewardVo.rewardResMap.clear();
            }
            if (!placingRewardVo.rewardItemMap.isEmpty()) {
                Utils.mergeMap(rewardMap, placingRewardVo.rewardItemMap);
                ProduceManager.inst().produceAdd(humanObj, placingRewardVo.rewardItemMap, MoneyItemLogKey.放置奖励);
                placingRewardVo.rewardItemMap.clear();
            }
            placingRewardVo.placingTime = 0;
            InstanceManager.inst().savePlacingRewardMap(humanObj);

            MsgCaptureSlave.capture_slave_reward_get_s2c.Builder msg = MsgCaptureSlave.capture_slave_reward_get_s2c.newBuilder();
            msg.setType(type);
            msg.addAllRewardList(InstanceManager.inst().to_p_rewardList(rewardMap));
            humanObj.sendMsg(msg);

            if (!rewardMap.isEmpty()) {
                InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, InstanceConstants.showType_0, InstanceManager.inst().to_p_rewardList(rewardMap));
            }
        });
    }

    public void freeSlave(Capture capture, long roleId){
        Map<Long,SlaveVo> slaveInfoMap = SlaveVo.mapFromJsonString(capture.getSlaveInfoMap());
        SlaveVo slaveVo = slaveInfoMap.get(roleId);
        if(slaveVo == null){
            return;
        }
        slaveInfoMap.remove(roleId);
        capture.setSlaveInfoMap(SlaveVo.mapToJsonString(slaveInfoMap));
        Map<Long,Integer> freeTimeMap = Utils.jsonToMapLongInt(capture.getFreeTimeMap());
        freeTimeMap.put(roleId, (int)((Port.getTime()+ ConfGlobal.get(ConfGlobalKey.capture_slave_cd).value * Time.HOUR)/Time.SEC));
//        freeTimeMap.put(roleId, (int)(Port.getTime()+ 5 * Time.MIN/Time.SEC));//todo:测试用
        capture.setFreeTimeMap(Utils.mapLongIntToJSON(freeTimeMap));
        capture.update();
    }

    /**
     * 奴隶战斗C2S消息
     * @param clientType 战斗类型
     * @param slaveId 奴隶ID
     */
    public void on_capture_slave_combat_c2s(HumanObject humanObj, int clientType, long slaveId) {
        Capture capture = humanObj.operation.capture;
        if (capture == null) {
            return;
        }

        Map<Long,SlaveVo> slaveInfoMap = SlaveVo.mapFromJsonString(capture.getSlaveInfoMap());

        // 确定实际的战斗类型
        int actualType;
        if (clientType == 1) {
            // 检查是否是反叛
            if (humanObj.id == slaveId) {
                actualType = COMBAT_REBEL;
            } else {
                actualType = COMBAT_CAPTURE;
            }
        } else if (clientType == 2) {
            actualType = COMBAT_HELP;
        } else {
            // 无效的类型
            return;
        }

        // 只在抓捕时进行检查
        if (actualType == COMBAT_CAPTURE) {
            // 检查奴隶是否是自己的
            SlaveVo slaveVo = slaveInfoMap.get(slaveId);
            if (slaveVo != null ){
                humanObj.sendMsg(MsgError.error_info_s2c.newBuilder().setCode(221));
                return;
            }
            // 检查奴隶上限
            if (slaveInfoMap.size() >= ConfGlobal.get(ConfGlobalKey.capture_slave_limit).value) {
                humanObj.sendMsg(MsgError.error_info_s2c.newBuilder().setCode(264));
                return;
            }
            if (slaveInfoMap.containsKey(slaveId)) {
                return;
            }

            // 检查冷却时间
            Map<Long,Integer> freeTimeMap = Utils.jsonToMapLongInt(capture.getFreeTimeMap());
            if (freeTimeMap.containsKey(slaveId) && freeTimeMap.get(slaveId) > Port.getTime()/Time.SEC) {
                humanObj.sendMsg(MsgError.error_info_s2c.newBuilder().setCode(223));
                return;
            }
        }

        // 获取Redis奴隶锁
        String lockKey = Utils.createStr("{}.{}", RedisKeys.captureLock, slaveId);
        RedisTools.lockWithExpireTimeAsync(EntityManager.redisClient, lockKey, Long.toString(humanObj.id), 180, lockRes -> {
            if (lockRes.failed() || !lockRes.result()) {
                return;
            }

            humanObj.combatId = slaveId;
            humanObj.combatSeed = Utils.getTimeSec();
            humanObj.combatType = actualType;

            MsgCaptureSlave.capture_slave_combat_s2c.Builder msg = MsgCaptureSlave.capture_slave_combat_s2c.newBuilder();
            msg.setCode(0);
            msg.setEid(0);
            msg.setSeed(humanObj.combatSeed);

            HumanBriefVO humanBriefVO = new HumanBriefVO(humanObj);
            msg.setAtkData(humanBriefVO.to_p_battle_role(humanBriefVO.getPlanId(HumanManager.PLAN_TYPE_HOME_BATTLE)));

            // 异步获取Human实体
            EntityManager.getEntityAsync(Human.class, slaveId, humanRes -> {
                if (humanRes.failed()) {
                    RedisTools.unLock(EntityManager.redisClient, lockKey);
                    return;
                }
                Human humanBasic = humanRes.result();
                if (humanBasic == null) {
                    RedisTools.unLock(EntityManager.redisClient, lockKey);
                    return;
                }

                // 根据不同的战斗类型设置对手
                long combatId = slaveId;
                switch (actualType) {
                    case COMBAT_HELP:
                    case COMBAT_REBEL:
                        combatId = humanBasic.getMasterId();
                        break;
                    case COMBAT_CAPTURE:
                        combatId = humanBasic.getMasterId() != 0 ? humanBasic.getMasterId() : slaveId;
                        break;
                }

                // 异步获取HumanData
                HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance();
                prx.getHumanBrief2(combatId);
                prx.listenResult((Boolean timeOut, Param results1, Param ctx) -> {
                    HumanBrief brief = results1.get("humanBrief");
                    if (brief == null) {
                        RedisTools.unLock(EntityManager.redisClient, lockKey);
                        return;
                    }
                    HumanBriefVO slaveBriefVo = new HumanBriefVO(brief);
                    humanObj.combatBriefVo = slaveBriefVo;
                    msg.setDefData(slaveBriefVo.to_p_battle_role(slaveBriefVo.getPlanId(HumanManager.PLAN_TYPE_HOME_BATTLE)));
                    humanObj.sendMsg(msg);
                });
            });
        });
    }

    /**
     * 奴隶战斗结果C2S消息
     * @param winId 胜利者ID
     */
    public void on_capture_slave_result_c2s(HumanObject humanObj, long winId) {
        if (humanObj.combatId == 0) {
            return;
        }
        // 获取锁
        String lockKey = Utils.createStr("{}.{}", RedisKeys.captureLock, humanObj.combatId);
        RedisTools.get(EntityManager.getRedisClient(), lockKey, (res)->{
            if(res.failed()){
                return;
            }
            String idStr = res.result();
            if(idStr == null || Utils.longValue(idStr) != humanObj.id){
                RedisTools.unLock(EntityManager.redisClient, lockKey);
                return;
            }

            EntityManager.getEntityAsync(Human.class, humanObj.combatId, targetHumanRes -> {
                if (targetHumanRes.failed()) {
                    RedisTools.unLock(EntityManager.redisClient, lockKey);
                    return;
                }
                Human targetHumanBasic = targetHumanRes.result();
                if (targetHumanBasic == null) {
                    RedisTools.unLock(EntityManager.redisClient, lockKey);
                    return;
                }

                long opponentId;
                switch (humanObj.combatType) {
                    case COMBAT_CAPTURE:
                        opponentId = targetHumanBasic.getMasterId() != 0 ? targetHumanBasic.getMasterId() : humanObj.combatId;
                        break;
                    case COMBAT_HELP:
                    case COMBAT_REBEL:
                        opponentId = targetHumanBasic.getMasterId();
                        break;
                    default:
                        humanObj.combatBriefVo = null;
                        RedisTools.unLock(EntityManager.redisClient, lockKey);
                        return;
                }

                EntityManager.getEntityAsync(Human.class, opponentId, opponentHumanRes -> {
                    if (opponentHumanRes.failed()) {
                        humanObj.combatBriefVo = null;
                        RedisTools.unLock(EntityManager.redisClient, lockKey);
                        return;
                    }
                    Human opponentHuman = opponentHumanRes.result();
                    if (opponentHuman == null) {
                        humanObj.combatBriefVo = null;
                        RedisTools.unLock(EntityManager.redisClient, lockKey);
                        return;
                    }

                    MsgCaptureSlave.capture_slave_result_s2c.Builder msg = MsgCaptureSlave.capture_slave_result_s2c.newBuilder();
                    msg.setCode(0);
                    msg.setVid(winId);
                    msg.setEName(opponentHuman.getName());
                    msg.setEHead(Define.p_head.newBuilder()
                            .setId(opponentHuman.getHeadSn())
                            .setFrameId(opponentHuman.getCurrentHeadFrameSn())
                            .setUrl(""));

                    if (winId == humanObj.id) {
                        msg.setIsWin(1);
                        HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance();
                        switch (humanObj.combatType) {
                            case COMBAT_CAPTURE:
                                targetHumanBasic.setMasterId(humanObj.id);
                                targetHumanBasic.setCaptureTime((int) (Port.getTime() / Time.SEC));
                                targetHumanBasic.update();
                                Capture capture = humanObj.operation.capture;
                                if (capture != null) {
                                    Map<Long, SlaveVo> slaveInfoMap = SlaveVo.mapFromJsonString(capture.getSlaveInfoMap());
                                    SlaveVo slaveVo = new SlaveVo();
                                    slaveVo.entTime = (int) ((Port.getTime() + ConfGlobal.get(ConfGlobalKey.capture_slave_free_time).value * Time.HOUR) / Time.SEC);
                                    slaveInfoMap.put(humanObj.combatId, slaveVo);
                                    capture.setSlaveInfoMap(SlaveVo.mapToJsonString(slaveInfoMap));
                                    capture.update();
                                }
                                prx.freeSlave(opponentId, humanObj.combatId);
                                // 抢夺1名跟班
                                humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_回归任务, TaskConditionTypeKey.TASK_TYPE_95, 1);
                                break;
                            case COMBAT_HELP:
                                targetHumanBasic.setMasterId(0);
                                targetHumanBasic.setCaptureTime(0);
                                targetHumanBasic.update();
                                prx.freeSlave(opponentId, humanObj.combatId);
                                break;
                            case COMBAT_REBEL:
                                humanObj.getHuman().setMasterId(0);
                                humanObj.getHuman().setCaptureTime(0);
                                humanObj.getHuman().update();
                                // 从原主人的奴隶列表中移除自己
                                prx.freeSlave(opponentId, humanObj.id);
                                break;
                        }
                        on_capture_slave_info_c2s(humanObj, humanObj.id);
                    } else {
                        msg.setIsWin(0);
                    }

                    HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance();
                    prx.captureAddEnemy(humanObj.combatId, humanObj.id);
                    humanObj.sendMsg(msg);

                    // 释放锁
                    RedisTools.unLock(EntityManager.redisClient, lockKey);

                    ArenaManager.inst().createHistoryProto(humanObj, opponentId, RedisKeys.captureBattleHistoryProtoList, winId == humanObj.id, humanObj.combatSeed);
                    humanObj.combatBriefVo = null;
                    HumanManager.inst().updateSystemModuleRedNum(humanObj, RedPointConstants.SystemSlave, RedPointConstants.ModuleSlaveCapture, 1);
                    // 添加异常情况检查和日志
                    if (opponentId == humanObj.id) {
                        Log.capture.error("异常战斗ID: humanId={}, opponentId={}, combatType={}",humanObj.id, opponentId, humanObj.combatType);
                    }
                });
            });

        });
    }

    public void addEnemy(Capture capture, long enemyId) {
        int max = ConfGlobal.get(ConfGlobalKey.capture_slave_enemy).intArray[1];
        capture.update();
        List<Long> hateList = Utils.strToLongList(capture.getHateList());
        if(hateList.contains(enemyId)){
            return;
        }
        if(hateList.size() >= max){
            hateList.remove(0);
        }
        hateList.add(enemyId);
        capture.setHateList(Utils.listToString(hateList));
        capture.update();
    }

    /**
     * 奴隶效果C2S消息
     * @param actionId 动作ID
     * @param targetId 目标ID
     */
    public void on_capture_slave_effect_c2s(HumanObject humanObj, int actionId, long targetId) {
        if(humanObj.isMsgIdCD(10017,3.0f)){
            return;
        }
        MsgCaptureSlave.capture_slave_effect_s2c.Builder msg = MsgCaptureSlave.capture_slave_effect_s2c.newBuilder();
        msg.setActionId(actionId);
        msg.setTargetId(targetId);
        humanObj.sendMsg(msg);
    }

    /**
     * 奴隶改名C2S消息
     * @param targetId 目标ID
     * @param slaveName 新的奴隶名称
     */
    public void on_capture_slave_rename_c2s(HumanObject humanObj, long targetId, String slaveName) {
        if (slaveName.isEmpty() || slaveName.length() > MAX_NAME_LEN) {
            return;
        }
        Capture capture = humanObj.operation.capture;
        if(capture == null){
            return;
        }
        Map<Long,SlaveVo> slaveInfoMap = SlaveVo.mapFromJsonString(capture.getSlaveInfoMap());
        SlaveVo slaveVo = slaveInfoMap.get(targetId);
        if(slaveVo == null){
            return;
        }
        slaveVo.slaveName = slaveName;
        slaveInfoMap.put(targetId, slaveVo);
        capture.setSlaveInfoMap(SlaveVo.mapToJsonString(slaveInfoMap));
        capture.update();
        MsgCaptureSlave.capture_slave_rename_s2c.Builder msg = MsgCaptureSlave.capture_slave_rename_s2c.newBuilder();
        msg.setCode(0);
        msg.setSlaveName(slaveName);
        msg.setTargetId(targetId);
        humanObj.sendMsg(msg);
    }

    /**
     * 播放奴隶视频C2S消息
     * @param vid 视频ID
     * @param source 来源
     */
    public void on_capture_slave_video_play_c2s(HumanObject humanObj, long vid, int source) {
        ArenaManager.inst().sendHistoryVideoProto(humanObj, vid, ParamKey.historyType_capture);
    }



    /**
     * 刷新奴隶列表C2S消息
     */
    public void on_capture_slave_refresh_c2s(HumanObject humanObj) {
        Capture capture = humanObj.operation.capture;
        long searchCd = ConfGlobal.get(ConfGlobalKey.capture_slave_research).intArray[0] * Time.SEC;
        if (capture.getNullListTime() > Port.getTime()) {
            return;
        }

        // 使用异步方法获取随机好友列表
        FriendManager.inst().getRandFriendList(humanObj, ConfGlobal.get(ConfGlobalKey.capture_slave_research).intArray[1], res -> {
            if (res.failed()) {
                Log.capture.error("获取随机好友列表失败");
                return;
            }

            List<Long> searchList = res.result();
            capture.setNullListTime(Port.getTime() + searchCd);
            capture.setNullList(Utils.listToString(searchList));
            capture.update();

            MsgCaptureSlave.capture_slave_refresh_s2c.Builder msg = MsgCaptureSlave.capture_slave_refresh_s2c.newBuilder();
            msg.setNextTime((int) (capture.getNullListTime() / Time.SEC));

            // 异步获取Human和Human2数据
            HumanData.getList(searchList, new Class[]{Human.class, Human2.class}, result -> {
                if (result.failed()) {
                    Log.capture.error("无法获取角色数据");
                    return;
                }

                List<HumanData> humanDataList = result.result();
                if(humanDataList == null){
                    return;
                }
                for (HumanData humanData : humanDataList) {
                    if (humanData == null) continue;
                    msg.addNullList(to_p_common_role(humanData.human, humanData.human2, 0, 0, 0, null));
                }
                humanObj.sendMsg(msg);
            });
        });
    }

    /**
     * 查看奴隶视频C2S消息
     * @param vidListList 视频ID列表
     */
    public void on_capture_slave_look_c2s(HumanObject humanObj, List<Long> vidListList) {
    }

    @Listener(EventKey.FUNCTION_OPEN)
    public void onFunctionOpen(Param param) {
        HumanObject humanObj = param.get("humanObj");
        List<Integer> openList = param.get("openList");
        if(openList.contains(CaptureSlaveType.FUNCTION_ID)){
            //检测仆人功能开启初始化
            if(!humanObj.operation.placingRewardMap.containsKey(PlacingRewardType.ONLINE_REWARD_2)&&humanObj.isModUnlock(80)){
                InstanceManager.inst().initPlacingReward(humanObj, PlacingRewardType.ONLINE_REWARD_2);
                InstanceManager.inst().savePlacingRewardMap(humanObj);
            }
        }
    }

    private Map<Long, Integer> getCleanedFreeTimeMap(Capture capture) {
        Map<Long, Integer> freeTimeMap = Utils.jsonToMapLongInt(capture.getFreeTimeMap());
        if (freeTimeMap.isEmpty()) {
            return freeTimeMap;
        }

        int now = (int) (Port.getTime() / Time.SEC);

        // 清理过期的冷却时间
        Iterator<Map.Entry<Long, Integer>> it = freeTimeMap.entrySet().iterator();
        while (it.hasNext()) {
            Map.Entry<Long, Integer> entry = it.next();
            if (entry.getValue() < now) {
                it.remove();
            }
        }
        return freeTimeMap;
    }
}
