package org.gof.demo.worldsrv.produce;

import java.util.*;

/**
 * 随机数工具
 *
 */
public class RandomUtil {


	private static Random random = new Random();

	public static int random(int max) {
		return random.nextInt(max);
	}

	public static int[] randomCommon(int min, int max, int n, int finderNum){
		if (n > (max - min + 1) || max < min) {
			return null;
		}
		int[] result = new int[n];
		int count = 0;
		while(count < n) {
			int num = (int) (Math.random() * (max - min)) + min;
			boolean flag = true;
			for (int j = 0; j < n; j++) {
				if(num == result[j] || num == finderNum){
					flag = false;
					break;
				}
			}
			if(flag){
				result[count] = num;
				count++;
			}
		}
		return result;
	}
	/**
	 * @param min 指定范围最小值
	 * @param max 指定范围最大值
	 * @param n 随机数个数
	 */
	public static int[] randomCommon(int min, int max, int n){
		// 有效性判断
		if (n > (max - min + 1) || max < min) {
			return null;
		}

		// 按随机个数声明存放数组
		int[] result = new int[n];

		// 统计生成个数
		int count = 0;
		int step = 0;
		// 生成个数小于n之前，一直生成
		while(count < n) {
			// 在指定范围内生成随机数
			int num = (int) (Math.random() * (max - min)) + min;

			// 重复标记
			boolean flag = true;

			// 将生成的随机数与数组中已生成的数作比较
			for (int j = 0; j < n; j++) {

				// 重复则标记
				if(num == result[j]){
					flag = false;
					break;
				}
			}

			// 不重复则加入数组
			if(flag) {
				result[count] = num;
				// 生成个数累加
				count++;
			}

			step ++;
			if(step >= 100){
				break;
			}
		}

		return result;
	}
	/**
	 * 包含最大最小值
	 *
	 * @param min
	 * @param max
	 * @return
	 */
	public static int random(int min, int max) {
		int minNum = min < max ? min : max;
		int maxNum = min < max ? max : min;
		return minNum + random.nextInt(maxNum - minNum + 1);
	}

	/**
	 * 根据几率 计算是否生成
	 *
	 * @param probability
	 * @return
	 */
	public static boolean isGenerate(int probability, int gailv) {
		if (gailv == 0) {
			gailv = 1000;
		}
		int random_seed = random.nextInt(gailv + 1);
		return probability >= random_seed;
	}

	/**
	 *
	 * gailv/probability 比率形式
	 *
	 * @param probability
	 * @param gailv
	 * @return
	 */
	public static boolean isGenerate2(int probability, int gailv) {
		if (probability == gailv) {
			return true;
		}
		if (gailv == 0) {
			return false;
		}
		int random_seed = random.nextInt(probability);
		return random_seed + 1 <= gailv;
	}

	/**
	 * 根据几率 计算是否生成
	 *
	 * @param probability
	 * @return
	 */
	public static boolean defaultIsGenerate(int probability, int baseProb) {
		int random_seed = random.nextInt(baseProb);
		return probability >= random_seed;
	}

	/**
	 * 从 min 和 max 中间随机一个值
	 *
	 * @param max
	 * @param min
	 * @return 包含min max
	 */
	public static int randomValue(int max, int min) {
		int temp = max - min;
		temp = RandomUtil.random.nextInt(temp + 1);
		temp = temp + min;
		return temp;
	}

	/**
	 * 返回在0-maxcout之间产生的随机数时候小于num
	 *
	 * @param num
	 * @return
	 */
	public static boolean isGenerateToBoolean(float num, int maxcout) {
		double count = Math.random() * maxcout;

		if (count < num) {
			return true;
		}
		return false;
	}

	/**
	 * 返回在0-maxcout之间产生的随机数时候小于num
	 *
	 * @param num
	 * @return
	 */
	public static boolean isGenerateToBoolean(int num, int maxcout) {
		double count = Math.random() * maxcout;

		if (count < num) {
			return true;
		}
		return false;
	}

	/**
	 * 随机产生min到max之间的整数值 包括min max
	 *
	 * @param min
	 * @param max
	 * @return
	 */
	public static int randomIntValue(int min, int max) {
		return (int) (Math.random() * (double) (max - min + 1)) + min;
	}

	public static float randomFloatValue(float min, float max) {
		return (float) (Math.random() * (double) (max - min)) + min;
	}

	public static <T> T randomItem(Collection<T> collection) {
		if (collection == null || collection.size() == 0) {
			return null;
		}
		int t = (int) (collection.size() * Math.random());
		int i = 0;
		for (Iterator<T> item = collection.iterator(); i <= t && item.hasNext();) {
			T next = item.next();
			if (i == t) {
				return next;
			}
			i++;
		}
		return null;
	}

	public static int randomIndexByProb(List<Integer> snList, List<Integer> probs){
		if (snList.size() != probs.size() || snList.size() == 0){
			return -1;
		}
		int index = randomIndexByProb(probs);
		if (index == -1){
			return -1;
		}
		return snList.get(index);
	}

	/**
	 *
	 *            根据总机率返回序号
	 * @return
	 */
	public static int randomIndexByProb(List<Integer> probs) {
		try {
			LinkedList<Integer> newprobs = new LinkedList<Integer>();
			for (int i = 0; i < probs.size(); i++) {
				// if (probs.get(i) > 0) {
				if (i == 0) {
					newprobs.add(probs.get(i));
				} else {
					newprobs.add(newprobs.get(i - 1) + probs.get(i));
				}
				// }
			}
			if (newprobs.size() <= 0) {
				return -1;
			}
			int last = newprobs.getLast();
			if (last == 0) {
				return -1;
			}
			// String[] split = last.split(Symbol.XIAHUAXIAN_REG);
			int random = random(last);
			for (int i = 0; i < newprobs.size(); i++) {
				int value = newprobs.get(i);
				// String[] split2 = string.split(Symbol.XIAHUAXIAN_REG);
				// if(Integer.parseInt(split2[1])>random){
				if (value > random) {
					return i;
				}
			}
		} catch (Exception e) {
			//logger.error("计算机率错误" + probs.toString(), e);
		}
		return -1;
	}

	/**
	 *
	 *            根据总机率返回序号
	 * @return
	 */
	public static int randomIndexByProb(Integer[] probs) {
		List<Integer> list = Arrays.asList(probs);
		return randomIndexByProb(list);
	}

	/**
	 * 非均匀分布的数组，返回命中数组元素的索引 全未命中返回-1
	 *
	 * @param rateArray
	 *            数组中各元素的值为该元素被命中的权重
	 * @return 命中的数组元素的索引
	 */
	public static int random(Integer[] rateArray) {
		int[] rateArrayInt = new int[rateArray.length];
		for (int i = 0; i < rateArray.length; i++) {
			rateArrayInt[i] = rateArray[i];
		}
		return random(rateArrayInt);
	}


	/**
	 * 非均匀分布的数组，返回命中数组元素的索引 全未命中返回-1
	 *
	 * @param rateArray
	 *            数组中各元素的值为该元素被命中的权重
	 * @return 命中的数组元素的索引
	 */
	public static int random(int[] rateArray) {
		if (null == rateArray) {
			throw new IllegalArgumentException("The random array must not be null!");
		}
		int arrayLength = rateArray.length;
		if (arrayLength == 0) {
			throw new IllegalArgumentException("The random array's length must not be zero!");
		}
		// 依次累加的和
		int rateSum = 0;
		// 从头开始 依次累加之后的各个元素和 的临时数组
		int[] rateSumArray = new int[arrayLength];

		for (int i = 0; i < arrayLength; i++) {

			if (rateArray[i] < 0) {
				throw new IllegalArgumentException("The array's element must not be equal or greater than zero!");
			}
			rateSum += rateArray[i];
			rateSumArray[i] = rateSum;
		}
		if (rateSum <= 0) {
			// 所有概率都为零，必然没有选中的元素，返回无效索引:-1
			return -1;
		}

		int randomInt = random(1, rateSum);
		int bingoIndex = -1;
		for (int i = 0; i < arrayLength; i++) {
			if (randomInt <= rateSumArray[i]) {
				bingoIndex = i;
				break;
			}
		}
		if (bingoIndex == -1) {
			throw new IllegalStateException("Cannot find out bingo index!");
		}
		return bingoIndex;
	}

	/**
	 * 根据权重列表得到不重复的随机索引
	 *
	 * @param weightList
	 *            权重数组
	 * @param count
	 *            需要的随机个数
	 * @return 当权重总和为0时返回空数组
	 */
	public static int[] getRandomUniqueIndex(List<Integer> weightList, int count) {
		if (weightList == null || count > weightList.size()) {
			return new int[0];
		}
		List<Integer> weights = new ArrayList<Integer>();
		for (Integer w : weightList) {
			weights.add(w);
		}
		int[] results = new int[count];
		List<Integer> tempResults = new ArrayList<Integer>();
		for (int i = 0; i < count; i++) {
			int index = random(weights.toArray(new Integer[0]));
			if (index == -1) {
				results = new int[0];
				break;
			}
			weights.remove(index);
			for (int j : tempResults) {
				if (j <= index) {
					index++;
				}
			}
			results[i] = index;
			tempResults.add(index);
			Collections.sort(tempResults);
		}
		return results;
	}

	/**
	 * @explain：从指定的数组中随机取count个，返回这个数组
	 * @return
	 * @return int [] 包含随机取的count个值的数组
	 */
	public static int[] getRandomIdList(int[] allIdList,int count){

		Random random = new Random();
		List<Integer> result = new ArrayList<>();
		for(int i =0; i<10000;i++){
			if(result.size() >= count){
				break;
			}
			int idx= random.nextInt(allIdList.length);
			int value = allIdList[idx];
			if(!result.contains(value)){
				result.add(value);
			}
		}

		int[] randomIdList = result.stream().mapToInt(Integer::valueOf).toArray();
		return randomIdList;
	}

	public static List<Integer> getRandomIdList(List<Integer> allIdList,int count){

		Random random = new Random();
		List<Integer> result = new ArrayList<>();
		if(allIdList.isEmpty()){
			return result;
		}
		if(allIdList.size()<count){
			return allIdList;
		}
		for(int i =0; i<10000;i++){
			if(result.size() >= count){
				break;
			}
			int idx= random.nextInt(allIdList.size());
			int value = allIdList.get(idx);
			if(!result.contains(value)){
				result.add(value);
			}
		}
		return result;
	}

	public static int[] getRandomArray(int[] paramArray,int count){
		if(paramArray.length<count){
			return paramArray;
		}
		int[] newArray=new int[count];
		Random random= new Random();
		int temp=0;//接收产生的随机数
		List<Integer> list=new ArrayList<Integer>();
		for(int i=1;i<=count;i++){
			temp=random.nextInt(paramArray.length);//将产生的随机数作为被抽数组的索引
			if(!(list.contains(temp))){
				newArray[i-1]=paramArray[temp];
				list.add(temp);
			}
			else{
				i--;
			}
		}
		return newArray;
	}

}
