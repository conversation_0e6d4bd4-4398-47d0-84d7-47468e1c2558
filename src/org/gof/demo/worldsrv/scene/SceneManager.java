package org.gof.demo.worldsrv.scene;

import org.gof.core.support.ManagerBase;

/**
 * 场景触发器
 *
 */
public class SceneManager extends ManagerBase {

	/**
	 * 获取实例
	 * 
	 * @return
	 */
	public static SceneManager inst() {
		return inst(SceneManager.class);
	}


//	public void repSceneInit(StageObjectInstance stageObject) {
//
//		repSceneInit(stageObject, stageObject.confMap.sceneID);
//
//	}
//
//	/**
//	 * 副本初始化，构建场景事件，触发器，事件数据结构
//	 * Scene{
//	 *     ScenePlot{
//	 *         event,
//	 *         trigger
//	 *     },
//	 *     ScenePlot{
//	 *         event,
//	 *         trigger
//	 *     }...
//	 * }
//	 * @param stageObject
//	 */
//	public void repSceneInit(StageObjectInstance stageObject, int sceneID) {
//
//		ConfScene confScence = ConfScene.get(sceneID);
//		if(confScence == null){
//			Log.temp.info("未找到对应的scene信息,sceneID={},不初始化副本剧情"+stageObject.confMap.sceneID);
//			return;
//		}
//		//构建所有的场景
//		List<ScenePlot> plotList = new ArrayList<>();
//		for (int i = 0; i < confScence.plotIDs.length; i++) {
//			//初始化 ScenfinishEventePlot
//			ConfScenePlot confPlot = ConfScenePlot.get(confScence.plotIDs[i]);
//			//配置错误
//			if(confPlot == null){
//				Log.stageCommon.error("场景配置confPlot不存在：[{}]", confScence.plotIDs[i]);
//				continue;
//			}
//
//			ScenePlot plot = new ScenePlot(confScence.plotIDs[i], confPlot, stageObject);
//
//			//加入自身到场景里
//			plotList.add(plot);
//			stageObject.plotMap.put(plot.id, plot);
//		}
//
//		//构建前置场景关系
//		for (ScenePlot sp : plotList) {
//			//如果没有前置场景
//			if(sp.conf.frontPlotIDs.length == 0)
//				continue;
//
//			//挨个注入前置场景对象
//			for (String fsp : sp.conf.frontPlotIDs) {
//				for (ScenePlot spr : plotList) {
//					if(spr.sn.equals(fsp))
//						sp.prePlots.put(spr.id, spr.sn);
//				}
//			}
//		}
//
//		//初始化地图上所有触发器
//		stageObject.plotTriggerCurr.clear();
//		List<Long> list;
//		for (ScenePlot sp : plotList) {
//			for (SceneTrigger trigger : sp.triggers.values()) {
//				if(!stageObject.plotTriggerCurr.containsKey(trigger.type)){
//					list = new LinkedList<>();
//					stageObject.plotTriggerCurr.put(trigger.type, list);
//				}else{
//					list = stageObject.plotTriggerCurr.get(trigger.type);
//				}
//				list.add(trigger.id);
//			}
//		}
//	}
//
//	/**
//	 * 根据状态重新设置触发器
//	 * @param stageObject
//	 */
//	public void resetPlots(StageObject stageObject) {
//	    //这里保证了只有正在进行中的剧本中的触发器才会被放进去
//		stageObject.plotTriggerCurr.clear();
//		List<Long> list;
//		List<String> triggers = new ArrayList<>();
//
//		for (ScenePlot sp : stageObject.plotMap.values()) {
//			if(sp.status == ScenePlot.SCENT_PLOT_STATUS_DOING){
//				for (SceneTrigger trigger : sp.triggers.values()) {
//					if(!stageObject.plotTriggerCurr.containsKey(trigger.type)){
//						list = new LinkedList<>();
//						stageObject.plotTriggerCurr.put(trigger.type, list);
//					}else{
//						list = stageObject.plotTriggerCurr.get(trigger.type);
//					}
//					list.add(trigger.id);
//
//					triggers.add(trigger.sn);
//				}
//
//			}
//		}
//
//		Log.instance.debug("当前剧情触发器={}",triggers);
//
//	}
//
//	/**
//	 * 完成一个事件
//	 *
//	 * 完成事件的时候，都需要检测是否有开启新的剧情或者结束某个剧情
//	 *
//	 */
//	public void finishEvent(long eventId, long poltId, StageObject stageObj) {
//		//如果剧情不存在，返回
//		ScenePlot plot = stageObj.plotMap.get(poltId);
//		if(plot == null){
//			return;
//		}
//
//		//如果当前剧情不是进行中，返回
//		if(plot.status != ScenePlot.SCENT_PLOT_STATUS_DOING) {
//			return;
//		}
//
//		//设置剧情完成状态
//		SceneEvent event = plot.events.get(eventId);
//
//  		event.status = SceneEvent.SCENE_EVENT_STATUS_FINISH;
//
//		//加入到完成列表，防止多次执行
//		stageObj.plotFinishEvent.add(event.sn);
//		stageObj.onPlotFinishEvent(event.sn);
//
//		//激活触发
//		Event.fire(EventKey.SCENE_TRIGGER_08, "stageObj", stageObj, "event", event);
//		if (S.isBridge) {
//			Event.fire(EventBridgeKey.BRIDGE_SCENE_TRIGGER_08, "stageObj", stageObj, "event", event);
//		}
//
//		if(!plot.isFinish()) {
//			return;
//		}
//
//		// 3类型死亡移除特殊处理，事件完成重现来过
//		for(SceneTrigger sceneTrigger : plot.triggers.values()){
//			ConfSceneTrigger confSceneTrigger = ConfSceneTrigger.get(sceneTrigger.sn);
//			if(confSceneTrigger != null){
//				if(confSceneTrigger.triggerType == SceneConstant.SCENE_TRIGGER_03 && confSceneTrigger.nParam3 == 1){
//					List<Integer> monsterSnList = Utils.intToIntegerList(confSceneTrigger.arrnParam1);
//					if(monsterSnList != null || !monsterSnList.isEmpty()){
//						stageObj.monsterDieList.removeAll(monsterSnList);
//						Log.temp.info("===移除死亡的snList={}", monsterSnList);
//					}
//				}
//			}
//		}
//
//
//
//		//如果当前剧情结束，就判断后面是否有剧情开启
//		List<String> rearPs = Arrays.asList(plot.conf.rearPlotIDs);// 后置剧情的ID集合
//		for(ScenePlot p : stageObj.plotMap.values()){
//			if(rearPs.contains(p.conf.sn)){
//				p.status = ScenePlot.SCENT_PLOT_STATUS_NOT_STARTED;
//			}
//		}
//
//		//判断前置剧情是否完成，只处理没有开始的
//		for (ScenePlot p : stageObj.plotMap.values()) {
//			 //只处理没有开始的
//			 if(p.status == ScenePlot.SCENT_PLOT_STATUS_NOT_STARTED){
//				  boolean can = p.canStart();
//				  if(can){
//				  	//如果可以开启，检测一下剧本的触发器是否已经触发过
//				  	SceneTriggerManager.inst().doEvent(stageObj, p);
//				  }
//			 }
// 		}
////		Log.game.info("剧情={}完成,重置触发器",plot.conf.sn);
//
//		//如果有发生状态改变的，重置剧情
//		resetPlots(stageObj);
//
//	}
//
//
//	/**
//	 * 获取AreaObject的消息
//	 * @param areaObj
//	 * @return
//	 */
//	public DStageArea getAreaObjectMsg(AreaObject areaObj ) {
//		DStageArea.Builder areaInfo = DStageArea.newBuilder();
//		areaInfo.setAreaType(EAreaType.TRANSPORT);
//		areaInfo.setToMapSn(areaObj.getConf().destSceneIDs[0]);
//		areaInfo.setShapeType(areaObj.getShapeType());
//		areaInfo.setPos(areaObj.getCenter().toMsg());
//		areaInfo.setSn(areaObj.getConf().sn);
////		areaInfo.setid
//		if(areaObj instanceof RoundAreaObject) {
//			RoundAreaObject robj = (RoundAreaObject)areaObj;
//			areaInfo.setRadius(robj.getRadius());
//		}
//
//		return areaInfo.build();
//	}
//
//	/**
//	 * 根据配表类型获取对应程序事件类型
//	 * @param type
//	 * @return
//	 */
//	public int getEventKey(int type){
//		//给一个默认的Key值
//		int eventKey = 0;
//		switch (type) {
//			case SceneConstant.SCENE_EVENT_05:
//				eventKey = EventKey.SCENE_EVENT_05;
//				break;
//			case SceneConstant.SCENE_EVENT_06:
//				eventKey = EventKey.SCENE_EVENT_06;
//				break;
//			case SceneConstant.SCENE_EVENT_09:
//				eventKey = EventKey.SCENE_EVENT_09;
//				break;
//			case SceneConstant.SCENE_EVENT_11:
//				eventKey = EventKey.SCENE_EVENT_11;
//				break;
//			case SceneConstant.SCENE_EVENT_14:
//				eventKey = EventKey.SCENE_EVENT_14;
//				break;
//			case SceneConstant.SCENE_EVENT_15:
//				eventKey = EventKey.SCENE_EVENT_15;
//				break;
//			case SceneConstant.SCENE_EVENT_17:
//				eventKey = EventKey.SCENE_EVENT_17;
//				break;
//			case SceneConstant.SCENE_EVENT_18:
//				eventKey = EventKey.SCENE_EVENT_18;
//				break;
//			case SceneConstant.SCENE_EVENT_21:
//				eventKey = EventKey.SCENE_EVENT_21;
//				break;
//			case SceneConstant.SCENE_EVENT_23:
//				eventKey = EventKey.SCENE_EVENT_23;
//				break;
//			case SceneConstant.SCENE_EVENT_07:
//				eventKey = EventKey.SCENE_EVENT_07;
//				break;
//			case SceneConstant.SCENE_EVENT_10:
//				eventKey = EventKey.SCENE_EVENT_10;
//				break;
//			case SceneConstant.SCENE_EVENT_24:
//				eventKey = EventKey.SCENE_EVENT_24;
//				break;
//			case SceneConstant.SCENE_EVENT_25:
//				eventKey = EventKey.SCENE_EVENT_25;
//				break;
//			case SceneConstant.SCENE_EVENT_26:
//				eventKey = EventKey.SCENE_EVENT_26;
//				break;
//			case SceneConstant.SCENE_EVENT_30:
//				eventKey = EventKey.SCENE_EVENT_30;
//				break;
//			case SceneConstant.SCENE_EVENT_34:
//				eventKey = EventKey.SCENE_EVENT_34;
//				break;
//			case SceneConstant.SCENE_EVENT_40:
//				eventKey = EventKey.SCENE_EVENT_40;
//				break;
//			case SceneConstant.SCENE_EVENT_41:
//				eventKey = EventKey.SCENE_EVENT_41;
//				break;
//			case SceneConstant.SCENE_EVENT_43:
//				eventKey = EventKey.SCENE_EVENT_43_ANSWER;
//				break;
//			case SceneConstant.SCENE_EVENT_46:
//				eventKey = EventKey.SCENE_EVENT_46;
//				break;
//			case SceneConstant.SCENE_EVENT_47:
//				eventKey = EventKey.SCENE_EVENT_47;
//				break;
//			case SceneConstant.SCENE_EVENT_48:
//				eventKey = EventKey.SCENE_EVENT_48;
//				break;
//			case SceneConstant.SCENE_EVENT_49:
//				eventKey = EventKey.SCENE_EVENT_49;
//				break;
//			case SceneConstant.SCENE_EVENT_50:
//				eventKey = EventKey.SCENE_EVENT_50;
//				break;
//			case SceneConstant.SCENE_EVENT_51:
//				eventKey = EventKey.SCENE_EVENT_51;
//				break;
//			case SceneConstant.SCENE_EVENT_52:
//				eventKey = EventKey.SCENE_EVENT_52;
//				break;
//			case SceneConstant.SCENE_EVENT_53:
//				eventKey = EventKey.SCENE_EVENT_53;
//				break;
//
//			//如果没有匹配，直接返回
//			default:
//				return eventKey;
//		}
//		return eventKey;
//	}

}