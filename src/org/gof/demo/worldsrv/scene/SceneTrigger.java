package org.gof.demo.worldsrv.scene;

/**
 * 场景数据结构：触发器
 * 
 * <AUTHOR>
 */
public class SceneTrigger {
	//常量
	private static final int SCENE_TRIGGER_STATUS_FINISH = 1;	 //已触发
	private static final int SCENE_TRIGGER_STATUS_NONE = 0;		//没触发

	public long id;						//唯一ID
	public String sn;					//配置SN
	private int status;					//状态
	public int type;						//触发器类型
//	public ConfSceneTrigger conf;	//配置
	
	public ScenePlot plot;			//场景剧情

	private boolean prohibit = false;//是否被禁止触发
	/**
	 * 构造方法
	 */
//	public SceneTrigger(String sn, ConfSceneTrigger conf, ScenePlot plot) {
//		this.id = plot.stageObject.getAutoId();
//		this.sn = sn;
//		this.conf = conf;
//		this.plot = plot;
//		if(conf != null){
//			this.type = conf.triggerType;
//		}
//		setStatus(SCENE_TRIGGER_STATUS_NONE);
//	}
//
//	/**
//	 * 触发触发器
//	 */
//	public void trigger(){
//		Log.instance.info("触发器触发={}",conf.sn);
//		setStatus(SCENE_TRIGGER_STATUS_FINISH);
//	}
//
//	/**
//	 * 重置状态
//	 */
//	public void reActivate(){
//		setStatus(SCENE_TRIGGER_STATUS_NONE);
//	}
//
//	/**
//	 * 触发器是否触发
//	 * @return
//	 */
//	public boolean isTrigger(){
//		return status == SCENE_TRIGGER_STATUS_FINISH;
//	}
//
//	private void setStatus(int status){
//		this.status = status;
//	}
}
