package org.gof.demo.worldsrv.scene;

import java.util.*;

/**
 * 剧情管理器
 */
public class SceneHandler {

    private Map<Long, ScenePlot> plotMap = new HashMap<>();			//所有的数据结构
    private Map<Integer, List<Long>> plotTriggerCurr = new HashMap<Integer, List<Long>>();//当前的进行中的剧情触发器
    private Map<Long, SceneTrigger> plotMoveTrigger = new HashMap<>();	//移动的trigger，用于优化处理，移动比较频繁
    private Map<Long, SceneTrigger> plotTriggerAll = new HashMap<>();			//所有的触发器
    private Set<String> plotFinishEvent = new HashSet<String>();					//已经完成的Event的SN，防止多次执行


}
