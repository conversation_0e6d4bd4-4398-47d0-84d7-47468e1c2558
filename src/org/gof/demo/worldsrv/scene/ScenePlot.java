package org.gof.demo.worldsrv.scene;

import org.gof.demo.worldsrv.stage.StageObject;

import java.util.HashMap;
import java.util.Map;

/**
 * 场景数据结构：剧情
 * 
 * <AUTHOR>
 */
public class ScenePlot {
	//常量
	public static final int SCENT_PLOT_STATUS_NOT_STARTED = 0;			//未开始
	public static final int  SCENT_PLOT_STATUS_DOING= 1;		//进行中
	public static final int  SCENT_PLOT_STATUS_FINISH= 2;		//完成
	
	public long id;										//唯一ID
	public String sn;									//配置SN
	public int status;									//状态
//	public ConfScenePlot conf;					//配置
	public StageObject stageObject;			//所属的StageObject
	
	public Map<Long, String> prePlots = new HashMap<>();		//前置剧情
	public Map<Long, SceneTrigger> triggers = new HashMap<>();		//触发器列表
	public Map<Long, SceneEvent> events = new HashMap<>();			//事件列表
	public long nextRuntime = 0;	//结束时间

//	/**
//	 * 构造方法
//	 */
//	public ScenePlot(String sn, ConfScenePlot confPlot, StageObject stageObject) {
//		this.id = stageObject.getAutoId();
//		this.sn = sn;
//		this.conf = confPlot;
//
//		Log.instance.debug("加载剧本={}",sn);
//
//		if(confPlot.frontPlotIDs.length > 0){
//			//如果有前置剧情的需求，那么状态设置成未开启
//			this.status = ScenePlot.SCENT_PLOT_STATUS_NOT_STARTED;
//		}else{
//			//如果没有，就设置正在进行
//			this.status = ScenePlot.SCENT_PLOT_STATUS_DOING;
//		}
//		this.stageObject = stageObject;
//
//		//初始化Trigger
//		for (int j = 0; j < confPlot.triggerIDs.length; j++) {
//			ConfSceneTrigger confTrigger = ConfSceneTrigger.get(confPlot.triggerIDs[j]);
//			SceneTrigger trigger = new SceneTrigger(confPlot.triggerIDs[j], confTrigger, this);
//			this.triggers.put(trigger.id, trigger);
//
//			//加入所有的触发器
//			stageObject.plotTriggerAll.put(trigger.id, trigger);
//			Log.instance.debug("初始化触发器 trigger.id={}, trigger.type={}, trigger.sn={}",trigger.id,trigger.type
//					,trigger.sn);
//		}
//
//
//		//加入一个随机事件
//		if(confPlot.randomEventIDs.length > 0) {
//			int randomIndex = RandomUtil.random(0, confPlot.randomEventIDs.length-1);
//			String randomSn = confPlot.randomEventIDs[randomIndex];
//			ConfSceneEvent randomEvent = ConfSceneEvent.get(randomSn);
//			if(randomEvent != null) {
//				SceneEvent event = new SceneEvent(randomSn, randomEvent, this);
//				addEvent(event);
//			}
//
//
//		}
//
//		//加入必然事件
//		if(confPlot.doEventIDs.length > 0){
//			for(String eventSn : confPlot.doEventIDs){
//				ConfSceneEvent mEvent = ConfSceneEvent.get(eventSn);
//				SceneEvent mustEvent = new SceneEvent(eventSn, mEvent, this);
//				addEvent(mustEvent);
//			}
//		}
//
//	}
//
//	/**
//	 * 启动剧本
//	 * 启动剧本就是开启所有剧本中的事件和触发器
//	 */
//	public void startup(){
//
//		List<Long> list;
//		for (SceneTrigger trigger : this.triggers.values()) {
//			if(!stageObject.plotTriggerCurr.containsKey(trigger.type)){
//				list = new LinkedList<>();
//				stageObject.plotTriggerCurr.put(trigger.type, list);
//			}else{
//				list = stageObject.plotTriggerCurr.get(trigger.type);
//			}
//			// 重新激活里面的触发器
//			trigger.reActivate();
//			list.add(trigger.id);
//		}
//
//		status = SCENT_PLOT_STATUS_DOING;
//
//	}
//
//	public void addEvent(SceneEvent event){
//		Log.stageCommon.debug("加载事件={}",event.sn);
//		this.events.put(event.id, event);
//	}
//
//	/**
//	 * 判断当前剧情是否完结
//	 * @return
//	 */
//	public boolean isFinish() {
//
//		//如果已经完成，直接返回
//		if(this.status == ScenePlot.SCENT_PLOT_STATUS_FINISH){
//			return true;
//		}
//
//		//如果未开始，返回
//		if(this.status == ScenePlot.SCENT_PLOT_STATUS_NOT_STARTED){
//			return false;
//		}
//
//		//挨个判断条件
//		boolean finish = true;
//		for (SceneEvent event : events.values()) {
//			if(event.status == SceneEvent.SCENE_EVENT_STATUS_NONE || event.status == SceneEvent.SCENE_EVENT_STATUS_DOING)
//				finish = false;
//		}
//
//		//已经完成
//		if(finish){
//			status = ScenePlot.SCENT_PLOT_STATUS_FINISH;
//			// 如果是循环剧本,就设置下一次运行的时间
//			if(conf.roundTime > 0){
//				nextRuntime = Port.getTime() + conf.roundTime;
//			}
//
//		}
//
//
//
//		return finish;
//	}
//
//	/**
//	 * 判断当前剧情是否可以开始
//	 * @return
//	 */
//	public boolean canStart() {
//		//如果当前状态不是未开始，直接返回true
//		if(status != ScenePlot.SCENT_PLOT_STATUS_NOT_STARTED)
//			return true;
//
//		//挨个判断前置条件是否都已经完成
//		boolean canStart = prePlotCompleted();
//
//		//如果可以开始，设置状态/;
//		if(canStart){
//			status = ScenePlot.SCENT_PLOT_STATUS_DOING;
//			Log.instance.info("启动剧本={}", conf.sn);
//		}
//
//		return canStart;
//	}
//
//
//
//	/**
//	 * 前置剧情是否完成
//	 * @return
//	 */
//	public boolean prePlotCompleted(){
//		for (Long plotId : prePlots.keySet()) {
//			ScenePlot p = stageObject.plotMap.get(plotId);
//			if(p!=null && !p.isFinish()){
//				return false;
//			}
//		}
//
//		return true;
//	}
//
//	//判断plot内的trigger是否都满足，如果满足激活事件
//	public boolean isTriggerSatisfy(){
//
//		//boolean satisfy = false;
//		if(conf.triggerCondition == 0){
//			//与的关系
//			for (SceneTrigger trigger : triggers.values()) {
//				if(!trigger.isTrigger()){
//					return false;
//				}
//			}
//			return true;
//		}else {
//			//或的关系
//			for (SceneTrigger trigger : triggers.values()) {
//
//				if(trigger.isTrigger()){
//					return true;
//				}
//			}
//			return false;
//		}
//
//	}


}
