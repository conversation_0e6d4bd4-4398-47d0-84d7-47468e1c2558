package org.gof.demo.worldsrv.pocketLine;


public class PocketLineEventSubKey {
	/** pay **/
	public static final String PAY = "PAY";
	/** 处理闪钻购买的充值 */
	public static final String PAY_FAKE = "PAY_FAKE";
	/** pay_tw **/
	public static final String PAY_TW = "PAY_TW";
	/** 消息球 **/
	public static final String NOTICE_BALL = "NOTICE_BALL";
	/** 禁言 **/	
	public static final String SILENCE = "SILENCE";
	/**充值礼包 */
	public static final String PAY_GIFT = "PAY_GIFT";
	/**充值礼包创建 */
	public static final String PAY_GIFT_CREATE = "PAY_GIFT_CREATE";
	/**离线奖励 */
	public static final String OFFLINE_REWARD = "OFFLINE_REWARD";
	/**玩家公会信息变动 */
	public static final String GUILD_UPDATE = "GUILD_JOIN";
	/**踢出公会成员 */
	public static final String GUILD_KICK = "GUILD_KICK";
	/**帮助*/
	public static final String GUILD_HELP = "GUILD_HELP";
	/**农场偷完成**/
	public static final String FARM_STEAL = "FARM_STEAL";
	/**农场被偷完成**/
	public static final String FARM_STOLEN = "FARM_STOLEN";
	/**建筑升级完成**/
	public static final String FARM_UPGRADE_FINISH = "FARM_UPGRADE_FINISH";
	/**停车奖励*/
	public static final String CAR_PARK_REWARD = "CAR_PARK_REWARD";
	/**胖头鱼在线*/
	public static final String GUILD_GVE_ONLINE = "GUILD_GVE_ONLINE";
	/**胖头鱼击杀*/
	public static final String GUILD_GVE_KILL = "GUILD_GVE_KILL";
	/**胖头鱼结算*/
	public static final String GUILD_GVE_SETTLE = "GUILD_GVE_SETTLE";

	public static final String GUILD_LEAGUE_ADD_SCORE = "GUILD_LEAGUE_ADD_SCORE";
	/**七夕活动收到鲜花**/
	public static final String RECEIVE_FLOWER = "RECEIVE_FLOWER";
	/**成就进度*/
	public static final String ACHIEVEMENT_PROGRESS = "ACHIEVEMENT_PROGRESS";
	/** 上架飞宠信息更新 */
	public static final String FLY_PET_BORROW_HYBRID_UPDATE = "FLY_PET_BORROW_HYBRID_UPDATE";
	/** 玩家飞宠配对搭档列表更新 */
	public static final String FLY_HYBRID_PARTNER_UPDATE = "FLY_HYBRID_PARTNER_UPDATE";
	/** 玩家飞宠搭档申请或者飞宠权限申请 */
	public static final String FLY_HYBRID_APPLY = "FLY_HYBRID_APPLY";
	/**武道会发放竞猜币*/
	public static final String KUNG_FU_RACE_ADD_BET_COIN = "KUNG_FU_RACE_ADD_BET_COIN";
	/**武道会更新任务*/
	public static final String KUNG_FU_RACE_UPDATE_TASK = "KUNG_FU_RACE_UPDATE_TASK";
	// 排位赛段位
	public static final String ARENA_RANKED_GRADE = "ARENA_RANKED_GRADE";
	// 好友变更
	public static final String UPDATE_FRIEND = "UPDATE_FRIEND";
    /**乱斗32强发放竞猜币*/
    public static final String FAMILY32_ADD_BET_COIN = "FAMILY32_ADD_BET_COIN";
}