package org.gof.demo.worldsrv.bridgeEntity;

import org.gof.core.gen.entity.Column;
import org.gof.core.gen.entity.Entity;

@Entity(entityName="GuildLeagueEnrollWarmUp", tableName="guild_league_enroll_warmup", listKey = "groupPvp", autoId = true)
public enum EntityGuildLeagueEnrollWarmUp {
    @Column(type=int.class, comment = "赛季", index = true)
    season,
    @Column(type=long.class, comment="宗门id", index = true)
    guildId,
    @Column(type=int.class, comment="段位")
    grade,
    @Column(type=int.class, comment = "乱斗组", index = true)
    groupPvp,
    @Column(type=int.class, comment = "来自服务器id")
    serverId,
    @Column(type=long.class, comment = "报名时间")
    time,
    ;
}
