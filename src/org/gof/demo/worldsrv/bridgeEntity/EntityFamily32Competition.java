package org.gof.demo.worldsrv.bridgeEntity;

import org.gof.core.gen.entity.Column;
import org.gof.core.gen.entity.Entity;

@Entity(entityName="Family32Competition", tableName="family32_competition", autoId = true, listKey = "seasonName")
public enum EntityFamily32Competition {
    @Column(type = String.class, comment = "赛季名称", length = 64, index = true)
    seasonName,
    @Column(type=int.class, comment = "战斗标识")
    battleFlag,
    @Column(type=int.class, comment = "循环赛分组id")
    groupId,
    @Column(type=int.class, comment = "循环赛战斗轮次")
    round,
    @Column(type=long.class, comment = "公会1")
    guildId1,
    @Column(type=long.class, comment = "公会2")
    guildId2,
    @Column(type = byte[].class, comment = "初始战斗数据", length = 65536)
    initialBattleData,
    @Column(type=long.class, comment = "路线1胜利公会")
    road1WinGuildId,
    @Column(type = byte[].class, comment = "路线1战斗数据", length = 65536)
    road1BattleData,
    @Column(type=long.class, comment = "路线2胜利公会")
    road2WinGuildId,
    @Column(type = byte[].class, comment = "路线2战斗数据", length = 65536)
    road2BattleData,
    @Column(type=long.class, comment = "路线3胜利公会")
    road3WinGuildId,
    @Column(type = byte[].class, comment = "路线3战斗数据", length = 65536)
    road3BattleData,
    ;
}
