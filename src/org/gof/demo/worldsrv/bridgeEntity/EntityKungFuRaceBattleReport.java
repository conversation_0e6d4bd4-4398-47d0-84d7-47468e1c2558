package org.gof.demo.worldsrv.bridgeEntity;

import org.gof.core.gen.entity.Column;
import org.gof.core.gen.entity.Entity;

@Entity(entityName="KungFuRaceBattleReport", tableName="kung_fu_race_battle_report", autoId = true, listKey = "seasonName")
public enum EntityKungFuRaceBattleReport {
    @Column(type = String.class, comment = "赛季名称", length = 64, index = true)
    seasonName,
    @Column(type=long.class, comment = "我方队伍")
    myTeamId,
    @Column(type=long.class, comment = "敌方队伍")
    enemyTeamId,
    @Column(type=int.class, comment = "当前积分")
    score,
    @Column(type=int.class, comment = "变化的积分")
    changeScore,
    @Column(type=long.class, comment = "战斗时间戳")
    time,
    ;
}
