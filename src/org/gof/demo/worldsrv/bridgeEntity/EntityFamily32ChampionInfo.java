package org.gof.demo.worldsrv.bridgeEntity;

import org.gof.core.dbsrv.redis.EntityManager;
import org.gof.core.gen.entity.Column;
import org.gof.core.gen.entity.Entity;

@Entity(entityName="Family32ChampionInfo", tableName="family32_champion_info", autoId = true, listKey = EntityManager.ALL_LIST_KEY)
public enum EntityFamily32ChampionInfo {
    @Column(type=int.class, comment = "赛区id", index = true)
    zoneId,
    @Column(type=int.class, comment = "第几赛季")
    season,
    @Column(type=int.class, comment = "赛区最小服务器id")
    zoneMinServerId,
    @Column(type=int.class, comment = "赛区最大服务器id")
    zoneMaxServerId,
    @Column(type=long.class, comment = "公会id")
    guildId,
    @Column(type = String.class, comment = "公会名字", length = 128)
    guildName,
    @Column(type=int.class, comment = "公会所属服务器")
    serverId,
    @Column(type = String.class, comment = "公会总战力", length = 64, defaults = "1")
    totalCombat,
    @Column(type=int.class, comment = "信仰值")
    worship,
    @Column(type=long.class, comment = "会长id")
    leaderId,
    @Column(type = String.class, comment = "公会旗帜", length = 1024, defaults = "[]")
    guildFlagJson,
    ;
}
