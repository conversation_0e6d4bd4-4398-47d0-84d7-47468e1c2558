package org.gof.demo.worldsrv.entity;

import org.gof.core.gen.entity.Column;
import org.gof.core.gen.entity.Entity;

@Entity(entityName="GuildApply", tableName="guild_apply", listKey = "guildId")
public enum EntityGuildApply {
	@Column(type=long.class, comment="玩家id")
	humanId,
	@Column(type=String.class, comment="玩家名字", length = 64)
	name,
	@Column(type=long.class, comment="公会id")
	guildId,
	@Column(type=String.class, comment="头像信息", defaults = "", length=256)
	roleHead,
	@Column(type=int.class, comment="职业")
	career,
	@Column(type=long.class, comment="申请时间")
	applyTime,
	@Column(type=int.class, comment="等级")
	level,

	@Column(type=String.class, comment = "扩展属性(用于处理bug)", length = 256, defaults = "")
	extendJSON,
	;
}
