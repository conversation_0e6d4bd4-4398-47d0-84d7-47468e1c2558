package org.gof.demo.worldsrv.entity;

import org.gof.core.gen.entity.Column;
import org.gof.core.gen.entity.Entity;

@Entity(entityName = "HumanSubjoinInfo" , tableName = "demo_human_subjoin_info")
public enum EntityHumanSubjoinInfo {
    @Column(type = String.class, comment = "记录0点是否重置", defaults = "{}", length=1024)
    reset0Json,
    @Column(type = String.class, comment = "记录5点是否重置", defaults = "{}", length=1024)
    reset5Json,
    @Column(type = String.class, comment = "记录每周0点是否重置", defaults = "{}", length=512)
    resetWeek0Json,
    @Column(type = String.class, comment = "记录每周5点是否重置", defaults = "{}", length=512)
    resetWeek5Json,
    @Column(type = int.class, comment = "飞宠是否已初始化", defaults = "0")
    flyPetInit,
    @Column(type = int.class, comment = "飞宠累计培育次数", defaults = "0")
    flyHybridNum,
    @Column(type = int.class, comment = "累计借用飞宠次数", defaults = "0")
    flyHybridBorrowNum
    ;
}
