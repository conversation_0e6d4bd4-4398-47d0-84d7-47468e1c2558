package org.gof.demo.worldsrv.entity;

import org.gof.core.gen.entity.Column;
import org.gof.core.gen.entity.Entity;

@Entity(entityName="Friend", tableName="demo_friend", autoCache = true)
public enum EntityFriend {
	@Column(type=String.class, comment="好友列表", length = 2048, defaults = "[]")
	friendList,
	@Column(type=String.class, comment="随机玩家列表", length = 512, defaults = "[]")
	randList,
	@Column(type=long.class, comment="随机玩家列表下次刷新时间", defaults = "0")
	randListTime,
	@Column(type=String.class, comment="黑名单列表", length = 2048, defaults = "[]")
	blackList,
	@Column(type=String.class, comment="发起申请列表", length = 2048, defaults = "[]")
	applyToList,
	@Column(type=String.class, comment="申请列表", length = 2048, defaults = "[]")
	applyList,
	@Column(type=int.class, comment="每日领取礼物数量", defaults = "0")
	giftNum,
	@Column(type=String.class, comment="亲密度map<好友ID，FavorabilityVo>", length = 4096, defaults = "{}")
	favorabilityMap,
	@Column(type=String.class, comment="亲密度map<好友ID，List<领奖id>>", length = 2048, defaults = "{}")
	favorabilityRewardMap,
	@Column(type=String.class, comment = "扩展属性(用于处理bug)", length = 200, defaults = "")
	extendJSON,
	@Column(type=int.class, comment="合服版号", defaults = "0")
	mergeVer,
	;

}
