package org.gof.demo.worldsrv.entity;

import org.gof.core.gen.entity.Column;
import org.gof.core.gen.entity.Entity;

@Entity(entityName="Angel", tableName="demo_angel")
public enum EntityAngel {
    @Column(type=String.class, comment = "星将星数", length = 1024, defaults = "{}")
    angelStartMap,
    @Column(type=String.class, comment = "发展星阵位置信息", length = 1024, defaults = "{}")
    developPosMap,
    @Column(type=String.class, comment = "战斗星阵位置信息方案", length = 512, defaults = "{}")
    angelArrayTabs,
    @Column(type = int.class, comment = "当前方案")
    currentTab,
    @Column(type=String.class, comment = "抽卡保底Map<sn,Map<type,num>>", length = 256, defaults = "{}")
    guaranteeMap,
    @Column(type=String.class, comment = "扩展属性(用于处理bug)", length = 200, defaults = "")
    extendJSON,
    ;
}
