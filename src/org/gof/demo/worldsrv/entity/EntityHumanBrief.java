package org.gof.demo.worldsrv.entity;

import org.gof.core.gen.entity.Column;
import org.gof.core.gen.entity.Entity;

@Entity(entityName = "HumanBrief", tableName = "human_brief", autoCache = true)
public enum EntityHumanBrief {
    @Column(type = String.class, comment = "名字", length = 64, defaults = "")
    name,
    @Column(type = int.class, comment = "等级", defaults = "1")
    level,
    @Column(type = int.class, comment = "服务器id", defaults = "0")
    serverId,
    @Column(type = long.class, comment = "当前头像", defaults = "0")
    headSn,
    @Column(type = int.class, comment = "当前头像框", defaults = "0")
    currentHeadFrameSn,
    @Column(type = int.class, comment = "职业sn", defaults = "0")
    jobSn,
    @Column(type = int.class, comment = "翅膀技能sn等级", defaults = "0")
    wingSkillSnLv,
    @Column(type = int.class, comment = "坐骑技能sn等级", defaults = "0")
    mountSkillSnLv,
    @Column(type = int.class, comment = "神器技能sn等级", defaults = "0")
    artifactSkillSnLv,
    @Column(type = int.class, comment = "星将星级", defaults = "0")
    angelStar,
    @Column(type = int.class, comment = "当前头衔", defaults = "0")
    currentTitleSn,
    @Column(type = long.class, comment = "飞宠id", defaults = "0")
    flyPetId,
    @Column(type = int.class, comment = "飞宠sn", defaults = "0")
    flyPetSn,
    @Column(type = long.class, comment = "公会ID", defaults = "0")
    guildId,
    @Column(type = String.class, comment = "帮会名称", length = 64, defaults = "")
    guildName,
    @Column(type = int.class, comment = "公会等级", defaults = "1")
    guildLv,
    @Column(type = int.class, comment = "公会人数", defaults = "1")
    guildHumanNum,
    @Column(type = String.class, comment = "最高战斗力", length = 64, defaults = "1")
    topCombat,
    @Column(type = byte[].class, comment = "角色战斗数据")
    battleRole,
    @Column(type = byte[].class, comment = "角色外观数据")
    roleFigure,

    @Column(type = String.class, comment = "默认行装", length = 64, defaults = "")
    planArrStr,
    @Column(type = int.class, comment = "当前行装id", defaults = "0")
    currentPlanId,

    @Column(type = byte[].class, comment = "角色战斗数据行装2")
    battleRole2,
    @Column(type = byte[].class, comment = "角色战斗数据行装3")
    battleRole3,
    @Column(type = byte[].class, comment = "角色战斗数据行装4")
    battleRole4,
    @Column(type = byte[].class, comment = "角色战斗数据行装5")
    battleRole5,
    @Column(type=int.class, comment="美观值", defaults = "0")
    charmValue,
    @Column(type=String.class, comment="展示的勋章列表", defaults = "[]", length = 256)
    showMedalList,
	;

}
