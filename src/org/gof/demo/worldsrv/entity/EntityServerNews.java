package org.gof.demo.worldsrv.entity;

import org.gof.core.gen.entity.Column;
import org.gof.core.gen.entity.Entity;

@Entity(entityName = "ServerNews", tableName = "server_news")
public enum EntityServerNews {
    @Column(type = int.class, comment = "服务器编号", index = true)
    serverId,
    @Column(type = int.class, comment = "新闻播报组")
    groupId,
    @Column(type = String.class, comment = "玩家id列表", length = 2048)
    humanIdList
}