package org.gof.demo.worldsrv.entity;

import org.gof.core.gen.entity.Column;
import org.gof.core.gen.entity.Entity;

@Entity(entityName="Mall", tableName="mall")
public enum EntityMall {
	@Column(type = String.class, comment = "商店类型对应已经购买次数", defaults = "[]", length=2048)
	mallBuyNumMap,

	@Column(type = String.class, comment = "充值商店对应已经购买次数", defaults = "[]", length=2048)
	payMallBuyNumMap,

	@Column(type=String.class, comment = "充值钻石商店", length = 512, defaults = "")
	starDiamondMallSnList,
	@Column(type=String.class, comment = "充值钻石商店", length = 512, defaults = "")
	diamondMallSnList,
	@Column(type=String.class, comment = "限时礼包到期", length = 512, defaults = "{}")
	limitTimeGiftMap,
	@Column(type=String.class, comment = "首冲时间", length = 512, defaults = "{}")
	firstPayGiftTimeMap,

	@Column(type=int.class, comment = "平均充值积分", defaults = "0")
	chargeScore,
	@Column(type = String.class, comment = "礼包档位记录", length = 1024, defaults = "{}")
	gearRangeJSON,
	@Column(type = String.class, comment = "充值表自选礼包的奖励设置存储", length = 1024, defaults = "{}")
	customRewardJSON,

	@Column(type=String.class, comment = "扩展属性(用于处理bug)", length = 200, defaults = "")
	extendJSON,
	;
}
