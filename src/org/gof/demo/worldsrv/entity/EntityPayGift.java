package org.gof.demo.worldsrv.entity;

import org.gof.core.gen.entity.Column;
import org.gof.core.gen.entity.Entity;

@Entity(entityName="PayGift", tableName="pay_gift", listKey = "humanId")
public enum EntityPayGift {
	@Column(type=long.class, comment = "玩家id", index=true)
	humanId,
	@Column(type=String.class, comment="充值id", length=128, index=true, defaults = "")
	payId,
	@Column(type=String.class, comment="充值Sn", length=128, index=true, defaults = "")
	giftCode,
	@Column(type=int.class, comment="可获得的vip点数")
	vipPoints,
	@Column(type=int.class, comment="购买失败后给玩家补单的钻石数量")
	amountToDiamond,
	@Column(type=int.class, comment="礼包的定价")
	price,
	@Column(type=int.class, comment="礼包性价比")
	priceValueRatio,
	@Column(type=int.class, comment="弹出时长秒")
	duration,
	@Column(type=int.class, comment="礼包限购数量")
	purchaseLimitAmount,
	@Column(type=int.class, comment="礼包已经数量")
	buyNum,

	@Column(type=String.class, comment="货币种类", defaults = "")
	currency,
	@Column(type=double.class, comment="对应货币价格")
	currencyPrice,
	@Column(type=String.class, comment="货币标签（符号）", defaults = "")
	currencyTag,
	@Column(type=String.class, comment="礼包模板id", defaults = "")
	templateId,


	//	@Column(type=boolean.class, comment = "是否支持AI")
//	isAISupported,
	@Column(type=String.class, comment="礼包内包含的物品", length=512,  defaults = "{}")
	items,
	@Column(type=boolean.class, comment = "是否显示背景图标")
	enabled,
	@Column(type=String.class, comment="背景图片", length=512,  defaults = "{}")
	background,

	@Column(type=long.class, comment = "创建时间")
	createTime,
	@Column(type=long.class, comment = "结束时间")
	endTime,

}
