package org.gof.demo.worldsrv.entity;

import org.gof.core.gen.entity.Column;
import org.gof.core.gen.entity.Entity;

@Entity(entityName="Capture", tableName="demo_capture", autoCache = true)
public enum EntityCapture {
    @Column(type=String.class, comment="雇员改名信息", length = 2048, defaults = "{}")
    slaveInfoMap,
    @Column(type=String.class, comment="解雇时间Map", length = 2048, defaults = "{}")
    freeTimeMap,
    @Column(type=String.class, comment="随机玩家列表", length = 256, defaults = "")
    nullList,
    @Column(type=long.class, comment="随机玩家列表下次刷新时间", defaults = "0")
    nullListTime,
    @Column(type=String.class, comment="仇人", length = 2048, defaults = "")
    hateList,
    @Column(type=String.class, comment="在线掉落的万倍", length = 1024, defaults = "{}")
    onlineDropTenKMap,
    @Column(type=String.class, comment="离线掉落的万倍", length = 1024, defaults = "{}")
    offlineDropTenKMap,
    @Column(type=String.class, comment = "扩展属性(用于处理bug)", length = 200, defaults = "")
    extendJSON,
    ;
}
