package org.gof.demo.worldsrv.entity;

import org.gof.core.gen.entity.Column;
import org.gof.core.gen.entity.Entity;

@Entity(entityName="Account", tableName="account", listKey = "account")
public enum EntityAccount {
	@Column(type=String.class, comment="账号", index=true, defaults = "")
	account,
	@Column(type=int.class, comment="serverId")
	serverId,
	@Column(type=long.class, comment="删除时间")
	delTime,

	@Column(type=long.class, comment="封号截止时间", defaults = "0", index=true)
	sealEndTime,
	@Column(type=long.class, comment="禁言截止时间", defaults = "0")
	silenceEndTime,

	@Column(type=String.class, comment = "扩展属性(用于处理bug)", length = 200, defaults = "")
	extendJSON,
	;
}
