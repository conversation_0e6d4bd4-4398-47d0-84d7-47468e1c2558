package org.gof.demo.worldsrv.entity;

import org.gof.core.gen.entity.Column;
import org.gof.core.gen.entity.Entity;

@Entity(entityName="ClientInfo", tableName="client_info")
public enum EntityClientInfo {
    @Column(type = long.class, comment = "玩家id", index=true)
    humanId,
    @Column(type = String.class, comment= "客戶端保存信息", length = 2048,  defaults = "")
    clientJSON,

    @Column(type=int.class, comment="强制引导id", defaults = "1")
    guideId,

    ;
}
