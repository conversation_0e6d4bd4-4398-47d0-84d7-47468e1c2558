package org.gof.demo.worldsrv.entity;

import org.gof.core.gen.entity.Column;
import org.gof.core.gen.entity.Entity;

@Entity(entityName="Mount", tableName="demo_mount", autoCache = true)
public enum EntityMount {
	@Column(type=int.class, comment="坐骑等级", defaults="0")
	level,
	@Column(type=int.class, comment="坐骑经验", defaults="0")
	exp,
	@Column(type=String.class, comment="神皮肤sn等级Map", length=1024, defaults="{}")
	skinLvMap,
	@Column(type=String.class, comment="天赋sn等级Map", length=256, defaults="0")
	talentLvMap,
	@Column(type=String.class, comment = "扩展属性(用于处理bug)", length = 200, defaults = "")
	extendJSON,
	;
}
