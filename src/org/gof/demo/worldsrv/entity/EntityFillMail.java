package org.gof.demo.worldsrv.entity;

import org.gof.core.gen.entity.Column;
import org.gof.core.gen.entity.Entity;

/**
 * 全服补偿邮件
 * <AUTHOR>
 *
 */
@Entity(entityName="FillMail", tableName="demo_fill_mail", listKey = "serverId")
public enum EntityFillMail {
	@Column(type=int.class, comment="serverId")
	serverId,
	@Column(type=String.class, comment="标题", length=2048, defaults = "")
	title,	
	@Column(type=String.class, comment="内容", length=2048, defaults = "")
	content,	
	@Column(type=long.class, comment="GM发送时间")
	sendTime,
	@Column(type=long.class, comment="可领取时间")
	startTime,	
	@Column(type=long.class, comment="有效期截止时间戳")
	endTime,
	@Column(type=int.class, comment="最小等级")
	levelMin,
	@Column(type=int.class, comment="最大等级")
	levelMax,
	@Column(type=long.class, comment="创角时刻小于此时间戳")
	registeredBefore,
	@Column(type=long.class, comment="创角时刻大于此时间戳")
	registeredAfter,
	@Column(type=String.class, comment="参数，额外数据", defaults = "")
	param,
	@Column(type=String.class, comment="背景图片", defaults = "")
	bannerLink,

	@Column(type=long.class, comment="系统发送给玩家的时间", defaults="0")
	sysSendTime,
	@Column(type=String.class, comment="物品ID", defaults = "")
	itemSn,	
	@Column(type=String.class, comment="物品数量", defaults = "")
	itemNum,
	@Column(type=String.class, comment="平台的标识", defaults = "")
	eventKey,
	;
}
