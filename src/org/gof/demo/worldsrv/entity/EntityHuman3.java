package org.gof.demo.worldsrv.entity;

import org.gof.core.gen.entity.Column;
import org.gof.core.gen.entity.Entity;

@Entity(entityName="Human3", tableName="demo_human3")
public enum EntityHuman3 {
	/*基础信息*/
	@Column(type=int.class, comment="语言", defaults = "1")
	language,
	@Column(type=int.class, comment="在线时间")
	timeSecOnline,
	@Column(type=long.class, comment="角色SessionKey", index = true)
	sessionKey,
	@Column(type=String.class, comment="默认装备外观map<部位，sn>", defaults = "{}")
	defaultEquipFigureMap,
	@Column(type=String.class, comment="对时器",defaults = "{}")
	atomicClock,
	@Column(type=long.class, comment="累计充值RMB",defaults = "0")
	totalRMB,
	@Column(type=long.class, comment="累计充值金额",defaults = "0")
	chargeGold,
	@Column(type=long.class, comment="累计充值金额(特殊)",defaults = "0")
	chargeGoldSpecial,
	@Column(type=String.class, comment="最高战斗力", defaults="1")
	topCombat,
	@Column(type=long.class, comment="禁言截止时间", defaults = "0")
	silenceEndTime,
	@Column(type=long.class, comment="喇叭禁言截止时间", defaults = "0")
	trumpetEndTime,
	@Column(type=int.class, comment="vip等级", defaults = "0")
	vipLv,
	@Column(type=int.class, comment="属性版本号", defaults = "0")
	propVerNum,
	@Column(type=String.class, comment="地图位置信息", length=512, defaults = "[]")
	stageHistory,
	@Column(type=String.class, comment="复活点位置信息",  defaults = "[]")
	stageRelive,
	@Column(type=String.class, comment = "所在跨服地图位置信息，List<List<Integer>>,id,sn,x,y,地图类型,进入地图的时间戳", length = 200, defaults = "")
	bridgeStageHistory,
	@Column(type=boolean.class, comment = "是否在跨服")
	bridgeStageIn,
	@Column(type=int.class, comment="当前所在场景")
	scene,
	@Column(type=int.class, comment="是否无限", defaults = "0")
	unlimited,
	@Column(type=String.class, comment="副本类型对应通关难度", defaults = "", length = 1024)
	repTypeDifficultyMap,
	@Column(type=String.class, comment="副本类型对应通关难度(组队十点防御值通关)", defaults = "", length = 256)
	repTypeTenDiffMap,
	@Column(type=String.class, comment="副本类型对应累计通过次数", defaults = "", length = 1024)
	repTypeNumMap,
	@Column(type=String.class, comment="解锁副本类型list", defaults = "", length = 1024)
	repTypeList,
	@Column(type=int.class, comment="累计开箱次数（神灯）")
	totalBoxNum,
	@Column(type=int.class, comment="累计启灵次数（雕像）")
	totalStatueNum,
	@Column(type=String.class, comment="拥有的技能sn", defaults = "", length = 2048)
	skillSnList,
	@Column(type=String.class, comment="技能图鉴:sn,等级", defaults = "", length = 1024)
	skillIllustratedMap,
	@Column(type=String.class, comment="技能方案名字", defaults = "", length = 256)
	skillTabNameJSON,
	@Column(type=int.class, comment="抽技能等级", defaults = "1")
	lotterySkillLv,
	@Column(type=int.class, comment="累计抽技能次数")
	totalCardSkillNum,
	@Column(type=boolean.class, comment="是否强化过技能", defaults = "false")
	addLvSkill,
	@Column(type=String.class, comment="同伴图鉴:sn,等级", defaults = "", length = 1024)
	petIllustratedMap,
	@Column(type=String.class, comment="同伴方案名字", defaults = "", length = 256)
	petTabNameJSON,
	@Column(type=int.class, comment="抽同伴等级", defaults = "1")
	lotteryPetLv,
	@Column(type=int.class, comment="累计抽同伴次数")
	totalCardPetNum,
	@Column(type=boolean.class, comment="是否强化过同伴", defaults = "false")
	addLvPet,
	@Column(type=long.class, comment="征战公会boss时间")
	guildBossTime,
	@Column(type=int.class, comment="boss次数", defaults = "2")
	guildBossNum,
	@Column(type=String.class, comment = "功能模块,战力", length = 1024, defaults = "")
	modPowerJSON,
	@Column(type=int.class, comment="定级赛次数", defaults = "0")
	arenaGradingNum,
	@Column(type=int.class, comment="定级赛胜利次数", defaults = "0")
	arenaGradingWinNum,
	@Column(type=int.class, comment="排位赛段位", defaults = "0")
	arenaBridgeGrad,
	@Column(type=boolean.class, comment="定级赛是否结束", defaults = "false")
	arenaGradingEnd,
	@Column(type=long.class, comment="定级赛结束时间")
	arenaGradingEndTime,
	@Column(type=boolean.class, comment="是否检测机器人", defaults="false", index=true)
	checkRobot,
	@Column(type=boolean.class, comment="是否有gm权限", defaults = "false")
	gm,
	@Column(type=String.class, comment = "gveSn", length = 1024, defaults = "")
	gveSnList,
	@Column(type=int.class, comment="预告奖励领取次数")
	previewNum,
	@Column(type=int.class, comment="累计公会捐献次数")
	guildDonateNum,
	@Column(type=String.class, comment = "表情包sn", length = 1024, defaults = "")
	emojiSnList,
	@Column(type=String.class, comment = "表情包sn和过期时间（0永久不在此）", length = 2048, defaults = "")
	emojiSnExpiredMap,
	@Column(type=boolean.class, comment="创角奖励是否发放", defaults = "false")
	isCreateReward,
	@Column(type=int.class, comment="修复版本", defaults = "0")
	fixVersion,
	@Column(type = long.class, comment = "邪眼升级材料当前消耗值，会随重置和分解回退", defaults = "0")
	fateCurrCost,
	@Column(type = long.class, comment = "邪眼升级材料历史最大消耗值", defaults = "0")
	fateMaxCost,
	@Column(type = long.class, comment = "常用的boolean类型变量",defaults = "0")
	flags,
	@Column(type = long.class, comment = "0点重置时间",defaults = "0")
	zeroResetTime,
	@Column(type=String.class, comment="小游戏已完成的章节sn", defaults = "[]", length = 1024)
	backLampFinishedIds,
	@Column(type=String.class, comment="小游戏已领奖的章节sn", defaults = "[]", length = 1024)
	backLampReceivedIds,
    @Column(type=int.class, comment="乱斗32强的赛季", defaults = "0")
    family32Season,
	;

}
