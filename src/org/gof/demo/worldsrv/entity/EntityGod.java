package org.gof.demo.worldsrv.entity;

import org.gof.core.gen.entity.Column;
import org.gof.core.gen.entity.Entity;

@Entity(entityName = "God", tableName = "god")
public enum EntityGod {
    @Column(type = byte[].class, comment = "玩家拥有的所有天使实例proto数据god_update_s2c")
    godsList,
    @Column(type = String.class, comment = "已解锁的位置", length = 64, defaults = "[]")
    unlockedPos,
    @Column(type = int.class, comment = "当前方案", defaults = "1")
    currentTab,
    @Column(type = String.class, comment = "当前阵容配置 Map<方案,Map<位置ID, 天使实例ID>>", length = 2048, defaults = "{}")
    formationTab,
    @Column(type = String.class, comment = "已激活的图鉴 Map<天使id标识, 等级>", length = 2048, defaults = "{}")
    activeCodexMap,
    @Column(type = String.class, comment = "可激活/升级的图鉴 Map<天使id标识, 等级>", length = 2048, defaults = "{}")
    availCodex,
    @Column(type = String.class, comment = "已激活的羁绊 List<羁绊sn>", length = 2048, defaults = "[]")
    activeCamps,
    @Column(type = String.class, comment = "可激活羁绊 List<羁绊sn>", length = 2048, defaults = "[]")
    availCamps,
    @Column(type = String.class, comment = "已拥有的皮肤 Map<皮肤SN, 等级>", length = 1024, defaults = "{}")
    ownedSkinsMap,
    @Column(type = String.class, comment = "已穿戴的皮肤 Map<天使实例ID, 皮肤SN>", length = 1024, defaults = "{}")
    equippedSkinsMap,
    @Column(type=int.class, comment = "目标奖励sn", defaults = "0")
    drawRewardSn,
    @Column(type=int.class, comment = "目标奖励经验", defaults = "0")
    drawExp,
    @Column(type = String.class, comment = "抽卡相关信息（包含保底）", length = 1024, defaults = "{}")
    drawInfo,
    @Column(type = String.class, comment = "扩展字段", length = 512, defaults = "{}")
    extendJson,
    ;
}