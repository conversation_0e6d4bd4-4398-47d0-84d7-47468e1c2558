package org.gof.demo.worldsrv.entity;

import org.gof.core.gen.entity.Column;
import org.gof.core.gen.entity.Entity;

@Entity(entityName="TaskInfo", tableName="task_info")
public enum EntityTaskInfo {
	@Column(type = String.class, comment = "主线任务JSON", defaults = "[]", length=1024)
	mainTaskJSON,
	@Column(type=String.class, comment="每日任务", defaults = "[]", length=1024)
	dailyTaskJSON,
	@Column(type=int.class, comment="每日任务积分")
	dailyScore,
	@Column(type=String.class, comment="每日任务进度领取", defaults = "[]", length=256)
	dailyBoxSnList,
	@Column(type=String.class, comment="头衔任务", defaults = "[]", length=1024)
	titleTaskJSON,
	@Column(type = String.class, comment = "功能预告任务已经领奖list", defaults = "", length=200)
	preFuncTaskList,
	@Column(type=String.class, comment="成就任务", defaults = "[]", length=1024)
	achievementTaskJSON,
	@Column(type=int.class, comment="成就统计已领取奖励id", defaults = "0")
	achievementTotalReceivedId,
	@Column(type = String.class, comment = "飞宠成就任务", defaults = "[]", length = 1024)
	flyAchieveTaskJSON,
	@Column(type = String.class, comment = "飞宠成就任务领取奖励", defaults = "", length = 1024)
	flyAchieveReceiveIdList,
	@Column(type=String.class, comment="武道会任务", defaults = "[]", length=1024)
	kungFuRaceTaskJSON,
	@Column(type=int.class, comment="武道会任务的赛季", defaults = "0")
	kungFuRaceTaskSeason,
	@Column(type=String.class, comment="回归任务", defaults = "[]", length=1024)
	backTaskJSON,
	@Column(type=String.class, comment = "扩展属性(用于处理bug)", length = 200, defaults = "")
	extendJSON,
	;
}
