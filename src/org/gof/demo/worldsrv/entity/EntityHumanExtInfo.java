package org.gof.demo.worldsrv.entity;

import org.gof.core.gen.entity.Column;
import org.gof.core.gen.entity.Entity;
import picocli.CommandLine;

@Entity(entityName = "HumanExtInfo", tableName = "demo_human_info")
public enum EntityHumanExtInfo {
    @Column(type=int.class, comment="头衔等级", defaults = "1")
    adventureTitleLv,
    @Column(type=long.class, comment="改名时间", defaults = "0")
    changeNameTime,
    @Column(type=int.class, comment="装备宝箱等级", defaults = "0")
    equipBoxLv,
    @Column(type=int.class, comment="装备宝箱经验", defaults = "0")
    equipBoxExp,
    @Column(type=long.class, comment="装备宝箱升级结束时间", defaults = "0")
    equipBoxETime,
    @Column(type=int.class, comment="装备宝箱皮肤", defaults = "0")
    equipBoxSkin,
    @Column(type=String.class, comment="装备宝箱信息", length = 2048, defaults = "")
    equipInfo,
    @Column(type=String.class, comment="装备图鉴列表", length = 2048, defaults = "[]")
    equipBookList,
    @Column(type=String.class, comment="称号列表", length = 1024, defaults = "")
    titleList,
    @Column(type=String.class, comment="头像框列表", length = 1024, defaults = "")
    headFrameList,
    @Column(type=String.class, comment="头像", length = 1024, defaults = "")
    headSnList,
    @Column(type=int.class, comment="当前聊天气泡", defaults = "0")
    currentChatBubbleSn,
    @Column(type=String.class, comment="聊天气泡列表", length = 1024, defaults = "")
    chatBubbleList,
    @Column(type=String.class, comment="时装列表List<SkinVo>", length = 1024, defaults = "")
    skinList,
    @Column(type=String.class, comment="设置信息", length=512, defaults = "")
    settingMap,
    @Column(type=long.class, comment="id生成器", defaults = "0")
    idGen,
    @Column(type=int.class, comment="当前行装", defaults = "1")
    plan,
    @Column(type=String.class, comment="行装信息", length=512, defaults = "{}")
    planMap,
    @Column(type=String.class, comment="默认行装信息", length=64, defaults = "")
    defaultPlanArr,
    @Column(type=String.class, comment="坐骑神器背饰方案", length=512, defaults = "{}")
    mountAtfWingTabMap,
    @Column(type=int.class, comment="坐骑神器背饰当前方案", defaults = "1")
    mountAtfWingTab,
    @Column(type=String.class, comment="放置奖励", length=2048, defaults = "{}")
    placingRewardMap,
    @Column(type=int.class, comment="放置奖励是否压缩", defaults = "0")
    isZipPlacing,
    @Column(type=String.class, comment="主线章节击杀怪物sn", length=2048, defaults = "{}")
    mainRepMonsterIdListMap,
    @Column(type=String.class, comment="玩家id对应屏蔽时间", length=2048, defaults = "{}")
    idBlockTimeMap,
    @Column(type=int.class, comment="转盘次数", defaults = "0")
    wheelNum,
    @Column(type=int.class, comment="转盘下次可转时间", defaults = "0")
    wheelNextTime,
    @Column(type=String.class, comment="7日登入1：登入，2：领奖", defaults = "")
    login7Day,
    @Column(type=String.class, comment="1类型等级免费基金已经领取", defaults = "")
    fundFreeGot1,
    @Column(type=String.class, comment="1类型等级高级基金已经领取", defaults = "")
    fundHighGot1,
    @Column(type=String.class, comment="2类型神灯免费基金已经领取", defaults = "")
    fundFreeGot2,
    @Column(type=String.class, comment="2类型神灯高级基金已经领取", defaults = "")
    fundHighGot2,

    @Column(type = String.class, comment = "可按天返还的充值信息{商品sn, 充值次数, 返还结束时间}", defaults = "[]", length = 4096)
    chargeInfo,
    @Column(type = String.class, comment = "活动组领奖sn，活动结束时间Map", defaults = "{}", length = 1024)
    actGroupInfoMap,
    @Column(type = int.class, comment = "新手装备", defaults = "0")
    equipInit,
    @Column(type = String.class, comment = "在线放置奖励万倍值", length = 1024, defaults = "{}")
    onlineDropTenKMap,
    @Column(type = String.class, comment = "离线放置奖励万倍值", length = 1024, defaults = "{}")
    offlineDropTenKMap,

    @Column(type=String.class, comment = "扩展属性(用于处理bug)", length = 200, defaults = "")
    extendJSON,

    @Column(type = String.class, comment = "模块的红点记录信息", length = 1024, defaults = "{}")
    moduleRedPoinJSON,

    @Column(type = int.class, comment = "sns分享状态")
    snsShareState,
    @Column(type = String.class, comment = "sns预约邮件发送状态")
    snsRewardStateJSON,

    @Column(type = String.class, comment = "解锁的神灯皮肤", length =  1024, defaults = "{}")
    treasureUnlockSkinJSON,
    @Column(type = String.class, comment = "装备属性过滤", length =  200, defaults = "{}")
    equipFilterMap,
    ;


}