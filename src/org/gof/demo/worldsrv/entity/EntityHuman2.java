package org.gof.demo.worldsrv.entity;

import org.gof.core.gen.entity.Column;
import org.gof.core.gen.entity.Entity;

@Entity(entityName="Human2", tableName="demo_human2", autoCache = true)
public enum EntityHuman2 {
	@Column(type=int.class, comment="体型sn")
	roleSn,
	@Column(type=int.class, comment="性别")
	sex,
	@Column(type=int.class, comment="当前发色", defaults = "0")
	hairColor,
	@Column(type=int.class, comment="当前皮肤", defaults = "0")
	currentSkin,
	@Column(type=int.class, comment="装备宝箱方案", defaults = "1")
	equipTab,
	@Column(type=String.class, comment="装备外观map<部位，sn>", defaults = "{}")
	equipFigureMap,
	@Column(type=int.class, comment="背饰装配", defaults="0")
	wingUse,
	@Column(type=int.class, comment="背饰技能等级sn", defaults="0")
	wingSkillSnLv,
	@Column(type=int.class, comment="坐骑装配", defaults="0")
	mountUse,
	@Column(type=int.class, comment="坐骑技能sn等级", defaults="0")
	mountSkillSnLv,
	@Column(type=int.class, comment="神器装配", defaults="0")
	artifactUse,
	@Column(type=int.class, comment="神器技能sn等级", defaults="0")
	artifactSkillSnLv,
	@Column(type=int.class, comment="武魂展示", defaults="0")
	fateShow,
	@Column(type=String.class, comment="武魂技能map<sn,lv>", defaults="{}")
	fateSkillMap,
	@Column(type=long.class, comment="公会ID", defaults="0")
	guildId,
	@Column(type=String.class, comment="帮会名称", defaults="")
	guildName,
	@Column(type=int.class, comment="帮会等级", defaults="")
	guildLv,
	@Column(type=String.class, comment="职位名称", defaults="")
	positionName,
	@Column(type=long.class, comment="队伍Id", defaults = "0")
	teamId,
	@Column(type=int.class, comment="客户端当前所在关卡", defaults = "1")
	repSnClient,
	@Column(type=int.class, comment="当前所在关卡", defaults = "1")
	repSn,
	@Column(type=String.class, comment="技能sn和等级", defaults = "", length = 2048)
	skillSnLvMap,
	@Column(type=String.class, comment="技能方案", defaults = "", length = 256)
	skillLineupJSON,
	@Column(type=String.class, comment="技能延迟方案", defaults = "", length = 256)
	skillDelayJSON,
	@Column(type=int.class, comment="当前使用技能方案", defaults = "1")
	useSkillLineup,
	@Column(type=String.class, comment="同伴sn和等级", defaults = "", length = 2048)
	petSnLvMap,
	@Column(type=String.class, comment="同伴方案", defaults = "", length = 256)
	petLineupJSON,
	@Column(type=int.class, comment="当前使用同伴方案", defaults = "1")
	usePetLineup,
	@Column(type=int.class, comment="组队带同伴sn")
	teamPetSn,
	@Column(type = String.class, comment = "伙伴皮肤等级映射", length = 2048, defaults = "{}")
	petSkinSnLvMap,
	@Column(type = String.class, comment = "伙伴方案中皮肤穿戴", length = 2048, defaults = "{}")
	petSkinLineupJSON,
	@Column(type=String.class, comment="科技被动", length = 512, defaults="{}")
	sciencePassiveMap,
	@Column(type=String.class, comment="功能解锁", length = 512, defaults="")
	functionList,
	@Column(type=String.class, comment="背饰被动", length = 512, defaults="{}")
	wingPassiveMap,
    @Column(type = String.class, comment = "飞宠全部行装搭配", length = 1024, defaults = "{}")
    flyPetPlanInfo,
	@Column(type = int.class, comment = "当前使用的飞宠行装", defaults = "1")
	useFlyPetLineup,
	@Column(type = int.class, comment = "主战星将星级1", defaults = "0")
	angelStar1,
	@Column(type = int.class, comment = "辅战星将技能2", defaults = "0")
	angelSkill2,
	@Column(type = int.class, comment = "辅战星将技能3", defaults = "0")
	angelSkill3,
	@Column(type=long.class, comment="回归开始时间")
	backStartTime,
	@Column(type=int.class, comment="触发回归时的开服天数")
	backOpenDay,
	@Column(type=int.class, comment="触发回归时的流失天数")
	backLossDay,
	@Column(type=int.class, comment="已触发回归活动次数")
	triggerBackNum,
	@Column(type=String.class, comment="已领取过回归登录奖励的天数集合", defaults = "[]", length = 64)
	backReceivedInfo,
	@Column(type=int.class, comment="累充轮次")
	accumulatedRechargeRound,
	@Column(type=int.class, comment="本轮累充天数")
	accumulatedRechargeDay,
	@Column(type=String.class, comment="本轮已领取过的累充奖励序号集合", defaults = "[]", length = 64)
	accumulatedRechargeReceivedInfo,
	;
}
