package org.gof.demo.worldsrv.entity;

import org.gof.core.gen.entity.Column;
import org.gof.core.gen.entity.Entity;

@Entity(entityName="Charm", tableName="charm", autoCache = true)
public enum EntityCharm {

	@Column(type=int.class, comment="美观值", defaults = "0")
	charmValue,
	@Column(type=String.class, comment="展示的勋章列表", defaults = "[]", length = 256)
	showMedalList,
	@Column(type=String.class, comment="已领取的美观等级奖励sn列表", defaults = "[]", length = 1024)
	receivedLevelRewardList,
	@Column(type=String.class, comment="已解锁的勋章列表", defaults = "[]", length = 1024)
	unlockMedalList,
	@Column(type = byte[].class, comment = "收藏室数据")
	collectionRoomData,
	;

}