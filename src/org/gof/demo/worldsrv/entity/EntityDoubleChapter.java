package org.gof.demo.worldsrv.entity;

import org.gof.core.gen.entity.Column;
import org.gof.core.gen.entity.Entity;

@Entity(entityName="DoubleChapter", tableName="double_chapter", autoCache = true)
public enum EntityDoubleChapter {
    @Column(type=String.class, comment="选择的奖励列表", length = 512, defaults = "")
    chooseRewardList,
    @Column(type=int.class, comment="当前难度", defaults = "0")
    chapter,
    @Column(type=int.class, comment="当前关卡", defaults = "0")
    level,
    @Column(type=int.class, comment="历史最高难度", defaults = "1")
    maxChapter,
    @Column(type = byte[].class, comment = "战斗数据")
    battleRole,
    @Column(type = byte[].class, comment = "助战队友")
    teammates,
    @Column(type = byte[].class, comment = "助战队友详细信息")
    teammatesDetail,
    @Column(type = byte[].class, comment = "助战队友战斗数据信息")
    teammatesBattleRole,
    @Column(type=long.class, comment="使用的队友", defaults = "0")
    useTeammate,
    @Column(type = byte[].class, comment = "技能延时")
    skillDelay,
    @Column(type = byte[].class, comment = "站位信息")
    battlePosList,
    @Column(type=int.class, comment="策略Id", defaults = "1")
    strategyId,
    @Column(type = byte[].class, comment = "通关信息")
    throughLevelInfo,
    @Column(type=long.class, comment="上次重置的周一0点", defaults = "0")
    lastResetTime,
    @Column(type = byte[].class, comment = "双人本助战申请设置")
    settingList,
    @Column(type=byte[].class, comment="申请列表")
    applyList,
    @Column(type=int.class, comment = "助战次数", defaults = "0")
    helpTimes,
    @Column(type = byte[].class, comment = "被我助战玩家数据")
    helpHumanData,
    @Column(type=long.class, comment="双人本助战次数下次重置时间", defaults = "0")
    chapterHelpResetTime,
    @Column(type=String.class, comment = "扩展属性(用于处理bug)", length = 200, defaults = "")
    extendJSON,
}
