package org.gof.demo.worldsrv.entity;

import org.gof.core.gen.entity.Column;
import org.gof.core.gen.entity.Entity;

@Entity(entityName="PocketLine", tableName="demo_pocket_line", listKey = "humanId")
public enum EntityPocketLine {
	@Column(type=long.class, comment="玩家Id", index=true)
	humanId,
	@Column(type=String.class, comment="模块名称", defaults = "")
	moduleName,
	@Column(type=String.class, comment="待办事项", length=1000, defaults = "")
	param,
	@Column(type=long.class, comment="待办创建时间")
	timeCreate,
	@Column(type=String.class, comment = "扩展属性(用于处理bug)", length = 200, defaults = "")
	extendJSON,
}
