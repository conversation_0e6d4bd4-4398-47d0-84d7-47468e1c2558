package org.gof.demo.worldsrv.entity;

import org.gof.core.gen.entity.Column;
import org.gof.core.gen.entity.Entity;

@Entity(entityName="Wing", tableName="demo_wing")
public enum EntityWing {
	@Column(type=String.class, comment="羽毛sn等级经验Map", length=512, defaults="{}")
	featherMap,
	@Column(type=String.class, comment="背饰皮肤sn等级Map", length=1024, defaults="{}")
	skinLvMap,
	@Column(type=String.class, comment="背饰sn等级Map,加方案弃用", length=1024, defaults="0")
	talentLvMap,
	@Column(type = int.class, comment = "当前方案", defaults="0")
	currentTab,
	@Column(type = byte[].class, comment = "天赋方案")
	talentTabInfo,
	@Column(type=String.class, comment="激活的图鉴sn", defaults="")
	collectionList,
	@Column(type=String.class, comment = "扩展属性(用于处理bug)", length = 200, defaults = "")
	extendJSON,
	;
}
