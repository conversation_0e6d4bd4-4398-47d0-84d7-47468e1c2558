package org.gof.demo.worldsrv.entity;

import org.gof.core.gen.entity.Column;
import org.gof.core.gen.entity.Entity;

@Entity(entityName="Equip", tableName="demo_equip", listKey = "humanId", autoCache = true)
public enum EntityEquip {
	@Column(type=long.class, comment = "玩家id", index=true)
	humanId,
	@Column(type=int.class, comment="方案索引", index=true)
	tab,
	@Column(type=String.class, comment="方案名字", defaults = "")
	tabName,
	@Column(type=String.class, comment="部位信息1", defaults = "{}")
	part1,
	@Column(type=String.class, comment="部位信息2", defaults = "{}")
	part2,
	@Column(type=String.class, comment="部位信息3", defaults = "{}")
	part3,
	@Column(type=String.class, comment="部位信息4", defaults = "{}")
	part4,
	@Column(type=String.class, comment="部位信息5", defaults = "{}")
	part5,
	@Column(type=String.class, comment="部位信息6", defaults = "{}")
	part6,
	@Column(type=String.class, comment="部位信息7", defaults = "{}")
	part7,
	@Column(type=String.class, comment="部位信息8", defaults = "{}")
	part8,
	@Column(type=String.class, comment="部位信息9", defaults = "{}")
	part9,
	@Column(type=String.class, comment="部位信息10", defaults = "{}")
	part10,
	@Column(type=String.class, comment = "扩展属性(用于处理bug)", length = 200, defaults = "")
	extendJSON,
	;
}
