package org.gof.demo.worldsrv.entity;

import org.gof.core.gen.entity.Column;
import org.gof.core.gen.entity.Entity;

@Entity(entityName="GuildMember", tableName="guild_member", listKey = "guildId")
public enum EntityGuildMember {
	@Column(type=long.class, comment="玩家id")
	humanId,
	@Column(type=String.class, comment="玩家名字", length = 64)
	name,
	@Column(type=long.class, comment="公会id")
	guildId,
	@Column(type=String.class, comment="头像信息", defaults = "", length=256)
	roleHead,
	@Column(type=int.class, comment="职业")
	career,
	@Column(type=int.class, comment="等级")
	level,
	@Column(type=long.class, comment="玩家战力")
	power,
	@Column(type=int.class, comment="在线", defaults = "0")
	online,
	@Column(type=long.class, comment="离线时间", defaults = "0")
	offlineTime,
	@Column(type=long.class, comment="加入时间")
	joinTime,
	@Column(type=long.class, comment = "累计捐赠的总额")
	donateSum,
	@Column(type=long.class, comment="本周捐赠的总额")
	donateWeek,
	@Column(type=long.class, comment="本周捐赠时间")
	donateWeekTime,
	@Column(type=int.class, comment="捐赠的次数")
	donateCount,
	@Column(type=long.class, comment="boss最高伤害")
	bossMaxHurt,

	@Column(type=long.class, comment="胖头鱼报名时间")
	gveAppTime,
	@Column(type=long.class, comment="胖头鱼伤害")
	gveHurt,
	@Column(type=long.class, comment="胖头鱼伤害对应时间")
	gveHurtUpdateTime,
	@Column(type=long.class, comment="胖头鱼发奖励时间")
	sendGveTime,
	@Column(type=String.class, comment = "扩展属性(用于处理bug)", length = 256, defaults = "")
	extendJSON,
	;
}
