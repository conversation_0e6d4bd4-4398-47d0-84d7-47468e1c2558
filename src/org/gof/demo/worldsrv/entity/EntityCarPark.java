package org.gof.demo.worldsrv.entity;

import org.gof.core.gen.entity.Column;
import org.gof.core.gen.entity.Entity;

@Entity(entityName="CarPark", tableName="demo_car_park", autoCache = true)
public enum EntityCarPark {
    @Column(type=String.class, comment="车库名字", defaults = "")
    name,
    @Column(type=int.class, comment="展示的车", defaults = "0")
    showCar,
    @Column(type=String.class, comment="坐骑信息Map", length = 2048, defaults = "{}")
    mountMap,
    @Column(type=String.class, comment="车位1", length = 1024, defaults = "{}")
    space1,
    @Column(type=String.class, comment="车位2", length = 1024, defaults = "{}")
    space2,
    @Column(type=String.class, comment="车位3", length = 1024, defaults = "{}")
    space3,
    @Column(type=String.class, comment="车位3", length = 1024, defaults = "{}")
    space4,
    @Column(type=String.class, comment="拥有的皮肤sn对应等级", length = 1024, defaults = "{}")
    skinLvMap,
    @Column(type=String.class, comment="穿戴的type对应sn信息", length = 512, defaults = "{}")
    skinTypeSnMap,
    @Column(type=String.class, comment="穿戴的装饰sn对应vo信息", length = 1024, defaults = "{}")
    skinDecSnVoMap,
    @Column(type=int.class, comment="保护是否开启", defaults = "0")
    isProtect,
    @Column(type=int.class, comment="保护条件", defaults = "0")
    protectType,
    @Column(type=int.class, comment="保护比例", defaults = "0")
    protectRatio,
    @Column(type=String.class, comment="List<Integer>(数量，时间)Map", defaults = "")
    defBuff,
    @Column(type=String.class, comment="List<Integer>(数量，时间)Map", defaults = "")
    atkBuff,
    @Column(type=String.class, comment="List<Integer>(数量，时间)Map", defaults = "")
    crossDefbuff,
    @Column(type=String.class, comment="List<Integer>(数量，时间)Map", defaults = "")
    crossAtkbuff,
    @Column(type=long.class, comment="驻守中的车库id，用来校验", defaults = "0")
    guardCrossId,
    @Column(type=long.class, comment="跨服驻守hp", defaults = "0")
    guardCrossHp,
    @Column(type=long.class, comment="抢夺的中的车库id，用来校验", defaults = "0")
    robCrossId,
    @Column(type=long.class, comment="跨服抢夺hp", defaults = "0")
    robCrossHp,
    @Column(type=long.class, comment="每日重置时间", defaults = "0")
    dailyResetTime,
    @Column(type=int.class, comment="车库收益", defaults = "0")
    income,
    @Column(type=String.class, comment="车位收益加成", defaults = "{}")
    rewardAddMap,
    @Column(type=String.class, comment = "扩展属性(用于处理bug)", length = 200, defaults = "")
    extendJSON,
}
