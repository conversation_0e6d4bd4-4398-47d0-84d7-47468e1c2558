package org.gof.demo.worldsrv.instance;

import org.gof.core.Port;
import org.gof.core.scheduler.ScheduleTask;
import org.gof.core.support.Param;
import org.gof.core.support.S;
import org.gof.core.support.TickTimer;
import org.gof.core.support.Time;
import org.gof.demo.battlesrv.stageObj.StageRandomUtils;
import org.gof.demo.battlesrv.stageObj.UnitObject;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.produce.ProduceVo;
import org.gof.demo.worldsrv.stage.InstanceStatistics;
import org.gof.demo.worldsrv.stage.StageObject;
import org.gof.demo.worldsrv.stage.StagePort;
import org.gof.demo.worldsrv.support.D;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.observer.Event;
import org.gof.demo.worldsrv.support.observer.EventBridgeKey;
import org.gof.demo.worldsrv.support.observer.EventKey;

import java.util.*;

public class StageObjectInstance extends StageObject {

    public boolean start = false;
    private long createdAt;                //副本创建时间
    public boolean destroy;            //是否正在摧毁
    public boolean isPass;            //是否已经通过
    public int repSn;
    public long enterTime = 0;       // 进入副本时间

    //家族副本的家族id
    public long factionId = 0;
    private int instanceType = 0;
    // 宗门转正boss血量
    private long bossHpTemp = 0;
    // 宗门转正bossId
    public long factionBossId = 0;
    // 定时同步转正boss血量
    private TickTimer factionUpTT = new TickTimer(Time.MIN);
    private TickTimer factionHPTT = new TickTimer(Time.HOUR);

    // 宗门遇袭bossId
    private long factionAttackBossId = 0L;

    //世界boss的入口npcId
    private int npcId = 0;

    //组队副本的队伍id
    private long teamId = 0;
    //伙伴试炼副本类型

    //副本无人后的销毁时间{普通副本}
    public static long[] DESTROY_TIMES = new long[]{10 * Time.SEC};

    //副本没人后的销毁时间
    public long destroyTime = 0;

    //掉落奖励缓存(每个人都有份)
    public List<ProduceVo> drop = new LinkedList<ProduceVo>();

    //伤害统计模块

    public Map<Long, InstanceStatistics> statisticsMap = new HashMap<>();
    //每秒同步副本内玩家伤害等数据
    public TickTimer syncStatisticTickTimer = new TickTimer(1000);

    // 200ms监测一次副本人数变化
    public TickTimer humanCountTickTimer = new TickTimer(1000);

    //每秒检测循环事件
    public TickTimer loopPlots = new TickTimer(Time.SEC);

    // 定时检测残留副本
    public TickTimer timingCheckTT = new TickTimer(90 * Time.MIN);

    private List<Integer> lvList = new ArrayList<>();

    public List<Integer> getLvList() {
        return lvList;
    }

    /**
     * 动态复活点
     */
    public RevivePoint dynamicRevivePoint;

    public StageObjectInstance(StagePort port, long stageId, int mapSn, int repSn, Param param) {
        this(port, stageId, mapSn, repSn, param, false);
    }

    public StageObjectInstance(StagePort port, long stageId, int mapSn, int repSn, Param param, boolean isAssist) {
        super(port, stageId, mapSn);

        this.repSn = repSn;
        this.isAssist = isAssist;
        createdAt = this.getTime();
        //副本就一个大格子

        this.cellWidth = this.width;
        this.cellHeight = this.height;

        this.randUtils = new StageRandomUtils(100);

        // 设置进入时间
        enterTime = Port.getTime();
        destroyTime = DESTROY_TIMES[0];

        //初始化副本剧情


    }


    @Override
    public void startup() {
        //默认出现
        super.startup();
    }

    @Override
    public void pulse() {
        if (!start) {
            return;
        }

        super.pulse();

        long curr = this.getTime();
        //如果一定时间内副本没人 删除(家族副本保持到时间结束以保障家族副本进度)
        if ((curr - this.createdAt > destroyTime) || super.isEnd()) {
            if (this.getHumanObjs().isEmpty()) {
                this.destory();
            }
        }
    }





    @Override
    public void destory() {
        if (this.destroy) return;
        //删除副本地图
        super.destory();

        //删除家族的记录
        if (factionId > 0) {
            Log.temp.error("===销毁副本，factionId={}, instanceType={}", factionId, instanceType);
        }

        this.destroy = true;

    }

    /**
     * 发送副本玩家的伤害等最新数据
     */
    public void sendInstanceStatistics() {
//        MsgInstance.SCInstanceStatistics.Builder statisticsMsg = MsgInstance.SCInstanceStatistics.newBuilder();
//
//        for (Long id : getHumanObjs().keySet()) {
//            HumanObject ho = getHumanObjs().get(id);
//            if (ho == null) {
//                continue;
//            }
//            InstanceStatistics statics = statisticsMap.get(ho.id);
//            if (statics == null) {
//                continue;
//            }
//            Define.InstanceStatisticsInfo info = statics.toMsg(createdAt);
//            info = info.toBuilder().setAffiliationId(id).build();
//            statisticsMsg.addInfo(info);
//        }
//
//        for (PartnerObject partnerObj : getPartnerObjs().values()) {
//            if (partnerObj == null) {
//                continue;
//            }
//            InstanceStatistics statics = statisticsMap.get(partnerObj.id);
//            if (statics == null) {
//                continue;
//            }
//            Define.InstanceStatisticsInfo info = statics.toMsg(createdAt);
//            info = info.toBuilder().setAffiliationId(partnerObj.parentObjectId).build();
//            statisticsMsg.addInfo(info);
//        }
//
//        for (HumanObject ho : getHumanObjs().values()) {
//            ho.sendMsg(statisticsMsg);
//        }
    }

    public void repPass() {
    }

	public void repPass(HumanObject humanObj){

	}

	public void timeout(){
		pauseAllUnitObjs();
	}

    @Override
    public void onUnitObjDie(UnitObject killer, UnitObject unitObject) {

        Event.fire(EventKey.SCENE_TRIGGER_14, "unitObject", unitObject, "killer", killer);
		if (S.isBridge) {
			Event.fire(EventBridgeKey.BRIDGE_SCENE_TRIGGER_14,"unitObject", unitObject,"killer",killer);
		}

        //黄俊峰 - 所有副本死亡后如果没有复活类型为空，就直接退出
        if (!unitObject.isHumanObj()) {
            return;
        }

        HumanObject humanObj = (HumanObject) unitObject;
        Port.getCurrent().getServices(D.SERV_STAGE_DEFAULT).scheduleOnce(new ScheduleTask() {
            @Override
            public void execute() {
//                StageManager.inst().quitToCommon(humanObj);//倒计时延时interval毫秒后发送
            }
        }, 5 * Time.SEC);
    }


    @Override
    public void onClientEnterEnd(HumanObject humanObj) {
        super.onClientEnterEnd(humanObj);
	}

    @Override
    public void onMonsterDie(int stageObjectSn) {
        super.onMonsterDie(stageObjectSn);
        if (this.isEnd()) {
            return;
        }

    }


}
