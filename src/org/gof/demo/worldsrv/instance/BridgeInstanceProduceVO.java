package org.gof.demo.worldsrv.instance;

import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.interfaces.ISerilizable;

import java.io.IOException;

public class BridgeInstanceProduceVO implements ISerilizable {
    public BridgeInstanceProduceVO() {}

    public int aidNum;
    public String everyDayAidNum;

    @Override
    public void writeTo(OutputStream out) throws IOException {
        out.write(aidNum);
        out.write(everyDayAidNum);
    }

    @Override
    public void readFrom(InputStream in) throws IOException {
        aidNum = in.read();
        everyDayAidNum = in.read();
    }
}
