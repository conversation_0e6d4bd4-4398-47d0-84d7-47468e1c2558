package org.gof.demo.worldsrv.instance;

import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.interfaces.ISerilizable;

import java.io.IOException;

public class BridgeInstanceConsumeVO implements ISerilizable {
    public int repSn;
    public int vitality;
    public boolean passNewerRep;



    @Override
    public void writeTo(OutputStream out) throws IOException {
        out.write(repSn);
        out.write(vitality);
        out.write(passNewerRep);
    }

    @Override
    public void readFrom(InputStream in) throws IOException {
        repSn = in.read();
        vitality = in.read();
        passNewerRep = in.read();
    }
}
