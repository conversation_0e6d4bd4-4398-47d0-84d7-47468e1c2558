//package org.gof.demo.worldsrv.instance.treasure;
//
//import org.apache.commons.lang3.StringUtils;
//import org.gof.core.Port;
//import org.gof.core.scheduler.ScheduleTask;
//import org.gof.core.support.Config;
//import org.gof.core.support.ParamKey;
//import org.gof.core.support.Utils;
//import org.gof.demo.battlesrv.stageObj.WorldObject;
//import org.gof.demo.battlesrv.support.Vector2D;
//import org.gof.demo.battlesrv.support.Vector3D;
//import org.gof.demo.worldsrv.achieve.AchieveTypeKey;
//import org.gof.demo.worldsrv.activityList.CalendarManager;
//import org.gof.demo.worldsrv.character.HumanObject;
//import org.gof.demo.worldsrv.character.MonsterObject;
//import org.gof.demo.worldsrv.college.CollegeManager;
//import org.gof.demo.worldsrv.config.ConfItemData;
//import org.gof.demo.worldsrv.config.ConfItemMake;
//import org.gof.demo.worldsrv.config.ConfSystemBroadcast;
//import org.gof.demo.worldsrv.faction.martial.FactionMartailKey;
//import org.gof.demo.worldsrv.faction.martial.FactionMartialManager;
//import org.gof.demo.worldsrv.gather.GatherObject;
//import org.gof.demo.worldsrv.global.GlobalConfVal;
//import org.gof.demo.worldsrv.inform.BroadcastKey;
//import org.gof.demo.worldsrv.inform.Inform;
//import org.gof.demo.worldsrv.instance.InstanceManager;
//import org.gof.demo.worldsrv.instance.StageObjectInstance;
//import org.gof.demo.worldsrv.item.ItemBagManager;
//import org.gof.demo.worldsrv.item.ItemChange;
//import org.gof.demo.worldsrv.item.ItemVO;
//import org.gof.demo.worldsrv.msg.Define;
//import org.gof.demo.worldsrv.msg.MsgTreasure;
//import org.gof.demo.worldsrv.produce.ProduceManager;
//import org.gof.demo.worldsrv.produce.ProduceVo;
//import org.gof.demo.worldsrv.produce.RandomUtil;
//import org.gof.demo.worldsrv.rewarditem.RewardHelper;
//import org.gof.demo.worldsrv.stage.StageManager;
//import org.gof.demo.worldsrv.stage.StagePort;
//import org.gof.demo.worldsrv.support.*;
//import org.gof.demo.worldsrv.support.enumKey.ConfGlobalKey;
//import org.gof.demo.worldsrv.support.enumKey.MoneyAddLogKey;
//import org.gof.demo.worldsrv.support.enumKey.MoneyReduceLogKey;
//import org.gof.demo.worldsrv.support.observer.Event;
//import org.gof.demo.worldsrv.support.observer.EventKey;
//import org.gof.demo.worldsrv.support.pathFinding.HeightFinding;
//import org.gof.demo.worldsrv.support.pathFinding.PathFinding;
//import org.gof.demo.worldsrv.treasureMap.TreasureMapServiceProxy;
//
//import java.util.*;
//
//public class StageTreasureRandomInstance extends StageObjectInstance {
//    public static final int TREASURE_MODEL_CHEST = 0;
//    public static final int TREASURE_MODEL_MONSTER = 1;
//    public static final int TREASURE_MODEL_GATHER = 2;
//
//    public int batch = 0;
//    public int allNum = 0;
//    public int randomType = -1;//模式0宝箱1杀怪2灵气
//    public long finderHumanId;//宝箱模式发现者ID roll + 2点
//    public int animaNum;//灵气模式采集灵气数
//    public Vector2D vector2D;
//    public boolean init = false;
//    public Map<Long, WorldObject> worldObjectsMap = new HashMap<>();
//    public long treasureMapId;
//    //灵气模式下 每个人采集的灵气数量
//    public Map<Long, Integer> humanGatherAnimaNum = new HashMap<>();
//
//    /**
//     * TODO
//     *@date 2021/6/16 14:22
//     *@param port
//     *@param stageId
//     *@param mapSn
//     *@param repSn
//     *@param num
//     *@param treasureMapId 队伍ID
//     *@return
//    */
//    public StageTreasureRandomInstance(StagePort port, long stageId, int mapSn, int repSn,int num,long treasureMapId) {
//        super(port, stageId, mapSn, repSn, null);
//        allNum = num;
//        String minePos = confRep.minePos;
//        this.treasureMapId = treasureMapId;
//        //获得出生点
//        String[] vectorString = new String[0];
//        if (!StringUtils.isEmpty(minePos)){
//            vectorString = minePos.split("\\|");
//        }
//        int index = RandomUtil.random(vectorString.length);
//        String[] vector = vectorString[index].split(",");
//        vector2D = new Vector2D(Double.parseDouble(vector[0]),Double.parseDouble(vector[2]));
//        createGather();
//    }
//
//    public void createGather(){
//        int randomRange = ConfGlobalUtils.getValue(ConfGlobalKey.探宝随机范围);
//        Vector2D vecTemp = getVectir2d(randomRange);
//        int digSn = ConfGlobalUtils.getValue(ConfGlobalKey.探宝挖掘点);
//        GatherObject gather = new GatherObject(this,digSn,String.valueOf(digSn));
//        gather.setPosNow(vecTemp);
//        gather.setDirNow(vector2D);
//        gather.startup();
//        worldObjectsMap.put(gather.id, gather);
//        batch++;
//
//        this.getHumanObjs().forEach((aLong, humanObject) -> {
//            humanObject.sendMsg(MsgTreasure.SCTreasureRollBatch.newBuilder().setBatch(batch).setNum(allNum).build());
//        });
//    }
//
//    public Vector2D getVectir2d(int randomRange){
//        Vector2D vectemp = vector2D;
//        try {
//            int range = ConfGlobalUtils.getValue(ConfGlobalKey.探宝生成点范围);
//            Vector3D start3D = HeightFinding.posHeight(this.confMap.sn, vectemp);
//            for (int i = 0 ; i < 10; i++){
//                Vector2D random2d = MathUtils.randomPosInCircle(vector2D, range, randomRange);
//                Vector3D end = HeightFinding.posHeight(this.confMap.sn, random2d);
//                Vector3D finish3D = PathFinding.raycast(this.confMap.sn, start3D, end);
////                if (random2d.x != finish3D.x || random2d.y != finish3D.y){
////                    Log.game.error("起始点：{}，终点：{}，阻挡点：{}", start3D, end, finish3D);
////                    continue;
////                }
////                if (!PathFinding.isPosInBlock(this.confMap.sn, end)){
////                    vectemp = end.toVector2D();
////                    break;
////                }
//                vectemp = end.toVector2D();
//            }
//        } catch (Exception e) {
//            Log.temp.error("===e={}", e);
//        }
//
//        return vectemp;
//    }
//    @Override
//    public void pulse() {
//        super.pulse();
//        if (isEnd()) {
//            return;
//        }
//        int remain = worldObjectsMap.size();
//        if (remain <= 0) {
//            try {
//                batchEndSendReward();
//            } catch (Exception e) {
//                Log.game.error("{}",e.getMessage());
//            }
//            finderHumanId = 0;
//            animaNum = 0;
//            int nextBatch = batch + 1;
//            if (nextBatch <= allNum) {
//                createGather();
//            } else {
//                //到这里说明副本结束了
//                if (confRep != null) {
//                    this.getHumanObjs().forEach((Long, humanObject) -> {
//                        Port.getCurrent().getServices(D.SERV_STAGE_DEFAULT).scheduleOnce(new ScheduleTask() {
//                            @Override
//                            public void execute() {
//                                InstanceManager.inst().leave(humanObject);
//                            }
//                        }, confRep.closeTime);
//                    });
//                }
//                TreasureMapServiceProxy prx = TreasureMapServiceProxy.newInstance();
//                prx.repEndRemoveTreasureMap(this.getTeamId());
//                this.end();
//            }
//        }
//    }
//
//    public void batchEndSendReward() {
//        Set<Integer> specialItemSnSet = getNeedBroadcastItemSnSet();
//        Collection<HumanObject> humanObjCollection = this.getHumanObjs().values();
//        // 构建玩家和掉落的映射
//        Map<Long, Integer> humanIdProduceSnMap = getHumanIdProduceSnMap(humanObjCollection);
//
//        for (HumanObject humanObj : humanObjCollection) {
//            long humanId = humanObj.getHumanId();
//            if (!humanIdProduceSnMap.containsKey(humanId)) {
//                continue;
//            }
//            MsgTreasure.SCTreasureReward.Builder msg = MsgTreasure.SCTreasureReward.newBuilder();
//            msg.setBatch(batch);
//            msg.setTimes(allNum);
//            msg.setHumanId(humanId);
//
//            int produceSn = humanIdProduceSnMap.get(humanId);
//            // 构造这个玩家的奖励
//            List<ProduceVo> pvoList = ProduceManager.inst().produceItem(0, 0, produceSn, 0);
//            // 遍历掉落转换为ItemVO并且确定哪些是需要播报的特殊奖励
//            List<Integer> specialRewardItemSnList = new ArrayList<>();
//            List<ItemVO> itemVOList = new ArrayList<>();
//            for (ProduceVo pvo : pvoList) {
//                itemVOList.add(new ItemVO(pvo.itemSn, pvo.num));
//                if (specialItemSnSet.contains(pvo.itemSn)) {
//                    specialRewardItemSnList.add(pvo.itemSn);
//                }
//                Define.DProduce.Builder dmsg = Define.DProduce.newBuilder();
//                dmsg.setSn(pvo.itemSn);
//                dmsg.setNum(pvo.num);
//                msg.addReward(dmsg);
//            }
//
//            RewardHelper.rewardFailSendMail(humanObj, itemVOList, null, MoneyAddLogKey.高级挖宝, true);
//            Event.fire(EventKey.HUMAN_ALL_BROADCAST, "type", BroadcastKey.BROADCAST_TYPE_3, "humanObj", humanObj, "itemVOList", itemVOList);
//            humanObj.sendMsg(msg);
//        }
//        humanGatherAnimaNum.clear();
//        for (HumanObject humanObj : humanObjCollection) {
//            // 更新学院每日任务成就
//            CollegeManager.inst().updateActivityCollegeTeam(humanObj, AchieveTypeKey.ACHIEVE_TYPE_11004, 1);
//            Event.fire(EventKey.UPDATE_ACHIEVE, "type", AchieveTypeKey.ACHIEVE_TYPE_5214, "humanObj", humanObj, "progress", 1);
//            Event.fire(EventKey.TREASURE,  "humanObj", humanObj, "progress", 1);
//        }
//    }
//
//    /**
//     * 创建玩家id和对应掉落sn的映射
//     */
//    public Map<Long, Integer> getHumanIdProduceSnMap(Collection<HumanObject> humanObjs) {
//        Map<Long, Integer> humanIdProduceSnMap = new HashMap<>();
//        if (randomType == TREASURE_MODEL_GATHER) {
//            // 采集模式，根据每个人采集的灵气数量决定每个人的宝箱品质
//            String[] produceSnStrs = ConfGlobalUtils.getStrArray(ConfGlobalKey.探宝灵气奖励);
//            int[] animaLevels = ConfGlobalUtils.getIntArray(ConfGlobalKey.探宝灵气奖励);
//            int[] produceSns = Arrays.asList(produceSnStrs).stream().mapToInt(Integer::parseInt).toArray();
//
//            for (HumanObject humanObj : humanObjs) {
//                int animaNum = humanGatherAnimaNum.getOrDefault(humanObj.getHumanId(), 0);
//                for (int i = animaLevels.length - 1; i >= 0; i--) {
//                    if (animaNum >= animaLevels[i]) {
//                        humanIdProduceSnMap.put(humanObj.getHumanId(), produceSns[i]);
//                        break;
//                    }
//                }
//            }
//        } else {
//            ConfGlobalKey key = (randomType == 1) ? ConfGlobalKey.探宝杀怪奖励 : ConfGlobalKey.探宝宝箱奖励;
//            String[] produceSns = ConfGlobalUtils.getStrArray(key);
//            int[] rates = ConfGlobalUtils.getIntArray(key);
//            for (HumanObject humanObj : humanObjs) {
//                int index = RandomUtil.random(rates);
//                int produceSn = Integer.parseInt(produceSns[index]);
//                humanIdProduceSnMap.put(humanObj.getHumanId(), produceSn);
//            }
//        }
//        return humanIdProduceSnMap;
//    }
//
//    public void gatherDigTrigger(HumanObject humanObj,long finderHumanId, Vector2D posNow, Vector2D dirNow){
//        this.finderHumanId = finderHumanId;
//        this.worldObjectsMap.clear();
//        int[] intArray = ConfGlobalUtils.getIntArray(ConfGlobalKey.探宝模式);
//
//        // 探宝道具
//        Integer consumeItemSn = ConfGlobalUtils.getValue(ConfGlobalKey.探宝道具ID);
//        this.getHumanObjs().forEach((aLong, humanObject) -> {
//            RewardHelper.checkAndConsume(humanObject,consumeItemSn,1, MoneyReduceLogKey.探宝副本);
//        });
//
//
//        FactionMartialManager.inst().assistPropDropRate(humanObj, FactionMartailKey.考古知识,intArray);
//        int index = Utils.getRandRange(intArray);
//
//        int randomRange = ConfGlobalUtils.getValue(ConfGlobalKey.探宝随机范围);
//        int infoSn = 0;
//        if (index == TREASURE_MODEL_CHEST) {//宝箱模式
//            int gatherSn = ConfGlobalUtils.getValue(ConfGlobalKey.探宝宝箱ID);
//            GatherObject gather = new GatherObject(this,gatherSn,String.valueOf(gatherSn));
//            gather.setPosNow(posNow);
//            gather.setDirNow(dirNow);
//            worldObjectsMap.put(gather.id,gather);
//            infoSn = 10201;
//        } else if (index == TREASURE_MODEL_MONSTER) {//怪物模式
//            int monsterSn = ConfGlobalUtils.getValue(ConfGlobalKey.探宝怪物ID);
//            MonsterObject mo =  StageManager.inst().refreshMonster(monsterSn, 0, this, posNow, dirNow, true);
//            worldObjectsMap.put(mo.id,mo);
//            infoSn = 10202;
//        } else if (index == TREASURE_MODEL_GATHER) {//灵气模式
//            int gasSn = ConfGlobalUtils.getValue(ConfGlobalKey.探宝灵气ID);
//            int gasNum = ConfGlobalUtils.getIntArray(ConfGlobalKey.探宝灵气ID)[0];
//            for (int i = 0; i<gasNum; i++) {
//                GatherObject gather = new GatherObject(this, gasSn, String.valueOf(gasSn));
//                Vector2D vecTemp = getVectir2d(randomRange);
//                gather.setPosNow(vecTemp);
//                gather.setDirNow(vector2D);
//                worldObjectsMap.put(gather.id,gather);
//            }
//            infoSn = 10203;
//        } else {
//            return;
//        }
//        worldObjectsMap.values().stream().forEach(object -> object.startup());
//        randomType = index;
//        for (Long humanId : this.getHumanObjs().keySet()){
//            Inform.sendInform(humanId, infoSn);
//        }
//    }
//
//    public void worldObjectsDie(Long humanId, long gatherId, int worldObjectSn){
//        if (this.finderHumanId == 0L) {
//            this.finderHumanId = humanId;
//        }
//        WorldObject worldObject = this.worldObjectsMap.get(gatherId);
//        if (this.worldObjectsMap.containsKey(gatherId)){
//            this.worldObjectsMap.remove(gatherId);
//        }
//        int gasSn = ConfGlobalUtils.getValue(ConfGlobalKey.探宝灵气ID);
//        if (gasSn == worldObjectSn) {
//            this.animaNum ++;
//            //记录玩家采集了多少
//            int num = 1;
//            if (humanGatherAnimaNum.containsKey(humanId)){
//                num = humanGatherAnimaNum.get(humanId) + 1;
//            }
//            humanGatherAnimaNum.put(humanId,num);
//            GatherObject gatherObject = (GatherObject)worldObject;
//            int time = (int) (gatherObject.getSurplusAliveTime() / 1000L);
//            if (worldObjectsMap.size() != 0) {
//                this.getHumanObjs().forEach((aLong, humanObject) -> {
//                    Inform.sendInform(aLong, 10204, this.worldObjectsMap.size(), time);
//                });
//            }
//        }
//    }
//
//    /**
//     * 获取需要播报的道具
//     */
//    private Set<Integer> getNeedBroadcastItemSnSet() {
//        Set<Integer> specialItemSnSet = new HashSet<>();
//        List<ConfSystemBroadcast> confSystemBroadcastList = GlobalConfVal.getSystemBroadcastList(BroadcastKey.BROADCAST_TYPE_3);
//        for (ConfSystemBroadcast conf : confSystemBroadcastList) {
//            specialItemSnSet.addAll(Utils.intToIntegerList(conf.parameter1));
//        }
//        return specialItemSnSet;
//    }
//}
