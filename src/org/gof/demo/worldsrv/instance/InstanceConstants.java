package org.gof.demo.worldsrv.instance;

/**
 * 副本常量
 * <AUTHOR>
 * @Date 2024/3/25
 * @Param
 */
public interface InstanceConstants {
    int INSTANCE_TYPE_INVALID = -1;// 非法副本
    int CHAPTER_1 = 1;//  主线副本chapter表
    int COINCHAPTER_2 = 2;// 突袭神灯小偷CoinChapter表
    int DIAMONDCHAPTER_3 = 3;// 挑战冰巢龙穴DiamondChapter表
    int LEGACY_CHAPTER_4 = 4;// 揭秘时间尽头legacy_chapter表
    int PVPCHAPTER_5 = 5;// pvp副本PvpChapter表
    int LEAGUESOLOCHAPTER_6 = 6;// 征战熔岩巨兽LeagueSoloChapter表
    int LEAGUEGVECHAPTER_7 = 7;// 讨伐郁郁胖头鱼LeagueGveChapter表
    int LEGACYTEAMCHAPTER_8 = 8;// 守卫残垣古城LegacyTeamChapter表(组队)
    int MOUNTCHAPTER_9 = 9;// 颠倒时序塔MountChapter表
    int FARMPVPCHAPTER_10 = 10;// 庄园内pvp副本FarmPvpChapter表
    int FATECHAPTER_11 = 11;// 命格副本FateChapter表
    int PKCHAPTER_12 = 12;// 切磋PkChapter表
    int WORLDBOSS_13 = 13;// 穿越深渊之门WorldBoss表
    int FAMILYBRAWLCHAPTER_14 = 14;// 家族乱斗FamilyBrawlChapter表
    int CROSSPVPCHAPTER_15 = 15;// 跨服排位赛CrossPvpChapter表
    int STRATEGYACTIVITYCHAPTER_16 = 16;// 菇生模拟器StrategyActivityChapter表
    int KFWARCHAPTER_17 = 17;// 跨服玩家战斗KfWarChapter表
    int KFWARCHAPTERMONSTER_18 = 18;// 跨服怪物战斗KfWarChapterMonster表
    int PARKPVPCHAPTER_19 = 19;// 车位PVP副本ParkPvpChapter表
    int HALLOWEENPVP_20 = 20;// 万圣节PVP副本HalloweenPvp表
    int ARTIFACTPREVIEWCHAPTER_21 = 21;// 神器预览副本ArtifactPreviewChapter表
    int ARTIFACTGEMCHAPTER_22 = 22;// 探秘焚焰神殿ArtifactGemChapter表
    int SEVENTRIALCHAPTER_23 = 23;// 恶魔入侵SevenTrialChapter表
    int CAPTURESLAVE_25 = 25;// 抓仆人CaptureSlave表
    int PARKCROSSPVPCHAPTER_26 = 26;// 跨服车位PVP副本ParkCrossPvpChapter表
    int REVERSIONWARCHAPTER_27 = 27;// 逆转之战ReversionWarChapter表
    int DARKTRIALCHAPTER_28 = 28;// 暗黑试炼-战士DarkTrialChapter表
    int DARKTRIALCHAPTER_29 = 29;// 暗黑试炼-弓手DarkTrialChapter表
    int DARKTRIALCHAPTER_30 = 30;// 暗黑试炼-法师DarkTrialChapter表
    int SEASONPVPCHAPTER_31 = 31;// 航海pvp战斗SeasonPvpChapter表
    int DOUBLELADDERCHAPTERCHAPTER_32 = 32;// 双人本DoubleLadderChapter表
    int KUNGFURACE_CHAPTER_33 = 33;// 武道会玩家战斗KfWarChapter表
    int ACTIVITY_CHAPTER_SLIME_120 = 120;// 史莱姆活动进入副本对应活动类型4300

    int darkTrialChapterType_1 = 1;// 暗黑试炼-战士
    int darkTrialChapterType_2 = 2;// 暗黑试炼-弓手DarkTrialChapter表
    int darkTrialChapterType_3 = 3;// 暗黑试炼-法师DarkTrialChapter表

    int dungeonType_1 = 1; // 时间
    int dungeonType_2 = 2; // 本次伤害
    int dungeonType_3 = 3; // 3是历史最高伤害
    int dungeonType_4 = 4;  // 23副本类型时值为排行榜
    int dungeonType_5 = 5;  //待定开服天数/工会boss等级/23副本类型时值为副本等级
    int dungeonType_6 = 6; // 历史最高难度（武魂副本里面使用该值代表是否可领每日奖励）
    int dungeonType_7 = 7;  // 23副本类型时值为本次积分
    int dungeonType_8 = 8;  // 23副本类型时值为总积分
    int dungeonType_9 = 9; // 扫荡等级
    int dungeonType_10 = 10;// 允许获得奖励
    int dungeonType_11 = 11;// 重置次数
    int dungeonType_12 = 12;// 是否开放
    int dungeonType_14 = 14;// 通关最高难度, 十点防御通关的最高难度


    int showType_0 = 0;// 普通奖励显示，如邮件
    int showType_5001 = 5001;// 奖励显示（神灯小偷副本结算、冰巢龙穴副本结算、黑暗试炼快速挑战结算）
    int showType_5015 = 5015;// 奖励显示（黑暗试炼快速挑战结算）
    int showType_1001008 = 1001008;// 折扣礼包使用
    int showType_1001007 = 1001007;// 挑战券
    int showType_1001009 = 1001009;// 停车场掠夺胜利
    int showType_1001010 = 1001010;// 停车场自动收车
    int showType_1001011 = 1001011;// 空投翻倍奖励

    int rewardType_1 = 1; // 1 加成波次奖励
    int rewardType_2 = 2;  // 2 援助奖励

    int lose = 1; // 失败
    int win = 0;	// 胜利

    int familyBrawlChapterSn = 140001; // 工会乱斗副本sn
    int parkChapterSn = 100001; // 车位副本sn

}

