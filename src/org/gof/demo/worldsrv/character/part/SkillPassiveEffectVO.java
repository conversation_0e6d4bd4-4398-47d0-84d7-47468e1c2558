package org.gof.demo.worldsrv.character.part;

import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.interfaces.ISerilizable;
import org.gof.demo.worldsrv.enumType.ESkillPassiveType;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 被动效果信息
 */
public class SkillPassiveEffectVO implements ISerilizable {

	// 类型对应记录时间秒， （ESkillPassiveType类型值，对应时间）
	private Map<Integer, Integer> typeSecMap = new HashMap<>();
	// 类型对应是否开启此类型功能（ESkillPassiveType类型值，对应是否开启）
	private Map<Integer, Boolean> typeOpenMap = new HashMap<>();

	/**
	 * 获取对应被动效果类型的值
	 * <AUTHOR>
	 * @Date 2021/9/6
	 * @Param
	 */
	public int getValue(ESkillPassiveType eSkillPassiveType) {
		int value = 0;
		if(typeSecMap.containsKey(eSkillPassiveType.value())){
			value = typeSecMap.get(eSkillPassiveType.value());
		}
		return value;
	}
	
	/** 
	 * 设置对应被动效果类型的值 
	 * <AUTHOR>
	 * @Date 2021/9/6
	 * @Param 
	 */
	public void setValue(ESkillPassiveType eSkillPassiveType, int value){
		typeSecMap.put(eSkillPassiveType.value(), value);
	}

	/** 
	 * 获取被动效果类型是否开启状态 
	 * <AUTHOR>
	 * @Date 2021/9/6
	 * @Param 
	 */
	public boolean isOpen(ESkillPassiveType eSkillPassiveType) {
		boolean isOpen = false;
		if(typeOpenMap.containsKey(eSkillPassiveType.value())){
			isOpen = typeOpenMap.get(eSkillPassiveType.value());
		}
		return isOpen;
	}

	/**
	 * 设置对应枚举类型状态是否开启
	 * <AUTHOR>
	 * @Date 2021/9/6
	 * @Param
	 */
	public void setOpen(ESkillPassiveType eSkillPassiveType, boolean isOpen) {
		typeOpenMap.put(eSkillPassiveType.value(), isOpen);
	}

	/** 
	 * 枚举类型对应值自增涨+1
	 * <AUTHOR>
	 * @Date 2021/9/6
	 * @Param
	 */
	public void addValuePlus(ESkillPassiveType eSkillPassiveType){
		int type = eSkillPassiveType.value();
		int value = 0;
		if(typeSecMap.containsKey(type)){
			value = typeSecMap.get(type);
		}
		if(value < Integer.MAX_VALUE){
			value ++;
		}
		typeSecMap.put(type, value);
	}


	@Override
	public void writeTo(OutputStream out) throws IOException {
		out.write(typeSecMap);
		out.write(typeOpenMap);
	}

	@Override
	public void readFrom(InputStream in) throws IOException {
		typeSecMap = in.read();
		typeOpenMap = in.read();
	}

}
