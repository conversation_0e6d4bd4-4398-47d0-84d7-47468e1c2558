//package org.gof.demo.worldsrv.character;
//
//import org.gof.core.InputStream;
//import org.gof.core.OutputStream;
//import org.gof.core.Port;
//import org.gof.core.support.TickTimer;
//import org.gof.core.support.Time;
//import org.gof.core.support.Utils;
//import org.gof.demo.battlesrv.ai.fighter.PartnerFighter;
//import org.gof.demo.battlesrv.manager.CombatChangeLog;
//import org.gof.demo.battlesrv.manager.PropManager;
//import org.gof.demo.battlesrv.sk.SkillBook;
//import org.gof.demo.battlesrv.sk.manager.BuffManager;
//import org.gof.demo.battlesrv.sk.manager.SkillManager;
//import org.gof.demo.battlesrv.stageObj.CharacterObject;
//import org.gof.demo.battlesrv.stageObj.UnitObject;
//import org.gof.demo.battlesrv.support.PropCalc;
//import org.gof.demo.worldsrv.camp.CampTypeKey;
//import org.gof.demo.worldsrv.character.part.RagePart;
//import org.gof.demo.worldsrv.competition.StageObjectCompetition;
//import org.gof.demo.worldsrv.config.ConfBuff;
//import org.gof.demo.worldsrv.config.ConfCharacterModel;
//import org.gof.demo.worldsrv.config.ConfGlobal;
//import org.gof.demo.worldsrv.config.ConfPartner;
//import org.gof.demo.worldsrv.entity.Partner;
//import org.gof.demo.worldsrv.entity.UnitPropPlus;
//import org.gof.demo.worldsrv.general.PartnerManager;
//import org.gof.demo.worldsrv.msg.Define;
//import org.gof.demo.worldsrv.msg.MsgStage;
//import org.gof.demo.worldsrv.stage.StageManager;
//import org.gof.demo.worldsrv.stage.StageObject;
//
//import org.gof.demo.worldsrv.support.Log;
//import org.gof.demo.worldsrv.support.enumKey.ConfGlobalKey;
//import org.gof.demo.worldsrv.tower.StageObjectTower;
//
//import java.io.IOException;
//import java.util.List;
//
//public class PartnerObjectMirr  extends CharacterObject {
//
//    // 定时恢复怒气
//    public TickTimer rageTickTimer = new TickTimer(Time.SEC);
//
//    public PartnerObjectMirr() { super(null); }
//
//    public PartnerObjectMirr(StageObject stageObj) {
//        super(stageObj);
//    }
//
//    public PartnerObjectMirr(HumanObjectMirr humanObjMirr,  int partnerSn) {
//        this(humanObjMirr, Port.applyId(), partnerSn);
//    }
//
//    public PartnerObjectMirr(HumanObjectMirr humanObjMirr, long partnerId, int partnerSn) {
//        super(humanObjMirr.stageObj);
//        ConfPartner confPartner = ConfPartner.get(partnerSn);
//        if(confPartner == null){
//            Log.temp.error("===confPartner配表错误， sn={}", partnerSn);
//            // 不终止，留着报错，方便查询发现问题
//        }
//        Partner partner = new Partner();
//        partner.setId(partnerId);
//        partner.setPartnerSn(partnerSn);
//        partner.setHumanId(humanObjMirr.id);
//        partner.setAdvLv(confPartner.order);
//        partner.setIdentification(confPartner.identification);
//        partner.setLevel(humanObjMirr.getHuman().getLevel());
//        partner.setModelSn(confPartner.showModel);
//        partner.setProfession(confPartner.job);
//        partner.setSn(String.valueOf(partnerSn));
//
//        dataPers.unit = partner;
//
//        this.id = partner.getId();
//        this.sn = String.valueOf(partnerSn);
//        this.name = confPartner.name;
//        this.parentObjectId = humanObjMirr.id;
//        this.modelSn = confPartner.showModel;
//        this.teamBundleID = humanObjMirr.getTeamBundleId();
//
//        //属性加成
//        UnitPropPlus pp = new UnitPropPlus();
//        pp.setId(this.id);
//        dataPers.unitPropPlus.init(pp);
//        // 初始化怒气
//        this.ragePart = new RagePart(0, ConfGlobal.get(510).value, 0);
//        // 伙伴属性
//        unitPropChange(new PropCalc(humanObjMirr.getPropPlusBase().toJSONStr()));
//        //设置血量
//        this.getUnit().setHpCur(getUnit().getHpMax());
//
//        ConfCharacterModel confCharacterModel = ConfCharacterModel.get(this.modelSn);
//        if(confCharacterModel != null){
//            this.confModel = ConfCharacterModel.get(this.modelSn);
//        }
//
//    }
//
//    public void initSkill(){
//        ConfPartner confPartner = ConfPartner.get(getPartner().getPartnerSn());
//        if(confPartner.partnerSkill == null){
//            return;
//        }
//
//        SkillBook book = new SkillBook();
//        this.setSkillBook(book);
//
//        // 普攻
//        for(int i = 0; i < confPartner.generalSkill.length; i++) {
//            getSkillBook().initSkill(confPartner.generalSkill[i], 1);
//        }
//        for(int i = 0; i < confPartner.partnerSkill.length; i++){
//            int skillSn = confPartner.partnerSkill[i];
//            getSkillBook().initSkill(skillSn, 1);
//        }
//
//        //初始化技能
//        SkillManager.inst().initSkillCastOrder(this);
////		UnitManager.inst().propCalc(this,null);
//    }
//
//    public void unitPropChange(PropCalc humanPropCalc){
//        Partner partner = (Partner)getUnit();
//
//        PropCalc partnerPropCalc = new PropCalc();
//        int combat = PartnerManager.inst().getPartnerPropCalcCambat(humanPropCalc, partnerPropCalc
//                , partner.getPartnerSn(), partner.getLevel(), partner.getFbLv());
//        dataPers.unitPropPlus.setBase(partnerPropCalc.toJSONStr());
//        ConfPartner confPartner = ConfPartner.get(partner.getPartnerSn());
//        partner.setSpeed(confPartner.speed);
//        partner.setCombat(combat);
//        PropManager.inst().propCalc(this, 0, CombatChangeLog.伙伴);
//    }
//
//    @Override
//    public boolean isAttackInitiatively() {
//        return false;
//    }
//
//    @Override
//    public int getProfession() {
//        return getPartner().getProfession();
//    }
//
//    @Override
//    public int getCamp() {
//        if(this.getParentObject()!=null)
//            return this.getParentObject().getCamp();
//        else
//            return CampTypeKey.TYPE_DEFAULT_HUMAN;
//    }
//
//    @Override
//    public long getTeamBundleId()
//    {
//        if(this.getParentObject()!=null)
//            return this.getParentObject().getTeamBundleId();
//        else
//            return this.teamBundleID;
//    }
//
//    @Override
//    public void pulse(int deltaTime) {
//        super.pulse(deltaTime);
//
//        pulseAI(deltaTime);
//
//        if(rageTickTimer.isPeriod(deltaTime)){
//            ConfPartner confPartner = ConfPartner.get(getPartner().getPartnerSn());
//            addRage(confPartner.rageRate);
//        }
//    }
//
//    @Override
//    public void writeTo(OutputStream out) throws IOException {
//        super.writeTo(out);
//
//        out.write(dataPers);
//    }
//
//    @Override
//    public void readFrom(InputStream in) throws IOException {
//        super.readFrom(in);
//
//        dataPers = in.read();
//    }
//
//    @Override
//    public Define.DStageObject.Builder createMsg(UnitObject otherObject) {
//        Partner partner = getPartner();
//
//        //移动中的目标路径
//        List<Define.DVector3> runPath = running.getRunPathMsg();
//
//        //玩家信息单元
//        Define.DStagePartner.Builder h = Define.DStagePartner.newBuilder();
//        h.setPartnerSn(partner.getPartnerSn());
//        h.addAllPosEnd(runPath);
//        h.setDPartner(createDPartner());
//        h.setTeamBundleID(teamBundleID);
//        h.setParentId(parentObjectId);
//
//        //buff信息
//        if(this.getBuffs() != null) {
//            for (int sn : this.getBuffs().keySet()) {
//                h.addBuffs(sn);
//            }
//        }
//
//        //阵营信息
//        h.setCampType(getCamp());
//
//        Define.DStageObject.Builder objInfo = Define.DStageObject.newBuilder();
//        objInfo.setObjId(id);
//        objInfo.setType(Define.EWorldObjectType.PARTNER);
//        objInfo.setName(name);
//        objInfo.setModelSn(partner.getModelSn());
//        objInfo.setPos(getPosNow().toMsg());
//        objInfo.setDir(createDirMsg());
//        objInfo.setPartner(h);
//
//        return objInfo;
//    }
//
//    public Partner getPartner() {
//        Partner partner = (Partner)this.dataPers.unit;
//        return partner;
//    }
//    public int getRage() { return  ragePart.getRage(); }
//
//    private MsgStage.SCStageObjectAppear.Builder createMsgBorn() {
//        MsgStage.SCStageObjectAppear.Builder msgBorn = MsgStage.SCStageObjectAppear.newBuilder();
//        msgBorn.setObjAppear(createMsg(null));
//        msgBorn.setType(2);
//
//        return msgBorn;
//    }
//
//    private Define.DPartner.Builder createDPartner(){
//        Define.DPartner.Builder dPartner = Define.DPartner.newBuilder();
//        dPartner.setId(id);
//        dPartner.setLevel(getPartner().getLevel());
//        dPartner.setExp(getPartner().getExp());
//        dPartner.setPartnerSn(getPartner().getPartnerSn());
//        dPartner.setUsePos(getPartner().getUsePos());
//        dPartner.setLock(getPartner().isLock());
//        dPartner.setUnit(createDUnit());
//        dPartner.setFbLv(getPartner().getFbLv());
//        dPartner.setFbExp(getPartner().getFbExp());
//
//        return dPartner;
//    }
//
//    @Override
//    public void startup() {
//        profession = getUnit().getProfession();
//        if(ragePart == null){
//            this.ragePart = new RagePart(0, ConfGlobal.get(510).value, 0);
//        }
//        getUnit().setHpCur(getUnit().getHpMax());
//        if(confModel == null){
//            ConfCharacterModel confCharacterModel = ConfCharacterModel.get(this.modelSn);
//            if(confCharacterModel != null){
//                this.confModel = ConfCharacterModel.get(this.modelSn);
//            }
//        }
//        this.stageEnter(stageObj);
//    }
//
//    public void stageShow() {
//        // 已在地图中的 忽略
//        if(isInWorld()) {
//            Log.stageCommon.warn("使活动单元进入地图时发现inWorld状态为true：data={}", this);
//            return;
//        }
//
//        // 设置状态为在地图中
//        if(dataPers.unit.getHpCur() > 0){
//            //非死亡状态，才能将inWorld的值设置为true(组队副本队员补齐，切换队长的时候，队长的伙伴可能是死亡状态，会导致复活不发appear消息)
//            setInWorld(true);
//        }
//
//        // 日志
//        if(Log.stageCommon.isInfoEnabled()) {
//            Log.stageCommon.info("地图单位进入地图: stageId={}, objId={}, objName={}", stageObj.id, id, name);
//        }
//
//        if(stageObj instanceof StageObjectCompetition || stageObj instanceof StageObjectTower){
//            ConfBuff buff = ConfBuff.get(ConfGlobalUtils.getValue(ConfGlobalKey.竞技场加血上限的BUFF));
//            if(buff!=null){
//                String propPlusJSON= Utils.toJOSNString(buff.propName, buff.propValue);
//                BuffManager.inst().add(this, id, buff.sn, propPlusJSON);
//            }
//        }
//        // 通知其他玩家 有地图单元进入视野
//        StageManager.inst().sendMsgToStage(createMsgBorn(), stageObj);
//
//        //加入AI
//        if(this.aiFighter == null) {
//            Log.temp.info("partner aiFighter sn {} name {} pos {}", this.sn, this.name, this.getPosNow());
//            int aiSn = ConfPartner.get(Utils.intValue(this.sn)).partnerAI;
//            aiFighter = new PartnerFighter<>(this, aiSn);
//            aiFighter.mountAI(this);
//        }
//
//    }
//
//    @Override
//    public void onRegister() {
//
//    }
//
//
//    public int getPartnerSn(){
//        return getPartner().getPartnerSn();
//    }
//}
