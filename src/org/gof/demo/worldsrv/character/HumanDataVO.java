package org.gof.demo.worldsrv.character;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.interfaces.ISerilizable;
import org.gof.core.support.ParamKey;
import org.gof.core.support.Utils;
import org.gof.demo.battlesrv.msgHandler.SkillManager;
import org.gof.demo.battlesrv.support.PropCalc;
import org.gof.demo.worldsrv.entity.Human;
import org.gof.demo.worldsrv.human.HumanManager;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.pet.PetManager;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 玩家数据同步相关信息
 */
public class HumanDataVO implements ISerilizable {

	public long humanId;
	public int level;
	public String name;
	public boolean isRoboot = false;
	public int jobSn;
	public Define.p_battle_role p_battle = null;
	public List<Define.p_active_skill> activeSkills = new ArrayList<>();
	public List<Define.p_passive_skill> passiveSkills = new ArrayList<>();

	public HumanDataVO() {
		// implements ISerilizable的类必须有无参构造函数
	}

	/**
	 * 生成玩家战斗数据
	 */
	public HumanDataVO(HumanObject humanObj, Object... objs) {
		Human human = humanObj.getHuman();
		humanId = humanObj.id;
		level = human.getLevel();
		name = human.getName();
		jobSn = humanObj.operation.profession.getJobSn();

		// 主动技能
		this.activeSkills.addAll(SkillManager.inst().getAllActiveSkill(humanObj));
		// 被动技能
		this.passiveSkills.addAll(SkillManager.inst().getAllPassiveSkill(humanObj));

		// TODO 差好多战斗数据

		Define.p_battle_role.Builder dInfo = Define.p_battle_role.newBuilder();
		dInfo.setId(humanId);
		dInfo.setName(name);
		dInfo.setLev(level);
		dInfo.setJob(jobSn);
		Define.p_role_skill.Builder dSkill = Define.p_role_skill.newBuilder();
		dSkill.addAllActiveSkill(activeSkills);
		dSkill.addAllPassiveSkill(passiveSkills);
		dInfo.setRoleSkill(dSkill);

		int lineup = humanObj.getHuman2().getUsePetLineup();
		List<Integer> petSnList = humanObj.operation.petData.lineupMap.get(lineup);
		if (petSnList == null && (lineup > lineup || lineup <= 0)) {
			humanObj.getHuman2().setUsePetLineup(1);
			petSnList = humanObj.operation.petData.lineupMap.get(1);
		}
		for (int petSn : petSnList) {
			if(petSn <= 0){
				continue;
			}
			dInfo.addPetList(PetManager.inst().to_p_role_pet(humanObj, petSn, lineup));
		}

		PropCalc propCalc = humanObj.getPropPlus();
		for (Map.Entry<Integer, BigDecimal> entry : propCalc.getDatas().entrySet()) {
			int key = entry.getKey();
			long value = HumanManager.inst().getCalculationValue(key, propCalc.getDatas());
			if(value == -1){
				// 1001、1002、1003、1024转换成最终属性1、2、3、24
				int newKey = HumanManager.inst().getType(key);
				value = propCalc.getBigDecimal(newKey).longValue();
			}
			dInfo.addAttrList(HumanManager.inst().to_p_key_value(key, value));
		}

		List<Integer> activeSnList = new ArrayList<>();
		for(Define.p_active_skill dActive : activeSkills){
			activeSnList.add(dActive.getSkillId());
		}

		List<Define.p_attr_obj_list> p_attr_objListAll = SkillManager.inst().to_p_attr_obj_list(passiveSkills,
				petSnList, activeSnList, HumanManager.inst().petAttList(propCalc, petSnList));

		dInfo.addAllAttrObjList(p_attr_objListAll);// p_attr_obj_list

		dInfo.setFigure(humanObj.to_p_role_figure());
		dInfo.setManualOperator(Utils.getTimeSec());
//		dInfo.addAllOperators();//p_battle_operator
		if(objs != null && objs.length > 0){
			int objType = Utils.intValue(objs[0]);
			if(objType == ParamKey.objType_2){
				dInfo.addExt(HumanManager.inst().to_p_key_value(1, Utils.intValue(objs[1])));
			}
		} else {
			// 默认1吧
			dInfo.addExt(HumanManager.inst().to_p_key_value(1, 1)); // dInfo.addAllExt(); //p_key_value
		}

		Define.p_head.Builder head = Define.p_head.newBuilder();
		head.setId(human.getHeadSn());
		head.setFrameId(human.getCurrentHeadFrameSn());
		head.setUrl("");
		dInfo.setHead(head);
		dInfo.setLeftHp(100);
		this.p_battle = dInfo.build();
	}

	public String to_p_battle_role_String(){
		JSONObject jo = new JSONObject();
		jo.put("p_battle", p_battle);
		return jo.toJSONString();
	}
	
	public HumanDataVO(String json) {
		JSONObject jo = Utils.toJSONObject(json);
		humanId = jo.getLongValue("humanId");
		level = jo.getIntValue("level");
		name = jo.getString("name");
		jobSn = jo.getIntValue("jobSn");
		p_battle = (Define.p_battle_role)jo.get("p_battle");
		activeSkills = (List<Define.p_active_skill>)jo.get("activeSkills");
		passiveSkills = (List<Define.p_passive_skill>)jo.get("passiveSkills");
	}

	public String toJSON(){
		JSONObject jo = new JSONObject();
		jo.put("humanId", humanId);
		jo.put("level", level);
		jo.put("name", name);
		jo.put("jobSn", jobSn);
//		jo.put("p_battle", p_battle);
		jo.put("activeSkills", activeSkills);
		jo.put("passiveSkills", passiveSkills);
		return jo.toJSONString();
	}
	

	@Override
	public String toString() {
		return new ToStringBuilder(this, ToStringStyle.SHORT_PREFIX_STYLE).append("humanId", humanId).toString();
	}

	@Override
	public void writeTo(OutputStream out) throws IOException {
		out.write(humanId);
	}

	@Override
	public void readFrom(InputStream in) throws IOException {
		humanId = in.read();
	}

	public Define.p_battle_role to_p_battle_role(Object... objs){
		Define.p_battle_role.Builder dInfo = p_battle.toBuilder();
		return HumanManager.inst().to_p_battle_role(dInfo, objs);
	}


}