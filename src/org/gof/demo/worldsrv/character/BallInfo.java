package org.gof.demo.worldsrv.character;


/**
 * 消息球 
 * <AUTHOR>
 *
 */
public class BallInfo{
	//唯一标识
//	public long id;
	//消息类型
	public int type;
	//发送者ID
	public long senderId;
	//发送者名称
	public String senderName;
	//发送者等级
	public int senderLevel;
	//队伍ID
	public long teamId;
	//帮会ID
	public long factionId;
	//帮会名称
	public String factionName;


	public BallInfo(int type,long senderId, String senderName, int senderLevel, long teamId, long factionId, String factionName){
//		this.id = Port.applyId();
		this.type = type;
		this.senderId = senderId;
		this.senderName = senderName;
		this.senderLevel = senderLevel;
		this.teamId = teamId;
		this.factionId = factionId;
		this.factionName = factionName;
	}
}
