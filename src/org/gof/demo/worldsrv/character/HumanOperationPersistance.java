package org.gof.demo.worldsrv.character;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.interfaces.ISerilizable;
import org.gof.core.support.Utils;
import org.gof.demo.battlesrv.msgHandler.SkillData;
import org.gof.demo.worldsrv.activity.data.ActivityControlData;
import org.gof.demo.worldsrv.angel.AngelData;
import org.gof.demo.worldsrv.charm.CharmData;
import org.gof.demo.worldsrv.config.ConfCustomMall;
import org.gof.demo.worldsrv.entity.*;
import org.gof.demo.worldsrv.equip.EquipInfo;
import org.gof.demo.worldsrv.flyPet.hybrid.FlyHybridBaseInfo;
import org.gof.demo.worldsrv.god.GodData;
import org.gof.demo.worldsrv.home.Fish.FishData;
import org.gof.demo.worldsrv.human.EModuleTableInit;
import org.gof.demo.worldsrv.human.HumanManager;
import org.gof.demo.worldsrv.human.PlanVo;
import org.gof.demo.worldsrv.instance.InstanceConstants;
import org.gof.demo.worldsrv.item.ItemData;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.pet.PetData;
import org.gof.demo.worldsrv.placingReward.PlacingRewardVo;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.task.TaskData;
import org.gof.demo.worldsrv.activity.ActivityEffect;

import java.io.IOException;
import java.util.*;

/**
 * 所有业务功能放这里
 *
 * <AUTHOR>
 * @Date 2024/2/23
 * @Param
 */
public class HumanOperationPersistance implements ISerilizable {

    public Profession profession;        // 职业信息
    public ItemData itemData = new ItemData();            // 所有物品道具存放表
    public TaskData taskData = new TaskData();            // 任务
    public Map<Integer, PlanVo> planVoMap;          //行装map
    public Map<Integer, Equip> equipsMap;                // 装备信息Map<方案，一套tab装备信息>
    public long equipIdGen = 1;                            // 装备ID生成器
    public PetData petData = new PetData();    //同伴/宠物
    public Map<Integer, PlacingRewardVo> placingRewardMap = new HashMap<>(); // 放置信息Map<放置类型，放置信息>
    public Friend friend; // 好友

    public Map<Long, Mail> mailMap = new HashMap<>(); // 玩家邮件

    public ClientInfo clientInfo; // 纯客户端记录数据
    public Mall mall;// 商店
    public SkillData skillData = new SkillData();    //技能
    public Mount mount; // 坐骑
    public Wing wing; // 背饰
    public Mine mine;//矿山
    public Capture capture;//抓捕
    public Privilege privilege;//特权
    public Map<Integer, Integer> scienceMap = new HashMap<>();//科技map
    public Map<Integer, Integer> sciencePassiveMap = new HashMap<>();//对技能加成x级
    public CarPark carPark;//车库
    public CarPark2 carPark2;//车库2
    public ActivityControlData activityControlData = new ActivityControlData();//运营活动相关
    public Human2 human2;//玩家信息
    public Human3 human3;//玩家信息
    private HumanBrief brief; // 玩家简要信息
    public CrossWarInfo crossWarInfo;

    public Map<Long, EquipInfo> equipBoxMap = new HashMap<>();//装备箱
	public Set<Integer> appearanceSet = new HashSet<>();//外观图鉴
	public Map<Integer, Integer> equipFilterMap = new HashMap<>();//装备属性过滤
    public Map<Integer, List<Long>> mainRepKillIdMap = new HashMap<>();

    public Map<Integer, Integer> repTypeNumMap = new HashMap<>();

    // region 副本难度获取相关方法
    /**
     * 副本难度记录，对应Human3的repTypeDifficultyMap字段
     */
    private Map<Integer, Integer> repDiffMap = new HashMap<>();
    public List<Long> fillMailIdList = new ArrayList<>();

    public Map<Integer, Long> emojiSnTimeMap = new HashMap<>(); //Utils.jsonToMapIntLong(human.getEmojiSnExpiredMap());
    public List<Integer> emojiSnList = new ArrayList<>(); // Utils.strToIntList(human.getEmojiSnList());

	public DoubleChapter doubleChapter; // 双人副本
	public AngelData angelData; //星将系统
	public CharmData charmData; //美观值
	public FishData fishData; //捕鱼
	public ActivityEffect activityEffect; //活动增益效果
	public GodData godData; //天使系统

    /**
     * 根据副本类型获取副本最大难度
     */
    public int getRepMaxDiffculty(int repType) {
        return repDiffMap.getOrDefault(repType, 0);
    }

	public int getDarkRepMaxDiffculty() {
		int diff28 = getRepMaxDiffculty(InstanceConstants.DARKTRIALCHAPTER_28);
		int diff29 = getRepMaxDiffculty(InstanceConstants.DARKTRIALCHAPTER_29);
		int diff30 = getRepMaxDiffculty(InstanceConstants.DARKTRIALCHAPTER_30);
		return Math.max(Math.max(diff28, diff29), diff30);
	}

    /**
     * 更新副本类型对应的最大难度
     */
    public void setRepMaxDiffculty(int repType, int newLevel) {
        int oldLevel = getRepMaxDiffculty(repType);
        if (oldLevel > newLevel) {
            return;
        }
        repDiffMap.put(repType, newLevel);
        human3.setRepTypeDifficultyMap(Utils.mapIntIntToJSON(repDiffMap));
    }

    /**
     * 登陆加载数据后的副本难度Map初始化
     */
    public void initRepDiffcultyMap() {
        if (human3 != null) {
            repDiffMap = Utils.jsonToMapIntInt(human3.getRepTypeDifficultyMap());
        }
    }
    // endregion 副本难度获取相关方法

	// region 自选礼包设置相关代码
	/**
	 * Map<充值表sn, Map<下标（第几个自选奖励）, List<Integer> {物品sn，物品数量}>>
	 */
	private Map<Integer, Map<Integer, List<Integer>>> payCustomGiftMap = new HashMap<>();

	public void initPayCustomGift() {
		if (mall == null) {
			return;
		}
		String payGiftJSON = mall.getCustomRewardJSON();
		if (Utils.isNullOrEmptyJSONString(payGiftJSON)) {
			return;
		}
		payCustomGiftMap = JSON.parseObject(payGiftJSON, new TypeReference<Map<Integer, Map<Integer, List<Integer>>>>() {
		});
	}

	public HumanBrief getBrief() {
		return brief;
	}

	public void setBrief(HumanBrief brief) {
		this.brief = brief;
	}

	/**
	 * 设置自选礼包的自选奖励
	 */
	public void setPayCustomGift(int paySn, List<Define.p_key_value> customGiftList) {
		ConfCustomMall confCustomMall = ConfCustomMall.get(paySn);
		if (confCustomMall == null) {
			Log.temp.error("玩家设置自选礼包查不到表ConfCustomMall, humanId={}, sn={}", human2.getId(), paySn);
			return;
		}
		for (Define.p_key_value kv : customGiftList) {
			int index = (int) kv.getK();
			int itemSn = (int) kv.getV();
			int itemNum = 0;
			for (int i = 0; i < confCustomMall.custom_reward.length; i++) {
				int[] chooses = confCustomMall.custom_reward[i];
				if (index == chooses[0]) {
					for (int j = 1; j < chooses.length - 1; j += 2) {
						int chooseItemSn = chooses[j];
						if (chooseItemSn == itemSn) {
							itemNum = chooses[j + 1];
							break;
						}
					}
				}
			}
			if (itemNum != 0) {
				Map<Integer, List<Integer>> customIndexGiftMap = payCustomGiftMap.computeIfAbsent(paySn, k -> new HashMap<>(2));
				List<Integer> itemList = new ArrayList<>();
				itemList.add(itemSn);
				itemList.add(itemNum);
				customIndexGiftMap.put(index, itemList);
			}
		}
		updateCustomGift();
	}

	/**
	 * 移除礼包自选设置
	 */
	public void removeCustomGift(int paySn) {
		payCustomGiftMap.remove(paySn);
		updateCustomGift();
	}

	/**
	 * 返回该自选礼包的自选奖励
	 */
	public Map<Integer, List<Integer>> getCustomGift(int paySn) {
		return payCustomGiftMap.get(paySn);
	}

	public void updateCustomGift() {
		mall.setCustomRewardJSON(JSON.toJSONString(payCustomGiftMap));
	}

	public Map<Integer, Map<Integer, List<Integer>>> getPayCustomGiftMap() {
		return payCustomGiftMap;
	}
	// endregion 自选礼包设置相关代码

	// region 礼包档位相关方法
	private Map<String, Integer> gearRangeMap = new HashMap<>();

	/**
	 * 初始化礼包档位
	 */
	public void initGearRangeMap() {
		if(mall == null){
			return;
		}
		gearRangeMap = Utils.jsonToMapStringInt(mall.getGearRangeJSON());
	}

	public int getGearRange(String key) {
		return gearRangeMap.getOrDefault(key, 0);
	}

	public void addGearRange(String key, int addValue) {
		int value = getGearRange(key);
		setGearRange(key, value + addValue);
	}

	public void setGearRange(String key, int value) {
		if (value == 0) {
			gearRangeMap.remove(key);
		} else {
			gearRangeMap.put(key, value);
		}
		mall.setGearRangeJSON(Utils.mapStringIntToJSON(gearRangeMap));
	}
	// endregion 礼包档位相关方法

	// region 飞宠系统
	public Map<Long, FlyPet> flyPetMap = new HashMap<>();

	public void initFlyPet(List<FlyPet> flyPetList) {
		for (FlyPet flyPet : flyPetList) {
			flyPetMap.put(flyPet.getId(), flyPet);
		}
	}

	public void addFlyPet(FlyPet flyPet) {
		flyPetMap.put(flyPet.getId(), flyPet);
	}

	private FlyPetHumanData flyPetData;// 培育基地数据entity
	public FlyHybridBaseInfo[] flyHybridBases = new FlyHybridBaseInfo[4];// 4个培养基地, 可能是空对象, 第四个基地需要特权卡
	public List<Integer> flyPetIllustratedList = new ArrayList<>(0);// 飞宠图鉴
	public Map<Long, Long> flyHybridSettingMap = new HashMap<>(1);// 配对设置
	public List<Long> flyPetStarIdList = new ArrayList<>(0);// 飞宠标星列表
	public List<Long> flyHybridPartnerIdList = new ArrayList<>(0);// 配对搭档列表
	public List<Long> flyHybridPartnerApplyList = new ArrayList<>(0);// 配对搭档申请列表

	public void initFlyPetHumanData(FlyPetHumanData entity) {
		flyPetData = entity;
		if (!Utils.isNullOrEmptyJSONString(flyPetData.getBase1())) {
			flyHybridBases[0] = JSON.parseObject(flyPetData.getBase1(), new TypeReference<FlyHybridBaseInfo>() {});
		}
		if (!Utils.isNullOrEmptyJSONString(flyPetData.getBase2())) {
			flyHybridBases[1] = JSON.parseObject(flyPetData.getBase2(), new TypeReference<FlyHybridBaseInfo>() {});
		}
		if (!Utils.isNullOrEmptyJSONString(flyPetData.getBase3())) {
			flyHybridBases[2] = JSON.parseObject(flyPetData.getBase3(), new TypeReference<FlyHybridBaseInfo>() {});
		}
		if (!Utils.isNullOrEmptyJSONString(flyPetData.getBase4())) {
			flyHybridBases[3] = JSON.parseObject(flyPetData.getBase4(), new TypeReference<FlyHybridBaseInfo>() {});
		}
		flyPetIllustratedList = Utils.strToIntList(entity.getFlyPetIllustrated());
		flyHybridSettingMap = Utils.jsonToMapLongLong(entity.getPartnerSettingJSON());
		flyPetStarIdList = Utils.strToLongList(entity.getStarFlyPetIds());
		flyHybridPartnerIdList = Utils.strToLongList(entity.getPartnerIds());
		flyHybridPartnerApplyList = Utils.strToLongList(entity.getPartnerApply());
	}

	public FlyPetHumanData initNewFlyPetHumanData(HumanObject humanObj) {
		FlyPetHumanData flyPetData = new FlyPetHumanData();
		flyPetData.setId(humanObj.id);
		flyPetData.persist();
		HumanManager.inst().setModInit(humanObj, EModuleTableInit.FlyPetHumanData, true);
		return flyPetData;
	}

	public void saveBaseInfo(HumanObject humanObj, int i) {
		if (flyPetData == null) {
			flyPetData = initNewFlyPetHumanData(humanObj);
		}
		String jsonString = flyHybridBases[i] == null ? "{}" : JSON.toJSONString(flyHybridBases[i]);
		if (i == 1) {
			flyPetData.setBase2(jsonString);
		} else if (i == 2) {
			flyPetData.setBase3(jsonString);
		} else if (i == 3) {
			flyPetData.setBase4(jsonString);
		} else {
			flyPetData.setBase1(jsonString);
		}
	}

	public void saveFlyPetIllustrated(HumanObject humanObj) {
		if (flyPetData == null) {
			flyPetData = initNewFlyPetHumanData(humanObj);
		}
		flyPetData.setFlyPetIllustrated(Utils.listToString(flyPetIllustratedList));
	}

	public void saveFlyHybridSetting(HumanObject humanObj) {
		if (flyPetData == null) {
			flyPetData = initNewFlyPetHumanData(humanObj);
		}
		flyPetData.setPartnerSettingJSON(Utils.mapLongLongToJSON(flyHybridSettingMap));
	}

	public void saveFlyPetStarIdList(HumanObject humanObj) {
		if (flyPetData == null) {
			flyPetData = initNewFlyPetHumanData(humanObj);
		}
		flyPetData.setStarFlyPetIds(Utils.listToString(flyPetStarIdList));
	}

	public void saveFlyPetPartnerIdList(HumanObject humanObj) {
		if (flyPetData == null) {
			flyPetData = initNewFlyPetHumanData(humanObj);
		}
		flyPetData.setPartnerIds(Utils.listToString(flyHybridPartnerIdList));
	}

	public void saveFlyPetPartnerApplyList(HumanObject humanObj) {
		if (flyPetData == null) {
			flyPetData = initNewFlyPetHumanData(humanObj);
		}
		flyPetData.setPartnerApply(Utils.listToString(flyHybridPartnerApplyList));
	}
	// endregion 飞宠系统

	/**
	 * 构造函数
	 */
	public HumanOperationPersistance() {
		equipsMap = new HashMap<>();
	}
	
	@Override
	public void writeTo(OutputStream out) throws IOException {
		out.write(profession);
		out.write(mailMap);
		out.write(itemData);
		out.write(clientInfo);
		out.write(planVoMap);
		out.write(equipsMap);
		out.write(equipIdGen);
		out.write(taskData);
		out.write(petData);
		out.write(placingRewardMap);
		out.write(friend);
		out.write(mall);
		out.write(skillData);
		out.write(mount);
		out.write(wing);
		out.write(mine);
		out.write(scienceMap);
		out.write(crossWarInfo);
		out.write(sciencePassiveMap);
		out.write(equipBoxMap);
		out.write(mainRepKillIdMap);
		out.write(repTypeNumMap);
		out.write(repDiffMap);
		out.write(fillMailIdList);
		out.write(emojiSnTimeMap);
		out.write(emojiSnList);
		out.write(payCustomGiftMap);
		out.write(brief);
		out.write(gearRangeMap);
		out.write(flyPetMap);
		out.write(flyPetData);
		out.write(flyHybridBases);
		out.write(flyPetIllustratedList);
		out.write(flyHybridSettingMap);
		out.write(flyPetStarIdList);
		out.write(charmData);
	}
	
	@Override
	public void readFrom(InputStream in) throws IOException {
		profession = in.read();
		mailMap.clear();
		mailMap.putAll(in.<Map<Long, Mail>>read());
		itemData = in.read();
		clientInfo = in.read();
		planVoMap.clear();
		planVoMap.putAll(in.<Map<Integer, PlanVo>>read());
		equipsMap.clear();
		equipsMap.putAll(in.<Map<Integer, Equip>>read());
		equipIdGen = in.read();
		taskData = in.read();
		petData = in.read();
		placingRewardMap.clear();
		placingRewardMap.putAll(in.<Map<Integer, PlacingRewardVo>>read());
		friend = in.read();
		mall= in.read();
		skillData= in.read();
		mount = in.read();
		wing = in.read();
		mine = in.read();
		scienceMap = in.read();
		crossWarInfo = in.read();
		sciencePassiveMap = in.read();
		equipBoxMap.clear();
		equipBoxMap.putAll(in.<Map<Long, EquipInfo>>read());
		mainRepKillIdMap.putAll(in.<Map<Integer, List<Long>>>read());
		repTypeNumMap.putAll(in.<Map<Integer, Integer>>read());
		repDiffMap.putAll(in.read());
		fillMailIdList = in.read();
		emojiSnTimeMap.putAll(in.<Map<Integer, Long>>read());
		emojiSnList = in.read();
		payCustomGiftMap = in.read();
		brief = in.read();
		gearRangeMap = in.read();
		flyPetMap = in.read();
		flyPetData = in.read();
		flyHybridBases = in.read();
		flyPetIllustratedList = in.read();
		flyHybridSettingMap = in.read();
		flyPetStarIdList = in.read();
		charmData = in.read();
	}
}
