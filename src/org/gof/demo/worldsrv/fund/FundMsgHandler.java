package org.gof.demo.worldsrv.fund;


import org.gof.core.support.observer.MsgReceiver;
import org.gof.demo.seam.msg.MsgParam;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.msg.MsgFund;

public class FundMsgHandler {
    /**
     * 基金信息C2S消息
     * @param param
     */
    @MsgReceiver(MsgFund.fund_info_c2s.class)
    public void fund_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgFund.fund_info_c2s msg = param.getMsg();
        FundManager.inst().on_fund_info_c2s(humanObj, msg.getFundId());
    }

    /**
     * 领取基金奖励C2S消息
     * @param param
     */
    @MsgReceiver(MsgFund.fund_get_reward_c2s.class)
    public void fund_get_reward_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgFund.fund_get_reward_c2s msg = param.getMsg();
        FundManager.inst().on_fund_get_reward_c2s(humanObj, msg.getFundId(),msg.getLv(),msg.getType());
    }

    /**
     * 一键领取基金奖励C2S消息
     * @param param
     */
    @MsgReceiver(MsgFund.fund_get_reward_all_c2s.class)
    public void fund_get_reward_all_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgFund.fund_get_reward_all_c2s msg = param.getMsg();
        FundManager.inst().on_fund_get_reward_all_c2s(humanObj, msg.getFundId());
    }

    @MsgReceiver(MsgFund.fund_list_c2s.class)
    public void fund_list_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        FundManager.inst().on_fund_list_c2s(humanObj);
    }
}
