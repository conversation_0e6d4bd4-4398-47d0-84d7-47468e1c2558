package org.gof.demo.worldsrv.time;

import org.gof.demo.support.Symbol;
import org.gof.demo.support.TimeUtil;

import java.util.List;

/**
 * 开服后的时间区间
 * 
 */
public class Time6 extends MyTime {

	/**
	 * [1-7 1200-1300][1-7 1200][1-7 *]
	 * 
	 * @param time
	 */
	public Time6(String time) {
		super(time);
	}

	@Override
	protected boolean parse(String timeStr,long nowTime, long todayZeroTime) {
		// 解析时间
		String[] arr = timeStr.split(" ");
		
		// 开服服天数
		String[] days = arr[0].split(Symbol.DUANHENGGAN);
		final int startDay = days[0].equals(Symbol.XINGHAO) ? 0 : Integer.valueOf(days[0]);
		final int endedDay = days[1].equals(Symbol.XINGHAO) ? Integer.MAX_VALUE : Integer.valueOf(days[1]);
		long openDayZeroTime = TimeUtil.get0clockTime(1);
		if(startDay>1){
			this.beginTime = openDayZeroTime+(startDay-1)*TimeUtil.DAY_TIME;
		}else{
			this.beginTime = 152225215151l;
		}
		if(Integer.MAX_VALUE!=endedDay){
			this.endTime = openDayZeroTime+endedDay*TimeUtil.DAY_TIME;
		}
		final int nowDay = 1;
		// 时间片段
		List<Integer[]> hourList = super.parseHourTime(arr[1]);
		if (!hourList.isEmpty()) {
			if(beginTime!=0){
				this.beginTime+=hourList.get(0)[0];
			}
			if(endTime!=Long.MAX_VALUE){
				Integer[] lastHour = hourList.get(hourList.size()-1);
				this.endTime+=lastHour[lastHour.length-1]-TimeUtil.DAY_TIME;
			}
		}
		// 无效日期
		if (startDay > nowDay || endedDay < nowDay)
			return false;
		if (hourList.isEmpty()) {
			if(startDay==1){
				super.list.add(new TimeInfo(this.beginTime, todayZeroTime + TimeUtil.DAY_TIME));
			}else{
				super.list.add(new TimeInfo(todayZeroTime, todayZeroTime + TimeUtil.DAY_TIME));
			}
			return true;
		}
		for (Integer[] item : hourList) {
			long start = item[0] + todayZeroTime;
			long ended = item.length == 1 ? 0 : todayZeroTime + item[1];
			super.list.add(new TimeInfo(start, ended));
		}
		return true;
	}
}
