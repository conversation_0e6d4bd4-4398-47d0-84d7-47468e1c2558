package org.gof.demo.worldsrv.time;

import org.gof.demo.support.Symbol;
import org.gof.demo.support.TimeUtil;

/**
 * 一个时间对象
 *
 */
public class Time5 extends MyTime {

	/**
	 * 2015-8-1 12:00:00 3600
	 * 
	 * @param time
	 */
	public Time5(String time) {
		super(time);
	}

	@Override
	protected boolean parse(String timeStr,long nowTime, long todayZeroTime) {
		// 解析时间
		String[] arr = timeStr.split(" ");
		arr[0] = arr[0].equals(Symbol.XINGHAO) ? "1971-1-1" : arr[0];
		arr[1] = arr[1].equals(Symbol.XINGHAO) ? "00:00:00" : arr[1];
		
		super.beginTime = TimeUtil.parseTime(arr[0] + " " + arr[1]);
		super.endTime = Long.MAX_VALUE;
		// 还未开始
		if (super.beginTime >= todayZeroTime+TimeUtil.DAY_TIME)
			return false;

		// 时间片段
		int addTime = Integer.valueOf(arr[2]) * 1000;
		long i = (nowTime - super.beginTime) / addTime;
		long time = super.beginTime + addTime * i;
		for (long size = todayZeroTime + TimeUtil.DAY_TIME; time <= size; time += addTime) {
			super.list.add(new TimeInfo(time, 0));
		}
		return true;
	}
}
