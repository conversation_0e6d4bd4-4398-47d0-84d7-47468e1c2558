package org.gof.demo.worldsrv.time;

import org.gof.demo.support.Symbol;
import org.gof.demo.support.TimeUtil;

import java.util.Calendar;
import java.util.List;

/**
 * 一个时间对象
 */
public class Time4 extends MyTime {

	/**
	 * 2015 * 1-10 2,4,6 1000-1200[周1为1、周日为0][年 月 周 星期 时间]
	 * 
	 * @param time
	 */
	public Time4(String time) {
		super(time);
	}

	@Override
	protected boolean parse(String timeStr, long nowTime, long todayZeroTime) {
		// 解析时间
		String[] arr = timeStr.split(" ");
		Calendar toDayCal = Calendar.getInstance();
		toDayCal.setTimeInMillis(todayZeroTime);
		
		Calendar calendar = Calendar.getInstance();
		calendar.setTimeInMillis(0);
		boolean isToDay = true; // 是否在今日
		// 解析年份
		if (!arr[0].equals(Symbol.XINGHAO)) {
			List<Integer> list = parseSymbolValue(arr[0]);
			calendar.set(Calendar.YEAR, list.get(0));
			//年初
			beginTime = TimeUtil.getYearmilli(calendar);
			calendar.set(Calendar.YEAR, list.get(list.size() - 1));
			//年底
			endTime = TimeUtil.getYearmilli(calendar)+TimeUtil.DAY_TIME*calendar.getActualMaximum(Calendar.DAY_OF_YEAR)-1;
			isToDay = list.contains(toDayCal.get(Calendar.YEAR));
		}
		// 解析月份
		final boolean MX = arr[1].equals(Symbol.XINGHAO);
		if (!MX) {
			List<Integer> list = parseSymbolValue(arr[1]);
			calendar.setTimeInMillis(beginTime);
			calendar.set(Calendar.MONTH, list.get(0) - 1);
			if(beginTime!=0){
				beginTime = calendar.getTimeInMillis();
			}
			calendar.setTimeInMillis(endTime);
			calendar.set(Calendar.MONTH, list.get(list.size() - 1) - 1);
			if(endTime!=Long.MAX_VALUE){
				//月底
				endTime = calendar.getTimeInMillis();
			}
			isToDay = isToDay ? list.contains(toDayCal.get(Calendar.MONTH) + 1) : false;
		}
		// 解析周份
		if (!arr[2].equals(Symbol.XINGHAO)) {
			List<Integer> list = parseSymbolValue(arr[2]);
			// 按年算周
			if (MX) {
				calendar.setTimeInMillis(beginTime);
				calendar.set(Calendar.WEEK_OF_YEAR, list.get(0));
				if(beginTime!=0){
					beginTime = calendar.getTimeInMillis();
				}
				if (endTime != Long.MAX_VALUE) {
					calendar.setTimeInMillis(endTime);
					calendar.set(Calendar.WEEK_OF_YEAR, list.get(list.size() - 1));
					endTime = calendar.getTimeInMillis();
				}
				isToDay = isToDay ? list.contains(toDayCal.get(Calendar.WEEK_OF_YEAR)) : false;
			}
			// 按月算周
			else {
				calendar.setTimeInMillis(beginTime);
				calendar.set(Calendar.WEEK_OF_MONTH, list.get(0));
				if(beginTime!=0){
					beginTime = calendar.getTimeInMillis();
				}
				if (endTime != Long.MAX_VALUE) {
					calendar.setTimeInMillis(endTime);
					calendar.set(Calendar.WEEK_OF_MONTH, list.get(list.size() - 1));
					endTime = calendar.getTimeInMillis();
				}
				isToDay = isToDay ? list.contains(toDayCal.get(Calendar.WEEK_OF_MONTH)) : false;
			}
		}
		// 解析星期
		if (!arr[3].equals(Symbol.XINGHAO)) {
			List<Integer> list = parseSymbolValue(arr[3]);
			if(beginTime!=0){
				calendar.setTimeInMillis(beginTime);
				calendar.set(Calendar.DAY_OF_WEEK, list.get(0) + 1);
				beginTime = calendar.getTimeInMillis();
			}
			if (endTime != Long.MAX_VALUE) {
				calendar.setTimeInMillis(endTime);
				int beforeDayOfWeek = calendar.get(Calendar.MONTH);
				int dayOfWeek = list.get(list.size() - 1);
				dayOfWeek=dayOfWeek==7?1 :dayOfWeek+1;
				calendar.set(Calendar.DAY_OF_WEEK, dayOfWeek);
				int newDayOfWeek = calendar.get(Calendar.MONTH);
				if(beforeDayOfWeek!=newDayOfWeek){
					//跨月了，减一周
					endTime = calendar.getTimeInMillis()-TimeUtil.DAY_TIME*7;
				}else{
					endTime = calendar.getTimeInMillis();
				}
			}
			isToDay = isToDay ? list.contains(toDayCal.get(Calendar.DAY_OF_WEEK) - 1) : false;
		}
		// 时间片段
		List<Integer[]> hourList = super.parseHourTime(arr[4]);
		if (!hourList.isEmpty()) {
			if(beginTime!=0){
				this.beginTime+=hourList.get(0)[0];
			}
			if(endTime!=Long.MAX_VALUE){
				Integer[] lastHour = hourList.get(hourList.size()-1);
				this.endTime+=lastHour[lastHour.length-1]-TimeUtil.DAY_TIME;
			}
		}
		if (!isToDay) {
			return false;
		}
		if (hourList.isEmpty()) {
			super.list.add(new TimeInfo(todayZeroTime, todayZeroTime + TimeUtil.DAY_TIME));
			return true;
		}
		for (Integer[] item : hourList) {
			long start = item[0] + todayZeroTime;
			long ended = item.length == 1 ? 0 : todayZeroTime + item[1];
			super.list.add(new TimeInfo(start, ended));
		}
		return true;
	}
}
