package org.gof.demo.worldsrv.activity.data.serverdata;

import com.alibaba.fastjson.JSONObject;
import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.interfaces.ISerilizable;
import org.gof.core.support.Utils;

import java.io.IOException;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * 公会充值活动全服数据
 */
public class ServerActDataGuildPay implements IServerActData, ISerilizable {
    
    /**
     * 公会充值记录 Map<guildId, Set<humanId>>
     */
    public Map<Long, Set<Long>> guildPayRecords = new HashMap<>();

    @Override
    public void fillFromJSONString(String json) {
        JSONObject jo = Utils.toJSONObject(json);
        if (jo.containsKey("guildPayRecords")) {
            JSONObject recordsJson = jo.getJSONObject("guildPayRecords");
            for (String guildIdStr : recordsJson.keySet()) {
                long guildId = Long.parseLong(guildIdStr);
                Set<Long> humanIds = new HashSet<>();
                String humanIdsStr = recordsJson.getString(guildIdStr);
//                if (!Utils.isNullOrEmpty(humanIdsStr)) {
//                    String[] humanIdArray = humanIdsStr.split(",");
//                    for (String humanIdStr : humanIdArray) {
//                        if (!Utils.isNullOrEmpty(humanIdStr)) {
//                            humanIds.add(Long.parseLong(humanIdStr));
//                        }
//                    }
//                }
                guildPayRecords.put(guildId, humanIds);
            }
        }
    }

    @Override
    public String toJSONString() {
        JSONObject jo = new JSONObject();
        JSONObject recordsJson = new JSONObject();
        for (Map.Entry<Long, Set<Long>> entry : guildPayRecords.entrySet()) {
            long guildId = entry.getKey();
            Set<Long> humanIds = entry.getValue();
            StringBuilder sb = new StringBuilder();
            for (long humanId : humanIds) {
                if (sb.length() > 0) {
                    sb.append(",");
                }
                sb.append(humanId);
            }
            recordsJson.put(String.valueOf(guildId), sb.toString());
        }
        jo.put("guildPayRecords", recordsJson);
        return jo.toJSONString();
    }

    @Override
    public void writeTo(OutputStream out) throws IOException {
        out.write(guildPayRecords);
    }

    @Override
    public void readFrom(InputStream in) throws IOException {
        guildPayRecords = in.read();
    }

    /**
     * 添加公会充值记录
     */
    public void addGuildPayRecord(long guildId, long humanId) {
        guildPayRecords.computeIfAbsent(guildId, k -> new HashSet<>()).add(humanId);
    }

    /**
     * 移除公会充值记录
     */
    public boolean removeGuildPayRecord(long guildId, long humanId) {
        Set<Long> humanIds = guildPayRecords.get(guildId);
        if (humanIds != null && humanIds.contains(humanId)) {
            humanIds.remove(humanId);
            if (humanIds.isEmpty()) {
                guildPayRecords.remove(guildId);
            }
            return true;
        }
        return false;
    }

    /**
     * 获取公会充值人数
     */
    public int getGuildPayCount(long guildId) {
        Set<Long> humanIds = guildPayRecords.get(guildId);
        return humanIds != null ? humanIds.size() : 0;
    }

    /**
     * 检查玩家是否已充值
     */
    public boolean hasPlayerPaid(long guildId, long humanId) {
        Set<Long> humanIds = guildPayRecords.get(guildId);
        return humanIds != null && humanIds.contains(humanId);
    }
}
