package org.gof.demo.worldsrv.activity.data.controldata;

import com.alibaba.fastjson.JSONObject;
import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.interfaces.ISerilizable;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.activity.ActivityVo;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.support.Log;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ControlBreakEggData implements ISerilizable, IControlData {

    public int total = 0;//消耗次数
    public int layer = 1;//当前层
    public int layerBreakNum;//统计，每层砸蛋次数
    public List<Integer> sumRewards = new ArrayList<>();//领取的层数奖励
    public List<Integer> chooseReward = new ArrayList<>();//index,goodSn,goodNum
    public Map<Integer,Integer> posBreakSnMap = new HashMap<>();//打开金蛋的pos对应ConfBreakGoldEggWeight.Sn

    public ControlBreakEggData() {

    }

    public ControlBreakEggData(String json) {
        JSONObject jo = Utils.toJSONObject(json);
        total = jo.getIntValue("total");
        layer = jo.getIntValue("layer");
        if(layer <= 0){
            layer = 1;
        }
        layerBreakNum = jo.getIntValue("layerBreakNum");
        sumRewards = Utils.strToIntList(jo.getString("sumRewards"));
        chooseReward = Utils.strToIntList(jo.getString("chooseReward"));
        posBreakSnMap = Utils.jsonToMapIntInt(jo.getString("posBreakSnMap"));
    }

    @Override
    public void writeTo(OutputStream out) throws IOException {
        out.write(total);
        out.write(layer);
        out.write(layerBreakNum);
        out.write(sumRewards);
        out.write(chooseReward);
        out.write(posBreakSnMap);
    }

    @Override
    public void readFrom(InputStream in) throws IOException {
        total = in.read();
        layer = in.read();
        layerBreakNum = in.read();
        sumRewards = in.read();
        chooseReward = in.read();
        posBreakSnMap = in.read();
    }

    @Override
    public void create(HumanObject humanObj, ActivityVo vo) {

    }

    @Override
    public String toJSON() {
        JSONObject jo = new JSONObject();
        jo.put("total", total);
        jo.put("layer", layer);
        jo.put("layerBreakNum", layerBreakNum);
        jo.put("sumRewards", Utils.listToString(sumRewards));
        jo.put("chooseReward", Utils.listToString(chooseReward));
        jo.put("posBreakSnMap", Utils.mapIntIntToJSON(posBreakSnMap));
        return jo.toJSONString();
    }

}
