package org.gof.demo.worldsrv.activity.data.controldata;

import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.activity.ActivityVo;
import org.gof.demo.worldsrv.character.HumanObject;

import java.util.HashMap;
import java.util.Map;

public class ControlDungeonCraftData implements IControlData {
    private int totalNum = 0; // 总打造次数
    private Map<Integer, Integer> craftCountMap = new HashMap<>(); // 不同类型的打造次数

    // 记录每种打造类型对应的每个大奖未中次数 Map<craftType, Map<rewardIndex, count>>
    private Map<Integer, Map<Integer, Integer>> rewardFailCountMap = new HashMap<>();


    public ControlDungeonCraftData() {
    }

    public ControlDungeonCraftData(String jsonStr) {
        if (Utils.isNullOrEmptyJSONString(jsonStr)) {
            return;
        }
        Map<String, Object> map = Utils.jsonToMap(jsonStr);
        if (map.containsKey("totalNum")) {
            this.totalNum = (int) map.get("totalNum");
        }
        if (map.containsKey("craftCountMap")) {
            this.craftCountMap = Utils.jsonToMapIntInt(map.get("craftCountMap").toString());
        }
        if (map.containsKey("rewardCountMap")) {
            Map<String, Object> outerMap = Utils.jsonToMap(map.get("rewardCountMap").toString());
            for (String craftTypeStr : outerMap.keySet()) {
                int craftType = Integer.parseInt(craftTypeStr);
                Map<Integer, Integer> innerMap = Utils.jsonToMapIntInt(outerMap.get(craftTypeStr).toString());
                rewardFailCountMap.put(craftType, innerMap);
            }
        }
    }

    @Override
    public String toJSON() {
        Map<String, Object> map = new HashMap<>();
        map.put("totalNum", totalNum);
        map.put("craftCountMap", Utils.mapIntIntToJSON(craftCountMap));

        // 序列化奖励次数记录
        Map<String, String> outerMap = new HashMap<>();
        for (Map.Entry<Integer, Map<Integer, Integer>> entry : rewardFailCountMap.entrySet()) {
            outerMap.put(String.valueOf(entry.getKey()), Utils.mapIntIntToJSON(entry.getValue()));
        }
        map.put("rewardCountMap", Utils.toJSONString(outerMap));

        return Utils.toJSONString(map);
    }

    @Override
    public void create(HumanObject humanObj, ActivityVo vo) {
        // 创建时不需要特殊处理
    }

    public int getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(int totalNum) {
        this.totalNum = totalNum;
    }

    public int getCraftCount(int craftType) {
        return craftCountMap.getOrDefault(craftType, 0);
    }

    public void setCraftCount(int craftType, int count) {
        craftCountMap.put(craftType, count);
    }

    /**
     * 获取某个大奖配置的当前打造次数
     * @param craftType 打造类型
     * @param rewardIndex 奖励索引
     * @return 当前打造次数
     */
    public int getRewardFailCount(int craftType, int rewardIndex) {
        Map<Integer, Integer> innerMap = rewardFailCountMap.get(craftType);
        return innerMap != null ? innerMap.getOrDefault(rewardIndex, 0) : 0;
    }

    /**
     * 设置某个大奖配置的当前打造次数
     * @param craftType 打造类型
     * @param rewardIndex 奖励索引
     * @param count 次数
     */
    public void setRewardFailCount(int craftType, int rewardIndex, int count) {
        rewardFailCountMap.computeIfAbsent(craftType, k -> new HashMap<>()).put(rewardIndex, count);
    }

    /**
     * 增加某个大奖配置的打造次数
     * @param craftType 打造类型
     * @param rewardIndex 奖励索引
     */
    public void incrementRewardFailCount(int craftType, int rewardIndex) {
        int current = getRewardFailCount(craftType, rewardIndex);
        setRewardFailCount(craftType, rewardIndex, current + 1);
    }

    /**
     * 重置某个大奖配置的打造次数
     * @param craftType 打造类型
     * @param rewardIndex 奖励索引
     */
    public void resetRewardFailCount(int craftType, int rewardIndex) {
        setRewardFailCount(craftType, rewardIndex, 0);
    }
}