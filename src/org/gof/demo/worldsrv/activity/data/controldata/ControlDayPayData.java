package org.gof.demo.worldsrv.activity.data.controldata;

import com.alibaba.fastjson.JSONObject;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.activity.ActivityVo;
import org.gof.demo.worldsrv.character.HumanObject;

public class ControlDayPayData implements IControlData {
    public int payDayNum = 0;     // 充值天数
    public int rewardIndex = 0;       // 是否领奖
    public int lastPayTime = 0;       // 上次计天时间
    public int chooseIndex = 0;       // 选择的奖励

    public ControlDayPayData(String json) {
        JSONObject jo = Utils.toJSONObject(json);
        payDayNum = jo.getIntValue("num");
        rewardIndex = jo.getIntValue("r");
        lastPayTime = jo.getIntValue("time");
        chooseIndex = jo.getIntValue("cI");
    }

    @Override
    public void create(HumanObject humanObj, ActivityVo vo) {

    }

    @Override
    public String toJSON() {
        JSONObject jo = new JSONObject();
        jo.put("num", payDayNum);
        jo.put("r", rewardIndex);
        jo.put("time", lastPayTime);
        jo.put("cI", chooseIndex);
        return jo.toString();
    }
}
