package org.gof.demo.worldsrv.activity.data.controldata;

import com.alibaba.fastjson.JSONObject;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.activity.ActivityManager;
import org.gof.demo.worldsrv.activity.ActivityVo;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.ConfActivityControl;
import org.gof.demo.worldsrv.config.ConfActivityTerm;
import org.gof.demo.worldsrv.config.ConfMonopolyGrid_0;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.support.Log;

import java.util.ArrayList;
import java.util.List;

public class ControlMonopolyData implements IControlData{

    public int pos = 1;//当前位置1开始
    public int circle;//当前圈数

    public int eventId;//当前事件ID
    //转盘次数
    public int turnCount = 0;
    public List<Integer> posLevel = new ArrayList<>();//位置等级0开始
    public List<Integer> getCircleReward = new ArrayList<>();//已经领取的圈数奖励

    public ControlMonopolyData(String json) {
        if (json == null || json.isEmpty()) {
            return;
        }
        JSONObject jo = Utils.toJSONObject(json);
        pos = jo.getIntValue("pos");
        circle = jo.getIntValue("cc");
        eventId = jo.getIntValue("eId");
        posLevel = Utils.strToIntList(jo.getString("posL"));
        getCircleReward = Utils.strToIntList(jo.getString("cR"));
        turnCount = jo.getIntValue("tC");
    }
    @Override
    public void create(HumanObject humanObj, ActivityVo vo) {
        ConfActivityControl confActivityControl = ConfActivityControl.get(vo.activitySn);
        if(confActivityControl == null) {
            Log.game.error("活动配置不存在，活动ID:{}", vo.activitySn);
            return;
        }
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(confActivityControl.type, vo.round);
        int boardId = confTerm.group_id;
        int boardSize = GlobalConfVal.getConfMonoPolyBoardSize(boardId);
        for (int i = 1; i <= boardSize; i++) {
            ConfMonopolyGrid_0 confMonopolyGrid_0 = ConfMonopolyGrid_0.get(boardId, i);
            posLevel.add(confMonopolyGrid_0.default_level);
        }
    }

    @Override
    public String toJSON() {
        JSONObject jo = new JSONObject();
        Utils.putIfNonZero(jo, "pos", pos);
        Utils.putIfNonZero(jo, "cc", circle);
        Utils.putIfNonZero(jo, "eId", eventId);
        Utils.putIfNotEmpty(jo, "posL", Utils.listToString(posLevel));
        Utils.putIfNotEmpty(jo, "cR", Utils.listToString(getCircleReward));
        Utils.putIfNonZero(jo, "tC", turnCount);
        return jo.toJSONString();
    }
}
