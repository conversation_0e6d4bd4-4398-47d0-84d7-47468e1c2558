package org.gof.demo.worldsrv.activity.data.controldata;

import com.alibaba.fastjson.JSONObject;
import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.interfaces.ISerilizable;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.activity.ActivityVo;
import org.gof.demo.worldsrv.character.HumanObject;

import java.io.IOException;
import java.util.List;

public class ControlPVPClueData implements ISerilizable, IControlData {
    /**
     * 今日加成
     */
    public List<Integer> addSkillSnList;
    /**
     * 可选加成（选完后该list就该清空，处理玩家没选加成就退出）
     */
    public List<Integer> chooseSkillSnList;
    /**
     * 跳过战斗次数
     */
    public int skipCount;
    /**
     * 今日战斗次数
     */
    public int passCount;
    /**
     * 今日对手
     */
    public List<Long> enemyIdList;
    /**
     * sn
     */
    public int clueSn;
    /**
     * 今日购买随机技能次数
     */
    public int buyNum;
    /**
     * 战败次数
     */
    public int failNum;

    public ControlPVPClueData(String json) {
        JSONObject jo = Utils.toJSONObject(json);
        addSkillSnList = Utils.strToIntList(jo.getString("as"));
        chooseSkillSnList = Utils.strToIntList(jo.getString("cs"));
        skipCount = jo.getIntValue("sc");
        passCount = jo.getIntValue("pc");
        enemyIdList = Utils.strToLongList(jo.getString("hl"));
        clueSn = jo.getIntValue("sn");
        buyNum = jo.getIntValue("bn");
        failNum = jo.getIntValue("fn");
    }

    @Override
    public void create(HumanObject humanObj, ActivityVo vo) {
    }

    @Override
    public String toJSON() {
        JSONObject jo = new JSONObject();
        jo.put("as", Utils.listToString(addSkillSnList));
        jo.put("cs", Utils.listToString(chooseSkillSnList));
        jo.put("sc", skipCount);
        jo.put("pc", passCount);
        jo.put("hl", Utils.listToString(enemyIdList));
        jo.put("sn", clueSn);
        jo.put("bn", buyNum);
        jo.put("fn", failNum);
        return jo.toJSONString();
    }

    @Override
    public void writeTo(OutputStream out) throws IOException {
        out.write(addSkillSnList);
        out.write(chooseSkillSnList);
        out.write(skipCount);
        out.write(passCount);
        out.write(enemyIdList);
        out.write(clueSn);
        out.write(buyNum);
        out.write(failNum);
    }

    @Override
    public void readFrom(InputStream in) throws IOException {
        addSkillSnList = in.read();
        chooseSkillSnList = in.read();
        skipCount = in.read();
        passCount = in.read();
        enemyIdList = in.read();
        clueSn = in.read();
        buyNum = in.read();
        failNum = in.read();
    }
}
