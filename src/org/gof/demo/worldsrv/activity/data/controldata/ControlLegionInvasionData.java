package org.gof.demo.worldsrv.activity.data.controldata;

import com.alibaba.fastjson.JSONObject;
import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.interfaces.ISerilizable;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.activity.ActivityManager;
import org.gof.demo.worldsrv.activity.ActivityVo;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.ConfActivityControl;
import org.gof.demo.worldsrv.config.ConfActivityTerm;
import org.gof.demo.worldsrv.config.ConfGlobal;
import org.gof.demo.worldsrv.config.ConfLegionInvasion_0;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.enumKey.ConfGlobalKey;

import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

public class ControlLegionInvasionData implements IControlData, ISerilizable {
    public int topChapterSn = 0;// 最高关卡
    public int firstRewardChapterSn = 0;// 首通奖励领取到的最大关卡
    public int level = 1;// 等级
    public int exp = 0;// 经验
    public int step = 0;// 步数
    public int energy = 10;// 体力
    public long nextTime = 0L;// 下次体力恢复一点的时间
    public int[] monsterGrids = new int[25];// 三消格子
    public Map<Integer, Integer> monsterNumMap = new HashMap<>();// 上阵怪物数量
    public Map<Integer, Integer> buffNumMap = new HashMap<>();// buff数量
    public Map<Integer, Integer> monsterCollectionRewardMap = new HashMap<>();// 怪物图鉴奖励状态
    public Map<Integer, Integer> monsterAddMap = new HashMap<>();// 本次夜游前新上阵的怪物（为了做10倍夜游）

    public ControlLegionInvasionData() {
    }

    public ControlLegionInvasionData(String json) {
        JSONObject jo = Utils.toJSONObject(json);
        topChapterSn = Utils.getJSONValue(jo, "tcs", 0);
        level = Utils.getJSONValue(jo, "lv", 0);
        exp = Utils.getJSONValue(jo, "exp", 0);
        step = Utils.getJSONValue(jo, "step", 0);
        energy = Utils.getJSONValue(jo, "eng", 0);
        nextTime = Utils.longValue(Utils.getJSONValue(jo, "nt", 0L));
        monsterGrids = Utils.strToIntArray(jo.getString("mg"));
        monsterNumMap = Utils.jsonToMapIntInt(Utils.getJSONValue(jo, "mnm", ""));
        buffNumMap = Utils.jsonToMapIntInt(Utils.getJSONValue(jo,"bnm", ""));
        monsterCollectionRewardMap = Utils.jsonToMapIntInt(Utils.getJSONValue(jo,"mcrm", ""));
        firstRewardChapterSn = Utils.getJSONValue(jo, "tcp", 0);
        monsterAddMap = Utils.jsonToMapIntInt(Utils.getJSONValue(jo, "mam", ""));
    }

    @Override
    public void create(HumanObject humanObj, ActivityVo vo) {
        ConfActivityControl confAct = ConfActivityControl.get(vo.activitySn);
        if (confAct == null) {
            Log.activity.error("ConfActivityControl找不到配置, humanId={}, activitySn={}", humanObj.getHuman().getId(), vo.activitySn);
            return;
        }
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(confAct.type, vo.round);
        if (confTerm == null) {
            Log.activity.error("ConfActivityTerm找不到配置, humanId={}, activitySn={}, round={}", humanObj.getHuman().getId(), vo.activitySn, vo.round);
            return;
        }
        topChapterSn = 0;
        level = 1;
        exp = 0;
        ConfLegionInvasion_0 conf = ConfLegionInvasion_0.get(confAct.type, confTerm.group_id, level);
        step = conf.limit;
        ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.Legion_initial_actionPower);
        energy = confGlobal.value;
        nextTime = 0L;
        monsterGrids = Arrays.copyOfRange(conf.initial_bead, 0, conf.initial_bead.length);;
        monsterNumMap.clear();
        buffNumMap.clear();
        monsterCollectionRewardMap.clear();
        firstRewardChapterSn = 0;
        monsterAddMap.clear();
    }

    @Override
    public String toJSON() {
        JSONObject jo = new JSONObject();
        jo.put("tcs", topChapterSn);
        jo.put("lv", level);
        jo.put("exp", exp);
        jo.put("step", step);
        jo.put("eng", energy);
        jo.put("nt", nextTime);
        jo.put("mg", Utils.listToString(Utils.intArrToList(monsterGrids)));
        jo.put("mnm", Utils.mapIntIntToJSON(monsterNumMap));
        jo.put("bnm", Utils.mapIntIntToJSON(buffNumMap));
        jo.put("mcrm", Utils.mapIntIntToJSON(monsterCollectionRewardMap));
        jo.put("tcp", firstRewardChapterSn);
        jo.put("mam", Utils.mapIntIntToJSON(monsterAddMap));
        return jo.toJSONString();
    }

    @Override
    public void writeTo(OutputStream out) throws IOException {
        out.write(topChapterSn);
        out.write(level);
        out.write(exp);
        out.write(step);
        out.write(energy);
        out.write(nextTime);
        out.write(monsterGrids);
        out.write(monsterNumMap);
        out.write(buffNumMap);
        out.write(monsterCollectionRewardMap);
        out.write(firstRewardChapterSn);
        out.write(monsterAddMap);
    }

    @Override
    public void readFrom(InputStream in) throws IOException {
        topChapterSn = in.read();
        level = in.read();
        exp = in.read();
        step = in.read();
        energy = in.read();
        nextTime = in.read();
        monsterGrids = in.read();
        monsterNumMap = in.read();
        buffNumMap = in.read();
        monsterCollectionRewardMap = in.read();
        firstRewardChapterSn = in.read();
        monsterAddMap = in.read();
    }
}
