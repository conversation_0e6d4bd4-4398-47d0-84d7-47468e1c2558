package org.gof.demo.worldsrv.activity.data.controldata;

import com.alibaba.fastjson.JSONObject;
import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.interfaces.ISerilizable;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.activity.ActivityVo;
import org.gof.demo.worldsrv.character.HumanObject;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 全民赶年兽个人活动数据
 */
public class ControlActivityBossData implements IControlData, ISerilizable {
    /**
     * 玩家伤害
     */
    public int dmg;
    /**
     * 个人奖励已领取的下标
     */
    public List<Integer> humanRewardList = new ArrayList<>(5);
    /**
     * 全服奖励已领取的下标
     */
    public List<Integer> serverRewardList = new ArrayList<>(5);

    public ControlActivityBossData() {
    }

    public ControlActivityBossData(String json) {
        JSONObject jo = Utils.toJSONObject(json);
        dmg = Utils.intValue(jo.get("dmg"));
        humanRewardList = Utils.strToIntList(jo.getString("hrm"));
        serverRewardList = Utils.strToIntList(jo.getString("srm"));
    }

    @Override
    public void writeTo(OutputStream out) throws IOException {
        out.write(dmg);
        out.write(humanRewardList);
        out.write(serverRewardList);
    }

    @Override
    public void readFrom(InputStream in) throws IOException {
        dmg = in.read();
        humanRewardList = in.read();
        serverRewardList = in.read();
    }

    @Override
    public void create(HumanObject humanObj, ActivityVo vo) {
    }

    @Override
    public String toJSON() {
        JSONObject jo = new JSONObject();
        jo.put("dmg", dmg);
        jo.put("hrm", Utils.listToString(humanRewardList));
        jo.put("srm", Utils.listToString(serverRewardList));
        return jo.toJSONString();
    }
}
