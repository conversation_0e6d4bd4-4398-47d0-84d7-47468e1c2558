package org.gof.demo.worldsrv.activity.data.controldata;

import com.alibaba.fastjson.JSONObject;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.activity.ActivityVo;
import org.gof.demo.worldsrv.character.HumanObject;

import java.util.HashMap;
import java.util.Map;

public class ControlSlimeMixData implements IControlData {
    public Map<Integer, Integer> mixCountMap = new HashMap<>();

    public ControlSlimeMixData() {
    }

    public ControlSlimeMixData(String json) {
        JSONObject jo = Utils.toJSONObject(json);
        this.mixCountMap = Utils.jsonToMapIntInt(jo.getString("mc"));
    }

    @Override
    public void create(HumanObject humanObj, ActivityVo vo) {
    }

    @Override
    public String toJSON() {
        JSONObject jo = new JSONObject();
        jo.put("mc", Utils.mapIntIntToJSON(mixCountMap));
        return jo.toJSONString();
    }
}
