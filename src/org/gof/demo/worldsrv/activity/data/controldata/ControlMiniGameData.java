package org.gof.demo.worldsrv.activity.data.controldata;

import com.alibaba.fastjson.JSONObject;
import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.interfaces.ISerilizable;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.activity.ActivityVo;
import org.gof.demo.worldsrv.character.HumanObject;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class ControlMiniGameData implements ISerilizable, IControlData {

    private Map<Integer, Integer> chapterStateMap = new HashMap<>();//章节状态

    public ControlMiniGameData() {

    }

    public ControlMiniGameData(String json){
        JSONObject jo = Utils.toJSONObject(json);
        chapterStateMap =  Utils.jsonToMapIntInt(jo.getString("chapterStateMap"));
    }

    public Map<Integer, Integer> getChapterStateMap() {
        return chapterStateMap;
    }

    public void setChapterState(int chapterId, int state){
        chapterStateMap.put(chapterId, state);
    }

    @Override
    public void writeTo(OutputStream out) throws IOException {
        out.write(chapterStateMap);
    }

    @Override
    public void readFrom(InputStream in) throws IOException {
        chapterStateMap = in.read();
    }

    @Override
    public void create(HumanObject humanObj, ActivityVo vo) {
    }

    @Override
    public String toJSON() {
        JSONObject jo = new JSONObject();
        jo.put("chapterStateMap", Utils.mapIntIntToJSON(chapterStateMap));
        return jo.toJSONString();
    }
}
