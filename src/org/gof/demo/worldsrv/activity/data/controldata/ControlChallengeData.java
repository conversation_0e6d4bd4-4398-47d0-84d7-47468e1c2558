package org.gof.demo.worldsrv.activity.data.controldata;

import com.alibaba.fastjson.JSONObject;
import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.interfaces.ISerilizable;
import org.gof.demo.worldsrv.activity.ActivityVo;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.item.ItemManager;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.support.Util;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;

import java.io.IOException;

public class ControlChallengeData implements ISerilizable, IControlData {
    /**
     * 历史最高伤害
     */
    public long score = 0L;

    public ControlChallengeData() {
    }

    public ControlChallengeData(String json) {
        if (json == null || Util.isNullOrEmptyJSONString(json)) {
            return;
        }
        JSONObject jo = JSONObject.parseObject(json);
        this.score = jo.getLongValue("score");
    }

    public void dailyReset(HumanObject humanObj, int itemSn, int maxNum) {
        int num = ItemManager.inst().getItemNum(humanObj, itemSn);
        int addNum = maxNum - num;
        if (addNum <= 0) {
            return;
        }
        ProduceManager.inst().produceAdd(humanObj, itemSn, maxNum - num, MoneyItemLogKey.勇者试炼);
    }

    @Override
    public void create(HumanObject humanObj, ActivityVo vo) {
    }

    @Override
    public String toJSON() {
        JSONObject jo = new JSONObject();
        jo.put("score", score);
        return jo.toJSONString();
    }

    @Override
    public void writeTo(OutputStream out) throws IOException {
        out.write(score);
    }

    @Override
    public void readFrom(InputStream in) throws IOException {
        score = in.read();
    }
}
