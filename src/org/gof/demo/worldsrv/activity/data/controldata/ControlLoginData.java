package org.gof.demo.worldsrv.activity.data.controldata;

import com.alibaba.fastjson.JSONObject;
import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.Port;
import org.gof.core.interfaces.ISerilizable;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.activity.ActivityVo;
import org.gof.demo.worldsrv.character.HumanObject;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class ControlLoginData implements ISerilizable, IControlData {
    public int day = 1;//登入天数
    public long lastTime = 0;//上次计天时间
    public List<Integer> getDay = new ArrayList<>();//领取的奖励天数
    public List<Integer> hightRewardDayList = new ArrayList<>();// 高级奖励(付费)领取的天数

    public ControlLoginData() {
    }

    public ControlLoginData(String json) {
        JSONObject jo = Utils.toJSONObject(json);
        day = jo.getIntValue("day");
        if (day <= 0) {
            day = 1;
        }
        lastTime = jo.getLongValue("lastTime");
        getDay = Utils.strToIntList(jo.getString("getDay"));
        hightRewardDayList = Utils.strToIntList(jo.getString("hrdl"));
    }

    @Override
    public void writeTo(OutputStream out) throws IOException {
        out.write(day);
        out.write(lastTime);
        out.write(getDay);
        out.write(hightRewardDayList);
    }

    @Override
    public void readFrom(InputStream in) throws IOException {
        day = in.read();
        lastTime = in.read();
        getDay = in.read();
        hightRewardDayList = in.read();
    }

    @Override
    public void create(HumanObject humanObj, ActivityVo vo) {
        lastTime = Port.getTime();
    }

    @Override
    public String toJSON() {
        JSONObject jo = new JSONObject();
        jo.put("day", day);
        jo.put("lastTime", lastTime);
        jo.put("getDay", Utils.listToString(getDay));
        if (!hightRewardDayList.isEmpty()) {
            jo.put("hrdl", Utils.listToString(hightRewardDayList));
        }
        return jo.toJSONString();
    }

}
