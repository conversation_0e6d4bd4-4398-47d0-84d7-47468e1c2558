package org.gof.demo.worldsrv.activity.data.controldata;

import com.alibaba.fastjson.JSONObject;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.activity.ActivityManager;
import org.gof.demo.worldsrv.activity.ActivityVo;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.ConfActivityControl;
import org.gof.demo.worldsrv.config.ConfActivityTerm;
import org.gof.demo.worldsrv.config.ConfDoubleDraw_0;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ControlDoubleDrawData implements IControlData {
    public Map<Integer, List<Long>> chooseMap = new HashMap<>(); // 自选奖励
    public Map<Integer, Integer> dropRewardMap = new HashMap<>(); // 已经掉落的奖励
    public int total = 0; // 总次数
    public List<Integer> claimSnList = new ArrayList<>(); // 已经领取的奖励
    public Map<Integer, Integer> guaranteeMap = new HashMap<>(); // 抽卡统计，用来保底的次数
    public int refreshTotal = 0; // 抽中大奖会刷新的次数
    public Map<Integer, Integer> replaceRewardMap = new HashMap<>(); // 抽中可替代统计，用来可替换的奖励条件
    public int round = 0; // 当前轮次
    public int totalRound = 0; // 当前总轮次

    public void create(HumanObject humanObj, ActivityVo vo) {
        ConfActivityControl confActivityControl = ConfActivityControl.get(vo.activitySn);
        int actType = confActivityControl.type;
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(actType, vo.round);
        ConfDoubleDraw_0 confDoubleDraw0 = ConfDoubleDraw_0.get(actType, confTerm.group_id);
        round = confDoubleDraw0.turn_id[0];
        totalRound = 1;
    }

    public ControlDoubleDrawData(String json) {
        JSONObject jo = Utils.toJSONObject(json);
        chooseMap = Utils.jsonToMapIntListLong(jo.getString("cM"));
        dropRewardMap = Utils.jsonToMapIntInt(jo.getString("dR"));
        total = jo.getIntValue("t");
        claimSnList = Utils.strToIntList(jo.getString("cSnL"));
        guaranteeMap = Utils.jsonToMapIntInt(jo.getString("guarM"));
        refreshTotal = jo.getIntValue("refT");
        replaceRewardMap = Utils.jsonToMapIntInt(jo.getString("repRdMap"));
        round = jo.getIntValue("r");
        totalRound = jo.getIntValue("tR");
    }

    @Override
    public String toJSON() {
        JSONObject jo = new JSONObject();
        jo.put("cM", Utils.mapIntListLongToJSON(chooseMap));
        jo.put("dR", Utils.mapIntIntToJSON(dropRewardMap));
        jo.put("t", total);
        jo.put("cSnL", Utils.listToString(claimSnList));
        jo.put("guarM", Utils.mapIntIntToJSON(guaranteeMap));
        jo.put("refT", refreshTotal);
        jo.put("repRdMap", Utils.mapIntIntToJSON(replaceRewardMap));
        jo.put("r", round);
        jo.put("tR", totalRound);
        return jo.toJSONString();
    }

}