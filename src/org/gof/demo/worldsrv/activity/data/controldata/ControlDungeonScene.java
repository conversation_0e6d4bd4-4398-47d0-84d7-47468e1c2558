package org.gof.demo.worldsrv.activity.data.controldata;

import com.alibaba.fastjson.JSONObject;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.activity.ActivityConst;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.*;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgAct2;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;

import java.util.*;
import java.util.stream.Collectors;

public class ControlDungeonScene {
    private final ControlDungeonData m_data;
    private HumanObject m_humanObj;
    // 关卡sn
    private int m_chapterId = 0;
    // 关卡内层数
    private int m_layer = 0;
    // 地图格子信息（K：pos，V：grid）
    private Map<Integer, Define.p_dungeon_grid.Builder> m_mapGirds = new HashMap<>();
    // 轮次
    private int m_round = 0;
    // 当前魔石数
    private int m_stoneNum = 0;
    // 玩家局内基础属性（K：type，V：value）
    private Map<Long, Long> m_baseAttrs;
    // 背包数据
    private List<Define.p_key_value.Builder> m_backPack = new ArrayList<>();
    // 装备数据（K：装备id，V：装备等级）
    private List<Define.p_key_value.Builder> m_equips;
    // 玩家拥有的持续性效果
    private List<Define.p_dungeon_effect.Builder> m_persistEffects = new ArrayList<>();
    // 吞噬魔石随机增益选项（K：增益id，V：等级）
    private List<Define.p_key_value.Builder> m_devourStoneRandomInfo = new ArrayList<>();
    // 玩家拥有的吞噬魔石增益（K：增益id，V：等级）
    private Map<Long, Long> m_devourStoneInfo = new HashMap<>();
    // 吞噬魔石次数
    private int m_devourStoneNum = 0;
    // 刷新增益次数（每次吞噬魔石后重置为0）
    private int m_devourRefreshBuffNum = 0;
    // 最深冒险层数
    private int m_maxLayer = 0;
    // 探索区域数
    private int m_exploreAreaNum = 0;
    // 击败怪物数
    private int m_killMonsterNum = 0;
    // 击败boss数
    private int m_killBossNum = 0;
    // 场景是否正在进入下一层
    private boolean m_isEnterNextLayer = false;
    // 场景是否关闭
    private boolean m_isClosed = false;
    // 变化的格子
    private List<Define.p_dungeon_grid.Builder> m_changeGrids = new ArrayList<>();
    // 变化的血量信息
    private List<Define.p_dungeon_hp_change_info.Builder> m_hpChangeList = new ArrayList<>();
    // 本回合获得的局外道具
    private List<Define.p_key_value.Builder> m_addItems = new ArrayList<>();
    // 上个回合血上限
    private long m_lastHpMax = 0;

    public ControlDungeonScene(ControlDungeonData data, int chapterId) {
        m_data = data;
        m_chapterId = chapterId;
        m_layer = 1;
        m_maxLayer = m_layer;
        m_round = 1;
        ConfDungeonChapter confChapter = ConfDungeonChapter.get(m_chapterId);
        m_stoneNum = confChapter != null ? confChapter.stone_initial : 0;
    }

    public ControlDungeonScene(ControlDungeonData data, Define.p_dungeon_scene_info.Builder sceneInfo) {
        m_data = data;
        m_chapterId = sceneInfo.getChapterId();
        m_layer = sceneInfo.getLayer();
        m_mapGirds = sceneInfo.getMapGridsBuilderList().stream()
                .collect(Collectors.toMap(
                        Define.p_dungeon_grid.Builder::getPos,
                        (v->v),
                        (existing, replacement) -> existing  // 处理重复key的情况，保留已存在的
                ));
        m_round = sceneInfo.getRound();
        m_stoneNum = sceneInfo.getStoneNum();
        m_baseAttrs = sceneInfo.getBaseAttrsList().stream().collect(Collectors.toMap(
                Define.p_key_value::getK,
                Define.p_key_value::getV,
                (existing, replacement) -> existing  // 处理重复key的情况，保留已存在的
        ));
        m_backPack = sceneInfo.getBackPackList().stream().map(Define.p_key_value::newBuilder).collect(Collectors.toList());
        m_equips = sceneInfo.getEquipsList().stream().map(Define.p_key_value::newBuilder).collect(Collectors.toList());
        m_persistEffects = sceneInfo.getPersistEffectsList().stream().map(Define.p_dungeon_effect::newBuilder).collect(Collectors.toList());
        m_devourStoneRandomInfo = sceneInfo.getDevourStoneRandomInfoList().stream().map(Define.p_key_value::newBuilder).collect(Collectors.toList());
        m_devourStoneInfo = sceneInfo.getDevourStoneInfoList().stream().collect(Collectors.toMap(
                Define.p_key_value::getK,
                Define.p_key_value::getV,
                (existing, replacement) -> existing  // 处理重复key的情况，保留已存在的
        ));
        m_devourStoneNum = sceneInfo.getDevourStoneNum();
        m_devourRefreshBuffNum = sceneInfo.getDevourRefreshBuffNum();
        m_maxLayer = sceneInfo.getMaxLayer();
        m_exploreAreaNum = sceneInfo.getExploreAreaNum();
        m_killMonsterNum = sceneInfo.getKillMonsterNum();
        m_killBossNum = sceneInfo.getKillBossNum();
        m_lastHpMax = getPlayerAttr(ActivityConst.DUNGEON_ATTR_HP_MAX);
    }

    public boolean init(int carriedFoodItemId, List<Define.p_key_value.Builder> carriedEquips, Map<Long, Long> initialAttrs){
        // 初始化装备
        initEquips(carriedEquips, initialAttrs);
        // 初始化血量
        long hpMax = getPlayerAttr(ActivityConst.DUNGEON_ATTR_HP_MAX);
        setPlayerBaseAttr(ActivityConst.DUNGEON_ATTR_HP, hpMax);
        m_lastHpMax = hpMax;
        if(carriedFoodItemId > 0){
            addItem(carriedFoodItemId, 1);
        }
        // 初始化地图
        if(!initMap()){
            return false;
        }
        // 记录关卡到达的最大层数
        m_data.setChapterMaxLayer(m_chapterId, m_layer);
        return true;
    }

    public void setHumanObject(HumanObject humanObject) {
        m_humanObj = humanObject;
    }

    public int getChapterId(){
        return m_chapterId;
    }

    public List<Define.p_dungeon_grid> getMapGridsList(){
        return m_mapGirds.values().stream().map(Define.p_dungeon_grid.Builder::build).collect(Collectors.toList());
    }

    public List<Define.p_key_value> getBaseAttrList(){
        return m_baseAttrs.entrySet().stream()
                .map(attr->Define.p_key_value.newBuilder().setK(attr.getKey()).setV(attr.getValue()).build())
                .collect(Collectors.toList());
    }

    public List<Define.p_key_value> getAttrList(){
        List<Define.p_key_value> attrList = new ArrayList<>();
        for(long type = ActivityConst.DUNGEON_ATTR_ATK; type<=ActivityConst.DUNGEON_ATTR_CRIT_RATE; type++){
            attrList.add(Define.p_key_value.newBuilder().setK(type).setV(getPlayerAttr(type)).build());
        }
        return attrList;
    }

    public List<Define.p_key_value> getBackPackList(){
        return m_backPack.stream().map(Define.p_key_value.Builder::build).collect(Collectors.toList());
    }

    public List<Define.p_key_value> getEquipsList(){
        return m_equips.stream().map(Define.p_key_value.Builder::build).collect(Collectors.toList());
    }

    public List<Define.p_dungeon_effect> getPersistEffectList(){
        return m_persistEffects.stream().map(Define.p_dungeon_effect.Builder::build).collect(Collectors.toList());
    }

    public List<Define.p_key_value> getDevourStoneRandomInfoList(){
        return m_devourStoneRandomInfo.stream().map(Define.p_key_value.Builder::build).collect(Collectors.toList());
    }

    public List<Define.p_key_value> getDevourStoneInfoList(){
        return m_devourStoneInfo.entrySet().stream()
                .map(info->Define.p_key_value.newBuilder().setK(info.getKey()).setV(info.getValue()).build())
                .collect(Collectors.toList());
    }

    public List<Define.p_dungeon_hp_change_info> getHpChangeList(){
        return m_hpChangeList.stream().map(Define.p_dungeon_hp_change_info.Builder::build).collect(Collectors.toList());
    }

    public List<Define.p_key_value> getAddItemList(){
        return m_addItems.stream().map(Define.p_key_value.Builder::build).collect(Collectors.toList());
    }

    public Define.p_dungeon_scene_info.Builder to_p_dungeon_scene_info() {
        Define.p_dungeon_scene_info.Builder sceneInfo = Define.p_dungeon_scene_info.newBuilder();
        sceneInfo.setChapterId(m_chapterId);
        sceneInfo.setLayer(m_layer);
        sceneInfo.setRound(m_round);
        sceneInfo.setStoneNum(m_stoneNum);
        sceneInfo.addAllMapGrids(getMapGridsList());
        sceneInfo.addAllBaseAttrs(getBaseAttrList());
        sceneInfo.addAllBackPack(getBackPackList());
        sceneInfo.addAllEquips(getEquipsList());
        sceneInfo.addAllPersistEffects(getPersistEffectList());
        sceneInfo.addAllDevourStoneRandomInfo(getDevourStoneRandomInfoList());
        sceneInfo.addAllDevourStoneInfo(getDevourStoneInfoList());
        sceneInfo.setDevourStoneNum(m_devourStoneNum);
        sceneInfo.setDevourRefreshBuffNum(m_devourRefreshBuffNum);
        sceneInfo.setMaxLayer(m_maxLayer);
        sceneInfo.setExploreAreaNum(m_exploreAreaNum);
        sceneInfo.setKillMonsterNum(m_killMonsterNum);
        sceneInfo.setKillBossNum(m_killBossNum);
        return sceneInfo;
    }

    /**
     * 构建本轮次的变化信息，构建后清除变化
     * @param sourcePos
     * @param sourceCavePos
     * @return
     */
    public Define.p_dungeon_change_info.Builder buildChangeInfo(int sourcePos, int sourceCavePos){
        Define.p_dungeon_change_info.Builder changeInfo = Define.p_dungeon_change_info.newBuilder();
        changeInfo.setSourcePos(sourcePos);
        changeInfo.setSourceCavePos(sourceCavePos);
        // 变化的格子，根据pos去重
        List<Define.p_dungeon_grid> distinctChangeGrids = new ArrayList<>();
        Set<Integer> posSet = new HashSet<>();
        for (Define.p_dungeon_grid.Builder grid : m_changeGrids) {
            if (posSet.add(grid.getPos())) {
                distinctChangeGrids.add(grid.build());
            }
        }
        changeInfo.addAllChangeGrids(distinctChangeGrids);
        changeInfo.addAllAttrs(getAttrList());
        changeInfo.addAllBackPack(getBackPackList());
        changeInfo.setStoneNum(m_stoneNum);
        changeInfo.addAllHpChangeList(getHpChangeList());
        changeInfo.addAllAddItems(getAddItemList());
        // 清除变化的格子
        m_changeGrids.clear();
        // 清除变化的血量信息
        m_hpChangeList.clear();
        // 清除本回合获得的局外道具
        m_addItems.clear();
        return changeInfo;
    }

    static public int getX(int pos){
        return pos % 100;
    }
    static public int getY(int pos){
        return pos / 100;
    }
    static public int buildPos(int x, int y){
        return 100 * y + x;
    }

    @Override
    public String toString(){
        JSONObject jo = new JSONObject();
        jo.put("chapterId", m_chapterId);
        jo.put("layer", m_layer);
        jo.put("round", m_round);
        jo.put("stoneNum", m_stoneNum);
        jo.put("mapGirds", m_mapGirds.values().stream().map(v->Arrays.asList(v.getPos(), v.getType(), v.getSn(), v.getState()))
                .collect(Collectors.toList()));
        jo.put("baseAttrs", m_baseAttrs);
        jo.put("backPack", m_backPack.stream().map(v->Arrays.asList(v.getK(), v.getV()))
                .collect(Collectors.toList()));
        jo.put("equips", m_equips.stream().map(v->Arrays.asList(v.getK(), v.getV()))
                .collect(Collectors.toList()));
        jo.put("persistEffect", m_persistEffects.stream().map(v->Arrays.asList(v.getSn(), v.getEndRemainRound()))
                .collect(Collectors.toList()));
        jo.put("devourStoneRandomInfo", m_devourStoneRandomInfo.stream().map(v->Arrays.asList(v.getK(), v.getV()))
                .collect(Collectors.toList()));
        jo.put("devourStoneInfo", m_devourStoneInfo);
        jo.put("devourStoneNum", m_devourStoneNum);
        jo.put("devourRefreshBuffNum", m_devourRefreshBuffNum);
        jo.put("maxLayer", m_maxLayer);
        jo.put("exploreAreaNum", m_exploreAreaNum);
        jo.put("killMonsterNum", m_killMonsterNum);
        jo.put("killBossNum", m_killBossNum);
        return jo.toJSONString();
    }

    /**
     * 初始化装备
     * @param carriedEquips
     * @param initialAttrs
     */
    public void initEquips(List<Define.p_key_value.Builder> carriedEquips, Map<Long, Long> initialAttrs){
        m_equips = carriedEquips;
        Map<Integer, Integer> addPercentMap = new HashMap<>();
        for(Define.p_key_value.Builder equip : m_equips) {
            int equipId = Utils.intValue(equip.getK());
            int level = Utils.intValue(equip.getV());
            ConfDungeonEquipLevel confEquipLevel = ControlDungeonData.getConfEquipLevel(equipId, level);
            if(confEquipLevel != null) {
                // 收集所有携带该装备的解锁属性
                List<int[]> allUnlockAttrs = ControlDungeonData.getAllUnlockAttr(confEquipLevel, ActivityConst.DUNGEON_EQUIP_UNLOCK_TYPE_CARRY);
                for(int[] unlockAttr : allUnlockAttrs) {
                    int attrType = unlockAttr[0];
                    int addValue = unlockAttr[1];
                    int addPercent = unlockAttr[2];
                    if(addValue > 0){
                        // 固定值直接累加
                        ControlDungeonData.addAttr(initialAttrs, attrType, addValue);
                    }
                    if(addPercent > 0){
                        // 累加百分比
                        addPercentMap.put(attrType, addPercentMap.getOrDefault(attrType, 0) + addPercent);
                    }
                }
                // 收集所有携带该装备的解锁效果
                List<Integer> allUnlockEffects = ControlDungeonData.getAllUnlockEffect(confEquipLevel);
                for(int effectId : allUnlockEffects) {
                    processEffect(effectId);
                }
            }
        }
        for(Map.Entry<Integer, Integer> addPercentAttr : addPercentMap.entrySet()) {
            int attrType = addPercentAttr.getKey();
            int addPercent = addPercentAttr.getValue();
            double value = initialAttrs.getOrDefault(Utils.longValue(attrType), 0L);
            if(value > 0){
                long finalValue = (long) Math.ceil(value * (ActivityConst.DUNGEON_RATE_10000 + addPercent) / ActivityConst.DUNGEON_RATE_10000);
                initialAttrs.put(Utils.longValue(attrType), finalValue);
                Log.activity.info("===转生魔剑携带装备的万分比属性，attrType={} addPercent={} value={} finalValue={} humanId={}",
                        attrType, addPercent, value, finalValue, m_humanObj.id);
            }
        }
        m_baseAttrs = initialAttrs;
        Log.activity.info("===转生魔剑局内初始化装备属性，initialAttrs={} humanId={}", initialAttrs, m_humanObj.id);
    }

    /**
     * 增加基础属性
     * @param type
     * @param value
     */
    public void addPlayerBaseAttr(long type, long value){
        // 基础属性不能小于0
        m_baseAttrs.put(type, Math.max(0, getPlayerBaseAttr(type) + value));
    }

    /**
     * 获取基础属性
     * @param type
     * @return
     */
    public long getPlayerBaseAttr(long type){
        return m_baseAttrs.getOrDefault(type, 0L);
    }

    /**
     * 设置基础属性
     * @param type
     * @param value
     */
    public void setPlayerBaseAttr(long type, long value){
        m_baseAttrs.put(type, value);
    }

    /**
     * 更新玩家当前血量
     * @param value
     * @param type
     */
    public void updatePlayerHp(long value, int type){
        updatePlayerHp(value, type, 0);
    }

    /**
     * 更新玩家当前血量
     * @param value
     * @param type
     * @param minHp
     */
    public void updatePlayerHp(long value, int type, long minHp){
        long hpMax = getPlayerAttr(ActivityConst.DUNGEON_ATTR_HP_MAX);
        long curHp = getPlayerAttr(ActivityConst.DUNGEON_ATTR_HP);
        if(curHp == 0){
            // 玩家已死亡
            return;
        }
        long newHp = Math.min(Math.max(minHp, curHp + value), hpMax);
        setPlayerBaseAttr(ActivityConst.DUNGEON_ATTR_HP, newHp);
        if(newHp == 0){
            onPlayerDead();
        }
        if(type != ActivityConst.DUNGEON_HP_CHANGE_TYPE_NONE){
            Define.p_dungeon_hp_change_info.Builder info = Define.p_dungeon_hp_change_info.newBuilder();
            info.setPos(0);
            info.setType(type);
            info.setValue(value);
            m_hpChangeList.add(info);
        }
//        Log.activity.info("===更新玩家血量，value={} newHp={} humanId={}", value, newHp, m_humanObj.id);
    }

    /**
     * 玩家死亡
     */
    public void onPlayerDead(){
        Log.activity.info("===转生魔剑玩家死亡，humanId={}", m_humanObj.id);
        // 死亡时立即结算
        settlement(false);
    }

    /**
     * 获取玩家属性
     * @param type
     */
    public long getPlayerAttr(long type){
        long baseValue = m_baseAttrs.getOrDefault(type, 0L);
        if(type == ActivityConst.DUNGEON_ATTR_HP){
            return baseValue;
        }
        long addValue = 0;
        long addPercent = 0;
        // 持续性效果
        for(Define.p_dungeon_effect.Builder effect : m_persistEffects){
            ConfDungeonEffect confEffect = ConfDungeonEffect.get(effect.getSn());
            if(confEffect != null && confEffect.value != null && confEffect.value[0] != null){
                if(confEffect.type == ActivityConst.DUNGEON_EFFECT_ADD_ATTR_VALUE_DURATION || confEffect.type == ActivityConst.DUNGEON_EFFECT_ADD_ATTR_PERCENT_DURATION){
                    int attrType = confEffect.value[0][0];
                    int value = confEffect.value[0][1];
                    if(type == attrType){
                        if(confEffect.type == ActivityConst.DUNGEON_EFFECT_ADD_ATTR_VALUE_DURATION){
                            addValue += value;
                        }
                        else{
                            addPercent += value;
                        }
                    }
                }
                else if(confEffect.type == ActivityConst.DUNGEON_EFFECT_CURSE){
                    if(type == ActivityConst.DUNGEON_ATTR_ATK){
                        double atkPercent = confEffect.value[0][0];
                        int value = (int) Math.ceil(effect.getCasterAttr() * atkPercent / ActivityConst.DUNGEON_RATE_10000);
                        addValue += (-value);
                    }
                }
                else if(confEffect.type == ActivityConst.DUNGEON_EFFECT_BURNING){
                    if(type == ActivityConst.DUNGEON_ATTR_ATK){
                        double atkPercent = confEffect.value[0][1];
                        int value = (int) Math.ceil(effect.getCasterAttr() * atkPercent / ActivityConst.DUNGEON_RATE_10000);
                        addValue += (-value);
                    }
                }
            }
        }
        // 吞噬魔石增益
        for(Map.Entry<Long, Long> profit : m_devourStoneInfo.entrySet()){
            ConfDungeonBuff confBuff = ConfDungeonBuff.get(Utils.intValue(profit.getKey()));
            if(confBuff != null && confBuff.value_base != null && confBuff.value_base.length == 2 && confBuff.value_factor != null){
                if(confBuff.type == ActivityConst.DUNGEON_PROFIT_ADD_ATTR_VALUE || confBuff.type == ActivityConst.DUNGEON_PROFIT_ADD_ATTR_PERCENT){
                    int attrType = confBuff.value_base[0];
                    int value = confBuff.value_base[1] + confBuff.value_factor[0] * (Utils.intValue(profit.getValue()) - 1);
                    if(type == attrType){
                        if(confBuff.type == ActivityConst.DUNGEON_PROFIT_ADD_ATTR_VALUE){
                            addValue += value;
                        }
                        else{
                            addPercent += value;
                        }
                    }
                }
            }
        }
        // 属性值最小是0
        long finalValue = Math.max(0, (baseValue + addValue) * (ActivityConst.DUNGEON_RATE_10000 + addPercent) / ActivityConst.DUNGEON_RATE_10000);
        if(type == ActivityConst.DUNGEON_ATTR_ATK || type == ActivityConst.DUNGEON_ATTR_DEF || type == ActivityConst.DUNGEON_ATTR_HP_MAX){
            // 攻防血最低是1
            finalValue = Math.max(1, finalValue);
        }
//        Log.activity.info("===getPlayerAttr[{}] baseValue={} addValue={} addPercent={} finalValue={} humanId={}",
//                type, baseValue, addValue, addPercent, finalValue, m_humanObj.id);
        return finalValue;
    }

    /**
     * 获取怪物基础属性
     * @param sn
     * @param type
     * @return
     */
    public long getMonsterBaseAttr(int sn, long type){
        ConfDungeonEnemy confEnemy = ConfDungeonEnemy.get(sn);
        ConfDungeonChapter confChapter = ConfDungeonChapter.get(m_chapterId);
        int chapterFactor = confChapter != null ? confChapter.factor : 0;
        if(type == ActivityConst.DUNGEON_ATTR_HP_MAX){
           return Utils.longValue(confEnemy.hp_base + confEnemy.hp_factor1 * chapterFactor);
        }
        if(type == ActivityConst.DUNGEON_ATTR_ATK){
            return Utils.longValue(confEnemy.atk_base + confEnemy.atk_factor1 * chapterFactor);
        }
        if(type == ActivityConst.DUNGEON_ATTR_DODGE_RATE){
            return Utils.longValue(confEnemy.dodge);
        }
        if(type == ActivityConst.DUNGEON_ATTR_HIT_RATE){
            return Utils.longValue(confEnemy.hit);
        }
        return 0;
    }

    /**
     * 获取怪物属性
     * @param monsterGrid
     * @param type
     * @return
     */
    public long getMonsterAttr(Define.p_dungeon_grid.Builder monsterGrid, long type){
        if(type == ActivityConst.DUNGEON_ATTR_HP){
            return monsterGrid.getMonsterHp();
        }
        long baseValue = getMonsterBaseAttr(monsterGrid.getSn(), type);
        long addValue = 0;
        long addPercent = 0;
        for(Define.p_dungeon_effect effect : monsterGrid.getMonsterEffectsList()){
            ConfDungeonEffect confEffect = ConfDungeonEffect.get(effect.getSn());
            if(confEffect != null && confEffect.value != null && confEffect.value[0] != null){
                if(type == ActivityConst.DUNGEON_ATTR_ATK && confEffect.type == ActivityConst.DUNGEON_EFFECT_REDUCE_TARGET_ATK){
                    int value = confEffect.value[0][0];
                    addPercent += (-value);
                }
            }
        }
        // 怪物自己的被动技能对属性的修正
        for(ConfDungeonSkill confSkill : getMonsterPassiveSkillList(monsterGrid)){
            int skillType = confSkill.type;
            int[] skillValue = confSkill.value;
            if(type == ActivityConst.DUNGEON_ATTR_ATK){
                if(skillType == ActivityConst.DUNGEON_SKILL_ADD_ATK_BASE_ON_SAME_NAME){
                    int value = skillValue[0];
                    addPercent += value * getSameNameMonsterNum(monsterGrid);
                }
                else if(skillType == ActivityConst.DUNGEON_SKILL_REDUCE_ATK_BASE_ON_DEAD_MONSTER){
                    int value = skillValue[0];
                    addPercent += (-value) * getDeadMonsterNum(monsterGrid);
                }
                else if(skillType == ActivityConst.DUNGEON_SKILL_ADD_ATK_ON_LOW_HP){
                    int lowPercent = skillValue[0];
                    int value = skillValue[1];
                    long hpMax = getMonsterAttr(monsterGrid, ActivityConst.DUNGEON_ATTR_HP_MAX);
                    long curHp = getMonsterAttr(monsterGrid, ActivityConst.DUNGEON_ATTR_HP);
                    if(curHp < hpMax * lowPercent / ActivityConst.DUNGEON_RATE_10000){
                        addPercent += value;
                    }
                }
            }
            else if(type == ActivityConst.DUNGEON_ATTR_DODGE_RATE){
                if(skillType == ActivityConst.DUNGEON_SKILL_ADD_DODGE){
                    int value = skillValue[0];
                    addValue += value;
                }
                else if(skillType == ActivityConst.DUNGEON_SKILL_DODGE_ZERO){
                    int openGridNum = skillValue[0];
                    int curOpenGridNum = (int)getAllGrids(monsterGrid.getSourceCavePos()).stream()
                            .filter(grid->grid.getState() == ActivityConst.DUNGEON_GRID_STATE_EXPLORED).count();
                    if(curOpenGridNum >= openGridNum){
                        // 闪避固定为0
                        Log.activity.info("===getMonsterAttr[{}] baseValue={} 闪避固定为0 humanId={}", type, baseValue, m_humanObj.id);
                        return 0;
                    }
                }
            }
        }
        ConfDungeonEnemy confSelf = ConfDungeonEnemy.get(monsterGrid.getSn());
        // 地图上除了自己以外的其他怪物的被动技能对属性的修正
        for(Define.p_dungeon_grid.Builder grid : getAllGrids(monsterGrid.getSourceCavePos())){
            if(grid.getType() != ActivityConst.DUNGEON_GRID_TYPE_MONSTER || grid.getPos() == monsterGrid.getPos()
                    || grid.getState() == ActivityConst.DUNGEON_GRID_STATE_NEVER_EXPLORED){
                continue;
            }
            for(ConfDungeonSkill confSkill : getMonsterPassiveSkillList(grid)){
                int skillType = confSkill.type;
                int[] skillValue = confSkill.value;
                ConfDungeonEnemy conf = ConfDungeonEnemy.get(grid.getSn());
                // 非同名
                boolean isNotSameName = conf != null && conf.enemy_model != confSelf.enemy_model;
                if(type == ActivityConst.DUNGEON_ATTR_ATK){
                    if(skillType == ActivityConst.DUNGEON_SKILL_ADD_ATK_EXCEPT_SAME_NAME && isNotSameName){
                        int value = skillValue[0];
                        addPercent += value;
                    }
                }
                else if(type == ActivityConst.DUNGEON_ATTR_HP_MAX){
                    if(skillType == ActivityConst.DUNGEON_SKILL_ADD_HP_EXCEPT_SAME_NAME && isNotSameName){
                        int value = skillValue[0];
                        addPercent += value;
                    }
                }
            }
        }
        // 属性值最小是0
        long finalValue = Math.max(0, (baseValue + addValue) * (ActivityConst.DUNGEON_RATE_10000 + addPercent) / ActivityConst.DUNGEON_RATE_10000);
        if(type == ActivityConst.DUNGEON_ATTR_ATK || type == ActivityConst.DUNGEON_ATTR_DEF || type == ActivityConst.DUNGEON_ATTR_HP_MAX){
            // 攻防血最低是1
            finalValue = Math.max(1, finalValue);
        }
//        Log.activity.info("===getMonsterAttr[{}] baseValue={} addValue={} addPercent={} finalValue={} humanId={}",
//                type, baseValue, addValue, addPercent, finalValue, m_humanObj.id);
        return finalValue;
    }

    /**
     * 获取怪物被动技能列表
     * @param monsterGrid
     * @return
     */
    static public Collection<ConfDungeonSkill> getMonsterPassiveSkillList(Define.p_dungeon_grid.Builder monsterGrid){
        ConfDungeonEnemy confEnemy = ConfDungeonEnemy.get(monsterGrid.getSn());
        if(confEnemy == null){
            return Collections.emptyList();
        }
        return Arrays.stream(confEnemy.skill).boxed()
                .filter(skillId->skillId != confEnemy.action_skill)
                .map(ConfDungeonSkill::get)
                .filter(skill->skill != null && skill.value != null && skill.value.length > 0)
                .collect(Collectors.toList());
    }

    /**
     * 获取场上同名怪的数量（排除自己）
     * @param monsterGrid
     * @return
     */
    public long getSameNameMonsterNum(Define.p_dungeon_grid.Builder monsterGrid){
        ConfDungeonEnemy confSelf = ConfDungeonEnemy.get(monsterGrid.getSn());
        if(confSelf == null){
            return 0;
        }
        return getAllGrids(monsterGrid.getSourceCavePos()).stream()
                .filter(grid->{
                    if(monsterGrid.getPos() == grid.getPos()){
                        return false;
                    }
                    if(grid.getState() == ActivityConst.DUNGEON_GRID_STATE_NEVER_EXPLORED){
                        return false;
                    }
                    ConfDungeonEnemy conf = ConfDungeonEnemy.get(grid.getSn());
                    return conf != null && conf.enemy_model == confSelf.enemy_model;
                }).count();
    }

    /**
     * 获取场上已死亡的怪物数量
     * @param monsterGrid
     * @return
     */
    public long getDeadMonsterNum(Define.p_dungeon_grid.Builder monsterGrid){
        // 怪物死亡后类型改为空地，但攻击力还在，可做已死亡标记
        return getAllGrids(monsterGrid.getSourceCavePos()).stream()
                .filter(grid->grid.getType() == ActivityConst.DUNGEON_GRID_TYPE_BLANK && grid.getMonsterAtk() > 0).count();
    }

    public void updateStoneNum(int addNum){
        m_stoneNum = Math.max(0, m_stoneNum + addNum);
    }

    public void clearGrid(int sourceCavePos){
        if(sourceCavePos == 0){
            m_mapGirds.clear();
            return;
        }
        Define.p_dungeon_grid.Builder cave = getGrid(sourceCavePos, 0);
        if(cave == null){
            return;
        }
        cave.clearCaveGrids();
    }

    public void addGrid(Define.p_dungeon_grid.Builder grid, int sourceCavePos){
        if(sourceCavePos == 0){
            m_mapGirds.put(grid.getPos(), grid);
            return;
        }
        Define.p_dungeon_grid.Builder cave = getGrid(sourceCavePos, 0);
        if(cave == null){
            return;
        }
        cave.addCaveGrids(grid);
    }

    public Collection<Define.p_dungeon_grid.Builder> getAllGrids(int sourceCavePos){
        if(sourceCavePos == 0){
            return m_mapGirds.values();
        }
        Define.p_dungeon_grid.Builder cave = getGrid(sourceCavePos, 0);
        if(cave == null){
            return Collections.emptyList();
        }
        return cave.getCaveGridsBuilderList();
    }

    /**
     * 获取怪物格
     * @param pos
     * @param sourceCavePos
     * @return
     */
    public Define.p_dungeon_grid.Builder getMonsterGrid(int pos, int sourceCavePos){
        Define.p_dungeon_grid.Builder monsterGrid = getGrid(pos, sourceCavePos);
        if(monsterGrid == null || monsterGrid.getType() != ActivityConst.DUNGEON_GRID_TYPE_MONSTER){
            return null;
        }
        return monsterGrid;
    }

    public Define.p_dungeon_grid.Builder getGrid(int pos, int sourceCavePos){
        if(sourceCavePos == 0){
            return m_mapGirds.get(pos);
        }
        Define.p_dungeon_grid.Builder cave = getGrid(sourceCavePos, 0);
        if(cave == null){
            return null;
        }
        List<Define.p_dungeon_grid.Builder> grids = cave.getCaveGridsBuilderList();
        for(Define.p_dungeon_grid.Builder grid : grids){
            if(grid.getPos() == pos){
                return grid;
            }
        }
        return null;
    }

    public Define.p_dungeon_grid.Builder getGrid(int x, int y, int sourceCavePos){
        return getGrid(buildPos(x, y), sourceCavePos);
    }

    public ConfDungeonConfig getRandomConfig(){
        ConfDungeonChapter confChapter = ConfDungeonChapter.get(m_chapterId);
        if(confChapter == null){
            Log.activity.warn("===转生魔剑获取配置表失败，ConfDungeonChapter[{}]不存在 humanId={}", m_chapterId, m_humanObj.id);
            return null;
        }
        List<ConfDungeonConfig> configList = new ArrayList<>();
        List<Integer> weightList = new ArrayList<>();
        for(ConfDungeonConfig conf : ConfDungeonConfig.findAll()){
            if(conf.group == confChapter.event_group && conf.day == m_layer){
                configList.add(conf);
                weightList.add(conf.weight);
            }
        }
        int index = Utils.getRandRange(weightList);
        if(index < 0){
            return null;
        }
        return configList.get(index);
    }

    static List<Integer> randomMonsters(int[] enemy_id, int[] enemy_num){
        if(enemy_id == null || enemy_num == null || enemy_id.length != enemy_num.length){
            return new ArrayList<>();
        }
        List<Integer> monsterIdList = new ArrayList<>();
        for(int i=0; i<enemy_num.length; i++){
            int num = enemy_num[i];
            for(int j=0; j<num; j++){
                monsterIdList.add(enemy_id[i]);
            }
        }
        return monsterIdList;
    }

    public boolean isBossLayer(){
        ConfDungeonChapter confChapter = ConfDungeonChapter.get(m_chapterId);
        if(confChapter == null){
            return false;
        }
        return m_layer >= confChapter.layer;
    }

    /**
     * 初始化地图
     */
    public boolean initMap(){
        if(isBossLayer()){
            return initBossLayerMap();
        }
        return initNormalLayerMap();
    }

    /**
     * 初始化地图格子
     * @param sourceCavePos
     */
    public void initMapGrids(int sourceCavePos){
        // 先清除所有格子
        clearGrid(sourceCavePos);
        int MAX_X = sourceCavePos > 0 ? ActivityConst.DUNGEON_CAVE_MAX_X : ActivityConst.DUNGEON_SCENE_MAX_X;
        int MAX_Y = sourceCavePos > 0 ? ActivityConst.DUNGEON_CAVE_MAX_Y : ActivityConst.DUNGEON_SCENE_MAX_Y;
        for(int x=1; x<=MAX_X; x++) {
            for(int y=1; y<=MAX_Y; y++) {
                Define.p_dungeon_grid.Builder grid = Define.p_dungeon_grid.newBuilder();
                grid.setSourceCavePos(sourceCavePos);
                grid.setType(ActivityConst.DUNGEON_GRID_TYPE_BLANK);
                grid.setPos(buildPos(x, y));
                if(sourceCavePos > 0){
                    // 洞窟地图默认全探明
                    grid.setState(ActivityConst.DUNGEON_GRID_STATE_EXPLORED);
                }
                addGrid(grid, sourceCavePos);
            }
        }
    }

    /**
     * 初始化事件
     * @param initialEvents
     * @param sourceCavePos
     * @param conf
     */
    public void initEvents(List<List<Integer>> initialEvents, int sourceCavePos, ConfDungeonConfig conf){
        for(List<Integer> event : initialEvents) {
            int pos = event.get(0);
            int sn = event.get(1);
            Define.p_dungeon_grid.Builder eventGrid = getGrid(pos, sourceCavePos);
            if(eventGrid == null) {
                Log.activity.warn("===转生魔剑初始化事件失败，pos={} sn={}", pos, sn);
                continue;
            }
            ConfDungeonEvent confEvent = ConfDungeonEvent.get(sn);
            if(confEvent == null) {
                Log.activity.warn("===转生魔剑初始化事件失败，ConfDungeonEvent[{}]不存在", sn);
                continue;
            }
            eventGrid.setSn(sn);
            eventGrid.setType(ActivityConst.DUNGEON_GRID_TYPE_EVENT);
            if(confEvent.event_type == ActivityConst.DUNGEON_EVENT_TYPE_TRAP){
                eventGrid.setTrapTriggerNum(confEvent.parameter != null && confEvent.parameter[0] != null && confEvent.parameter[0][0] > 0 ?
                        confEvent.parameter[0][0] : 1);
            }
            else if(confEvent.event_type == ActivityConst.DUNGEON_EVENT_TYPE_SHOP){
                // 刷新商店商品列表
                List<Integer> shopItemList = getRandomListByWeight(Arrays.stream(confEvent.shop_id).boxed().collect(Collectors.toList()),
                        Arrays.stream(confEvent.shop_weight).boxed().collect(Collectors.toList()), confEvent.shop_num);
                for(int itemId : shopItemList){
                    eventGrid.addShopItemList(Define.p_key_value.newBuilder().setK(itemId).setV(1));
                }
            }
            else if(confEvent.event_type == ActivityConst.DUNGEON_EVENT_TYPE_CAVE){
                // 初始化洞窟地图
                initCaveMap(confEvent, eventGrid, conf);
            }

            if(confEvent.cover == 0){
                // 不需要被石块覆盖的事件，直接设置为已探索
                eventGrid.setState(ActivityConst.DUNGEON_GRID_STATE_EXPLORED);
            }
            if(confEvent.cond_type == ActivityConst.DUNGEON_EVENT_CONDITION_DECRYPT){
                // 初始化顺序解密信息
                List<Integer> decryptInfo = Arrays.asList(1,2,3,4);
                Collections.shuffle(decryptInfo);
                eventGrid.addAllDecryptInfo(decryptInfo);
            }
        }
    }

    /**
     * 初始化怪物
     * @param initialMonsters
     * @param sourceCavePos
     * @param eventKey
     */
    public void initMonsters(List<List<Integer>> initialMonsters, int sourceCavePos, int eventKey){
        for(List<Integer> monster : initialMonsters) {
            int pos = monster.get(0);
            int sn = monster.get(1);
            Define.p_dungeon_grid.Builder monsterGrid = getGrid(pos, sourceCavePos);
            if(monsterGrid != null) {
                initMonster(monsterGrid, sn, eventKey);
            }
        }
        // 初始化怪物后，更新所有怪物的属性
        updateAllMonsterAttr(sourceCavePos, false);
    }

    private void initMonster(Define.p_dungeon_grid.Builder monsterGrid, int sn, int keySn){
        ConfDungeonEnemy conf = ConfDungeonEnemy.get(sn);
        if(conf == null) {
            Log.activity.warn("===转生魔剑初始化怪物失败，pos={} sn={}", monsterGrid.getPos(), sn);
            return;
        }
        // 如果初始化怪物时，该格子是钥匙事件，则记录keySn
        boolean isKeyEvent = monsterGrid.getType() == ActivityConst.DUNGEON_GRID_TYPE_EVENT && monsterGrid.getSn() == keySn;
        if(isKeyEvent){
            monsterGrid.setKeySn(keySn);
            Log.activity.info("===转生魔剑初始化怪物，怪物和钥匙叠在一起，pos={} sn={}", monsterGrid.getPos(), sn);
        }
        monsterGrid.setSn(sn);
        monsterGrid.setType(ActivityConst.DUNGEON_GRID_TYPE_MONSTER);
        monsterGrid.setMonsterHpMax(getMonsterAttr(monsterGrid, ActivityConst.DUNGEON_ATTR_HP_MAX));
        monsterGrid.setMonsterHp(getMonsterAttr(monsterGrid, ActivityConst.DUNGEON_ATTR_HP_MAX));
        monsterGrid.setMonsterAtk(getMonsterAttr(monsterGrid, ActivityConst.DUNGEON_ATTR_ATK));
        monsterGrid.setMonsterRoundCd(conf.cd);
    }

    /**
     * 初始化普通层地图
     */
    public boolean initNormalLayerMap(){
        // 1.初始化地图格子
        initMapGrids(0);
        ConfDungeonConfig conf = getRandomConfig();
        if(conf == null){
            Log.activity.warn("===转生魔剑初始化普通地图失败，ConfDungeonConfig[{}]不存在 humanId={}", m_chapterId, m_humanObj.id);
            return false;
        }
        // 2.初始化事件
        List<List<Integer>> initialEvents = new ArrayList<>();
        if(!genNormalLayerInitialEvents(conf, initialEvents)){
            Log.activity.warn("===转生魔剑初始化普通地图失败，生成[{},{}]初始化事件错误 humanId={}", m_chapterId, m_layer, m_humanObj.id);
            return false;
        }
        initEvents(initialEvents, 0, conf);
        // 3.初始化怪物
        List<List<Integer>> initialMonsters = new ArrayList<>();
        if(!genNormalLayerInitialMonsters(conf, initialMonsters)){
            Log.activity.warn("===转生魔剑初始化普通地图失败，生成[{},{}]初始化怪物错误 humanId={}", m_chapterId, m_layer, m_humanObj.id);
            return false;
        }
        initMonsters(initialMonsters, 0, conf.event_key);
        // 4.随机一个空地为已探索的起始格
        if(!randomStartGrid()){
            Log.activity.warn("===转生魔剑初始化普通地图失败，生成[{},{}]起始格错误 humanId={}", m_chapterId, m_layer, m_humanObj.id);
            return false;
        }
        Log.activity.info("===转生魔剑初始化普通地图[{},{}]成功，initialEvents={} initialMonsters={} humanId={}",
                m_chapterId, m_layer, initialEvents, initialMonsters, m_humanObj.id);
        return true;
    }

    /**
     * 生成普通层初始化事件
     * @param conf
     * @param initialEvents
     * @return
     */
    public boolean genNormalLayerInitialEvents(ConfDungeonConfig conf, List<List<Integer>> initialEvents){
        // 增加机关事件，不出现在地图边缘，先生成
        List<Integer> trapEventPosList = new ArrayList<>();
        if(!genTrapInitialEvents(conf, initialEvents, trapEventPosList)){
            return false;
        }
        // 事件可分配在空地和非机关的格子上
        List<Integer> eventPosList = getAllGrids(0).stream()
                .filter(grid -> grid.getType() == ActivityConst.DUNGEON_GRID_TYPE_BLANK && !trapEventPosList.contains(grid.getPos()))
                .map(Define.p_dungeon_grid.Builder::getPos).collect(Collectors.toList());
        List<Integer> eventIdList = new ArrayList<>();
        // 增加门和钥匙事件
        eventIdList.add(conf.event_door);
        eventIdList.add(conf.event_key);
        // 增加血瓶事件
        if(conf.heal_num > 0){
            for(int i = 0; i < conf.heal_num; i++){
                eventIdList.add(ActivityConst.DUNGEON_EVENT_ID_HEAL);
            }
        }
        // 增加魔石事件
        if(conf.stone_num > 0){
            for(int i = 0; i < conf.stone_num; i++){
                eventIdList.add(ActivityConst.DUNGEON_EVENT_ID_STONE);
            }
        }
        // 增加商店事件
        if(conf.event_shop > 0){
            eventIdList.add(conf.event_shop);
        }
        // 增加随机事件
        List<Integer> randomEventIdList = genRandomEvents(conf);
        if(randomEventIdList != null && !randomEventIdList.isEmpty()){
            eventIdList.addAll(randomEventIdList);
        }
        // 给所有事件随机分配pos
        List<Integer> eventPosResultList = getRandomPosList(eventPosList, eventIdList.size());
        if(eventPosResultList.isEmpty()){
            Log.activity.warn("===转生魔剑普通层[{},{}] eventPosResultList.isEmpty()", m_chapterId, m_layer);
            return false;
        }
        for(int i=0; i<eventPosResultList.size(); i++){
            initialEvents.add(Arrays.asList(eventPosResultList.get(i), eventIdList.get(i)));
        }
        return true;
    }

    /**
     * 生成机关的初始化事件
     * @param conf
     * @param initialEvents
     * @return
     */
    public boolean genTrapInitialEvents(ConfDungeonConfig conf, List<List<Integer>> initialEvents, List<Integer> trapEventPosList){
        // 机关事件可分配在非地图边缘的空地的格子上
        List<Integer> eventPosList = getAllGrids(0).stream()
                .filter(grid -> {
                    if(grid.getType() != ActivityConst.DUNGEON_GRID_TYPE_BLANK){
                        return false;
                    }
                    int pos = grid.getPos();
                    if(getX(pos) == 1 || getX(pos) == ActivityConst.DUNGEON_SCENE_MAX_X || getY(pos) == 1 || getY(pos) == ActivityConst.DUNGEON_SCENE_MAX_Y){
                        return false;
                    }
                    return true;
                })
                .map(Define.p_dungeon_grid.Builder::getPos).collect(Collectors.toList());
        List<Integer> eventIdList = new ArrayList<>();
        // 增加机关事件
        List<Integer> trapEventIdList = getRandomListByWeight(Arrays.stream(conf.event_trap_id).boxed().collect(Collectors.toList()),
                Arrays.stream(conf.event_trap_weight).boxed().collect(Collectors.toList()), 1);
        if(trapEventIdList.isEmpty()){
            // 机关事件数量允许为空
            Log.activity.info("===转生魔剑普通层[{},{}] 无机关事件", m_chapterId, m_layer);
            return true;
        }
        eventIdList.addAll(trapEventIdList);
        // 给所有机关事件随机分配pos
        List<Integer> eventPosResultList = getRandomPosList(eventPosList, eventIdList.size());
        if(eventPosResultList.isEmpty()){
            Log.activity.warn("===转生魔剑普通层[{},{}]机关事件 eventPosResultList.isEmpty()", m_chapterId, m_layer);
            return false;
        }
        for(int i=0; i<eventPosResultList.size(); i++){
            initialEvents.add(Arrays.asList(eventPosResultList.get(i), eventIdList.get(i)));
        }
        trapEventPosList.addAll(eventPosResultList);
//        Log.activity.info("===转生魔剑普通层[{},{}] 机关事件位置={}", m_chapterId, m_layer, trapEventPosList);
        return true;
    }

    public List<Integer> genRandomEvents(ConfDungeonConfig conf){
        List<Integer> randomEventIdList = new ArrayList<>();
        List<Integer> eventNumList = new ArrayList<>();
        List<Integer> eventNumWeightList = new ArrayList<>();
        for(int[] param : conf.event_num){
            eventNumList.add(param[0]);
            eventNumWeightList.add(param[1]);
        }
        int index = Utils.getRandRange(eventNumWeightList);
        if(index < 0){
            Log.activity.warn("===转生魔剑[{},{}] getRandRange index < 0", m_chapterId, m_layer);
            return null;
        }
        int eventNum = eventNumList.get(index);
        if(eventNum > 0){
            randomEventIdList = getRandomListByWeight(Arrays.stream(conf.event_id).boxed().collect(Collectors.toList()),
                    Arrays.stream(conf.event_weight).boxed().collect(Collectors.toList()), eventNum);
            if(randomEventIdList.isEmpty()){
                Log.activity.warn("===转生魔剑[{},{}] randomEventIdList.isEmpty()", m_chapterId, m_layer);
                return null;
            }
        }
        return randomEventIdList;
    }

    /**
     * 生成普通层初始化怪物
     * @param conf
     * @param initialMonsters
     * @return
     */
    public boolean genNormalLayerInitialMonsters(ConfDungeonConfig conf, List<List<Integer>> initialMonsters){
        // 怪物可分配在空地和钥匙事件的格子上
        List<Integer> monsterPosList = getAllGrids(0).stream()
                .filter(grid -> grid.getType() == ActivityConst.DUNGEON_GRID_TYPE_BLANK ||
                        (grid.getType() == ActivityConst.DUNGEON_GRID_TYPE_EVENT && grid.getSn() == conf.event_key))
                .map(Define.p_dungeon_grid.Builder::getPos).collect(Collectors.toList());
        List<Integer> monsterIdList = randomMonsters(conf.enemy_id, conf.enemy_num);
        if(monsterIdList.isEmpty()){
            Log.activity.warn("===转生魔剑普通层[{},{}] monsterIdList.isEmpty()", m_chapterId, m_layer);
            return false;
        }
        List<Integer> monsterPosResultList = getRandomPosList(monsterPosList, monsterIdList.size());
        if(monsterPosResultList.isEmpty()){
            Log.activity.warn("===转生魔剑普通层[{},{}] monsterPosResultList.isEmpty()", m_chapterId, m_layer);
            return false;
        }
        for(int i=0; i<monsterPosResultList.size(); i++){
            initialMonsters.add(Arrays.asList(monsterPosResultList.get(i), monsterIdList.get(i)));
        }
        return true;
    }

    /**
     * 随机一个空地为已探索的起始格
     * @return
     */
    public boolean randomStartGrid(){
        int exploredNum = 1;
        List<Integer> blankPosList = getAllGrids(0).stream()
                .filter(grid -> grid.getType() == ActivityConst.DUNGEON_GRID_TYPE_BLANK)
                .map(Define.p_dungeon_grid.Builder::getPos).collect(Collectors.toList());
        List<Integer> exploredPosResultList = getRandomPosList(blankPosList, exploredNum);
        if(exploredPosResultList.isEmpty()){
            return false;
        }
        for(int pos : exploredPosResultList){
            Define.p_dungeon_grid.Builder grid = getGrid(pos, 0);
            if(grid != null){
                grid.setState(ActivityConst.DUNGEON_GRID_STATE_EXPLORED);
            }
        }
        return true;
    }

    /**
     * 初始化boss层地图
     */
    public boolean initBossLayerMap(){
        // 1.初始化地图格子
        initMapGrids(0);
        ConfDungeonConfig conf = getRandomConfig();
        if(conf == null){
            Log.activity.warn("===转生魔剑初始化Boss地图失败，ConfDungeonConfig==null humanId={}", m_humanObj.id);
            return false;
        }
        // 2.初始化boss
        initialBoss(conf);
        // 3.初始化事件
        List<List<Integer>> initialEvents = new ArrayList<>();
        if(!genBossLayerInitialEvents(conf, initialEvents)){
            Log.activity.warn("===转生魔剑初始化Boss地图失败，生成[{},{}]初始化事件错误 humanId={}", m_chapterId, m_layer, m_humanObj.id);
            return false;
        }
        initEvents(initialEvents, 0, conf);
        // 4.初始化怪物
        List<List<Integer>> initialMonsters = new ArrayList<>();
        if(!genBossLayerInitialMonsters(conf, initialMonsters)){
            Log.activity.warn("===转生魔剑初始化Boss地图失败，生成[{},{}]初始化怪物错误 humanId={}", m_chapterId, m_layer, m_humanObj.id);
            return false;
        }
        initMonsters(initialMonsters, 0, conf.event_key);
        Log.activity.info("===转生魔剑初始化Boss地图[{},{}]成功，initialEvents={} initialMonsters={} humanId={}",
                m_chapterId, m_layer, initialEvents, initialMonsters, m_humanObj.id);
        return true;
    }

    /**
     * 初始化boss
     *
     * @param conf
     */
    public void initialBoss(ConfDungeonConfig conf){
        int bossId = conf.boss_id;
        if(bossId == 0){
            return;
        }
        Define.p_dungeon_grid.Builder bossGrid = getGrid(ActivityConst.DUNGEON_BOSS_POS, 0);
        if(bossGrid != null) {
            initMonster(bossGrid, bossId, conf.event_key);
        }
        // boss层的所有格子全探明
        getAllGrids(0).forEach(grid->grid.setState(ActivityConst.DUNGEON_GRID_STATE_EXPLORED));
//        Log.activity.info("===转生魔剑生成Boss[{}]成功 humanId={}", bossId, m_humanObj.id);
    }

    /**
     * 生成boss层初始化事件
     * @param conf
     * @param initialEvents
     * @return
     */
    public boolean genBossLayerInitialEvents(ConfDungeonConfig conf, List<List<Integer>> initialEvents){
        // 事件可分配在第一第二行的格子上
        List<Integer> eventPosList = getAllGrids(0).stream()
                .map(Define.p_dungeon_grid.Builder::getPos)
                .filter(pos -> getY(pos) <= 2).collect(Collectors.toList());
        List<Integer> eventIdList = new ArrayList<>();
        // 增加钥匙事件
        eventIdList.add(conf.event_key);
        // 增加随机事件
        List<Integer> randomEventIdList = genRandomEvents(conf);
        if(randomEventIdList != null && !randomEventIdList.isEmpty()){
            eventIdList.addAll(randomEventIdList);
        }
        // 只截取前两行容纳得下的事件
        eventIdList = eventIdList.subList(0, Math.min(eventIdList.size(), eventPosList.size()));
        // 给所有事件随机分配pos
        List<Integer> eventPosResultList = getRandomPosList(eventPosList, eventIdList.size());
        if(eventPosResultList.isEmpty()){
            Log.activity.warn("===转生魔剑boss层[{},{}] eventPosResultList.isEmpty()", m_chapterId, m_layer);
            return false;
        }
        for(int i=0; i<eventPosResultList.size(); i++){
            initialEvents.add(Arrays.asList(eventPosResultList.get(i), eventIdList.get(i)));
        }
        // 增加门事件，门分配在最下方中间603位置
        initialEvents.add(Arrays.asList(ActivityConst.DUNGEON_BOSS_LAYER_DOOR_POS, conf.event_door));
        return true;
    }

    /**
     * 生成boss层初始化怪物
     * @param conf
     * @param initialMonsters
     * @return
     */
    public boolean genBossLayerInitialMonsters(ConfDungeonConfig conf, List<List<Integer>> initialMonsters){
        // 怪物可分配在boss左右两列的位置
        List<Integer> monsterIdList = randomMonsters(conf.enemy_id, conf.enemy_num);
        if(monsterIdList.isEmpty()){
            Log.activity.warn("===转生魔剑boss层[{},{}] monsterIdList.isEmpty()", m_chapterId, m_layer);
            return false;
        }
        if(monsterIdList.size() > ActivityConst.DUNGEON_BOSS_LAYER_MONSTER_POS_LIST.size()){
            Log.activity.warn("===转生魔剑boss层[{},{}] 怪物数量={}大于最大配置数量={}",
                    m_chapterId, m_layer, monsterIdList.size(), ActivityConst.DUNGEON_BOSS_LAYER_MONSTER_POS_LIST.size());
            return false;
        }
        for(int i=0; i<monsterIdList.size(); i++){
            initialMonsters.add(Arrays.asList(ActivityConst.DUNGEON_BOSS_LAYER_MONSTER_POS_LIST.get(i), monsterIdList.get(i)));
        }
        return true;
    }

    /**
     * 发送场景信息
     */
    public void sendSceneInfo() {
        ConfDungeonChapter confChapter = ConfDungeonChapter.get(m_chapterId);
        if(confChapter == null){
            Log.activity.warn("===发送场景信息失败，ConfDungeonChapter[{}]不存在 humanId={}", m_chapterId, m_humanObj.id);
            return;
        }
        MsgAct2.act_dungeon_scene_info_s2c.Builder msg = MsgAct2.act_dungeon_scene_info_s2c.newBuilder();
        msg.setActType(m_data.objectData.type);
        msg.setChapterId(m_chapterId);
        msg.setLayer(m_layer);
        msg.setRound(m_round);
        msg.setStoneNum(m_stoneNum);
        msg.setCostStoneNum(getCostNum(confChapter.stone_cost, m_devourStoneNum));
        msg.addAllMapGrids(getMapGridsList());
        msg.addAllAttrs(getAttrList());
        msg.addAllBackPack(getBackPackList());
        msg.addAllEquips(getEquipsList());
        msg.addAllPersistEffects(getPersistEffectList());
        m_humanObj.sendMsg(msg);
//        Log.activity.info("===发送场景信息 scene={} humanId={}", this, m_humanObj.id);
    }
    /**
     * 该格子是否能探索
     * @param targetPos
     * @param sourceCavePos
     * @return
     */
    public boolean canExplore(int targetPos, int sourceCavePos){
        int x = getX(targetPos);
        int y = getY(targetPos);
        Define.p_dungeon_grid.Builder grid = getGrid(targetPos, sourceCavePos);
        if(grid == null) {
            Log.activity.warn("===转生魔剑探索失败，grid[{},{}]不存在 humanId={}", targetPos, sourceCavePos, m_humanObj.id);
            return false;
        }
        if(grid.getState() == ActivityConst.DUNGEON_GRID_STATE_EXPLORED) {
            Log.activity.warn("===转生魔剑探索失败，grid[{},{}]已被探明 humanId={}", targetPos, sourceCavePos, m_humanObj.id);
            return false;
        }
        // targetPos的上下左右没有已探明的格子，则未连通
        if(!isExploredGrid(x, y - 1, sourceCavePos) && !isExploredGrid(x, y + 1, sourceCavePos)
                && !isExploredGrid(x - 1, y, sourceCavePos) && !isExploredGrid(x + 1, y, sourceCavePos)) {
            Log.activity.warn("===转生魔剑探索失败，grid[{},{}]未连通 humanId={}", targetPos, sourceCavePos, m_humanObj.id);
            return false;
        }
        // targetPos的周围8格有已探明的怪物格子，则被封锁
        if(isExploredMonsterGrid(x - 1, y - 1, sourceCavePos) || isExploredMonsterGrid(x, y - 1, sourceCavePos)
                || isExploredMonsterGrid(x + 1, y - 1, sourceCavePos) || isExploredMonsterGrid(x - 1, y, sourceCavePos)
                || isExploredMonsterGrid(x + 1, y, sourceCavePos) || isExploredMonsterGrid(x - 1, y + 1, sourceCavePos)
                || isExploredMonsterGrid(x, y + 1, sourceCavePos) || isExploredMonsterGrid(x + 1, y + 1, sourceCavePos)) {
            Log.activity.warn("===转生魔剑探索失败，grid[{},{}]周围有怪物，被封锁 humanId={}", targetPos, sourceCavePos, m_humanObj.id);
            return false;
        }
        return true;
    }

    public boolean isExploredGrid(int x, int y, int sourceCavePos){
        Define.p_dungeon_grid.Builder grid = getGrid(buildPos(x, y), sourceCavePos);
        return grid != null && grid.getState() == ActivityConst.DUNGEON_GRID_STATE_EXPLORED;
    }

    public boolean isExploredMonsterGrid(int x, int y, int sourceCavePos){
        Define.p_dungeon_grid.Builder grid = getGrid(buildPos(x, y), sourceCavePos);
        return grid != null && grid.getState() == ActivityConst.DUNGEON_GRID_STATE_EXPLORED && grid.getType() == ActivityConst.DUNGEON_GRID_TYPE_MONSTER;
    }


    /**
     * 转生魔剑探索
     * @param targetPos
     * @param sourceCavePos
     */
    public boolean explore(int targetPos, int sourceCavePos) {
        if(!canExplore(targetPos, sourceCavePos)) {
            return false;
        }
        // 格子标记为已探索
        Define.p_dungeon_grid.Builder grid = getGrid(targetPos, sourceCavePos);
        grid.setState(ActivityConst.DUNGEON_GRID_STATE_EXPLORED);
        m_exploreAreaNum++;
        // 构建返回消息
        MsgAct2.act_dungeon_explore_s2c.Builder msg = MsgAct2.act_dungeon_explore_s2c.newBuilder();
        msg.setActType(m_data.objectData.type);
        msg.setChapterId(m_chapterId);
        msg.setGrid(grid);
        m_humanObj.sendMsg(msg);
//        Log.activity.info("===转生魔剑探索成功，targetPos={} humanId={}", targetPos, m_humanObj.id);
        return true;
    }

    /**
     * 转生魔剑触发事件
     *
     * @param targetPos
     * @param param
     * @param sourceCavePos
     */
    public boolean triggerEvent(int targetPos, Define.p_dungeon_event_param param, int sourceCavePos) {
        Define.p_dungeon_grid.Builder eventGrid = getGrid(targetPos, sourceCavePos);
        if(eventGrid == null || eventGrid.getState() != ActivityConst.DUNGEON_GRID_STATE_EXPLORED){
            Log.activity.warn("===转生魔剑触发事件失败，grid[{},{}]未被探明 humanId={}", targetPos, sourceCavePos, m_humanObj.id);
            return false;
        }
        if(eventGrid.getType() != ActivityConst.DUNGEON_GRID_TYPE_EVENT){
            Log.activity.warn("===转生魔剑触发事件失败，grid[{},{}]不是事件 humanId={}", targetPos, sourceCavePos, m_humanObj.id);
            return false;
        }
        Define.p_key_value.Builder languageId = Define.p_key_value.newBuilder();
        // 处理事件
        if(!processEvent(eventGrid, param, languageId)){
            return false;
        }
        if(m_isEnterNextLayer || m_isClosed){
            // 进入下一层或者结算，就不通知事件结果了
//            Log.activity.info("===转生魔剑触发事件成功，且进入下一层或结算，humanId={}", m_humanObj);
            return true;
        }
        // 处理后再添加grid为已变化
        m_changeGrids.add(eventGrid);
        // 根据玩家血上限变化，更新玩家当前血量
        updatePlayerHpByHpMaxChange();
        // 构建返回消息
        MsgAct2.act_dungeon_trigger_event_s2c.Builder msg = MsgAct2.act_dungeon_trigger_event_s2c.newBuilder();
        msg.setActType(m_data.objectData.type);
        msg.setChapterId(m_chapterId);
        msg.setChangeInfo(buildChangeInfo(targetPos, sourceCavePos));
        if(languageId.getV() > 0){
            msg.setLanguageId(Utils.intValue(languageId.getV()));
        }
        m_humanObj.sendMsg(msg);
//        Log.activity.info("===转生魔剑触发事件成功，pos={} sn={} humanId={}", eventGrid.getPos(), eventGrid.getSn(), m_humanObj);
        return true;
    }

    /**
     * 处理事件
     * @param eventGrid
     * @param param
     * @param languageId
     * @return
     */
    public boolean processEvent(Define.p_dungeon_grid.Builder eventGrid, Define.p_dungeon_event_param param, Define.p_key_value.Builder languageId){
        ConfDungeonEvent confEvent = ConfDungeonEvent.get(eventGrid.getSn());
        if(confEvent == null){
            Log.activity.warn("===转生魔剑处理事件失败，ConfDungeonEvent[{}]不存在 humanId={}", eventGrid.getSn(), m_humanObj.id);
            return false;
        }
        if(isBossLayer()){
            boolean hasBoss = getAllGrids(0).stream().anyMatch((grid->{
                if(grid.getType() == ActivityConst.DUNGEON_GRID_TYPE_MONSTER){
                    ConfDungeonEnemy conf = ConfDungeonEnemy.get(grid.getSn());
                    return conf != null && conf.enemy_type == ActivityConst.DUNGEON_MONSTER_TYPE_BOSS;
                }
                return false;
            }));
            if(hasBoss){
                Log.activity.warn("===转生魔剑处理事件失败，boss未被击败，无法触发事件[{},{}] humanId={}", eventGrid.getPos(), eventGrid.getSn(), m_humanObj.id);
                return false;
            }
        }
        if(confEvent.event_type == ActivityConst.DUNGEON_EVENT_TYPE_CAVE){
            // 洞窟已经初始化，直接返回
            return true;
        }
        if(confEvent.event_type == ActivityConst.DUNGEON_EVENT_TYPE_SHOP){
            return processShopEvent(eventGrid, param);
        }
        if(!checkEventCondition(confEvent, eventGrid)){
            return false;
        }
        int[] effectInfo = null;
        if(confEvent.event_type == ActivityConst.DUNGEON_EVENT_TYPE_NORMAL || confEvent.event_type == ActivityConst.DUNGEON_EVENT_TYPE_DOOR || confEvent.event_type == ActivityConst.DUNGEON_EVENT_TYPE_PICK_UP){
            effectInfo = getEventEffectInfo(confEvent.event_effect_id);
        }
        else if(confEvent.event_type == ActivityConst.DUNGEON_EVENT_TYPE_RESOURCE || confEvent.event_type == ActivityConst.DUNGEON_EVENT_TYPE_SUMMON
                || confEvent.event_type == ActivityConst.DUNGEON_EVENT_TYPE_TRAP){
            effectInfo = getEventEffectInfo(confEvent.select1_event_effect);
        }
        else if(confEvent.event_type == ActivityConst.DUNGEON_EVENT_TYPE_DECISION){
            int[][] eventEffectParam = param.getChoose() == 1 ? confEvent.select1_event_effect :
                    param.getChoose() == 2 ? confEvent.select2_event_effect : confEvent.select3_event_effect;
            effectInfo = getEventEffectInfo(eventEffectParam);
        }
        if(effectInfo == null){
            Log.activity.warn("===转生魔剑处理事件失败，随机effectInfo失败 sn={} humanId={}", eventGrid.getSn(), m_humanObj.id);
            return false;
        }
        languageId.setV(effectInfo[0]);
        int effectId = effectInfo[1];
        if(!processEffect(effectId, eventGrid)){
            return false;
        }
        // 触发事件后，再扣除提交的物品
        if(confEvent.cond_type == ActivityConst.DUNGEON_EVENT_CONDITION_COMMIT_ITEM){
            int itemId = confEvent.cond_value[0];
            int costNum = confEvent.cond_value[1];
            costDropItem(itemId, costNum);
//            Log.activity.info("===扣除掉落物品，dropItem=[{},{}] humanId={}", itemId, costNum, m_humanObj.id);
        }
        // 触发事件后，减少可触发次数或变成空地（召唤事件不处理，因为本格已经变为了怪物）
        if(confEvent.event_type == ActivityConst.DUNGEON_EVENT_TYPE_TRAP && eventGrid.getTrapTriggerNum() > 1){
            eventGrid.setTrapTriggerNum(eventGrid.getTrapTriggerNum() - 1);
        }
        else if(confEvent.event_type != ActivityConst.DUNGEON_EVENT_TYPE_SUMMON){
            eventGrid.setType(ActivityConst.DUNGEON_GRID_TYPE_BLANK);
            eventGrid.setSn(0);
        }
        return true;
    }

    static public int[] getEventEffectInfo(int[][] eventEffectParam){
        List<Integer> languageIdList = new ArrayList<>();
        List<Integer> effectList = new ArrayList<>();
        List<Integer> weightList = new ArrayList<>();
        for(int[] array : eventEffectParam){
            if(array.length == 3){
                languageIdList.add(array[0]);
                effectList.add(array[1]);
                weightList.add(array[2]);
            }
        }
        int index = Utils.getRandRange(weightList);
        if(index < 0){
            return null;
        }
        return new int[]{languageIdList.get(index), effectList.get(index)};
    }

    /**
     * 条件检查
     * @param confEvent
     * @param eventGrid
     * @return
     */
    public boolean checkEventCondition(ConfDungeonEvent confEvent, Define.p_dungeon_grid.Builder eventGrid){
        int sourceCavePos = eventGrid.getSourceCavePos();
        switch (confEvent.cond_type) {
            case ActivityConst.DUNGEON_EVENT_CONDITION_KILL_ALL_ENEMY:
                if(getAllGrids(sourceCavePos).stream().anyMatch((v->v.getType() == ActivityConst.DUNGEON_GRID_TYPE_MONSTER))){
                    Log.activity.warn("===转生魔剑条件检查失败，未击败当前层所有敌人 humanId={}", m_humanObj.id);
                    return false;
                }
                return true;
            case ActivityConst.DUNGEON_EVENT_CONDITION_DECRYPT:
                return true;
            case ActivityConst.DUNGEON_EVENT_CONDITION_UNLOCK_DOOR:
                if(eventGrid.getDoorState() == ActivityConst.DUNGEON_DOOR_STATE_LOCK){
                    Log.activity.warn("===转生魔剑条件检查失败，门[{},{}]未激活 humanId={}", eventGrid.getPos(), eventGrid.getSn(), m_humanObj.id);
                    return false;
                }
                return true;
            case ActivityConst.DUNGEON_EVENT_CONDITION_COMMIT_ITEM:
                int itemId = confEvent.cond_value[0];
                int costNum = confEvent.cond_value[1];
                if(!canCostDropItem(itemId, costNum)){
                    Log.activity.warn("===转生魔剑条件检查失败，itemId={}数量不足{}个 humanId={}", itemId, costNum, m_humanObj.id);
                    return false;
                }
                return true;
            case ActivityConst.DUNGEON_EVENT_CONDITION_KILL_BOSS:
                boolean hasBoss = getAllGrids(sourceCavePos).stream().anyMatch((grid->{
                    if(grid.getType() == ActivityConst.DUNGEON_GRID_TYPE_MONSTER){
                        ConfDungeonEnemy conf = ConfDungeonEnemy.get(grid.getSn());
                        return conf != null && conf.enemy_type == ActivityConst.DUNGEON_MONSTER_TYPE_BOSS;
                    }
                    return false;
                }));
                if(hasBoss){
                    Log.activity.warn("===转生魔剑条件检查失败，未击败当前层BOSS humanId={}", m_humanObj.id);
                    return false;
                }
                return true;
            default:
                return true;
        }
    }

    /**
     * 处理商店事件
     * @param eventGrid
     * @param param
     * @return
     */
    public boolean processShopEvent(Define.p_dungeon_grid.Builder eventGrid, Define.p_dungeon_event_param param){
        int buyIndex = param.getBuyItemIndex();
        Define.p_key_value.Builder shopItem = eventGrid.getShopItemListBuilder(buyIndex);
        if(shopItem == null || shopItem.getV() == 0){
            Log.activity.warn("===转生魔剑购买商品失败，buyIndex={}的商品不存在或已售罄 humanId={}", buyIndex, m_humanObj.id);
            return false;
        }
        int itemId = Utils.intValue(shopItem.getK());
        ConfDungeonItem confItem = ConfDungeonItem.get(itemId);
        if(confItem == null){
            Log.activity.warn("===转生魔剑购买商品失败，ConfDungeonItem[{}]的物品不存在 humanId={}", itemId, m_humanObj.id);
            return false;
        }
        int price = confItem.price;
        if(m_stoneNum < price){
            Log.activity.warn("===转生魔剑购买商品失败，魔石不足 m_stoneNum={} < price={} humanId={}", m_stoneNum, price, m_humanObj.id);
            return false;
        }
        updateStoneNum(-price);
        shopItem.setV(shopItem.getV() - 1);
        addDropItem(itemId, 1);
//        Log.activity.info("===转生魔剑购买商品成功，itemId={} price={} humanId={}", itemId, price, m_humanObj.id);
        return true;
    }

    /**
     * 获取随机列表
     * @param dataList
     * @param weightList
     * @param randomNum
     * @return
     */
    static public List<Integer> getRandomListByWeight(List<Integer> dataList, List<Integer> weightList, int randomNum){
        List<Integer> resultList = new ArrayList<>();
        if(dataList.size() != weightList.size()){
            Log.activity.warn("===获取随机列表失败，dataList.size()={} != weightList.size()={}", dataList.size(), weightList.size());
            return resultList;
        }
        if(dataList.size() < randomNum){
            Log.activity.warn("===获取随机列表失败，dataList.size()={} < randomNum={}", dataList.size(), randomNum);
            return resultList;
        }
        for(int i = 0; i < randomNum; i++){
            int index = Utils.getRandRange(weightList);
            int randomData = dataList.remove(index);
            weightList.remove(index);
            resultList.add(randomData);
        }
        return resultList;
    }

    /**
     * 初始化洞窟地图
     * @param confEvent
     * @param eventGrid
     * @param conf
     * @return
     */
    public boolean initCaveMap(ConfDungeonEvent confEvent, Define.p_dungeon_grid.Builder eventGrid, ConfDungeonConfig conf){
        int sourceCavePos = eventGrid.getPos();
        // 1.初始化地图格子
        initMapGrids(sourceCavePos);
        // 2.初始化事件
        int[] initParam = getCaveInitParam(confEvent.parameter);
        if (initParam == null) {
            Log.activity.warn("===转生魔剑初始化洞窟失败，initParam==null humanId={}", m_humanObj.id);
            return false;
        }
        int eventId = initParam[2];
        // 洞窟的事件固定在地图中间202位置
        List<List<Integer>> initialEvents = Arrays.asList(Arrays.asList(202, eventId));
        initEvents(initialEvents, sourceCavePos, conf);
        // 3.初始化怪物
        int minNum = initParam[0];
        int maxNum = initParam[1];
        int monsterNum = Utils.randomBetween(minNum, maxNum);
        // 怪物可分配在空地的格子上
        List<Integer> monsterPosList = getAllGrids(sourceCavePos).stream()
                .filter(grid -> grid.getType() == ActivityConst.DUNGEON_GRID_TYPE_BLANK)
                .map(Define.p_dungeon_grid.Builder::getPos).collect(Collectors.toList());
        List<Integer> monsterPosResultList = getRandomPosList(monsterPosList, monsterNum);
        if(monsterPosResultList.isEmpty()){
            return false;
        }
        List<List<Integer>> initialMonsters = new ArrayList<>();
        for(int pos : monsterPosResultList){
            int index = Utils.getRandRange(conf.enemy_id);
            if(index < 0){
                continue;
            }
            int monsterId = conf.enemy_id[index];
            initialMonsters.add(Arrays.asList(pos, monsterId));
        }
        initMonsters(initialMonsters, sourceCavePos, 0);
        Log.activity.info("===转生魔剑初始化洞窟成功，initialEvents={} initialMonsters={} humanId={}", initialEvents, initialMonsters, m_humanObj.id);
        return true;
    }

    static public int[] getCaveInitParam(int[][] parameter){
        List<int[]> paramList = new ArrayList<>();
        List<Integer> eventList = new ArrayList<>();
        List<Integer> weightList = new ArrayList<>();
        for(int[] array : parameter){
            if(array.length == 4){
                paramList.add(array);
                weightList.add(array[3]);
            }
        }
        int index = Utils.getRandRange(weightList);
        if(index < 0){
            return null;
        }
        return paramList.get(index);
    }

    static public List<Integer> getRandomPosList(List<Integer> posList, int randomNum){
        List<Integer> weightList = new ArrayList<>();
        // 纯随机，默认相同权重
        for(int i=0; i<posList.size(); i++) {
            weightList.add(100);
        }
        return getRandomListByWeight(posList, weightList, randomNum);
    }

    public void processEffect(int effectId){
        processEffect(effectId, null);
    }

    /**
     * 处理效果
     * @param effectId
     * @param targetGrid
     * @return
     */
    public boolean processEffect(int effectId, Define.p_dungeon_grid.Builder targetGrid){
        if(effectId == 0){
            return false;
        }
        ConfDungeonEffect confEffect = ConfDungeonEffect.get(effectId);
        if(confEffect == null){
            Log.activity.warn("===转生魔剑添加效果失败，ConfDungeonEffect[{}]不存在 humanId={}", effectId, m_humanObj.id);
            return false;
        }
        if(confEffect.value == null || confEffect.value[0] == null){
            if(confEffect.type != ActivityConst.DUNGEON_EFFECT_UNLOCK_DOOR && confEffect.type != ActivityConst.DUNGEON_EFFECT_NEXT_LAYER
                    && confEffect.type != ActivityConst.DUNGEON_EFFECT_SETTLE && confEffect.type != ActivityConst.DUNGEON_EFFECT_EXPLORE_NINE_SQUARE_GRID
                    && confEffect.type != ActivityConst.DUNGEON_EFFECT_EXPLORE_CROSS_GRID && confEffect.type != ActivityConst.DUNGEON_EFFECT_STUN_TARGET){
                Log.activity.warn("===转生魔剑添加效果失败，ConfDungeonEffect[{}]的效果值错误 humanId={}", effectId, m_humanObj.id);
                return false;
            }
        }
        switch (confEffect.type) {
            case ActivityConst.DUNGEON_EFFECT_ADD_GOLD_RATE:
            case ActivityConst.DUNGEON_EFFECT_ADD_HP_ON_ENTER_NEXT_LAYER:
            case ActivityConst.DUNGEON_EFFECT_ADD_STONE_ON_ENTER_NEXT_LAYER:
            case ActivityConst.DUNGEON_EFFECT_ADD_ATTR_VALUE_DURATION:
            case ActivityConst.DUNGEON_EFFECT_ADD_ATTR_PERCENT_DURATION:
            case ActivityConst.DUNGEON_EFFECT_POISON:
            case ActivityConst.DUNGEON_EFFECT_CURSE:
            case ActivityConst.DUNGEON_EFFECT_BURNING:
                return addPersistEffect(confEffect, targetGrid);
            case ActivityConst.DUNGEON_EFFECT_UNLOCK_DOOR:
                return unlockDoor(targetGrid);
            case ActivityConst.DUNGEON_EFFECT_NEXT_LAYER:
                return enterNextLayer();
            case ActivityConst.DUNGEON_EFFECT_SETTLE:
                return settlement(true);
            case ActivityConst.DUNGEON_EFFECT_ADD_HP_AND_MAX:
                {
                    int addValue = confEffect.value[0][0];
                    int addMaxValue = confEffect.value[0][1];
                    // 血量未满时回血X点（不超过上限），血量已满时提升Y点血上限（同步提升当前血量）
                    long hp = getPlayerAttr(ActivityConst.DUNGEON_ATTR_HP);
                    long hpMax = getPlayerAttr(ActivityConst.DUNGEON_ATTR_HP_MAX);
                    if(hp == hpMax){
                        addPlayerBaseAttr(ActivityConst.DUNGEON_ATTR_HP_MAX, addMaxValue);
                    }
                    updatePlayerHp(addValue, ActivityConst.DUNGEON_HP_CHANGE_TYPE_HEAL);
//                    Log.activity.info("===加血和血上限，addValue={} addMaxValue={}", addValue, addMaxValue);
                }
                break;
            case ActivityConst.DUNGEON_EFFECT_ADD_STONE:
                {
                    int addStoneNum = confEffect.value[0][0];
                    updateStoneNum(addStoneNum);
//                    Log.activity.info("===增加魔石，addStoneNum={}", addStoneNum);
                }
                break;
            case ActivityConst.DUNGEON_EFFECT_ADD_ITEM:
                {
                    int[] rewards = confEffect.value[0];
                    for(int dropId : rewards){
                        addDropItemByGroupId(dropId);
                    }
//                    Log.activity.info("===获得奖励道具，rewards={}", rewards);
                }
                break;
            case ActivityConst.DUNGEON_EFFECT_FIRST_REWARD:
                {
                    boolean isReceived = m_data.isReceivedFirstReward(m_chapterId, effectId);
                    int[] rewards = isReceived ? confEffect.value[1] : confEffect.value[0];
                    for(int dropId : rewards){
                        addDropItemByGroupId(dropId);
                    }
                    if(!isReceived){
                        m_data.addReceivedFirstRewardEffects(m_chapterId, effectId);
                    }
                    Log.activity.info("===获得首次奖励道具，是否首次={} rewards={}", !isReceived, rewards);
                }
                break;
            case ActivityConst.DUNGEON_EFFECT_HARM_MIN_HP_ENEMY:
                if(!rangeHarm(confEffect, targetGrid)){
                    return false;
                }
                // 对玩家造成1点伤害
                updatePlayerHp(-1, ActivityConst.DUNGEON_HP_CHANGE_TYPE_NORMAL);
                return true;
            case ActivityConst.DUNGEON_EFFECT_HARM_NINE_SQUARE_ENEMY:
            case ActivityConst.DUNGEON_EFFECT_HARM_CROSS_ENEMY:
                return rangeHarm(confEffect, targetGrid);
            case ActivityConst.DUNGEON_EFFECT_HARM_TARGET:
                return targetHarm(confEffect, targetGrid);
            case ActivityConst.DUNGEON_EFFECT_EXPLORE_NINE_SQUARE_GRID:
            case ActivityConst.DUNGEON_EFFECT_EXPLORE_CROSS_GRID:
                rangeExplore(confEffect, targetGrid);
                break;
            case ActivityConst.DUNGEON_EFFECT_SUMMON_MONSTER:
                return summonMonster(confEffect, targetGrid);
            case ActivityConst.DUNGEON_EFFECT_REDUCE_TARGET_ATK:
            case ActivityConst.DUNGEON_EFFECT_STUN_TARGET:
                return addPersistEffectToMonster(confEffect, targetGrid);
            case ActivityConst.DUNGEON_EFFECT_RESTORE_HP_VALUE:
                {
                    int addValue = confEffect.value[0][0];
                    updatePlayerHp(addValue, ActivityConst.DUNGEON_HP_CHANGE_TYPE_HEAL);
//                    Log.activity.info("===回血{}点", addValue);
                }
                break;
            case ActivityConst.DUNGEON_EFFECT_RESTORE_HP_PERCENT:
                {
                    double addValue = confEffect.value[0][0];
                    long hpMax = getPlayerAttr(ActivityConst.DUNGEON_ATTR_HP_MAX);
                    updatePlayerHp((long) Math.ceil(hpMax * addValue / ActivityConst.DUNGEON_RATE_10000), ActivityConst.DUNGEON_HP_CHANGE_TYPE_HEAL);
//                    Log.activity.info("===回血{}%", addValue);
                }
                break;
            default:
                break;
        }
        return true;
    }

    /**
     * 添加持续性效果
     * @param confEffect
     * @param targetGrid
     * @return
     */
    public boolean addPersistEffect(ConfDungeonEffect confEffect, Define.p_dungeon_grid.Builder targetGrid){
        int sn = confEffect.sn;
        int duration = confEffect.duration;
        long casterAttr = 0;
        if(targetGrid != null && (confEffect.type == ActivityConst.DUNGEON_EFFECT_POISON || confEffect.type == ActivityConst.DUNGEON_EFFECT_CURSE
                || confEffect.type == ActivityConst.DUNGEON_EFFECT_BURNING)){
            // 保存施法者当前的攻击力
            casterAttr = getMonsterAttr(targetGrid, ActivityConst.DUNGEON_ATTR_ATK);
        }
        for(Define.p_dungeon_effect.Builder effect : m_persistEffects){
            if(effect.getSn() == sn && duration > 0){
                // 同sn的效果，刷新持续时间
                if(confEffect.type == ActivityConst.DUNGEON_EFFECT_ADD_ATTR_VALUE_DURATION || confEffect.type == ActivityConst.DUNGEON_EFFECT_ADD_ATTR_PERCENT_DURATION){
                    // 重复吃药做成延长buff持续时间
                    effect.setEndRemainRound(effect.getEndRemainRound() + duration);
                }
                else{
                    // 其他buff，比如中毒，还是覆盖持续时间
                    effect.setEndRemainRound(duration);
                }
                effect.setCasterAttr(casterAttr);
//                Log.activity.info("===刷新持续性效果，sn={} duration={}", sn, duration);
                return true;
            }
        }
        Define.p_dungeon_effect.Builder effect = Define.p_dungeon_effect.newBuilder();
        effect.setSn(sn);
        effect.setEndRemainRound(duration > 0 ? duration : -1);
        effect.setCasterAttr(casterAttr);
        m_persistEffects.add(effect);
//        Log.activity.info("===添加持续性效果，sn={} duration={}", sn, duration);
        return true;
    }

    /**
     * 给怪物添加持续性效果
     * @param confEffect
     * @param targetGrid
     * @return
     */
    public boolean addPersistEffectToMonster(ConfDungeonEffect confEffect, Define.p_dungeon_grid.Builder targetGrid){
        if(targetGrid == null || targetGrid.getState() == ActivityConst.DUNGEON_GRID_STATE_NEVER_EXPLORED || targetGrid.getType() != ActivityConst.DUNGEON_GRID_TYPE_MONSTER){
            Log.activity.warn("===给怪物添加持续性效果失败，grid[{},{}]不是已探明的怪物 humanId={}", targetGrid.getPos(), targetGrid.getSourceCavePos(), m_humanObj.id);
            return false;
        }
        int sn = confEffect.sn;
        int duration = confEffect.duration;
        Define.p_dungeon_effect.Builder effect = Define.p_dungeon_effect.newBuilder();
        effect.setSn(sn);
        effect.setEndRemainRound(duration > 0 ? duration : -1);
        targetGrid.addMonsterEffects(effect);
        m_changeGrids.add(targetGrid);
//        Log.activity.info("===给怪物添加持续性效果，sn={} duration={}", sn, duration);
        return true;
    }

    /**
     * 激活门
     * @param targetGrid
     * @return
     */
    public boolean unlockDoor(Define.p_dungeon_grid.Builder targetGrid){
        if(targetGrid == null){
            Log.activity.warn("===激活门失败，targetGrid == null");
            return false;
        }
        int sourceCavePos = targetGrid.getSourceCavePos();
        for(Define.p_dungeon_grid.Builder grid : getAllGrids(sourceCavePos)){
            if(grid.getType() == ActivityConst.DUNGEON_GRID_TYPE_EVENT){
                ConfDungeonEvent conf = ConfDungeonEvent.get(grid.getSn());
                if(conf != null && conf.event_type == ActivityConst.DUNGEON_EVENT_TYPE_DOOR && grid.getDoorState() == ActivityConst.DUNGEON_DOOR_STATE_LOCK){
                    grid.setDoorState(ActivityConst.DUNGEON_DOOR_STATE_UNLOCK);
                    m_changeGrids.add(grid);
//                    Log.activity.info("===激活门，pos={} sourceCavePos={}", grid.getPos(), sourceCavePos);
                    return true;
                }
            }
        }
        return false;
    }

    public boolean enterNextLayer(){
        m_layer++;
        m_maxLayer = m_layer;
        m_round = 1;
        // 初始化地图
        if(!initMap()){
            return false;
        }
        // 记录关卡到达的最大层数
        m_data.setChapterMaxLayer(m_chapterId, m_layer);
        int addHpValue = 0;
        int addStoneNum = 0;
        // 持续性效果
        for(Define.p_dungeon_effect.Builder effect : m_persistEffects){
            ConfDungeonEffect confEffect = ConfDungeonEffect.get(effect.getSn());
            if(confEffect != null && confEffect.value != null && confEffect.value[0] != null){
                if(confEffect.type == ActivityConst.DUNGEON_EFFECT_ADD_HP_ON_ENTER_NEXT_LAYER){
                    double value = confEffect.value[0][0];
                    addHpValue += (int) Math.ceil(getPlayerAttr(ActivityConst.DUNGEON_ATTR_HP_MAX) * value / ActivityConst.DUNGEON_RATE_10000);
                }
                else if(confEffect.type == ActivityConst.DUNGEON_EFFECT_ADD_STONE_ON_ENTER_NEXT_LAYER){
                    addStoneNum += confEffect.value[0][0];
                }
            }
        }
        // 吞噬魔石增益
        for(Map.Entry<Long, Long> profit : m_devourStoneInfo.entrySet()){
            ConfDungeonBuff conf = ConfDungeonBuff.get(Utils.intValue(profit.getKey()));
            if(conf != null && conf.value_base != null && conf.value_factor != null){
                if(conf.type == ActivityConst.DUNGEON_PROFIT_ADD_HP_ON_ENTER_NEXT_LAYER){
                    double value = conf.value_base[0] + conf.value_factor[0] * (Utils.intValue(profit.getValue()) - 1);
                    addHpValue += (int) Math.ceil(getPlayerAttr(ActivityConst.DUNGEON_ATTR_HP_MAX) * value / ActivityConst.DUNGEON_RATE_10000);
                }
            }
        }
        // 1.移除临时的加属性效果（除了永久性效果为-1，其他都删除）
        m_persistEffects.removeIf(effect -> effect.getEndRemainRound() >= 0);
        // 2.根据玩家血上限变化，更新玩家当前血量
        updatePlayerHpByHpMaxChange();
        // 3.进入下一层时回血和增加魔石
        if(addHpValue > 0){
            updatePlayerHp(addHpValue, ActivityConst.DUNGEON_HP_CHANGE_TYPE_NONE);
        }
        if(addStoneNum > 0){
            updateStoneNum(addStoneNum);
        }
        sendSceneInfo();
        m_isEnterNextLayer = true;
        // 清除变化的格子
        m_changeGrids.clear();
        Log.activity.info("===转生魔剑进入下一层 scene={} addHpValue={} addStoneNum={} humanId={}", this, addHpValue, addStoneNum, m_humanObj.id);
        return true;
    }

    static public int[] getDropItem(int groupId){
        List<ConfDungeonDrop> dropItemList = new ArrayList<>();
        List<Integer> weightList = new ArrayList<>();
        for(ConfDungeonDrop conf : ConfDungeonDrop.findAll()){
            if(conf.group == groupId){
                dropItemList.add(conf);
                weightList.add(conf.weight);
            }
        }
        int index = Utils.getRandRange(weightList);
        if(index < 0){
            return null;
        }
        ConfDungeonDrop dropItem = dropItemList.get(index);
        int itemId = dropItem.item_id;
        int itemNum = Utils.randomBetween(dropItem.min, dropItem.max);
        return new int[]{itemId, itemNum};
    }

    /**
     * 增加掉落物品
     * @param groupId
     * @return
     */
    public void addDropItemByGroupId(int groupId){
        int[] dropItem = getDropItem(groupId);
        if(dropItem == null || dropItem.length != 2){
            Log.activity.warn("===获得掉落物品失败，groupId={} humanId={}", groupId, m_humanObj.id);
            return;
        }
        int itemId = dropItem[0];
        int itemNum = dropItem[1];
        addDropItem(itemId, itemNum);
    }

    /**
     * 增加掉落物品
     * @param itemId
     * @param itemNum
     */
    public void addDropItem(int itemId, int itemNum){
        ConfDungeonItem confItem = ConfDungeonItem.get(itemId);
        if(confItem == null){
            Log.activity.warn("===获得掉落物品失败，ConfDungeonItem[{}]不存在 humanId={}", itemId, m_humanObj.id);
            return;
        }
        switch(confItem.type){
            case ActivityConst.DUNGEON_ITEM_TYPE_FOOD_MATERIAL:
            case ActivityConst.DUNGEON_ITEM_TYPE_GOODS:
                ProduceManager.inst().produceAdd(m_humanObj, itemId, itemNum, MoneyItemLogKey.转生魔剑);
                m_addItems.add(Define.p_key_value.newBuilder().setK(itemId).setV(itemNum));
                break;
            case ActivityConst.DUNGEON_ITEM_TYPE_USABLE_ITEM:
                addItem(itemId, itemNum);
                break;
            case ActivityConst.DUNGEON_ITEM_TYPE_CURRENCY:
                if(itemId == ActivityConst.DUNGEON_ITEM_ID_STONE){
                    updateStoneNum(itemNum);
                }
                break;
            default:
                Log.activity.warn("===获得掉落物品失败，itemId={} type={}无法处理 humanId={}", itemId, confItem.type, m_humanObj.id);
                return;
        }
        Log.activity.info("===获得掉落物品，dropItem=[{},{}] humanId={}", itemId, itemNum, m_humanObj.id);
    }

    public int getDropItemNum(int itemId){
        ConfDungeonItem confItem = ConfDungeonItem.get(itemId);
        if(confItem == null){
            return 0;
        }
        switch(confItem.type){
            case ActivityConst.DUNGEON_ITEM_TYPE_FOOD_MATERIAL:
            case ActivityConst.DUNGEON_ITEM_TYPE_GOODS:
                return Utils.intValue(ProduceManager.inst().getCostProduce(m_humanObj, itemId));
            case ActivityConst.DUNGEON_ITEM_TYPE_USABLE_ITEM:
                int itemIndex = getFirstItemIndex(itemId);
                return getItemNum(itemIndex);
            case ActivityConst.DUNGEON_ITEM_TYPE_CURRENCY:
                if(itemId == ActivityConst.DUNGEON_ITEM_ID_STONE){
                    return m_stoneNum;
                }
                return 0;
            default:
                return 0;
        }
    }

    /**
     * 能否扣除掉落物品
     * @param itemId
     * @param costNum
     */
    public boolean canCostDropItem(int itemId, int costNum){
        int curNum = getDropItemNum(itemId);
        return curNum >= costNum;
    }

    /**
     * 扣除掉落物品
     * @param itemId
     * @param costNum
     */
    public void costDropItem(int itemId, int costNum){
        ConfDungeonItem confItem = ConfDungeonItem.get(itemId);
        if(confItem == null){
            Log.activity.warn("===扣除掉落物品失败，ConfDungeonItem[{}]不存在 humanId={}", itemId, m_humanObj.id);
            return;
        }
        switch(confItem.type){
            case ActivityConst.DUNGEON_ITEM_TYPE_FOOD_MATERIAL:
            case ActivityConst.DUNGEON_ITEM_TYPE_GOODS:
                ProduceManager.inst().costItem(m_humanObj, itemId, costNum, MoneyItemLogKey.转生魔剑);
                break;
            case ActivityConst.DUNGEON_ITEM_TYPE_USABLE_ITEM:
                int itemIndex = getFirstItemIndex(itemId);
                costItem(itemIndex, costNum);
                break;
            case ActivityConst.DUNGEON_ITEM_TYPE_CURRENCY:
                if(itemId == ActivityConst.DUNGEON_ITEM_ID_STONE){
                    updateStoneNum(-costNum);
                }
                break;
            default:
                Log.activity.warn("===扣除掉落物品失败，itemId={} type={}无法处理 humanId={}", itemId, confItem.type, m_humanObj.id);
                break;
        }
    }

    /**
     * 对目标造成伤害
     * @param confEffect
     * @param targetGrid
     * @return
     */
    public boolean targetHarm(ConfDungeonEffect confEffect, Define.p_dungeon_grid.Builder targetGrid){
        if(targetGrid == null){
            Log.activity.warn("===对目标造成伤害失败，targetGrid == null，confEffect={}", confEffect.sn);
            return false;
        }
        double addPercent = confEffect.value[0][0];
        long atk = getPlayerAttr(ActivityConst.DUNGEON_ATTR_ATK);
        long damage = (long) Math.ceil(atk * addPercent / ActivityConst.DUNGEON_RATE_10000);
        updateMonsterHp(targetGrid, -damage, ActivityConst.DUNGEON_HP_CHANGE_TYPE_NORMAL);
        m_changeGrids.add(targetGrid);
        return true;
    }

    /**
     * 范围伤害
     * @param confEffect
     * @param targetGrid
     * @return
     */
    public boolean rangeHarm(ConfDungeonEffect confEffect, Define.p_dungeon_grid.Builder targetGrid){
        if(targetGrid == null){
            Log.activity.warn("===范围伤害失败，targetGrid == null，confEffect={}", confEffect.sn);
            return false;
        }
        List<Define.p_dungeon_grid.Builder> effectTargets = getEffectTargets(confEffect.type, targetGrid);
        if(confEffect.value[0] == null){
            Log.activity.warn("===范围伤害失败，ConfDungeonEffect[{}]配置错误 humanId={}", confEffect.sn, m_humanObj.id);
            return false;
        }
        ConfDungeonChapter confChapter = ConfDungeonChapter.get(m_chapterId);
        if(confChapter == null){
            Log.activity.warn("===范围伤害失败，ConfDungeonChapter[{}]不存在 humanId={}", m_chapterId, m_humanObj.id);
            return false;
        }
        double addPercent = confEffect.value[0][0];
        long atk = getPlayerAttr(ActivityConst.DUNGEON_ATTR_ATK);
        long damage = (long) Math.ceil(atk * addPercent / ActivityConst.DUNGEON_RATE_10000);
        for(Define.p_dungeon_grid.Builder monsterGrid : effectTargets){
            updateMonsterHp(monsterGrid, -damage, ActivityConst.DUNGEON_HP_CHANGE_TYPE_NORMAL);
        }
        m_changeGrids.addAll(effectTargets);
        return true;
    }

    /**
     * 获取效果目标
     * @param effectType
     * @param targetGrid
     * @return
     */
    public List<Define.p_dungeon_grid.Builder> getEffectTargets(int effectType, Define.p_dungeon_grid.Builder targetGrid){
        List<Define.p_dungeon_grid.Builder> targets = new ArrayList<>();
        int targetPos = targetGrid.getPos();
        int sourceCavePos = targetGrid.getSourceCavePos();
        int curX = getX(targetPos);
        int curY = getY(targetPos);
        if(effectType == ActivityConst.DUNGEON_EFFECT_HARM_MIN_HP_ENEMY){
            Define.p_dungeon_grid.Builder minHpGrid = null;
            for(Define.p_dungeon_grid.Builder grid : getAllGrids(sourceCavePos)){
                if(grid.getType() == ActivityConst.DUNGEON_GRID_TYPE_MONSTER && grid.getState() == ActivityConst.DUNGEON_GRID_STATE_EXPLORED){
                    if(minHpGrid == null || grid.getMonsterHp() < minHpGrid.getMonsterHp()){
                        minHpGrid = grid;
                    }
                }
            }
            if(minHpGrid != null){
                targets.add(minHpGrid);
            }
        }
        else if(effectType == ActivityConst.DUNGEON_EFFECT_EXPLORE_NINE_SQUARE_GRID || effectType == ActivityConst.DUNGEON_EFFECT_HARM_NINE_SQUARE_ENEMY){
            for(int x=curX-1; x<=curX+1; x++){
                for(int y=curY-1; y<=curY+1; y++){
                    if(x == curX && y == curY){
                        continue;
                    }
                    targets.add(getGrid(x, y, sourceCavePos));
                }
            }
            targets = filterEffectTargets(targets, effectType);
        }
        else if(effectType == ActivityConst.DUNGEON_EFFECT_EXPLORE_CROSS_GRID || effectType == ActivityConst.DUNGEON_EFFECT_HARM_CROSS_ENEMY){
            for(int x=1; x<=ActivityConst.DUNGEON_SCENE_MAX_X; x++){
                if(x == curX){
                    continue;
                }
                targets.add(getGrid(x, curY, sourceCavePos));
            }
            for(int y=1; y<=ActivityConst.DUNGEON_SCENE_MAX_Y; y++){
                if(y == curY){
                    continue;
                }
                targets.add(getGrid(curX, y, sourceCavePos));
            }
            targets = filterEffectTargets(targets, effectType);
        }
        Log.activity.info("===getEffectTargets，targets={}", targets.stream().map(v->Arrays.asList(v.getPos(), v.getType(), v.getSn())).collect(Collectors.toList()));
        return targets;
    }

    static public List<Define.p_dungeon_grid.Builder> filterEffectTargets(List<Define.p_dungeon_grid.Builder> targets, int effectType){
        return targets.stream().filter(grid-> {
            if(grid != null){
                // 未探索的格子
                if(effectType == ActivityConst.DUNGEON_EFFECT_EXPLORE_NINE_SQUARE_GRID || effectType == ActivityConst.DUNGEON_EFFECT_EXPLORE_CROSS_GRID){
                    if(grid.getState() == ActivityConst.DUNGEON_GRID_STATE_NEVER_EXPLORED){
                        return true;
                    }
                }
                // 已发现的怪物
                if(effectType == ActivityConst.DUNGEON_EFFECT_HARM_NINE_SQUARE_ENEMY || effectType == ActivityConst.DUNGEON_EFFECT_HARM_CROSS_ENEMY){
                    if(grid.getState() == ActivityConst.DUNGEON_GRID_STATE_EXPLORED && grid.getType() == ActivityConst.DUNGEON_GRID_TYPE_MONSTER){
                        return true;
                    }
                }
            }
            return false;
        }).collect(Collectors.toList());
    }

    /**
     * 更新怪物血量
     * @param monsterGrid
     * @param value
     * @param type
     */
    public void updateMonsterHp(Define.p_dungeon_grid.Builder monsterGrid, long value, int type){
        updateMonsterHp(monsterGrid, value, type, 0);
    }

    /**
     * 更新怪物血量
     * @param monsterGrid
     * @param value
     * @param type
     * @param minHp
     */
    public void updateMonsterHp(Define.p_dungeon_grid.Builder monsterGrid, long value, int type, long minHp){
        long hpMax = getMonsterAttr(monsterGrid, ActivityConst.DUNGEON_ATTR_HP_MAX);
        long curHp = getMonsterAttr(monsterGrid, ActivityConst.DUNGEON_ATTR_HP);
        long newHp = Math.min(Math.max(minHp, curHp + value), hpMax);
        monsterGrid.setMonsterHp(newHp);
        int pos = monsterGrid.getPos();
        int sn = monsterGrid.getSn();
        if(newHp <= 0){
            onMonsterDead(monsterGrid);
        }
        if(type != ActivityConst.DUNGEON_HP_CHANGE_TYPE_NONE){
            Define.p_dungeon_hp_change_info.Builder info = Define.p_dungeon_hp_change_info.newBuilder();
            info.setPos(pos);
            info.setType(type);
            info.setValue(value);
            m_hpChangeList.add(info);
        }
//        Log.activity.info("===更新怪物血量，pos={} sn={} value={} newHp={} humanId={}", pos, sn, value, newHp, m_humanObj.id);
    }

    /**
     * 怪物死亡
     * @param monsterGrid
     */
    public void onMonsterDead(Define.p_dungeon_grid.Builder monsterGrid){
//        Log.activity.info("===转生魔剑怪物死亡，pos={} sn={} humanId={}", monsterGrid.getPos(), monsterGrid.getSn(), m_humanObj.id);
        if(monsterGrid.getMonsterSummonEventId() > 0){
            // 怪物死亡触发召唤事件的奖励效果
            ConfDungeonEvent confEvent = ConfDungeonEvent.get(monsterGrid.getMonsterSummonEventId());
            if(confEvent != null && confEvent.event_type == ActivityConst.DUNGEON_EVENT_TYPE_SUMMON){
                int[] effectInfo = getEventEffectInfo(confEvent.event_effect_id);
                if(effectInfo != null){
                    // 召唤奖励效果暂时不影响其他格子，不会产生格子变化
                    processEffect(effectInfo[1], monsterGrid);
                }
            }
        }
        ConfDungeonEnemy confEnemy = ConfDungeonEnemy.get(monsterGrid.getSn());
        if(confEnemy != null){
            if(confEnemy.enemy_type == ActivityConst.DUNGEON_MONSTER_TYPE_BOSS){
                m_killBossNum++;
            }
            else{
                m_killMonsterNum++;
            }
        }
        // 怪物+钥匙，怪物死后显示钥匙
        if(monsterGrid.getKeySn() > 0){
            monsterGrid.setSn(monsterGrid.getKeySn());
            monsterGrid.setType(ActivityConst.DUNGEON_GRID_TYPE_EVENT);
        }
        else{
            monsterGrid.setSn(0);
            monsterGrid.setType(ActivityConst.DUNGEON_GRID_TYPE_BLANK);
        }
        // 下面的代码必须在更改type为BLANK后调用
        if(monsterGrid.getSourceCavePos() == 0){
            // 洞窟敌全灭不给魔石
            if(getAllGrids(0).stream().noneMatch((v->v.getType() == ActivityConst.DUNGEON_GRID_TYPE_MONSTER))){
                ConfDungeonChapter confChapter = ConfDungeonChapter.get(m_chapterId);
                if(confChapter != null && confChapter.clear_reward != null && confChapter.clear_reward.length == 2){
                    int addStoneNum = confChapter.clear_reward[0] + confChapter.clear_reward[1] * (m_layer - 1);
                    double addPercent = 0;
                    // 吞噬魔石增益
                    for(Map.Entry<Long, Long> profit : m_devourStoneInfo.entrySet()){
                        ConfDungeonBuff confBuff = ConfDungeonBuff.get(Utils.intValue(profit.getKey()));
                        if(confBuff != null && confBuff.value_base != null){
                            if(confBuff.type == ActivityConst.DUNGEON_PROFIT_ADD_STONE_ON_ALL_MONSTER_DEAD){
                                double value = confBuff.value_base[0] + confBuff.value_factor[0] * (Utils.intValue(profit.getValue()) - 1);
                                addPercent += value;
                            }
                        }
                    }
                    int finalAddNum = Math.min((int) Math.ceil(addStoneNum * (ActivityConst.DUNGEON_RATE_10000 + addPercent) / ActivityConst.DUNGEON_RATE_10000),
                            ActivityConst.DUNGEON_ON_ALL_MONSTER_DEAD_ADD_MAX_STONE);
                    updateStoneNum(finalAddNum);
                    Log.activity.info("===转生魔剑[{},{}]敌全灭，获得魔石={} humanId={}", m_chapterId, m_layer, finalAddNum, m_humanObj.id);
                }
            }
        }
    }

    /**
     * 范围探索
     * @param confEffect
     * @param targetGrid
     */
    public void rangeExplore(ConfDungeonEffect confEffect, Define.p_dungeon_grid.Builder targetGrid){
        if(targetGrid == null){
            Log.activity.warn("===范围探索失败，targetGrid == null，confEffect={}", confEffect.sn);
            return;
        }
        List<Define.p_dungeon_grid.Builder> effectTargets = getEffectTargets(confEffect.type, targetGrid);
        for(Define.p_dungeon_grid.Builder grid : effectTargets){
            grid.setState(ActivityConst.DUNGEON_GRID_STATE_EXPLORED);
        }
        m_changeGrids.addAll(effectTargets);
    }

    public boolean summonMonster(ConfDungeonEffect confEffect, Define.p_dungeon_grid.Builder targetGrid){
        int monsterId = confEffect.value[0][0];
        if(targetGrid == null){
            Log.activity.warn("===召唤怪物[{}]失败，targetGrid == null，confEffect={}", monsterId, confEffect.sn);
            return false;
        }
        int eventId = targetGrid.getSn();
        targetGrid.setMonsterSummonEventId(eventId);
        initMonster(targetGrid, monsterId, 0);
//        Log.activity.info("===召唤怪物[{}] pos={} eventId={}", monsterId, targetGrid.getPos(), eventId);
        return true;
    }

    /**
     * 转生魔剑战斗
     *
     * @param targetPos
     * @param sourceCavePos
     */
    public boolean doBattle(int targetPos, int sourceCavePos) {
        // 战斗只能选择怪物格
        Define.p_dungeon_grid.Builder monsterGrid = getMonsterGrid(targetPos, sourceCavePos);
        if(monsterGrid == null){
            Log.activity.warn("===转生魔剑战斗失败，grid[{},{}]不是怪物 humanId={}", targetPos, sourceCavePos, m_humanObj.id);
            return false;
        }
        if(monsterGrid.getState() != ActivityConst.DUNGEON_GRID_STATE_EXPLORED){
            Log.activity.warn("===转生魔剑战斗失败，grid[{},{}]未被探明 humanId={}", targetPos, sourceCavePos, m_humanObj.id);
            return false;
        }
        if(!processBattle(monsterGrid)){
            return false;
        }
        if(m_isClosed){
            // 玩家被打死的结算，就不通知战斗结果了
            Log.activity.info("===转生魔剑战斗结束，玩家被打死，humanId={}", m_humanObj);
            return true;
        }
        // 处理后再添加grid为已变化
        m_changeGrids.add(monsterGrid);
        // 构建返回消息
        MsgAct2.act_dungeon_battle_s2c.Builder msg = MsgAct2.act_dungeon_battle_s2c.newBuilder();
        msg.setActType(m_data.objectData.type);
        msg.setChapterId(m_chapterId);
        msg.setChangeInfo(buildChangeInfo(targetPos, sourceCavePos));
        m_humanObj.sendMsg(msg);
//        Log.activity.info("===转生魔剑战斗成功，pos={} sn={} humanId={}", monsterGrid.getPos(), monsterGrid.getSn(), m_humanObj.id);
        return true;
    }

    /**
     * 处理战斗
     * @param monsterGrid
     * @return
     */
    public boolean processBattle(Define.p_dungeon_grid.Builder monsterGrid){
        ConfDungeonEnemy confEnemy = ConfDungeonEnemy.get(monsterGrid.getSn());
        if(confEnemy == null){
            Log.activity.warn("===转生魔剑处理战斗失败，ConfDungeonEnemy[{}]不存在 humanId={}", monsterGrid.getSn(), m_humanObj.id);
            return false;
        }
        // 计算玩家对怪物造成的伤害
        long[] atkMonsterDamageInfo = calcPlayerAttackMonsterDamage(monsterGrid);
        long atkMonsterDamage = atkMonsterDamageInfo[0];
        int atkMonsterType = Utils.intValue(atkMonsterDamageInfo[1]);
        if(atkMonsterDamage > 0){
            // 怪物反伤给玩家
            monsterDamageRebound(monsterGrid, atkMonsterDamage);
        }
        // 被晕的怪不会反击
        if(!isMonsterStunned(monsterGrid)) {
            // 计算怪物对玩家造成的伤害
            long[] atkPlayerDamageInfo = calcMonsterAttackPlayerDamage(monsterGrid, 1);
            long atkPlayerDamage = atkPlayerDamageInfo[0];
            int atkPlayerType = Utils.intValue(atkPlayerDamageInfo[1]);
            if(atkPlayerDamage > 0){
                // 怪物给玩家上debuff
                monsterAddDebuff(monsterGrid);
            }
            // 对玩家造成伤害
            updatePlayerHp(-atkPlayerDamage, atkPlayerType);
        }
        // 对怪物造成伤害，放在最后，否则怪物死了无法给玩家上debuff
        updateMonsterHp(monsterGrid, -atkMonsterDamage, atkMonsterType);
        return true;
    }

    static public boolean isMonsterStunned(Define.p_dungeon_grid.Builder monsterGrid){
        return monsterGrid.getMonsterEffectsList().stream().anyMatch(effect->{
            ConfDungeonEffect confEffect = ConfDungeonEffect.get(effect.getSn());
            return confEffect != null && confEffect.type == ActivityConst.DUNGEON_EFFECT_STUN_TARGET;
        });
    }

    /**
     * 计算玩家对怪物造成的伤害
     *
     * @param monsterGrid
     */
    public long[] calcPlayerAttackMonsterDamage(Define.p_dungeon_grid.Builder monsterGrid){
        int pos = monsterGrid.getPos();
        int sn = monsterGrid.getSn();
        long hitRate = getPlayerAttr(ActivityConst.DUNGEON_ATTR_HIT_RATE) - getMonsterAttr(monsterGrid, ActivityConst.DUNGEON_ATTR_DODGE_RATE);
        int random = Utils.randomBetween(1, 100);
        if(random > hitRate){
//            Log.activity.info("===转生魔剑战斗，怪物[{},{}]闪避 random={} hitRate={} humanId={}", pos, sn, random, hitRate, m_humanObj.id);
            return new long[]{0, ActivityConst.DUNGEON_HP_CHANGE_TYPE_DODGE};
        }
        long critRate = getPlayerAttr(ActivityConst.DUNGEON_ATTR_CRIT_RATE);
        boolean bCrit = Utils.randomBetween(1, 100) <= critRate;
        long baseDamage = getPlayerAttr(ActivityConst.DUNGEON_ATTR_ATK);
        // 怪物减伤被动
        double reduceDamagePercent = 0;  // 怪物减伤百分比
        for(ConfDungeonSkill confSkill : getMonsterPassiveSkillList(monsterGrid)) {
            int skillType = confSkill.type;
            if(skillType == ActivityConst.DUNGEON_SKILL_DAMAGE_REDUCE){
                reduceDamagePercent += confSkill.value[0];
            }
        }
        // reduceDamagePercent用double避免整数相除，小数部分被截断。最终伤害值向上取整
        long damage = (long)Math.ceil(baseDamage * (bCrit ? 2 : 1) * (ActivityConst.DUNGEON_RATE_10000 - reduceDamagePercent) / ActivityConst.DUNGEON_RATE_10000);
//        Log.activity.info("===转生魔剑战斗，玩家对怪物[{},{}]产生{}的伤害 暴击={} 减伤={}% humanId={}",
//                pos, sn, damage, bCrit, reduceDamagePercent, m_humanObj.id);
        return new long[]{damage, bCrit ? ActivityConst.DUNGEON_HP_CHANGE_TYPE_CRIT : ActivityConst.DUNGEON_HP_CHANGE_TYPE_NORMAL};
    }

    /**
     * 计算怪物对玩家造成的伤害
     * @param monsterGrid
     * @param damageRate
     * @return
     */
    public long[] calcMonsterAttackPlayerDamage(Define.p_dungeon_grid.Builder monsterGrid, int damageRate){
        int pos = monsterGrid.getPos();
        int sn = monsterGrid.getSn();
        long hitRate = getMonsterAttr(monsterGrid, ActivityConst.DUNGEON_ATTR_HIT_RATE) - getPlayerAttr(ActivityConst.DUNGEON_ATTR_DODGE_RATE);
        int random = Utils.randomBetween(1, 100);
        if(random > hitRate){
//            Log.activity.info("===转生魔剑战斗，玩家闪避怪物[{},{}]的攻击 random={} hitRate={} humanId={}", pos, sn, random, hitRate, m_humanObj.id);
            return new long[]{0, ActivityConst.DUNGEON_HP_CHANGE_TYPE_DODGE};
        }
        long blockRate = getPlayerAttr(ActivityConst.DUNGEON_ATTR_BLOCK_RATE);
        boolean bBlock = Utils.randomBetween(1, 100) <= blockRate;
        long baseDamage = Math.max(0, getMonsterAttr(monsterGrid, ActivityConst.DUNGEON_ATTR_ATK) * damageRate - getPlayerAttr(ActivityConst.DUNGEON_ATTR_DEF));
        long damage = Math.max(1, Utils.longValue(baseDamage * (bBlock ? 0.5 : 1)));
//        Log.activity.info("===转生魔剑战斗，怪物[{},{}]对玩家产生{}的伤害 bBlock={} humanId={}", pos, sn, damage, bBlock, m_humanObj.id);
        return new long[]{damage, bBlock ? ActivityConst.DUNGEON_HP_CHANGE_TYPE_BLOCK : ActivityConst.DUNGEON_HP_CHANGE_TYPE_NORMAL};
    }

    /**
     * 怪物反伤给玩家
     * @param monsterGrid
     * @param atkMonsterDamage
     */
    public void monsterDamageRebound(Define.p_dungeon_grid.Builder monsterGrid, long atkMonsterDamage){
        double reboundPercent = 0;
        for(ConfDungeonSkill confSkill : getMonsterPassiveSkillList(monsterGrid)) {
            int skillType = confSkill.type;
            if(skillType == ActivityConst.DUNGEON_SKILL_DAMAGE_REBOUND){
                reboundPercent += confSkill.value[0];
            }
        }
        if(reboundPercent > 0){
            // 反伤值向上取整
            long reboundDamage = (long)Math.ceil(atkMonsterDamage * reboundPercent / ActivityConst.DUNGEON_RATE_10000);
//            Log.activity.info("===怪物[{},{}]反伤，reboundDamage={} humanId={}", monsterGrid.getPos(), monsterGrid.getSn(), reboundDamage, m_humanObj.id);
            updatePlayerHp(-reboundDamage, ActivityConst.DUNGEON_HP_CHANGE_TYPE_REBOUND);
        }
    }

    /**
     * 怪物给玩家上debuff
     * @param monsterGrid
     */
    public void monsterAddDebuff(Define.p_dungeon_grid.Builder monsterGrid){
        for(ConfDungeonSkill confSkill : getMonsterPassiveSkillList(monsterGrid)) {
            int skillType = confSkill.type;
            if(skillType == ActivityConst.DUNGEON_SKILL_ATK_ADD_POISON || skillType == ActivityConst.DUNGEON_SKILL_ATK_ADD_CURSE
                    || skillType == ActivityConst.DUNGEON_SKILL_ATK_ADD_BURNING){
                processEffect(confSkill.value[0], monsterGrid);
            }
        }
    }

    /**
     * 怪物使用自主技能
     * @param monsterGrid
     */
    public void monsterUseSkill(Define.p_dungeon_grid.Builder monsterGrid){
        int sourceCavePos = monsterGrid.getSourceCavePos();
        int sn = monsterGrid.getSn();
        int pos = monsterGrid.getPos();
        ConfDungeonEnemy confEnemy = ConfDungeonEnemy.get(sn);
        if(confEnemy == null){
            Log.activity.warn("===转生魔剑怪物[{},{}]使用技能失败，ConfDungeonEnemy[{}]不存在 humanId={}", pos, sn, sn, m_humanObj.id);
            return;
        }
        int skillId = confEnemy.action_skill;
        ConfDungeonSkill confSkill = ConfDungeonSkill.get(skillId);
        if(confSkill == null){
            Log.activity.warn("===转生魔剑怪物[{},{}]使用技能失败，ConfDungeonSkill[{}]不存在 humanId={}", pos, sn, skillId, m_humanObj.id);
            return;
        }
        if(confSkill.value == null){
            Log.activity.warn("===转生魔剑怪物[{},{}]使用技能失败，ConfDungeonSkill[{}]的技能效果值错误 humanId={}", pos, sn, skillId, m_humanObj.id);
            return;
        }
        switch (confSkill.type){
            case ActivityConst.DUNGEON_SKILL_HURT_PLAYER:
            case ActivityConst.DUNGEON_SKILL_HURT_PLAYER_AND_EXPLOSION:
                {
                    long[] damageInfo = calcMonsterAttackPlayerDamage(monsterGrid, confSkill.value[0]);
                    long damage = damageInfo[0];
                    int type = Utils.intValue(damageInfo[1]);
                    updatePlayerHp(-damage, type);
                    if(damage > 0){
                        // 怪物给玩家上debuff
                        monsterAddDebuff(monsterGrid);
                    }
                    boolean bExplosion = confSkill.type == ActivityConst.DUNGEON_SKILL_HURT_PLAYER_AND_EXPLOSION;
                    if(bExplosion){
                        updateMonsterHp(monsterGrid, -monsterGrid.getMonsterHp(), ActivityConst.DUNGEON_HP_CHANGE_TYPE_EXPLOSION);
                    }
                    Log.activity.info("===转生魔剑怪物[{},{}]使用技能[{}]，damage={} humanId={}", pos, sn, bExplosion ? "自爆":"投掷", damage, m_humanObj.id);
                }
                break;
            case ActivityConst.DUNGEON_SKILL_HEAL_ALL_MONSTER:
                {
                    double healPercent = confSkill.value[0];
                    long monsterAtk = getMonsterAttr(monsterGrid, ActivityConst.DUNGEON_ATTR_ATK);
                    long healValue = (long) Math.ceil(monsterAtk * healPercent / ActivityConst.DUNGEON_RATE_10000);
                    for(Define.p_dungeon_grid.Builder grid : getAllGrids(sourceCavePos)){
                        if(grid.getType() == ActivityConst.DUNGEON_GRID_TYPE_MONSTER && grid.getState() == ActivityConst.DUNGEON_GRID_STATE_EXPLORED){
                            updateMonsterHp(grid, healValue, ActivityConst.DUNGEON_HP_CHANGE_TYPE_HEAL);
                        }
                    }
                    Log.activity.info("===转生魔剑怪物[{},{}]使用技能[群体加血]，healValue={} humanId={}", pos, sn, healPercent, m_humanObj.id);
                }
                break;
            default:
                Log.activity.warn("===转生魔剑怪物[{},{}]使用技能失败，confSkill.type={}的技能无法主动释放 humanId={}", pos, sn, confSkill.type, m_humanObj.id);
                break;
        }
    }

    /**
     * 获取物品
     * @param itemIndex
     * @return
     */
    public Define.p_key_value.Builder getItem(int itemIndex){
        if(itemIndex < 0 || itemIndex >= m_backPack.size()){
            Log.activity.warn("===转生魔剑getItem失败，backPack[{}]不存在 humanId={}", itemIndex, m_humanObj.id);
            return null;
        }
        Define.p_key_value.Builder item = m_backPack.get(itemIndex);
        if(item == null) {
            Log.activity.warn("===转生魔剑getItem失败，backPack[{}]不存在 humanId={}", itemIndex, m_humanObj.id);
            return null;
        }
        ConfDungeonItem conf = ConfDungeonItem.get(Utils.intValue(item.getK()));
        if(conf == null){
            Log.activity.warn("===转生魔剑getItem失败，ConfDungeonItem[{}]不存在 humanId={}", item.getK(), m_humanObj.id);
            return null;
        }
        return item;
    }

    /**
     * 添加物品
     * @param itemId
     * @param addNum
     * @return
     */
    public boolean addItem(int itemId, int addNum){
        ConfDungeonItem conf = ConfDungeonItem.get(itemId);
        if(conf == null){
            Log.activity.warn("===转生魔剑添加物品失败，ConfDungeonItem[{}]不存在 humanId={}", itemId, m_humanObj.id);
            return false;
        }
        if(conf.stackable == 0){
            // 不可堆叠
            for(int i=0; i<addNum; i++){
                m_backPack.add(Define.p_key_value.newBuilder().setK(itemId).setV(1));
            }
            return true;
        }
        // 可堆叠
        for(Define.p_key_value.Builder item : m_backPack){
            if(item.getK() == itemId){
                item.setV(item.getV() + addNum);
                return true;
            }
        }
        m_backPack.add(Define.p_key_value.newBuilder().setK(itemId).setV(addNum));
        return true;
    }

    /**
     * 获取背包里第一个指定itemId的物品index
     * @param itemId
     * @return
     */
    public int getFirstItemIndex(int itemId){
        for(int i=0; i<m_backPack.size(); i++){
            Define.p_key_value.Builder item = m_backPack.get(i);
            if(item.getK() == itemId){
                return i;
            }
        }
        Log.activity.warn("===背包不存在itemId={} humanId={}", itemId, m_humanObj.id);
        return -1;
    }

    public int getItemNum(int itemIndex){
        Define.p_key_value.Builder item = getItem(itemIndex);
        if(item == null) {
            return 0;
        }
        return Utils.intValue(item.getV());
    }

    /**
     * 能否扣除物品
     * @param item
     * @param costNum
     * @return
     */
    public boolean canCostItem(Define.p_key_value.Builder item, int costNum){
        return item != null && item.getV() >= costNum;
    }

    public boolean canCostItem(int itemIndex, int costNum){
        Define.p_key_value.Builder item = getItem(itemIndex);
        if(item == null) {
            return false;
        }
        return canCostItem(item, costNum);
    }

    /**
     * 扣除物品
     * @param itemIndex
     * @param costNum
     * @return
     */
    public boolean costItem(int itemIndex, int costNum){
        Define.p_key_value.Builder item = getItem(itemIndex);
        if(item == null) {
            return false;
        }
        if(item.getV() < costNum) {
            Log.activity.warn("===转生魔剑扣除物品失败，itemIndex={} item=[{},{}] costNum={} humanId={}",
                    itemIndex, item.getK(), item.getV(), costNum, m_humanObj.id);
            return false;
        }
        if(item.getV() <= costNum) {
            m_backPack.remove(itemIndex);
        }
        else {
            item.setV(item.getV() - costNum);
        }
        return true;
    }

    /**
     * 能否使用物品
     * @param item
     * @param targetPos
     * @param sourceCavePos
     * @return
     */
    public boolean canUseItem(Define.p_key_value.Builder item, int targetPos, int sourceCavePos){
        ConfDungeonItem conf = ConfDungeonItem.get(Utils.intValue(item.getK()));
        if(conf.usable == 0){
            Log.activity.warn("===转生魔剑使用物品失败，item[{}]不可使用 humanId={}", item.getK(), m_humanObj.id);
            return false;
        }
        if(conf.usable_type == 1){
            // 使用物品只能选择怪物格
            Define.p_dungeon_grid.Builder grid = getMonsterGrid(targetPos, sourceCavePos);
            if(grid == null){
                Log.activity.warn("===转生魔剑使用物品失败，grid[{},{}]不是怪物 humanId={}", targetPos, sourceCavePos, m_humanObj.id);
                return false;
            }
        }
        if(!canCostItem(item, 1)) {
            Log.activity.warn("===转生魔剑使用物品失败，item[{}]数量不足 humanId={}", item.getK(), m_humanObj.id);
            return false;
        }
        return true;
    }

    /**
     * 转生魔剑使用物品
     * @param itemIndex
     * @param targetPos
     * @param sourceCavePos
     * @return
     */
    public boolean useItem(int itemIndex, int targetPos, int sourceCavePos) {
        Define.p_key_value.Builder item = getItem(itemIndex);
        if(item == null) {
            return false;
        }
        // 能否使用物品
        if(!canUseItem(item, targetPos, sourceCavePos)) {
            return false;
        }
        ConfDungeonItem confItem = ConfDungeonItem.get(Utils.intValue(item.getK()));
        // 使用物品只能选择怪物格
        if(!processEffect(confItem.effect_id, getMonsterGrid(targetPos, sourceCavePos))){
            return false;
        }
        // 物品生效后，再扣除物品
        costItem(itemIndex, 1);
        // 根据玩家血上限变化，更新玩家当前血量
        updatePlayerHpByHpMaxChange();
        // 构建返回消息
        MsgAct2.act_dungeon_use_item_s2c.Builder msg = MsgAct2.act_dungeon_use_item_s2c.newBuilder();
        msg.setActType(m_data.objectData.type);
        msg.setChapterId(m_chapterId);
        msg.setChangeInfo(buildChangeInfo(targetPos, sourceCavePos));
        msg.setItemId(Utils.intValue(item.getK()));
        m_humanObj.sendMsg(msg);
//        Log.activity.info("===转生魔剑使用物品成功，itemIndex={} itemId={} targetPos={} humanId={}", itemIndex, item.getK(), targetPos, m_humanObj.id);
        return true;
    }

    /**
     * 增加行动轮次
     * @param targetPos
     * @param sourceCavePos
     * @param afterExplore
     */
    public void increaseRound(int targetPos, int sourceCavePos, boolean afterExplore){
        if(m_isEnterNextLayer){
            m_isEnterNextLayer = false;
            return;
        }
        // 无论是否在洞窟，round都增加，只在服务端计数用
        m_round++;
        // 根据sourceCavePos取到当前地图，只在当前地图的格子上增加行动轮次
        Collection<Define.p_dungeon_grid.Builder> allGrids = getAllGrids(sourceCavePos);
        // 1.计算怪物反击技能伤害
        for(Define.p_dungeon_grid.Builder grid : allGrids) {
            if(afterExplore && grid.getPos() == targetPos) {
                // 排除本轮探索点击的格子
                continue;
            }
            if(grid.getType() == ActivityConst.DUNGEON_GRID_TYPE_MONSTER && grid.getState() == ActivityConst.DUNGEON_GRID_STATE_EXPLORED){
                if(isMonsterStunned(grid)) {
                    // 跳过被晕的怪
                    continue;
                }
                // 有自主技能cd的怪物格，减剩余轮次，并同步变化
                ConfDungeonEnemy conf = ConfDungeonEnemy.get(grid.getSn());
                if(conf == null || conf.cd == 0) {
                    continue;
                }
                int remainCd = grid.getMonsterRoundCd();
                if(remainCd > 1){
                    grid.setMonsterRoundCd(remainCd-1);
                }
                else{
                    // 轮次cd到了，怪物发动技能攻击玩家，cd重置
                    grid.setMonsterRoundCd(conf.cd);
                    monsterUseSkill(grid);
                }
                m_changeGrids.add(grid);
            }
        }
        // 2.dot效果扣血（暂时只有玩家会被上dot，怪物还不会）
        int dotValue = 0;
        for(Define.p_dungeon_effect.Builder effect : m_persistEffects){
            ConfDungeonEffect confEffect = ConfDungeonEffect.get(effect.getSn());
            if(confEffect != null && confEffect.value != null && confEffect.value[0] != null){
                if(confEffect.type == ActivityConst.DUNGEON_EFFECT_POISON || confEffect.type == ActivityConst.DUNGEON_EFFECT_BURNING){
                    double atkPercent = confEffect.value[0][0];
                    int addValue = (int) Math.ceil(effect.getCasterAttr() * atkPercent / ActivityConst.DUNGEON_RATE_10000);
                    dotValue += addValue;
                }
            }
        }
        if(dotValue > 0){
            // dot扣除玩家血量
            updatePlayerHp(-dotValue, ActivityConst.DUNGEON_HP_CHANGE_TYPE_NORMAL);
        }
        // 3.移除玩家和怪物的过期效果
        m_persistEffects.removeIf(effect -> {
            if (effect.getEndRemainRound() > 0) {
                effect.setEndRemainRound(effect.getEndRemainRound() - 1);
            }
            return effect.getEndRemainRound() == 0;
        });
        for(Define.p_dungeon_grid.Builder grid : allGrids) {
            List<Define.p_dungeon_effect.Builder> effects = grid.getMonsterEffectsList().stream().map(Define.p_dungeon_effect::newBuilder).collect(Collectors.toList());
            if(!effects.isEmpty()){
                effects.removeIf(effect -> {
                    if (effect.getEndRemainRound() > 0) {
                        effect.setEndRemainRound(effect.getEndRemainRound() - 1);
                    }
                    return effect.getEndRemainRound() == 0;
                });
                grid.clearMonsterEffects();
                grid.addAllMonsterEffects(effects.stream().map(Define.p_dungeon_effect.Builder::build).collect(Collectors.toList()));
            }
        }
        // 4.根据玩家血上限变化，更新玩家当前血量
        updatePlayerHpByHpMaxChange();
        // 5.更新怪物的持续效果后，更新所有怪物的属性，并通知客户端
        updateAllMonsterAttr(sourceCavePos, true);

        // 发送轮次变化信息
        MsgAct2.act_dungeon_round_change_s2c.Builder msg = MsgAct2.act_dungeon_round_change_s2c.newBuilder();
        msg.setActType(m_data.objectData.type);
        msg.setChapterId(m_chapterId);
        msg.setRound(m_round);
        msg.setChangeInfo(buildChangeInfo(targetPos, sourceCavePos));
        msg.addAllPersistEffects(getPersistEffectList());
        m_humanObj.sendMsg(msg);
//        Log.activity.info("===转生魔剑轮次变化信息 humanId={} round={} m_persistEffects={}", m_humanObj.id, m_round, m_persistEffects.stream().map(v->Arrays.asList(v.getSn(), v.getEndRemainRound()))
//                .collect(Collectors.toList()));
    }

    /**
     * 根据玩家血上限变化，更新玩家当前血量
     */
    public void updatePlayerHpByHpMaxChange(){
        long curHpMax = getPlayerAttr(ActivityConst.DUNGEON_ATTR_HP_MAX);
        if(curHpMax != m_lastHpMax){
            // 血上限降低，当前血量跟随减少，最多减少到1
//            Log.activity.info("===玩家血上限变化，当前血量跟随变化{}", curHpMax - m_lastHpMax);
            updatePlayerHp(curHpMax - m_lastHpMax, ActivityConst.DUNGEON_HP_CHANGE_TYPE_NONE, 1);
            m_lastHpMax = curHpMax;
        }
    }

    /**
     * 更新所有怪物的属性
     * @param sourceCavePos
     * @param notifyChange
     */
    public void updateAllMonsterAttr(int sourceCavePos, boolean notifyChange){
        Collection<Define.p_dungeon_grid.Builder> allGrids = getAllGrids(sourceCavePos);
        for(Define.p_dungeon_grid.Builder grid : allGrids) {
            if(grid.getType() == ActivityConst.DUNGEON_GRID_TYPE_MONSTER && grid.getState() == ActivityConst.DUNGEON_GRID_STATE_EXPLORED){
                boolean isChanged = false;
                long curAtk = getMonsterAttr(grid, ActivityConst.DUNGEON_ATTR_ATK);
                long lastAtk = grid.getMonsterAtk();
                if(curAtk != lastAtk){
//                    Log.activity.info("===怪物[{}-{},{}] 攻击力从{}变化为{}，变化值{}", sourceCavePos, grid.getPos(), grid.getSn(), lastAtk, curAtk, curAtk - lastAtk);
                    grid.setMonsterAtk(curAtk);
                    isChanged = true;
                }
                long curHpMax = getMonsterAttr(grid, ActivityConst.DUNGEON_ATTR_HP_MAX);
                long lastHpMax = grid.getMonsterHpMax();
                if(curHpMax != lastHpMax){
                    // 血上限降低，当前血量跟随减少，最多减少到1
//                    Log.activity.info("===怪物[{}-{},{}] 血上限从{}变化为{}，当前血量跟随变化{}",
//                            sourceCavePos, grid.getPos(), grid.getSn(), lastHpMax, curHpMax, curHpMax - lastHpMax);
                    updateMonsterHp(grid, curHpMax - lastHpMax, ActivityConst.DUNGEON_HP_CHANGE_TYPE_NONE, 1);
                    grid.setMonsterHpMax(curHpMax);
                    isChanged = true;
                }
                if(isChanged && notifyChange){
                    m_changeGrids.add(grid);
                }
            }
        }
    }

    /**
     * 发送吞噬魔石增益信息
     */
    public void sendDevourStoneInfo(){
        ConfDungeonChapter confChapter = ConfDungeonChapter.get(m_chapterId);
        if(confChapter == null){
            Log.activity.warn("===发送吞噬魔石增益信息失败，ConfDungeonChapter[{}]不存在 humanId={}", m_chapterId, m_humanObj.id);
            return;
        }
        MsgAct2.act_dungeon_devour_stone_info_s2c.Builder msg = MsgAct2.act_dungeon_devour_stone_info_s2c.newBuilder();
        msg.setActType(m_data.objectData.type);
        msg.setChapterId(m_chapterId);
        msg.setStoneNum(m_stoneNum);
        msg.setCostStoneNum(getCostNum(confChapter.stone_cost, m_devourStoneNum));
        msg.setRefreshItemNum(getDropItemNum(confChapter.buff_refresh_item));
        msg.setCostRefreshItemNum(getCostNum(confChapter.buff_refresh_cost, m_devourRefreshBuffNum));
        msg.addAllDevourStoneRandomInfo(getDevourStoneRandomInfoList());
        msg.addAllDevourStoneInfo(getDevourStoneInfoList());
        m_humanObj.sendMsg(msg);
//        Log.activity.info("===发送吞噬魔石增益信息，msg={} humanId={}", msg, m_humanObj.id);
    }

    static public int getCostNum(int[] costArray, int index){
        if(costArray.length < 1){
            return 0;
        }
        return costArray[Math.min(index, costArray.length - 1)];
    }

    /**
     * 转生魔剑吞噬魔石操作
     * @param type
     * @param chooseIndex
     * @return
     */
    public boolean devourStoneOp(int type, int chooseIndex){
        ConfDungeonChapter confChapter = ConfDungeonChapter.get(m_chapterId);
        if(confChapter == null){
            Log.activity.warn("===转生魔剑吞噬魔石操作失败，ConfDungeonChapter[{}]不存在 humanId={}", m_chapterId, m_humanObj.id);
            return false;
        }
        Define.p_key_value.Builder stoneChangeInfo = Define.p_key_value.newBuilder();
        if(!processStoneOp(type, chooseIndex, stoneChangeInfo)){
            return false;
        }
        MsgAct2.act_dungeon_devour_stone_op_s2c.Builder msg = MsgAct2.act_dungeon_devour_stone_op_s2c.newBuilder();
        msg.setActType(m_data.objectData.type);
        msg.setChapterId(m_chapterId);
        msg.setStoneNum(m_stoneNum);
        msg.setCostStoneNum(getCostNum(confChapter.stone_cost, m_devourStoneNum));
        msg.setRefreshItemNum(getDropItemNum(confChapter.buff_refresh_item));
        msg.setCostRefreshItemNum(getCostNum(confChapter.buff_refresh_cost, m_devourRefreshBuffNum));
        msg.addAllDevourStoneRandomInfo(getDevourStoneRandomInfoList());
        if(stoneChangeInfo.getK() > 0){
            msg.setDevourStoneChangeInfo(stoneChangeInfo);
        }
        msg.addAllAttrs(getAttrList());
        msg.addAllBackPack(getBackPackList());
        m_humanObj.sendMsg(msg);
//        Log.activity.info("===转生魔剑吞噬魔石操作成功，type={} chooseIndex={} humanId={}", type, chooseIndex, m_humanObj.id);
        return true;
    }

    public boolean processStoneOp(int type, int chooseIndex, Define.p_key_value.Builder stoneChangeInfo){
        switch(type){
            case ActivityConst.DUNGEON_DEVOUR_STONE_OP_TYPE_COST:
                return processStoneOpCost();
            case ActivityConst.DUNGEON_DEVOUR_STONE_OP_TYPE_REFRESH:
                return processStoneOpRefresh();
            case ActivityConst.DUNGEON_DEVOUR_STONE_OP_TYPE_CHOOSE:
                return processStoneOpChoose(chooseIndex, stoneChangeInfo);
            default:
                Log.activity.warn("===转生魔剑吞噬魔石操作失败，type={}无法处理 humanId={}", type, m_humanObj.id);
                return false;
        }
    }

    public boolean processStoneOpCost(){
        ConfDungeonChapter confChapter = ConfDungeonChapter.get(m_chapterId);
        if(confChapter == null){
            return false;
        }
        if(!m_devourStoneRandomInfo.isEmpty()){
            Log.activity.warn("===转生魔剑吞噬魔石失败，随机增益列表已存在 humanId={}", m_humanObj.id);
            return false;
        }
        int costNum = getCostNum(confChapter.stone_cost, m_devourStoneNum);
        if(m_stoneNum < costNum){
            Log.activity.warn("===转生魔剑吞噬魔石失败，魔石不足 m_stoneNum={} < costNum={} humanId={}", m_stoneNum, costNum, m_humanObj.id);
            return false;
        }
        List<Integer> randomProfitList = getStoneRandomProfitList();
        if(randomProfitList.isEmpty()){
            Log.activity.warn("===转生魔剑吞噬魔石失败，可选择的随机列表为空 humanId={}", m_humanObj.id);
            return false;
        }
        for(int profitSn : randomProfitList){
            Define.p_key_value.Builder randomProfit = Define.p_key_value.newBuilder().setK(profitSn).setV(getDevourStoneProfitLevel(profitSn) + 1);
            m_devourStoneRandomInfo.add(randomProfit);
        }
        m_devourStoneNum++;
        updateStoneNum(-costNum);
//        Log.activity.info("===转生魔剑吞噬魔石成功，m_devourStoneNum={} m_stoneNum={} m_devourStoneRandomInfo={} humanId={}",
//                m_devourStoneNum, m_stoneNum,
//                m_devourStoneRandomInfo.stream().map(v->Arrays.asList(v.getK(), v.getV())).collect(Collectors.toList()), m_humanObj.id);
        return true;
    }

    public boolean processStoneOpRefresh(){
        ConfDungeonChapter confChapter = ConfDungeonChapter.get(m_chapterId);
        if(confChapter == null){
            return false;
        }
        if(m_devourStoneRandomInfo.isEmpty()){
            Log.activity.warn("===转生魔剑刷新魔石增益列表失败，随机增益列表不存在 humanId={}", m_humanObj.id);
            return false;
        }
        int costNum = getCostNum(confChapter.buff_refresh_cost, m_devourRefreshBuffNum);
        // 刷新增益列表，消耗道具
        int refreshItemId = confChapter.buff_refresh_item;
        if(!canCostDropItem(refreshItemId, costNum)){
            Log.activity.warn("===转生魔剑刷新魔石增益列表失败，道具[{}]不足{}个 humanId={}", refreshItemId, costNum, m_humanObj.id);
            return false;
        }
        m_devourStoneRandomInfo.clear();
        List<Integer> randomProfitList = getStoneRandomProfitList();
        for(int profitSn : randomProfitList){
            Define.p_key_value.Builder randomProfit = Define.p_key_value.newBuilder().setK(profitSn).setV(getDevourStoneProfitLevel(profitSn) + 1);
            m_devourStoneRandomInfo.add(randomProfit);
        }
        m_devourRefreshBuffNum++;
        // 刷新列表后，再扣除物品
        costDropItem(refreshItemId, costNum);
//        Log.activity.info("===转生魔剑刷新魔石增益列表成功，m_devourRefreshBuffNum={} m_devourStoneRandomInfo={} humanId={}",
//                m_devourRefreshBuffNum,
//                m_devourStoneRandomInfo.stream().map(v->Arrays.asList(v.getK(), v.getV())).collect(Collectors.toList()),
//                m_humanObj.id);
        return true;
    }

    public boolean processStoneOpChoose(int chooseIndex, Define.p_key_value.Builder stoneChangeInfo){
        if(chooseIndex < 0 || chooseIndex >= m_devourStoneRandomInfo.size()){
            Log.activity.warn("===转生魔剑选择魔石增益失败，随机增益[{}]不存在 humanId={}", chooseIndex, m_humanObj.id);
            return false;
        }
        Define.p_key_value.Builder chooseProfit = m_devourStoneRandomInfo.get(chooseIndex);
        if(chooseProfit == null){
            Log.activity.warn("===转生魔剑选择魔石增益失败，随机增益[{}]不存在 humanId={}", chooseIndex, m_humanObj.id);
            return false;
        }
        // 增加魔石增益等级
        addDevourStoneProfitLevel(chooseProfit.getK(), stoneChangeInfo);
        m_devourStoneRandomInfo.clear();
        m_devourRefreshBuffNum = 0;
        // 根据玩家血上限变化，更新玩家当前血量
        updatePlayerHpByHpMaxChange();
//        Log.activity.info("===转生魔剑选择魔石增益成功，stoneChangeInfo={} humanId={}",
//                Arrays.asList(stoneChangeInfo.getK(), stoneChangeInfo.getV()), m_humanObj.id);
        return true;
    }

    /**
     * 获取吞噬魔石随机增益id
     * @return
     */
    public List<Integer> getStoneRandomProfitList(){
        List<Integer> profitSnList = new ArrayList<>();
        List<Integer> weightList = new ArrayList<>();
        for(ConfDungeonBuff conf : ConfDungeonBuff.findAll()){
            if(getDevourStoneProfitLevel(conf.sn) < conf.level_max){
                profitSnList.add(conf.sn);
                weightList.add(conf.weight);
            }
        }
        return getRandomListByWeight(profitSnList, weightList, Math.min(weightList.size(), ActivityConst.DUNGEON_DEVOUR_STONE_PROFITS_NUM));
    }

    /**
     * 获取魔石增益等级
     * @param sn
     * @return
     */
    public long getDevourStoneProfitLevel(long sn){
        return m_devourStoneInfo.getOrDefault(sn, 0L);
    }

    /**
     * 增加魔石增益等级
     *
     * @param sn
     * @param stoneChangeInfo
     */
    public void addDevourStoneProfitLevel(long sn, Define.p_key_value.Builder stoneChangeInfo){
        ConfDungeonBuff conf = ConfDungeonBuff.get(Utils.intValue(sn));
        if(conf == null){
            return;
        }
        long curLevel = getDevourStoneProfitLevel(sn);
        long newLevel = Math.min(curLevel + 1, conf.level_max);
        m_devourStoneInfo.put(sn, newLevel);
        stoneChangeInfo.setK(sn).setV(newLevel);
    }

    /**
     * 场景是否关闭
     * @return
     */
    public boolean isClosed(){
        return m_isClosed;
    }

    /**
     * 转生魔剑结算
     * @param isWin
     * @return
     */
    public boolean settlement(boolean isWin) {
        if(m_isClosed){
            Log.activity.warn("===转生魔剑结算失败，已经结算完成 humanId={}", m_humanObj.id);
            return false;
        }
        ConfDungeonChapter confChapter = ConfDungeonChapter.get(m_chapterId);
        if(confChapter == null || confChapter.attr_points == null || confChapter.attr_points.length != 3){
            Log.activity.warn("===转生魔剑结算失败，ConfDungeonChapter[{}]配置错误 humanId={}", m_chapterId, m_humanObj.id);
            return false;
        }
        long attrScore = getPlayerAttr(ActivityConst.DUNGEON_ATTR_HP_MAX) * confChapter.attr_points[0] +
                getPlayerAttr(ActivityConst.DUNGEON_ATTR_ATK) * confChapter.attr_points[1] +
                getPlayerAttr(ActivityConst.DUNGEON_ATTR_DEF) * confChapter.attr_points[2];
        int adventureScore = m_maxLayer * confChapter.layer_points + m_exploreAreaNum * confChapter.area_points +
                m_killMonsterNum * confChapter.enemy_points + m_killBossNum * confChapter.boss_points + Utils.intValue(attrScore);
        int addExp = confChapter.exp;
        int addCoin = confChapter.coin;
        // 获取金币加成
        int addPercent = 0;
        for(Define.p_dungeon_effect.Builder effect : m_persistEffects){
            ConfDungeonEffect confEffect = ConfDungeonEffect.get(effect.getSn());
            if(confEffect != null && confEffect.value != null && confEffect.value[0] != null){
                if(confEffect.type == ActivityConst.DUNGEON_EFFECT_ADD_GOLD_RATE){
                    addPercent += confEffect.value[0][0];
                }
            }
        }
        int settleExtraAddGold = 0;
        for(Map.Entry<Long, Long> profit : m_devourStoneInfo.entrySet()){
            ConfDungeonBuff confBuff = ConfDungeonBuff.get(Utils.intValue(profit.getKey()));
            if(confBuff != null && confBuff.value_base != null && confBuff.value_factor != null){
                if(confBuff.type == ActivityConst.DUNGEON_PROFIT_ADD_GOLD_ON_SETTLE){
                    int value = confBuff.value_base[0] + confBuff.value_factor[0] * (Utils.intValue(profit.getValue()) - 1);
                    settleExtraAddGold += value;
                }
            }
        }
        // （关卡基础金币+评分*金币系数）*金币加成+探索额外获取
        int addGold = (confChapter.base_gold + adventureScore * confChapter.gold_factor / ActivityConst.DUNGEON_RATE_10000)
                * (ActivityConst.DUNGEON_RATE_10000 + addPercent) / ActivityConst.DUNGEON_RATE_10000 + settleExtraAddGold;
        int[][] rewards = {{ActivityConst.DUNGEON_ITEM_ID_EXP, addExp}, {ActivityConst.DUNGEON_ITEM_ID_COIN, addCoin}, {ActivityConst.DUNGEON_ITEM_ID_GOLD, addGold}};
        ProduceManager.inst().produceAdd(m_humanObj, rewards, MoneyItemLogKey.转生魔剑);

        m_isClosed = true;
        if(isWin){
            m_data.setPassedChapterId(m_chapterId);
        }
        MsgAct2.act_dungeon_settle_info_s2c.Builder msg = MsgAct2.act_dungeon_settle_info_s2c.newBuilder();
        msg.setActType(m_data.objectData.type);
        msg.setChapterId(m_chapterId);
        msg.setMaxLayer(m_maxLayer);
        msg.setExploreAreaNum(m_exploreAreaNum);
        msg.setKillMonsterNum(m_killMonsterNum);
        msg.setKillBossNum(m_killBossNum);
        msg.setAttrScore(Utils.intValue(attrScore));
        msg.setAdventureScore(adventureScore);
        msg.setAddGold(addGold);
        msg.setAddExp(addExp);
        msg.setAddCoin(addCoin);
        msg.setIsWin(isWin ? 1 : 0);
        m_humanObj.sendMsg(msg);
        Log.activity.info("===转生魔剑结算成功，scene={} humanId={}", this, m_humanObj.id);
        return true;
    }
}
