package org.gof.demo.worldsrv.activity.data.controldata;

import com.alibaba.fastjson.JSONArray;
import io.vertx.core.json.JsonArray;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.activity.ActivityVo;
import org.gof.demo.worldsrv.character.HumanObject;

import java.util.ArrayList;
import java.util.List;

public class ControlCohesionData implements IControlData {
    public List<Integer> gotRewardList = new ArrayList<>();

    @Override
    public void create(HumanObject humanObj, ActivityVo vo) {
    }

    public ControlCohesionData(String json) {
        gotRewardList = Utils.strToIntList(json);
    }

    @Override
    public String toJSON() {
        JSONArray array = new JSONArray();
        array.addAll(gotRewardList);
        return array.toJSONString();
    }
}
