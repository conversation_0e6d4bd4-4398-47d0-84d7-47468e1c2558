package org.gof.demo.worldsrv.activity.data;

import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.Port;
import org.gof.core.interfaces.ISerilizable;
import org.gof.core.support.Time;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.entity.ActControlData;
import org.gof.demo.worldsrv.support.Log;

import java.io.IOException;
import java.util.*;

public class ActivityControlData implements ISerilizable {
    private Map<Integer, ActivityControlObjectData> controlObjectDataMap = new HashMap<>();

    public ActivityControlData() { }

    //登录的时候 解析数据
    public void init(List<ActControlData> list, HumanObject humanObj) {
        for (ActControlData controlData : list) {
            int activityType = controlData.getActivityType();
            if (controlObjectDataMap.containsKey(activityType)) {
                // 处理重复活动的情况
                ActivityControlObjectData existingData = controlObjectDataMap.get(activityType);
                long existingCloseTime = existingData.getActControlData().getCloseTime();
                long newCloseTime = controlData.getCloseTime();
                long currentTime = Utils.getTimeSec();

                // 如果现有活动未过期，新活动已过期，保留现有活动，删除新活动
                if (existingCloseTime > currentTime && newCloseTime <= currentTime) {
                    Log.game.error("玩家活动数据初始化时发现重复活动，保留现有活动，删除列表排后。humanId={}, activityId={}", controlData.getHumanId(), controlData.getActivitySn());
                    controlData.remove();
                    continue;
                }

                // 如果现有活动已过期，新活动未过期，更新为新活动
                if (existingCloseTime <= currentTime && newCloseTime > currentTime) {
                    controlObjectDataMap.put(activityType, new ActivityControlObjectData(activityType, controlData, humanObj));
                    Log.game.error("玩家活动数据初始化时发现重复活动，活动已过期删除过期活动，列表排后的未过期，更新为列表排后。humanId={}, activityId={}", existingData.getActControlData().getHumanId(), existingData.getActControlData().getActivitySn());
                    existingData.getActControlData().remove();
                    continue;
                }

                // 如果都已过期，删除列表排前的
                if (existingCloseTime <= currentTime && newCloseTime <= currentTime) {
                    controlObjectDataMap.put(activityType, new ActivityControlObjectData(activityType, controlData, humanObj));
                    Log.game.error("玩家活动数据初始化时发现重复过期活动，删除列表排前的。humanId={}, activityId={}", existingData.getActControlData().getHumanId(), existingData.getActControlData().getActivitySn());
                    existingData.getActControlData().remove();
                    continue;
                }

                // 如果都未过期，保留关闭时间较晚的那个
                if (newCloseTime >= existingCloseTime) {
                    String useExisting = existingData.getActControlData().getExtendJSON();
                    if(useExisting == null || !useExisting.equals("1")){
                        controlObjectDataMap.put(activityType, new ActivityControlObjectData(activityType, controlData, humanObj));
                        controlData.setExtendJSON("1");
                    }
                }
            } else {
                // 不存在重复活动，直接添加
                controlObjectDataMap.put(activityType, new ActivityControlObjectData(activityType, controlData, humanObj));
            }
        }
    }

    public Map<Integer, ActivityControlObjectData> getControlObjectDataMap() {
        return controlObjectDataMap;
    }

    /**
     * 保存该类型的数据
     * @param controlType
     * @param
     * @param
     */
    public void addControlObjectData(int controlType, ActivityControlObjectData data){
        controlObjectDataMap.put(controlType, data);
    }

    /***
     * 获取该玩家该活动数据
     * @param controlType 活动类型
     * @return
     */
    public ActivityControlObjectData getControlObjectData(int controlType){
        return controlObjectDataMap.get(controlType);
    }

    public void removerObjectData(int controlType){
        ActivityControlObjectData data = controlObjectDataMap.remove(controlType);
        if (data != null){
            data.getActControlData().remove();
        }
    }

    /**
     * 获取过期的活动类型列表
     * @return 过期活动的sn列表
     */
    public List<Integer> getTimeoutActivitySn() {
        long now = Port.getTime() / Time.SEC;
        List<Integer> timeoutList = new ArrayList<>();

        // 遍历所有活动数据
        for (Map.Entry<Integer, ActivityControlObjectData> entry : controlObjectDataMap.entrySet()) {
            ActivityControlObjectData objectData = entry.getValue();

            // 检查活动是否过期
            if (objectData != null && objectData.getActControlData() != null) {
                long closeTime = objectData.getActControlData().getCloseTime();
                if (closeTime <= now) {
                    timeoutList.add(objectData.getActControlData().getActivitySn());
                }
            }
        }

        return timeoutList;
    }

    @Override
    public void writeTo(OutputStream out) throws IOException {
        out.write(controlObjectDataMap);
    }

    @Override
    public void readFrom(InputStream in) throws IOException {
        controlObjectDataMap.clear();
        controlObjectDataMap.putAll(in.<Map<Integer, ActivityControlObjectData>>read());
    }
    public void updateAll(){
        for (ActivityControlObjectData data : controlObjectDataMap.values()) {
            data.getActControlData().update();
        }
    }
}
