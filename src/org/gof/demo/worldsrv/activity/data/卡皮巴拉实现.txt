//协议设计
protobuf协议格式像这样：message act_mashle_alchemy_farm_info_s2c {
             	option (msgid) = 6507;
             	uint32 is_push = 1;
             	uint64 role_id = 2;
             	uint32 type = 3;
             	uint32 start_time = 4;
             	uint32 time = 5;
             	uint64 damage = 6;
             	uint32 cnt = 7;
             	uint32 battle_cnt = 8;
             	repeated uint64 share_list = 9 [packed=false];
             }

需要1：卡皮巴拉活动信息:
需要2：卡皮巴拉活动进入关卡：需要扣除体力值
需要3：卡皮巴拉活动结束关卡：
需要4：卡皮巴拉关卡内进度上报：
需要5：天赋升级：定义属性类型常量ATTR_HP,ATTR_ATK,ATTR_DEF，客户端发来属性类型，读取配置ConfSlimeTalent conf = ConfSlimeTalent.get(当前等级),ConfSlimeTalent.sn代表等级，
ConfSlimeTalent.hp_add代表血量增加，ConfSlimeTalent.atk_add代表攻击增加，ConfSlimeTalent.def_add代表防御增加。给ControlStaminaData中的对应类型升级talentLevelMap。
ConfSlimeTalent.cost代表消耗的数量。返回给客户端升级成功后的天赋数据。同时会属性变更
需要7：大吉转盘抽奖：
需要8：体力值刷新：
需要9：战力和属性的变化：

message p_slime_event {
	uint32 event_sn = 1;
	uint32 state = 2;
	uint32 effect_sn = 3;
	uint64 curr_hp = 4;
	repeated p_key_value_list effect_value = 5;//效果值
	repeated p_key_value fight_attr = 6;//关卡内属性
}

message p_slime_day {
	uint32 day = 1;
	repeated p_slime_event event_list = 2;
}
//卡皮巴拉数据
message act_slime_info_c2s {
    option (msgid) = 6511;
    uint32 act_type = 1;
}

message act_slime_info_s2c {
    option (msgid) = 6512;
    uint32 act_type = 1;
    uint32 level = 2;
    uint64 exp = 3;
    uint64 combat = 4;
    uint32 rank = 5;
    uint32 stamina = 6;//体力
    uint64 next_recover_time = 7;//体力下次恢复时间
    uint32 cost_stamina = 8;//累计消耗体力
    repeated p_key_value attr_list = 9;
    uint32 fight_stage = 10;
    uint64 curr_hp = 11;
    repeated p_key_value fight_attr = 12;
   	repeated p_slime_day fight_info = 13;
    repeated uint32 passed_stage = 14;//通关关卡
    repeated p_key_value talent_lv = 15;
    repeated p_key_value skill_pos_sn = 16;
    repeated p_key_value wheel_attr = 17;
    uint32 wheel_count = 18;//可转次数
    uint32 big_luck_count = 19;//大吉余数
}


// 进入关卡
message act_slime_enter_stage_c2s {
    option (msgid) = 6513;
    uint32 act_type = 1;
    uint32 stage_id = 2;
}

message act_slime_enter_stage_s2c {
    option (msgid) = 6514;
    uint32 act_type = 1;
    uint32 stage_id = 2;
    uint32 result = 3;
}

// 结束关卡
message act_slime_finish_stage_c2s {
    option (msgid) = 6515;
    uint32 act_type = 1;
    uint32 stage_id = 2;
    uint32 result = 3; // 0失败 1成功
}

message act_slime_finish_stage_s2c {
    option (msgid) = 6516;
    uint32 act_type = 1;
    uint32 stage_id = 2;
    uint32 result = 3;
}

// 关卡内进度上报
message act_slime_stage_progress_c2s {
    option (msgid) = 6517;
    uint32 act_type = 1;
    p_slime_day day_info = 2;
}

message act_slime_stage_progress_s2c {
    option (msgid) = 6518;
    uint32 act_type = 1;
    uint32 result = 2;
}

// 天赋升级
message act_slime_talent_upgrade_c2s {
    option (msgid) = 6519;
    uint32 act_type = 1;
    uint32 attr_type = 2;
}

message act_slime_talent_upgrade_s2c {
    option (msgid) = 6540;
    uint32 act_type = 1;
    repeated p_key_value talent_level = 2;
    repeated p_key_value attr_list = 3;
}

// 大吉转盘抽奖
message act_slime_wheel_draw_c2s {
    option (msgid) = 6541;
    uint32 act_type = 1;
    uint32 wheel_id = 2;
}

message act_slime_wheel_draw_s2c {
    option (msgid) = 6542;
    uint32 act_type = 1;
    uint32 wheel_id = 2;
    uint32 index = 3;
    repeated p_key_value attr_list = 4;
}

// 体力值刷新
message act_slime_refresh_c2s {
    option (msgid) = 6543;
    uint32 act_type = 1;
}


// 体力值刷新
message act_slime_refresh_s2c {
    option (msgid) = 6544;
    uint32 act_type = 1;
    uint32 slime = 2;
    uint64 next_recover_time = 3;
}

// 战力和属性变化
message act_slime_attr_change_s2c {
    option (msgid) = 6545;
    uint32 act_type = 1;
    uint64 combat = 2;
    repeated p_key_value attr_list = 3;
}

找到对应的fightAttrtype给加上数值：
1001=攻击固定值 给key是1的加
3001=攻击百分比 给key是1的加
1002=生命固定值 给key是2的加
3002=生命百分比 给key是2的加
1024=防御固定值 给key是24的加
3024=防御百分比 给key是24的加

