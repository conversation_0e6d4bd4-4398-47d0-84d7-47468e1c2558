1:协议设计
protobuf协议格式像这样：message act_mashle_alchemy_farm_info_s2c {
             	option (msgid) = 6507;
             	uint32 is_push = 1;
             	uint64 role_id = 2;
             	uint32 type = 3;
             	uint32 start_time = 4;
             	uint32 time = 5;
             	uint64 damage = 6;
             	uint32 cnt = 7;
             	uint32 battle_cnt = 8;
             	repeated uint64 share_list = 9 [packed=false];
             }
2:配置的读取：单主键例子：ConfMonopolyGrid confMonopolyGrid = ConfMonopolyGrid.get(sn);confMonopolyGrid.grid_type
3:配置的读取：多主键例子：ConfMonopolyGridLevel_0 confMonopolyGridLevel = ConfMonopolyGridLevel.get(boardId,type);confMonopolyGridLevel.level
4:全局表字段的读取：ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.legacy_team_chapter_assist_reward.SN);
5：道具的扣除：ProduceManager.inst().checkAndCostItem(humanObj, confRandom.desc_parm[0], confRandom.desc_parm[1], MoneyItemLogKey.大富翁);
6:道具的增加：ProduceManager.inst().produceAdd(humanObj, reward, MoneyItemLogKey.大富翁);
7：协议变成字符串：String s = Utils.compressProtoLZ4(Builder.build()),字符串变成协议：Builder = public static <T extends Message> T decompressProtoLZ4(String compressedData, Parser<T> parser)


