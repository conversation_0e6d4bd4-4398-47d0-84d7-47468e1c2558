package org.gof.demo.worldsrv.activity;
import org.gof.demo.worldsrv.activity.calculator.*;
import org.gof.demo.worldsrv.activity.data.controldata.*;
import org.gof.demo.worldsrv.support.StringZipUtils;

import java.lang.reflect.Constructor;
import java.util.*;

import static org.gof.demo.worldsrv.activity.ActivityControlType.*;


public class ActivityControlTypeFactory {
    private static final Map<Class<? extends IActivityControl>, Set<Integer>> typeDataMap = new HashMap<>();

    private static final Set<Integer> rankRushSet = new HashSet<>(Arrays.asList(Act_6, Act_7, Act_8, Act_9, Act_10, Act_11, Act_12, Act_13, Act_14));
    private static final Set<Integer> warTokenSet = new HashSet<>(Arrays.asList(Act_6210, Act_23, Act_28, Act_44, Act_167, Act_1167, Act_2052, Act_2053, Act_2167,
            Act_2100, Act_2101, Act_3011, Act_3167, Act_3267, Act_3367, Act_3467, Act_3567, Act_3667, Act_3767, Act_3867, Act_4067, Act_4167, Act_4267, Act_4467,Act_5014,
            Act_3968, Act_4567, Act_4668, Act_4780, Act_5012, Act_4867, Act_4967, Act_1203, Act_2301, Act_5067, Act_5167, Act_6303, Act_6403));
    private static final Set<Integer> luckyCatSet = new HashSet<>(Arrays.asList(Act_69));
    private static final Set<Integer> mountDrawSet = new HashSet<>(Arrays.asList(Act_5, Act_15, Act_16, Act_17, Act_166, Act_1166, Act_2166,
            Act_3003, Act_3166, Act_3266, Act_3366, Act_3466, Act_3566, Act_3666, Act_3766, Act_3866, Act_4066, Act_4166, Act_4266, Act_4466, Act_4566, Act_4866, Act_4966,
            Act_5066, Act_6302, Act_6402));
    private static final Set<Integer> festivalSearchSet = new HashSet<>(Arrays.asList(Act_168, Act_1168, Act_2168, Act_3168, Act_3368, Act_3868, Act_4068, Act_4168, Act_4268, Act_4468, Act_4568, Act_4968, Act_5068, Act_6305));
    private static final Set<Integer> breakEggSet = new HashSet<>(Arrays.asList(Act_170, Act_1170, Act_2170, Act_3170, Act_3370, Act_3470, Act_3870, Act_4270));
    private static final Set<Integer> boxTowerSet = new HashSet<>(Arrays.asList(Act_3270, Act_3570, Act_3670, Act_3770, Act_4170, Act_4470, Act_4870, Act_5070, Act_6406));
    private static final Set<Integer> LoginSet = new HashSet<>(Arrays.asList(Act_171, Act_1171, Act_2171, Act_3002, Act_3171, Act_3271,
            Act_3371, Act_3471, Act_3571, Act_3671, Act_3771, Act_3871, Act_4000, Act_4071, Act_4001, Act_4471, Act_4571));//签到,登录天数
    private static final Set<Integer> LoginSet2 = new HashSet<>(Arrays.asList(Act_4304, Act_4777, Act_1201, Act_2203, Act_5051, Act_6205));//签到,活动开启天数
    private static final Set<Integer> generalSet = new HashSet<>(Arrays.asList());
    private static final Set<Integer> type_20Set = new HashSet<>(Arrays.asList(Act_20));
    private static final Set<Integer> sevenTrialSet = new HashSet<>(Arrays.asList(
            Act_56, Act_57, Act_58, Act_59, Act_60, Act_61, Act_62,
            Act_84, Act_85, Act_86, Act_87, Act_88, Act_89, Act_90
    ));
    private static final Set<Integer> miniGameSet = new HashSet<>(Arrays.asList(Act_169, Act_1169, Act_2169, Act_4069, Act_4469, Act_4869, Act_5069));
    private static final Set<Integer> snsShareSet = new HashSet<>(Arrays.asList(Act_2012, Act_2013, Act_2014, Act_2015));
    private static final Set<Integer> banabataFlowerSet = new HashSet<>(Arrays.asList(Act_25, Act_3773));
    private static final Set<Integer> challengeBossSet = new HashSet<>(Arrays.asList(Act_72));
    private static final Set<Integer> miniGameSevenTrialSet = new HashSet<>(Arrays.asList(Act_73, Act_3269, Act_3569, Act_5169));
    private static final Set<Integer> miniGameMatch3Set = new HashSet<>(Arrays.asList(Act_52, Act_3169, Act_3369, Act_3669, Act_4569, Act_4969));
    private static final Set<Integer> dayPaySet = new HashSet<>(Arrays.asList(Act_2004));
    private static final Set<Integer> monopolySet = new HashSet<>(Arrays.asList(Act_4070, Act_4303, Act_4776));//大富翁
    private static final Set<Integer> slimeMixSet = new HashSet<>(Arrays.asList(Act_4302));//史莱姆融合
    private static final Set<Integer> shareGameSet = new HashSet<>(Arrays.asList(Act_4269));//吃豆豆

    public static final Set<Integer> slimeSet = new HashSet<>(Arrays.asList(Act_4300, Act_6211));//史莱姆
    private static final Set<Integer> rankCopySet = new HashSet<>(Arrays.asList(Act_3966, Act_3967, Act_4305, Act_4306, Act_4666, Act_4667,
            Act_4772, Act_4773, Act_6212, Act_6213));
    /**
     * 黄金塔活动
     */
    private static final Set<Integer> goldenTowerSet = new HashSet<>(Arrays.asList(Act_4570, Act_4970, Act_5170, Act_6306));
    /**
     * PVP加成战斗活动（圣诞献礼）
     */
    public static final Set<Integer> pvpClueSet = new HashSet<>(Arrays.asList(Act_3268, Act_3568, Act_3668, Act_4868, Act_5168, Act_6405));
    /**
     * 打地鼠小游戏
     */
    public static final Set<Integer> whackMoleSet = new HashSet<>(Arrays.asList(Act_64, Act_3469, Act_3769, Act_4169, Act_6404));
    /**
     * 全民赶年兽
     */
    public static final Set<Integer> bossSet = new HashSet<>(Arrays.asList(Act_3473, Act_4373, Act_6202));
    /**
     * 年兽来袭
     */
    public static final Set<Integer> yearBeastSet = new HashSet<>(Arrays.asList(Act_3468, Act_3869, Act_6304));
    /**
     * 超值卡
     */
    public static final Set<Integer> weekCardSet = new HashSet<>(Arrays.asList(Act_2003));
    /**
     * 限时礼盒
     */
    public static final Set<Integer> luckyBagSet = new HashSet<>(Arrays.asList(Act_3012, Act_3013));
    /**
     * 浇灌树
     */
    public static final Set<Integer> waterTreeSet = new HashSet<>(Arrays.asList(Act_3010));
    /**
     * 跨服战
     */
    public static final Set<Integer> crossWarSet = new HashSet<>(Arrays.asList(Act_33));
    /**
     * 百魔夜行
     */
    public static final Set<Integer> legionInvasionSet = new HashSet<>(Arrays.asList(Act_3965));
    public static final Set<Integer> refreshGiftSet = new HashSet<>(Arrays.asList(Act_2008));// 多档刷新礼包
    public static final Set<Integer> castleSet = new HashSet<>(Arrays.asList(Act_4665));// 大战魔王城
    /**
     * 转生魔剑
     */
    private static final Set<Integer> dungeonSet = new HashSet<>(Arrays.asList(Act_4771));
    public static final Set<Integer> campSet = new HashSet<>(Arrays.asList(Act_4774));// 阵营对抗
    public static final Set<Integer> dungeonCraftSet = new HashSet<>(Arrays.asList(Act_4775));// 装备打造
    public static final Set<Integer> skinTrySet = new HashSet<>(Arrays.asList(Act_4778, Act_6206));// 皮肤试用
    public static final Set<Integer> cardEliminateSet = new HashSet<>(Arrays.asList(Act_5011));// 卡牌消除
    public static final Set<Integer> groupGiftSet = new HashSet<>(Arrays.asList(Act_1206));// 团购
    public static final Set<Integer> doubleDrawSet = new HashSet<>(Arrays.asList(Act_1202, Act_5166));// 合服转盘
    public static final Set<Integer> ControlCohesionData = new HashSet<>(Arrays.asList(Act_1204));//全服提交道具
    public static final Set<Integer> guildPaySet = new HashSet<>(Arrays.asList(Act_1208));// 公会充值活动
    public static final Set<Integer> addFriendSet = new HashSet<>(Arrays.asList(Act_1205));// 交友会
    public static final Set<Integer> accumulateScoreSet = new HashSet<>(Arrays.asList(Act_2206));// 累计积分
    public static final Set<Integer> airdropGiftSet = new HashSet<>(Arrays.asList(Act_8001));// 空投礼包
    public static final Set<Integer> blackMarketSet = new HashSet<>(Arrays.asList(Act_8005));// 黑市活动
    public static final Set<Integer> fruitMergeSet = new HashSet<>(Arrays.asList(Act_5013));// 合成大西瓜
    public static final Set<Integer> collectTradeSet = new HashSet<>(Arrays.asList(Act_5054));// 集换字
    public static final Set<Integer> luckyLotterySet = new HashSet<>(Arrays.asList(Act_5053));// 福签抽奖
    public static final Set<Integer> wuzhiLoveSet = new HashSet<>(Arrays.asList(Act_6208));// 无职联动-好感度活动
    public static final Set<Integer> figureCollectionSet = new HashSet<>(Arrays.asList(Act_6204));// 无职联动-手办收集活动
    public static final Set<Integer> wuzhiResearchSet = new HashSet<>(Arrays.asList(Act_6203));// 无职联动-七星研究活动

    static {
        typeDataMap.put(ActivityControlRankRush.class, rankRushSet);
        typeDataMap.put(ActivityControlWarToken.class, warTokenSet);
        typeDataMap.put(ActivityControlLuckyCat.class, luckyCatSet);
        typeDataMap.put(ActivityControlMountDraw.class, mountDrawSet);
        typeDataMap.put(ActivityControlFestivalSearch.class, festivalSearchSet);
        typeDataMap.put(ActivityControlBreakEgg.class, breakEggSet);
        typeDataMap.put(ActivityControlLogin.class, LoginSet);
        typeDataMap.put(ActivityControlLogin2.class, LoginSet2);
        typeDataMap.put(ActivityControlGeneral.class, generalSet);
        typeDataMap.put(ActivityControlType_20.class, type_20Set);
        typeDataMap.put(ActivityControlSevenTrial.class, sevenTrialSet);
        typeDataMap.put(ActivityControlMiniGame.class, miniGameSet);
        typeDataMap.put(ActivityControlSnsShareMail.class, snsShareSet);
        typeDataMap.put(ActivityControlTanabataFlower.class, banabataFlowerSet);
        typeDataMap.put(ActivityControlChallengeBoss.class, challengeBossSet);
        typeDataMap.put(ActivityControlMiniGameSevenTrial.class, miniGameSevenTrialSet);
        typeDataMap.put(ActivityControlMiniGameMatch3.class, miniGameMatch3Set);
        typeDataMap.put(ActivityControlPVPClue.class, pvpClueSet);
        typeDataMap.put(ActivityControlBoxTower.class, boxTowerSet);
        typeDataMap.put(ActivityControlMiniGameWhackMole.class, whackMoleSet);
        typeDataMap.put(ActivityControlBoss.class, bossSet);
        typeDataMap.put(ActivityControlMiniGameYearBeast.class, yearBeastSet);
        typeDataMap.put(ActivityControlWeekCard.class, weekCardSet);
        typeDataMap.put(ActivityControlLuckyBag.class, luckyBagSet);
        typeDataMap.put(ActivityControlWaterTree.class, waterTreeSet);
        typeDataMap.put(ActivityControlCrossWar.class, crossWarSet);
        typeDataMap.put(ActivityControlDayPay.class, dayPaySet);
        typeDataMap.put(ActivityControlLegionInvasion.class, legionInvasionSet);
        typeDataMap.put(ActivityControlMonopoly.class, monopolySet);
        typeDataMap.put(ActivityControlSlimeMix.class, slimeMixSet);
        typeDataMap.put(ActivityControlShareGame.class, shareGameSet);
        typeDataMap.put(ActivityControlSlime.class, slimeSet);
        typeDataMap.put(ActivityControlRankCopy.class, rankCopySet);
        typeDataMap.put(ActivityControlRefreshGift.class, refreshGiftSet);
        typeDataMap.put(ActivityControlGoldenTower.class, goldenTowerSet);
        typeDataMap.put(ActivityControlCastle.class, castleSet);
		typeDataMap.put(ActivityControlDungeon.class, dungeonSet);
        typeDataMap.put(ActivityControlCamp.class, campSet);
        typeDataMap.put(ActivityControlDungeonCraft.class, dungeonCraftSet);
        typeDataMap.put(ActivityControlSkinTry.class, skinTrySet);
        typeDataMap.put(ActivityControlCardEliminate.class, cardEliminateSet);
        typeDataMap.put(ActivityControlGroupGift.class, groupGiftSet);
        typeDataMap.put(ActivityControlDoubleDraw.class, doubleDrawSet);
        typeDataMap.put(ActivityControlGuildPay.class, guildPaySet);
        typeDataMap.put(ActivityControlCohesion.class, ControlCohesionData);
        typeDataMap.put(ActivityControlAddFriend.class, addFriendSet);
        typeDataMap.put(ActivityControlAccumulateScore.class, accumulateScoreSet);
        typeDataMap.put(ActivityControlAirdropGift.class, airdropGiftSet);
        typeDataMap.put(ActivityControlBlackMarket.class, blackMarketSet);
        typeDataMap.put(ActivityControlFruitMerge.class, fruitMergeSet);
        typeDataMap.put(ActivityControlCollectTrade.class, collectTradeSet);
        typeDataMap.put(ActivityControlLuckyLottery.class, luckyLotterySet);
        typeDataMap.put(ActivityControlWuzhiLove.class, wuzhiLoveSet);
        typeDataMap.put(ActivityControlFigureCollection.class, figureCollectionSet);
        typeDataMap.put(ActivityControlWuzhiResearch.class, wuzhiResearchSet);
    }

    public static List<Integer> getTypeList(Class<? extends IActivityControl> clazz) {
        return new ArrayList<>(typeDataMap.get(clazz));
    }

    public static Set<Integer> getTypeSet(Class<? extends IActivityControl> clazz) {
        return typeDataMap.get(clazz);
    }

    public static IActivityControl getTypeData(int type) {
        for (Map.Entry<Class<? extends IActivityControl>, Set<Integer>> entry : typeDataMap.entrySet()) {
            if (entry.getValue().contains(type)) {
                return createInstance(entry.getKey(), type);
            }
        }
        return createInstance(ActivityControlGeneral.class, type);
    }

    private static IActivityControl createInstance(Class<? extends IActivityControl> clazz, int type) {
        try {
            Constructor<? extends IActivityControl> constructor = clazz.getDeclaredConstructor(int.class);
            return constructor.newInstance(type);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static IControlData getTypeClass(int type, String json) {
        json = StringZipUtils.unzip(json);
        IControlData controlData = null;

        if(warTokenSet.contains(type)){
            controlData = new ControlWarTokenData(json);
        } else if(luckyCatSet.contains(type)){
            controlData = new ControlLuckyCatData(json);
        } else if(mountDrawSet.contains(type)){
            controlData = new ControlMountDrawData(json);
        } else if(festivalSearchSet.contains(type)){
            controlData = new ControlFestivalSearchData(json);
        } else if(breakEggSet.contains(type)){
            controlData = new ControlBreakEggData(json);
        } else if(LoginSet.contains(type)){
            controlData = new ControlLoginData(json);
        }else if(LoginSet2.contains(type)){
            controlData = new ControlLoginData(json);
        } else if(miniGameSet.contains(type)) {
            controlData = new ControlMiniGameData(json);
        } else if (snsShareSet.contains(type)) {
            controlData = new ControlSnsShareData(json);
        } else if(banabataFlowerSet.contains(type)){
            controlData = new ControlTanabataFlowerData(json);
        } else if (challengeBossSet.contains(type)) {
            controlData = new ControlChallengeData(json);
        } else if (pvpClueSet.contains(type)) {
            controlData = new ControlPVPClueData(json);
        } else if(boxTowerSet.contains(type)){
            controlData = new ControlBoxTowerData(json);
        } else if (whackMoleSet.contains(type)) {
            controlData = new ControlWhackMoleData(json);
        } else if (bossSet.contains(type)) {
            controlData = new ControlActivityBossData(json);
        } else if (weekCardSet.contains(type)) {
            controlData = new ControlWeekCardData(json);
        } else if (luckyBagSet.contains(type)) {
            controlData = new ControlLuckyBagData(json);
        } else if (waterTreeSet.contains(type)) {
            controlData = new ControlWaterTreeData(json);
        } else if (dayPaySet.contains(type)) {
            controlData = new ControlDayPayData(json);
        } else if (legionInvasionSet.contains(type)) {
            controlData = new ControlLegionInvasionData(json);
        } else if (monopolySet.contains(type)) {
            controlData = new ControlMonopolyData(json);
        } else if (slimeMixSet.contains(type)) {
            controlData = new ControlSlimeMixData(json);
        } else if (shareGameSet.contains(type)) {
            controlData = new ControlShareGameData(json);
        } else if (slimeSet.contains(type)) {
            controlData = new ControlSlimeData(json);
        } else if (refreshGiftSet.contains(type)) {
            controlData = new ControlRefreshGiftData(json);
        } else if (goldenTowerSet.contains(type)) {
            controlData = new ControlGoldenTowerData(json);
        } else if (castleSet.contains(type)) {
            controlData = new ControlCastleData(json);
        } else if (dungeonSet.contains(type)) {
            controlData = new ControlDungeonData(json);
        } else if (campSet.contains(type)) {
            controlData = new ControlCampData(json);
        } else if(dungeonCraftSet.contains(type)){
            controlData = new ControlDungeonCraftData(json);
        } else if (skinTrySet.contains(type)) {
            controlData = new ControlSkinTryData(json);
        } else if (cardEliminateSet.contains(type)) {
            controlData = new ControlCardEliminateData(json);
        } else if (groupGiftSet.contains(type)) {
            controlData = new ControlGroupGiftData(json);
        } else if (guildPaySet.contains(type)) {
            controlData = new ControlGuildPayData(json);
        } else if(ControlCohesionData.contains(type)){
            controlData = new ControlCohesionData(json);
        } else if (addFriendSet.contains(type)) {
            controlData = new ControlAddFriendData(json);
        } else if (doubleDrawSet.contains(type)) {
            controlData = new ControlDoubleDrawData(json);
        } else if (accumulateScoreSet.contains(type)) {
            controlData = new ControlAccumulateScoreData(json);
        } else if (airdropGiftSet.contains(type)) {
            controlData = new ControlAirdropGiftData(json);
        } else if (blackMarketSet.contains(type)) {
            controlData = new ControlBlackMarketData(json);
        } else if(fruitMergeSet.contains(type)){
            controlData = new ControlFruitMergeData(json);
        } else if (collectTradeSet.contains(type)) {
            controlData = new ControlCollectTradeData(json);
        } else if (wuzhiLoveSet.contains(type)) {
            controlData = new ControlWuzhiLoveData(json);
        } else if (figureCollectionSet.contains(type)) {
            controlData = new ControlFigureCollectionData(json);
        } else if (wuzhiResearchSet.contains(type)) {
            controlData = new ControlWuzhiResearchData(json);
        }
        return controlData;
    }
}
