package org.gof.demo.worldsrv.activity;

import org.gof.demo.battlesrv.support.PropCalc;

import java.util.HashMap;
import java.util.Map;

/**
 * 活动增益效果类
 * 用于存储和管理活动期间的各种增益效果
 */
public class ActivityEffect {
    
    // 体力恢复间隔缩短（分钟数）
    private int staminaRecoverReduce = 0;
    
    // 冒险获得金币数量提升（百分比）
    private int adventureGoldBonus = 0;
    
    // 攻击BOSS道具额外数量
    private int bossItemBonus = 0;
    // 星渊石额外数量
    private int mountChapterStoneBonus = 0;
    // 竞技场入场券上限提升数量
    private int arenaTicketLimitBonus = 0;
    
    // 好感度提升 Map<角色ID, 提升百分比>
    private Map<Integer, Integer> loveBonus = new HashMap<>();
    
    // 副本奖励提升 Map<副本类型, 奖励提升百分比>
    private Map<Integer, Integer> dungeonRewardBonus = new HashMap<>();
    
    // 玩家属性加成
    private PropCalc attrBonus = new PropCalc();
    

    
    public ActivityEffect() {
    }

    /**
     * 应用增益效果
     * @param effectType 增益类型
     * @param subType 增益子类型
     * @param value 参数值
     */
    public void applyEffect(int effectType, int subType, int value) {
        switch (effectType) {
            case EActivityType.EFFECT_STAMINA_RECOVER_REDUCE: // 体力恢复间隔缩短
                staminaRecoverReduce += value;
                break;
            case EActivityType.EFFECT_ADVENTURE_GOLD_BONUS: // 冒险获得金币数量提升
                adventureGoldBonus += value;
                break;
            case EActivityType.EFFECT_BOSS_ITEM_BONUS: // 攻击BOSS道具额外数量
                bossItemBonus += value;
                break;
            case EActivityType.EFFECT_LOVE_BONUS: // 好感度提升
                int currentLoveBonus = loveBonus.getOrDefault(subType, 0);
                loveBonus.put(subType, currentLoveBonus + value);
                break;
            case EActivityType.EFFECT_DUNGEON_REWARD_BONUS: // 副本奖励提升
                int currentDungeonBonus = dungeonRewardBonus.getOrDefault(subType, 0);
                dungeonRewardBonus.put(subType, currentDungeonBonus + value);
                break;
            case EActivityType.EFFECT_MOUNT_CHAPTER_STONE_BONUS: // 星渊石额外数量
                mountChapterStoneBonus += value;
                break;
            case EActivityType.EFFECT_PLAYER_ATTR_BONUS: // 玩家属性加成
                attrBonus.plus(Integer.toString(subType), value);
                break;
            case EActivityType.EFFECT_ARENA_TICKET_LIMIT_BONUS: // 竞技场入场券上限提升
                arenaTicketLimitBonus += value;
                break;
        }
    }
    
    /**
     * 清除所有增益效果
     */
    public void clearAllEffects() {
        staminaRecoverReduce = 0;
        adventureGoldBonus = 0;
        bossItemBonus = 0;
        mountChapterStoneBonus = 0;
        arenaTicketLimitBonus = 0;
        loveBonus.clear();
        dungeonRewardBonus.clear();
        attrBonus.removeAll();
    }
    
    /**
     * 获取属性加成的PropCalc对象
     */
    public PropCalc getPlayerAttrPropCalc() {
        return attrBonus;
    }
    
    // Getters
    public int getStaminaRecoverReduce() {
        return staminaRecoverReduce;
    }
    
    public int getAdventureGoldBonus() {
        return adventureGoldBonus;
    }
    
    public int getBossItemBonus() {
        return bossItemBonus;
    }
    
    public int getLoveBonus(int characterId) {
        return loveBonus.getOrDefault(characterId, 0);
    }
    
    public int getDungeonRewardBonus(int dungeonType) {
        return dungeonRewardBonus.getOrDefault(dungeonType, 0);
    }
    
    public int getMountChapterStoneBonus() {
        return mountChapterStoneBonus;
    }
    
    public int getArenaTicketLimitBonus() {
        return arenaTicketLimitBonus;
    }
    
    public Map<Integer, Integer> getLoveBonusMap() {
        return new HashMap<>(loveBonus);
    }
    
    public Map<Integer, Integer> getDungeonRewardBonusMap() {
        return new HashMap<>(dungeonRewardBonus);
    }

}
