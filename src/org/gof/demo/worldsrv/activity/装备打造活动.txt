添加活动：获得类型4775 活动名DungeonCraft controlData要记录不同craft_type对应的打造次数,打造出大奖就置为0,
协议://神将打造信息
   message act_dungeon_craft_info_c2s {
       option (msgid) = 6553;
       uint32 act_type = 1;
   }

   message act_dungeon_craft_info_s2c {
       option (msgid) = 6554;
       uint32 act_type = 1;
       uint32 total_num = 2;//打造总次数
   }

   message act_dungeon_craft_c2s {
       option (msgid) = 6555;
       uint32 act_type = 1;
       uint32 craft_type = 2;
       uint32 num = 3;//打造次数
   }

   message act_dungeon_craft_s2c {
       option (msgid) = 6556;
       uint32 act_type = 1;
       uint32 craft_type = 2;
       uint32 num = 3;//打造次数
       uint32 total_num = 4;//打造总次数
       repeated p_reward big_rewards = 5;//大奖奖励
   }
   这个活动要用的之前的装备打造功能.在配表ConfEquipment加了字段 int act_id:装备的活动表id和int[][] sellRewardActivity:活动期间出售获得.
   需要在GlobalConfVal.java中把原来的装备索引排除掉填了活动id的.同时建立不同活动id的装备索引.在处理act_dungeon_craft_c2s的时候用这个活动的装备索引.
   同时在售卖装备的时候要判断是否试活动装备,如果是要判断活动开启了就按照sellRewardActivity来售卖获得.
   act_dungeon_craft_c2s装备打造的时候num只有1次和10连.打造读取ConfDungeonCraft.success_weight成功的概率万分比值, ConfDungeonCraft.fix_type合成类型对应craft_type
   ConfDungeonCraft.item_cost当次消耗的道具ConfDungeonCraft.fail_reward_type失败关联的装备打造类型ConfDungeonCraftPro.type,ConfDungeonCraftPro这个表是打造概率表,
   ConfDungeonCraftPro.get(打造类型,装备宝箱等级)获取装备品质概率（十万分比）,这个概率和ConfTreasureLevel.pro_quality是一样的,这个活动的品质概率读这里