package org.gof.demo.worldsrv.activity.calculator;

import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.activity.ActivityManager;
import org.gof.demo.worldsrv.activity.data.ActivityControlObjectData;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.ConfActivityTerm;
import org.gof.demo.worldsrv.mail.MailManager;

import java.util.Map;

/**
 * sns分享活动只在活动开启后发一封邮件
 */
public class ActivityControlSnsShareMail extends ActivityControlGeneral {

    public ActivityControlSnsShareMail(int type) {
        super(type);
    }

    @Override
    public void sendActivityData(HumanObject humanObj) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type, data.getActControlData().getRound());
        if (confTerm == null) {
            return;
        }
        Map<Integer, Integer> snsRewardStateMap = Utils.jsonToMapIntInt(humanObj.getHumanExtInfo().getSnsRewardStateJSON());
        Integer rewardState = snsRewardStateMap.getOrDefault(type, 0);
        if (rewardState != 0) {
            return;
        }
        snsRewardStateMap.put(type, 1);
        humanObj.getHumanExtInfo().setSnsRewardStateJSON(Utils.mapIntIntToJSON(snsRewardStateMap));
//        humanObj.getHumanExtInfo().update();
        MailManager.inst().sendMail(humanObj.id, MailManager.SYS_SENDER, confTerm.parameter, "", "", null, null);
    }
}
