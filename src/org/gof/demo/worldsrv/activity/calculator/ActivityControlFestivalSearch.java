package org.gof.demo.worldsrv.activity.calculator;


import org.gof.core.Port;
import org.gof.core.support.Time;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.activity.ActivityManager;
import org.gof.demo.worldsrv.activity.data.ActivityControlObjectData;
import org.gof.demo.worldsrv.activity.data.controldata.ControlFestivalSearchData;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.*;
import org.gof.demo.worldsrv.instance.InstanceConstants;
import org.gof.demo.worldsrv.instance.InstanceManager;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgAct;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;


public class ActivityControlFestivalSearch extends AbstractActivityControl{
    private static ActivityControlFestivalSearch instance = new ActivityControlFestivalSearch(0);

    public static ActivityControlFestivalSearch getInstance(int type) {
        instance.type = type;
        return instance;
    }

    public ActivityControlFestivalSearch(int type) {
        super(type);
    }

    private static final int STATE_INIT = 0;
    private static final int STATE_FINISH = 1;
    private static final int STATE_REWARD = 2;

    public Define.p_act.Builder toActivityData(HumanObject humanObj, int activitySn) {
        Define.p_act.Builder p_act = super.toActivityData(humanObj);
        return p_act;
    }

    public void on_act_autumn_pig_find_c2s(HumanObject humanObj, int pigId) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            return;
        }
        if(pigId == 0){
            on_perfect_reward_c2s(humanObj);
            return;
        }
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type,data.getActControlData().getRound());
        if(confTerm == null){
            return;
        }

        int rankNum = 1;
        ConfMoonfestivalSearch conf = ConfMoonfestivalSearch.get(type, confTerm.group_id, rankNum);
        while (conf != null) {
            if (conf.id == pigId) {
                break;
            }
            rankNum++;
            conf = ConfMoonfestivalSearch.get(type, confTerm.group_id, rankNum);
        }
        if (conf == null || conf.type != type) {
            return;
        }

        ControlFestivalSearchData controlData = (ControlFestivalSearchData) data.getControlData();
        if(controlData.getPigState(pigId) == 2){
            return;
        }
        if(controlData.getDay() + 1 != conf.open_time){
            return;
        }
        int openDays = Utils.getDaysBetween(data.getActControlData().getOpenTime()* Time.SEC, Port.getTime())+1;
        if (conf.open_time > openDays) {
            return;
        }
        controlData.setPigState(pigId, 2);
        controlData.setDay(conf.open_time);
        data.updateControlData();
        ProduceManager.inst().produceAdd(humanObj, conf.clue_reward, MoneyItemLogKey.中秋搜寻);
        InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, InstanceConstants.showType_0, conf.clue_reward);

        MsgAct.act_autumn_pig_find_s2c.Builder msg = MsgAct.act_autumn_pig_find_s2c.newBuilder();
        msg.setActType(type);
        msg.setPigId(pigId);
        humanObj.sendMsg(msg);
    }

    //完美奖励领取
    public void on_perfect_reward_c2s(HumanObject humanObj) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            return;
        }
        ControlFestivalSearchData controlData = (ControlFestivalSearchData) data.getControlData();
        if (controlData.getIsReward() == 1) {
            return;
        }
        ConfActivityTerm confActivityTerm = ActivityManager.inst().getActivityTerm(type,data.getActControlData().getRound());
        if(confActivityTerm == null){
            return;
        }
        int rankNum = 1;
        ConfMoonfestivalSearch confSearch = ConfMoonfestivalSearch.get(type,confActivityTerm.group_id,rankNum);
        while (confSearch != null){
            if(controlData.getPigState(confSearch.id) != 2){
                return;
            }
            rankNum++;
            confSearch = ConfMoonfestivalSearch.get(type,confActivityTerm.group_id,rankNum);
        }
        controlData.setIsReward(1);
        data.updateControlData();

        int[][] reward = confActivityTerm.reward;
        if(reward == null || reward.length < 1){
            Log.game.error("ConfActivitycost.get().reward is null type={},trem={}",type,data.getActControlData().getRound());
            return;
        }
        ProduceManager.inst().produceAdd(humanObj, reward, MoneyItemLogKey.中秋搜寻);
        InstanceManager.inst().sendMsg_goods_show_s2c(humanObj,InstanceConstants.showType_0,reward);

        MsgAct.act_autumn_pig_find_s2c.Builder msg = MsgAct.act_autumn_pig_find_s2c.newBuilder();
        msg.setActType(type);
        msg.setPigId(0);
        humanObj.sendMsg(msg);
    }

    public void sendActivityData(HumanObject humanObj){
        super.sendActivityData(humanObj);
        on_act_autumn_pig_info_c2s(humanObj);
    }

    public void on_act_autumn_pig_info_c2s(HumanObject humanObj) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            return;
        }
        ControlFestivalSearchData controlData = (ControlFestivalSearchData) data.getControlData();
        MsgAct.act_autumn_pig_info_s2c.Builder msg = MsgAct.act_autumn_pig_info_s2c.newBuilder();
        msg.setActType(type);
        msg.setTotalRewardType(controlData.getIsReward()==1?1:0);
        ConfActivityControl conf = ConfActivityControl.get(data.getActControlData().getActivitySn());
        if (conf == null) {
            Log.game.error("ConfActivityControl not found, sn={}", data.getActControlData().getActivitySn());
            return;
        }
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type,data.getActControlData().getRound());
        if(confTerm == null){
            return;
        }
        int rankNum = 1;
        ConfMoonfestivalSearch confSearch = ConfMoonfestivalSearch.get(type, confTerm.group_id, rankNum);
        int dayUse = controlData.getDay() + 1;
        int openDay = Utils.getDaysBetween(data.getActControlData().getOpenTime() * Time.SEC, Port.getTime()) + 1;
        while (confSearch != null) {
            Define.p_autumn_pig.Builder pig = Define.p_autumn_pig.newBuilder();
            pig.setId(confSearch.id);
            int state = controlData.getPigState(confSearch.id);
            if (state != 2) {
                pig.setState((confSearch.open_time <= dayUse && confSearch.open_time <= openDay) ? 1 : 0);
            } else {
                pig.setState(state);
            }
            msg.addPigInfo(pig);
            rankNum++;
            confSearch = ConfMoonfestivalSearch.get(type, confTerm.group_id, rankNum);
        }
        humanObj.sendMsg(msg);
    }
}
