package org.gof.demo.worldsrv.activity.calculator;


import org.gof.core.Port;
import org.gof.core.dbsrv.redis.EntityManager;
import org.gof.core.dbsrv.redis.RedisTools;
import org.gof.core.support.Time;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.activity.ActivityManager;
import org.gof.demo.worldsrv.activity.ActivityVo;
import org.gof.demo.worldsrv.activity.data.ActivityControlObjectData;
import org.gof.demo.worldsrv.activity.data.controldata.ControlPVPClueData;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.*;
import org.gof.demo.worldsrv.entity.ActControlData;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.human.HumanData;
import org.gof.demo.worldsrv.human.HumanGlobalServiceProxy;
import org.gof.demo.worldsrv.inform.ErrorTip;
import org.gof.demo.worldsrv.inform.Inform;
import org.gof.demo.worldsrv.instance.InstanceManager;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgAct;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.rank.RankManager;
import org.gof.demo.worldsrv.rank.RankParamKey;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.ReasonResult;
import org.gof.demo.worldsrv.support.enumKey.ConfGlobalKey;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;
import org.gof.demo.worldsrv.team.TeamMember;

import java.util.*;

/**
 * 圣诞献礼，pvp给加成
 */
public class ActivityControlPVPClue extends AbstractActivityControl {
    public ActivityControlPVPClue(int type) {
        super(type);
    }

    @Override
    protected void initActivityControlData(HumanObject humanObj, ActivityControlObjectData data, ConfActivityControl confControl, ActivityVo vo) {
        super.initActivityControlData(humanObj, data, confControl, vo);
        if (humanObj.isModUnlock(confControl.newfunctionID)) {
            dailyResetActivityData(humanObj);
        }
    }

    @Override
    public void unlockActivity(HumanObject humanObj) {
        super.unlockActivity(humanObj);
        dailyResetActivityData(humanObj);
    }

    /**
     * 重置每日的加成并且重新筛选对手
     */
    @Override
    public void dailyResetActivityData(HumanObject humanObj) {
        super.dailyResetActivityData(humanObj);
        ActivityControlObjectData controlData = humanObj.operation.activityControlData.getControlObjectData(type);
        if (controlData == null) {
            return;
        }
        // 重置数据
        ControlPVPClueData data = (ControlPVPClueData) controlData.getControlData();
        data.addSkillSnList.clear();
        data.chooseSkillSnList.clear();
        data.skipCount = 0;
        data.passCount = 0;
        data.enemyIdList.clear();
        data.clueSn = 0;
        data.buyNum = 0;
        data.failNum = 0;
        controlData.updateControlData();
        // 随机敌人（先取到自己的排行榜，然后再根据公式运算）
        String redisKey = RankManager.inst().getRedisRankTypeKey(RankParamKey.rankTypeRep_1003, humanObj.getHuman().getServerId());
        RedisTools.getMyRank(EntityManager.redisClient, redisKey, String.valueOf(humanObj.id), handler -> {
            if (handler.failed()) {
                Log.activity.error("圣诞献礼重置时查询玩家的关卡排名出错, humanId={}, e={}", humanObj.id, handler.cause().getMessage(), handler.cause());
                return;
            }
            long rank = handler.result();
            List<Integer> confSnList = getPvpClueSnList(controlData);
            if (confSnList == null || confSnList.size() == 0) {
                Log.activity.error("圣诞献礼重置时取不到今日可挑战关卡, humanId={}", humanObj.id);
                return;
            }
            // 计算所有关卡敌人强度
            int min = Integer.MAX_VALUE;
            int max = 0;
            List<int[]> chooseRangeList = new ArrayList<>();
            for (Integer sn : confSnList) {
                ConfChristmasPVPclue conf = ConfChristmasPVPclue.get(sn);
                if (conf.is_battle == 0) {
                    continue;
                }
                int currMinRank = Math.round(rank * conf.enemylevel[2] * 1.0f / 100);
                int currMaxRank = Math.round(rank * conf.enemylevel[1] * 1.0f / 100 + conf.enemylevel[0]);
                min = Math.min(min, currMinRank);
                max = Math.max(max, currMaxRank);
                chooseRangeList.add(new int[]{
                        currMinRank,
                        currMaxRank
                });
            }
            final int finalMin = min;
            final int finalMax = max;
            RedisTools.getRankListByIndex(EntityManager.redisClient, redisKey, finalMin, finalMax, false, handler1 -> {
                if (handler1.failed()) {
                    Log.activity.error("圣诞献礼重置时查询范围关卡排名出错, humanId={}, min={}, max={}, e={}", humanObj.id, finalMin, finalMax, handler.cause().getMessage(), handler.cause());
                    return;
                }
                List<Long> allHumanIdList = new ArrayList<>(finalMax - finalMin);
                for (Object obj : handler1.result()) {
                    long humanId = Utils.longValue(obj);
                    if (humanId != humanObj.id) {
                        // 过滤玩家本身
                        allHumanIdList.add(humanId);
                    }
                }
                List<Long> rangeHumanIdList = new ArrayList<>(allHumanIdList.size());
                for (int i = 0; i < chooseRangeList.size(); i++) {
                    int[] ranges = chooseRangeList.get(i);
                    int startIndex = ranges[0] - finalMin;
                    int finalIndex = ranges[1] - finalMin;
                    for (; startIndex <= finalIndex && startIndex < allHumanIdList.size(); startIndex++) {
                        Long humanId = allHumanIdList.get(startIndex);
                        if (data.enemyIdList.contains(humanId)) {
                            continue;
                        }
                        rangeHumanIdList.add(humanId);
                    }
                    if (rangeHumanIdList.isEmpty()) {
                        if (i == 0) {
                            Log.activity.error("圣诞献礼随机敌人可选敌人为空！填充自己做容错, humanId={}, rangesMin={}, rangesMax={}, finalMin={}, finalMax={}, allHumanIdSize={}",
                                    humanObj.id, ranges[0], ranges[1], finalMin, finalMax, allHumanIdList.size());
                            // 第一个敌人要塞两遍
                            data.enemyIdList.add(humanObj.id);
                            data.enemyIdList.add(humanObj.id);
                        } else {
                            Log.activity.error("圣诞献礼随机敌人可选敌人为空！填充已有敌人做容错, humanId={}, rangesMin={}, rangesMax={}, finalMin={}, finalMax={}, allHumanIdSize={}",
                                    humanObj.id, ranges[0], ranges[1], finalMin, finalMax, allHumanIdList.size());
                            int index = data.enemyIdList.size() > 1 ? data.enemyIdList.size() - 2 : 0;
                            data.enemyIdList.add(data.enemyIdList.get(index));
                        }
                    } else {
                        Collections.shuffle(rangeHumanIdList);// 直接打乱取第一个
                        long humanId = rangeHumanIdList.get(0);
                        if (i == 0) {
                            data.enemyIdList.add(humanId);// 第一个敌人要塞两遍
                        }
                        data.enemyIdList.add(humanId);
                        rangeHumanIdList.clear();
                    }
                }
                controlData.updateControlData();
            });
        });
    }

    /**
     * 根据活动类型、活动期数、活动天数返回pvp配表sn
     */
    public List<Integer> getPvpClueSnList(ActivityControlObjectData controlData) {
        Map<Integer, Map<Integer, List<Integer>>> roundDayMap = GlobalConfVal.pvpClueMap.get(type);
        if (roundDayMap == null) {
            return null;
        }
        ActControlData actData = controlData.getActControlData();
        int round = actData.getRound();
        ConfActivityTerm conf = ActivityManager.inst().getActivityTerm(type, round);
        if (conf == null) {
            Log.activity.error("根据活动类型和范围找不到对应的活动期数表, type={}, round={}", type, round);
            return null;
        }
        Map<Integer, List<Integer>> dayMap = roundDayMap.get(conf.group_id);
        if (dayMap == null) {
            return null;
        }

        int day = Utils.getDaysBetween(actData.getOpenTime() * Time.SEC, Port.getTime()) + 1;
        return dayMap.get(day);
    }

    /**
     * 随机通关模式（直接通关或者战斗）
     */
    public int[][] randomClueMode(HumanObject humanObj) {
        ActivityControlObjectData controlData = humanObj.operation.activityControlData.getControlObjectData(type);
        if (controlData == null) {
            return null;
        }
        // 根据活动类型、活动期数和天数取出相关配表
        List<Integer> confSnList = getPvpClueSnList(controlData);
        if (confSnList == null || confSnList.size() == 0) {
            Log.activity.error("圣诞献礼活动超出天数, humanId={}", humanObj.id);
            return null;
        }
        // 根据活动数据种的今日战斗次数和跳过战斗次数随机通关方式（是直接通关还是战斗通关）
        ControlPVPClueData data = (ControlPVPClueData) controlData.getControlData();
        int passCount = data.passCount;
        if (passCount >= confSnList.size()) {
            Log.activity.error("今日可挑战次数已满");
            return null;
        }
        int[][] results = null;
        int clueSn = confSnList.get(passCount);
        if (passCount == 0) {
            // 今日首次是直接给
            data.passCount++;// 通关次数要加1
            data.clueSn = confSnList.get(0);
            results = giveClueReward(humanObj);
        } else if (data.skipCount == 0) {
            // 每关第一场都是免费
            data.skipCount++;// 通关次数要加1
            data.clueSn = confSnList.get(0);
            results = giveClueReward(humanObj);
        } else {
            // 今日非首次，先是判断是不是保底进入战斗，不是再去随机
            int skipCount = data.skipCount;
            ConfChristmasPVPclue conf = ConfChristmasPVPclue.get(clueSn);
            if (conf.is_battle == 1 && skipCount >= conf.limit) {
                data.clueSn = clueSn;// 随机满次数一定战斗
            } else {
                int[] sns = new int[]{
                        confSnList.get(0),
                        clueSn
                };
                int[] weights = new int[]{
                        ConfChristmasPVPclue.get(confSnList.get(0)).weight,
                        ConfChristmasPVPclue.get(clueSn).weight,
                };
                int index = Utils.randomByWeight(weights);
                clueSn = sns[index];
                data.clueSn = clueSn;
                conf = ConfChristmasPVPclue.get(clueSn);
                if (conf.is_battle == 0) {
                    data.skipCount++;// 加跳过次数，不加通关次数
                    results = giveClueReward(humanObj);
                }
            }
        }
        controlData.updateControlData();
        return results;
    }

    /**
     * 发奖励
     */
    public int[][] giveClueReward(HumanObject humanObj) {
        ActivityControlObjectData controlData = humanObj.operation.activityControlData.getControlObjectData(type);
        if (controlData == null) {
            return null;
        }
        ControlPVPClueData data = (ControlPVPClueData) controlData.getControlData();
        ConfChristmasPVPclue conf = ConfChristmasPVPclue.get(data.clueSn);
        ProduceManager.inst().produceAdd(humanObj, conf.reward, MoneyItemLogKey.圣诞献礼);
        data.clueSn = 0;
        return conf.reward;
    }

    /**
     * 抽取随机技能
     */
    public void randomSkillToChoose(HumanObject humanObj) {
        ActivityControlObjectData controlData = humanObj.operation.activityControlData.getControlObjectData(type);
        if (controlData == null) {
            return;
        }
        ControlPVPClueData data = (ControlPVPClueData) controlData.getControlData();
        int round = controlData.getActControlData().getRound();
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type, round);
        if (confTerm == null) {
            Log.activity.error("根据活动类型和范围找不到对应的活动期数表, type={}, round={}", type, round);
            return;
        }

        // 筛选出符合条件的技能id和权重
        List<Integer> skillSnList = new ArrayList<>();
        List<Integer> skillWeightList = new ArrayList<>();
        Map<Integer, List<Integer>> roundMap = GlobalConfVal.pvpClueBuffMap.getOrDefault(type, new HashMap<>(0));
        List<Integer> snList = roundMap.getOrDefault(confTerm.group_id, new ArrayList<>(0));
        for (Integer sn : snList) {
            ConfChristmasPvpBuffList conf = ConfChristmasPvpBuffList.get(sn);
            if (data.addSkillSnList.contains(conf.skill_id)) {
                continue;
            }
            skillSnList.add(conf.skill_id);
            skillWeightList.add(conf.weight);
        }

        if (skillSnList.isEmpty()) {
            Log.activity.error("圣诞献礼可选加成为空!");
            return;
        }

        data.chooseSkillSnList.clear();
        // 随机3个技能出来
        int num = 3;
        for (int i = 0; i < num; i++) {
            int index = Utils.randomByWeight(skillWeightList);
            int skillSn = skillSnList.remove(index);
            skillWeightList.remove(index);
            data.chooseSkillSnList.add(skillSn);
        }
    }

    /**
     * 发送加成、待选加成、加成购买次数更新
     */
    public void sendUpdateBuffMsg(HumanObject humanObj) {
        ActivityControlObjectData controlData = humanObj.operation.activityControlData.getControlObjectData(type);
        if (controlData == null) {
            return;
        }
        ControlPVPClueData data = (ControlPVPClueData) controlData.getControlData();
        MsgAct.act_halloween_arena_buff_update_s2c.Builder msg = MsgAct.act_halloween_arena_buff_update_s2c.newBuilder();
        msg.setActType(type);
        msg.addAllBuffList(data.addSkillSnList);
        msg.addAllChooseBuff(data.chooseSkillSnList);
        msg.setBuyNum(data.buyNum);
        humanObj.sendMsg(msg);
    }

    /**
     * info消息
     */
    public void on_halloween_arena_info_c2s(HumanObject humanObj) {
        ActivityControlObjectData controlData = humanObj.operation.activityControlData.getControlObjectData(type);
        if (controlData == null) {
            return;
        }
        // 获取今日可以参与的战斗sn
        List<Integer> clueSnList = getPvpClueSnList(controlData);

        ControlPVPClueData data = (ControlPVPClueData) controlData.getControlData();

        // 构造消息
        MsgAct.act_halloween_arena_info_s2c.Builder msg = MsgAct.act_halloween_arena_info_s2c.newBuilder();
        msg.setActType(type);
        msg.setChapter(clueSnList != null && data.passCount >= clueSnList.size() ? 1 : 0);
        msg.setCanCandy(data.clueSn != 0 ? 0 : 1);
        msg.addAllBuffList(data.addSkillSnList);
        msg.addAllChooseBuff(data.chooseSkillSnList);
        msg.setBuyNum(data.buyNum);
        msg.setFailNum(data.failNum);
        if (clueSnList != null && data.passCount >= clueSnList.size()) {
            humanObj.sendMsg(msg);
            return;
        }
        long enemyId = data.passCount >= data.enemyIdList.size() ? 0 : data.enemyIdList.get(data.passCount);
        if (enemyId == 0L) {
            Log.activity.error("圣诞献礼获取今日挑战玩家id为0, humanId={}, passCount={}, enemyIdList={}", humanObj.id, data.passCount, Utils.listToString(data.enemyIdList));
            Inform.sendMsg_error(humanObj, ErrorTip.SystemDefault);
            return;
        }
        // 查询对手的信息
        HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance();
        prx.getPBattleRoleMsg(enemyId, false);
        prx.listenResult((result, context) -> {
            Define.p_battle_role pBattleRole = result.get("p_battle_role");
            msg.setDefData(pBattleRole);
            humanObj.sendMsg(msg);
        });
    }

    /**
     * 索要糖果，就是直接通关或者准备进行战斗
     */
    public void on_act_halloween_arena_trick_c2s(HumanObject humanObj) {
        int[][] rewards = randomClueMode(humanObj);
        ActivityControlObjectData controlData = humanObj.operation.activityControlData.getControlObjectData(type);
        if (controlData == null) {
            return;
        }
        ControlPVPClueData data = (ControlPVPClueData) controlData.getControlData();
        MsgAct.act_halloween_arena_trick_s2c.Builder msg = MsgAct.act_halloween_arena_trick_s2c.newBuilder();
        msg.setActType(type);
        msg.setCanCandy(data.clueSn == 0 ? 1 : 0);
        msg.addAllReward(InstanceManager.inst().to_p_rewardList(rewards));
        humanObj.sendMsg(msg);
    }

    /**
     * 返回战斗数据
     */
    public void on_act_halloween_arena_combat_c2s(HumanObject humanObj) {
        ActivityControlObjectData controlData = humanObj.operation.activityControlData.getControlObjectData(type);
        if (controlData == null) {
            return;
        }
        ControlPVPClueData data = (ControlPVPClueData) controlData.getControlData();
        long enemyId = data.passCount >= data.enemyIdList.size() ? 0 : data.enemyIdList.get(data.passCount);
        if (enemyId == 0L) {
            Log.activity.error("圣诞献礼战斗时找不到敌人id, humanId={}, passCount={}, enemyIdList={}", humanObj.id, data.passCount, Utils.listToString(data.enemyIdList));
            Inform.sendMsg_error(humanObj, ErrorTip.SystemDefault);
            return;
        }
        List<Integer> clueSnList = getPvpClueSnList(controlData);

        // 构造玩家战斗单位，添加加成
        HumanData humanData = HumanData.getHumanData(humanObj);
        TeamMember member = new TeamMember(humanData);
        Define.p_battle_role.Builder pAtkBattleRole = member.p_battle.toBuilder();
        Define.p_role_skill.Builder pRoleSkill = pAtkBattleRole.getRoleSkill().toBuilder();
        for (Integer skillSn : data.addSkillSnList) {
            Define.p_passive_skill.Builder skillMsg = Define.p_passive_skill.newBuilder();
            skillMsg.setSkillId(skillSn);
            skillMsg.setSkillLv(1);
            pRoleSkill.addPassiveSkill(skillMsg);
        }
        pAtkBattleRole.setRoleSkill(pRoleSkill);

        // 构造消息
        MsgAct.act_halloween_arena_combat_s2c.Builder msg = MsgAct.act_halloween_arena_combat_s2c.newBuilder();
        msg.setActType(type);
        msg.setChapter(clueSnList != null && data.passCount >= clueSnList.size() ? 1 : 0);
        msg.setSeed(Utils.getTimeSec());
        msg.setAtkBattleRole(pAtkBattleRole);

        // 查询对手的信息
        HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance();
        prx.getPBattleRoleMsg(enemyId, true);
        prx.listenResult((result, context) -> {
            Define.p_battle_role pBattleRole = result.get("p_battle_role");
            msg.setDefBattleRole(pBattleRole);
            humanObj.sendMsg(msg);
        });
    }

    /**
     * 战斗通关
     */
    public void on_act_halloween_arena_result_c2s(HumanObject humanObj, int result) {
        ActivityControlObjectData controlData = humanObj.operation.activityControlData.getControlObjectData(type);
        if (controlData == null) {
            return;
        }
        ControlPVPClueData data = (ControlPVPClueData) controlData.getControlData();
        if (result == 0) {
            data.failNum++;
            controlData.updateControlData();
            return;
        }
        data.passCount++;
        data.skipCount = 0;
        data.failNum = 0;
        // 发奖励
        int[][] rewards = giveClueReward(humanObj);
        // 随机技能进待选列表
        List<Integer> clueSnList = getPvpClueSnList(controlData);
        if (clueSnList != null && data.passCount < clueSnList.size()) {
            randomSkillToChoose(humanObj);
        }
        controlData.updateControlData();
        // 发消息
        MsgAct.act_halloween_arena_result_s2c.Builder msg = MsgAct.act_halloween_arena_result_s2c.newBuilder();
        msg.setActType(type);
        msg.setChapter(clueSnList != null && data.passCount >= clueSnList.size() ? 1 : 0);
        msg.setResult(result);
        msg.addAllChooseBuff(data.chooseSkillSnList);
        msg.addAllReward(InstanceManager.inst().to_p_rewardList(rewards));
        humanObj.sendMsg(msg);
        // 发消息刷敌人
        on_halloween_arena_info_c2s(humanObj);
    }

    /**
     * 消耗货币随机给待选加成
     */
    public void on_act_halloween_arena_buy_buff_c2s(HumanObject humanObj) {
        ActivityControlObjectData controlData = humanObj.operation.activityControlData.getControlObjectData(type);
        if (controlData == null) {
            return;
        }
        ControlPVPClueData data = (ControlPVPClueData) controlData.getControlData();
        if (!data.chooseSkillSnList.isEmpty()) {
            Log.activity.error("圣诞献礼购买buff失败, 需要先选择其中一个！humanId={}, name={}", humanObj.id, humanObj.name);
            return;
        }
        int buyNum = data.buyNum;
        ConfGlobal conf = ConfGlobal.get(ConfGlobalKey.ChristmasPvp_Getbuff_cost);
        int[][] costs = Utils.parseIntArray2(conf.strValue);
        if (buyNum >= costs.length) {
            Inform.sendMsg_error(humanObj, ErrorTip.CountNotEnough);
            return;
        }
        if (costs[buyNum][1] > 0) {
            ReasonResult rr = ProduceManager.inst().checkAndCostItem(humanObj, costs[buyNum][0], costs[buyNum][1], MoneyItemLogKey.圣诞献礼);
            if (!rr.success) {
                Inform.sendMsg_error(humanObj, ErrorTip.DiamondNotEnough);
                return;
            }
        }
        data.buyNum++;
        randomSkillToChoose(humanObj);
        controlData.updateControlData();
        sendUpdateBuffMsg(humanObj);
    }

    /**
     * 选择加成
     */
    public void on_act_halloween_arena_choose_buff_c2s(HumanObject humanObj, int skillSn) {
        ActivityControlObjectData controlData = humanObj.operation.activityControlData.getControlObjectData(type);
        if (controlData == null) {
            return;
        }
        ControlPVPClueData data = (ControlPVPClueData) controlData.getControlData();
        if (!data.chooseSkillSnList.contains(skillSn)) {
            Log.activity.error("圣诞献礼选择到不存在的加成, humanId={}, chooseBuff={}, skillSn={}", humanObj.id, Utils.listToString(data.chooseSkillSnList), skillSn);
            Inform.sendMsg_error(humanObj, ErrorTip.SystemDefault);
            return;
        }
        data.addSkillSnList.add(skillSn);
        data.chooseSkillSnList.clear();
        controlData.updateControlData();
        sendUpdateBuffMsg(humanObj);
    }

    public void on_act_halloween_arena_buy_pass_c2s(HumanObject humanObj) {
        ActivityControlObjectData controlData = humanObj.operation.activityControlData.getControlObjectData(type);
        if (controlData == null) {
            return;
        }
        ControlPVPClueData data = (ControlPVPClueData) controlData.getControlData();
        ConfGlobal conf = ConfGlobal.get(ConfGlobalKey.ChristmasPvp_battlefail_skip);
        int[][] costs = Utils.parseIntArray2(conf.strValue);
        if (data.failNum < conf.value) {
            // 战败次数不足
            Inform.sendMsg_error(humanObj, ErrorTip.CountNotEnough);
            return;
        }
        ReasonResult rr = ProduceManager.inst().checkAndCostItem(humanObj, costs, MoneyItemLogKey.圣诞献礼);
        if (!rr.success) {
            Inform.sendMsg_error(humanObj, ErrorTip.DiamondNotEnough);
            return;
        }
        ConfChristmasPVPclue confClue = ConfChristmasPVPclue.get(data.clueSn);
        if (confClue.is_battle == 0) {
            return;
        }
        on_act_halloween_arena_result_c2s(humanObj, 1);
        MsgAct.act_halloween_arena_buy_pass_s2c.Builder msg = MsgAct.act_halloween_arena_buy_pass_s2c.newBuilder();
        humanObj.sendMsg(msg);
    }
}
