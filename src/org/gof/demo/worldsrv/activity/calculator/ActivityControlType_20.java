package org.gof.demo.worldsrv.activity.calculator;

import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.activity.ActivityControlType;
import org.gof.demo.worldsrv.activity.ActivityManager;
import org.gof.demo.worldsrv.activity.data.ActivityControlObjectData;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.ConfActivityControl;
import org.gof.demo.worldsrv.config.ConfActivityTerm;
import org.gof.demo.worldsrv.entity.ActControlData;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgAct;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.task.type.activityData.ActivityTaskVO;

import java.util.Map;


public class ActivityControlType_20 extends AbstractActivityControl{
    private static ActivityControlType_20 instance = new ActivityControlType_20(ActivityControlType.Act_20);

    public static ActivityControlType_20 getInstance(int type) {
        instance.type = type;
        return instance;
    }

    public ActivityControlType_20() {
        super(ActivityControlType.Act_20);
    }

    public ActivityControlType_20(int type) {
        super(type);
    }

    @Override
    public void sendActivityData(HumanObject humanObj) {
        Define.p_act.Builder act = toActivityData(humanObj);
        if (act == null) {
            return;
        }
        if(!humanObj.openActivitySnList.contains(act.getId())){
            return;
        }
        ConfActivityControl conf = ConfActivityControl.get(act.getId());
        if(!humanObj.isModUnlock(conf.newfunctionID)){
            return;
        }
        MsgAct.act_update_s2c.Builder msg =  MsgAct.act_update_s2c.newBuilder();
        msg.setUpdate(act);
        humanObj.sendMsg(msg);
        Log.game.info("玩家{}发送活动数据,{}", humanObj.id, msg);
    }


    @Override
    public Define.p_act.Builder toActivityData(HumanObject humanObj) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            Log.game.error("玩家{}信息失败,{}", humanObj.id, type);
            return null;
        }
        ActControlData actControlData = data.getActControlData();
        int activitySn = actControlData.getActivitySn();
        ConfActivityControl confControl = ConfActivityControl.get(activitySn);
        if(confControl == null) {
            Log.game.error("玩家{}获取失败ConfActivityControl sn={}", humanObj.id, activitySn);
            return null;
        }

        Define.p_act.Builder act = Define.p_act.newBuilder();
        act.setId((int)actControlData.getActivitySn());
        act.setType(type);

        int roundId = 0;
        if(confControl.is_round == 1){
            act.setRound(actControlData.getRound());
            roundId = ActivityManager.inst().getActivityTermSn(confControl.type, actControlData.getRound());
            if(actControlData.getRound() == activitySn){
                Log.temp.error("===活动出错了sn={}, round={}, roundId={}", activitySn, actControlData.getRound(), roundId);
            }
        } else {
            act.setRound(activitySn);
            roundId = ActivityManager.inst().getActivityTermSn(confControl.type, activitySn);
            if(actControlData.getRound() != activitySn){
                Log.temp.error("===活动出错了sn={}, round={}, roundId={}", activitySn, actControlData.getRound(), roundId);
            }
        }
        act.setRoundId(roundId);

        act.setStartTime(actControlData.getOpenTime());
        act.setEndTime(actControlData.getCloseTime());
        act.setState(0);// TODO
        //todo:不知道填什么
        act.addExtra(Define.p_key_value.newBuilder().setK(1).setV(7));

        Map<Integer, ActivityTaskVO> taskVOMap = data.getActivityTask();
        if(taskVOMap == null) {
            return act;
        }
        for (ActivityTaskVO taskVO : taskVOMap.values()) {
            Define.p_act_task.Builder task = Define.p_act_task.newBuilder();
            task.setTaskId(taskVO.getActivitySn());
            task.setGroupId(taskVO.getGroupSn());
            task.setCount(taskVO.getPlan());
            task.setState(taskVO.getStatus());
            act.addTaskList(task);
        }
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(confControl.type, actControlData.getRound());
        if(confTerm == null || confTerm.task_list == null || confTerm.task_list.length == 0) {
            return act;
        }
        Map<Integer, Integer> perfectMap = Utils.jsonToMapIntInt(actControlData.getPerfectMap());
        for (int i = 0; i < confTerm.task_list.length; i++) {
            Define.p_act_task.Builder group = ActivityManager.inst().to_p_act_task_group(confTerm.task_list[i],perfectMap);
            act.addTaskList(group);
        }
        return act;
    }
}
