package org.gof.demo.worldsrv.activity.calculator;

import org.gof.core.Port;
import org.gof.core.support.Time;
import org.gof.demo.support.TimeUtil;
import org.gof.demo.worldsrv.activity.ActivityManager;
import org.gof.demo.worldsrv.activity.data.ActivityControlObjectData;
import org.gof.demo.worldsrv.activity.data.controldata.ControlCollectTradeData;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.ConfActivityTerm;
import org.gof.demo.worldsrv.config.ConfCollectTrade_0;
import org.gof.demo.worldsrv.inform.ErrorTip;
import org.gof.demo.worldsrv.inform.Inform;
import org.gof.demo.worldsrv.instance.InstanceConstants;
import org.gof.demo.worldsrv.instance.InstanceManager;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgAct;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.ReasonResult;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;

import java.util.List;
import java.util.Map;

public class ActivityControlCollectTrade extends AbstractActivityControl {
    public ActivityControlCollectTrade(int type) {
        super(type);
    }

    @Override
    public void on_act_info_c2s(HumanObject humanObj) {
        ActivityControlObjectData controlData = humanObj.operation.activityControlData.getControlObjectData(type);
        if (controlData == null)
            return;
        ControlCollectTradeData fontData = (ControlCollectTradeData) controlData.getControlData();
        MsgAct.act_collect_trade_info_s2c.Builder msg = MsgAct.act_collect_trade_info_s2c.newBuilder();
        msg.setActType(type);
        for (Map.Entry<Integer, Integer> entry : fontData.collectMap.entrySet()) {
            msg.addCollectList(Define.p_key_value.newBuilder().setK(entry.getKey()).setV(entry.getValue()).build());
        }
        humanObj.sendMsg(msg.build());
    }

    public void on_act_collect_exchange_c2s(HumanObject humanObj, int sn, int num) {
        ActivityControlObjectData controlData = humanObj.operation.activityControlData.getControlObjectData(type);
        if (controlData == null)
            return;
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type, controlData.getActControlData().getRound());
        if (confTerm == null) {
            Log.activity.error("[集换字]活动配置不存在, actType={}, round={}", type, controlData.getActControlData().getRound());
            return;
        }
        long openTime = controlData.getActControlData().getOpenTime() * Time.SEC + confTerm.parameter * Time.DAY;
        if (Port.getTime() < openTime) {
            Log.activity.error("[集换字]活动还不可兑换奖励");
            return;
        }
        ConfCollectTrade_0 conf = ConfCollectTrade_0.get(type, confTerm.group_id, sn);
        if (conf == null) {
            Log.activity.error("[集换字]活动配置不存在, actType={}, round={}, sn={}", type, controlData.getActControlData().getRound(), sn);
            return;
        }
        ControlCollectTradeData tradeData = (ControlCollectTradeData) controlData.getControlData();
        int buyNum = tradeData.collectMap.getOrDefault(sn, 0);
        if (conf.limit != 0 && buyNum + num > conf.limit) {
            Inform.sendMsg_error(humanObj, ErrorTip.CountNotEnough);
            return;
        }
        ReasonResult rr = ProduceManager.inst().checkAndCostItem(humanObj, conf.cost, num, MoneyItemLogKey.集换字);
        if (!rr.success) {
            Inform.sendMsg_error(humanObj, ErrorTip.ItemNotEnough);
            return;
        }
        tradeData.collectMap.put(sn, buyNum + num);
        controlData.updateControlData();
        ProduceManager.inst().produceAdd(humanObj, conf.reward, num, MoneyItemLogKey.集换字);
        InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, InstanceConstants.showType_0, conf.reward, num);
        on_act_info_c2s(humanObj);
    }
}
