package org.gof.demo.worldsrv.activity.calculator;

import org.gof.core.Port;
import org.gof.core.support.ParamKey;
import org.gof.core.support.Utils;
import org.gof.demo.battlesrv.manager.PropManager;
import org.gof.demo.battlesrv.support.PropCalc;
import org.gof.demo.battlesrv.support.PropKey;
import org.gof.demo.worldsrv.activity.ActivityControlTypeFactory;
import org.gof.demo.worldsrv.activity.ActivityManager;
import org.gof.demo.worldsrv.activity.ActivityVo;
import org.gof.demo.worldsrv.activity.EActivityType;
import org.gof.demo.worldsrv.activity.data.ActivityControlObjectData;
import org.gof.demo.worldsrv.activity.data.controldata.ControlSlimeData;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.*;
import org.gof.demo.worldsrv.entity.ActControlData;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.human.HumanManager;
import org.gof.demo.worldsrv.mall.MallManager;
import org.gof.demo.worldsrv.mall.PayMallTypeKey;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgAct2;
import org.gof.demo.worldsrv.msg.MsgDungeon;
import org.gof.demo.worldsrv.msg.MsgError;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.rank.RankManager;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.enumKey.ConfGlobalKey;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;
import org.gof.demo.worldsrv.task.TaskConditionTypeKey;
import com.pwrd.op.LogOp;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ActivityControlSlime extends AbstractActivityControl {

    public static final int BIG_LUCK_EVENT_TYPE = 4;
    private static ActivityControlSlime instance = new ActivityControlSlime(0);

    public static ActivityControlSlime getInstance(int type) {
        instance.type = type;
        return instance;
    }

    @Override
    public void sendActivityData(HumanObject humanObj) {
        super.sendActivityData(humanObj);
    }

    public ActivityControlSlime(int type) {
        super(type);
    }

    public void on_act_slime_info_c2s(HumanObject humanObj) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            return;
        }
        ControlSlimeData controlData = (ControlSlimeData) data.getControlData();
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type, data.getActControlData().getRound());
        RankManager.inst().getMyRankAndScore(confTerm.rank_id, humanObj.getHuman().getServerId(), humanObj.id, ret -> {
            if (ret.failed()) {
                Log.activity.error("排行榜查询失败, rankId={}, serverId={}, humanId={}, e={}", confTerm.rank_id, humanObj.getHuman().getServerId(), humanObj.getHumanId(), ret.cause().getMessage(), ret.cause());
                return;
            }
            controlData.setRank(Utils.intValue(ret.result().get(0)));
            controlData.setActType(type);
            controlData.getStamina();
            if(controlData.getFightStage() == 0){
                humanObj.sendMsg(controlData.getBuilder());
                return;
            }
            //原始数据
            List<Define.p_key_value> fightAttrList = new ArrayList<>(controlData.getFightAttrList());
            //发送给客户端的数据
            List<Define.p_key_value> sendfightAttrList = fightAttrToClient(controlData);
            if(fightAttrList.isEmpty()){
                controlData.setFightAttr(sendfightAttrList);
            }
            if(data.getActControlData().getState() == EActivityType.STATE_ENDSHOW && controlData.getFightStage() > 0){
                controlData.setFightStage(0);
                controlData.clearFightAttr();
                controlData.clearFightInfo();
                data.updateControlData();
            }
            humanObj.sendMsg(controlData.getBuilder());
            controlData.setFightAttr(fightAttrList);
        });
    }

    public void on_act_slime_enter_stage_c2s(HumanObject humanObj, int stageId) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            Log.activity.error("slime没有活动数据, humanId={}, type={}", humanObj.getHumanId(), type);
            return;
        }
        ControlSlimeData controlData = (ControlSlimeData) data.getControlData();
        if (controlData.getFightStage() > 0) {
            Log.activity.error("slime重复进入章节{},{}", humanObj.getHumanId(), type);
            return;
        }
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type, data.getActControlData().getRound());
        if (confTerm == null) {
            Log.activity.error("活动轮次配置表没有找到活动sn:{},轮次:{}", data.getActControlData().getActivitySn(), data.getActControlData().getRound());
            return;
        }
        ConfSlimeLevel_0 confSlimeLevel_0 = ConfSlimeLevel_0.get(type, confTerm.group_id, stageId);
        if(confSlimeLevel_0 == null){
            Log.activity.error("活动轮次配置表没有找到活动sn:{},轮次:{},章节:{}", data.getActControlData().getActivitySn(), data.getActControlData().getRound(), stageId);
            return;
        }
        ConfSlimeDungeon_0 confSlimeDungeon = ConfSlimeDungeon_0.get(type, confTerm.group_id, stageId);
        if(confSlimeDungeon == null){
            Log.activity.error("slime章节表没有找到活动sn:{},轮次:{},章节:{},副本:{}", data.getActControlData().getActivitySn(), data.getActControlData().getRound(), stageId, type);
            return;
        }
        if(controlData.getCostStamina() < confSlimeDungeon.unlock_condition){
            Log.activity.error("slime消耗体力不足未解锁章节{},{}", humanObj.getHumanId(), controlData.getCostStamina());
            return;
        }
        //上一关是否通过
        if(stageId > 1 && !controlData.getPassedStage().contains(stageId - 1)){
            Log.activity.error("slime未通关不可以进入下一章节{},{}", humanObj.getHumanId(), controlData.getPassedStage());
            humanObj.sendMsg(MsgError.error_info_s2c.newBuilder().setCode(505));
            return;
        }
        if(!controlData.consumeStamina(confSlimeDungeon.cost)){
            Log.activity.error("slime体力不足, humanId={}, type={}", humanObj.getHumanId(), type);
            return;
        }

        ActivityManager.inst().addActivityProgress(humanObj, TaskConditionTypeKey.TASK_TYPE_2006, 0, confSlimeDungeon.cost);
        IActivityControl control = ActivityControlTypeFactory.getTypeData(confTerm.parameter);
        if(control == null || !(control instanceof ActivityControlWarToken)) {
            Log.activity.error("翻牌活动{}找不到对应的战力{}", type, confTerm.parameter);
        }else {
            ActivityControlWarToken activityControlWarToken = (ActivityControlWarToken) control;
            activityControlWarToken.addWartokenExp(humanObj, confSlimeDungeon.cost);
        }
        controlData.setFightStage(stageId);
        controlData.initFightAttr();
        data.updateControlData();

        MsgAct2.act_slime_enter_stage_s2c.Builder msg = MsgAct2.act_slime_enter_stage_s2c.newBuilder();
        msg.setActType(type);
        msg.setStageId(stageId);
        msg.setSlime(controlData.getStamina());
        msg.setNextRecoverTime(controlData.getNextRecoverTime());
        msg.setCurrHp(controlData.getCurrHp());
        msg.addAllFightAttr(controlData.getFightAttrList());
        humanObj.sendMsg(msg);
    }

    public void on_act_slime_finish_stage_c2s(HumanObject humanObj, int stageId, int result) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            Log.activity.error("slime没有活动数据, humanId={}, type={}", humanObj.getHumanId(), type);
            return;
        }
        ControlSlimeData controlData = (ControlSlimeData) data.getControlData();
        if (controlData.getFightStage() != stageId) {
            Log.activity.error("slime章节不匹配, humanId={}, type={}, stageId={}, fightStage={}", humanObj.getHumanId(), type, stageId, controlData.getFightStage());
            return;
        }
        if (result == 1) {
            if(!controlData.getPassedStage().contains(stageId)){
                controlData.addPassedStage(stageId);
                addActivityProgress(humanObj, TaskConditionTypeKey.TASK_TYPE_2007, stageId, false, new Object[]{type,stageId});
            }
            controlData.setFightStage(0);
            data.updateControlData();
        } else {
            controlData.setFightStage(0);
        }
//        addDayLogOp(humanObj, controlData);
        int fightLv = controlData.fightLv;
        long fightCombat = controlData.fightCombat;
        controlData.clearFightAttr();
        controlData.clearFightInfo();
        data.updateControlData();
        MsgAct2.act_slime_finish_stage_s2c.Builder msg = MsgAct2.act_slime_finish_stage_s2c.newBuilder();
        msg.setActType(type);
        msg.setStageId(stageId);
        msg.setResult(result);
        msg.setStartLv(fightLv);
        msg.setStartCombat(fightCombat);
        humanObj.sendMsg(msg);
    }

    /**
     * 处理史莱姆事件进度上报
     */
    public void on_act_slime_stage_progress_c2s(HumanObject humanObj, Define.p_slime_event event) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            Log.activity.error("slime没有活动数据, humanId={}, type={}", humanObj.getHumanId(), type);
            return;
        }
        ControlSlimeData controlData = (ControlSlimeData) data.getControlData();
        if(event.getDay() < controlData.day){
            Log.activity.error("slime上报天数错误, humanId={}, type={}, currDay={}, eventDay = {}", humanObj.getHumanId(), type, controlData.day, event.getDay());
            return;
        }
        if(controlData.eventCount >= 15){
            Log.activity.error("slime上报事件数量超过上限, humanId={}, type={}, eventCount={}", humanObj.getHumanId(), type, controlData.eventCount);
            return;
        }
        ConfSlimeIncident confIncident = ConfSlimeIncident.get(event.getEventSn());
        if (confIncident == null) {
            Log.activity.error("slime没有找到事件配置, humanId={}, type={}, eventSn={}", humanObj.getHumanId(), type, event.getEventSn());
            return;
        }
        // 检查是否是新的一天，如果是则记录前一天的日志
        if (event.getDay() > controlData.day) {
//            addDayLogOp(humanObj, controlData);
            // 更新新的一天
            controlData.day = event.getDay();
            controlData.eventCount = 1;
        } else if (event.getDay() == controlData.day) {
            controlData.eventCount++;
        } else {
            Log.activity.error("slime上报事件天数错误, humanId={}, type={}, currDay={}, eventDay = {}", humanObj.getHumanId(), type, controlData.day, event.getDay());
            return;
        }

        if(confIncident.incident_type == BIG_LUCK_EVENT_TYPE) {
            // 大幸运事件
            controlData.setBigLuckCount(controlData.getBigLuckCount() + 1);
            Log.activity.error("大幸运事件, add=1, humanId={}, type={}, eventCount={}", humanObj.getHumanId(), type, controlData.getBigLuckCount());
        }

        // 添加事件到战斗信息列表
        controlData.addFightInfo(event);

        // 如果有效果配置，处理效果
        ConfSlimeEffect conf = ConfSlimeEffect.get(event.getEffectSn());
        if( conf != null) {
            boolean attrChanged = false;

            // 处理三种效果
            Map<Integer, List<Long>> effectMap = new HashMap<>();
            for(Define.p_key_value_list effectValue : event.getEffectValueList()){
                effectMap.put((int)effectValue.getK(), effectValue.getListList());
            }
            attrChanged |= processEffect(humanObj, controlData, conf.effect_type1, conf.value1, effectMap.get(conf.effect_type1));
            attrChanged |= processEffect(humanObj, controlData, conf.effect_type2, conf.value2, effectMap.get(conf.effect_type2));
            attrChanged |= processEffect(humanObj, controlData, conf.effect_type3, conf.value3, effectMap.get(conf.effect_type3));

            // 如果属性发生变化，发送更新消息
            if (attrChanged) {
                sendSlimeAttrChange(humanObj, controlData);
            }
        }

        data.updateControlData();
    }

    private static void addDayLogOp(HumanObject humanObj, ControlSlimeData controlData) {
        // 收集前一天的事件数据
        List<Integer> eventSnList = new ArrayList<>();
        List<Integer> effectSnList = new ArrayList<>();
        List<Define.p_slime_event> fightInfo = controlData.getFightInfo();
        for (int i = fightInfo.size() - 1; i >= 0; i--) {
            Define.p_slime_event oldEvent = fightInfo.get(i);
            if (oldEvent.getDay() != controlData.day) {
                break;
            }
            eventSnList.add(oldEvent.getEventSn());
            if (oldEvent.getEffectSn() > 0) {
                effectSnList.add(oldEvent.getEffectSn());
            }
        }

        // 记录日志
        if (!eventSnList.isEmpty()) {
            LogOp.log("slimeDaily",
                    humanObj.getHuman().getId(),
                    Port.getTime(),
                    Utils.formatTime(Port.getTime(), "yyyy-MM-dd"),
                    humanObj.getHuman().getAccount(),
                    humanObj.getHuman().getName(),
                    humanObj.getHuman().getLevel(),
                    humanObj.getHuman().getServerId(),
                    controlData.day,
                    controlData.eventCount,
                    Utils.listToString(eventSnList),
                    Utils.listToString(effectSnList)
            );

        }
    }

    /**
     * 处理单个效果
     */
    private boolean processEffect(HumanObject humanObj, ControlSlimeData controlData, int effectType, int[][] value,
                                  List<Long> effectValue) {
        if (effectType <= 0 || value == null) {
            return false;
        }

        boolean attrChanged = false;
        switch (effectType) {
            case 1: // 属性
                PropCalc propCalc = new PropCalc(controlData.getFightAttrList());
                long currHp = controlData.getCurrHp();
                for (int i = 0; i < value.length; i++) {
                    int attrType = value[i][0];
                    long attrValue;
                    if (value[i].length == 2) { // 固定值
                        attrValue = value[i][1];
                    } else { // 随机值
                        attrValue = effectValue != null ? effectValue.get(i) : Utils.random(value[i][1], value[i][2]);
                    }
                    // 更新战斗属性
                    currHp = changeFightAttr(attrType, attrValue, propCalc, currHp);
                    attrChanged = true;
                }
                if (attrChanged){
                    List<Define.p_key_value> attrList = ActivityControlSlime.getPropList(propCalc, false);
                    controlData.setFightAttr(attrList);
                    controlData.setCurrHp(currHp);
                    Log.activity.debug("slime属性变化, humanId={}, type={}, attrList={}", humanObj.getHumanId(), type, attrList);
                }
                break;

            case 2: // 道具
            case 11: // 道具
                ProduceManager.inst().produceAdd(humanObj, value, MoneyItemLogKey.史莱姆);
                break;
            case 4: // 赌博机
            case 5: // 转盘
                if(effectValue != null && effectValue.size() > 0){
                    long gambleSn = effectValue.get(0);
                    ConfSlimeGamble confGamble = ConfSlimeGamble.get((int)gambleSn);
                    if (confGamble == null) {
                        Log.activity.error("slime没有找到转盘配置, humanId={}, type={}, gambleSn={}", humanObj.getHumanId(), type, gambleSn);
                        return false;
                    }
                    Map<Integer, Integer> rewardMap;
                    if(confGamble.isCombo > 0){
                        rewardMap = Utils.intArrToIntMap(confGamble.reward, 3);
                    }else {
                        rewardMap = Utils.intArrToIntMap(confGamble.reward);
                    }
                    ProduceManager.inst().produceAdd(humanObj, rewardMap, MoneyItemLogKey.史莱姆);
                }
                break;
            case 6: // 触发另外的效果和扣血
                if(effectValue != null){
                    for (long buffSn : effectValue) {
                        attrChanged |= processBuff(humanObj, controlData, (int) buffSn, true);
                    }
                    if(effectValue.size() >= 2){
                        int damage = ConfGlobal.get(ConfGlobalKey.Slime_lucky_turntable_cost2).value;
                        controlData.setCurrHp((long)(controlData.getCurrHp() * (1 - damage / 100f)));
                        attrChanged = true;
                    }
                }
                break;
            case 7: // 恢复生命万分比
                long healAmount = 0;
                if(value[0][0] > 0){
                    healAmount = (long)(controlData.getMaxHp() * (value[0][0] / 10000f));
                }else {
                    healAmount = (long)(controlData.getCurrHp() * (value[0][0] / 10000f));
                }
                currHp = controlData.getCurrHp() + healAmount;
                currHp = Math.min(currHp, controlData.getMaxHp());
                attrChanged |= currHp != controlData.getCurrHp();
                controlData.setCurrHp(currHp);
                break;
            case 8: // 触发另外的效果
            case 9: // 触发另外的效果
            case 10: // 触发另外的效果
                if(effectValue != null){
                    for (long buffSn : effectValue) {
                        attrChanged |= processBuff(humanObj, controlData, (int) buffSn, true);
                    }
                }
                break;
            case 12: // 触发另外的效果
            if(effectValue != null){
                int skinSn = Utils.intValue(effectValue.get(0));
                controlData.setSkin(skinSn);
            }
            break;

            // 其他类型暂不处理
        }

        return attrChanged;
    }

    public boolean processBuff(HumanObject humanObj, ControlSlimeData controlData, int buffSn, boolean inStage) {
        ConfSlimeBuffList confBuff = ConfSlimeBuffList.get(buffSn);
        if (confBuff == null) {
            Log.activity.error("找不到buff配置, buffSn={}", buffSn);
            return false;
        }
        boolean attrChanged = false;
        switch (confBuff.effect_type) {
            case 1: // 属性
                if(inStage){
                    PropCalc propCalc = new PropCalc(controlData.getFightAttrList());
                    long currHp = controlData.getCurrHp();
                    for (int i = 0; i < confBuff.value.length; i++) {
                        int attrType = confBuff.value[i][0];
                        long attrValue = confBuff.value[i][1];
                        // 更新战斗属性
                        currHp = changeFightAttr(attrType, attrValue, propCalc, currHp);
                        attrChanged = true;
                    }
                    if (attrChanged){
                        List<Define.p_key_value> attrList = ActivityControlSlime.getPropList(propCalc, false);
                        controlData.setFightAttr(attrList);
                        controlData.setCurrHp(currHp);
                        Log.activity.debug("slime属性变化, humanId={}, type={}, attrList={}", humanObj.getHumanId(), type, attrList);
                    }
                }else {
                    PropCalc propCalc = new PropCalc(controlData.getBuilder().getWheelAttrList());
                    for (int i = 0; i < confBuff.value.length; i++) {
                        int attrType = confBuff.value[i][0];
                        long attrValue = confBuff.value[i][1];
                        // 更新战斗属性
                        propCalc.plus(attrType, new BigDecimal(attrValue));
                        controlData.getBuilder().clearWheelAttr();
                        controlData.getBuilder().addAllWheelAttr(ActivityControlSlime.getPropList(propCalc, false));
                        attrChanged = true;
                    }
                    return attrChanged;
                }
                break;
            case 2: // 道具
                ProduceManager.inst().produceAdd(humanObj, confBuff.value, MoneyItemLogKey.史莱姆);
                break;

            case 7: // 恢复生命万分比
                long healAmount = (long)(controlData.getMaxHp() * (confBuff.value[0][0] / 10000f));
                long currHp = controlData.getCurrHp() + healAmount;
                currHp = Math.min(currHp, controlData.getMaxHp());
                controlData.setCurrHp(currHp);
                attrChanged = true;
                break;

            // 其他类型暂不处理
        }
        return attrChanged;
    }

    /**
     * 处理史莱姆天赋升级请求
     */
    public void on_act_slime_talent_upgrade_c2s(HumanObject humanObj, int attrType) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            Log.activity.error("slime没有活动数据, humanId={}, type={}", humanObj.getHumanId(), type);
            return;
        }
        ControlSlimeData controlData = (ControlSlimeData) data.getControlData();

        // 获取当前期数配置
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type, data.getActControlData().getRound());
        if (confTerm == null) {
            Log.activity.error("活动轮次配置表没有找到活动sn:{},轮次:{}", data.getActControlData().getActivitySn(), data.getActControlData().getRound());
            return;
        }

        // 获取当前天赋等级
        int currLevel = 0;
        for (Define.p_key_value talent : controlData.getBuilder().getTalentLvList()) {
            if (talent.getK() == attrType) {
                currLevel = (int)talent.getV();
                break;
            }
        }
        // 当前等级技能配置
        ConfSlimeLevel_0 confSlimeLevel_0 = ConfSlimeLevel_0.get(type, confTerm.group_id, controlData.getLevel());
        if (confSlimeLevel_0 == null) {
            Log.activity.error("活动轮次配置表没有找到活动sn:{},轮次:{},等级:{}", data.getActControlData().getActivitySn(), data.getActControlData().getRound(), controlData.getLevel());
            return;
        }
        if(currLevel >= confSlimeLevel_0.talent_limit){
            Log.activity.error("天赋等级已满, type={}, group={}, level={}", type, confTerm.group_id, currLevel);
            return;
        }
        // 获取下一级天赋配置
        ConfSlimeTalent confTalent = ConfSlimeTalent.get(type, confTerm.group_id, currLevel + 1);
        if (confTalent == null) {
            Log.activity.error("找不到天赋配置, type={}, group={}, level={}", type, confTerm.group_id, currLevel + 1);
            return;
        }

        // 检查消耗
        int cost;
        if(attrType == PropKey.atk.getAttributeSn()){
            cost = confTalent.atk_add[2];
        }else if(attrType == PropKey.hp.getAttributeSn()){
            cost = confTalent.hp_add[2];
        }else if(attrType == PropKey.def.getAttributeSn()){
            cost = confTalent.def_add[2];
        }else {
            Log.activity.error("无效的属性类型, attrType={}", attrType);
            return;
        }
        int itemSn = ConfGlobal.get("Slime_talent_item").value;
        // 扣除材料
        if (!ProduceManager.inst().checkAndCostItem(humanObj, itemSn, cost, MoneyItemLogKey.史莱姆).success) {
            Log.activity.error("道具不足, type={}, group={}, level={}, costNum={}", type, confTerm.group_id, currLevel + 1, cost);
            return;
        }

        // 更新天赋等级
        boolean found = false;
        Define.p_key_value.Builder newTalent = Define.p_key_value.newBuilder().setK(attrType).setV(currLevel + 1);
        for (int i = 0; i < controlData.getBuilder().getTalentLvCount(); i++) {
            Define.p_key_value talent = controlData.getBuilder().getTalentLv(i);
            if (talent.getK() == attrType) {
                controlData.getBuilder().setTalentLv(i, newTalent);
                found = true;
                break;
            }
        }
        if (!found) {
            controlData.getBuilder().addTalentLv(newTalent);
        }

        // 计算并更新战力
         recalculateCombat(humanObj, controlData, confTerm);

        // 保存数据
        data.updateControlData();

        // 发送响应消息
        MsgAct2.act_slime_talent_upgrade_s2c.Builder msg = MsgAct2.act_slime_talent_upgrade_s2c.newBuilder();
        msg.setActType(type);
        msg.addAllTalentLevel(controlData.getBuilder().getTalentLvList());
        humanObj.sendMsg(msg);

        sendSlimeAttrChange(humanObj, controlData);
    }

    public void on_act_slime_wheel_draw_c2s(HumanObject humanObj) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            Log.activity.error("slime没有活动数据, humanId={}, type={}", humanObj.getHumanId(), type);
            return;
        }
        ControlSlimeData controlData = (ControlSlimeData) data.getControlData();

        // 获取当前期数配置
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type, data.getActControlData().getRound());
        if (confTerm == null) {
            Log.activity.error("活动轮次配置表没有找到活动sn:{},轮次:{}", data.getActControlData().getActivitySn(), data.getActControlData().getRound());
            return;
        }
        ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.Slime_turntable_needtime);
        int costNum = confGlobal.value;
        if(controlData.getBigLuckCount() < costNum){
            Log.activity.error("slime大幸运次数不足, humanId={}, type={}, bigLuckCount={}", humanObj.getHumanId(), type, controlData.getBigLuckCount());
            return;
        }
        // 扣除材料
        controlData.setBigLuckCount(controlData.getBigLuckCount() - costNum);
        // Random select buff by weight
        int totalWeight = 0;
        Map<Integer, ConfSlimeBuffList> buffMap = new HashMap<>();

        // Filter buffs by group and calculate total weight
        for (int confSn : confGlobal.intArray) {
            ConfSlimeBuffList conf = ConfSlimeBuffList.get(confSn);
            if (conf == null) {
                Log.activity.error("slime大吉转盘找不到buff配置, buffSn={}", confSn);
                continue;
            }
            totalWeight += conf.weight;
            buffMap.put(conf.sn, conf);
        }

        // Random select
        int randWeight = Utils.random(1, totalWeight+1);
        int currWeight = 0;
        ConfSlimeBuffList selectedBuff = null;

        for (ConfSlimeBuffList conf : buffMap.values()) {
            currWeight += conf.weight;
            if (randWeight <= currWeight) {
                selectedBuff = conf;
                break;
            }
        }

        if (selectedBuff != null) {
            // Handle buff effect
            boolean attrChanged = processBuff(humanObj, controlData, selectedBuff.sn, false);

            // Update data if attributes changed
            if (attrChanged) {
                recalculateCombat(humanObj, controlData, confTerm);
                sendSlimeAttrChange(humanObj, controlData);
            }

            // Save data
            data.updateControlData();

            // Send response
            MsgAct2.act_slime_wheel_draw_s2c.Builder msg = MsgAct2.act_slime_wheel_draw_s2c.newBuilder();
            msg.setActType(type);
            msg.setBuffSn(selectedBuff.sn);
            msg.setBigLuckNum(controlData.getBigLuckCount());
            msg.addAllWheelAttr(controlData.getBuilder().getWheelAttrList());
            humanObj.sendMsg(msg);
            Log.activity.error("slime大吉转盘, humanId={}, type={}, buffSn={}", humanObj.getHumanId(), type, selectedBuff.sn);
        }
    }

    public void on_act_slime_skill_refine_c2s(HumanObject humanObj, int pos) {
        // 获取活动数据
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            Log.activity.error("史莱姆没有活动数据, humanId={}, type={}", humanObj.getHumanId(), type);
            return;
        }

        ControlSlimeData controlData = (ControlSlimeData) data.getControlData();

        // 获取当前期数配置
        ActControlData actData = data.getActControlData();
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type, actData.getRound());
        if (confTerm == null) {
            Log.activity.error("找不到活动期数配置, type={}, round={}", type, actData.getRound());
            return;
        }

        // 检查槽位是否有效
        ConfSlimeLevel_0 levelConf = ConfSlimeLevel_0.get(type, confTerm.group_id, controlData.getLevel());
        if (levelConf == null) {
            Log.activity.error("找不到等级配置, type={}, level={}", type, controlData.getLevel());
            return;
        }

        // 检查槽位是否已开启
        if (pos < 0 || pos+1 > levelConf.skill_count) {
            Log.activity.error("技能槽位未开启, humanId={}, pos={}, maxPos={}",
                humanObj.getHumanId(), pos, levelConf.skill_count);
            return;
        }

        // 根据权重随机选择新技能
        int totalWeight = 0;
        Map<Integer, Integer> weightMap = new HashMap<>();
        for (ConfSlimeSkill conf : ConfSlimeSkill.findAll()) {
            totalWeight += conf.weight_item;
            weightMap.put(conf.sn, conf.weight_item);
        }

        // 随机选择一个技能
        int randWeight = Utils.random(1, totalWeight);
        int selectedSn = 0;
        int currWeight = 0;
        for (Map.Entry<Integer, Integer> entry : weightMap.entrySet()) {
            currWeight += entry.getValue();
            if (randWeight <= currWeight) {
                selectedSn = entry.getKey();
                break;
            }
        }

        // 更新技能
        Define.p_slime_skill.Builder skill = Define.p_slime_skill.newBuilder();
        skill.setPos(pos);
        skill.setRefineSkill(selectedSn);
        skill.setUseSkill(0);
        skill.setRefineCount(0);

        // 更新或添加技能
        int count = 0;
        for (int i = 0; i < controlData.getBuilder().getSkillInfoCount(); i++) {
            Define.p_slime_skill existingSkill = controlData.getBuilder().getSkillInfo(i);
            if (existingSkill.getPos() == pos) {
                count = existingSkill.getRefineCount();
                if(!ProduceManager.inst().checkAndCostItem(humanObj, getRefineCostNum((int)count), MoneyItemLogKey.史莱姆).success){
                    Log.activity.error("玩家：{}道具不足, type={}, pos={}, costNum={}", humanObj.id, type, pos, getRefineCostNum((int)count));
                    return;
                }
                count++;
                skill.setUseSkill(existingSkill.getUseSkill());
                skill.setRefineSkill(selectedSn);
                skill.setRefineCount(count);
                controlData.getBuilder().setSkillInfo(i, skill);
                break;
            }
        }
        if (count == 0) {
            if(!ProduceManager.inst().checkAndCostItem(humanObj, getRefineCostNum((int)count), MoneyItemLogKey.史莱姆).success){
                Log.activity.error("玩家：{}道具不足, type={}, pos={}, level={}, costNum", humanObj.id, type, pos, getRefineCostNum((int)count));
                return;
            }
            controlData.getBuilder().addSkillInfo(skill);
            count++;
        }

        // 保存数据
        data.updateControlData();

        // 发送响应消息
        MsgAct2.act_slime_skill_refine_s2c.Builder msg = MsgAct2.act_slime_skill_refine_s2c.newBuilder();
        msg.setActType(type);
        msg.setPos(pos);
        msg.setSn(selectedSn);
        msg.setRefineCount(count);
        humanObj.sendMsg(msg);
    }

    private int[][] getRefineCostNum(int count) {
        ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.Slime_refine_item);
        count = Math.min(count, confGlobal.intArray.length-1);
        int[][] cost = new int[1][2];
        cost[0][0] = confGlobal.value;
        cost[0][1] = confGlobal.intArray[count];
        return cost;
    }

    public void on_act_slime_skill_choose_c2s(HumanObject humanObj, int pos, int isReplace) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            Log.activity.error("slime没有活动数据, humanId={}, type={}", humanObj.getHumanId(), type);
            return;
        }
        ActControlData actData = data.getActControlData();
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type, actData.getRound());
        if (confTerm == null) {
            Log.activity.error("找不到活动期数配置, type={}, round={}", type, actData.getRound());
            return;
        }
        ControlSlimeData controlData = (ControlSlimeData) data.getControlData();
        MsgAct2.act_slime_skill_choose_s2c.Builder msg = MsgAct2.act_slime_skill_choose_s2c.newBuilder();
        msg.setActType(type);
        msg.setPos(pos);
        msg.setIsReplace(isReplace);
        for (int i = 0; i < controlData.getBuilder().getSkillInfoCount(); i++) {
            Define.p_slime_skill.Builder skill = controlData.getBuilder().getSkillInfo(i).toBuilder();
            if (skill.getPos() == pos) {
                if(isReplace > 0){
                    skill.setUseSkill(skill.getRefineSkill());
                    msg.setSkillSn(skill.getRefineSkill());
                    skill.setRefineSkill(0);
                    controlData.getBuilder().setSkillInfo(i, skill);
                    recalculateCombat(humanObj, controlData, confTerm);
                }else {
                    msg.setSkillSn(controlData.getBuilder().getSkillInfo(i).getUseSkill());
                }
                break;
            }
        }
        // 发送消息
        humanObj.sendMsg(msg);
        data.updateControlData();
        sendSlimeAttrChange(humanObj, controlData);
    }

    public void on_act_slime_refresh_c2s(HumanObject humanObj) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            Log.activity.error("slime没有活动数据, humanId={}, type={}", humanObj.getHumanId(), type);
            return;
        }
        ControlSlimeData controlData = (ControlSlimeData) data.getControlData();
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type, data.getActControlData().getRound());
        if (confTerm == null) {
            Log.activity.error("活动轮次配置表没有找到活动sn:{},轮次:{}", data.getActControlData().getActivitySn(), data.getActControlData().getRound());
            return;
        }
        MsgAct2.act_slime_refresh_s2c.Builder msg = MsgAct2.act_slime_refresh_s2c.newBuilder();
        controlData.isStaminaChange = false;
        msg.setActType(type);
        msg.setSlime(controlData.getStamina());
        msg.setNextRecoverTime(controlData.getNextRecoverTime());
        if(controlData.isStaminaChange){
            data.updateControlData();
        }
        humanObj.sendMsg(msg);
    }

    public void sendSlimeAttrChange(HumanObject humanObj, ControlSlimeData controlData) {
        MsgAct2.act_slime_attr_change_s2c.Builder msg = MsgAct2.act_slime_attr_change_s2c.newBuilder();
        msg.setActType(type);
        msg.setCombat(controlData.getCombat());
        msg.setCurrHp(controlData.getCurrHp());
        msg.setLv(controlData.getLevel());
        msg.setExp(controlData.getExp());
        msg.setBigLuckCount(controlData.getBigLuckCount());
        msg.addAllAttrList(controlData.getAttrListList());
        List<Define.p_key_value> attr =  fightAttrToClient(controlData);
        msg.addAllFightAttrList(attr);
        humanObj.sendMsg(msg);
    }

    private List<Define.p_key_value> fightAttrToClient(ControlSlimeData controlData) {
        List<Define.p_key_value> fightAttrList = new ArrayList<>();
        PropCalc fightAttr = new PropCalc(controlData.getFightAttrList());
        for (Map.Entry<Integer, BigDecimal> entry : fightAttr.getDatas().entrySet()) {
            int key = entry.getKey();
            long value = HumanManager.inst().getCalculationValue(key, fightAttr.getDatas());
            if(value == -1){
                value = fightAttr.getBigDecimal(key).longValue();
            }
            Define.p_key_value.Builder attr = HumanManager.inst().to_p_key_value(key, value);
            fightAttrList.add(attr.build());
        }
        return fightAttrList;
    }

    /**
     * 重新计算史莱姆战力值
     * @param controlData 史莱姆控制数据
     */
    public void recalculateCombat(HumanObject humanObj, ControlSlimeData controlData, ConfActivityTerm confTerm) {
        int actType = controlData.getBuilder().getActType();
        int groupId = confTerm.group_id;
        ConfSlimeLevel_0 conf = ConfSlimeLevel_0.get(actType, groupId, controlData.getLevel());
        if(conf == null){
            Log.activity.error("活动轮次配置表没有找到活动type:{},轮次:{},等级:{}", actType, groupId, controlData.getLevel());
            return;
        }
        // 创建属性计算器
        PropCalc propCalc = new PropCalc();

        // 1. 添加等级属性
        propCalc.plus(conf.base_attr);

        // 2. 添加天赋属性
        for (Define.p_key_value talent : controlData.getBuilder().getTalentLvList()) {
            int attrType = (int)talent.getK();
            int level = (int)talent.getV();
            ConfSlimeTalent confSlimeTalent = ConfSlimeTalent.get(controlData.getBuilder().getActType(), groupId, level);
            if (confSlimeTalent != null) {
                if (attrType == PropKey.atk.getAttributeSn()) {
                    propCalc.plus(confSlimeTalent.atk_add[0], new BigDecimal(confSlimeTalent.atk_add[1]));
                } else if (attrType == PropKey.hp.getAttributeSn()) {
                    propCalc.plus(confSlimeTalent.hp_add[0], new BigDecimal(confSlimeTalent.hp_add[1]));
                } else if (attrType == PropKey.def.getAttributeSn()) {
                    propCalc.plus(confSlimeTalent.def_add[0], new BigDecimal(confSlimeTalent.def_add[1]));
                }
            }
        }

        // 4. 添加转盘属性
        for (Define.p_key_value wheelAttr : controlData.getBuilder().getWheelAttrList()) {
            propCalc.plus((int)wheelAttr.getK(), new BigDecimal(wheelAttr.getV()));
        }

        // 5. 添加技能属性
        for (Define.p_slime_skill skill : controlData.getBuilder().getSkillInfoList()) {
            int skillSn = skill.getUseSkill();
            ConfSlimeSkill confSlimeSkill = ConfSlimeSkill.get(skillSn);
            if (confSlimeSkill != null) {
                propCalc.plus(confSlimeSkill.effect[0], new BigDecimal(confSlimeSkill.effect[1]));
            }
        }

        // 6. 属性重新计算（处理万分比加成）
        propCalc = PropManager.inst().recalculatePropPlus(propCalc);

        controlData.getBuilder().clearAttrList();
        for (Map.Entry<Integer, BigDecimal> entry : propCalc.getDatas().entrySet()) {
            int key = entry.getKey();
            // 1001、1002、1003、1024转换成最终属性1、2、3、24
            int newKey = HumanManager.inst().getType(key);
            long value = HumanManager.inst().getCalculationValue(key, propCalc.getDatas());
            if(value == -1){
                // 1001、1002、1003、1024转换成最终属性1、2、3、24
                value = propCalc.getBigDecimal(newKey).longValue();
            }
            Define.p_key_value.Builder attr = HumanManager.inst().to_p_key_value(key, value);
            controlData.getBuilder().addAttrList(attr);
        }

        // 7. 计算最终战力
        long combat = 0;
        for (Map.Entry<Integer, BigDecimal> entry : propCalc.getDatas().entrySet()) {
            int key = entry.getKey();
            BigDecimal value;

            // 对于1001、1002、1003、1024这些属性，使用对应的1、2、3、24的值
            if (key == 1001 || key == 1002 || key == 1003 || key == 1024) {
                int finalKey = key % 1000; // 将1001转为1，1002转为2，以此类推
                value = propCalc.getBigDecimal(finalKey);
                if (value == null || value.compareTo(BigDecimal.ZERO) == 0) {
                    continue;
                }
            } else {
                value = entry.getValue();
                if (value.compareTo(BigDecimal.ZERO) == 0) {
                    continue;
                }
            }

            float mul = GlobalConfVal.getCombatCoefficient(key);
            if (mul == 0) {
                continue;
            }
            combat += value.multiply(new BigDecimal(mul)).longValue();
        }

        controlData.setCombat(combat);
        if(combat > controlData.maxCombat){
            controlData.maxCombat = combat;
            addActivityProgress(humanObj, TaskConditionTypeKey.TASK_TYPE_2005, combat, false, new Object[]{controlData.maxCombat});
        }
    }

    public static List<Define.p_key_value> getPropList(PropCalc propCalc, boolean isRecalc) {
        if (isRecalc) {
            propCalc = PropManager.inst().recalculatePropPlus(propCalc);
        }
        List<Define.p_key_value> attrList = new java.util.ArrayList<>();
        for (Map.Entry<Integer, BigDecimal> entry : propCalc.getDatas().entrySet()) {
            int key = entry.getKey();
            long value = propCalc.getBigDecimal(key).longValue();
            Define.p_key_value.Builder attr = HumanManager.inst().to_p_key_value(key, value);
            attrList.add(attr.build());
        }
        return attrList;
    }

    @Override
    public void itemUse(HumanObject humanObj, int goodsId, int num) {
        ActivityControlObjectData controlData = humanObj.operation.activityControlData.getControlObjectData(type);
        if (controlData == null) {
            return;
        }
        ActControlData actData = controlData.getActControlData();
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type, actData.getRound());
        if (confTerm == null) {
            Log.activity.error("活动找不到活动期数表ConfActivityTerm, type={}, round={}", type, actData.getRound());
            return;
        }
        ControlSlimeData data = (ControlSlimeData) controlData.getControlData();
        data.setStamina(data.getStamina() + num);
        controlData.updateControlData();
        on_act_slime_refresh_c2s(humanObj);
    }

    @Override
    public void itemAdd(HumanObject humanObj, int goodsId, int num) {
        ActivityControlObjectData controlData = humanObj.operation.activityControlData.getControlObjectData(type);
        if (controlData == null) {
            return;
        }
        ActControlData actData = controlData.getActControlData();
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type, actData.getRound());
        if (confTerm == null) {
            Log.activity.error("活动找不到活动期数表ConfActivityTerm, type={}, round={}", type, actData.getRound());
            return;
        }

        ControlSlimeData data = (ControlSlimeData) controlData.getControlData();

        // 获取当前等级和经验
        int currentLevel = data.getLevel();
        long currentExp = data.getExp();

        // 累加经验值
        long newExp = currentExp + num;
        boolean levelUp = false;

        while (true) {
            // 获取当前等级配置
            ConfSlimeLevel_0 confLevel = ConfSlimeLevel_0.get(type, confTerm.group_id, currentLevel);
            if (confLevel == null) {
                break;
            }

            // 获取下一级配置
            ConfSlimeLevel_0 nextLevelConf = ConfSlimeLevel_0.get(type, confTerm.group_id, currentLevel + 1);
            if (nextLevelConf == null) {
                // 已达到最高等级，限制经验值上限
                break;
            }

            // 判断是否可以升级
            if (newExp >= nextLevelConf.exp_upgrade) {
                currentLevel++;
                levelUp = true;
            }else {
                // 经验值不足，停止升级
                break;
            }
        }

        // 更新等级和经验
        data.setLevel(currentLevel);
        data.setExp(newExp);

        // 如果发生升级，重新计算战斗属性
        if (levelUp) {
            recalculateCombat(humanObj, data, confTerm);
            String conditionKey = MallManager.inst().getCommonConditionKey(PayMallTypeKey.slime_level, currentLevel, type);
            if(conditionKey != null){
                MallManager.inst().pushLimitGift26Common(humanObj, conditionKey);
            }
        }

        // 更新数据
        controlData.updateControlData();

        // 发送属性变化消息
        sendSlimeAttrChange(humanObj, data);
    }
  private long changeFightAttr(int attrType, long attrValue, PropCalc fightAttr ,long currHp) {
      Log.activity.debug("slime血量={} ", currHp);
      long maxHp;
      switch (attrType) {
          case 1001: // 攻击固定值
              fightAttr.plus(1001, BigDecimal.valueOf(attrValue));
              if (fightAttr.getBigDecimal(1001).compareTo(BigDecimal.ZERO) < 0) {
                  fightAttr.put(1001, BigDecimal.ZERO);
              }
              Log.activity.debug("slime攻击属性变化 attrType={}, attrValue={}", attrType, fightAttr.getBigDecimal(1));
              break;
          case 3001: // 攻击万分比
              fightAttr.mul(1001, 1 + attrValue/10000f);
              if (fightAttr.getBigDecimal(1).compareTo(BigDecimal.ZERO) < 0) {
                  fightAttr.put(1001, BigDecimal.ZERO);
              }
              Log.activity.debug("slime攻击属性变化, attrType={}, attrValue={}, finalVale={}", attrType, attrValue, fightAttr.getBigDecimal(1001));
              break;
          case 1002: // 生命固定值
              fightAttr.plus(1002, BigDecimal.valueOf(attrValue));
              maxHp = fightAttr.getBigDecimal(1002).longValue();
              if (maxHp < 0) {
                  fightAttr.put(1002, BigDecimal.ZERO);
              }
              if(attrValue > 0){
                  currHp += attrValue;
                  currHp = currHp > maxHp ? maxHp : currHp;
              }
                Log.activity.debug("slime生命属性变化, attrType={}, attrValue={}, finalVale={}", attrType, attrValue, fightAttr.getBigDecimal(1002));
              break;
          case 3002: // 生命百分比
              float mult = 1 + attrValue/10000f;
              // 记录当前最大生命值
              long oldMaxHp = fightAttr.getBigDecimal(1002).longValue();

              // 更新最大生命
              fightAttr.mul(1002, mult);
              maxHp = fightAttr.getBigDecimal(1002).longValue();
              if (maxHp < 0) {
                  fightAttr.put(1002, BigDecimal.ZERO);
                  maxHp = 0;
              }

              // 计算最大生命值增加了多少
              long hpIncrease = maxHp - oldMaxHp;

              // 当前生命值增加相同的绝对值
              if (hpIncrease > 0) {
                  currHp += hpIncrease;
              }

              // 确保不超过最大生命
              currHp = currHp > maxHp ? maxHp : currHp;

              break;
          case 1024: // 防御固定值
              fightAttr.plus(1024, BigDecimal.valueOf(attrValue));
              if (fightAttr.getBigDecimal(1024).compareTo(BigDecimal.ZERO) < 0) {
                  fightAttr.put(1024, BigDecimal.ZERO);
              }
              Log.activity.debug("slime防御属性变化, attrType={}, attrValue={}, finalVale={}", attrType, attrValue, fightAttr.getBigDecimal(1024));
              break;
          case 3024: // 防御百分比
              fightAttr.mul(1024, 1 + attrValue/10000f);
              if (fightAttr.getBigDecimal(1024).compareTo(BigDecimal.ZERO) < 0) {
                  fightAttr.put(1024, BigDecimal.ZERO);
              }
              Log.activity.debug("slime防御属性变化, attrType={}, attrValue={}, finalVale={}", attrType, attrValue, fightAttr.getBigDecimal(1024));
              break;
          case 1003: // 攻速
                fightAttr.plus(3, BigDecimal.valueOf(attrValue));
                if (fightAttr.getBigDecimal(3).compareTo(BigDecimal.ZERO) < 0) {
                    fightAttr.put(3, BigDecimal.ZERO);
                }
                BigDecimal total_att_speed = Utils.getBigDecimal(fightAttr.getBigDecimal(3), fightAttr.getBigDecimal(3003));
                fightAttr.put(1003, total_att_speed);
                Log.activity.debug("slime攻速属性变化, attrType={}, attrValue={}, finalVale={}", attrType, attrValue, fightAttr.getBigDecimal(1003));
                break;
          case 3003: // 攻速百分比
              fightAttr.mul(3, 1 + attrValue / 10000f);
              if (fightAttr.getBigDecimal(3).compareTo(BigDecimal.ZERO) < 0) {
                  fightAttr.put(3, BigDecimal.ZERO);
              }
              total_att_speed = Utils.getBigDecimal(fightAttr.getBigDecimal(3), fightAttr.getBigDecimal(3003));
              fightAttr.put(1003, total_att_speed);
              Log.activity.debug("slime攻速属性变化, attrType={}, attrValue={}, finalVale={}", attrType, attrValue, fightAttr.getBigDecimal(1003));
              break;
          default:
              Log.activity.debug("slime特殊属性变化, attrType={}, attrValue={}, currentValue", attrType, attrValue, fightAttr.getBigDecimal(attrType));
              fightAttr.plus(attrType, BigDecimal.valueOf(attrValue));
              Log.activity.debug("slime特殊属性变化, attrType={}, attrValue={}, finalVale={}", attrType, attrValue, fightAttr.getBigDecimal(attrType));
              break;
      }
      Log.activity.debug("slime血量={} ", currHp);
      return currHp;
  }

    public void on_dungeon_battle_start_c2s(HumanObject humanObj, int dungeonType, int level) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            Log.activity.error("slime没有活动数据, humanId={}, type={}", humanObj.getHumanId(), dungeonType);
            return;
        }
        ControlSlimeData controlData = (ControlSlimeData) data.getControlData();

        int job = ConfGlobal.get((ConfGlobalKey.Slime_initial_job.SN)).value;
        ConfJobs confJobs = ConfJobs.get(job);
        if(confJobs == null){
            Log.activity.error("slime没有找到职业配置表, humanId={}, type={}, job={}", humanObj.getHumanId(), dungeonType, job);
            return;
        }
        Define.p_battle_role.Builder role = Define.p_battle_role.newBuilder();
        Define.p_role_figure.Builder figure = Define.p_role_figure.newBuilder();
        figure.setJobFigure(job);
        figure.addSkinList(Define.p_key_value.newBuilder().setK(2).setV(controlData.getSkin() > 0 ? controlData.getSkin() : confJobs.skin));
        Define.p_role_skill.Builder skill = Define.p_role_skill.newBuilder();
        for (int i = 1; i <=5; ++i){
            Define.p_key_value.Builder equip = Define.p_key_value.newBuilder();
            equip.setK(i);
            equip.setV(0);
            figure.addEquipList(equip);
        }
        role.setRoleSkill(skill);

        role.setId(humanObj.getHumanId());
        role.setName(humanObj.getHuman().getName());
        role.setLev(controlData.getLevel());
        role.setJob(job);
        role.addAllAttrList(controlData.getFightAttrList());
        role.addExt(Define.p_key_value.newBuilder().setK(ParamKey.extPosType_4).setV(controlData.getCurrHp()));
        role.setFigure(figure);
        Log.activity.debug("slime副本战斗开始,设置了当前血量为：{}", controlData.getCurrHp());

        MsgDungeon.dungeon_battle_start_s2c.Builder msg = MsgDungeon.dungeon_battle_start_s2c.newBuilder();
        msg.setCode(0);
        msg.setType(dungeonType);
        msg.setDungeonId(level);
        msg.setBattleCheckout(2);
        msg.setRandomSeed(Utils.getTimeSec());
        msg.addRoles(role);
        humanObj.sendMsg(msg);
    }

    public void on_dungeon_battle_result_c2s(HumanObject humanObj, int dungeonType, int dungeonId, int result, List<Define.p_key_value> argsList) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            Log.activity.error("slime没有活动数据, humanId={}, type={}", humanObj.getHumanId(), dungeonType);
            return;
        }
        ControlSlimeData controlData = (ControlSlimeData) data.getControlData();
        if (result != 0) {
            MsgDungeon.dungeon_battle_result_s2c.Builder msg = MsgDungeon.dungeon_battle_result_s2c.newBuilder();
            msg.setCode(0);
            msg.setType(dungeonType);
            msg.setDungeonId(dungeonId);
            msg.setResult(result);
            humanObj.sendMsg(msg);
            return;
        }
        if (argsList == null || argsList.isEmpty()) {
            Log.activity.error("slime副本战斗结果没有数据, humanId={}, type={}, dungeonId={}, result={}", humanObj.getHumanId(), type, dungeonId, result);
            return;
        }
        for (Define.p_key_value args : argsList) {
            if (args.getK() == ParamKey.extPosType_4) {
                long hp = args.getV();
                long maxHp = controlData.getMaxHp();
                Log.activity.debug("slime副本战斗结果, hp={} ,最大血量 = {}", hp, maxHp);
                if(hp > 0){
                    hp = hp > maxHp ? maxHp : hp;
                }
                controlData.setCurrHp(hp);
                data.updateControlData();
                sendSlimeAttrChange(humanObj, controlData);
                MsgDungeon.dungeon_battle_result_s2c.Builder msg = MsgDungeon.dungeon_battle_result_s2c.newBuilder();
                msg.setCode(0);
                msg.setType(dungeonType);
                msg.setDungeonId(dungeonId);
                msg.setResult(result);
                humanObj.sendMsg(msg);
                return;
            }
        }
    }

    public void on_act_slime_sweep_c2s(HumanObject humanObj, int stageId, int count, List<Define.p_key_value> itemList, int bigLuckNum) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            Log.activity.error("slime没有活动数据, humanId={}, type={}", humanObj.getHumanId(), type);
            return;
        }

        ControlSlimeData controlData = (ControlSlimeData) data.getControlData();
        ActControlData actData = data.getActControlData();
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type, actData.getRound());
        if (confTerm == null) {
            Log.activity.error("找不到活动期数配置, type={}, round={}", type, actData.getRound());
            return;
        }

        // 获取关卡配置
        ConfSlimeDungeon_0 confSlimeDungeon = ConfSlimeDungeon_0.get(type, confTerm.group_id, stageId);
        if (confSlimeDungeon == null) {
            Log.activity.error("slime章节表没有找到活动sn:{},轮次:{},章节:{},副本:{}", actData.getActivitySn(), actData.getRound(), stageId, type);
            return;
        }

        // 扫荡条件
        if (!controlData.getPassedStage().contains(stageId)) {
            Log.activity.error("slime未通关不可以扫荡{},{}", humanObj.getHumanId(), controlData.getPassedStage());
            return;
        }

        // 检查体力是否足够
        int cost = confSlimeDungeon.cost * count;
        if (!controlData.consumeStamina(cost)) {
            Log.activity.error("slime体力不足, humanId={}, type={}", humanObj.getHumanId(), type);
            return;
        }

        // 验证所有道具是否合法
        Map<Integer, Integer> rewardMap = new HashMap<>();
        if (itemList != null && !itemList.isEmpty()) {
            for (Define.p_key_value item : itemList) {
                int itemSn = (int)item.getK();
                int itemNum = (int)item.getV() * count;
                if (!GlobalConfVal.isInSlimeEffectItemSet(itemSn)) {
                    Log.activity.error("非法的史莱姆效果道具, humanId={}, itemSn={}", humanObj.getHumanId(), itemSn);
                    continue;
                }
                rewardMap.put(itemSn, rewardMap.getOrDefault(itemSn,0) + itemNum);
            }
        }

        // 更新大吉数量
        if (bigLuckNum > 0) {
            controlData.setBigLuckCount(controlData.getBigLuckCount() + bigLuckNum * count);
            Log.activity.error("大幸运事件, add={}, count={}, humanId={}, type={}, eventCount={}", bigLuckNum * count, count, humanObj.getHumanId(), type, controlData.getBigLuckCount());
        }

        // 更新数据
        data.updateControlData();
        // 奖励道具
        ProduceManager.inst().produceAdd(humanObj, rewardMap, MoneyItemLogKey.史莱姆扫荡);

        ActivityManager.inst().addActivityProgress(humanObj, TaskConditionTypeKey.TASK_TYPE_2006, 0, cost);
        IActivityControl control = ActivityControlTypeFactory.getTypeData(confTerm.parameter);
        if(control == null || !(control instanceof ActivityControlWarToken)) {
            Log.activity.error("翻牌活动{}找不到对应的战力{}", type, confTerm.parameter);
        }else {
            ActivityControlWarToken activityControlWarToken = (ActivityControlWarToken) control;
            activityControlWarToken.addWartokenExp(humanObj, cost);
        }

        // 发送响应
        MsgAct2.act_slime_sweep_s2c.Builder msg = MsgAct2.act_slime_sweep_s2c.newBuilder();
        msg.setActType(type);
        msg.setStageId(stageId);
        msg.setCount(count);
        msg.setResult(0);
        humanObj.sendMsg(msg);

        MsgAct2.act_slime_refresh_s2c.Builder msgStamina = MsgAct2.act_slime_refresh_s2c.newBuilder();
        msgStamina.setActType(type);
        msgStamina.setSlime(controlData.getStamina());
        msgStamina.setNextRecoverTime(controlData.getNextRecoverTime());
        humanObj.sendMsg(msgStamina);
    }

    public void on_act_slime_auto_setting_c2s(HumanObject humanObject, boolean isAuto, List<Define.p_key_value> autoSwitchUpdateList, List<Define.p_key_string> autoArgUpdateList) {
        ActivityControlObjectData data = humanObject.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            Log.activity.error("slime没有活动数据, humanId={}, type={}", humanObject.getHumanId(), type);
            return;
        }
        ControlSlimeData controlData = (ControlSlimeData) data.getControlData();
        controlData.setAuto(isAuto);
        controlData.updateAutoSwitch(autoSwitchUpdateList);
        controlData.updateAutoArg(autoArgUpdateList);
        MsgAct2.act_slime_auto_setting_s2c.Builder msg = MsgAct2.act_slime_auto_setting_s2c.newBuilder();
        msg.setActType(type);
        msg.setIsAuto(isAuto);
        msg.addAllAutoSwitchUpdate(autoSwitchUpdateList);
        msg.addAllAutoArgUpdate(autoArgUpdateList);
        humanObject.sendMsg(msg);

        data.updateControlData();
    }
}
