package org.gof.demo.worldsrv.activity.calculator;

import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.activity.ActivityManager;
import org.gof.demo.worldsrv.activity.data.ActivityControlObjectData;
import org.gof.demo.worldsrv.activity.data.controldata.ControlMonopolyData;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.*;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.instance.InstanceConstants;
import org.gof.demo.worldsrv.instance.InstanceManager;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgAct2;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.ReasonResult;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;
import org.gof.demo.worldsrv.task.TaskConditionTypeKey;

import java.util.*;

public class ActivityControlMonopoly extends AbstractActivityControl {

    public static final int GRID_TYPE_1 = 1; // 起始格
    public static final int GRID_TYPE_2 = 2;
    public static final int GRID_TYPE_3 = 3;
    public static final int GRID_TYPE_4 = 4;// 转盘格
    public static final int GRID_TYPE_5 = 5;
    public static final int GRID_TYPE_6 = 6;

    public static final int EVENT_TYPE_1 = 1;//随机升级地块
    public static final int EVENT_TYPE_2 = 2;//获得兑换道具
    public static final int EVENT_TYPE_3 = 3;//下一个普通奖励格奖励翻倍
    public static final int EVENT_TYPE_4 = 4;// 随机降级地块
    public static final int EVENT_TYPE_5 = 5;// 扣除道具
    public static final int EVENT_TYPE_6 = 6;//下一次投掷只能移动1格

    private static ActivityControlMonopoly instance = new ActivityControlMonopoly(0);

    public static ActivityControlMonopoly getInstance(int type) {
        instance.type = type;
        return instance;
    }

    public ActivityControlMonopoly(int type) {
        super(type);
    }

    public void on_act_monopoly_info_c2s(HumanObject humanObj) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            return;
        }
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type,data.getActControlData().getRound());
        if(confTerm == null){
            Log.game.error("活动时间配置不存在，活动id:{},type={},round={}", data.getActControlData().getActivityType(), type, data.getActControlData().getRound());
            return;
        }

        ControlMonopolyData controlData = (ControlMonopolyData) data.getControlData();
        MsgAct2.act_monopoly_info_s2c.Builder msg = MsgAct2.act_monopoly_info_s2c.newBuilder();
        msg.setActType(type);
        msg.setCircle(controlData.circle);
        msg.setPos(controlData.pos);
        msg.setEvent(controlData.eventId);
        int boardId = confTerm.group_id;
        int boardSize = GlobalConfVal.getConfMonoPolyBoardSize(boardId);
        for (int i = 1; i <= boardSize; i++) {
            ConfMonopolyGrid_0 confMonopolyGrid = ConfMonopolyGrid_0.get(boardId,i);
            Define.p_monopoly_grid.Builder grid = Define.p_monopoly_grid.newBuilder();
            grid.setPos(i);
            grid.setType(confMonopolyGrid.grid_type);
            grid.setLevel(controlData.posLevel.get(i-1));
            msg.addGridList(grid);
        }
        for (int i = 0; i < controlData.getCircleReward.size(); i++) {
            msg.addRewardCircleList(controlData.getCircleReward.get(i));
        }
        humanObj.sendMsg(msg);
    }

    public void on_act_monopoly_dice_c2s(HumanObject humanObj, int opType, int diceNum) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            return;
        }
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type, data.getActControlData().getRound());
        if (confTerm == null) {
            Log.game.error("活动时间配置不存在，活动id:{},type={},round={}",
                    data.getActControlData().getActivityType(), type, data.getActControlData().getRound());
            return;
        }

        int boardId = confTerm.group_id;
        ControlMonopolyData controlData = (ControlMonopolyData) data.getControlData();

        // 检查消耗
        ConfMonopoly confDice = ConfMonopoly.get(boardId);
        ReasonResult result;
        if (diceNum >= 1 && diceNum <= 6) {
            result = ProduceManager.inst().checkAndCostItem(humanObj, confDice.universal_dice, confDice.cost_num3, MoneyItemLogKey.大富翁);
        } else if (diceNum == 10) {
            result = ProduceManager.inst().checkAndCostItem(humanObj, confDice.mulit_cost, confDice.cost_num2, MoneyItemLogKey.大富翁);
            if(result.success){
                ActivityManager.inst().addActivityProgress(humanObj, TaskConditionTypeKey.TASK_TYPE_6, confDice.cost_num2, confDice.mulit_cost, confDice.cost_num2);
            }
        } else {
            result = ProduceManager.inst().checkAndCostItem(humanObj, confDice.single_cost, confDice.cost_num1, MoneyItemLogKey.大富翁);
            if(result.success){
                ActivityManager.inst().addActivityProgress(humanObj, TaskConditionTypeKey.TASK_TYPE_6, confDice.cost_num1, confDice.single_cost, confDice.cost_num1);
            }
        }
        if (!result.success) {
            return;
        }

        MsgAct2.act_monopoly_dice_s2c.Builder msg = MsgAct2.act_monopoly_dice_s2c.newBuilder();
        msg.setActType(type);
        msg.setOpType(opType);

        // 处理10连抽或单次
        processRolls(humanObj, confTerm, controlData, diceNum, boardId, msg);
        data.updateControlData();
        humanObj.sendMsg(msg);
    }

    private void processRolls(HumanObject humanObj, ConfActivityTerm confTerm, ControlMonopolyData controlData, int diceNum, int boardId,
                              MsgAct2.act_monopoly_dice_s2c.Builder msg) {
        int rolls = diceNum == 10 ? 10 : 1;
        int boardSize = GlobalConfVal.getConfMonoPolyBoardSize(boardId);
        Map<Integer, Integer> totalReward = new HashMap<>();
        if(confTerm.reward != null && confTerm.reward.length > 0) {
            Utils.intArrToIntMap(totalReward, confTerm.reward, rolls);
        }

        for (int i = 0; i < rolls; i++) {
            Define.p_monopoly_dice.Builder diceBuilder = Define.p_monopoly_dice.newBuilder();
            // 获取骰子点数
            int actualDiceNum;
            ConfMonopolyRandom_0 confRandom = ConfMonopolyRandom_0.get(controlData.eventId, boardId);
            if(confRandom != null && confRandom.type == EVENT_TYPE_6) {
                actualDiceNum = 1;
                controlData.eventId = 0;
            } else if (diceNum >= 1 && diceNum <= 6) {
                actualDiceNum = diceNum;
            } else {
                actualDiceNum = Utils.random(1, 7);
            }
            diceBuilder.setDiceNum(actualDiceNum);

            // 记录所有经过的格子
            int currentPos = controlData.pos;
            int passPos = currentPos;
            for (int step = 1; step < actualDiceNum; step++) {
                ++passPos;
                if (passPos > boardSize) {
                    controlData.circle++;
                    passPos = passPos - boardSize;
                }

                // 处理经过的格子
                ConfMonopolyGrid_0 passGrid = ConfMonopolyGrid_0.get(boardId, passPos);
                int passGridType = passGrid.grid_type;

                // 处理经过奖励
                int passGridLevel = controlData.posLevel.get(passPos - 1);
                ConfMonopolyGridLevel_0 passConfLevel = ConfMonopolyGridLevel_0.get(boardId, passGridType, passGridLevel);
                if (passConfLevel != null) {
                    Define.p_monopoly_reward.Builder rewardBuilder = null;
                    if(passConfLevel.through_reward != null){
                        rewardBuilder = Define.p_monopoly_reward.newBuilder();
                        rewardBuilder.setPos(passPos);
                        rewardBuilder.addAllRewards(ProduceManager.inst().to_p_rewardList(passConfLevel.through_reward));
                        Utils.intArrToIntMap(totalReward, passConfLevel.through_reward);
                    }
                    if(passConfLevel.event > 0){
                        if (rewardBuilder == null){
                            rewardBuilder = Define.p_monopoly_reward.newBuilder();
                            rewardBuilder.setPos(passPos);
                        }
                        rewardBuilder.setEventId(passConfLevel.event);
                        ConfMonopolyRandom_0 confRandomNew = ConfMonopolyRandom_0.get(passConfLevel.event,boardId);
                        if(confRandomNew.type == EVENT_TYPE_2){
                            Utils.intArrToIntMap(totalReward, confRandomNew.desc_parm);
                            rewardBuilder.addAllRewards(ProduceManager.inst().to_p_rewardList(confRandomNew.desc_parm));
                        }else {
                            List<Integer> eventValues = new ArrayList<>();
                            handleEvent(humanObj, controlData, confRandomNew, boardId, eventValues);
                            rewardBuilder.addAllEventValue(eventValues);
                        }
                    }
                    if(rewardBuilder != null) {
                        diceBuilder.addThroughReward(rewardBuilder);
                    }
                }
            }

            // 处理最终停留的格子
            ++passPos;
            if (passPos > boardSize) {
                // 经过起始格
                passPos = passPos - boardSize;
                controlData.circle++;
            }
            controlData.pos = passPos;
            diceBuilder.setPos(passPos);
            diceBuilder.setCircle(controlData.circle);

            ConfMonopolyGrid_0 grid = ConfMonopolyGrid_0.get(boardId, passPos);
            int gridType = grid.grid_type;
            int gridLevel = controlData.posLevel.get(passPos - 1);

            // 处理停留格子奖励和事件
            ConfMonopolyGridLevel_0 confLevel = ConfMonopolyGridLevel_0.get(boardId, gridType, gridLevel);
            if (confLevel != null) {
                // 处理停留奖励
                if (confLevel.output_id > 0) {
                    int outputId = confLevel.output_id;
                    //如果是转盘反保底
                    if(confLevel.type == GRID_TYPE_4){
                        outputId = getOutputId(controlData, confLevel, outputId);
                    }
                    Map<Integer, Integer> stayReward = ProduceManager.inst().getDropMap(outputId);
                    if(confRandom != null && confRandom.type == EVENT_TYPE_3 && gridType == GRID_TYPE_6) {
                        for(Map.Entry<Integer, Integer> entry : stayReward.entrySet()){
                            int key = entry.getKey();
                            int value= entry.getValue();
                            totalReward.put(key, stayReward.getOrDefault(key, 0) + value * 2);
                            diceBuilder.addRewards(Define.p_reward.newBuilder().setGtid(key).setNum(value * 2));
                        }
                        controlData.eventId = 0;
                    }else {
                        Utils.mergeMap(totalReward, stayReward);
                        diceBuilder.addAllRewards(ProduceManager.inst().to_p_rewardList(stayReward));
                    }
                }
                if (confLevel.stay_reward != null) {
                    Utils.intArrToIntMap(totalReward, confLevel.stay_reward);
                    diceBuilder.addAllRewards(ProduceManager.inst().to_p_rewardList(confLevel.stay_reward));
                }

                // 处理随机事件
                if (confLevel.random != null && confLevel.random.length > 0) {
                    int randomIndex = Utils.randomByWeight2D(confLevel.random, 1);
                    int eventId = confLevel.random[randomIndex][0];
//                    eventId = 2;
                    if (eventId > 0) {
                        ConfMonopolyRandom_0 confRandomNew = ConfMonopolyRandom_0.get(eventId,boardId);
                        if (confRandomNew != null) {
                            // 某些事件需要下一次才生效
                            diceBuilder.setEventId(eventId);
                            if(confRandomNew.type == EVENT_TYPE_2){
                                Utils.intArrToIntMap(totalReward, confRandomNew.desc_parm);
                                diceBuilder.addAllRewards(ProduceManager.inst().to_p_rewardList(confRandomNew.desc_parm));
                            }else {
                                List<Integer> eventValues = new ArrayList<>();
                                handleEvent(humanObj, controlData, confRandomNew, boardId, eventValues);
                                diceBuilder.addAllEventValue(eventValues);
                            }
                        }
                    }
                }

                if(confLevel.event > 0){
                    Define.p_monopoly_reward.Builder throughRewardBuilder = Define.p_monopoly_reward.newBuilder();
                    throughRewardBuilder.setPos(passPos);
                    throughRewardBuilder.setEventId(confLevel.event);
                    ConfMonopolyRandom_0 confRandomNew = ConfMonopolyRandom_0.get(confLevel.event,boardId);
                    if(confRandomNew.type == EVENT_TYPE_2){
                        Utils.intArrToIntMap(totalReward, confRandomNew.desc_parm);
                        throughRewardBuilder.addAllRewards(ProduceManager.inst().to_p_rewardList(confRandomNew.desc_parm));
                    }else {
                        List<Integer> eventValues = new ArrayList<>();
                        handleEvent(humanObj, controlData, confRandomNew, boardId, eventValues);
                        throughRewardBuilder.addAllEventValue(eventValues);
                    }
                    diceBuilder.addThroughReward(throughRewardBuilder);
                }
            }
            msg.addDice(diceBuilder);
        }
        // 添加总奖励
        if (!totalReward.isEmpty()) {
            ProduceManager.inst().produceAdd(humanObj, totalReward, MoneyItemLogKey.大富翁);
        }
    }

    private int getOutputId(ControlMonopolyData controlData, ConfMonopolyGridLevel_0 confLevel, int outputId) {
        if (confLevel.turn_output != null && confLevel.turn_output.length > 0) {
            // 根据turnCount查找对应区间的output_id
            int targetOutputId = confLevel.output_id; // 默认使用配置的output_id

            for (int[] turnRange : confLevel.turn_output) {
                if (turnRange.length >= 2) {
                    if (controlData.turnCount >= turnRange[0]) {
                        targetOutputId = turnRange[1];
                    } else {
                        break; // 找到第一个大于turnCount的区间就停止
                    }
                }
            }
//            Log.activity.info("替换了转盘掉落:{}，{}", outputId, targetOutputId);
            // 使用找到的outputId替换原始的outputId
            outputId = targetOutputId;
        }
        // 增加转盘次数
        controlData.turnCount++;
//        Log.activity.info("转盘次数:{}", controlData.turnCount);
        return outputId;
    }

    private void handleEvent(HumanObject humanObj, ControlMonopolyData controlData,
                             ConfMonopolyRandom_0 confRandom, int boardId, List<Integer> eventValues) {
        if (confRandom == null || confRandom.desc_parm == null || confRandom.desc_parm.length == 0) {
            return;
        }

        switch (confRandom.type) {
            case EVENT_TYPE_1: // 随机升级地块
                randomUpgradeGrids(controlData, confRandom.desc_parm[0], boardId, eventValues);
                break;
            case EVENT_TYPE_3: // 获得兑换道具
            case EVENT_TYPE_6: // 获得兑换道具
                controlData.eventId = confRandom.id;
                break;
            case EVENT_TYPE_4: // 随机降级地块
                randomDowngradeGrids(controlData, confRandom.desc_parm[0], boardId, eventValues);
                break;
            case EVENT_TYPE_5: // 扣除道具
                if (confRandom.desc_parm.length >= 2) {
                    ProduceManager.inst().checkAndCostItem(humanObj, confRandom.desc_parm[0],
                            confRandom.desc_parm[1], MoneyItemLogKey.大富翁);
                }
                break;
        }
    }

    private void randomUpgradeGrids(ControlMonopolyData controlData, int count, int boardId, List<Integer> eventValues) {
        List<Integer> upgradablePositions = new ArrayList<>();
        for (int i = 0; i < controlData.posLevel.size(); i++) {
            ConfMonopolyGrid_0 grid = ConfMonopolyGrid_0.get(boardId, i + 1);
            if (grid.grid_type == 6) {
                int nextLevel = controlData.posLevel.get(i) + 1;
                if (ConfMonopolyGridLevel_0.get(boardId, grid.grid_type, nextLevel) != null) {
                    upgradablePositions.add(i);
                }
            }
        }
        Log.game.debug("当前的地块等级：{}，找到可以随机升级的地块:{}", controlData.posLevel, upgradablePositions);
        Collections.shuffle(upgradablePositions);
        for (int i = 0; i < Math.min(count, upgradablePositions.size()); i++) {
            int pos = upgradablePositions.get(i);
            controlData.posLevel.set(pos, controlData.posLevel.get(pos) + 1);
            eventValues.add(pos + 1);
            Log.game.debug("升级地块:{}", pos+1);
        }
    }

    private void randomDowngradeGrids(ControlMonopolyData controlData, int count, int boardId, List<Integer> eventValues) {
        List<Integer> downgradablePositions = new ArrayList<>();
        for (int i = 0; i < controlData.posLevel.size(); i++) {
            ConfMonopolyGrid_0 grid = ConfMonopolyGrid_0.get(boardId, i + 1);
            if (grid.grid_type == 6 && controlData.posLevel.get(i) > 1) {
                downgradablePositions.add(i);
            }
        }
        Log.game.debug("当前的地块等级：{}，找到可以随机降级的地块:{}", controlData.posLevel, downgradablePositions);
        Collections.shuffle(downgradablePositions);
        for (int i = 0; i < Math.min(count, downgradablePositions.size()); i++) {
            int pos = downgradablePositions.get(i);
            controlData.posLevel.set(pos, controlData.posLevel.get(pos) - 1);
            eventValues.add(pos + 1);
            Log.game.debug("降级地块:{}", pos+1);
        }
    }

    public void on_act_monopoly_circle_reward_c2s(HumanObject humanObj, int rewardId) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            return;
        }
        ControlMonopolyData controlData = (ControlMonopolyData) data.getControlData();
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type, data.getActControlData().getRound());
        if (confTerm == null) {
            Log.game.error("活动时间配置不存在，活动id:{},type={},round={}",
                    data.getActControlData().getActivityType(), type, data.getActControlData().getRound());
            return;
        }

        int boardId = confTerm.group_id;
        ConfMonopolyTurnReward_0 confCircleReward = ConfMonopolyTurnReward_0.get(boardId, rewardId);
        if (confCircleReward == null) {
            return;
        }

        if (controlData.getCircleReward.contains(rewardId)) {
            return;
        }

        ProduceManager.inst().produceAdd(humanObj, confCircleReward.reward, MoneyItemLogKey.大富翁);
        controlData.getCircleReward.add(rewardId);
        InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, InstanceConstants.showType_0, confCircleReward.reward);
        data.updateControlData();
        MsgAct2.act_monopoly_circle_reward_s2c.Builder msg = MsgAct2.act_monopoly_circle_reward_s2c.newBuilder();
        msg.setActType(type);
        msg.addAllRewardCircleList(controlData.getCircleReward);
        humanObj.sendMsg(msg);
    }
}
