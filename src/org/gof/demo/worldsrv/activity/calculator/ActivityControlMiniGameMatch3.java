package org.gof.demo.worldsrv.activity.calculator;

import org.gof.core.Port;
import org.gof.core.support.Time;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.activity.data.ActivityControlObjectData;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.ConfArcheryChapter;
import org.gof.demo.worldsrv.config.ConfGgbondChapter;
import org.gof.demo.worldsrv.msg.MsgAct;
import org.gof.demo.worldsrv.task.TaskConditionTypeKey;

public class ActivityControlMiniGameMatch3 extends AbstractActivityControl {

    public ActivityControlMiniGameMatch3(int type) {
        super(type);
    }

    public void on_act_halloween_match_3_info_c2s(HumanObject humanObj) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            return;
        }
        int day = Utils.getDaysBetween(data.getActControlData().getOpenTime() * Time.SEC, Port.getTime()) + 1;
        MsgAct.act_halloween_match_3_info_s2c.Builder msg = MsgAct.act_halloween_match_3_info_s2c.newBuilder();
        for (ConfGgbondChapter conf : ConfGgbondChapter.findAll()) {
            if (conf.open_day <= day) {
                msg.addChapterId(conf.sn);
            }
        }
        msg.setActType(type);
        humanObj.sendMsg(msg);
    }

    public void on_act_halloween_match_3_result_c2s(HumanObject humanObj, int result, int chapterId, int step) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            return;
        }
        int day = Utils.getDaysBetween(data.getActControlData().getOpenTime() * Time.SEC, Port.getTime()) + 1;
        ConfGgbondChapter conf = ConfGgbondChapter.get(chapterId);
        if (conf == null || conf.open_day > day) {
            return;
        }
        if (result == 1) {
            int progress = 0;
            for (int i = 0; i < conf.star_step.length; i++) {
                if (step <= conf.star_step[i]) {
                    progress = conf.star_step.length - i;
                    break;
                }
            }
            addActivityProgress(humanObj, TaskConditionTypeKey.TASK_TYPE_48, progress, chapterId, progress);
        }
        MsgAct.act_halloween_match_3_result_s2c.Builder msg = MsgAct.act_halloween_match_3_result_s2c.newBuilder();
        msg.setResult(result);
        msg.setChapterId(chapterId);
        msg.setStep(step);
        msg.setActType(type);
        humanObj.sendMsg(msg);
    }
}
