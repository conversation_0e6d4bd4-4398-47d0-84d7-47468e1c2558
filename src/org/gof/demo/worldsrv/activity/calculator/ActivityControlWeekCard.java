package org.gof.demo.worldsrv.activity.calculator;

import com.alibaba.fastjson.JSONObject;
import org.gof.core.Port;
import org.gof.core.support.Time;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.activity.ActivityManager;
import org.gof.demo.worldsrv.activity.ActivityVo;
import org.gof.demo.worldsrv.activity.data.ActivityControlObjectData;
import org.gof.demo.worldsrv.activity.data.controldata.ControlWeekCardData;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.ConfActivityControl;
import org.gof.demo.worldsrv.config.ConfActivityTerm;
import org.gof.demo.worldsrv.config.ConfWeeklyCard;
import org.gof.demo.worldsrv.entity.ActControlData;
import org.gof.demo.worldsrv.instance.InstanceConstants;
import org.gof.demo.worldsrv.instance.InstanceManager;
import org.gof.demo.worldsrv.mail.MailManager;
import org.gof.demo.worldsrv.mall.PayMallTypeKey;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgAct;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;
import org.gof.demo.worldsrv.support.enumKey.RewardStateKey;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ActivityControlWeekCard extends AbstractActivityControl {
    public ActivityControlWeekCard(int type) {
        super(type);
    }

    @Override
    public void onActivityEndShow(HumanObject humanObj, ConfActivityControl confActivityControl) {
        ActivityControlObjectData controlData = humanObj.operation.activityControlData.getControlObjectData(type);
        if (controlData == null) {
            return;
        }
        ControlWeekCardData data = (ControlWeekCardData) controlData.getControlData();
        if (data.isBuy) {
            ActControlData actData = controlData.getActControlData();
            ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type, actData.getRound());
            if (confTerm != null) {
                int day = Utils.getDaysBetween(actData.getOpenTime() * Time.SEC, Port.getTime()) + 1;
                Map<Integer, Integer> itemNumMap = new HashMap<>();
                for (ConfWeeklyCard conf : ConfWeeklyCard.findAll()) {
                    if (conf.type != type || conf.act_group != confTerm.group_id
                            || day < conf.day || data.rewardList.contains(conf.sn)) {
                        continue;
                    }
                    Utils.intArrToIntMap(itemNumMap, conf.reward);
                }
                if (!itemNumMap.isEmpty()) {
                    JSONObject jo = new JSONObject();
                    JSONObject joTemp = new JSONObject();
                    joTemp.put(MailManager.MAIL_K_8, confActivityControl.sn);
                    jo.put(MailManager.MAIL_PARAM_1, joTemp);
                    MailManager.inst().sendMail(humanObj.id, MailManager.SYS_SENDER, 10113, "", jo.toJSONString(), Utils.mapIntIntToJSON(itemNumMap), null);
                }
            }
        }
 		// 父类一定要最后调用
        super.onActivityEndShow(humanObj, confActivityControl);
    }

    @Override
    protected void initActivityControlData(HumanObject humanObj, ActivityControlObjectData data, ConfActivityControl confControl, ActivityVo vo) {
        super.initActivityControlData(humanObj, data, confControl, vo);
        clearPayMallBuyNum(humanObj, (confPayMall) -> confPayMall.type == PayMallTypeKey.PAY_Type_13);
    }

    /**
     * 支付完更新支付状态
     */
    @Override
    public void pay(HumanObject humanObj, int payMallSn) {
        ActivityControlObjectData controlData = humanObj.operation.activityControlData.getControlObjectData(type);
        if (controlData == null) {
            return;
        }
        ControlWeekCardData data = (ControlWeekCardData) controlData.getControlData();
        if (data.isBuy) {
            return;
        }
        data.isBuy = true;
        controlData.updateControlData();
        on_act_week_card_info_c2s(humanObj);
    }

    public void on_act_week_card_info_c2s(HumanObject humanObj) {
        ActivityControlObjectData controlData = humanObj.operation.activityControlData.getControlObjectData(type);
        if (controlData == null) {
            return;
        }
        ActControlData actData = controlData.getActControlData();
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type, actData.getRound());
        if (confTerm == null) {
            return;
        }
        int day = Utils.getDaysBetween(actData.getOpenTime() * Time.SEC, Port.getTime()) + 1;
        ControlWeekCardData data = (ControlWeekCardData) controlData.getControlData();
        MsgAct.act_week_card_info_s2c.Builder msg = MsgAct.act_week_card_info_s2c.newBuilder();
        msg.setIsBuy(data.isBuy ? 1 : 0);
        for (ConfWeeklyCard conf : ConfWeeklyCard.findAll()) {
            if (conf.type != type || conf.act_group != confTerm.group_id) {
                continue;
            }
            Define.p_key_value.Builder pkv = Define.p_key_value.newBuilder();
            pkv.setK(conf.sn);
            if (day < conf.day) {
                pkv.setV(RewardStateKey.未达成条件.getType());
            } else {
                if (data.rewardList.contains(conf.sn)) {
                    pkv.setV(RewardStateKey.已领取.getType());
                } else {
                    pkv.setV(data.isBuy ? RewardStateKey.可领取.getType() : RewardStateKey.未达成条件.getType());
                }
            }
            msg.addStatusList(pkv);
        }
        humanObj.sendMsg(msg);
    }

    public void on_act_week_card_reward_c2s(HumanObject humanObj, int sn) {
        ConfWeeklyCard conf = ConfWeeklyCard.get(sn);
        if (conf == null) {
            Log.activity.error("【超值卡】活动领奖时取不到配置数据, sn={}", sn);
            return;
        }
        ActivityControlObjectData controlData = humanObj.operation.activityControlData.getControlObjectData(type);
        if (controlData == null) {
            return;
        }
        ControlWeekCardData data = (ControlWeekCardData) controlData.getControlData();
        if (!data.isBuy) {
            return;
        }
        if (data.rewardList.contains(sn)) {
            Log.activity.error("【超值卡】活动领奖时已经领取过奖励, humanId={}, sn={}", humanObj.id, sn);
            return;
        }
        int day = Utils.getDaysBetween(controlData.getActControlData().getOpenTime() * Time.SEC, Port.getTime()) + 1;
        if (day < conf.day) {
            Log.activity.error("【超值卡】活动领奖时不在领奖时间内, humanId={}, sn={}, day={}", humanObj.id, sn, day);
            return;
        }
        data.rewardList.add(sn);
        controlData.updateControlData();
        List<Define.p_reward> rewardList = ProduceManager.inst().rewardProduceToMsg(humanObj, conf.reward, MoneyItemLogKey.超值卡);
        MsgAct.act_week_card_reward_s2c.Builder msg = MsgAct.act_week_card_reward_s2c.newBuilder();
        msg.setRewardId(sn);
        msg.addAllRewardList(rewardList);
        humanObj.sendMsg(msg);
        InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, InstanceConstants.showType_0, rewardList);
    }
}
