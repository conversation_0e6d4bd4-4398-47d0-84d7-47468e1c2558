package org.gof.demo.worldsrv.activity.calculator;

import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.activity.ActivityManager;
import org.gof.demo.worldsrv.activity.data.ActivityControlObjectData;
import org.gof.demo.worldsrv.activity.data.controldata.ControlMountDrawData;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.*;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.instance.InstanceConstants;
import org.gof.demo.worldsrv.instance.InstanceManager;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgAct;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.enumKey.ConfGlobalKey;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;

import java.util.*;
import java.util.stream.Collectors;

public class ActivityControlMountDraw extends AbstractActivityControl {
    private static ActivityControlMountDraw instance = new ActivityControlMountDraw(0);

    public static ActivityControlMountDraw getInstance(int type) {
        instance.type = type;
        return instance;
    }

    public ActivityControlMountDraw(int type) {
        super(type);
    }
    @Override
    public void sendActivityData(HumanObject humanObj) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            return;
        }
        super.sendActivityData(humanObj);
        on_act_mount_carnival_info_c2s(humanObj);
    }

    public void on_act_mount_carnival_info_c2s(HumanObject humanObj) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            return;
        }
        MsgAct.act_mount_carnival_info_s2c.Builder msg = MsgAct.act_mount_carnival_info_s2c.newBuilder();
        msg.setActType(type);
        ControlMountDrawData mountDraw = (ControlMountDrawData) data.getControlData();
        if (mountDraw == null) {
            Log.game.error("发送活动数据玩家{}的活动{}的数据为空", humanObj.id,type);
            return;
        }
        msg.setCount(mountDraw.total);
        msg.addAllCountRewardList(mountDraw.claimSnList);
        int garanteedTimes = ConfGlobal.get(ConfGlobalKey.mount_draw_garanteed_times.SN).value;
        msg.setRemainDraw(garanteedTimes - mountDraw.refreshTotal%garanteedTimes);
        for (Map.Entry<Integer, Integer> entry : mountDraw.dropRewardMap.entrySet()) {
            msg.addGetDrop(Define.p_key_value.newBuilder().setK(entry.getKey()).setV(entry.getValue()));
        }
        if (mountDraw.chooseMap.isEmpty()) {
            for (int replaceSn : mountDraw.replaceRewardMap.keySet()){
                ConfMountDraw confMountDraw = ConfMountDraw.get(replaceSn);
                if(confMountDraw == null){
                    continue;
                }
                Define.p_key_value.Builder p_guaranteed = Define.p_key_value.newBuilder();
                p_guaranteed.setK(confMountDraw.sn);
                p_guaranteed.setV(confMountDraw.guaranteed - mountDraw.guaranteeMap.getOrDefault(confMountDraw.sn,0));
                msg.addGuaranteedList(p_guaranteed);
            }
        }
        for (Map.Entry<Integer, List<Long>> entry : mountDraw.chooseMap.entrySet()) {
            Define.p_key_value_list.Builder chooseMsg = Define.p_key_value_list.newBuilder();
            chooseMsg.setK(entry.getKey());
            chooseMsg.addAllList(entry.getValue());
            msg.addChooseList(chooseMsg);
            ConfMountDraw confMountDraw = ConfMountDraw.get(entry.getKey());
            if(confMountDraw == null){
                continue;
            }
            Define.p_key_value.Builder p_guaranteed = Define.p_key_value.newBuilder();
            p_guaranteed.setK(confMountDraw.sn);
            p_guaranteed.setV(confMountDraw.guaranteed - mountDraw.guaranteeMap.getOrDefault(confMountDraw.sn,0));
            msg.addGuaranteedList(p_guaranteed);
        }
        for (Map.Entry<Integer, Integer> entry : mountDraw.guaranteeMap.entrySet()) {
            ConfMountDraw confMountDraw = ConfMountDraw.get(entry.getKey());
            if(confMountDraw == null){
                continue;
            }
            boolean isExist = false;
            for (int i = 0; i < msg.getGuaranteedListCount(); i++) {
                if (msg.getGuaranteedList(i).getK() == confMountDraw.sn) {
                    isExist = true;
                }
            }
            if (isExist) {
                continue;
            }
            Define.p_key_value.Builder p_guaranteed = Define.p_key_value.newBuilder();
            p_guaranteed.setK(confMountDraw.sn);
            p_guaranteed.setV(confMountDraw.guaranteed - entry.getValue());
            msg.addGuaranteedList(p_guaranteed);
        }
        humanObj.sendMsg(msg);
    }

    public void mount_carnival_draw(HumanObject humanObj, int drawType) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            return;
        }
        int drawCount = drawType == 1 ? 1 : 10;
        ConfActivityTerm term = ActivityManager.inst().getActivityTerm(type, data.getActControlData().getRound());
        if(term == null) {
            Log.game.error("活动{}的轮次{}不存在", type, data.getActControlData().getRound());
            return;
        }
        if(term.cost == null || term.cost.length < 2){
            Log.game.error("活动{}的轮次{}的消耗不存在", type, data.getActControlData().getRound());
            return;
        }
        if (!ProduceManager.inst().checkAndCostItem(humanObj, term.cost[0],term.cost[1]*drawCount, MoneyItemLogKey.转盘活动).success) {
            return;
        }
        ControlMountDrawData mountDraw = (ControlMountDrawData) data.getControlData();
        int groupId = term.group_id;
        List<ConfMountDraw> confMountDraws = GlobalConfVal.getConfMountDraws(type, groupId);

        Map<Integer,Integer> rewardMap = new HashMap<>();
        MsgAct.act_mount_carnival_draw_s2c.Builder msg = MsgAct.act_mount_carnival_draw_s2c.newBuilder();
        msg.setActType(type);
        msg.setType(drawType);

        for (int i = 0; i < drawCount; ++i) {
            ConfMountDraw selectedDraw = selectDraw(confMountDraws, mountDraw.guaranteeMap, mountDraw.dropRewardMap, mountDraw, groupId);
            if(selectedDraw == null) {
                Log.game.error("玩家{}的抽卡活动{}的数据为空", humanObj.id, type);
                continue;
            }
            //统计
            mountDraw.total += 1;
            for (ConfMountDraw confMountDraw : confMountDraws) {
                if (confMountDraw.limited > 0 || confMountDraw.guaranteed > 0) {
                    mountDraw.guaranteeMap.put(confMountDraw.sn, mountDraw.guaranteeMap.getOrDefault(confMountDraw.sn, 0) + 1);
                }
            }
            if(selectedDraw.num > 0) {
                mountDraw.dropRewardMap.put(selectedDraw.sn, mountDraw.dropRewardMap.getOrDefault(selectedDraw.sn, 0) + 1);
            }

            if(selectedDraw.is_jackpot == 1){
                //头奖
                mountDraw.refreshTotal = 0;
                mountDraw.dropRewardMap.clear();
                Iterator<Map.Entry<Integer, Integer>> it = mountDraw.guaranteeMap.entrySet().iterator();
                while (it.hasNext()) {
                    Map.Entry<Integer, Integer> entry = it.next();
                    ConfMountDraw confMountDraw = ConfMountDraw.get(entry.getKey());
                    if(confMountDraw.is_jackpot > 0){
                        it.remove();
                    }
                }
            }else if(selectedDraw.is_jackpot == 2){
                //二奖
                mountDraw.refreshTotal = (mountDraw.refreshTotal-1)/100+100;//弄成满100的倍数
                Iterator<Map.Entry<Integer, Integer>> it = mountDraw.guaranteeMap.entrySet().iterator();
                while (it.hasNext()) {
                    Map.Entry<Integer, Integer> entry = it.next();
                    if(selectedDraw.sn == entry.getKey()){
                        it.remove();
                    }
                }
            }else {
                //独立保底刷新
                mountDraw.refreshTotal += 1;
                if(selectedDraw.guaranteed > 0 && selectedDraw.num == 0){
                    mountDraw.guaranteeMap.remove(selectedDraw.sn);
                    if(mountDraw.refreshTotal % 100 == 0){
                        //抽到了不是头奖的大奖
                        mountDraw.refreshTotal -= 1;
                    }
                }
            }

            //额外奖励
            int[][] extraReward = term.reward;
            if (extraReward != null&& extraReward.length > 0) {
                Utils.intArrToIntMap(rewardMap, extraReward);
            }
            //奖励
            List<Long> chooseList = mountDraw.chooseMap.get(selectedDraw.sn);
            if(chooseList == null || chooseList.size() == 0){
                rewardMap.put(selectedDraw.reward[0], rewardMap.getOrDefault(selectedDraw.reward[0], 0) + selectedDraw.reward[1]);
                msg.addRewardList(Define.p_reward.newBuilder().setGtid(selectedDraw.reward[0]).setNum(selectedDraw.reward[1]));
            }else {
                rewardMap.put((int)chooseList.get(1).longValue(), (int)chooseList.get(2).longValue());
                msg.addRewardList(Define.p_reward.newBuilder().setGtid((int)chooseList.get(1).longValue()).setNum((int)chooseList.get(2).longValue()));
            }

            if (selectedDraw.replace_unlock > 0) {
                int newReplaceRewardNum = mountDraw.replaceRewardMap.getOrDefault(selectedDraw.sn, 0)+1;
                mountDraw.replaceRewardMap.put(selectedDraw.sn, newReplaceRewardNum);
                if (newReplaceRewardNum >= selectedDraw.replace_unlock && selectedDraw.reward_replace.length > 0) {
                    int rewardSn = selectedDraw.reward[0];
                    int index = 0;
                    for (int j = 0; j < selectedDraw.reward_replace.length; j++) {
                        int[] reward = selectedDraw.reward_replace[j];
                        if (reward[1] == rewardSn) {
                            index = j;
                            break;
                        }
                    }
                    int[] reward = selectedDraw.reward_replace[index];
                    mountDraw.chooseMap.put(selectedDraw.sn, Arrays.asList((long) reward[0], (long) reward[1], (long) reward[2]));
                }
            }
            msg.addDropIdList(selectedDraw.sn);
        }
        ProduceManager.inst().produceAdd(humanObj, rewardMap, MoneyItemLogKey.转盘活动);
        data.updateControlData();
        humanObj.sendMsg(msg);
        sendActivityData(humanObj);
    }

    public ConfMountDraw selectDraw(List<ConfMountDraw> confMountDraws, Map<Integer, Integer> guaranteeMap, Map<Integer, Integer> dropRewardMap, ControlMountDrawData mountDraw, int groupId) {
        List<ConfMountDraw> validDraws = confMountDraws.stream()
                .filter(confDraw -> guaranteeMap.getOrDefault(confDraw.sn, 0) >= confDraw.limited)
                .filter(confDraw -> dropRewardMap.getOrDefault(confDraw.sn, 0) < confDraw.num || confDraw.num == 0)
                .collect(Collectors.toList());

        List<ConfMountDraw> guaranteedDraws = confMountDraws.stream()
                .filter(confDraw -> confDraw.guaranteed > 0 && guaranteeMap.getOrDefault(confDraw.sn, 0) >= confDraw.guaranteed - 1)
                .filter(confDraw -> confDraw.num == 0 || dropRewardMap.getOrDefault(confDraw.sn, 0) < confDraw.num)
                .collect(Collectors.toList());

        ConfMountDraw selectedDraw = null;
        if (guaranteedDraws.size() > 0) {
            selectedDraw = guaranteedDraws.get(0);
        } else if ((mountDraw.refreshTotal+1) % 100 == 0) {
            int num = dropRewardMap.values().stream().mapToInt(Integer::intValue).sum() + 1;
            ConfMountDrawGuaranteed confMountDrawGuaranteed = GlobalConfVal.getConfMountDrawGuaranteed(type, groupId, num);
            if(confMountDrawGuaranteed == null){
                Log.temp.error("活动{}的轮次{}的奖励{}不存在", type, groupId, num);
                return null;
            }
            int[][] filteredRewards = Arrays.stream(confMountDrawGuaranteed.reward)
                    .filter(confDraw -> dropRewardMap.getOrDefault(confDraw[0], 0) < ConfMountDraw.get(confDraw[0]).num || ConfMountDraw.get(confDraw[0]).num == 0)
                    .toArray(int[][]::new);
            int index = Utils.randomByWeight2D(filteredRewards, 1);
            selectedDraw = ConfMountDraw.get(filteredRewards[index][0]);
        } else {
            int index = Utils.randomByWeight(validDraws.stream().mapToInt(confDraw -> confDraw.weight).toArray());
            selectedDraw = validDraws.get(index);
        }

        return selectedDraw;
    }

    private String[] getConfGlobalItem(ConfGlobalKey key) {
        String[] drawExpend = ConfGlobal.get(key).strValue.split("\\|");
        for (String str : drawExpend) {
            String[] drawExpendArr = str.split("\\,");
            if (drawExpendArr.length != 2) {
                Log.game.error("ConfGlobal表中的数据mount_draw_expend中的数据strValue格式错误");
                return null;
            }
            if (Integer.parseInt(drawExpendArr[0]) == type) {
                String[] itemArr = drawExpendArr[1].split("_");
                if (itemArr.length != 2) {
                    Log.game.error("ConfGlobal表中的数据mount_draw_expend中的数据strValue格式错误");
                    return null;
                }
                return itemArr;
            }
        }
        return null;
    }

    public void mount_carnival_count_reward(HumanObject humanObj, int rewardId) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            return;
        }
        ControlMountDrawData mountDraw = (ControlMountDrawData)data.getControlData();
        if (mountDraw == null) {
            Log.game.error("发送活动数据玩家{}的活动{}的数据为空", humanObj.id, 0);
            return;
        }
        if (mountDraw.claimSnList.contains(rewardId)) {
            return;
        }
        ConfActivityTerm confActivityTerm = ActivityManager.inst().getActivityTerm(type, data.getActControlData().getRound());
        if (confActivityTerm == null) {
            Log.game.error("活动{}的轮次{}不存在", type, data.getActControlData().getRound());
            return;
        }
        int groupId = confActivityTerm.group_id;

        List<ConfMountDrawCumulativeTimes> confMountDrawCumulativeTimes = GlobalConfVal.getConfMountDrawCumulativeTimes(type, groupId);
        if (confMountDrawCumulativeTimes == null) {
            Log.game.error("活动{}的轮次{}不存在", type, data.getActControlData().getRound());
            return;
        }
        if (!confMountDrawCumulativeTimes.stream().anyMatch(item -> item.sn == rewardId)) {
            Log.game.error("活动{}的轮次{}的奖励{}不存在", type, data.getActControlData().getRound(), rewardId);
            return;
        }
        ConfMountDrawCumulativeTimes confMountDrawCumulativeTime = ConfMountDrawCumulativeTimes.get(rewardId);
        if (mountDraw.total < confMountDrawCumulativeTime.cumulative_times) {
            return;
        }
        mountDraw.claimSnList.add(rewardId);
        data.updateControlData();
        ProduceManager.inst().produceAdd(humanObj, confMountDrawCumulativeTime.reward, MoneyItemLogKey.转盘活动);
        InstanceManager.inst().sendMsg_goods_show_s2c(humanObj,InstanceConstants.showType_0,confMountDrawCumulativeTime.reward);
        MsgAct.act_mount_carnival_count_reward_s2c.Builder msg = MsgAct.act_mount_carnival_count_reward_s2c.newBuilder();
        msg.setId(rewardId);
        msg.setActType(type);
        msg.addRewardList(Define.p_reward.newBuilder().setGtid(confMountDrawCumulativeTime.reward[0]).setNum(confMountDrawCumulativeTime.reward[1]));
        humanObj.sendMsg(msg);
    }

    public void on_act_mount_carnival_choose_c2s(HumanObject humanObj, int dropId, int choose) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            return;
        }
        ControlMountDrawData mountDraw = (ControlMountDrawData)data.getControlData();
        if (mountDraw == null) {
            Log.game.error("发送活动数据玩家{}的活动{}的数据为空", humanObj.id, 0);
            return;
        }
        ConfMountDraw confMountDraw = ConfMountDraw.get(dropId);
        if (confMountDraw == null || confMountDraw.reward_replace==null || confMountDraw.reward_replace.length == 0) {
            Log.game.error("活动{}的轮次{}的奖励{}不存在", type, data.getActControlData().getRound(), dropId);
            return;
        }
        if(mountDraw.replaceRewardMap.getOrDefault(dropId, 0) < confMountDraw.replace_unlock) {
            return;
        }
        ConfActivityTerm confActivityTerm = ActivityManager.inst().getActivityTerm(type, data.getActControlData().getRound());
        if (confActivityTerm == null) {
            Log.game.error("活动{}的轮次{}不存在", type, data.getActControlData().getRound());
            return;
        }
        int groupId = confActivityTerm.group_id;
        List<ConfMountDraw> confMountDraws = GlobalConfVal.getConfMountDraws(type, groupId);
        if (!confMountDraws.stream().anyMatch(item -> item.sn == dropId)) {
            Log.game.error("活动{}的轮次{}的奖励{}不存在", type, data.getActControlData().getRound(), dropId);
            return;
        }
        List<Long> chooseList = mountDraw.chooseMap.get(dropId);
        if(chooseList != null && chooseList.get(0) == (long) choose) {
            return;
        }
        for (int[] replace : confMountDraw.reward_replace) {
            if (replace[0] == choose) {
                mountDraw.chooseMap.put(dropId, Arrays.asList((long) choose, (long) replace[1], (long) replace[2]));
                break;
            }
        }
        data.updateControlData();
        on_act_mount_carnival_info_c2s(humanObj);
    }
}
