package org.gof.demo.worldsrv.activity.calculator;

import org.gof.core.support.Utils;
import org.gof.demo.distr.cross.CrossManager;
import org.gof.demo.distr.cross.domain.CrossPoint;
import org.gof.demo.distr.cross.domain.CrossType;
import org.gof.demo.worldsrv.activity.ActivityManager;
import org.gof.demo.worldsrv.activity.data.ActivityControlObjectData;
import org.gof.demo.worldsrv.activity.data.controldata.ControlBlackMarketData;
import org.gof.demo.worldsrv.blackMarket.BlackMarketCrossServiceProxy;
import org.gof.demo.worldsrv.blackMarket.BlackMarketVO;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.ConfBlackMarket;
import org.gof.demo.worldsrv.config.ConfMall;
import org.gof.demo.worldsrv.entity.ActControlData;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.human.HumanSettingConstants;
import org.gof.demo.worldsrv.inform.ErrorTip;
import org.gof.demo.worldsrv.inform.Inform;
import org.gof.demo.worldsrv.instance.InstanceConstants;
import org.gof.demo.worldsrv.instance.InstanceManager;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgAct;
import org.gof.demo.worldsrv.msg.MsgRank;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.rank.RankCrossServiceProxy;
import org.gof.demo.worldsrv.rank.RankInfo;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.ReasonResult;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;

import java.util.List;
import java.util.Map;

public class ActivityControlBlackMarket extends AbstractActivityControl {
    public ActivityControlBlackMarket(int type) {
        super(type);
    }

    @Override
    public void dailyResetActivityData(HumanObject humanObj) {
        super.dailyResetActivityData(humanObj);
        ActivityControlObjectData controlData = humanObj.operation.activityControlData.getControlObjectData(type);
        if (controlData == null) {
            return;
        }
        ControlBlackMarketData data = (ControlBlackMarketData)controlData.getControlData();
        data.refreshHumanMall();
        controlData.updateControlData();
    }

    @Override
    public void on_act_info_c2s(HumanObject humanObj) {
        ActivityControlObjectData controlData = humanObj.operation.activityControlData.getControlObjectData(type);
        if (controlData == null) {
            return;
        }
        ActControlData actData = controlData.getActControlData();
        ControlBlackMarketData data = (ControlBlackMarketData)controlData.getControlData();
        int serverId = humanObj.getHuman().getServerId();
        int groupIndex = GlobalConfVal.getCrossServerIdGroup(CrossType.cross_black_market.getType(), serverId);
        if (groupIndex == 0) {
            Log.rank.error("取不到跨服分组, type={}, serverId={}", CrossType.cross_black_market.getType(), serverId);
            return;
        }
        CrossManager.getInstance().callCrossFunc(CrossType.cross_black_market, serverId, handler ->  {
            if (handler.failed()) {
                Log.rank.error("查询跨服节点报错, serverId={}", serverId, handler.cause());
                return;
            }
            CrossPoint point = handler.result();
            BlackMarketCrossServiceProxy prx = BlackMarketCrossServiceProxy.newInstance(point.getNodeId());
            prx.getBlackMarketInfo(serverId, humanObj.id);
            prx.listenResult((result, context) -> {
                ReasonResult rr = result.get("result");
                if (!rr.success) {
                    Inform.user(humanObj.getHuman().getId(), Inform.提示操作, rr);
                    return;
                }
                MsgAct.act_black_market_info_s2c.Builder msg = MsgAct.act_black_market_info_s2c.newBuilder();
                Map<Integer, BlackMarketVO> voList = result.get("voMap");
                for (ConfBlackMarket conf : ConfBlackMarket.findAll()) {
                    Define.p_black_market_item.Builder dItem = Define.p_black_market_item.newBuilder();
                    dItem.setSn(conf.sn);
                    if (conf.mall_tab == 1) {
                        // 跨服商店
                        BlackMarketVO vo = voList.get(conf.sn);
                        dItem.setGoodsIndex(vo.goodsIndex);
                        dItem.setBuyNum(vo.buyNum);
                        dItem.setOpenPreTime(vo.openPreTime);
                        dItem.setPreNum(vo.preNum);
                    } else if (conf.mall_tab == 3) {
                        // 个人商店
                        dItem.setGoodsIndex(data.mallIndexMap.getOrDefault(conf.sn, 0));
                        dItem.setBuyNum(data.mallNumMap.getOrDefault(conf.sn, 0));
                    }
                    msg.addItemList(dItem);
                }
                msg.setActType(type);
                msg.setNextRefreshTime(ActivityManager.inst().getNextFreshTime(actData.getActivitySn()));
                msg.setNextOpenPreTime(ActivityManager.inst().getNextOpenPreTime(actData.getActivitySn()));
                humanObj.sendMsg(msg.build());
            });
        });
    }

    public void on_act_black_market_buy_c2s(HumanObject humanObj, int sn, int num) {
        ActivityControlObjectData controlData = humanObj.operation.activityControlData.getControlObjectData(type);
        if (controlData == null) {
            return;
        }
        ConfBlackMarket conf = ConfBlackMarket.get(sn);
        if (conf == null) {
            Log.activity.error("[黑市]没有配置跨服商店, sn={}", sn);
            return;
        }
        if (conf.mall_tab == 1) {
            // 跨服商店
            int serverId = humanObj.getHuman().getServerId();
            int group = GlobalConfVal.getCrossServerIdGroup(CrossType.cross_black_market.getType(), serverId);
            if (group == 0) {
                Log.activity.error("[黑市]取不到跨服分组, type={}, serverId={}", CrossType.cross_black_market.getType(), serverId);
                return;
            }
            CrossManager.getInstance().callCrossFunc(CrossType.cross_black_market, serverId, handler ->  {
                if (handler.failed()) {
                    Log.activity.error("[黑市]查询跨服节点报错, serverId={}", serverId, handler.cause());
                    return;
                }
                CrossPoint point = handler.result();
                BlackMarketCrossServiceProxy prx = BlackMarketCrossServiceProxy.newInstance(point.getNodeId());
                prx.getItemInfo(serverId, humanObj.id, sn);
                prx.listenResult((result, context) -> {
                    ReasonResult rr = result.get("result");
                    if (!rr.success) {
                        Inform.user(humanObj.id, Inform.提示操作, rr);
                        return;
                    }
                    BlackMarketVO vo = result.get("vo");
                    int mallSn = conf.goods[vo.goodsIndex][0];
                    ConfMall confMall = ConfMall.get(mallSn);
                    if (confMall == null) {
                        Log.activity.error("[黑市]没有配置商店, sn={}", mallSn);
                        return;
                    }
                    ReasonResult rr1 = ProduceManager.inst().canCostIntArr(humanObj, confMall.price, num);
                    if (!rr1.success) {
                        Inform.sendMsg_error(humanObj, ErrorTip.DiamondNotEnough);
                        return;
                    }
                    Map<Integer, Long> settingMap = Utils.jsonToMapIntLong(humanObj.getHumanExtInfo().getSettingMap());
                    boolean isHide = settingMap.getOrDefault(HumanSettingConstants.BroadCastHideName, 0L) == 1L;
                    prx.buyItem(serverId, humanObj.id, sn, num, humanObj.name, isHide);
                    prx.listenResult((result1, context1) -> {
                        ReasonResult rr2 = result1.get("result");
                        if (!rr2.success) {
                            Inform.user(humanObj.id, Inform.提示操作, rr);
                            return;
                        }
                        ProduceManager.inst().costItem(humanObj, confMall.price[0], (long) confMall.price[1] * num, MoneyItemLogKey.黑市);
                        if (conf.cell_type == 1) {
                            // 限购的话，是直接给道具奖励的
                            ProduceManager.inst().produceAdd(humanObj, confMall.goods[0], confMall.goods[1] * num, MoneyItemLogKey.黑市);
                            InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, InstanceConstants.showType_0, confMall.goods[0], confMall.goods[1] * num);
                        }
                        MsgAct.act_black_market_update_s2c.Builder msg = MsgAct.act_black_market_update_s2c.newBuilder();
                        msg.setActType(type);
                        List<BlackMarketVO> voList = result1.get("voList");
                        for (BlackMarketVO vo1 : voList) {
                            Define.p_black_market_item.Builder dItem = Define.p_black_market_item.newBuilder();
                            dItem.setSn(vo1.sn);
                            dItem.setGoodsIndex(vo1.goodsIndex);
                            dItem.setBuyNum(vo1.buyNum);
                            dItem.setOpenPreTime(vo1.openPreTime);
                            dItem.setPreNum(vo1.preNum);
                            msg.addItemList(dItem);
                        }
                        humanObj.sendMsg(msg.build());
                    });
                });
            });
        } else if (conf.mall_tab == 3) {
            // 个人商店
            ControlBlackMarketData data = (ControlBlackMarketData)controlData.getControlData();
            int buyNum = data.mallNumMap.getOrDefault(sn, 0);
            int goodsIndex = data.mallIndexMap.getOrDefault(sn, 0);
            int mallSn = conf.goods[goodsIndex][0];
            ConfMall confMall = ConfMall.get(mallSn);
            if (buyNum + num > confMall.limit) {
                Inform.sendMsg_error(humanObj, ErrorTip.CountNotEnough);
                return;
            }
            if (confMall.price != null && confMall.price.length != 0) {
                ReasonResult rr = ProduceManager.inst().checkAndCostItem(humanObj, confMall.price[0], confMall.price[1] * num, MoneyItemLogKey.黑市);
                if (!rr.success) {
                    Inform.sendMsg_error(humanObj, ErrorTip.DiamondNotEnough);
                    return;
                }
            }
            ProduceManager.inst().produceAdd(humanObj, confMall.goods[0], confMall.goods[1] * num, MoneyItemLogKey.黑市);
            InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, InstanceConstants.showType_0, confMall.goods[0], confMall.goods[1] * num);
            data.mallNumMap.put(sn, buyNum + num);
            controlData.updateControlData();
            Define.p_black_market_item.Builder dItem = Define.p_black_market_item.newBuilder();
            dItem.setSn(conf.sn);
            dItem.setGoodsIndex(goodsIndex);
            dItem.setBuyNum(buyNum + num);
            dItem.setOpenPreTime(0L);
            dItem.setPreNum(0);
            MsgAct.act_black_market_update_s2c.Builder msg = MsgAct.act_black_market_update_s2c.newBuilder();
            msg.setActType(type);
            msg.addItemList(dItem);
            humanObj.sendMsg(msg.build());
        }
    }
}
