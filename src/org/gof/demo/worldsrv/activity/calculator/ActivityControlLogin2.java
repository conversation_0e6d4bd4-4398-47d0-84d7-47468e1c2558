package org.gof.demo.worldsrv.activity.calculator;

import org.gof.core.Port;
import org.gof.core.support.Time;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.activity.ActivityManager;
import org.gof.demo.worldsrv.activity.EActivityType;
import org.gof.demo.worldsrv.activity.data.ActivityControlObjectData;
import org.gof.demo.worldsrv.activity.data.controldata.ControlLoginData;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.ConfActLogin;
import org.gof.demo.worldsrv.config.ConfActivityTerm;
import org.gof.demo.worldsrv.entity.ActControlData;
import org.gof.demo.worldsrv.instance.InstanceConstants;
import org.gof.demo.worldsrv.instance.InstanceManager;
import org.gof.demo.worldsrv.mall.MallManager;
import org.gof.demo.worldsrv.msg.MsgAct;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;

import java.util.*;

public class ActivityControlLogin2 extends AbstractActivityControl {
    public ActivityControlLogin2(int type) {
        super(type);
    }

    public void dailyResetActivityData(HumanObject humanObj) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            return;
        }
        ActControlData actControlData = data.getActControlData();
        if (actControlData == null) {
            return;
        }
        if (actControlData.getState() == EActivityType.STATE_ENDSHOW) {
            return;
        }
        ControlLoginData controlData = (ControlLoginData) data.getControlData();
        if (controlData == null) {
            return;
        }
        long now = Port.getTime();
        MsgAct.act_login_info_s2c.Builder msg = MsgAct.act_login_info_s2c.newBuilder();
        msg.setDay(Utils.getDaysBetween(actControlData.getOpenTime() * Time.SEC, now) + 1);
        msg.setActType(type);
        msg.addAllGetDay(controlData.getDay);
        msg.addAllGetHighDay(controlData.hightRewardDayList);
        humanObj.sendMsg(msg);
        super.dailyResetActivityData(humanObj);
    }

    public void onLoginInfo(HumanObject humanObj) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            return;
        }
        ControlLoginData controlData = (ControlLoginData) data.getControlData();
        if (controlData == null) {
            return;
        }
        MsgAct.act_login_info_s2c.Builder msg = MsgAct.act_login_info_s2c.newBuilder();
        msg.setDay(Utils.getDaysBetween(data.getActControlData().getOpenTime() * Time.SEC, Port.getTime()) + 1);
        msg.setActType(type);
        msg.addAllGetDay(controlData.getDay);
        msg.addAllGetHighDay(controlData.hightRewardDayList);
        humanObj.sendMsg(msg);
    }

    public void onLoginReward(HumanObject humanObj, int sn, int gear) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            return;
        }
        ControlLoginData controlData = (ControlLoginData) data.getControlData();
        if (controlData == null) {
            return;
        }
        ConfActLogin conf = ConfActLogin.get(sn);
        if (conf == null) {
            Log.temp.error("===ConfActLogin not found={}===", sn);
            return;
        }
        int day = Utils.getDaysBetween(data.getActControlData().getOpenTime() * Time.SEC, Port.getTime()) + 1;
        if (conf.day > day) {
            return;
        }
        ActControlData actData = data.getActControlData();
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type, actData.getRound());
        if (confTerm == null) {
            Log.temp.error("[签到活动]找不到活动期数表, type={}, round={}", type, actData.getRound());
            return;
        }
        List<int[]> rewardList = new ArrayList<>();
        if (gear == 1) {
            if (controlData.hightRewardDayList.contains(conf.sn)) {
                return;
            }
            if (!MallManager.inst().isBuyPayMall(humanObj, confTerm.parameter)) {
                return;
            }
            controlData.hightRewardDayList.add(conf.sn);
            rewardList.addAll(Arrays.asList(conf.pay_reward));
        } else {
            if (controlData.getDay.contains(conf.sn)) {
                return;
            }
            controlData.getDay.add(conf.sn);
            rewardList.addAll(Arrays.asList(conf.reward));

            if (confTerm.parameter != 0 && conf.pay_reward != null) {
                if (MallManager.inst().isBuyPayMall(humanObj, confTerm.parameter) && !controlData.hightRewardDayList.contains(conf.sn)) {
                    controlData.hightRewardDayList.add(conf.sn);
                    rewardList.addAll(Arrays.asList(conf.pay_reward));
                }
            }
        }
        data.updateControlData();
        int[][] rewards = rewardList.toArray(new int[0][]);
        ProduceManager.inst().produceAdd(humanObj, rewards, MoneyItemLogKey.登录活动);
        InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, InstanceConstants.showType_0, rewards);

        MsgAct.act_login_reward_s2c.Builder msg = MsgAct.act_login_reward_s2c.newBuilder();
        msg.setActType(type);
        msg.addAllGetDay(controlData.getDay);
        msg.addAllGetHighDay(controlData.hightRewardDayList);
        humanObj.sendMsg(msg);
    }
}
