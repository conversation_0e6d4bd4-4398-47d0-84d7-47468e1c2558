package org.gof.demo.worldsrv.activity.calculator;

import org.gof.core.Port;
import org.gof.core.support.Time;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.activity.ActivityManager;
import org.gof.demo.worldsrv.activity.data.ActivityControlObjectData;
import org.gof.demo.worldsrv.activity.data.controldata.ControlDayPayData;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.ConfActivityTerm;
import org.gof.demo.worldsrv.config.ConfPayMall;
import org.gof.demo.worldsrv.instance.InstanceConstants;
import org.gof.demo.worldsrv.instance.InstanceManager;
import org.gof.demo.worldsrv.mall.MallManager;
import org.gof.demo.worldsrv.mall.PayMallTypeKey;
import org.gof.demo.worldsrv.msg.MsgAct;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;

public class ActivityControlDayPay extends AbstractActivityControl{
    private static ActivityControlDayPay instance = new ActivityControlDayPay(0);

    public static ActivityControlDayPay getInstance(int type) {
        instance.type = type;
        return instance;
    }

    @Override
    public void sendActivityData(HumanObject humanObj) {
        MallManager.inst().sendMsg_pay_mall_info_s2c(humanObj, PayMallTypeKey.PAY_Type_29);
        super.sendActivityData(humanObj);
        onPayDayInfo(humanObj);
    }

    public void onPayDayInfo(HumanObject humanObj){
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            return;
        }
        MsgAct.act_day_pay_info_s2c.Builder msg = MsgAct.act_day_pay_info_s2c.newBuilder();
        msg.setActType(type);
        ControlDayPayData dayPayData = (ControlDayPayData) data.getControlData();
        if (dayPayData == null) {
            Log.game.error("发送活动数据玩家{}的活动{}的数据为空", humanObj.id,type);
            return;
        }
        msg.setPayDayNum(dayPayData.payDayNum);
        msg.setReward(dayPayData.rewardIndex);
        msg.setChooseIndex(dayPayData.chooseIndex);
        humanObj.sendMsg(msg);
    }

    @Override
    public void dailyResetActivityData(HumanObject humanObj) {
        onPayDayInfo(humanObj);
    }

    @Override
    public void pay(HumanObject humanObj, int payMallSn) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            return;
        }
        ControlDayPayData dayPayData = (ControlDayPayData) data.getControlData();
        if (Utils.isToday(dayPayData.lastPayTime* Time.SEC)){
            return;
        }
        dayPayData.payDayNum++;
        dayPayData.lastPayTime = Utils.getTimeSec();
        data.updateControlData();

        MsgAct.act_day_pay_info_s2c.Builder msg = MsgAct.act_day_pay_info_s2c.newBuilder();
        msg.setActType(type);
        msg.setPayDayNum(dayPayData.payDayNum);
        msg.setReward(dayPayData.rewardIndex);
        msg.setChooseIndex(dayPayData.chooseIndex);
        humanObj.sendMsg(msg);
    }

    public void getReward(HumanObject humanObj, int index) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            return;
        }
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type,data.getActControlData().getRound());
        if(confTerm == null){
            Log.game.error("活动时间配置不存在，活动id:{},type={},round={}", data.getActControlData().getActivityType(), type, data.getActControlData().getRound());
            return;
        }
        ControlDayPayData dayPayData = (ControlDayPayData) data.getControlData();
        if (dayPayData == null) {
            Log.game.error("发送活动数据玩家{}的活动{}的数据为空", humanObj.id,type);
            return;
        }
        if (dayPayData.rewardIndex > 0) {
            Log.game.error("连冲活动玩家已经领取过奖励了humanId={},actType={}", humanObj.id,type);
            return;
        }
        if (dayPayData.payDayNum < confTerm.parameter) {
            Log.game.error("连冲活动玩家连冲天数未达到humanId={},actType={},payDayNum={}", humanObj.id,type,dayPayData.payDayNum);
            return;
        }
        //客户端发来的索引是1开始的
        index -= 1;
        if(confTerm.reward.length <= index){
            Log.game.error("连冲活动奖励配置不存在humanId={},actType={},index={}", humanObj.id,type,index);
            return;
        }
        dayPayData.rewardIndex = index + 1;
        data.updateControlData();

        ProduceManager.inst().produceAdd(humanObj, confTerm.reward[index], MoneyItemLogKey.充值礼包);
        InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, InstanceConstants.showType_0, confTerm.reward[index]);

        MsgAct.act_day_pay_info_s2c.Builder msg = MsgAct.act_day_pay_info_s2c.newBuilder();
        msg.setActType(type);
        msg.setPayDayNum(dayPayData.payDayNum);
        msg.setReward(dayPayData.rewardIndex);
        humanObj.sendMsg(msg);
    }

    public ActivityControlDayPay(int type) {
        super(type);
    }

    public void on_day_pay_choose_reward(HumanObject humanObj, int index) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            return;
        }
        ControlDayPayData dayPayData = (ControlDayPayData) data.getControlData();
        if (dayPayData == null) {
            Log.game.error("发送活动数据玩家{}的活动{}的数据为空", humanObj.id,type);
            return;
        }
        dayPayData.chooseIndex = index;
        data.updateControlData();
    }

    public boolean canPay(HumanObject humanObj, ConfPayMall confPayMall){
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            return false;
        }
        ControlDayPayData dayPayData = (ControlDayPayData) data.getControlData();
        if (dayPayData == null) {
            Log.game.error("发送活动数据玩家{}的活动{}的数据为空", humanObj.id,type);
            return false;
        }
        int openTime = data.getActControlData().getOpenTime();
        int openDay = Utils.getDaysBetween(Port.getTime(), openTime*Time.SEC) + 1;
        if (Utils.intValue(confPayMall.parameter) != openDay) {
            return false;
        }
        return true;
    }
}
