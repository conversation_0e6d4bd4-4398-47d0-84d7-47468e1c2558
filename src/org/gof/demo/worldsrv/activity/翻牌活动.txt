新建活动类型4811命名CardEliminate
1.系统概述
1. 每消耗1门票可进行1局游戏
2. 游戏中玩家需将背着的卡牌背面，翻到两张相同的卡牌可进行消除
3. 默认最多可同时存在2张翻开的卡牌，若没成功消除，则卡牌翻回背面
4. 每次翻牌需消耗1点局内体力；局内npc会提供目标，成功翻到npc指定卡牌可获得局内体力奖励；
5. 将棋盘上的卡牌全部配对成功可进入下一关，可以从buff池中3选1一个buff技能；选择相同的buff技能可升星增强效果
6. 体力=0时本局游戏结束
7. 卡牌消除可获得积分，连续消除可获得额外积分，累计总积分用于排行榜排名
2.2.玩法规则
1. 游戏开始：每局游戏需消耗1门票；读取全局表字段"CardEliminateTicket"的intArray字段5|3600|1，代表初始拥有5门票，每3600秒恢复1个，门票的实现参考ActivityControlCastle活动的体力值实现
相关协议// 体力值刷新
message act_stamina_refresh_c2s {
    option (msgid) = 6544;
    uint32 act_type = 1;
}

// 体力值刷新
message act_stamina_refresh_s2c {
    option (msgid) = 6545;
    uint32 act_type = 1;
    uint32 stamina = 2;
    uint64 next_recover_time = 3;
}
  1. 门票获取途径：
    1. 时间自动恢复
    2. 使用门票道具恢复
3. 棋盘生成：ControlCardEliminateData中需要生成一个List<Integer>用来保存生成出来的纸牌。当max_score是0的时候是新手引导。数据的存储和还原像ControlCastleData一样。act_card_eliminate_info_s2c放到这个里面再放到json里面
注意新手引导要保证最开始的两次翻牌可以消除。如果生成的棋盘不可以消除的时候要交换棋盘格中可以消除的到第二张翻开的位置
  1. 每局游戏从第一轮开始，消除本轮全部卡牌时进入下一轮，翻开两张相同卡牌可消除，翻开的卡牌数量达到上限时，没有可消除的卡牌，点击翻开的卡牌翻回背面。客户端服务器各自翻回背面，不需要同步
  2. 每轮的卡牌总数量读取CardEliminateDungeon_0_蜘蛛纸牌关卡表【card_num】字段。ConfCardEliminateDungeon_0.get(actType, group, 轮次).card_num
    1. 卡牌总数有6、12、16，20共4种情况，分别以2x3/3x4/4x4/4x5的样式排列行x列。协议中的pos从1开始从上到下，从左到右
  3. 确定总数量后，根据【type_num】字段确认本轮共有几种元素（游戏中最多有十种元素的卡牌），每种元素至少需要1组（2张）牌，其余卡牌用本轮元素按组填充即可
  4. 确认元素种类数量type_num后，从1~10随机type_num个数，每个数就是对应的元素id。ConfCardEliminateElement_0.get(actType, group, 元素id)判断一下是否存在，不存在就报错
4. 任务npc生成：
  1. 根据CardEliminateDungeon_0_蜘蛛纸牌关卡表【task_num】字段确认本轮的任务数量，根据【task_patience】字段确认每个任务的耐心值
  2. 任务元素从棋盘生成时确认的元素种类中抽取，元素抽取不重复
  3. 耐心、奖励信息【reward】字段，奖励为耐心奖励
    1. 当前任务npc显示在中心位置，耐心、奖励信息【reward】字段，奖励为体力数量奖励
    2. 可以看见下一个任务npc在完成任务或耐心=0时，换下一个npc，
    4. 每次消耗局内体力，npc耐心-1，耐心=0时，此任务未完成，npc离开
5. 消除规则：
  1. 每局游戏开始时有初始局内体力值，读取全局表【Card_stamina_initial】字段intArray[0]，每通关一轮，局内体力增加intArray[1]
  2. 每次翻牌消耗1局内体力，局内体力=0时本轮游戏结束
  3. 成功翻开2张相同元素的卡牌，可消除但是还留在牌面上，获得积分奖励
  4. 场上翻开的卡牌数量达到上限（默认为2，通过buff可变成3）时，已经消除的不算，若没有可消除的卡牌，通过点击翻开的卡牌翻回背面
6. 消除积分&连消奖励：
    1. 当连续翻牌消除时，根据连消次数，获得对应积分加成；最终积分=基础积分x连消积分加成
    2. 翻开卡牌数量达到上限并翻回但没有消除时，连消中断
    3. 连消次数从1次开始计算，连消分数读取CardEliminateCombo_蜘蛛纸牌连消表【sn】是连消次数，【score】是连消分数
8. buff规则：
  1. 每轮通关后，将获得1次buff3选1的机会
    1. buff以后每局都生效
    2. 每局游戏，最多随机出3种buff被选择，已选3种后，只能刷出已选择的可升星buff，若已选buff全部满星，每轮结束后不再选择buff，选择已经安装的buff，buff可以升星
    3. buff相关信息读取cardEliminateBuff_蜘蛛纸牌buff表相关参数 ConfCardEliminateBuff.findAll()
        1. 抽取权重读取【rate】字段
        2. 解锁条件读取【unlock】字段，玩家累计分数达到要求时，此buff进入随机池
      4. buff首次获取时为1星，重复选取将提升星级，满星时随机结果不再出现此buff
        1. 已有的buff效果不能被其他buff效果替换
        2. 星级上限及每级技能效果参数读取【star】【parameter】
      5. buff效果：
        1. 透视效果：仅对未翻开的卡牌生效，显示未翻开卡牌的元素图案，依然可点击翻开
          1. 翻牌时，有15%概率将相连的1张牌翻开（随机翻开选中卡牌周围一圈上下左右对角线的卡牌，如果没有可翻开的则不翻）【parameter】 int[][]=1500,1|3000,1|4500,1每个星级的概率,翻牌数量
          2. 卡牌消除时，有30%的概率将任意1张盖住的卡牌翻开int[][]=3000,1|4500,1|4500,2 概率,翻牌数量
          3. npc初始耐心值上限+1 int[][]=1|2|3
          4. 翻牌时，有50%的概率使当前场上npc耐心+1  概率,耐心值数量
          5. 场上最多可同时存在3张翻开的牌
          6. 获得npc奖励时，会随机透视1张牌，透视持续2步 透视牌数，持续2步
          7. 翻牌时，有15%概率随机透视1张牌，透视持续2步 概率,透视牌数，持续2步
          8. 盖牌时，有40%概率触发场上当前npc额外赠送体力+1 概率,体力值数量
          9. 消除积分+5，本局游戏内有效
      9. 结算
        1. 本局游戏结束后，本局分数，使用花露，历史最高，本次排名，并展示获得的奖励
  6.新手引导：
  1. 首次游玩时的第一关，首个任务图案的卡牌固定放在第一行1、2号位，保证引导点击能正常消除，当max_score是0的时候是新手引导
  3. 引导点击1号位翻开第一张卡牌
  4. 引导点击2号位翻开第二张卡牌
  协议我重新调整为// 翻牌活动信息请求

