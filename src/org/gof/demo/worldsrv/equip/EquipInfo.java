package org.gof.demo.worldsrv.equip;

import com.alibaba.fastjson.JSONObject;
import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.interfaces.ISerilizable;
import org.gof.core.support.Utils;
import org.gof.demo.battlesrv.manager.PropManager;
import org.gof.demo.battlesrv.support.PropCalc;
import org.gof.demo.worldsrv.config.ConfEquipment;
import org.gof.demo.worldsrv.msg.Define;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class EquipInfo  implements ISerilizable {
    public static int weapon = 1;//武器
    public static int hat = 2;//帽子
    public static int face = 3;//面饰
    public static int shoes = 10;//鞋子
    public static int max_part = 10;//最大部位

    public long id;
    public int sn;
    public int lv;
    public PropCalc baseAttr = new PropCalc();
    public PropCalc randAttr = new PropCalc();

    public EquipInfo() {
    }

    public EquipInfo(long id) {
        this.id = id;
    }

    public JSONObject toJsonObj() {
        JSONObject json = new JSONObject();
        json.put("id", id);
        json.put("sn", sn);
        json.put("lv", lv);
        json.put("base", baseAttr.toJSONStr());
        json.put("rand", randAttr.toJSONStr());
        return json;
    }

    public String toJsonString() {
        return toJsonObj().toJSONString();
    }

    public void fromJsonString(String jsonStr) {
        JSONObject json = Utils.toJSONObject(jsonStr);
        fromJsonObj(json);
    }

    public void fromJsonObj(JSONObject json) {
        id = json.getLongValue("id");
        sn = json.getIntValue("sn");
        lv = json.getIntValue("lv");
        baseAttr.plus(json.getString("base"));
        randAttr.plus(json.getString("rand"));
    }

    public Define.p_equip.Builder toProtoBuilder(int tab) {
        Define.p_equip.Builder builder = Define.p_equip.newBuilder();
        builder.setEquipId(id);
        builder.setConfigId(sn);
        builder.setEquipLv(lv);
        int location = tab == 0 ? 0 : ConfEquipment.get(sn).part;
        builder.setLocation(location);
        builder.setTab(tab);
        List<Define.p_key_value> baseAttrList = baseAttr.dPropList();
        List<Define.p_key_value> randAttrList = randAttr.dPropList();
        builder.addAllBaseAttr(baseAttrList);
        builder.addAllRandAttr(randAttrList);
        PropCalc attrAll = new PropCalc();
        attrAll.plus(baseAttr);
        attrAll.plus(randAttr);
        long combat = PropManager.inst().getEquipCombat(attrAll, lv).longValue();
        builder.setCombat(combat);
        return builder;
    }

    public static String toMapJsonString(Map<Long, EquipInfo> equipInfos) {
        JSONObject json = new JSONObject();
        for (Map.Entry<Long, EquipInfo> entry : equipInfos.entrySet()) {
            json.put(String.valueOf(entry.getKey()), entry.getValue().toJsonObj());
        }
        return json.toJSONString();
    }


    public static Map<Long, EquipInfo> fromMapJsonString(String jsonString) {
        Map<Long, EquipInfo> equipInfos = new HashMap<>();
        JSONObject json = Utils.toJSONObject(jsonString);
        for (Map.Entry<String, Object> entry : json.entrySet()) {
            EquipInfo equipInfo = new EquipInfo();
            equipInfo.fromJsonObj((JSONObject) entry.getValue());
            equipInfos.put(Long.valueOf(entry.getKey()), equipInfo);
        }
        return equipInfos;
    }

    @Override
    public void writeTo(OutputStream out) throws IOException {
        out.write(id);
        out.write(sn);
        out.write(lv);
        out.write(baseAttr);
        out.write(randAttr);
    }

    @Override
    public void readFrom(InputStream in) throws IOException {
        id = in.read();
        sn = in.read();
        lv = in.read();
        baseAttr = in.read();
        randAttr = in.read();
    }
}
