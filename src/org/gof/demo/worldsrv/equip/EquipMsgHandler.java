package org.gof.demo.worldsrv.equip;

import org.gof.core.support.Param;
import org.gof.core.support.observer.MsgReceiver;
import org.gof.demo.seam.msg.MsgParam;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.msg.MsgEquip;


public class EquipMsgHandler {

    /**
     * 装备信息
     * @param param
     */
    @MsgReceiver(MsgEquip.equip_info_c2s.class)
    public void equip_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        EquipManager.inst().equip_info_c2s(humanObj);
    }

    /**
     * 装备穿戴
     * @param param
     */
    @MsgReceiver(MsgEquip.equip_wear_c2s.class)
    public void equip_wear_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgEquip.equip_wear_c2s msg = param.getMsg();
        EquipManager.inst().equip_wear_c2s(humanObj, msg.getEquipId());
    }

    /**
     * 卖出装备
     * @param param
     */
    @MsgReceiver(MsgEquip.equip_shop_c2s.class)
    public void equip_shop_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgEquip.equip_shop_c2s msg = param.getMsg();
        EquipManager.inst().equip_shop_c2s(humanObj, msg.getEquipIdsList());
    }

    /**
     * 装备宝箱信息
     * @param param
     */
    @MsgReceiver(MsgEquip.equip_box_info_c2s.class)
    public void equip_box_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        EquipManager.inst().equip_box_info_c2s(humanObj);
    }

    /**
     * 装备宝箱等级
     * @param param
     */
    @MsgReceiver(MsgEquip.equip_box_lv_c2s.class)
    public void equip_box_lv_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        EquipManager.inst().equip_box_lv_c2s(humanObj);
    }

    /**
     * 装备宝箱开启
     * @param param
     */
    @MsgReceiver(MsgEquip.equip_box_open_c2s.class)
    public void equip_box_open_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        EquipManager.inst().equip_box_open_c2s(humanObj);
    }

    /**
     * 装备宝箱一键开启
     * @param param
     */
    @MsgReceiver(MsgEquip.equip_box_open_all_c2s.class)
    public void equip_box_open_all_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgEquip.equip_box_open_all_c2s msg = param.getMsg();
        EquipManager.inst().equip_box_open_all_c2s(humanObj,msg.getNum(),msg.getQuality());
    }

    /**
     * 装备图鉴列表
     * @param param
     */
    @MsgReceiver(MsgEquip.equip_book_list_c2s.class)
    public void equip_book_list_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        EquipManager.inst().equip_book_list_c2s(humanObj);
    }

    /**
     * 装备形象列表
     * @param param
     */
    @MsgReceiver(MsgEquip.equip_figure_list_c2s.class)
    public void equip_figure_list_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        EquipManager.inst().equip_figure_list_c2s(humanObj);
    }

    /**
     * 装备形象穿戴
     * @param param
     */
    @MsgReceiver(MsgEquip.equip_figure_c2s.class)
    public void equip_figure_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgEquip.equip_figure_c2s msg = param.getMsg();
        EquipManager.inst().equip_figure_c2s(humanObj, msg.getFigure());
    }

    /**
     * 装备选择
     * @param param
     */
    @MsgReceiver(MsgEquip.equip_select_c2s.class)
    public void equip_select_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgEquip.equip_select_c2s msg = param.getMsg();
        EquipManager.inst().equip_select_c2s(humanObj, msg.getEquipId());
    }

    /**
     * 装备标签信息
     * @param param
     */
    @MsgReceiver(MsgEquip.equip_tab_info_c2s.class)
    public void equip_tab_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        EquipManager.inst().equip_tab_info_c2s(humanObj);
    }

    /**
     * 装备选择标签
     * @param param
     */
    @MsgReceiver(MsgEquip.equip_choose_tab_c2s.class)
    public void equip_choose_tab_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgEquip.equip_choose_tab_c2s msg = param.getMsg();
        EquipManager.inst().equip_choose_tab_c2s(humanObj, msg.getTab());
    }

    /**
     * 装备修改标签名
     * @param param
     */
    @MsgReceiver(MsgEquip.equip_change_tab_name_c2s.class)
    public void equip_change_tab_name_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgEquip.equip_change_tab_name_c2s msg = param.getMsg();
        EquipManager.inst().equip_change_tab_name_c2s(humanObj, msg.getTab(), msg.getName());
    }

    /**
     * 装备宝箱皮肤
     * @param param
     */
    @MsgReceiver(MsgEquip.equip_box_skin_c2s.class)
    public void equip_box_skin_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        EquipManager.inst().equip_box_skin_c2s(humanObj);
    }

    /**
     * 装备修改宝箱皮肤
     * @param param
     */
    @MsgReceiver(MsgEquip.equip_change_box_skin_c2s.class)
    public void equip_change_box_skin_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgEquip.equip_change_box_skin_c2s msg = param.getMsg();
        EquipManager.inst().equip_change_box_skin_c2s(humanObj, msg.getId());
    }

    @MsgReceiver(MsgEquip.equip_box_skin_unlock_c2s.class)
    public void equip_box_skin_unlock_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgEquip.equip_box_skin_unlock_c2s msg = param.getMsg();
        EquipManager.inst().equip_box_skin_unlock_c2s(humanObj, msg.getSn());
    }

    /**
     * 装备属性信息
     */
    @MsgReceiver(MsgEquip.equip_filter_info_c2s.class)
    public void equip_filter_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        EquipManager.inst().equip_filter_info_c2s(humanObj);
    }

    /**
     * 装备属性筛选
     */
    @MsgReceiver(MsgEquip.equip_filter_attr_c2s.class)
    public void equip_filter_attr_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgEquip.equip_filter_attr_c2s msg = param.getMsg();
        EquipManager.inst().equip_filter_attr_c2s(humanObj, msg.getOrder(), msg.getAttrId());
    }
}
