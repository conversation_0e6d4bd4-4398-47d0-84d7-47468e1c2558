package org.gof.demo.worldsrv.equip;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.gof.core.Port;
import org.gof.core.support.ManagerBase;
import org.gof.core.support.Param;
import org.gof.core.support.Time;
import org.gof.core.support.Utils;
import org.gof.core.support.observer.Listener;
import org.gof.demo.battlesrv.manager.CombatChangeLog;
import org.gof.demo.battlesrv.manager.PropManager;
import org.gof.demo.battlesrv.support.PropCalc;
import org.gof.demo.worldsrv.activity.ActivityEffectHelper;
import org.gof.demo.worldsrv.activity.ActivityManager;
import org.gof.demo.worldsrv.character.HumanDailyResetInfo;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.charm.CharmManager;
import org.gof.demo.worldsrv.common.GameServiceProxy;
import org.gof.demo.worldsrv.config.*;
import org.gof.demo.worldsrv.entity.*;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.human.HumanManager;
import org.gof.demo.worldsrv.human.PlanVo;
import org.gof.demo.worldsrv.inform.Inform;
import org.gof.demo.worldsrv.instance.InstanceConstants;
import org.gof.demo.worldsrv.instance.InstanceManager;
import org.gof.demo.worldsrv.item.ItemConstants;
import org.gof.demo.worldsrv.item.ItemManager;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgEquip;
import org.gof.demo.worldsrv.privilege.PrivilegeManager;
import org.gof.demo.worldsrv.privilege.PrivilegeType;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.produce.ProduceVo;
import org.gof.demo.worldsrv.support.*;
import org.gof.demo.worldsrv.support.enumKey.*;
import org.gof.demo.worldsrv.support.observer.Event;
import org.gof.demo.worldsrv.support.observer.EventKey;
import org.gof.demo.worldsrv.task.TaskConditionTypeKey;
import org.gof.demo.worldsrv.treasureSkin.TreasureSkinInfo;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static org.gof.demo.worldsrv.equip.EquipInfo.*;


public class EquipManager extends ManagerBase {

	/**
	 * 获取实例
	 *
	 */
	public static EquipManager inst() {
		return inst(EquipManager.class);
	}

	public static final int UPGRADE = 1;
	public static final int ADD = 2;
	public static final int DELETE = 3;
	public static final int BOX_MAX = 40;//装备宝箱容量

	public static final int TAB_MAX = 6;

	public static final int FIGURE_NONE = -1;
	public static final int FIGURE_DEFAULT = 0;

	/**
	 * 装备信息
	 * @param humanObj
	 */
	public void equip_info_c2s(HumanObject humanObj) {
		MsgEquip.equip_info_s2c.Builder msg = MsgEquip.equip_info_s2c.newBuilder();
//		int tab = humanObj.getHuman().getEquipTab();
//		Equip equip = humanObj.operation.equipsMap.get(tab);
		for (Map.Entry<Integer, Equip> entry : humanObj.operation.equipsMap.entrySet()) {
			int tab = entry.getKey();
			Equip equip = entry.getValue();
			for (int i = weapon; i <= EquipInfo.max_part; ++i) {
				String jsonString = getEquipPart(equip,i);
				if (!Utils.isNullOrEmptyJSONString(jsonString)) {
					EquipInfo equipInfo = new EquipInfo();
					equipInfo.fromJsonString(jsonString);
					Define.p_equip.Builder p_equip = equipInfo.toProtoBuilder(tab);
					msg.addEquipList(p_equip);
				}
			}
		}
		Map<Long, EquipInfo> equipBoxMap = humanObj.operation.equipBoxMap;
		for (EquipInfo equipInfo : equipBoxMap.values()){
			Define.p_equip.Builder p_equip = equipInfo.toProtoBuilder(0);
			msg.addEquipList(p_equip);
		}
		humanObj.sendMsg(msg);
	}

	/**
	 * 装备穿戴
	 */
	public void equip_wear_c2s(HumanObject humanObj, long equipId){
		//装备宝箱中取装备
		Map<Long, EquipInfo> equipBoxMap = humanObj.operation.equipBoxMap;
		EquipInfo equipInfo = equipBoxMap.get(equipId);
		if (equipInfo == null) {
			return;
		}

		int tab = humanObj.getHuman2().getEquipTab();

		EquipInfo oldEquipInfo = null;
		if (!humanObj.operation.equipsMap.containsKey(tab)) {
			//数据库没标签页时
			long id = Port.applyId();
			Equip equip = new Equip();
			equip.setId(id);
			equip.setHumanId(humanObj.getHumanId());
			equip.setTab(tab);
			ConfEquipment conf = ConfEquipment.get(equipInfo.sn);
			if (conf ==null) {
				Log.game.error("EquipManager.equip_wear_c2s，表ConfEquipment:找不到配置表,sn={}", equipInfo.sn);
                return;
			}
			setEquipPart(equip, conf.part, equipInfo.toJsonString());
			equip.persist();
			humanObj.operation.equipsMap.put(tab, equip);
		} else {
			//数据库有标签页时
            Equip equip = humanObj.operation.equipsMap.get(tab);
            ConfEquipment conf = ConfEquipment.get(equipInfo.sn);
            if(conf ==null){
                Log.game.error("EquipManager.equip_wear_c2s，表ConfEquipment:找不到配置表,sn={}", equipInfo.sn);
                return;
            }
			String jsonString2 = getEquipPart(equip, conf.part);
			if(!Utils.isNullOrEmptyJSONString(jsonString2)){
				oldEquipInfo = new EquipInfo();
				oldEquipInfo.fromJsonString(getEquipPart(equip,conf.part));
			}
            setEquipPart(equip, conf.part, equipInfo.toJsonString());
			equip.update();
		}

		//脱下身上的装备放回装备宝箱
		if (oldEquipInfo != null) {
			equipBoxMap.put(oldEquipInfo.id,oldEquipInfo);
		}
		equipBoxMap.remove(equipId);

		MsgEquip.equip_change_s2c.Builder msg = MsgEquip.equip_change_s2c.newBuilder();
		msg.setType(UPGRADE);
		msg.setSubType(0);
		Define.p_equip.Builder p_equip = equipInfo.toProtoBuilder(tab);
		msg.addChangeList(p_equip);
		if (oldEquipInfo!= null) {
			Define.p_equip.Builder p_equip2 = oldEquipInfo.toProtoBuilder(0);
			msg.addChangeList(p_equip2);
		}
		humanObj.sendMsg(msg);
		updateEquipPorpCalc(humanObj);

		humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_冒险任务, TaskConditionTypeKey.TASK_TYPE_7);
		humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_成就任务, TaskConditionTypeKey.TASK_TYPE_7);
		ActivityManager.inst().addActivityProgress(humanObj, TaskConditionTypeKey.TASK_TYPE_7, 0);
		Event.fire(EventKey.UPDATE_NEWS, "humanObj", humanObj, "newsCondition", NewsConditionTypeKey.EquipQuality, "value", ConfEquipment.get(equipInfo.sn).quality);
	}

	public void updateEquipPorpCalc(HumanObject humanObj){
		PropCalc propCalc = getEquipPropCalc(humanObj, humanObj.getHuman2().getEquipTab());
		humanObj.dataPers.unitPropPlus.setFieldRead(UnitPropPlus.K.equip, propCalc.toJSONStr());
		PropManager.inst().propCalc(humanObj, CombatChangeLog.装备);
	}

	public PropCalc getEquipPropCalc(HumanObject humanObj, int tab){
		PropCalc propCalc = new PropCalc();
		Equip equip = humanObj.operation.equipsMap.get(tab);
		if(equip == null){
			return propCalc;
		}
		for(int i = weapon; i <= EquipInfo.max_part; ++i){
			String jsonString = getEquipPart(equip,i);
			if(!Utils.isNullOrEmptyJSONString(jsonString)){
				EquipInfo equipInfo = new EquipInfo();
				equipInfo.fromJsonString(jsonString);
				propCalc.plus(equipInfo.baseAttr);
				propCalc.plus(equipInfo.randAttr);
			}
		}
		return propCalc;
	}

	private void setEquipPart(Equip equip, int part, String jsonString){
		switch (part){
			case 1:
                equip.setPart1(jsonString);
                break;
            case 2:
                equip.setPart2(jsonString);
                break;
            case 3:
                equip.setPart3(jsonString);
                break;
            case 4:
                equip.setPart4(jsonString);
                break;
            case 5:
				equip.setPart5(jsonString);
				break;
			case 6:
				equip.setPart6(jsonString);
				break;
			case 7:
				equip.setPart7(jsonString);
				break;
			case 8:
				equip.setPart8(jsonString);
				break;
			case 9:
				equip.setPart9(jsonString);
				break;
			case 10:
				equip.setPart10(jsonString);
				break;

			default:
				Log.game.error("EquipManager.setEquipPart: part is error, part={}", part);
				break;
		}
	}

	public String getEquipPart(Equip equip, int part){
		switch (part) {
			case 1:
				return equip.getPart1();
			case 2:
				return equip.getPart2();
			case 3:
				return equip.getPart3();
			case 4:
				return equip.getPart4();
			case 5:
				return equip.getPart5();
			case 6:
				return equip.getPart6();
			case 7:
				return equip.getPart7();
			case 8:
				return equip.getPart8();
			case 9:
				return equip.getPart9();
			case 10:
				return equip.getPart10();
			default:
				Log.game.error("EquipManager.getEquipPart: part is error, part={}", part);
				return null;
		}
	}

	/**
 	 *卖出装备
	 */
	public void equip_shop_c2s(HumanObject humanObj, List<Long> equipIdsList) {
		Map<Long, EquipInfo> equipBoxMap = humanObj.operation.equipBoxMap;
		ConfTreasureLevel conf = ConfTreasureLevel.get(humanObj.getHumanExtInfo().getEquipBoxLv());

		HumanDailyResetInfo info = humanObj.getDailyResetInfo(DailyResetTypeKey.pvpTicket.getType());
		int tickLimit = info.getValue();

		Map<Integer, Integer> itemNumMap = new HashMap<>();

		MsgEquip.equip_change_s2c.Builder msg = MsgEquip.equip_change_s2c.newBuilder();
		boolean isAddPvpTicket = false;
		int num = 0;
		for(long equipId : equipIdsList){
			EquipInfo equipInfo = equipBoxMap.get(equipId);
			if(equipInfo == null){
				msg.addChangeList(Define.p_equip.newBuilder().setEquipId(equipId));
				continue;
			}
			msg.addChangeList(Define.p_equip.newBuilder().setEquipId(equipId));
			equipBoxMap.remove(equipId);
			num++;
			ConfEquipment confEquipment = ConfEquipment.get(equipInfo.sn);
			if(confEquipment == null){
				msg.addChangeList(Define.p_equip.newBuilder().setEquipId(equipId));
				Log.game.error("EquipManager.equip_shop_c2s，表ConfEquipment:找不到配置表,sn={}", equipInfo.sn);
				continue;
			}

			// 判断是否是活动装备
			if (confEquipment.act_id > 0 && humanObj.openActivitySnList.contains(confEquipment.act_id)) {
				// 检查活动是否开启
				// 活动开启，使用活动期间的售卖奖励
				if (confEquipment.sellRewardActivity != null) {
					for (int[] reward : confEquipment.sellRewardActivity) {
						if (itemNumMap.containsKey(reward[0])) {
							itemNumMap.put(reward[0], itemNumMap.get(reward[0]) + reward[1]);
						} else {
							itemNumMap.put(reward[0], reward[1]);
						}
					}
				}
			} else {
				// 非活动装备，使用正常售卖奖励
				if (confEquipment.sellReward != null) {
					for (int[] reward : confEquipment.sellReward) {
						if (itemNumMap.containsKey(reward[0])) {
							itemNumMap.put(reward[0], itemNumMap.get(reward[0]) + reward[1]);
						} else {
							itemNumMap.put(reward[0], reward[1]);
						}
					}
				}
			}
			if(tickLimit >= conf.ticketmax){
				continue;
			}
			int tickNum = ItemManager.inst().getItemNum(humanObj, ItemConstants.goods_挑战券);
			int vipNum = PrivilegeManager.inst().getPrivilegeEffectValue(humanObj, PrivilegeType.CHALLENGE);
			int activityNum = ActivityEffectHelper.getArenaTicketLimitBonus(humanObj);
			if(tickNum >= ConfGlobal.get(ConfGlobalKey.pvp_ticket_max.SN).value + vipNum + activityNum){
				continue;
			}
			if(Utils.random(10000) < conf.ticketprob) {
				tickLimit++;
				isAddPvpTicket = true;
				itemNumMap.put(ItemConstants.goods_挑战券, itemNumMap.getOrDefault(ItemConstants.goods_挑战券, 0) + 1);
			}
		}
		if(tickLimit != info.getValue()){
			info.setValue(tickLimit);
			humanObj.saveDailyResetRecord();
		}
		humanObj.getHumanExtInfo().setEquipInfo(EquipInfo.toMapJsonString(equipBoxMap));
//		humanObj.getHumanExtInfo().update();

		ProduceManager.inst().produceAdd(humanObj, itemNumMap, MoneyItemLogKey.装备售出);
		if(isAddPvpTicket){
			InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, InstanceConstants.showType_1001007,ItemConstants.goods_挑战券, 1);
		}

		msg.setType(DELETE);
		msg.setSubType(0);
		humanObj.sendMsg(msg);

		MsgEquip.equip_shop_s2c.Builder msg2 = MsgEquip.equip_shop_s2c.newBuilder();
		for (Map.Entry<Integer, Integer> entry : itemNumMap.entrySet()) {
			Define.p_reward.Builder reward = Define.p_reward.newBuilder();
			reward.setGtid(entry.getKey());
			reward.setNum(entry.getValue());
			msg2.addReward(reward);
		}
		humanObj.sendMsg(msg2);

		humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_主线, TaskConditionTypeKey.TASK_TYPE_9, num);
		humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_成就任务, TaskConditionTypeKey.TASK_TYPE_9, num);
		humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_战令, TaskConditionTypeKey.TASK_TYPE_9, num);
		ActivityManager.inst().addActivityProgress(humanObj, TaskConditionTypeKey.TASK_TYPE_9, num, num);
	}
	

	/**
	 * 装备宝箱信息
	 */
	public void equip_box_info_c2s(HumanObject humanObj) {
		MsgEquip.equip_box_info_s2c.Builder msg = MsgEquip.equip_box_info_s2c.newBuilder();
		msg.setEtime((int)(humanObj.getHumanExtInfo().getEquipBoxETime()/Time.SEC));
		msg.setLv(humanObj.getHumanExtInfo().getEquipBoxLv());
		msg.setExp(humanObj.getHumanExtInfo().getEquipBoxExp());
		humanObj.sendMsg(msg);
	}

	/**
	 * 装备宝箱升级
	 */
	public void equip_box_lv_c2s(HumanObject humanObj) {
		if(Port.getTime() < humanObj.getHumanExtInfo().getEquipBoxETime()){
			return;
		}

		int lv = humanObj.getHumanExtInfo().getEquipBoxLv();
		int exp = humanObj.getHumanExtInfo().getEquipBoxExp();
		ConfTreasureLevel conf = ConfTreasureLevel.get(lv);
		if(conf == null || conf.expend == null){
			return;
		}

		int[][] expend = conf.expend;
		if(exp < expend.length){
			//升级加经验
			int costSn = expend[exp][0];
			int costNum = expend[exp][1];
			ReasonResult reason = ProduceManager.inst().canCostProduce(humanObj, costSn, costNum);
			if(!reason.success){
				return;
			}
			ProduceManager.inst().costItem(humanObj, costSn, costNum, MoneyItemLogKey.打开神灯消耗);
			humanObj.getHumanExtInfo().setEquipBoxExp(exp+1);
		}else {
			long interval = conf.time * Time.SEC;
			humanObj.getHumanExtInfo().setEquipBoxETime(interval + Port.getTime());
			humanObj.startCheckEquipBox(interval);
		}
//		humanObj.getHumanExtInfo().update();
		equip_box_info_c2s(humanObj);
	}

	/**
	 * 装备宝箱开启
	 */
	public void equip_box_open_c2s(HumanObject humanObj) {
		MsgEquip.equip_box_open_s2c.Builder msg = MsgEquip.equip_box_open_s2c.newBuilder();
		ReasonResult reason = ProduceManager.inst().canCostProduce(humanObj,TokenItemType.Money1001, 1);
		if(!reason.success){
			msg.setEquipId(-1);
			humanObj.sendMsg(msg);
			return;
		}
		HumanExtInfo extInfo = humanObj.getHumanExtInfo();
		ConfGlobal confInit = ConfGlobal.get("Initial_random_equipment");
		EquipInfo equipInfo = null;
        if(confInit != null && extInfo.getEquipInit() < confInit.strArray.length){
			String[] str = Utils.splitStr(confInit.strArray[extInfo.getEquipInit()],"\\,");
			equipInfo = createEquip(humanObj, humanObj.operation.profession.getJobSn(), humanObj.getHuman().getLevel(), humanObj.idGen(), extInfo.getEquipBoxLv(),Utils.intValue(str[0]),Utils.intValue(str[1]));
			if(equipInfo!=null){
				extInfo.setEquipInit(extInfo.getEquipInit()+1);
			}
		}else {
			equipInfo = createEquip(humanObj, humanObj.operation.profession.getJobSn(), humanObj.getHuman().getLevel(), humanObj.idGen(),humanObj.getHumanExtInfo().getEquipBoxLv(),0,0);
		}
		if(equipInfo == null){
			msg.setEquipId(-1);
			humanObj.sendMsg(msg);
			return;
		}
		if(!addToEquipBox(humanObj, equipInfo)){
			msg.setEquipId(-1);
			humanObj.sendMsg(msg);
			return;
		}

		ProduceManager.inst().costItem(humanObj, TokenItemType.Money1001,1, MoneyItemLogKey.打开神灯消耗);

		msg.setEquipId(equipInfo.id);
		humanObj.sendMsg(msg);

		MsgEquip.equip_change_s2c.Builder msg2 = MsgEquip.equip_change_s2c.newBuilder();
		msg2.setType(ADD);
		msg2.setSubType(0);
		Define.p_equip.Builder p_equip = equipInfo.toProtoBuilder(0);
		msg2.addChangeList(p_equip);
		humanObj.sendMsg(msg2);

		//添加图鉴
		List<Integer> toBookList = new ArrayList<>();
		toBookList.add(equipInfo.sn);
		addBookList(humanObj, toBookList);

		Event.fire(EventKey.EQUIP_BOX_OPEN_NUM, "humanObj", humanObj, "num", 1);
	}

	public int getEquipGrade(int lv){
		return Math.min(lv, GlobalConfVal.MAX_LEVEL);
	}
	/*生成装备
	 * @param humanObj
	 */
	public EquipInfo createEquip(HumanObject humanObj, int job, int lv, long id, int boxLv, int part, int quality){
		EquipInfo equipInfo = new EquipInfo(id);

		initEquipLevel(equipInfo, lv);
		//从默认职业和当前职业随机装备配置
		int grade = equipInfo.lv;
		//过滤出符合宝箱品质的装备，和部位符合的装备
		ConfTreasureLevel confTreasureLevel = ConfTreasureLevel.get(boxLv);
		int index1 = Utils.randomByWeight2D(confTreasureLevel.pro_quality,1);
		quality = quality == 0 ? confTreasureLevel.pro_quality[index1][0] : quality;
		quality = limitResetQuality(humanObj, boxLv, quality);
		int defaultJob = 1;
		Set<Integer> confDefaultEquipments = GlobalConfVal.getConfEquipments(defaultJob, grade, quality);
		Set<Integer> confEquipments = new HashSet<>();
		if(job != defaultJob) {
			confEquipments = GlobalConfVal.getConfEquipments(ConfJobs.get(job).transmog_list[1], grade, quality);
			if(confEquipments == null||confEquipments.isEmpty()){
				Log.game.error("ConfEquipment找不到对应的配置, humanId={}, profession={}, grade={}, quality={}", humanObj.id, job, grade, quality);
				return null;
			}
		}

		List<ConfEquipment> confQualityEquipments = new ArrayList<>();
		for(int confSn : confDefaultEquipments){
			ConfEquipment conf = ConfEquipment.get(confSn);
			if(conf == null){
				Log.game.error("ConfEquipment找不到对应的配置, sn={}", confSn);
				continue;
			}
			if(part == 0 || part == conf.part){
				confQualityEquipments.add(conf);
			}
		}
		for(int confSn : confEquipments){
			ConfEquipment conf = ConfEquipment.get(confSn);
			if(conf == null){
				Log.game.error("ConfEquipment找不到对应的配置, sn={}", confSn);
				continue;
			}
			if(part == 0 || part == conf.part){
				confQualityEquipments.add(conf);
			}
		}
		if(confQualityEquipments.isEmpty()){
			Log.game.error("ConfEquipment找不到对应的配置, profession={}, grade={}, quality={}", job, grade, quality);
            return null;
		}

		int index = Utils.random(confQualityEquipments.size());
		ConfEquipment confEquipment = confQualityEquipments.get(index);

		if (!initEquipAttributes(humanObj, equipInfo, confEquipment)) return null;
		return equipInfo;
	}

	public int limitResetQuality(HumanObject humanObj, int boxLv, int quality) {
		ConfGlobal confunlock = ConfGlobal.get(ConfGlobalKey.equip_unlock_condition.SN);
		if(confunlock != null){
			Human human = humanObj.getHuman();
			int[][] arr = Utils.parseIntArray2(confunlock.strValue);
			for(int[] arr1 : arr){
				if(quality < arr1[0]){
					break;
				} else {
					int day = Utils.getDaysBetween(Port.getTime(), Util.getOpenServerTime(human.getServerId())) + 1;
					// 品质，开服天数，等级，神灯等级
					if(day < arr1[1] || human.getLevel() < arr1[2] || boxLv < arr1[3]){
						quality = arr1[0] - 1;
						break;
					}
				}
			}
		}
		return quality;
	}

	private void initEquipLevel(EquipInfo equipInfo, int lv) {
		ConfGlobal confGlobal = ConfGlobal.get(101);
		//随机装备等级
		equipInfo.lv = lv - confGlobal.value + Utils.randomByWeight(confGlobal.intArray);
		equipInfo.lv = Math.max(equipInfo.lv, 1);
		equipInfo.lv = Math.min(equipInfo.lv, GlobalConfVal.MAX_LEVEL);
	}

	private boolean initEquipAttributes(HumanObject humanObj, EquipInfo equipInfo, ConfEquipment confEquipment) {
		equipInfo.sn = confEquipment.sn;
		PropCalc baseAttr = new PropCalc();
		PropCalc randAttr = new PropCalc();

		//基础属性
		int groupId = 1000+ equipInfo.lv;
		if(confEquipment.part != weapon){
			groupId = 2000+ equipInfo.lv;
		}
		List<ConfEquipmentAttr> confAttr = GlobalConfVal.getConfEquipmentAttr(groupId);
		for (ConfEquipmentAttr conf : confAttr){
			if(Utils.random(10000)>conf.pro){
				continue;
			}
			int atrr = Utils.random(conf.value[0],conf.value[1]+1);

			String[] multiple = confEquipment.multiple.split("\\|");
			//属性倍率map，key为属性id，value为倍率，不存在取key为0的值为倍率
			Map<Integer, Float> multipleMap = new HashMap<>();
			for(String str : multiple){
				String[] strs = str.split(",");
				multipleMap.put(Utils.intValue(strs[0]), Utils.floatValue(strs[1]));
			}
			float multipleValue = multipleMap.containsKey(conf.attrId) ? multipleMap.get(conf.attrId) : multipleMap.get(0);
			atrr = (int)Math.ceil(atrr*multipleValue);
			baseAttr.plus(conf.attrId, BigDecimal.valueOf(atrr));
		}

		//随机属性
		if(confEquipment.number > 0 && confEquipment.advanced > 0){
			List<ConfEquipmentAttr> confRandAttr = GlobalConfVal.getConfEquipmentAttr(confEquipment.advanced);
			if(confRandAttr.size() < confEquipment.number){
				Log.game.error("ConfEquipmentAttr找不到对应的配置, advanced={}, number={}", confEquipment.advanced, confEquipment.number);
				return false;
			}
			int[] weight = new int[confRandAttr.size()];
			Collection<Integer> excludeIds = humanObj.operation.equipFilterMap.values();
			for(int i = 0; i < confRandAttr.size(); ++i){
				if(excludeIds.contains(confRandAttr.get(i).attrId)){
					weight[i] = 0;
//					Log.game.debug("成功属性过滤, equipSn={}, attrId={}, number={}", confEquipment.sn, confRandAttr.get(i).attrId);
					continue;
				}
				weight[i] = confRandAttr.get(i).pro;
			}
			for(int i = 0; i < confEquipment.number; ++i){
				int index2 = Utils.randomByWeight(weight);
				ConfEquipmentAttr conf = confRandAttr.get(index2);
				int atrr = Utils.random(conf.value[0],conf.value[1]+1);
				randAttr.plus(conf.attrId, BigDecimal.valueOf(atrr));
				weight[index2] = 0;
			}
		}

		equipInfo.baseAttr = baseAttr;
		equipInfo.randAttr = randAttr;
		return true;
	}
	/*
	* 装备宝箱加装备
	* @param humanObj
	* @param equipInfo
	 */
	public boolean addToEquipBox(HumanObject humanObj, EquipInfo equipInfo){
		Map<Long, EquipInfo> equipBoxMap = humanObj.operation.equipBoxMap;
		if(equipBoxMap.size() >= BOX_MAX){
			Log.game.error("玩家：{}，id：{}装备宝箱已满", humanObj.getHuman().getName(), humanObj.getHumanId());
			return false;
		}
		if(equipBoxMap.containsKey(equipInfo.id)){
			Log.game.error("玩家：{}，装备id出现重复 id：{}", humanObj.getHumanId(), equipInfo.id);
			return false;
		}
		equipBoxMap.put(equipInfo.id, equipInfo);
		HumanExtInfo extInfo = humanObj.getHumanExtInfo();
		extInfo.setEquipInfo(EquipInfo.toMapJsonString(humanObj.operation.equipBoxMap));
		return true;
	}

	/**
	 * 一键装备宝箱开启
	 */
	public void equip_box_open_all_c2s(HumanObject humanObj, int num, int quality) {
		ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.batch_auto_open_box.SN);
		String[] strArray = confGlobal.strValue.split("\\|");
		int maxNum = 1;
		for(String str : strArray){
			String[] strs = str.split(",");
			if(humanObj.getHumanExtInfo().getEquipBoxLv() >= Utils.intValue(strs[0])){
				maxNum = Utils.intValue(strs[1]);
			}else {
				break;
			}
		}
		num = num > maxNum ? maxNum : num;

		ReasonResult reason = ProduceManager.inst().checkAndCostItem(humanObj, TokenItemType.Money1001, num, MoneyItemLogKey.打开神灯消耗);
		if(!reason.success){
			return;
		}
		MsgEquip.equip_box_open_all_s2c.Builder msg = MsgEquip.equip_box_open_all_s2c.newBuilder();

		MsgEquip.equip_change_s2c.Builder msg2 = MsgEquip.equip_change_s2c.newBuilder();
		msg2.setType(ADD);
		msg2.setSubType(1);

		List<Integer> toBookList = new ArrayList<>();
		Map<Long, EquipInfo> equipBoxMap = humanObj.operation.equipBoxMap;
		int humanlv = humanObj.getHuman().getLevel();
		int job = humanObj.operation.profession.getJobSn();
		for(int i = 0; i < num; ++i){
			EquipInfo equipInfo = createEquip(humanObj, job, humanlv, humanObj.idGen(), humanObj.getHumanExtInfo().getEquipBoxLv(),0,0);
			if(equipInfo==null){
				ProduceManager.inst().produceAdd(humanObj,TokenItemType.Money1001, 1, MoneyItemLogKey.打开神灯消耗);
				continue;
			}
			toBookList.add(equipInfo.sn);
			equipBoxMap.put(equipInfo.id, equipInfo);
			msg.addEquipIds(equipInfo.id);
			Define.p_equip.Builder p_equip = equipInfo.toProtoBuilder(0);
			msg2.addChangeList(p_equip);
		}

		humanObj.sendMsg(msg2);
		humanObj.sendMsg(msg);

		addBookList(humanObj,toBookList);

		Event.fire(EventKey.EQUIP_BOX_OPEN_NUM, "humanObj", humanObj, "num", num);
	}

	public void addBookList(HumanObject humanObj, List<Integer> snList){
		MsgEquip.equip_book_update_s2c.Builder msg = MsgEquip.equip_book_update_s2c.newBuilder();
		JSONArray bookList = null;

		// 创建已存在的appearanceFilter集合
		Set<Integer> existingFilters = humanObj.operation.appearanceSet;
		for(int sn : snList) {
			ConfEquipment confEquipment = ConfEquipment.get(sn);
			if(confEquipment == null) {
				continue;
			}

			// 检查appearanceFilter是否重复
			if(existingFilters.contains(confEquipment.appearanceFilter)) {
				continue;
			}
			if(confEquipment.part == weapon || confEquipment.part == hat || confEquipment.part == face) {
				if(bookList == null){
					bookList = Utils.toJSONArray(StringZipUtils.unzip(humanObj.getHumanExtInfo().getEquipBookList()));
				}
				Map<Integer,Integer> defaultFigureMap = Utils.jsonToMapIntInt(humanObj.getHuman3().getDefaultEquipFigureMap());
				defaultFigureMap.put(confEquipment.part,sn);
				humanObj.getHuman3().setDefaultEquipFigureMap(Utils.mapIntIntToJSON(defaultFigureMap));
				humanObj.getHuman3().update();
				Map<Integer,Integer> figureMap = Utils.jsonToMapIntInt(humanObj.getHuman2().getEquipFigureMap());
				int figureSn = figureMap.getOrDefault(confEquipment.part,0);
				if(figureSn == 0) {
					equip_figure_list_c2s(humanObj);
				}

				bookList.add(sn);
				existingFilters.add(confEquipment.appearanceFilter);
				msg.addAddList(sn);
			}
		}
		if(msg.getAddListCount() > 0){
			String bookListStr = bookList.toJSONString();
			if(bookListStr.length()>=2048){
				bookListStr = StringZipUtils.zip(bookListStr);
			}
			humanObj.getHumanExtInfo().setEquipBookList(bookListStr);
			humanObj.sendMsg(msg);
		}
	}

	public void equip_book_list_c2s(HumanObject humanObj) {
		MsgEquip.equip_book_list_s2c.Builder msg = MsgEquip.equip_book_list_s2c.newBuilder();
		JSONArray bookList = Utils.toJSONArray(StringZipUtils.unzip(humanObj.getHumanExtInfo().getEquipBookList()));
		for(Object sn : bookList){
			msg.addGotList(Utils.intValue(sn));
		}
		humanObj.sendMsg(msg);
	}


	public void equip_figure_list_c2s(HumanObject humanObj) {
		MsgEquip.equip_figure_list_s2c.Builder msg = MsgEquip.equip_figure_list_s2c.newBuilder();
		Map<Integer,Integer> figureMap = Utils.jsonToMapIntInt(humanObj.getHuman2().getEquipFigureMap());
		//遍历weapon到face的部位不存在或者value=0就去默认取
		for(int i = weapon; i <= face; ++i){
			int sn = figureMap.getOrDefault(i,FIGURE_DEFAULT);
			msg.addFigureList(HumanManager.inst().to_p_key_value(i,sn));
		}
		humanObj.sendMsg(msg);
	}

	public void equip_figure_c2s(HumanObject humanObj, Define.p_key_value figure) {
		int part = (int)figure.getK();
		int sn = (int)figure.getV();
		if(part < weapon || part > face){
			return;
		}
		Human human = humanObj.getHuman();
		Human2 human2 = humanObj.getHuman2();
		if(sn == FIGURE_NONE || sn == FIGURE_DEFAULT){
			Map<Integer,Integer> figureMap = Utils.jsonToMapIntInt(human2.getEquipFigureMap());
			figureMap.put(part,sn);
			human2.setEquipFigureMap(Utils.mapIntIntToJSON(figureMap));
			MsgEquip.equip_figure_s2c.Builder msg = MsgEquip.equip_figure_s2c.newBuilder();
			msg.setFigureList(figure.toBuilder());
			humanObj.sendMsg(msg);
			human.update();
			return;
		}
		ConfEquipment confEquipment = ConfEquipment.get(sn);
		if(confEquipment == null){
			return;
		}
		if(confEquipment.part != part){
			return;
		}
		JSONArray bookList = Utils.toJSONArray(StringZipUtils.unzip(humanObj.getHumanExtInfo().getEquipBookList()));
		boolean contains = false;
		for (int i = 0; i < bookList.size(); i++) {
			Object item = bookList.get(i);
			if (item instanceof Number && ((Number) item).intValue() == sn) {
				contains = true;
				break;
			}
		}
		if (!contains) {
			if(confEquipment.unlockLevel != 0){
				if(human.getLevel() < confEquipment.unlockLevel){
					return;
				}
			}
		}

		Map<Integer,Integer> figureMap = Utils.jsonToMapIntInt(human2.getEquipFigureMap());
		figureMap.put(part,sn);
		human2.setEquipFigureMap(Utils.mapIntIntToJSON(figureMap));
		MsgEquip.equip_figure_s2c.Builder msg = MsgEquip.equip_figure_s2c.newBuilder();
		msg.setFigureList(figure.toBuilder());
		humanObj.sendMsg(msg);
		human.update();
	}

	public void equip_select_c2s(HumanObject humanObj, long equipId) {
		MsgEquip.equip_select_s2c.Builder msg = MsgEquip.equip_select_s2c.newBuilder();
		Map<Long,EquipInfo> equipBoxMap = EquipInfo.fromMapJsonString(humanObj.getHumanExtInfo().getEquipInfo());
		EquipInfo equipInfo = equipBoxMap.get(equipId);
		if(equipInfo == null){
			return;
		}
		Define.p_equip.Builder p_equip = equipInfo.toProtoBuilder(humanObj.getHuman2().getEquipTab());
		msg.setEquipInfo(p_equip);
		humanObj.sendMsg(msg);
	}

	/**
	 * 装备标签信息
	 */
	public void equip_tab_info_c2s(HumanObject humanObj) {
		MsgEquip.equip_tab_info_s2c.Builder msg = MsgEquip.equip_tab_info_s2c.newBuilder();
		msg.setTab(humanObj.getHuman2().getEquipTab());
		Define.p_key_string.Builder p_key = Define.p_key_string.newBuilder();
		Map<Integer, Equip> equipsMap = humanObj.operation.equipsMap;
		int tabLimit = getTabLimit(humanObj.getHuman().getLevel());
		for (int i = 1; i <= tabLimit; ++i){
			Equip equip = equipsMap.get(i);
			if(equip == null){
				p_key.setK(i);
				p_key.setS("");
			}else {
				p_key.setK(i);
				p_key.setS(equip.getTabName());
			}
			msg.addUnlockList(p_key.build());
		}
		humanObj.sendMsg(msg);
	}

	/**
	 * 装备选择标签
	 */
	public void equip_choose_tab_c2s(HumanObject humanObj, int tab) {
		if(tab == humanObj.getHuman2().getEquipTab()){
			return;
		}
		if(tab < 1 || tab > getTabLimit(humanObj.getHuman().getLevel())){
			return;
		}
		humanObj.getHuman2().setEquipTab(tab);
		humanObj.getHuman().update();
		HumanManager.inst().setPlanTab(humanObj, PlanVo.TAB_EQUIP, tab);
		equip_tab_info_c2s(humanObj);
		equip_info_c2s(humanObj);
		HumanManager.inst().handleRolePlanInfoC2S(humanObj);
		updateEquipPorpCalc(humanObj);
		humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_冒险任务, TaskConditionTypeKey.TASK_TYPE_7);
		humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_成就任务, TaskConditionTypeKey.TASK_TYPE_7);
		ActivityManager.inst().addActivityProgress(humanObj, TaskConditionTypeKey.TASK_TYPE_7, 0);
		HumanManager.inst().getHumanBrief(humanObj, true);
	}

	private int getTabLimit(int level){
		ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.equip_tab_limit.SN);
		String[] strArray = confGlobal.strValue.split("\\|");
		int maxTab = 1;
		for(String str : strArray){
			String[] strs = str.split(",");
			if(level >= Utils.intValue(strs[1])){
				maxTab = Utils.intValue(strs[0]);
			}else {
				break;
			}
		}
		return maxTab;
	}

	/**
	 * 装备修改标签名
	 */
	public void equip_change_tab_name_c2s(HumanObject humanObj, int tab, String name) {
		if(tab < 1 || tab > getTabLimit(humanObj.getHuman().getLevel())){
			return;
		}
		Equip equip = humanObj.operation.equipsMap.get(tab);
		if(equip != null){
			equip.setTabName(name);
			equip.update();
		}else {
			equip = new Equip();
			equip.setHumanId(humanObj.getHumanId());
			equip.setTab(tab);
			equip.setTabName(name);
			equip.persist();
			humanObj.operation.equipsMap.put(tab, equip);
		}
		MsgEquip.equip_change_tab_name_s2c.Builder msg = MsgEquip.equip_change_tab_name_s2c.newBuilder();
		msg.setTab(tab);
		msg.setName(name);
		humanObj.sendMsg(msg);
	}

	/**
	 * 装备宝箱皮肤
	 */
	public void equip_box_skin_c2s(HumanObject humanObj) {
		MsgEquip.equip_box_skin_s2c.Builder msg = MsgEquip.equip_box_skin_s2c.newBuilder();
		msg.setId(humanObj.getHumanExtInfo().getEquipBoxSkin());
		msg.addAllUnlockList(humanObj.getTreasureSkinMap().keySet());
		humanObj.sendMsg(msg);
	}

	/**
	 * 装备修改宝箱皮肤
	 */
	public void equip_change_box_skin_c2s(HumanObject humanObj, int sn) {
		ConfTreasureSkin conf = ConfTreasureSkin.get(sn);
		Map<Integer, TreasureSkinInfo> treasureSkinMap = humanObj.getTreasureSkinMap();
		if (conf.costItem != null && !treasureSkinMap.containsKey(sn)) {
			return;
		}
		MsgEquip.equip_box_skin_s2c.Builder msg = MsgEquip.equip_box_skin_s2c.newBuilder();
        humanObj.getHumanExtInfo().setEquipBoxSkin(sn);
//		humanObj.getHumanExtInfo().update();
        msg.setId(sn);
		msg.addAllUnlockList(treasureSkinMap.keySet());
        humanObj.sendMsg(msg);
	}

	/**
	 * 解锁宝箱皮肤
	 */
	public void equip_box_skin_unlock_c2s(HumanObject humanObj, int skinSn) {
		ConfTreasureSkin conf = ConfTreasureSkin.get(skinSn);
		Map<Integer, TreasureSkinInfo> treasureSkinMap = humanObj.getTreasureSkinMap();
		if (treasureSkinMap.containsKey(skinSn) || conf.costItem == null) {
			return;
		}
		ReasonResult rr = ProduceManager.inst().checkAndCostItem(humanObj, conf.costItem[0], conf.costItem[1], MoneyItemLogKey.皮肤解锁);
		if (!rr.success) {
			Inform.sendMsg_error(humanObj, 3);
			return;
		}
		humanObj.addTreasureSkin(skinSn);
		// 重新计算美观值
		CharmManager.reCalcCharmValue(humanObj, true);
		MsgEquip.equip_box_skin_unlock_s2c.Builder msg = MsgEquip.equip_box_skin_unlock_s2c.newBuilder();
		msg.setCode(0);
		msg.setSn(skinSn);
		humanObj.sendMsg(msg);
	}

	//登入装备宝箱升级处理
	@Listener(EventKey.HUMAN_LOGIN)
	public void onHumanLogin(Param param) {
		HumanObject humanObj = param.get("humanObj");
		checkEquipBox(humanObj);
		long endTime = humanObj.getHumanExtInfo().getEquipBoxETime();
		if(endTime == 0){
			return;
		}
		Long now = Port.getTime();
		if (endTime < now) {
			equipBoxUpgradeFinish(humanObj);
		}else {
			humanObj.startCheckEquipBox(endTime - now);
		}

	}

	private void checkEquipBox(HumanObject humanObj) {
		if(humanObj.operation.equipBoxMap.isEmpty()){
			return;
		}
		if(humanObj.operation.equipsMap.isEmpty()){
			return;
		}
		List<Long> updateIdList = new ArrayList<>();
		for(EquipInfo equipInfo : humanObj.operation.equipBoxMap.values()){
			for(Equip equip : humanObj.operation.equipsMap.values()){
				for (int i = weapon; i <= EquipInfo.max_part; ++i) {
					String jsonString = getEquipPart(equip, i);
					JSONObject jo = Utils.toJSONObject(jsonString);
					if (jo.getLongValue("id") == equipInfo.id) {
						updateIdList.add(equipInfo.id);
						break;
					}
				}
			}
		}
		if(!updateIdList.isEmpty()){
			Log.temp.error("===装备id出现冲突，humanId={}, updateIdList={}", humanObj.id, updateIdList);
			for(long id : updateIdList){
				EquipInfo equipInfo = humanObj.operation.equipBoxMap.get(id);
				humanObj.operation.equipBoxMap.remove(id);
				equipInfo.id = humanObj.idGen();
				humanObj.operation.equipBoxMap.put(equipInfo.id, equipInfo);
			}
		}
	}

	//登入装备宝箱升级处理
	@Listener(EventKey.HUMAN_UPGRADE)
	public void onHumanUpgrade(Param param) {
		HumanObject humanObj = param.get("humanObj");
		if(getTabLimit(humanObj.getHuman().getLevel()-1)!=getTabLimit(humanObj.getHuman().getLevel())){
			equip_tab_info_c2s(humanObj);
		}
	}

	//装备宝箱升级完成
	public void equipBoxUpgradeFinish(HumanObject humanObj) {
		humanObj.getHumanExtInfo().setEquipBoxETime(0);
		humanObj.getHumanExtInfo().setEquipBoxLv(humanObj.getHumanExtInfo().getEquipBoxLv() + 1);
		humanObj.getHumanExtInfo().setEquipBoxExp(0);
//		humanObj.getHumanExtInfo().update();
		equip_box_info_c2s(humanObj);
		Event.fire(EventKey.TASK_CONDITION_TYPE, "humanObj", humanObj, "type", TaskConditionTypeKey.TASK_主线,
				"taskType", TaskConditionTypeKey.TASK_TYPE_20, "paramType", TaskConditionTypeKey.PARAM_TYPE_1);
		humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_冒险任务, TaskConditionTypeKey.TASK_TYPE_20);
		ActivityManager.inst().addActivityProgress(humanObj, TaskConditionTypeKey.TASK_TYPE_20, 0);

		Event.fire(EventKey.UPGRADE_FINISH, "type", HumanManager.Upgrade_Finish_Box, "humanId", humanObj.getHuman().getId(), "guildId", humanObj.getHuman2().getGuildId(), "subType", 0);
	}

	public void EquipBoxSpeedUp(HumanObject humanObj, int speedUpTime) {
		if (humanObj.getHumanExtInfo().getEquipBoxETime() == 0) {
			return;
		}
		humanObj.getHumanExtInfo().setEquipBoxETime(humanObj.getHumanExtInfo().getEquipBoxETime() - speedUpTime*Time.SEC);
//		humanObj.getHumanExtInfo().update();
		if(humanObj.getHumanExtInfo().getEquipBoxETime() > Port.getTime()){
			equip_box_info_c2s(humanObj);
		}
		humanObj.periodCheckerSpeedUp(PeriodKey.periodCheckEquipBox, speedUpTime*Time.SEC, null);
	}

	public void giveEquip(HumanObject humanObj, List<ProduceVo> voList, MoneyItemLogKey log) {
		List<Integer> toBookList = new ArrayList<>();//添加图鉴
		for (ProduceVo produceVo : voList){
			ConfEquipment confEquipment = ConfEquipment.get(produceVo.itemSn);
			if(confEquipment == null || confEquipment.preAttr.length < 2){
				Log.game.error("ConfEquipment找不到对应的配置,sn={},log={}", produceVo.itemSn,log);
				continue;
			}
			EquipInfo equipInfo = new EquipInfo(humanObj.idGen());
			equipInfo.sn = confEquipment.sn;
			equipInfo.lv = confEquipment.level;

			PropCalc baseAttr = new PropCalc();
			PropCalc randAttr = new PropCalc();

			//基础属性
			int groupId = 1000+equipInfo.lv;
			if(confEquipment.part != weapon){
				groupId = 2000+equipInfo.lv;
			}
			List<ConfEquipmentAttr> confAttr = GlobalConfVal.getConfEquipmentAttr(groupId);
			for (ConfEquipmentAttr conf : confAttr){
				int atrr = conf.value[1];
				String[] multiple = confEquipment.multiple.split("\\|");
				//属性倍率map，key为属性id，value为倍率，不存在取key为0的值为倍率
				Map<Integer, Float> multipleMap = new HashMap<>();
				for(String str : multiple){
					String[] strs = str.split(",");
					multipleMap.put(Utils.intValue(strs[0]), Utils.floatValue(strs[1]));
				}
				float multipleValue = multipleMap.containsKey(conf.attrId) ? multipleMap.get(conf.attrId) : multipleMap.get(0);
				atrr = (int)Math.ceil(atrr*multipleValue);
				baseAttr.plus(conf.attrId, BigDecimal.valueOf(atrr));
			}

			//随机属性
			if(confEquipment.number > 0 && confEquipment.advanced > 0){
				List<ConfEquipmentAttr> confRandAttr = GlobalConfVal.getConfEquipmentAttr(confEquipment.advanced);
				if(confRandAttr.size() < confEquipment.number){
					Log.game.error("ConfEquipmentAttr找不到对应的配置, advanced={}, number={}", confEquipment.advanced, confEquipment.number);
					return;
				}
				int[] weight = new int[confRandAttr.size()];
				for(int i = 0; i < confRandAttr.size(); ++i){
					weight[i] = confRandAttr.get(i).pro;
				}
				for(int i = 0; i < confEquipment.number; ++i){
					int index2 = Utils.randomByWeight(weight);
					ConfEquipmentAttr conf = confRandAttr.get(index2);
					int atrr = Utils.random(conf.value[0],conf.value[1]+1);
					randAttr.plus(conf.attrId, BigDecimal.valueOf(atrr));
					weight[index2] = 0;
				}
			}

			equipInfo.baseAttr = baseAttr;
			equipInfo.randAttr = randAttr;

			if(!EquipManager.inst().addToEquipBox(humanObj, equipInfo)){
				return;
			}
			toBookList.add(equipInfo.sn);
			MsgEquip.equip_change_s2c.Builder msg2 = MsgEquip.equip_change_s2c.newBuilder();
			msg2.setType(EquipManager.ADD);
			msg2.setSubType(0);
			Define.p_equip.Builder p_equip = equipInfo.toProtoBuilder(0);
			msg2.addChangeList(p_equip);
			humanObj.sendMsg(msg2);

		}
		EquipManager.inst().addBookList(humanObj, toBookList);
	}

	public void addAppearance(HumanObject humanObj, List<ProduceVo> voList) {
		for(ProduceVo vo : voList){
			EquipManager.inst().addBookList(humanObj, Collections.singletonList(vo.itemSn));
		}
	}

	public void fixDuplicateEquipBook(HumanObject humanObj) {
		JSONArray bookList = Utils.toJSONArray(StringZipUtils.unzip(humanObj.getHumanExtInfo().getEquipBookList()));

		Set<Integer> uniqueFilters = new HashSet<>();
		JSONArray newBookList = new JSONArray();

		// 遍历bookList进行去重
		for (int i = 0; i < bookList.size(); i++) {
			int bookId = bookList.getIntValue(i);
			int filter = ConfEquipment.get(bookId).appearanceFilter;
			// 如果这个filter还没出现过,则保留该装备
			if (uniqueFilters.add(filter)) {
				newBookList.add(bookId);
			}
		}
		// 保存去重后的结果
		String newBookListStr = newBookList.toJSONString();
		if(newBookListStr.length()>=2048){
			newBookListStr = StringZipUtils.zip(newBookListStr);
		}
		humanObj.getHumanExtInfo().setEquipBookList(newBookListStr);
	}

	public void equip_filter_info_c2s(HumanObject humanObj) {
		Map<Integer, Integer> filterMap = humanObj.operation.equipFilterMap;
		MsgEquip.equip_filter_info_s2c.Builder msg = MsgEquip.equip_filter_info_s2c.newBuilder();
		for (Map.Entry<Integer, Integer> entry : filterMap.entrySet()) {
			msg.addFilterAttrs(HumanManager.inst().to_p_key_value(entry.getKey(), entry.getValue()));
		}
		humanObj.sendMsg(msg);
	}

	public void equip_filter_attr_c2s(HumanObject humanObj, int order, int attrId) {
		ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.lamp_attr_filter.SN);
		Map<Integer, Integer> boxLvLimitMap = new HashMap<>();
		for (String str : confGlobal.strArray) {
			String[] strs = str.split(",");
			boxLvLimitMap.put(Utils.intValue(strs[0]), Utils.intValue(strs[1]));
		}
		if(!boxLvLimitMap.containsKey(order) || humanObj.getHumanExtInfo().getEquipBoxLv() < boxLvLimitMap.get(order)){
			Log.game.error("装备属性筛选错误，order={}, attrId={}, humanObj.getHumanExtInfo().getEquipBoxLv()={}", order, attrId, humanObj.getHumanExtInfo().getEquipBoxLv());
			return;
		}
		if(attrId != 0 && !Arrays.stream(confGlobal.intArray).boxed().collect(Collectors.toList()).contains(attrId)){
			Log.game.error("装备属性筛选错误，order={}, attrId={}", order, attrId);
			return;
		}
		Map<Integer, Integer> filterMap = humanObj.operation.equipFilterMap;
		filterMap.put(order, attrId);
		humanObj.getHumanExtInfo().setEquipFilterMap(Utils.mapIntIntToJSON(filterMap));
		equip_filter_info_c2s(humanObj);
	}

	public EquipInfo createEquip(HumanObject humanObj, int EquipSn) {
		int lv = humanObj.getHuman().getLevel();
		EquipInfo equipInfo = new EquipInfo(humanObj.idGen());
		equipInfo.sn = EquipSn;
		initEquipLevel(equipInfo, lv);
		ConfEquipment confEquipment = ConfEquipment.get(EquipSn);
		if(!initEquipAttributes(humanObj,equipInfo, confEquipment)){
			return null;
		}
		if(!EquipManager.inst().addToEquipBox(humanObj, equipInfo)){
			return null;
		}
		//添加图鉴
		List<Integer> toBookList = new ArrayList<>();
		toBookList.add(equipInfo.sn);
		EquipManager.inst().addBookList(humanObj, toBookList);
		return equipInfo;
	}
}