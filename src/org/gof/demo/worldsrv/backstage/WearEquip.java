package org.gof.demo.worldsrv.backstage;

import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.interfaces.ISerilizable;

import java.io.IOException;
import java.util.List;

/**
 *装备
 */
public class WearEquip implements ISerilizable {
    
//        public  int sn ;	//装备位编号SN
        public  Integer qianghuaLv ;	//精炼等级
        public  String chongxingLv ;	//铭刻等级
        public List<String>  gemGroup;	//宝石数组
        public  String itemName ;	//对应物品名称
        public  Integer quality ;//品质
        public  Integer rcast ;//重铸

    public WearEquip() {
    }

    @Override
    public void writeTo(OutputStream out) throws IOException {
//        out.write(sn);
        out.write(qianghuaLv);
        out.write(chongxingLv);
        out.write(gemGroup);
        out.write(itemName);
        out.write(quality);
        out.write(rcast);
    }

    @Override
    public void readFrom(InputStream in) throws IOException {

//        sn = in.read();
        qianghuaLv = in.read();
        chongxingLv = in.read();
        gemGroup = in.read();
        itemName = in.read();
        quality = in.read();
        rcast = in.read();
    }
}
