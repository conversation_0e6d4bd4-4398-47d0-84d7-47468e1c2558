package org.gof.demo.worldsrv.backstage;

import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.interfaces.ISerilizable;

import java.io.IOException;


/**
 * 出战魂灵
 */
public class FightSoul implements ISerilizable {

    public String name;//名称
    public int trainLv;//培养等级
    public int integral;//洗髓积分

    public FightSoul() {
    }

    @Override
    public void writeTo(OutputStream out) throws IOException {
        out.write(name);
        out.write(trainLv);
        out.write(integral);
    }

    @Override
    public void readFrom(InputStream in) throws IOException {
        name=in.read();
        trainLv=in.read();
        integral=in.read();
    }
}
