package org.gof.demo.worldsrv.backstage;

import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.interfaces.ISerilizable;

import java.io.IOException;

/**
 * 魂骨
 */
public class SoulBoneLoop implements ISerilizable {
    public String soulBoneName;//魂骨名称
    public int soulQuality;//魂骨品质
    public int strengthenLv;//强化等级
    public int stairsLv;//升阶等级

    public SoulBoneLoop() {
    }

    @Override
    public void writeTo(OutputStream out) throws IOException {
        out.write(soulBoneName);
        out.write(soulQuality);
        out.write(strengthenLv);
        out.write(stairsLv);
    }

    @Override
    public void readFrom(InputStream in) throws IOException {
        soulBoneName = in.read();
        soulQuality = in.read();
        strengthenLv = in.read();
        stairsLv = in.read();
    }
}
