package org.gof.demo.worldsrv.backstage;

import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.interfaces.ISerilizable;

import java.io.IOException;

/**
 * 背包物品
 */
public class PropBag implements ISerilizable {

    private String name;
    private int count;

    public PropBag() {
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    @Override
    public void writeTo(OutputStream out) throws IOException {
        out.write(name);
        out.write(count);
    }

    @Override
    public void readFrom(InputStream in) throws IOException {
        name = in.read();
        count = in.read();
    }
}
