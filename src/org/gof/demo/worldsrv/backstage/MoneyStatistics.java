package org.gof.demo.worldsrv.backstage;

import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.interfaces.ISerilizable;

import java.io.IOException;


/**
 * 货币统计
 */
public class MoneyStatistics implements ISerilizable {

    public String moneyName;
    public int moneyNum;

    public MoneyStatistics() {
    }

    @Override
    public void writeTo(OutputStream out) throws IOException {
        out.write(moneyName);
        out.write(moneyNum);

    }

    @Override
    public void readFrom(InputStream in) throws IOException {
        moneyName = in.read();
        moneyNum = in.read();
    }
}
