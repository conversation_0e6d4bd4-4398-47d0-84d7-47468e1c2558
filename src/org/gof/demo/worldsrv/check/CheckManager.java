package org.gof.demo.worldsrv.check;

import com.alibaba.fastjson.JSONObject;
import org.gof.core.Port;
import org.gof.core.support.*;
import org.gof.core.support.observer.Listener;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.observer.EventKey;

public class CheckManager extends ManagerBase {

    public static CheckManager inst() { return inst(CheckManager.class); }


    public void checkCreateLogin(JSONObject jo){
        if(!S.isAdmin){
            return;
        }
        CheckAdminServiceProxy prx = CheckAdminServiceProxy.newInstance(NodeAdapter.admin());
        prx.checkCreateLogin(jo.toJSONString());

    }

    public void checkLogin(JSONObject jo) {
        if(!S.isAdmin){
            return;
        }
        CheckAdminServiceProxy prx = CheckAdminServiceProxy.newInstance(NodeAdapter.admin());
        prx.checkLogin(jo.toJSONString());
    }


    public void checkLongin(long humanId, String account, boolean result, ECheckTypeKey eCheckType) {
//        if(!humanObj.getHuman().isCheckRobot()){
//            return;
//        }
        JSONObject jo = new JSONObject();
        jo.put("result", result);
        jo.put("humanId", humanId);
        jo.put("account", account);
        jo.put("eCheckType", eCheckType.getValue());
        CheckWorldServiceProxy prx = CheckWorldServiceProxy.newInstance();
        prx.checkLoginResult(jo.toJSONString());
    }

    @Listener(EventKey.HUMAN_LOGIN)
    public void onHumanLogin(Param params){
        HumanObject humanObj = params.get("humanObj");
        if(!humanObj.getHuman().isCheckRobot()){
            return;
        }
        CheckWorldServiceProxy prx = CheckWorldServiceProxy.newInstance();
        prx.addOnlineHumanId(humanObj.id);
    }


    public void checkAdminServer(){
//        CheckAdminServiceProxy prx = CheckAdminServiceProxy.newInstance(NodeAdapter.admin());//bridgeInstance(Distr.NODE_ADMIN_SERVER);
//        prx.checkAdminService();
//        prx.listenResult(this::_result_checkAdminServer);
    }

    private void _result_checkAdminServer(boolean timeout, Param results, Param context){
//        if(!timeout){
//            Log.temp.info("===远程{}", results.getBoolean("result"));
//            return;
//        }
//        Log.temp.info("===远程链接超时");
//        Port.getCurrent().getNode().addRemoteNode(Distr.NODE_ADMIN_SERVER);
    }
}
