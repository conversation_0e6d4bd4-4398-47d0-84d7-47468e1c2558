package org.gof.demo.worldsrv.check;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.gof.core.*;
import org.gof.core.gen.proxy.DistrClass;
import org.gof.core.gen.proxy.DistrMethod;
import org.gof.core.scheduler.ScheduleTask;
import org.gof.core.support.*;
import org.gof.demo.seam.account.AccountManager;
import org.gof.demo.seam.account.AccountObject;
import org.gof.demo.seam.account.AccountService;
import org.gof.demo.support.DataPreloader;
import org.gof.demo.support.IPersistObj;
import org.gof.demo.worldsrv.common.GamePort;
import org.gof.demo.worldsrv.common.GameServiceBase;
import org.gof.demo.worldsrv.entity.Human;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.human.HumanGlobalServiceProxy;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.support.C;
import org.gof.demo.worldsrv.support.D;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.Util;
import org.gof.demo.worldsrv.support.observer.Event;
import org.gof.demo.worldsrv.support.observer.EventKey;
import org.gof.platform.ConstPf;
import org.gof.platform.LogPF;

import java.util.*;

@DistrClass(
		servId = D.SERV_CHECK_WORLD,
		importClass = {List.class},
		localOnly = false
)
public class CheckWorldService extends GameServiceBase {

	private TickTimer scheduleTimer = new TickTimer(10000);//调度的处理
	// 检测类型, 返回结果
	private Map<Integer, String> typeResultMap = new HashMap<>();
	// 检测类型, 开始检测时间
	private Map<Integer, Long> typeOpenTimeMap = new HashMap<>();
	// 检测创建的玩家id, 玩家账号
	private Map<Long, String> humanIdAccountMap = new HashMap<>();

	private String accountKey = "yxjqrs";

	private int[] roleSnArr = new int[]{11,12,13};

	private List<Long> humanIdList = new ArrayList<>();

	private LinkedList<Long> onLineHumanIdList = new LinkedList<>();

	private TickTimer ttimer = new TickTimer(1000);//调度的处理

	private TickTimer ttconn = new TickTimer(60 * Time.SEC);//调度的处理

	public static List<Integer> serverIdListNow = new ArrayList<>();

	private int index = 0;
	private int timeoutNum = 0;

//	private List<Long> onLineHumanIdList = new ArrayList<>();
	/**
	 * 构造函数
	 *
	 * @param port
	 */
	public CheckWorldService(GamePort port) {
		super(port);
	}

	@Override
	public void pulseOverride() {

	}



	@Override
	protected void init() {
		if(S.isAdmin || S.isBridge || S.isCross){
			Log.temp.error("===启动服务器，判定不是游戏服， isAdmin={}，isBridge={}，isGameLeagueOpen={}", S.isAdmin, S.isBridge, S.isGameLeagueOpen);
			return;
		}
		JSONObject jo = new JSONObject();
		String remoteNodeId = D.NODE_WORLD_BRIDGE_PREFIX + C.GAME_SERVER_ID;
		jo.put("nodeId", remoteNodeId);
		jo.put("serverId", C.GAME_SERVER_ID);

		String addrWorld = Distr.getNodeAddr(Distr.NODE_DEFAULT);
		String ipWorld = addrWorld.replace("tcp://", "").split(":")[0];
		jo.put("httpUrl", Utils.createStr("{}:{}", ipWorld, ConstPf.HTTP_PORT1));

//		Log.game.info("准备注册游戏服信息到中心服！serverId={}，nodeId={}", C.GAME_SERVER_ID, remoteNodeId);
//		CheckAdminServiceProxy prx = CheckAdminServiceProxy.adminInstance(NodeAdapter.admin());
//		prx.checkLink(jo.toJSONString());

		if(S.isRedis){
			Log.temp.info("===代码未实现，检测的先不处理");
			return;
		}
		String whereSql = Utils.createStr(" where {} = {}", Human.K.checkRobot, true);
		PersistHuman persistHuman = new PersistHuman();
		DataPreloader.PreloadByPage(persistHuman, persistHuman.getTable(),
				persistHuman.getPageNum(), whereSql);
	}

	private class PersistHuman implements IPersistObj {
		public String getTable() {
			return Human.tableName;
		}

		public int getPageNum() {
			return 1000;
		}

		public void load(Record r) {
			Human human = new Human(r);
//			human.remove();
			humanIdAccountMap.put(human.getId(), human.getAccount());
			humanIdList.add(human.getId());
		}
	}

	@DistrMethod
	public void checkServer(String json) {
		Log.temp.info("==={}", json);
	}


	@DistrMethod
	public void checkCreateLogin(String json) {
		long nowTime = Port.getTime();
		JSONObject jo = Utils.toJSONObject(json);
		int type = jo.getIntValue("type");

		// 检测创建登录
		String account = "";
		int num = 1;
		if(humanIdList.isEmpty()){
			account = Utils.createStr("{}00{}", accountKey, 1);
		} else {
			long humanId = humanIdList.get(humanIdList.size()-1);
			String accountNow = humanIdAccountMap.get(humanId);
			num = Utils.intValue(accountNow.replaceAll(accountKey, "")) + 1;
			account = Utils.createStr("{}00{}", accountKey, num);
		}
		boolean test = false;
		boolean isCheck = true;
		long connId = Config.ROBOT_FROM_ADMIN_CONN_ID;
		AccountService serv = port.getNode().getPort(Distr.PORT_DEFAULT).getServices(Distr.SERV_GATE);
		ConnectionStatus connStatus = new ConnectionStatus();
		connStatus.status = ConnectionStatus.STATUS_GATE;
		connStatus.account = account;

		CallPoint connPoint = new CallPoint(Distr.NODE_DEFAULT, Distr.PORT_DEFAULT, serv.getId());

		AccountObject obj = new AccountObject(connId, connStatus, connPoint);
		obj.eCheckType = ECheckTypeKey.checkCreateLogin;
		obj.serverId = C.GAME_SERVER_ID;

		typeOpenTimeMap.put(ECheckTypeKey.checkCreateLogin.value, nowTime);
		AccountManager.inst().characterCreate(obj, test, isCheck);



//		int serverId = Utils.intValue(Config.GAME_SERVER_ID); //服务器ID
//		Define.ELanguage eLanguage = Define.ELanguage.zh;
//		String channel = "1";
//
//		Param param = new Param();
//		param.put("connPoint", connPoint);
//		param.put("connStatus", connStatus);
//		param.put("channel", channel);
//		param.put("account", account);
//		param.put("serverId", serverId);
//		param.put("accountObj", null);
//		param.put("eLanguage", eLanguage);
//		int zone = Util.getServerIdZoneCode(serverId);
//		param.put("zone", zone);
//
//		AccountManager.inst().login(account, channel, param);
	}

	@DistrMethod
	public void checkLogin(String json) {
		long nowTime = Port.getTime();
		JSONObject jo = Utils.toJSONObject(json);
		int type = jo.getIntValue("type");
		if(humanIdList.isEmpty()){
			Log.temp.error("===检测没有角色账号，humanIdList={}", humanIdList);
			return;
		}
		typeOpenTimeMap.put(type, nowTime);
		// 检测登录
		// TODO 先从已经创建中随机吧
		long humanId = humanIdList.get(Utils.random(humanIdList.size()));
		String account = humanIdAccountMap.get(humanId);

		boolean test = false;
		boolean isCheck = true;
		long connId = Config.ROBOT_FROM_ADMIN_CONN_ID;
		ConnectionStatus connStatus = new ConnectionStatus();
		connStatus.status = ConnectionStatus.STATUS_GATE;
		connStatus.account = account;

		AccountService serv = port.getNode().getPort(Distr.PORT_DEFAULT).getServices(Distr.SERV_GATE);
		if(serv == null){
			Log.temp.info("===serv={}", serv);
		}

		CallPoint connPoint = new CallPoint(Distr.NODE_DEFAULT, Distr.PORT_DEFAULT, serv.getId());

		AccountObject obj = new AccountObject(connId, connStatus, connPoint);
		obj.eCheckType = ECheckTypeKey.checkLogin;
		obj.humanId = humanId;

		Param param = new Param();
		param.put("connPoint", connPoint);
		param.put("connStatus", connStatus);
		param.put("channel", obj.status.channel);
		param.put("account", account);
		param.put("serverId", Utils.intValue(Config.GAME_SERVER_ID));
		param.put("accountObj", null);
		param.put("eLanguage", Define.ELanguage.zh);
		param.put("zone", 0);
		AccountManager.inst().login(account, obj.status.channel, param);
	}

	@DistrMethod
	public void checkLoginResult(String json) {
		long nowTime = Port.getTime();
		JSONObject jo = Utils.toJSONObject(json);
		int eCheckType = jo.getIntValue("eCheckType");
		typeOpenTimeMap.remove(eCheckType);

		String gameServerId = Config.GAME_SERVER_ID;
		jo.put("serverId", gameServerId);

		Log.temp.info("===json={}", json);

		if(eCheckType == ECheckTypeKey.checkCreateLogin.getValue()){
			CheckAdminServiceProxy prx = CheckAdminServiceProxy.newInstance(NodeAdapter.admin());
			prx._result_check(jo.toJSONString());
		} else if(eCheckType == ECheckTypeKey.checkLogin.getValue()){
			CheckAdminServiceProxy prx = CheckAdminServiceProxy.newInstance(NodeAdapter.admin());
			prx._result_check(jo.toJSONString());
		}

		CheckAdminServiceProxy prx = CheckAdminServiceProxy.newInstance(NodeAdapter.admin());
		prx._result_check(jo.toJSONString());
	}

	@DistrMethod
	public void addOnlineHumanId(long humanId){
		if(onLineHumanIdList.contains(humanId)){
			return;
		}
		onLineHumanIdList.add(humanId);
		Log.temp.info("===添加检测玩家humanId={}， 在线检测集合list={}", humanId, onLineHumanIdList.size());
	}

	@DistrMethod
	public void removeOnlineHumanId(long humanId){
		if(onLineHumanIdList.contains(humanId)){
			onLineHumanIdList.remove(humanId);
		}
		Log.temp.info("===移除检测玩家humanId={}， 在线检测集合list={}", humanId, onLineHumanIdList);
	}



	@DistrMethod
	public void payResult(String json) {
		JSONObject jo = Util.toJSONObject(json);
		LogPF.platform.info("记录收到充值， jo={}", jo);
		Event.fire(EventKey.PAY, jo);
	}

	@DistrMethod
	public void update(String json) {
		JSONObject jo = Utils.toJSONObject(json);

	}

	@DistrMethod
	public void update1(Object... objs) {

	}

	@DistrMethod
	public void update2(String str) {

	}
}