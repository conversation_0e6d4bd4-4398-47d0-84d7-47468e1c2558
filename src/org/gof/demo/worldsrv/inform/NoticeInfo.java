package org.gof.demo.worldsrv.inform;

import org.gof.core.Port;
import org.gof.core.support.TickTimer;

public class NoticeInfo {
    public long time;// 时间
    public int split;// 间隔秒
    public int count;//次数
    public String content;// 字符串 内容
    public TickTimer gmNotice = new TickTimer();

    public NoticeInfo(long time,int split,int count,String content){
        this.content = content;
        this.count = count;
        this.split = split;
        this.time = time;
        long now = Port.getTime();
        if (now > time) {
            time = now;
        }
        gmNotice = new TickTimer(time, split * 1000L, true);
    }

    public void noticeToClient(){
        if (count <= 0) {
            gmNotice.stop();
            return;
        }
//        Inform.all(Inform.通告滚动, content, null, null, null);
//        Inform.all(Inform.系统, content, null, null, null);
        count --;
    }
}
