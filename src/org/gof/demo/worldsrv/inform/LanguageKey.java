package org.gof.demo.worldsrv.inform;

/**
 * Language表格的sn
 */
public class LanguageKey {
    public static final int FlyHybridAllow = 205076;// 同意成为对方的飞宠搭档
    public static final int FlyHybridRefuse = 205077;// 拒绝成为对方的飞宠搭档
    public static final int FlyPermissionAllow = 205079;// 同意对方使用飞宠
    public static final int FlyPermissionRefuse = 205080;// 拒绝对方使用飞宠
    public static final int DoubleChapterFaild = 204790;//不好意思，暂时无法成为您的战友
    public static final int DoubleChapterChatFaild = 204791;//对方设置了不接收私聊邀请，已将邀请发送至对方的助战管理
    public static final int DoubleChapterLimit = 204792;//对方设置了邀请权限，发送失败
    public static final int DoubleChapterFullLimit = 204793;//对方的助战人数已达上限
    public static final int DoubleChapterAlreadySend = 204840;//已发送过申请，请耐心等待
    public static final int DoubleChapterAlreadyAdd = 204839;//对方已是您的战友，不能重复申请
    public static final int DoubleChapterAlreadyAgree = 204789;//已同意成为您的战友，快去挑战吧
}
