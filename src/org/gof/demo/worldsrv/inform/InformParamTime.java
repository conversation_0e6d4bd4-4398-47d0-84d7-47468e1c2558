package org.gof.demo.worldsrv.inform;

import com.alibaba.fastjson.JSONObject;
import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.interfaces.ISerilizable;

import java.io.IOException;

/**
 * 用于时间显示xx小时xx分钟xx秒，为了把TimeUtil.msecToTime的拼接过程移至客户端
 *
 * @Author: chenkeyi
 * @Date: 2023/7/27 14:16
 */
public class InformParamTime implements ISerilizable {
    private long time = 0L;

    public InformParamTime() {}

    public InformParamTime(long time) {
        this.time = time;
    }

    public InformParamTime(long time, String timePattern) {
        this.time = time;
    }

    public JSONObject toJSONObj() {
        JSONObject jsonObj = new JSONObject();
//        jsonObj.put("valueParseType", Inform.PARAM_JSON_PARSETYPE_TIME);
        jsonObj.put("value", time);
        return jsonObj;
    }

    @Override
    public void writeTo(OutputStream out) throws IOException {
        out.write(time);
    }

    @Override
    public void readFrom(InputStream in) throws IOException {
        time = in.read();
    }
}
