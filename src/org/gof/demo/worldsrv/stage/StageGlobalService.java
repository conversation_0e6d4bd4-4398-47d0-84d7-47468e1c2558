package org.gof.demo.worldsrv.stage;

import com.google.protobuf.Message;
import org.gof.core.CallPoint;
import org.gof.core.Port;
import org.gof.core.Service;
import org.gof.core.connsrv.ConnectionProxy;
import org.gof.core.gen.proxy.DistrClass;
import org.gof.core.gen.proxy.DistrMethod;
import org.gof.core.scheduler.ScheduleMethod;
import org.gof.core.support.*;
import org.gof.demo.battlesrv.support.Vector2D;
import org.gof.demo.support.NodeAdapter;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.character.HumanObjectServiceProxy;
import org.gof.demo.worldsrv.common.DataResetService;
import org.gof.demo.worldsrv.entity.Human;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.human.HumanGlobalServiceProxy;
import org.gof.demo.worldsrv.human.HumanManager;
import org.gof.demo.worldsrv.human.HumanStatusManager;
import org.gof.demo.worldsrv.support.D;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.observer.Event;
import org.gof.demo.worldsrv.support.observer.EventKey;

import java.util.*;
import java.util.Map.Entry;


@DistrClass(
        servId = D.SERV_STAGE_GLOBAL,
        importClass = {HumanObject.class,  List.class, Vector2D.class, Message.class,
                Param.class, Map.class, StageHistory.class},
        localOnly = false
)
public class StageGlobalService extends Service {

    //地图信息集合<stageId,StageGlobalInfo>
    private final Map<Long, StageGlobalInfo> infos = new HashMap<Long, StageGlobalInfo>();

    private final Map<Integer, Map<Integer, List<Long>>> zoneMapSnIdMap = new HashMap<>();

    // 分线系统 key是主地图的 sn。 value 是对应的所有分线地图
//    private final Map<Integer, Map<Long, StageGlobalInfo>> stageMulLines = new HashMap<Integer, Map<Long, StageGlobalInfo>>();
    // 区, 地图sn, stageId, 数据
    private final Map<Integer, Map<Integer, Map<Long, StageGlobalInfo>>> zoneStagePartMap = new HashMap<>();
    // 分线系统 key是主地图的 sn。 分线数
    private final Map<Integer, Map<Integer, List<Integer>>> zoneMapPartNumMap = new HashMap<>();

    // 玩家id对应玩家去哪个线程
    private Map<Long, Integer> humanLineNumMap = new HashMap<>();
    // 队伍id,队伍中玩家
    private Map<Long, List<Long>> teamIdHumanIdListMap = new HashMap<>();
    // 玩家对应队伍id
    private Map<Long, Long> humanIdTeamIdMap = new HashMap<>();
    // 队伍id对应玩家去哪个线程
    private Map<Long, Integer> teamIdLineNumMap = new HashMap<>();

    //打印在线人数计时器
    private TickTimer onlineLogTimer = new TickTimer(5 * Time.MIN);
    //打印在线人数计时器
    private TickTimer onlineLogAllTimer = new TickTimer(15 * Time.MIN);

    public StageGlobalService(Port port) {
        super(port);
    }

    @Override
    public Object getId() {
        return D.SERV_STAGE_GLOBAL;
    }

    /**
     * 注册地图信息
     *
     * @param stageId
     * @param mapSn
     */
    @DistrMethod
    public void infoRegister(long stageId, int mapSn, String stageName, String nodeId, String portId, int zone, int lineNum) {
//        Log.temp.info("===准备注册infoRegister, stageId={}, mapSn={} zone={}, lineNum={}", stageId, mapSn, zone, lineNum);
        if (infos.containsKey(stageId))
            return;


        //创建信息对象并缓存
        StageGlobalInfo info = new StageGlobalInfo(stageId, mapSn, stageName, nodeId, portId, zone);
        info.lineNum = lineNum;
        infos.put(stageId, info);

        //添加分线信息
        if(mapSn == GlobalConfVal.mainSn){
            addStagePart(info);
        }
//        Log.temp.info("===注册地图成功infoRegister, stageId={}, mapSn={} zone={}, lineNum={}", stageId, mapSn, zone, lineNum);
        Event.fireEx(EventKey.STAGE_REGISTER, mapSn,
                "stageSn", mapSn,
                "nodeId", nodeId,
                "portId", portId,
                "stageId", stageId);


    }

    private void addStagePart(StageGlobalInfo info){
        int zone = info.zone;
        int mapSn = info.sn;

        Map<Integer, List<Long>> mapSnIdMap = zoneMapSnIdMap.get(zone);
        if(mapSnIdMap == null){
            mapSnIdMap = new HashMap<>();
            zoneMapSnIdMap.put(zone, mapSnIdMap);
        }
        List<Long> idList = mapSnIdMap.get(mapSn);
        if(idList == null){
            idList = new ArrayList<>();
            mapSnIdMap.put(mapSn, idList);
        }
        if(!idList.contains(info.id)){
            idList.add(info.id);
        }

        Map<Integer, Map<Long, StageGlobalInfo>> stageStageIdInfoMap = zoneStagePartMap.get(zone);
        if(stageStageIdInfoMap == null){
            stageStageIdInfoMap = new HashMap<>();
            zoneStagePartMap.put(zone, stageStageIdInfoMap);
        }

        Map<Long, StageGlobalInfo> infoMap = stageStageIdInfoMap.get(mapSn);
        if (infoMap == null) {
            infoMap = new LinkedHashMap<>();
            stageStageIdInfoMap.put(info.sn, infoMap);
        }
        infoMap.put(info.id, info);
        updateLineNum(mapSn, zone, info.lineNum, false);
    }

    /** 
     * 获取分配的分线数
     * <AUTHOR>
     * @Date 2023/9/20
     * @Param 
     */
    private void updateLineNum(int mapSn, int zone, int lineNum, boolean isRemove){
        Map<Integer, List<Integer>> mapSnLineNumMap = zoneMapPartNumMap.get(zone);
        if(mapSnLineNumMap == null){
            mapSnLineNumMap = new HashMap<>();
            zoneMapPartNumMap.put(zone, mapSnLineNumMap);
        }
        List<Integer> lineNumList = mapSnLineNumMap.get(mapSn);
        if(lineNumList == null){
            lineNumList = new ArrayList<>();
            mapSnLineNumMap.put(mapSn, lineNumList);
        }
        if(isRemove){
            lineNumList.remove(lineNum);
        } else {
            if(!lineNumList.contains(lineNum)){
                lineNumList.add(lineNum);
            }
        }
        if(S.isTestLog) {
            Log.temp.info("===zoneMapPartNumMap={}, infos={}", zoneMapPartNumMap, infos);
        }
    }

    /**
     * 设置副本结束
     *
     * @param stageId
     */
    @DistrMethod
    public void setEnd(long stageId) {
        StageGlobalInfo info = infos.get(stageId);
        if (info != null) {
            info.isEnd = true;
        }
    }

    @DistrMethod
    public void end(long stageId){
        StageGlobalInfo info = infos.get(stageId);
        if (info != null) {
            StageObjectServiceProxy proxy = StageObjectServiceProxy.newInstance(info.nodeId, info.portId, stageId);
            proxy.end();
        }
    }

    /**
     * 注销地图信息
     *
     * @param stageId
     */
    @DistrMethod
    public void infoCancel(long stageId) {
        if(Utils.isDebugMode()){
            Log.temp.info("销毁地图：stageId={}", stageId);
        }
        StageGlobalInfo information = infos.remove(stageId);
        if (information == null) {
            Log.stageCommon.warn("销毁地图时发现地图已不存在：stageId={}", stageId);
            return;
        }
        int zone = information.zone;
        int mapSn = information.sn;
        Map<Integer, Map<Long, StageGlobalInfo>> mapSnStageInfoMap = zoneStagePartMap.get(zone);
        if(mapSnStageInfoMap == null){
            return;
        }
        Map<Long, StageGlobalInfo> stageInfoMap = mapSnStageInfoMap.get(mapSn);
        if(stageInfoMap.containsValue(stageId)){
            stageInfoMap.remove(stageId);
        }
        updateLineNum(mapSn, zone, information.lineNum, true);

//		Event.fireEx(EventKey.STAGE_CANCEL, information.sn,
//				"stageSn", information.sn,
//				"nodeId", port.getCallFromNodeId(),
//				"portId", port.getCallFromPortId(),
//				"stageId", stageId);
    }

    @DistrMethod
    public void destroy(long stageId) {
        StageGlobalInfo info = infos.get(stageId);
        if (info == null) {
            return;
        }

        StageServiceProxy prx = StageServiceProxy.newInstance(info.nodeId, info.portId, D.SERV_STAGE_DEFAULT);
        prx.destroy(stageId);
    }

    /**
     * 玩家登录初始化地图
     * @param humanId
     */
    @DistrMethod
    public void login(long humanId, Param param) {
        Long pid = port.createReturnAsync();
        if(S.isTestLog) {
            Log.stageCommon.error("全局地图登陆StageGlobalService login, pid={},humanId={}, param={}", pid, humanId, param);
        }
//        loginCheckStage(pid, humanId, param);
        Vector2D bornPos = new Vector2D(-1f, -1f);
//        StageHistory bornHistory = getBornStageHistory(param.getString("stageHistory"));
//        long stageId = applyStage(bornHistory.stageId, new Param());

        long stageId = applyStage(GlobalConfVal.mainSn, param);
        if(stageId < 0 || stageId == GlobalConfVal.mainSn){
            createStageLogin(pid, humanId, param);
            return;
        }
        Log.temp.info("===stageId={}, humanId={}, pid={}", stageId, humanId, pid);
        loginStage(pid, humanId, stageId, param);

    }

    private StageHistory getBornStageHistory(String stageHistory) {
        List<StageHistory> stageHistoryList = StageHistory.stageHistoryList(stageHistory);

        StageHistory stageHistoryInfoCommon = null;
//        for (StageHistory stageHistoryInfo : stageHistoryList) {
//            ConfMap conf = ConfMap.get(stageHistoryInfo.mapSn);
//            if(conf == null){
//                Log.temp.error("===ConfMap配表错误， not find sn={}", stageHistoryInfo.mapSn);
//                continue;
//            }
//            if (conf.type.equals(StageMapTypeKey.rep.getS())) {
//                StageGlobalInfo information = this.infos.get(stageHistoryInfo.stageId);
//                if (information == null || information.isEnd) {
//                    //跳过结束的副本{超时或胜利的副本}
//                    continue;
//                }
//                //演武场不返回
//                if(conf.gameType.equals(StageGameTypeKey.competition.getS())){
//                    continue;
//                }
//                return stageHistoryInfo;
//            }
//            if (conf.type.equals(StageMapTypeKey.common.getS())) {
//                stageHistoryInfoCommon = stageHistoryInfo;
//        }
//        }

        return stageHistoryInfoCommon;
    }

    /***
     * 玩家登录初始化地图
     * @param pid
     * @param humanId
     * @param param
     */
    private void loginCheckStage(long pid, long humanId, Param param) {
        Vector2D bornPos = new Vector2D(-1f, -1f);
//        StageHistory bornHistory = getBornStageHistory(param.getString("stageHistory"));
//        long stageId = applyStage(bornHistory.stageId, new Param());

        long stageId = applyStage(GlobalConfVal.mainSn, param);
        Log.temp.info("===stageId={}", stageId);
        loginStage(pid, humanId, stageId, param);
    }

    private void createStageLogin(long pid, long humanId, Param param){
        int zone = Utils.getParamValue(param,"zone", 0);
        int mapSn = GlobalConfVal.mainSn;
        //如果需要分线 并且是一般地图
        //创建新地图
        //获得地图分线数量
        int lineNum = getLineNum(zone, mapSn);
        long stageID = Port.applyId();
//        String portId = StageManager.inst().getStagePortId();
//
//        if(S.isTestLog) {
//            Log.temp.info("====stageid={}, portId={}, lineNum={}", stageID, portId, lineNum);
//        }
//        StageServiceProxy proxy = StageServiceProxy.newInstance(Distr.getNodeId(portId), portId, D.SERV_STAGE_DEFAULT);
//        proxy.createStageCommon(stageID, mapSn, lineNum);
//        proxy.listenResult(this::_result_createStageLogin, "pid", pid, "humanId", humanId, "param", param);

//        String mapName = "主地图";
//        //	提前注册
//        infoRegister(stageID, mapSn, mapName, Distr.getNodeId(portId), portId, zone, lineNum);
    }

    private void _result_createStageLogin(Param results, Param context){
        long stageId = Utils.getParamValue(results, "stageId", 0L);
        long pid = Utils.getParamValue(context, "pid", 0L);
        long humanId = Utils.getParamValue(context, "humanId", 0L);
        Param param = Utils.getParamValue(context, "param", new Param());
        StageGlobalInfo information = this.infos.get(stageId);
        if (information == null) {
            Log.temp.error("===登录出错StageGlobalInfo=null, humanId={}, stageId={}, results={}, context={}",
                    humanId, stageId, results, context);
            port.returnsAsync(pid, "code", -2000);
            return;
        }
        StageObjectServiceProxy prx = StageObjectServiceProxy.newInstance(information.nodeId, information.portId, information.id);
        prx.login(humanId, stageId, param);
        prx.listenResult(this::_result_login1, "pid", pid);
    }

    private void loginStage(long pid, long humanId, long stageId, Param param) {
        StageGlobalInfo information = this.infos.get(stageId);
        if (information == null) {
            Log.temp.error("===StageGlobalInfo=null, humanId={}, stageId={}", humanId, stageId);
            port.returnsAsync(pid, "code", -2000);
            return;
        }
//        Log.temp.error("===StageGlobalInfo={}, humanId={}, stageId={}, {}", information, humanId, stageId, param);
        StageObjectServiceProxy prx = StageObjectServiceProxy.newInstance(information.nodeId, information.portId, information.id);
        prx.login(humanId, stageId, param);
        prx.listenResult(this::_result_login1, "pid", pid);
    }

    public void _result_login1(boolean timeout, Param results, Param context) {
        Long pid = context.get("pid");
        if (timeout) {
            Log.temp.error("===超时了==result={}, context={}", results, context);
            port.returnsAsync(pid, "code", -2000);
            return;
        }
        port.returnsAsync(pid, results.toArray());
    }

    /**
     * 玩家登陆游戏后，发现玩家之前离线在跨服地图，判断玩家能否回归跨服地图（ 不创建地图）
     * @param
     */
    @DistrMethod
    public void canLoginBridgeStage(List<StageHistory> stageHistoryList) {
        for(StageHistory stageHistory : stageHistoryList) {
            StageGlobalInfo information = this.infos.get(stageHistory.stageId);
            if(information != null) {
                //判断玩家是否能进入地图
                if(canEnterBridge(information)) {
                    port.returns("canEnter", true, "nodeId", information.nodeId, "stageId", stageHistory.stageId, "pos", new Vector2D(stageHistory.posX, stageHistory.posY));
                    return;
                }
            }
        }
        port.returns("canEnter", false);
    }

    private boolean canEnterBridge(StageGlobalInfo info) {
//        ConfMap conf = ConfMap.get(info.sn);
//
//        if (conf.type.equals(StageMapTypeKey.common.getS())) {
//            Log.game.error("玩家登录检查时，发现跨服列表中不知为何会有common地图 需要检查");
//            return false;
//        }
//
//
//        if (conf.type.equals(StageMapTypeKey.rep.getS())) {
//            ConfRep confRep = ConfRep.get(conf.sn);
//            if (confRep != null) {
//                ConfTeamPlatform confTeamPlatform = ConfTeamPlatform.get(confRep.sn);
//                if (confTeamPlatform != null && confTeamPlatform.cross == 1) {
//                    return false;
//                }
//            }
//
//            //组队副本
//            if (info.isEnd) {
//                //跳过结束的副本{超时或胜利的副本}
//                return false;
//            }
//            //演武场不返回
//            if(conf.gameType.equals(StageGameTypeKey.competition.getS())){
//                return false;
//            }
//
//            return true;
//        }

        return false;
    }


    /**
     * 地图玩家数量增加
     *
     * @param stageId
     */
    @DistrMethod
    public void stageHumanNumAdd(long stageId) {
        stageHumanNumChange(stageId, true);
    }

    /**
     * 地图玩家数量减少
     *
     * @param stageId
     */
    @DistrMethod
    public void stageHumanNumReduce(long stageId) {
        stageHumanNumChange(stageId, false);
    }

    private long applyStage(long stageId, Param param) {
        long resultStageId = GlobalConfVal.mainSn;

        int zone = Utils.getParamValue(param, "zone", 0);
//        // 是否是退出副本
//        boolean isQuitToCommon = Utils.getParamValue(param, "quitToCommon", false);
//        // 玩家id
//        long humanId = Utils.getParamValue(param, "humanId", 0L);
//        // 如果组队，队伍id
//        long teamId = 0;
//        // 如果组队，队伍人数
//        int memberNum = 0;
//        // 如果组队，队伍成员尽量去同一个分线
//        boolean isToLine = false;
//        // 是否退出副本
//        if(isQuitToCommon){
//            // 玩家是否组队进副本
//            if(humanIdTeamIdMap.containsKey(humanId)){
//                // 获取玩家对应的队伍id
//                teamId = humanIdTeamIdMap.get(humanId);
//            }
//
//            if(teamIdHumanIdListMap.containsKey(teamId)){
//                // 获取队伍中的玩家humanId
//                List<Long> memberList = teamIdHumanIdListMap.get(teamId);
//                if(memberList != null){
//                    memberNum = memberList.size();
//                    if(memberNum > 1){
//                        isToLine = true;
//                    }
//                }
//            }
//        }

        StageGlobalInfo info = infos.get(stageId);
        if (info == null) {
            if(stageId > 0){
                Log.stageCommon.error("StageGlobalInfo info is null stageId is {}", stageId);
            }
        } else {
            if(info.humanNum + 1 <= GlobalConfVal.mainSnMaxNum){
                return stageId;
            }
        }
        int mapSn = GlobalConfVal.mainSn;
        // 获取要去哪个分线stageId,没有则返回-1
        resultStageId = getLineNumStageId(stageId, zone, mapSn);
//        //如果需要分线 并且是一般地图
//        if (resultStageId < 0 || resultStageId == GlobalConfVal.mainSn) {
//            //创建新地图
//            //获得地图分线数量
//            int lineNum = getLineNum(zone, mapSn);
//            long stageID = Port.applyId();
//            String portId = StageManager.inst().getStagePortId();
//            StageServiceProxy proxy = StageServiceProxy.newInstance(Distr.getNodeId(portId), portId, D.SERV_STAGE_DEFAULT);
//            proxy.createStageCommon(stageID, mapSn, lineNum);
//
//            String mapName = "主地图";
//            //	提前注册
//            infoRegister(stageID, mapSn, mapName, Distr.getNodeId(portId), portId, zone, lineNum);
//            return stageID;
//        }
        return resultStageId;
    }

    private int getLineNum(int zone, int mapSn){
        int lineNum = 1;
        // 分线系统 key是主地图的 sn。 分线数
        Map<Integer, List<Integer>> mapSnLineNumMap = zoneMapPartNumMap.get(zone);
        if(mapSnLineNumMap == null){
            mapSnLineNumMap = new HashMap<>();
            zoneMapPartNumMap.put(zone, mapSnLineNumMap);
        }
        List<Integer> lineNumList = mapSnLineNumMap.get(mapSn);
        if(lineNumList == null){
            lineNumList = new ArrayList<>();
            mapSnLineNumMap.put(mapSn, lineNumList);
        }
        while (lineNumList.contains(lineNum)){
            lineNum++;
        }
        return lineNum;
    }

    private long getLineNumStageId(long stageId, int zone, int mapSn){
        long resultStageId = GlobalConfVal.mainSn;
        Map<Integer, Map<Long, StageGlobalInfo>> mapSnIdInfoMap = zoneStagePartMap.get(zone);
        int humanMaxNum = 100;
        if(mapSn == GlobalConfVal.mainSn){
            humanMaxNum = GlobalConfVal.mainSnMaxNum;
        }
        if (mapSnIdInfoMap == null || humanMaxNum == 1) {
            return resultStageId;
        }
        Map<Long, StageGlobalInfo> infoMap = mapSnIdInfoMap.get(mapSn);
        if(infoMap == null){
            return resultStageId;
        }
        StageGlobalInfo sginfo = infoMap.get(stageId);
        if(sginfo != null && sginfo.humanNum + 1 <= GlobalConfVal.mainSnMaxNum){
            return stageId;
        }
        for(StageGlobalInfo info : infoMap.values()){
            if (info.humanNum + 1 < humanMaxNum) {
                return info.id;
            }
        }
        return resultStageId;
    }

    /**
     * mapSn
    * @param stageId
     */
    @DistrMethod
    public void applyStageBySn(long stageId, Param param) {
        long resultStageId = -1;
		// 是否是退出副本
        boolean isQuitToCommon = Utils.getParamValue(param, "quitToCommon", false);
		// 玩家id
        long humanId = Utils.getParamValue(param, "humanId", 0L);
		// 如果组队，队伍id
        long teamId = 0;
        // 如果组队，队伍人数
        int memberNum = 0;
        // 如果组队，队伍成员尽量去同一个分线
        boolean isToLine = false;
        // 是否退出副本
        if(isQuitToCommon){
            // 玩家是否组队进副本
            if(humanIdTeamIdMap.containsKey(humanId)){
                // 获取玩家对应的队伍id
                teamId = humanIdTeamIdMap.get(humanId);
            }
            if(teamIdHumanIdListMap.containsKey(teamId)){
                // 获取队伍中的玩家humanId
                List<Long> memberList = teamIdHumanIdListMap.get(teamId);
                if(memberList != null){
                    memberNum = memberList.size();
                    if(memberNum > 1){
                        isToLine = true;
                    }
                }
            }
        }

        StageGlobalInfo info = infos.get(stageId);
        if (info == null) {
            Log.stageCommon.error("StageGlobalInfo info is null stageId is {}", stageId);
            resultStageId = -1;
            Log.temp.error("出现了无法处理的stageid = {}", stageId);
            port.returns("stageId", resultStageId);
            return;
        }
        port.returns("stageId", stageId);
    }

    /**
     * 地图玩家数量变动
     *
     * @param stageId
     * @param add
     */
    private void stageHumanNumChange(long stageId, boolean add) {
        StageGlobalInfo info = this.infos.get(stageId);

        if (info == null) return;

//		Log.game.info("stageHumanNumChange stageId {} lineNum {} humanNum {} isAdd {}", stageId, info.lineNum, info.humanNum, add);

        //地图人数变动
        if (add) {
            info.humanNum = Math.max(0, info.humanNum + 1);
        } else {
            info.humanNum = Math.max(0, info.humanNum - 1);

            //如果为0 那么删除这个
            if (info.humanNum <= 0) {
                removeEmptyMap(info);
            }
        }
    }

    private void removeEmptyMap(StageGlobalInfo info) {
        if(info.sn == GlobalConfVal.mainSn){
            return;
        }
        ;
        Map<Long, StageGlobalInfo> mulLines = zoneStagePartMap.get(info.zone).get(info.sn);
        if (mulLines == null) {
            return;
        }
        //不处理主地图
        if (info.humanNum <= 0 && info.lineNum > 1 && info.id != info.sn) {
            //只处理分线地图
            //将地图从全局信息中删除
            infoCancel(info.id);

            StageServiceProxy proxy = StageServiceProxy.newInstance(info.nodeId, info.portId, D.SERV_STAGE_DEFAULT);
            proxy.destroy(info.id);
        }
    }

    @DistrMethod
    public void switchToStage(HumanObject humanObj, long stageTargetId, Object... params) {
        Long pid = port.createReturnAsync();
        Param param = new Param(params);
        boolean isBridge = Utils.getParamValue(param,"bridge" , false);
        boolean isLeague = Utils.getParamValue(param,"isLeague" , false);
        if(param.containsKey("serverId")){
            param.put("serverId", humanObj.getHuman().getServerId());
        }
//        Log.stageCommon.info("player = {} switch stage stageTargetId ={} isBridge = {}", humanObj.name, stageTargetId, isBridge);
        // 如果是切换的跨服需要去跨服申请场景
        StageGlobalServiceProxy proxy = isBridge ? StageGlobalServiceProxy.newInstance(NodeAdapter.bridge(isLeague))
                : StageGlobalServiceProxy.newInstance();
        proxy.applyStageBySn(stageTargetId, param);

        proxy.listenResult(this::_result_switchToStageNew, "pid", pid, ParamKey.humanObj, humanObj,"param" , param);
    }


    private void _result_switchToStageNew(Param results, Param context) {
        long stageTargetId = results.get("stageId");
        HumanObject humanObj = context.get("humanObj");
        switchToStageAysn(humanObj, stageTargetId, context.get("param"));
    }

    private void switchToStageAysn(HumanObject humanObj, long stageTargetId , Param param) {
        //原地图信息
        StageGlobalInfo infoSource = this.infos.get(humanObj.getStageNowId());

        //是否是跨服切换
        boolean bridge = Utils.getParamValue(param , "bridge", false);
        boolean isLeague = Utils.getParamValue(param,"isLeague" , false);

        if (infoSource == null) {
            Log.temp.error("====原地图信息找不到， humanId={}, stageId={}, stageHistory={}",
                    humanObj.id, humanObj.getStageNowId(), humanObj.getHuman3().getStageHistory());
        }

        /* 离开原地图 */
        //从原地图清离,原地图玩家数-1
        if (bridge) {
            if (infoSource != null) {
                //如果是跨服的话，并不是真正的离开，只是隐藏玩家
                HumanObjectServiceProxy prxSource = HumanObjectServiceProxy.newInstance(infoSource.nodeId, infoSource.portId, humanObj.id);
                prxSource.bridgeStageHide();
            }
            // 跨服需要去跨服进了
            Log.game.info("bridge enter stage isLeague {}", isLeague);
            StageGlobalServiceProxy.newInstance(NodeAdapter.bridge(isLeague))._switchStageEnter(humanObj,stageTargetId,param);
        } else {
            StageGlobalInfo temp = this.infos.get(stageTargetId);
            //如果地图不存在那么修正到玩家出生
//            if (temp == null) {
//                ConfMap conf = ConfMap.get(HumanManager.stageInitSn);
//                stageTargetId = conf.sn;
//                temp = this.infos.get(stageTargetId);
//            }
            humanObj.setSwitchFrom(HumanManager.inst().stageHistorySn(humanObj));
            if (infoSource != null) {
                HumanObjectServiceProxy prxSource = HumanObjectServiceProxy.newInstance(infoSource.nodeId, infoSource.portId, humanObj.id);
                prxSource.leave(stageTargetId);
                //状态管理器
                HumanStatusManager.inst().prevSwitch(humanObj, infoSource.sn, temp.sn);
                //如果是新手本
                if (infoSource.sn == HumanManager.stageInitRepSn) {
                    stageHumanNumReduce(HumanManager.stageInitRepSn);
                } else {
                    //只有在目标和源都是主城地图的时候才处理
                    stageHumanNumReduce(humanObj.getStageNowId());
                }
            }
            // 本服直接进入
            _switchStageEnter(humanObj, stageTargetId, param);
        }
    }

    /**
     * 由于跨服的话，这里的逻辑需要再跨服线程中调用，所以需要把进入相关的方法拆分出来
     * @param humanObj
     * @param stageTargetId
     * @param param
     */
    @DistrMethod
    public void _switchStageEnter(HumanObject humanObj, long stageTargetId , Param param){
        Human human = humanObj.getHuman();

        // 角色朝向
        Vector2D dir = Utils.getParamValue(param , "dir", null);
        // 出生点
        Vector2D posAppear = Utils.getParamValue(param , "posAppear", null);
        // 是否是复活
        boolean isRevive = Utils.getParamValue(param , "isRevive", false);
        //是否是跨服切换
        boolean bridge = Utils.getParamValue(param , "bridge", false);
        boolean isLeague = Utils.getParamValue(param,"isLeague" , false);

        //根据配置重设出生点
        StageGlobalInfo temp = this.infos.get(stageTargetId);

        //如果地图不存在那么修正到玩家出生
//        if (temp == null) {
//            ConfMap conf = ConfMap.get(HumanManager.stageInitSn);
//            Vector2D vecTemp = StageManager.inst().getBirthPosFromMapSn(HumanManager.stageInitSn);//StageBattleManager.inst().randomPosInCircle(StageManager.inst().getBirthPosFromMapSn(HumanManager.stageInitSn, humanObj.getCampType()), 0, 3);
//            stageTargetId = conf.sn;
//            temp = this.infos.get(stageTargetId);
//            posAppear = vecTemp;
//        }else if (null == posAppear) {
//            posAppear = StageManager.inst().getBirthPosFromMapSn(temp.sn);
//        }

        if(temp == null){
			Log.temp.error("如果地图不存在,stageId={}", stageTargetId);
            return;
        }
        // 退出副本，且是镜像本， 不同地图则重新获取位置
        if(param.containsKey("quitToCommon") && param.containsKey("oldMapSn") && param.containsKey("oldRepSn")) {
            int oldRepSn = param.getInt("oldRepSn");
            // 处理组队镜像本退出不同地图位置错误
//            if(StageManager.inst().isEscortMap(oldRepSn)){
//                ConfMap oldConfMap = ConfMap.get(param.getInt("oldMapSn"));
//                if(param.getBoolean("quitToCommon") && oldConfMap.original != temp.sn){
//                    posAppear = StageManager.inst().getBirthPosFromMapSn(temp.sn);
//                }
//            }
        }


//        //常规地图位置
//        ConfMap conf = ConfMap.get(temp.sn);
//        //玩家新地图的坐标
//        humanObj.setPosDir(posAppear, dir);
//
//        //更新玩家地图路径信息
//        HumanManager.inst().recordStage(humanObj, stageTargetId, temp.sn, conf.type, conf.gameType);//, posOld);
//        /* 进入新地图 */
//        StageGlobalInfo infoTarget = this.infos.get(stageTargetId);
//
//        //在跨服服务器需要修改连接服务器节点信息
//        if(bridge || S.isBridge) {
//            humanObj.connPoint.nodeId = D.NODE_WORLD_BRIDGE_PREFIX + humanObj.realServerId;
//            Log.game.debug("humanobj bridge modify node info node id {}, real serverid {}", humanObj.connPoint.nodeId, humanObj.realServerId);
//            HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.bridgeInstance(NodeAdapter.world(humanObj.realServerId));
//            proxy.registerToBridge(human.getId(), infoTarget.id, infoTarget.sn, infoTarget.name, infoTarget.nodeId, infoTarget.portId , infoTarget.lineNum, isLeague);
//
//            if(S.isBridge){
//                ConfMap confSource = ConfMap.get(infoTarget.sn);
//                proxy.bridgeStageHistorySet(humanObj.id, infoTarget.id, infoTarget.sn, confSource.type, posAppear);
//            }
//        }else {
//            //更新玩家全局信息
//            HumanGlobalServiceProxy prxHumanStatus = HumanGlobalServiceProxy.newInstance();
//            prxHumanStatus.stageIdModify(human.getId(), infoTarget.id, infoTarget.sn, infoTarget.name, infoTarget.nodeId, infoTarget.portId, infoTarget.lineNum);
//        }
//
//        //注册玩家至新地图，新地图玩家数+1(业务逻辑只能写在此处前，防止玩家数据提前串行化，到时修改失效)
//        StageObjectServiceProxy prxTarget = StageObjectServiceProxy.newInstance(infoTarget.nodeId, infoTarget.portId, infoTarget.id);
//        prxTarget.register(humanObj, isRevive, stageTargetId);
//        prxTarget.listenResult(this::_result_switchToStage, "infoTarget", infoTarget, "humanObj", humanObj,"bridge", bridge, "isLeague", isLeague);
//
//        //切完地图强制同步当前地图数量
//        stageHumanNumAdd(stageTargetId);
    }

    public void _result_switchToStage(Param results, Param context) {
        boolean success = Utils.getParamValue(results, "success", false);
        int repSn =  Utils.getParamValue(results, "repSn", 0);
        Vector2D posNow = Utils.getParamValue(results, "posNow", null);
        Vector2D dirNow = Utils.getParamValue(results, "dirNow", null);
        int count = Utils.getParamValue(results, "counter", 0);    //当前玩家数量
        HumanObject humanObj = Utils.getParamValue(context, "humanObj", null);
        StageGlobalInfo infoTarget = Utils.getParamValue(context, "infoTarget", null);
        boolean bridge = Utils.getParamValue(context, "bridge", false);

//        String asset = Utils.getParamValue(results, "asset", "");
//        boolean isLeague = context.get("isLeague");
        //打日志
//		Log.stageCommon.error("强制同步地图在线玩家数量，地图{}，实际在线量{}，记录在线量{}", infoTarget.sn, count, infoTarget.humanNum);

        if(humanObj == null || infoTarget == null || posNow == null || dirNow ==null){
            Log.temp.error("===results={}, context={}", results, context);
            return;
        }



        //强制同步数量
        infoTarget.humanNum = count;

        if (!success) {
            Log.stageCommon.error("切换场景出现错误，返回主城");
            StageGlobalServiceProxy prx = StageGlobalServiceProxy.newInstance();
            prx.quitToCommon(humanObj, infoTarget.sn, repSn);
            return;
        }
        if(humanObj.getHuman().isCheckRobot()){
            Log.temp.info("===检测的玩家不需要通知，humanId={}", humanObj.id);
            return;
        }

        /* 消息处理 */
        //返回地图切换消息

        //玩家连接
        CallPoint connPoint = humanObj.connPoint;

        Log.temp.info("===通知切图消息，humanId={}， name={}, connPoint={}", humanObj.id, humanObj.name, connPoint);

        //更新连接服务器的玩家信息
        ConnectionProxy prx2 = ConnectionProxy.newInstance(connPoint.nodeId, connPoint.portId, connPoint.servId);
        prx2.updateStatus(infoTarget.nodeId, infoTarget.portId);

        if(bridge || S.isBridge) {
            HumanGlobalServiceProxy hgprx = HumanGlobalServiceProxy.newInstance(NodeAdapter.world(humanObj.realServerId));

            //去玩家所在服务器设置玩家的跨服地图信息，方便重登进入
//            ConfMap confSource = ConfMap.get(infoTarget.sn);
//            hgprx.bridgeStageHistorySet(humanObj.id, infoTarget.id, infoTarget.sn, confSource.type, posNow);
        }
    }


    /**
     * 退出副本 活动 等地图到进入前的主地图
     *
     * @param humanObj
     * @param params
     */
    @DistrMethod
    public void quitToCommon(HumanObject humanObj, int nowSn, int repSn, Object... params) {

        Param param = new Param(params);
        //是否需要返回
        boolean callBack = Utils.getParamValue(param, "callBack", false);
        boolean isRevive = Utils.getParamValue(param, "revive", false); 

        //是否退出跨服地图
        boolean quitBridge = Utils.getParamValue(param, "quitBridge", false);

        //如果是从跨服退出到世界服
        if(quitBridge) {
			Log.temp.error("===humanId={}, quitBridge={}, param={}", humanObj.id, quitBridge, param);
        
            //如果是跨服的话，离开地图，
            switchStageLeave(humanObj, -19999);

            //离开跨服地图，需要删除跨服humanGlobal的信息
            HumanGlobalServiceProxy proxyBridge = HumanGlobalServiceProxy.newInstance();
            proxyBridge.cancel(humanObj.id);

            //跨服之后传回本服数据
//            Param backParam = bridgeBackServerParam(humanObj);
            // 需要找到原本地图的相关信息
//            HumanGlobalServiceProxy proxyWorld = HumanGlobalServiceProxy.bridgeInstance(D.NODE_WORLD_BRIDGE_PREFIX + humanObj.realServerId);
//            proxyWorld.bridgeToWorldBack(humanObj.id, backParam);

            if(callBack) port.returns();
            return ;
        }


        int mapSn = HumanManager.inst().stageHistoryCommonSn(humanObj, isRevive);


//        ConfMap conf = ConfMap.get(nowSn);
//
//        if (mapSn > 0 && conf != null) {
//            Vector2D posAppear = HumanManager.inst().stageHistoryCommon(humanObj, isRevive);
//            ConfMap toMapConf = ConfMap.get(mapSn);
//            if(toMapConf.activityListSn.length > 1){
//                int activitySn = toMapConf.activityListSn[1];
//                boolean isOpen = CalendarManager.inst().isOpen(ConfActivityList.get(activitySn));
//                if(!isOpen){
//                    mapSn = ConfGlobal.get(3124).value;
//                    posAppear = new Vector2D(ConfGlobal.get(3124).floatArray[0], ConfGlobal.get(3124).floatArray[2]);
//                }
//            }
//            //如果是镜像副本 并且当前地图和要去的地图资源配置一样
//            if (StageManager.inst().isEscortMap(repSn) && conf.asset.equals(ConfMap.get(nowSn).asset)) {
//                Log.game.info("取镜像本当前位置 pos={}",humanObj.getPosNow());
//                posAppear = humanObj.getPosNow();
//            }
//            // 新手本退出固定位置
//            ConfRep confRep = ConfRep.get(repSn);
//            if(confRep != null && confRep.repType == InstanceConstants.INSTANCE_TYPE_NEWER) {
//                ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.创建角色的出生点.SN);
//                if (confGlobal != null){
//                    posAppear = new Vector2D(confGlobal.intArray[0], confGlobal.intArray[2]);
//                }
//            }
//
//            // 放入新的数据
//            param.put("isRevive", isRevive);
//            param.put("posAppear", posAppear);
//            param.put("oldMapSn", nowSn);
//            param.put("oldRepSn", repSn);
//
//            // 把原有或当前的数据放入object数据组
//            Object[] objectArray = new Object[param.size() * 2];
//            int index = 0;
//            for(String str : param.keySet()){
//                objectArray[index] = str;
//                objectArray[index + 1] = param.get(str);
//                index += 2;
//            }
//            switchToStage(humanObj, mapSn, objectArray);
//        } else {
//            Log.stageCommon.error("leave error {} -> {}", nowSn, mapSn);
//        }
//
//        if (callBack) {
//            port.returns();
//        }
    }

	/**
	 * 星斗秘境专用
	 * <AUTHOR>
	 * @Param
	 */
    @DistrMethod
    public void bridgeSwitchStageLeave(HumanObject humanObj, long stageTargetId){
        //目标地图信息
        StageGlobalInfo infoSource = this.infos.get(stageTargetId);
        if(infoSource == null){
            return;
        }
        if(S.isBridge){
            Log.temp.error("===离开原地图, humanId={}, infoSource={}, stageTargetId={}", humanObj.id, infoSource, stageTargetId);
        }

        HumanObjectServiceProxy prxSource = HumanObjectServiceProxy.newInstance(infoSource.nodeId, infoSource.portId, humanObj.id);
        prxSource.leave(-1);
    }


    /**
     * 离开原地图
     */
    private void switchStageLeave(HumanObject humanObj,long stageTargetId) {
        //目标地图信息
        StageGlobalInfo infoSource = this.infos.get(humanObj.getStageNowId());
        if(infoSource == null){
            return;
        }

        HumanObjectServiceProxy prxSource = HumanObjectServiceProxy.newInstance(infoSource.nodeId, infoSource.portId, humanObj.id);
        prxSource.leave(stageTargetId);
        humanObj.setSwitchFrom(HumanManager.inst().stageHistorySn(humanObj));

        //如果是新手本
        if (infoSource.sn == HumanManager.stageInitRepSn) {
            stageHumanNumReduce(HumanManager.stageInitRepSn);
        } else {
            //只有在目标和源都是主城地图的时候才处理
            stageHumanNumReduce(humanObj.getStageNowId());
        }
    }


    /**
     * 在心跳里打印场景信息，并且调用清理
     */
    @Override
    public void pulseOverride() {
        long now = Port.getTime();

        //打印各个地图的分线以及对应的人数
        pulseStageLog(now);

        //打印全部地图
        pulseStageLogAll(now);

    }

    /**
     * 打印场景信息
     *
     * @param now
     */
    private void pulseStageLog(Long now) {
        if (!onlineLogTimer.isPeriod(now)) {
            return;
        }

       Map<Integer, Integer> lineNums = new HashMap<>();

        int countAll = 0;
        for (Entry<Integer, Map<Integer, Map<Long, StageGlobalInfo>>> entry : zoneStagePartMap.entrySet()) {
            int zone = entry.getKey();
            Map<Integer, Map<Long, StageGlobalInfo>> value = entry.getValue();
            for (Entry<Integer, Map<Long, StageGlobalInfo>> entryInfo : value.entrySet()) {
                for (StageGlobalInfo info : entryInfo.getValue().values()) {
                    countAll += info.humanNum;
                    if (lineNums.containsKey(info.lineNum)) {
                        lineNums.put(info.lineNum, lineNums.get(info.lineNum) + 1);
                    } else {
                        lineNums.put(info.lineNum, 1);
                    }
                }
            }
        }

        Log.temp.error("pulseStageLog countAllHuman {}--------------------------------", countAll);
        Log.temp.error("pulseStageLog countAllStage {}--------------------------------", infos.size());
        Log.temp.error("pulseStageLog countAllLine {}--------------------------------", lineNums.size());
    }

    /**
     * 打印场景详细信息，并且做错误清理
     *
     * @param now
     */
    private void pulseStageLogAll(Long now) {
        if (!onlineLogAllTimer.isPeriod(now)) {
            return;
        }

        Map<Integer, Integer> infoHumanCount = new HashMap<Integer, Integer>();
        for (StageGlobalInfo info : infos.values()) {
            if (infoHumanCount.containsKey(info.sn)) {
                infoHumanCount.put(info.sn, infoHumanCount.get(info.sn) + info.humanNum);
            } else {
                infoHumanCount.put(info.sn, info.humanNum);
            }

            //清楚莫名其妙的东西
            clearErrorStage(info, now);
        }

    }

    private void clearErrorStage(StageGlobalInfo info, Long now) {
        //如果场景人数为0 并且是副本 并且建立时间大于10分钟清除
//        ConfMap conf = ConfMap.get(info.sn);
//        if (info.humanNum == 0) {
//            if (conf != null && conf.type.equals(StageMapTypeKey.rep.getS())) {
//                StageObjectServiceProxy prxTarget = StageObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
//                prxTarget.clearError(now);
//            }
//        } else {
//            // add by wuchunxin 热更原因，写在一个方法调用里
//            if (conf.gameType.equals("waittingzone") || conf.gameType.equals("waittingFinals") || conf.gameType.equals(StageGameTypeKey.quiz)) {
//                StageObjectServiceProxy prxTarget = StageObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
//                prxTarget.clearError(now);
//            }
//        }
    }

    /**
     * 每小时执行一次(刷出地图上的组队boss)
     */
    @ScheduleMethod(DataResetService.CRON_DAY_HOUR)
    public void refreshTeamBoss() { }

    /**
     * 每小时的N分执行(清除地图上的组队boss)
     */
    @ScheduleMethod("0 30 * * * ?")
    public void clearTeamBoss() {}


    public void getHumanInfo(Collection<StageGlobalInfo> infos, long humanId, boolean isShow) {

//        CompetitionServiceProxy proxy = CompetitionServiceProxy.newInstance();
//        proxy.getHumanObjInfo(humanId);
//        proxy.listenResult(this::_result_getHumanInfo, "infos", infos, "isShow", isShow);

    }

    public void _result_getHumanInfo(Param results, Param context) {

//        CompetitionHumanObj humanObj = results.get("obj");

        Collection<StageGlobalInfo> infos = context.get("infos");

        boolean isShow = context.get("isShow");

//		CompetitionMirror competitionMirr = humanObj.humanMirror;

//        for (StageGlobalInfo info : infos) {
//
//            StageObjectServiceProxy prx = StageObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
//            prx.showCampLeader(humanObj, isShow);
//        }

    }

    /**
     * 是否能切地图
     * @param stageId
     */
    @DistrMethod
    public void isSwichExtensionLineNum(long stageId){
        StageGlobalInfo stageInfo = infos.get(stageId);
        if(stageInfo == null){
            port.returns(false);
            return;
        }
//        ConfMap confMap = ConfMap.get(stageInfo.sn);
//        if(confMap == null){
//            port.returns(false);
//            return;
//        }
//        // 已经满了不能切
//        if(stageInfo.humanNum >= confMap.humanMaxNum){
//            Log.game.info("humanNum {} maxNum {}", stageInfo.humanNum, confMap.humanMaxNum);
//            port.returns(false);
//            return;
//        }
        port.returns(true);
    }



    @DistrMethod
    public void batchStageObjectEvent(List<Integer> mapList, int key, Param param) {
        for (long mapSn : mapList) {
            StageGlobalInfo info = infos.get(mapSn);
            if (info == null)
                continue;

            StageObjectServiceProxy prx = StageObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
            prx.onStageObjectEvent(key,param);
        }
    }

    /**
     *
     * @param mapSn 地图sn=0的时候，就是所有地图
     * @param key
     * @param param
     */
    @DistrMethod
    public void onStageObjectEvent(int mapSn,int key, Param param){
        for(StageGlobalInfo info :infos.values()) {
            if(mapSn != 0 &&  info.sn != mapSn){
                continue;
            }
            StageObjectServiceProxy prx = StageObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
            prx.onStageObjectEvent(key,param);
        }
    }

    /**
     * 发送消息给指定mapSn的地图
     * @param msg
     * @param mapSn
     */
    @DistrMethod(argsImmutable=true)
    public void sendMsgToMap(Message msg, int mapSn, Vector2D pos) {
        for(StageGlobalInfo info :infos.values()){
            if(info.sn != mapSn){
                continue;
            }
            StageObjectServiceProxy prx = StageObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
            prx.sendMsg(msg,pos);
        }

    }

    /**
     * 统一设置地图怪物血量
     * @param mapSn
     */
    @DistrMethod
    public void setMonsterHp(int mapSn, int sn, long hpCur){
        for(StageGlobalInfo info :infos.values()){
            if(info.sn != mapSn){
                continue;
            }
            StageObjectServiceProxy prx = StageObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
            prx.setMonsterHp(sn ,hpCur);
        }
    }


    @DistrMethod
    public void backMethod1(String param) {

    }

    @DistrMethod
    public void backMethod2(String param) {

    }

    @DistrMethod
    public void backupMethod3(String param) {

    }

    @DistrMethod
    public void backupMethod4(String param) {

    }

    /** 
     * 玩家组队进入副本时同步一下成员id，且重置分线，退出副本时用。
     * <AUTHOR>
     * @Date 2021/5/3
     * @Param
     */
    @DistrMethod
    public void updateTeamIdHumanIdList(long teamId, List<Long> humanIdList){
        if(humanIdList == null || humanIdList.isEmpty()){
            if(teamIdHumanIdListMap.containsKey(teamId)){
                teamIdHumanIdListMap.remove(teamId);
            }
            if(teamIdLineNumMap.containsKey(teamId)){
                teamIdLineNumMap.remove(teamId);
            }
            return;
        }
        // 进入组队副本保存队伍id和成员id信息
        teamIdHumanIdListMap.put(teamId, humanIdList);
        // 记录玩家id对应的队伍id
        humanIdList.forEach(humanId -> humanIdTeamIdMap.put(humanId, teamId));
        // 重置队伍id对应分线为0， 退出时第一个玩家为准设置队伍去哪个分线
        teamIdLineNumMap.put(teamId, 0);
        // 打个日志
        Log.temp.info("=== teamId={}, humanIdTeamIdMap={}, teamIdHumanIdListMap={}", teamId, humanIdTeamIdMap, teamIdHumanIdListMap);

    }


    @DistrMethod
    public void getInfo(int mapSn, int line){
        List<Integer> lineNumList = new ArrayList<>();
        for(StageGlobalInfo info :infos.values()) {
            if (info.sn != mapSn) {
                continue;
            }
            if (info.lineNum == line){
                port.returns("nodeId", info.nodeId, "portId", info.portId, "id", info.id);
                return;
            }
            lineNumList.add(info.lineNum);
        }
//        Log.temp.error("===mapSn={}, line={}, lineNumList={}", mapSn, line, lineNumList);

        port.returns("nodeId", "");
    }



    @DistrMethod
    public void getStageId(long stageId){
        boolean isExist = false;
        StageGlobalInfo stageGlobalInfo = infos.get(stageId);
        if(stageGlobalInfo != null){
            isExist = true;
        }
        port.returns("isExist", isExist, "stageId", stageId);
    }

    @DistrMethod
    public void checkStageEnter(long stageId, int repSn){
        boolean isEnter = false;
//        int tips = 4961;
//        StageGlobalInfo stageGlobalInfo = infos.get(stageId);
//        if(stageGlobalInfo != null){
//            ConfRep confRep = ConfRep.get(repSn);
//            if(stageGlobalInfo.humanNum < confRep.warNumMax){
//                isEnter = true;
//            } else {
//                tips = 4960;
//            }
//        }
//        port.returns("isEnter", isEnter, "stageId", stageId, "tips", tips);
    }



    @DistrMethod
    public void gmKickMap(int mapSn){
        for(StageGlobalInfo info : infos.values()){
            if(info.sn != mapSn){
                continue;
            }
            StageObjectServiceProxy prx = StageObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
            prx.kickHumanObj();
        }
    }

    @DistrMethod
    public void gmGetMap(int mapSn){
        int num = 0;
        for(StageGlobalInfo info : infos.values()){
            if(info.sn != mapSn){
                continue;
            }
            num += info.humanNum;
        }
        port.returns("mapSn", mapSn, "num", num);
    }



    @DistrMethod
    public void checkStageMapEnter(long stageId, int mapSn){
        StageGlobalInfo stageGlobalInfo = infos.get(stageId);
        if(stageGlobalInfo != null){
            StageObjectServiceProxy prx = StageObjectServiceProxy.newInstance(stageGlobalInfo.nodeId, stageGlobalInfo.portId, stageGlobalInfo.id);
//            prx.checkStageMapEnter();
            prx.listenResult(this::_result_checkStageMapEnter, "pid", port.createReturnAsync(), "stageId", stageId);
            return;
        }
        Log.temp.error("===准备区不存在, stageId={}", stageId);
        port.returns("isEnter", false, "stageId", stageId);
    }

    private void _result_checkStageMapEnter(boolean timeout, Param results, Param context){
        long pid = context.get("pid");
        if(timeout){
            Log.temp.error("=准备区超时了{}", context);
            port.returnsAsync(pid, "isEnter", false, "stageId", 0);
            return;
        }
        port.returnsAsync(pid, "isEnter", results.get("isEnter"), "stageId", context.get("stageId"));
    }

    @DistrMethod
    public void checkStageMapEnter(long stageId, int mapSn, int type){
//        boolean isEnter = false;
//        StageGlobalInfo stageGlobalInfo = infos.get(stageId);
//        if(stageGlobalInfo != null){
//            ConfMap confMap = ConfMap.get(mapSn);
//            if(stageGlobalInfo.humanNum < confMap.humanMaxNum){
//                isEnter = true;
//            }
//        }
//        port.returns("isEnter", isEnter, "stageId", stageId);
    }



    @DistrMethod
    public void gm_getMapSn(int mapSn){
        List<Long> stageIdList = new ArrayList<>();
        for(StageGlobalInfo info : infos.values()){
            if(info.sn == mapSn){
                stageIdList.add(info.id);
            }
        }
        port.returns("stageIdList", stageIdList);
    }

    @DistrMethod
    public void getLeagueWaittingZoneHumanNum(long stageId){
        StageGlobalInfo info = infos.get(stageId);
        if(info == null){
            port.returns("humanNum", 0);
            return;
        }
        port.returns("humanNum", info.humanNum);
    }

}
