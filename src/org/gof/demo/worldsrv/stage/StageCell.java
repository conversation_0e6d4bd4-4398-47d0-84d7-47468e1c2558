package org.gof.demo.worldsrv.stage;

import org.gof.core.Chunk;
import org.gof.core.support.S;
import org.gof.demo.battlesrv.stageObj.WorldObject;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.character.MonsterObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class StageCell {
	public int i;		//行
	public int j;		//列
	private long stageId;		//所属地图
	private Map<Long, WorldObject> worldObjects = new HashMap<Long,WorldObject>();			//该cell内所有地图单位
	private Map<Long, HumanObject> humans = new HashMap<>();											//该cell内所有玩家
	private Map<Long, MonsterObject> monsters = new HashMap<>();

	List<Integer> idList = new ArrayList<Integer>();
	List<Chunk> chunkList = new ArrayList<Chunk>();

	public StageCell(long stageId, int i, int j) {
		this.stageId = stageId;
		this.i = i;
		this.j = j;

	}

	public void sendMsg() {
		if(idList.size() <= 0) {
			return;
		}

		List<Integer> ids = new ArrayList<>(idList);
		List<Chunk> chunk = new ArrayList<>(chunkList);
		for(HumanObject ho : getHumans().values()) {
			// 如果玩家在跨服，本服的场景消息就不对他广播
			if(!S.isBridge && ho.bridge) continue;
			ho.sendMsg(ids, chunk);
		}

		idList.clear();
		chunkList.clear();
	}
	
	/**
	 * 是否属于同一张地图的Cell
	 * @return
	 */
	public boolean isInSameStage(StageCell cell) {
		return this.stageId == cell.stageId;
	}
	
	/**
	 * 添加地图单元
	 * @param obj
	 */
	public void addWorldObject(WorldObject obj) {
		worldObjects.put(obj.id,obj);
		
		//记录玩家
		if(obj instanceof HumanObject) {
			humans.put(obj.id, (HumanObject)obj);
			return;
		}

		//记录怪物
//		if(obj instanceof MonsterObject) {
//			monsters.put(obj.id, (MonsterObject)obj);
//			return;
//		}


	}
	
	/**
	 * 删除地图单元
	 * @param obj
	 */
	public void delWorldObject(WorldObject obj) {
		worldObjects.remove(obj.id);
		
		//删除玩家
		if(obj instanceof HumanObject) {
			this.humans.remove(obj.id);
			return;
		}
		
		//删除玩家
//		if(obj instanceof MonsterObject) {
//			this.monsters.remove(obj.id);
//			return;
//		}

	}
	
	/**
	 * 判断两Cell是否为同一个
	 * @param cell
	 * @return
	 */
	public boolean equals(StageCell cell) {
		if(this.i == cell.i && this.j == cell.j) return true;
		return false;
	}
	
	//根据id获取对象
	public WorldObject getWorldObject(long id) {
		return worldObjects.get(id);
	}
	public HumanObject getHuman(long id) {
		return humans.get(id);
	}
	public MonsterObject getMonster(long id) {
		return monsters.get(id);
	}

	public Map<Long, WorldObject> getWorldObjects() {
		return worldObjects;
	}

	public Map<Long, MonsterObject> getMonsters() {
		return monsters;
	}

	public Map<Long, HumanObject> getHumans() {
		return humans;
	}
	
	public Map<Long, HumanObject> getHumanObjects() {
		return humans;
	}
	
}
