package org.gof.demo.worldsrv.match;

/**
 * 加入匹配队列时同时加入匹配池，每帧出队的成员从池中挑选待匹配成员，同时，它也做为待匹配的成员供其它在队列中成员匹配，
 * 这里有一个不变式，即，不在池中或虽然在池中但已被合并的匹配分组无法参与匹配
 */

public class MatchPool {
    private int maxSize;
//    protected Deque<MatchGroup> matchQ = new ArrayDeque<>();
//
//    // Map<grpId, group>
//    protected Map<Long, MatchGroup> poolGrps = new HashMap<>();
//
//    private List<Long> mergedGrps = new ArrayList<>();
//    private List<MatchGroup> timeoutGrps = new ArrayList<>();
//    protected List<MatchGroup> matchedGrps = new ArrayList<>();
//    protected List<MatchPair> matchPairs = new ArrayList<>();
    private IMatcher matcher;

    public MatchPool(int maxSize, IMatcher matcher) {
        this.maxSize = maxSize;
        this.matcher = matcher;
    }

    public int init() {
        return 0;
    }

    public void loop(long now) {
//        while (!matchQ.isEmpty()) {
//            // 被合并的(grp.isMerged() == true)匹配组会从池中被移除，队列中被取出来的如果正好是这个匹配组，就跳过去
//            MatchGroup grp = matchQ.pollFirst();
//            if (!poolGrps.containsKey(grp.getId()))
//                continue;
//
//            boolean isMatch = findMatch(grp);
//            if (!isMatch) {
//                grp.setNextMatchTime(now + 1000);
//            } else {
//                grp.setNextMatchTime(0);
//            }
//        }
//
//        clearMergedGrps();
//        clearMatchedPoolGrps();
//        checkNextMatch(now);
//        checkTimeoutMatch(now);
    }


}