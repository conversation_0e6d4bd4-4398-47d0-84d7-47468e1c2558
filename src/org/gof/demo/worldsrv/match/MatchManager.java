package org.gof.demo.worldsrv.match;

import org.gof.core.support.ManagerBase;


public class MatchManager extends ManagerBase {
    public static final int MATCH_TYPE_NONE = 0;
    public static final int MATCH_TYPE_3V3 = 1;
    public static final int MATCH_TYPE_5V5 = 2;

    public static final int MATCH_TYPE_10v10_OCCUPY = 4;
    public static final int MATCH_TYPE_10v10_RESOURCE = 5;
    public static final int MATCH_TYPE_10v10_CAPTURE = 6;


    public static final int MATCH_TYPE_REP = 7;
    public static final int MATCH_TYPE_FACTIONWAR_QUALIFY = 8;
    public static final int MATCH_TYPE_FACTIONWAR_FINAL = 9;
    public static final int MATCH_TYPE_MAX = 10;

    public static final int MATCH_3V3_CAPACITY = 2;
    public static final int MATCH_5V5_CAPACITY = 3;
    public static final int MATCH_10V10_CAPACITY = 6;// 改成6v6
    public static final int MATCH_FACTIONWAR_CAPACITY = 5;

    public static final int MATCH_STATE_WAITCONFIRM = 0;
    public static final int MATCH_STATE_READY = 1;
    public static final int MATCH_STATE_CANCEL = 2;


    public static MatchManager inst() {
        return inst(MatchManager.class);
    }

    public MatchMember createMatchMember(long humanId, String name, int level, int profession, int combat, int soul,
                                         String modelSn, String fashionSn, int score, long teamId, int lineNum, int mapSn) {
        MatchMember matchMember = new MatchMember();
        matchMember.humanId = humanId;
        matchMember.name = name;
        matchMember.level = level;
        matchMember.profession = profession;
        matchMember.combat = combat;
        matchMember.soul = soul;
        matchMember.modelSn = modelSn;
        matchMember.isRobot = false;
        matchMember.score = score;
        matchMember.teamId = teamId;
        matchMember.fashionSn = fashionSn;
//        matchMember.headFrame = FashionManager.inst().getHeadFrame(matchMember.fashionSn);
        matchMember.lineNum = lineNum;
        matchMember.mapSn = mapSn;
        return matchMember;
    }


}
