package org.gof.demo.worldsrv.match;

import org.gof.demo.support.AtomicAutoIdGen;

import java.util.HashMap;
import java.util.Map;

public class ArenaRobotGenerator {
    private Map<Long, ArenaHuman> arenaHumans = new HashMap<>();
    private AtomicAutoIdGen idGen;

    public ArenaRobotGenerator() {
        idGen = new AtomicAutoIdGen();
    }

    public ArenaHuman getArenaHuman(long humanId) {
        return arenaHumans.get(humanId);
    }

//    public void genRobots(IMatcher matcher, int matchType, int robotType) {
//        Collection<ConfCharacterRobot> confRobots = ConfCharacterRobot.findAll();
//        for (ConfCharacterRobot confRobot : confRobots) {
//            if (confRobot.type != robotType)
//                continue;
//
//            batchAddArenaRobots(confRobot, matchType, MatchService.BATTLE_FIELD_MATCH_POOL_TYPE_ROBOT, confRobot.type);
//        }
//
//        for (ArenaHuman arenaHuman : arenaHumans.values()) {
//            MatchGroup grp = createRobotMatchGroup(matcher, arenaHuman);
//            matcher.addToMatch(grp);
//        }
//    }
//
//    private void batchAddArenaRobots(ConfCharacterRobot confRobot, int matchType, int poolType, int robotType) {
//        int[] points = confRobot.point;
//        int[] levels = confRobot.level;
//        for (int score = points[0]; score <= points[1]; score++) {
//            int minLv = levels[0];
//            int maxLv = levels[1];
//            int lv = minLv + RandomUtils.nextInt(maxLv - minLv);
//            createArenaRobot(confRobot, matchType, poolType, robotType, score, lv);
//        }
//    }
//
//    private MatchGroup createRobotMatchGroup(IMatcher matcher, ArenaHuman robot) {
//        List<MatchMember> members = new ArrayList<>();
//        MatchMember member = new MatchMember();
//        member.humanId = robot.humanId;
//        member.name = robot.name;
//        member.modelSn = robot.modelSn;
//        member.score = robot.score;
//        member.combat = robot.combat;
//        member.level = robot.level;
//        member.soul = robot.soul;
//        member.fashionSn = robot.fashionSn;
//        member.lineNum = robot.lineNum;
//        member.mapSn = robot.mapSn;
//        ConfPropCalc confPropCalc = ConfPropCalc.get(member.level);
//        String propName[] = confPropCalc.propName;
//        int hpIdx = 0;
//        for (int i = 0; i < propName.length; i++) {
//            if (propName[i].equals("hpMax")) {
//                hpIdx = i;
//                break;
//            }
//        }
//
//        member.curHp = confPropCalc.propValue[hpIdx];
//        member.maxHp = confPropCalc.propValue[hpIdx];
//        member.state = MatchManager.MATCH_STATE_READY;
//        member.isRobot = true;
//        member.partnerCount = getArenaPartnerNum(robot);
//        member.partnerVOList = createEmptyVOs(robot, member.partnerCount);
//        members.add(member);
//        MatchGroup grp = matcher.createMatchGroup(members);
//        grp.setPoolType(robot.poolType);
//        return grp;
//    }
//
//    private List<TeamPartnerVO> createEmptyVOs(ArenaHuman robot, int num) {
//        List<TeamPartnerVO> result = new ArrayList<>();
//        for (int i = 0; i < num; i++) {
//            TeamPartnerVO partnerVO = new TeamPartnerVO();
//            partnerVO.lv = robot.level;
//            partnerVO.partnerSn = robot.partnerSnList[i];
//            result.add(partnerVO);
//        }
//        return result;
//    }
//
//    /**
//     * 根据机器人的匹配类型，返回配置的伙伴数量
//     */
//    private int getArenaPartnerNum(ArenaHuman robot) {
//        int partnerNum = 0;
//        if(robot.partnerSnList == null){
//            return 0;
//        }
//        if (robot.matchType == MatchManager.MATCH_TYPE_3V3 || robot.matchType == MatchManager.MATCH_TYPE_5V5
//                || robot.matchType == MatchManager.MATCH_TYPE_10v10_OCCUPY
//                || robot.matchType == MatchManager.MATCH_TYPE_10v10_RESOURCE
//                || robot.matchType == MatchManager.MATCH_TYPE_10v10_CAPTURE) {
//            int grp = PartnerManager.inst().getFormationTypeByMatchType(robot.matchType);
//            int maxNum = PartnerManager.inst().getPartnerMaxNumByFormation(grp);
//            partnerNum = robot.partnerSnList.length > maxNum ? maxNum : robot.partnerSnList.length;
//        }
//        return partnerNum;
//    }
//
//    private void createArenaRobot(ConfCharacterRobot confRobot, int matchType, int poolType, int robotType, int score, int lv) {
//        HumanObject robotObj = new HumanObject(null, confRobot, true, -1, idGen.getAutoId());
//
//        ArenaHuman arenaHuman = humanToArenaHuman(robotObj);
//        arenaHuman.level = lv;
//        arenaHuman.score = score;
//        arenaHuman.matchType = matchType;
//        arenaHuman.poolType = poolType;
//        arenaHuman.robotType = robotType;
//
//        if (confRobot.partnerNum > 0) {
//            assignPartnerToRobot(confRobot, arenaHuman);
//        }
//
//        arenaHumans.put(arenaHuman.humanId, arenaHuman);
//    }
//
//    private void assignPartnerToRobot(ConfCharacterRobot confRobot, ArenaHuman arenaHuman) {
//        Set<Integer> partnerSet = new HashSet<>();
//        int partnerSnList[] = new int[confRobot.partnerNum];
//        int partnerFactorList[] = new int[confRobot.partnerNum];
//        int i = 0;
//        while (partnerSet.size() < confRobot.partnerNum) {
//            int idx = RandomUtils.nextInt(confRobot.partnerSn.length);
//            int partnerSn = confRobot.partnerSn[idx];
//            boolean isOk = partnerSet.add(partnerSn);
//            if (isOk) {
//                partnerSnList[i] = partnerSn;
//                partnerFactorList[i] = confRobot.partnerFactor[idx];
//                i++;
//            }
//        }
//
//        arenaHuman.partnerSnList = partnerSnList;
//        arenaHuman.partnerFactorList = partnerFactorList;
//    }
//
//    private ArenaHuman humanToArenaHuman(HumanObject humanObj) {
//        ArenaHuman arenaHuman = new ArenaHuman();
//
//        arenaHuman.humanId = humanObj.getHumanId();
//        arenaHuman.sn = humanObj.getHuman().getSn();
//        arenaHuman.modelSn = humanObj.getHuman().getModelSn();
//        arenaHuman.name = humanObj.getHuman().getName();
//        arenaHuman.level = humanObj.getHuman().getLevel();
//        arenaHuman.combat = humanObj.getHuman().getCombat();
//        arenaHuman.skill = SkillInbornManager.inst().getSkillJson(humanObj);
//        arenaHuman.camp = humanObj.getCampType();
//        arenaHuman.factionName = humanObj.getHuman().getFactionName();
//        arenaHuman.isRobot = true;
//        arenaHuman.soul = humanObj.getHuman().getSoul();
//        arenaHuman.fashionSn = humanObj.getHuman().getEquipFashionSnMap();
//        arenaHuman.soulSn = humanObj.getHuman().getSoulSn();
//        if(humanObj.stageObj != null){
//            arenaHuman.lineNum = humanObj.stageObj.lineNum;
//            arenaHuman.mapSn = humanObj.stageObj.sn;
//        }
//
//        arenaHuman.skillGroupSn = humanObj.getHuman().getSkillGroupSn();
//        arenaHuman.base = humanObj.getMirrPropPlus().toJSONStr();
//        return arenaHuman;
//    }


}
