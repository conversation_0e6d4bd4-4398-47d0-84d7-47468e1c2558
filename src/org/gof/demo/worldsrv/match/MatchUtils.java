package org.gof.demo.worldsrv.match;

import org.gof.core.support.S;
import org.gof.demo.support.NodeAdapter;

public class MatchUtils {
    public static MatchServiceProxy createMatchService(int targetSn) {
        MatchServiceProxy prx = null;
//        ConfTeamPlatform confTeamPlatform = ConfTeamPlatform.get(targetSn);
//        if (confTeamPlatform == null) {
//            prx = MatchServiceProxy.newInstance();
//            return prx;
//        }
//
//        if (confTeamPlatform.cross == 1) {
//            prx = MatchServiceProxy.bridgeInstance(NodeAdapter.bridge());
//        } else {
//            prx = MatchServiceProxy.newInstance();
//        }

        return prx;
    }

    public static MatchInfoServiceProxy createMatchInfoService() {
        MatchInfoServiceProxy prx = null;
        if (S.isBridge) {
            prx = MatchInfoServiceProxy.newInstance();
        } else {
            prx = MatchInfoServiceProxy.newInstance(NodeAdapter.bridge());
        }

        return prx;
    }
}
