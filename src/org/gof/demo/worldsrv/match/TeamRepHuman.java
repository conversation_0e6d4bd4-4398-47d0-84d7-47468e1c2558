package org.gof.demo.worldsrv.match;

import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.interfaces.ISerilizable;

import java.io.IOException;

public class TeamRepHuman implements ISerilizable {
    public long humanId;
    public int level;
    public int combat;
    public int camp;
    public int soul;
    public int profession;
    public int robotType;
    public String sn;
    public String skill;
    public String name;
    public String factionName;
    public String modelSn;
    public String soulSn;
    public int skillGroupSn;
    public String base;
    public String fashionSn;
    public int headFrame;
    public boolean isRobot;
    public int lineNum;
    public int mapSn;


    @Override
    public void writeTo(OutputStream out) throws IOException {
        out.write(sn);
        out.write(humanId);
        out.write(level);
        out.write(combat);
        out.write(camp);
        out.write(soul);
        out.write(robotType);
        out.write(skill);
        out.write(skillGroupSn);
        out.write(name);
        out.write(factionName);
        out.write(modelSn);
        out.write(soulSn);
        out.write(profession);
        out.write(base);
        out.write(fashionSn);
        out.write(headFrame);
        out.write(lineNum);
        out.write(mapSn);
    }

    @Override
    public void readFrom(InputStream in) throws IOException {
        sn = in.read();
        humanId = in.read();
        level = in.read();
        combat = in.read();
        camp = in.read();
        soul = in.read();
        robotType = in.read();
        skill = in.read();
        skillGroupSn = in.read();
        name = in.read();
        factionName = in.read();
        modelSn = in.read();
        soulSn = in.read();
        profession = in.read();
        base = in.read();
        fashionSn = in.read();
        headFrame = in.read();
        lineNum= in.read();
        mapSn = in.read();

    }
}
