package org.gof.demo.worldsrv.match;

import org.gof.core.gen.proxy.DistrClass;
import org.gof.core.gen.proxy.DistrMethod;
import org.gof.core.support.NodeAdapter;
import org.gof.core.support.S;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.common.GamePort;
import org.gof.demo.worldsrv.common.GameServiceBase;
import org.gof.demo.worldsrv.human.HumanGlobalServiceProxy;
import org.gof.demo.worldsrv.support.D;
import org.gof.demo.worldsrv.support.Log;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 匹配
 */
@DistrClass(
        servId = D.SERV_MATCH_INFO,
        importClass = {List.class, Map.class, ArenaHuman.class, MatchMember.class},
        localOnly = false
)

public class MatchInfoService  extends GameServiceBase {
    private Map<Long, MatchInfo> matchInfos = new HashMap<>();


    public MatchInfoService(GamePort port) {
        super(port);
    }

    @Override
    protected void init() {

    }

    @Override
    public void pulseOverride() {

    }

    @DistrMethod
    public void batchClearArenaMatchInfo(List<Long> humanIds, int matchType, boolean isNotify) {
        for (long humanId : humanIds) {
            clearMatchInfo(humanId);
        }

        if (S.isBridge) {
            for (long humanId : humanIds) {
                List<Long> tmpIdList = new ArrayList<>();
                tmpIdList.add(humanId);
                int serverId = Utils.getServerIdByHumanId(humanId);
                if (serverId == 0)
                    continue;

//                HumanGlobalServiceProxy.bridgeInstance(NodeAdapter.world(serverId)).batchNotifyClearMatchInfo(tmpIdList, matchType, isNotify);
            }
        } else {
//            HumanGlobalServiceProxy.newInstance().batchNotifyClearMatchInfo(humanIds, matchType, isNotify);
        }
    }

    @DistrMethod
    public void batchClearRepMatchInfo(List<Long> humanIds, int targetSn, boolean isNotify) {
        for (long humanId : humanIds) {
            clearMatchInfo(humanId);
        }

        if (S.isBridge) {
            for (long humanId : humanIds) {
                List<Long> tmpIdList = new ArrayList<>();
                tmpIdList.add(humanId);
                int serverId = Utils.getServerIdByHumanId(humanId);
                if (serverId == 0)
                    continue;

                HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance(NodeAdapter.world(serverId));
                if (prx != null) {
//                    prx.batchNotifyClearRepMatchInfo(tmpIdList, targetSn, isNotify);
                }
            }
        } else {
//            HumanGlobalServiceProxy.newInstance().batchNotifyClearRepMatchInfo(humanIds, targetSn, isNotify);
        }
    }

    /**
     * 获取匹配信息
     * @param humanId
     */
    @DistrMethod
    public void getMatchInfo(long humanId) {
        if (matchInfos.containsKey(humanId)) {
            MatchInfo info = matchInfos.get(humanId);
            port.returns(info);
        } else {
            port.returns();
        }
    }

    /**
     * 在本地注册匹配信息
     * @param type
     * @param matchMembers
     */
    @DistrMethod
    public void registerMatchInfo(int type, List<MatchMember> matchMembers, int targetSn) {
        for (MatchMember member : matchMembers) {
            if (member.isRobot)
                continue;

            MatchInfo info = addMatchInfo(member.humanId, member.grpId, member.name, type, member.isRobot);
            if (targetSn > 0) {
                info.targetSn = targetSn;
            }
        }
    }

    @DistrMethod
    public void removeMatchInfo(long humanId) {
        clearMatchInfo(humanId);
    }

    @DistrMethod
    public void removeMatchInfoList(List<Long> humanIds) {
        for (long humanId : humanIds) {
            clearMatchInfo(humanId);
        }
    }

    private MatchInfo addMatchInfo(long humanId, long grpId, String name, int matchType, boolean isRobot) {
        MatchInfo info = new MatchInfo();
        info.humanId = humanId;
        info.grpId = grpId;
        info.name = name;
        info.matchType = matchType;
        matchInfos.put(humanId, info);
        return info;
    }

    private void clearMatchInfo(long humanId) {
        if (matchInfos.containsKey(humanId)) {
            MatchInfo info = matchInfos.get(humanId);
            Log.team.debug("{} clear match info {}", humanId, info.matchType);
            matchInfos.remove(humanId);
        }
    }

}
