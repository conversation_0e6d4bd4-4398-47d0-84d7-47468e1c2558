package org.gof.demo.worldsrv.match;

import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.interfaces.ISerilizable;

import java.io.IOException;

public class TeamRepHumanMirror implements ISerilizable {
    public long humanID;
    public int level;
    public String name;
    public String humanSn;
    public String modelSn;
    public int profession;
    public int sex;
    public int combat;
    public String skill;
    public int skillGroupSn;
    public String base;
    public long enterTime;
    public int campType;

    @Override
    public void writeTo(OutputStream out) throws IOException {
        out.write(humanID);
        out.write(level);
        out.write(name);
        out.write(humanSn);
        out.write(modelSn);
        out.write(profession);
        out.write(sex);
        out.write(combat);
        out.write(skill);
        out.write(skillGroupSn);
        out.write(base);
        out.write(enterTime);
        out.write(campType);
    }

    @Override
    public void readFrom(InputStream in) throws IOException {
        humanID = in.read();
        level = in.read();
        name = in.read();
        humanSn = in.read();
        profession = in.read();
        sex = in.read();
        combat = in.read();
        skill = in.read();
        skillGroupSn = in.read();
        base = in.read();
        enterTime = in.read();
        campType = in.read();
    }
}
