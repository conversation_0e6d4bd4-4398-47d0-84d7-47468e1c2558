//package org.gof.demo.worldsrv.match;
//
//import org.gof.demo.worldsrv.config.ConfTeamPlatform;
//
//public class TeamRepMatchRule {
//    public static boolean canGroupAcceptMember(int targetSn, MatchGroup grpTeam, MatchMember memberSingle) {
//        int minJoinLv = grpTeam.getMinJoinLv();
//        int maxJoinLv = grpTeam.getMaxJoinLv();
//        if (memberSingle.level < minJoinLv || memberSingle.level > maxJoinLv) {
//            return false;
//        }
//
//        ConfTeamPlatform confTeamPlatform = ConfTeamPlatform.get(targetSn);
//        if (confTeamPlatform.professionLimit == null || confTeamPlatform.professionLimit.isEmpty()) {
//            return true;
//        }
//
//        String[] professionLimits = confTeamPlatform.professionLimit.split(",");
//
//
//        return true;
//    }
//
//
//}
