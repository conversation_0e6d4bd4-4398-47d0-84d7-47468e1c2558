package org.gof.demo.worldsrv.share;

import org.gof.core.support.ManagerBase;
import org.gof.core.support.Param;
import org.gof.core.support.Utils;
import org.gof.core.support.observer.Listener;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.ConfGlobal;
import org.gof.demo.worldsrv.entity.HumanExtInfo;
import org.gof.demo.worldsrv.human.FuncOpenType;
import org.gof.demo.worldsrv.instance.InstanceConstants;
import org.gof.demo.worldsrv.instance.InstanceManager;
import org.gof.demo.worldsrv.msg.MsgAct;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.support.enumKey.ConfGlobalKey;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;
import org.gof.demo.worldsrv.support.enumKey.RewardStateKey;
import org.gof.demo.worldsrv.support.observer.EventKey;
import org.gof.demo.worldsrv.task.TaskConditionTypeKey;

import java.util.*;

/**
 * sns分享
 */
public class ShareManager extends ManagerBase {
    public static ShareManager inst() {
        return inst(ShareManager.class);
    }

    @Listener(EventKey.HUMAN_LOGIN_FINISH)
    public void _listener_HUMAN_LOGIN_FINISH(Param param) {
        HumanObject humanObj = Utils.getParamValue(param, "humanObj", null);
        sendSnsShareState(humanObj);
    }

    private void sendSnsShareState(HumanObject humanObj) {
        if (!humanObj.funcOpenList.contains(FuncOpenType.FUNC_SNS_SHARE)) {
            return;
        }

        MsgAct.act_sns_share_state_s2c.Builder msg = MsgAct.act_sns_share_state_s2c.newBuilder();
        msg.setState(humanObj.getHumanExtInfo().getSnsShareState());
        humanObj.sendMsg(msg);
    }

    @Listener(EventKey.FUNCTION_OPEN)
    public void _listener_FUNCTION_OPEN(Param param) {
        HumanObject humanObj = Utils.getParamValue(param, "humanObj", null);
        List<Integer> openFuncList = Utils.getParamValue(param, "openList", new ArrayList<Integer>());
        if (openFuncList.contains(FuncOpenType.FUNC_SNS_SHARE)) {
            initSnsShare(humanObj);
        }
    }

    @Listener(EventKey.FUNCTION_OPEN_LOGIN)
    public void _listener_FUNCTION_OPEN_LOGIN(Param param) {
        HumanObject humanObj = Utils.getParamValue(param, "humanObj", null);
        int sn = Utils.getParamValue(param, "sn", 0);
        if (sn == FuncOpenType.FUNC_SNS_SHARE) {
            initSnsShare(humanObj);
        }
    }

    /**
     * 功能404解锁的时候，就变成可以分享
     */
    private void initSnsShare(HumanObject humanObj) {
        HumanExtInfo extInfo = humanObj.getHumanExtInfo();
        if (extInfo.getSnsShareState() != RewardStateKey.未达成条件.getType()) {
            return;
        }
        extInfo.setSnsShareState(RewardStateKey.可领取.getType());
        extInfo.update();
        MsgAct.act_sns_share_state_s2c.Builder msg = MsgAct.act_sns_share_state_s2c.newBuilder();
        msg.setState(extInfo.getSnsShareState());
        humanObj.sendMsg(msg);
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_回归任务, TaskConditionTypeKey.TASK_TYPE_23, 1);
    }

    /**
     * 领取sns分享奖励
     */
    public void _msg_act_sns_share_reward_c2s(HumanObject humanObj) {
        HumanExtInfo extInfo = humanObj.getHumanExtInfo();
        if (extInfo.getSnsShareState() != RewardStateKey.可领取.getType()) {
            return;
        }

        ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.sns_show_reward.SN);
        Map<Integer, Integer> rewardMap = new HashMap<>();
        for (int i = 0; (i + 1) < confGlobal.intArray.length; i += 2) {
            int num = rewardMap.computeIfAbsent(confGlobal.intArray[i], k -> 0);
            num += confGlobal.intArray[i + 1];
            rewardMap.put(confGlobal.intArray[i], num);
        }
        extInfo.setSnsShareState(RewardStateKey.已领取.getType());
        extInfo.update();
        ProduceManager.inst().produceAdd(humanObj, rewardMap, MoneyItemLogKey.SNS_分享);

        MsgAct.act_sns_share_state_s2c.Builder msg = MsgAct.act_sns_share_state_s2c.newBuilder();
        msg.setState(extInfo.getSnsShareState());
        humanObj.sendMsg(msg);

        InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, InstanceConstants.showType_0, rewardMap);
    }

}
