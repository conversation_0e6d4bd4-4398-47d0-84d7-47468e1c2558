package org.gof.demo.worldsrv.appearance;

import org.gof.core.support.observer.MsgReceiver;
import org.gof.demo.seam.msg.MsgParam;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.msg.MsgChatBubble;
import org.gof.demo.worldsrv.msg.MsgHeadFrame;
import org.gof.demo.worldsrv.msg.MsgTitle;


public class AppearanceMsgHandler {
    /**
     * 处理称号信息C2S消息
     * @param param
     */
    @MsgReceiver(MsgTitle.title_info_c2s.class)
    public void title_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgTitle.title_info_c2s msg = param.getMsg();
        AppearanceManager.inst().title_info_c2s(humanObj);
    }

    /**
     * 处理穿戴称号C2S消息
     * @param param
     */
    @MsgReceiver(MsgTitle.title_wear_c2s.class)
    public void title_wear_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgTitle.title_wear_c2s msg = param.getMsg();
        AppearanceManager.inst().title_wear_c2s(humanObj, msg.getCfgId());
    }

    /**
     * 处理称号红点C2S消息
     * @param param
     */
    @MsgReceiver(MsgTitle.title_red_point_c2s.class)
    public void title_red_point_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgTitle.title_red_point_c2s msg = param.getMsg();
        AppearanceManager.inst().title_red_point_c2s(humanObj, msg.getCfgId());
    }

    /**
     * 处理头像框信息C2S消息
     * @param param
     */
    @MsgReceiver(MsgHeadFrame.head_frame_info_c2s.class)
    public void head_frame_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgHeadFrame.head_frame_info_c2s msg = param.getMsg();
        AppearanceManager.inst().head_frame_info_c2s(humanObj);
    }

    /**
     * 处理穿戴头像框C2S消息
     * @param param
     */
    @MsgReceiver(MsgHeadFrame.head_frame_wear_c2s.class)
    public void head_frame_wear_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgHeadFrame.head_frame_wear_c2s msg = param.getMsg();
        AppearanceManager.inst().head_frame_wear_c2s(humanObj, msg.getCfgId());
    }

    /**
     * 处理头像框红点C2S消息
     * @param param
     */
    @MsgReceiver(MsgHeadFrame.head_frame_red_point_c2s.class)
    public void head_frame_red_point_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgHeadFrame.head_frame_red_point_c2s msg = param.getMsg();
        AppearanceManager.inst().head_frame_red_point_c2s(humanObj, msg.getCfgId());
    }

    /**
     * 处理聊天气泡信息C2S消息
     * @param param
     */
    @MsgReceiver(MsgChatBubble.chat_bubble_info_c2s.class)
    public void chat_bubble_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        AppearanceManager.inst().chat_bubble_info_c2s(humanObj);
    }

    /**
     * 处理更改聊天气泡C2S消息
     * @param param
     */
    @MsgReceiver(MsgChatBubble.change_chat_bubble_c2s.class)
    public void change_chat_bubble_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgChatBubble.change_chat_bubble_c2s msg = param.getMsg();
        AppearanceManager.inst().change_chat_bubble_c2s(humanObj, msg.getCfgId());
    }

    /**
     * 处理聊天气泡红点C2S消息
     * @param param
     */
    @MsgReceiver(MsgChatBubble.chat_bubble_red_point_c2s.class)
    public void chat_bubble_red_point_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgChatBubble.chat_bubble_red_point_c2s msg = param.getMsg();
        AppearanceManager.inst().chat_bubble_red_point_c2s(humanObj, msg.getCfgId());
    }


}
