package org.gof.demo.worldsrv.home.Fish;

/**
 * 鱼类升级信息
 */
public class FishUpgradeInfo {
    /** 鱼类组SN */
    public int fishTypeSn;
    
    /** 原等级 */
    public int oldLevel;
    
    /** 新等级 */
    public int newLevel;
    
    /** 剩余经验 */
    public int remainExp;
    
    /** 升级消耗的货币ID */
    public int costCurrencyId;
    
    /** 升级消耗的货币数量 */
    public long costAmount;
    
    public FishUpgradeInfo() {
    }
    
    public FishUpgradeInfo(int fishTypeSn, int oldLevel, int newLevel, int remainExp, int costCurrencyId, long costAmount) {
        this.fishTypeSn = fishTypeSn;
        this.oldLevel = oldLevel;
        this.newLevel = newLevel;
        this.remainExp = remainExp;
        this.costCurrencyId = costCurrencyId;
        this.costAmount = costAmount;
    }
    
    @Override
    public String toString() {
        return "FishUpgradeInfo{" +
                "fishTypeSn=" + fishTypeSn +
                ", oldLevel=" + oldLevel +
                ", newLevel=" + newLevel +
                ", remainExp=" + remainExp +
                ", costCurrencyId=" + costCurrencyId +
                ", costAmount=" + costAmount +
                '}';
    }
}
