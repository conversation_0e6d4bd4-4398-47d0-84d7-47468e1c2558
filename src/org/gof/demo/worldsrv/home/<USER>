package org.gof.demo.worldsrv.home;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.interfaces.ISerilizable;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.msg.Define;

import javax.xml.bind.DatatypeConverter;
import java.io.IOException;
import java.util.List;

public class StolenVo implements ISerilizable {
    public long cropId;//作物ID
    public long id;//被偷玩家ID
    public int startTime;//开始时间
    public int endTime;//结束时间
    public String name;//被偷玩家名字
    public long head;//被偷玩家头像
    public int level;//被偷玩家等级

    public StolenVo() {
    }
    public StolenVo(long cropId, long id, int startTime, int endTime, String name, long head, int level) {
        this.cropId = cropId;
        this.id = id;
        this.startTime = startTime;
        this.endTime = endTime;
        this.name = name;
        this.head = head;
        this.level = level;
    }
    public String toJsonString() {
        JSONObject json = new JSONObject();
        json.put("cp", cropId);
        json.put("id", id);
        json.put("st", startTime);
        json.put("et", endTime);
        json.put("n", name);
        json.put("h", head);
        json.put("l", level);
        return json.toJSONString();
    }

    public void fromJsonString(String jsonString) {
        JSONObject json = Utils.toJSONObject(jsonString);
        if(json.isEmpty()){
            return;
        }
        cropId = json.getLongValue("cp");
        id = json.getLongValue("id");
        startTime = json.getIntValue("st");
        endTime = json.getIntValue("et");
        name = json.getString("n");
        head = json.getLongValue("h");
        level = json.getIntValue("l");
    }

    public Define.p_farm_self_stolen.Builder build(){
        Define.p_farm_self_stolen.Builder builder = Define.p_farm_self_stolen.newBuilder();
        builder.setCropId(cropId);
        builder.setId(id);
        builder.setStartTime(startTime);
        builder.setEndTime(endTime);
        builder.setName(name);
        Define.p_head.Builder headBuilder = Define.p_head.newBuilder();
        headBuilder.setId(head);
        headBuilder.setFrameId(0);
        headBuilder.setUrl("");
        builder.setHead(headBuilder);
        builder.setLevel(level);
        return builder;
    }

    static public String listToJsonString(List<StolenVo> list) {
        JSONArray jsonArray = new JSONArray();
        for (StolenVo stolenVo : list) {
            jsonArray.add(stolenVo.toJsonString());
        }
        return jsonArray.toJSONString();
    }

    static public List<StolenVo> listFromJsonString(String jsonString) {
        JSONArray jsonArray = Utils.toJSONArray(jsonString);
        List<StolenVo> list = new java.util.ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            StolenVo stolenVo = new StolenVo();
            stolenVo.fromJsonString(jsonArray.getString(i));
            list.add(stolenVo);
        }
        return list;
    }

    public static String listToProtoString(List<StolenVo> list) {
        List<String> stolenList = new java.util.ArrayList<>();
        for (StolenVo stolenVo : list) {
            byte[] byteArray = stolenVo.build().build().toByteArray();
            stolenList.add(DatatypeConverter.printHexBinary(byteArray));
        }
        return String.join(",", stolenList);
    }

    public static List<Define.p_farm_self_stolen> listFromProtoString(String protoString) {
        List<Define.p_farm_self_stolen> stolenList = new java.util.ArrayList<>();
        String[] encodedList = protoString.split(",");
        for (String encodedStolen : encodedList) {
            try {
                byte[] byteArray = DatatypeConverter.parseHexBinary(encodedStolen);
                Define.p_farm_self_stolen stolen = Define.p_farm_self_stolen.parseFrom(byteArray);
                stolenList.add(stolen);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return stolenList;
    }

    @Override
    public void writeTo(OutputStream out) throws IOException {
        out.write(cropId);
        out.write(id);
        out.write(startTime);
        out.write(endTime);
        out.write(name);
        out.write(head);
        out.write(level);
    }

    @Override
    public void readFrom(InputStream in) throws IOException {
        cropId = in.read();
        id = in.read();
        startTime = in.read();
        endTime = in.read();
        name = in.read();
        head = in.read();
        level = in.read();
    }
}
