package org.gof.demo.worldsrv.home;

import com.alibaba.fastjson.JSONObject;
import com.google.protobuf.GeneratedMessageV3;
import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.Port;
import org.gof.core.interfaces.ISerilizable;
import org.gof.core.support.Time;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.msg.Define;

import java.io.IOException;
import java.util.*;

public class LandVo implements ISerilizable {
    public int landId; //土地ID
    public long cropId; //茬id
    public int seedId; //种子ID
    public int cfgId; //配置ID
    public int state; //状态
    public int startTime; //开始时间
    public int accTime; //加速时间
    public int endTime; //结束时间
    public int fruitCount; //果实数量
    public int realFruitCount; //真实果实数量
    public int helpTimes; //帮助次数
    public RobberVo robber = new RobberVo(); //劫财者
    public List<Long> battleList = new ArrayList<>(); //战斗列表
    public List<Long> shareList = new ArrayList<>(); //分享列表
    public Map<Long, Long> robberInfoMap = new HashMap<>(); //劫财者列表

    public LandVo() {}
    public LandVo(int landId) {
        this.landId= landId;
    }
    public LandVo(String jsonStr){
        fromJsonString(jsonStr);
    }

    public void clear() {
        cropId = 0;
        seedId = 0;
        cfgId = 0;
        state = 0;
        startTime = 0;
        accTime = 0;
        endTime = 0;
        fruitCount = 0;
        realFruitCount = 0;
        helpTimes = 0;
        robber = new RobberVo();
        battleList.clear();
        shareList.clear();
        robberInfoMap.clear();
    }

    public void toMature() {
        state = EHomeType.MATURE;
        startTime = 0;
        accTime = 0;
        endTime = 0;
    }

    public String toJsonString() {
        JSONObject json = new JSONObject();
        if (landId != 0) json.put("l", landId);
        if (cropId != 0) json.put("cp", cropId);
        if (seedId != 0) json.put("s", seedId);
        if (cfgId != 0) json.put("c", cfgId);
        if (state != 0) json.put("st", state);
        if (startTime != 0) json.put("stt", startTime);
        if (accTime != 0) json.put("at", accTime);
        if (endTime != 0) json.put("et", endTime);
        if (fruitCount != 0) json.put("fc", fruitCount);
        if (realFruitCount != 0) json.put("rfc", realFruitCount);
        if (helpTimes != 0) json.put("ht", helpTimes);
        if (robber != null && !robber.toJsonString().isEmpty()) json.put("r", robber.toJsonString());
        if (battleList != null && !battleList.isEmpty()) json.put("bl", Utils.listToString(battleList));
        if (shareList != null && !shareList.isEmpty()) json.put("sl", Utils.listToString(shareList));
        if (robberInfoMap != null && !robberInfoMap.isEmpty()) json.put("rm", Utils.mapLongLongToJSON(robberInfoMap));
        return json.toJSONString();
    }

    public void fromJsonString(String jsonString) {
        JSONObject json = Utils.toJSONObject(jsonString);
        if(json.isEmpty()){
            return;
        }
        landId = json.getIntValue("l");
        cropId = json.getLongValue("cp");
        seedId = json.getIntValue("s");
        cfgId = json.getIntValue("c");
        state = json.getIntValue("st");
        startTime = json.getIntValue("stt");
        accTime = json.getIntValue("at");
        endTime = json.getIntValue("et");
        fruitCount = json.getIntValue("fc");
        realFruitCount = json.getIntValue("rfc");
        helpTimes = json.getIntValue("ht");
        robber = new RobberVo();
        robber.fromJsonString(json.getString("r"));
        battleList = Utils.strToLongList(json.getString("bl"));
        shareList = Utils.strToLongList(json.getString("sl"));
        robberInfoMap = Utils.jsonToMapLongLong(json.getString("rm"));
    }

    public Define.p_farm_land.Builder build(long roleId) {
        Define.p_farm_land.Builder builder = Define.p_farm_land.newBuilder();
        builder.setId(landId);
        if(cropId == 0){
            return builder;
        }
        Define.p_farm_crop.Builder crop = Define.p_farm_crop.newBuilder();
        crop.setId(cropId);
        crop.setRoleId(roleId);
        crop.setSeedId(seedId);
        crop.setCfgId(cfgId);
        if(state == EHomeType.COLLECTING && endTime < Port.getTime()/ Time.SEC){
            crop.setState(EHomeType.WAIT_FETCH);
            crop.setEndTime(0);
        }else {
            crop.setState(state);
            crop.setEndTime(endTime);
        }
        crop.setStartTime(startTime);
        crop.setAccTime(accTime);
        crop.setFruitCount(fruitCount);
        crop.setRealFruitCount(realFruitCount);
        crop.setHelpTimes(helpTimes);
        if(robber.id != 0){
            crop.setRobber(robber.build());
        }
        for(long id : battleList) {
            crop.addBattleList(id);
        }
        for(long id : shareList) {
            crop.addShareList(id);
        }
        for(Map.Entry<Long, Long> entry : robberInfoMap.entrySet()) {
            Define.p_key_value.Builder robberInfo = Define.p_key_value.newBuilder();
            robberInfo.setK(entry.getKey());
            robberInfo.setV(entry.getValue());
            crop.addRobberList(robberInfo);
        }
        builder.setCrop(crop);
        return builder;
    }

    @Override
    public void writeTo(OutputStream out) throws IOException {
        out.write(landId);
        out.write(cropId);
        out.write(seedId);
        out.write(cfgId);
        out.write(state);
        out.write(startTime);
        out.write(accTime);
        out.write(endTime);
        out.write(fruitCount);
        out.write(realFruitCount);
        out.write(helpTimes);
        out.write(robber);
        out.write(battleList);
        out.write(shareList);
        out.write(robberInfoMap);
    }

    @Override
    public void readFrom(InputStream in) throws IOException {
        landId = in.read();
        cropId = in.read();
        seedId = in.read();
        cfgId = in.read();
        state = in.read();
        startTime = in.read();
        accTime = in.read();
        endTime = in.read();
        fruitCount = in.read();
        realFruitCount = in.read();
        helpTimes = in.read();
        robber = in.read();
        battleList = in.read();
        shareList = in.read();
        robberInfoMap = in.read();
    }
}
