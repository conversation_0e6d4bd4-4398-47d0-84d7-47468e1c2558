package org.gof.demo.worldsrv.home;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.interfaces.ISerilizable;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.msg.Define;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class StatueTabVo implements ISerilizable {
    public String name = "";
    public List<StatueVo> statueVoList = new ArrayList<>();

    public int getLockStatueNum() {
        int num = 0;
        for (StatueVo statueVo : statueVoList) {
            if (statueVo.isLock == EHomeType.BUFF_LOCK) {
                num++;
            }
        }
        return num;
    }

    public int getQuiltyStatueNum(int quality) {
        int num = 0;
        for (StatueVo statueVo : statueVoList) {
            if (statueVo.quality >= quality) {
                num++;
            }
        }
        return num;
    }

    public String toJsonString() {
        JSONObject json = new JSONObject();
        json.put("n", name);
        JSONArray statueVoJsonArray = new JSONArray();
        for (StatueVo statueVo : statueVoList) {
            statueVoJsonArray.add(statueVo.toJsonString());
        }
        json.put("s", statueVoJsonArray);
        return json.toJSONString();
    }

    public void fromJsonString(String jsonString) {
        JSONObject json = Utils.toJSONObject(jsonString);
        name = json.getString("n");
        JSONArray statueVoJsonArray = json.getJSONArray("s");
        for (int i = 0; i < statueVoJsonArray.size(); i++) {
            StatueVo vo = new StatueVo();
            vo.fromJsonString(statueVoJsonArray.getString(i));
            statueVoList.add(vo);
        }
    }

    public Define.p_farm_statue_tab build(int tab) {
        Define.p_farm_statue_tab.Builder builder = Define.p_farm_statue_tab.newBuilder();
        builder.setTab(tab);
        builder.setName(name);
        for (StatueVo statueVo : statueVoList) {
            builder.addAttrList(statueVo.build());
        }
        return builder.build();
    }

    @Override
    public void writeTo(OutputStream out) throws IOException {
        out.write(name);
        out.write(statueVoList);
    }

    @Override
    public void readFrom(InputStream in) throws IOException {
        name = in.read();
        statueVoList = in.read();
    }
}