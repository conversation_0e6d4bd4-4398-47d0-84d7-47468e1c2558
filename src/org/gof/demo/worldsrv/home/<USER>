package org.gof.demo.worldsrv.home;

import com.alibaba.fastjson.JSONObject;
import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.interfaces.ISerilizable;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.msg.Define;

import java.io.IOException;
import java.util.Map;

public class BuildVo implements ISerilizable {
    public int cfgId; //配置ID
    public int level; //状态
    public int startTime; //开始时间
    public int accTime; //加速时间
    public int endTime; //结束时间

    public void upGrade() {
        level++;
        startTime = 0;
        accTime = 0;
        endTime = 0;
    }

    public String toJsonString() {
        JSONObject json = new JSONObject();
        json.put("c", cfgId);
        json.put("l", level);
        json.put("st", startTime);
        json.put("at", accTime);
        json.put("et", endTime);
        return json.toJSONString();
    }

    public void fromJsonString(String jsonString) {
        JSONObject json = Utils.toJSONObject(jsonString);
        if(json.isEmpty()){
            return;
        }
        cfgId = json.getIntValue("c");
        level = json.getIntValue("l");
        startTime = json.getIntValue("st");
        accTime = json.getIntValue("at");
        endTime = json.getIntValue("et");
    }

    public Define.p_farm_building.Builder build() {
        Define.p_farm_building.Builder builder = Define.p_farm_building.newBuilder();
        builder.setCfgId(cfgId);
        builder.setLevel(level);
        builder.setStartTime(startTime);
        builder.setAccTime(accTime);
        builder.setEndTime(endTime);
        return builder;
    }

    public static String mapToJsonString(Map<Integer, BuildVo> map) {
        JSONObject jsonObject = new JSONObject();
        for (Map.Entry<Integer, BuildVo> entry : map.entrySet()) {
            jsonObject.put(entry.getKey().toString(), entry.getValue().toJsonString());
        }
        return jsonObject.toJSONString();
    }

    public static Map<Integer, BuildVo> mapFromJsonString(String jsonString) {
        JSONObject jsonObject = Utils.toJSONObject(jsonString);
        Map<Integer, BuildVo> map = new java.util.HashMap<>();
        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
            BuildVo vo = new BuildVo();
            vo.fromJsonString(entry.getValue().toString());
            map.put(Integer.parseInt(entry.getKey()), vo);
        }
        return map;
    }

    @Override
    public void writeTo(OutputStream out) throws IOException {
        out.write(cfgId);
        out.write(level);
        out.write(startTime);
        out.write(accTime);
        out.write(endTime);
    }

    @Override
    public void readFrom(InputStream in) throws IOException {
        cfgId = in.read();
        level = in.read();
        startTime = in.read();
        accTime = in.read();
        endTime = in.read();
    }
}
