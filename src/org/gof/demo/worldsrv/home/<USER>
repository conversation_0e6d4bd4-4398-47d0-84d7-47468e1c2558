package org.gof.demo.worldsrv.home;

import com.alibaba.fastjson.JSONObject;
import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.Port;
import org.gof.core.dbsrv.redis.EntityManager;
import org.gof.core.interfaces.ISerilizable;
import org.gof.core.support.Param;
import org.gof.core.support.Time;
import org.gof.demo.worldsrv.character.HumanObjectServiceProxy;
import org.gof.demo.worldsrv.config.ConfFarmGreens;
import org.gof.demo.worldsrv.config.ConfGoods;
import org.gof.demo.worldsrv.entity.*;
import org.gof.demo.worldsrv.human.HumanGlobalInfo;
import org.gof.demo.worldsrv.human.HumanGlobalServiceProxy;
import org.gof.demo.worldsrv.human.HumanManager;
import org.gof.demo.worldsrv.msg.MsgHome;
import org.gof.demo.worldsrv.pocketLine.Pocket;
import org.gof.demo.worldsrv.pocketLine.PocketLineEventSubKey;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.observer.Event;
import org.gof.demo.worldsrv.support.observer.EventKey;

import java.io.IOException;
import java.util.*;

public class FarmVo implements Comparable<FarmVo>, ISerilizable {
    public long id; //农场ID就是玩家ID
    public Map<Integer, LandVo> landMap = new HashMap<>(); //土地列表
    public Map<Integer, BuildVo> buildMap = new HashMap<>(); //建筑列表
    public long nextCheckTime; //下一次检查的时间
    public int state; //状态
    public int stateEndTime; //状态时间

    public FarmVo(){}
    public FarmVo(Farm farm){
        this.id = farm.getId();
        for(int i = EHomeType.LAND_1; i <= EHomeType.LAND_MAX; i++){
            LandVo landVo = new LandVo();
            landVo.fromJsonString(HomeManager.inst().getLand(farm,i));
            if(landVo.landId == 0){
                continue;
            }
            landMap.put(landVo.landId, landVo);
        }
        buildMap = BuildVo.mapFromJsonString(farm.getBuildMap());
        updateStateAndTime();
        calculateNextCheckTime();
    }

    public void handleLandStateGrowingEnd(int landId, Farm farm) {
        LandVo landVo = landMap.get(landId);
        landVo.toMature();
        int oldState = state;
        int oldStateEndTime = stateEndTime;
        updateStateAndTime();
        calculateNextCheckTime();
        HomeManager.inst().setLand(farm,landId,landVo.toJsonString());
        farm.update();
        if(oldState != state || oldStateEndTime != stateEndTime){
            EntityManager.getEntityAsync(Human.class, id, (res) -> {
                if (res.failed()) {
                    Log.game.error("无法获取玩家Human数据humanID={}", id);
                    return;
                }
                Human humanBasic = (Human) res.result();
                if (humanBasic == null) {
                    Log.game.error("更新农场状态失败，找不到玩家数据，id={}", id);
                    return;
                }
                humanBasic.setFarmState(state);
                humanBasic.setFarmTime(stateEndTime);
                humanBasic.update();
            });
        }
        sendOnlineFarmInfo(id,EHomeType.UPDATE_REASON_NONE);
    }

    private void sendOnlineFarmInfo(long id, int reson){
        HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance();
        prx.getInfo(id);
        prx.listenResult(this::_result_globalSendFarmInfo,"reson",reson);
    }

    private void _result_globalSendFarmInfo(Param results, Param context){
        int reson = context.getInt("reson");
        HumanGlobalInfo info = results.get();
        if (info != null) {
            // 在线
            HumanObjectServiceProxy prx = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
            prx.sendFarmInfo(reson);
        }else {
            // 不在线
            if(reson == EHomeType.UPDATE_REASON_BUILD){
                Pocket.add(id, PocketLineEventSubKey.FARM_UPGRADE_FINISH, "");
            }
        }
    }

    public void calculateNextCheckTime() {
        nextCheckTime = Long.MAX_VALUE;

        for (LandVo landVo : landMap.values()) {
            if (landVo.state == EHomeType.GROWING) {
                nextCheckTime = Math.min(nextCheckTime, (landVo.endTime - landVo.accTime) * Time.SEC);
            } else if(landVo.state == EHomeType.STEALING){
                nextCheckTime = Math.min(nextCheckTime, landVo.endTime * Time.SEC);
            }
        }
        for (BuildVo buildVo : buildMap.values()) {
            if (buildVo.startTime > 0) {
                nextCheckTime = Math.min(nextCheckTime, (buildVo.endTime - buildVo.accTime) * Time.SEC);
            }
        }
    }

    public boolean updateStateAndTime() {
        int initialState = this.state;
        int initialStateEndTime = this.stateEndTime;

        boolean hasMatureCrop = false;
        boolean hasGrowingCrop = false;
        int minGrowingTime = Integer.MAX_VALUE;

        for (LandVo landVo : landMap.values()) {
            if (landVo.state == EHomeType.MATURE) {
                hasMatureCrop = true;
            } else if (landVo.state == EHomeType.GROWING) {
                hasGrowingCrop = true;
                minGrowingTime = Math.min(minGrowingTime, landVo.endTime);
            }
        }

        if (hasMatureCrop) {
            this.state = EHomeType.FARM_STEAL;
            this.stateEndTime = 0;
        } else if (hasGrowingCrop) {
            this.state = EHomeType.FARM_GROWING;
            this.stateEndTime = minGrowingTime;
        } else {
            this.state = EHomeType.FARM_NONE;
            this.stateEndTime = 0;
        }

        return this.state != initialState || this.stateEndTime != initialStateEndTime;
    }

    public void handleLandStateStealingEnd(int landId, Farm farm) {
        LandVo landVo = landMap.get(landId);
        landVo.toMature();
        RobberVo oldRobberVo = landVo.robber;
        landVo.robber = new RobberVo();

        int oldState = state;
        int oldStateEndTime = stateEndTime;
        updateStateAndTime();
        calculateNextCheckTime();

        HomeManager.inst().setLand(farm,landId,landVo.toJsonString());
        farm.update();
        if(oldState != state || oldStateEndTime != stateEndTime){
            EntityManager.getEntityAsync(Human.class, id, (res) -> {
                if (res.failed()) {
                    Log.game.error("无法获取玩家Human数据humanID={}", id);
                    return;
                }
                Human humanBasic = (Human) res.result();
                if (humanBasic == null) {
                    Log.game.error("更新农场状态失败，找不到玩家数据，id={}", id);
                    return;
                }
                humanBasic.setFarmState(state);
                humanBasic.setFarmTime(stateEndTime);
                humanBasic.update();
            });
        }
        HumanGlobalServiceProxy prx1 = HumanGlobalServiceProxy.newInstance();
        //被偷成功
        prx1.getInfo(id);
        prx1.listenResult(this::_result_globalStolenCompleted, "landVo", landVo, "oldRobberVo", oldRobberVo);
        //偷成功
        prx1.getInfo(oldRobberVo.id);
        prx1.listenResult(this::_result_globalStealCompleted, "landVo", landVo, "oldRobberVo", oldRobberVo);
    }

    private void _result_globalStolenCompleted(Param results, Param context){
        LandVo landVo = context.get("landVo");
        RobberVo oldRobberVo = context.get("oldRobberVo");
        HumanGlobalInfo info = results.get();
        if (info != null) {
            // 在线
            HumanObjectServiceProxy prx = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
            prx.stolenCompleted(this, landVo.landId, oldRobberVo);
        }else {
            JSONObject jo = HomeManager.inst().getStolenCompletedJson(this, landVo.landId, oldRobberVo);
            Pocket.add(id, PocketLineEventSubKey.FARM_STOLEN, jo.toJSONString());
        }
    }

    private void _result_globalStealCompleted(Param results, Param context){
        LandVo landVo = context.get("landVo");
        RobberVo oldRobberVo = context.get("oldRobberVo");
        HumanGlobalInfo info = results.get();
        if (info != null) {
            // 在线
            HumanObjectServiceProxy prx = HumanObjectServiceProxy.newInstance(info.nodeId, info.portId, info.id);
            prx.stealCompleted(this, landVo.landId);
        }else {
            int addAttr = HomeManager.inst().getBuildingAttr(buildMap,EHomeType.HARVEST_ADD_ATTR);

            ConfFarmGreens confFarmGreens = HomeManager.inst().getConfFarmGreens(landVo.cropId);
            if(confFarmGreens == null){
                Log.game.error("confFarmGreens is null cfgId={}",landVo.cropId);
                return;
            }
            int rewardSn = confFarmGreens.reward[0];
            ConfGoods confGoods = ConfGoods.get(rewardSn);
            if(confGoods == null){
                Log.game.error("confGoods is null sn={}",rewardSn);
                return;
            }
            int stealNum = confFarmGreens.reward[1] * confFarmGreens.stolenRatio/10000 * (1+addAttr/10000);
            int convertNum = stealNum * confGoods.effect[1][0];

            JSONObject jo = new JSONObject();
            jo.put("t", 1);//偷成功
            jo.put("fId", id);
            jo.put("crop",landVo.cropId);
            jo.put("sn",rewardSn);
            jo.put("sNum",stealNum);
            jo.put("cNum",convertNum);
            jo.put("time", (int)(Port.getTime()/Time.SEC));
            Pocket.add(oldRobberVo.id, PocketLineEventSubKey.FARM_STEAL, jo.toJSONString());
        }
    }

    public void handleBuildingUpgradeEnd(BuildVo buildVo, Farm farm) {
        buildVo.level++;
        buildVo.startTime = 0;
        buildVo.accTime = 0;
        buildVo.endTime = 0;
        calculateNextCheckTime();
        farm.setBuildMap(BuildVo.mapToJsonString(buildMap));
        // 重新計算
        for (LandVo landVo : landMap.values()){
            ConfFarmGreens confFarmGreens = ConfFarmGreens.get(landVo.cfgId);
            if (confFarmGreens != null) {
                int speedAttr = HomeManager.inst().getBuildingAttr(BuildVo.mapFromJsonString(farm.getBuildMap()), EHomeType.PLANT_SPEED_ATTR);
                int growingTime = (int)(confFarmGreens.time / (1 + speedAttr / 10000.0));
                if(landVo.state == EHomeType.GROWING){
                    landVo.endTime = landVo.startTime + growingTime;
                    if(landVo.endTime < Port.getTime() / Time.SEC){
                        landVo.toMature();
                    }
                }
                landVo.fruitCount = confFarmGreens.reward[1];
                int addAttr = HomeManager.inst().getBuildingAttr(BuildVo.mapFromJsonString(farm.getBuildMap()), EHomeType.HARVEST_ADD_ATTR);
                landVo.realFruitCount = (int)(confFarmGreens.reward[1] * (1 + addAttr / 10000.0));
                HomeManager.inst().setLand(farm,landVo.landId,landVo.toJsonString());
            }
        }
        Log.farm.info("建筑升级升级结束：farmId={},buildVo = ", farm.getId(), farm.getBuildMap());
        farm.update();
        MsgHome.home_farm_building_lev_up_s2c.Builder msg = MsgHome.home_farm_building_lev_up_s2c.newBuilder();
        msg.setBuilding(buildVo.build());
        HumanGlobalServiceProxy.newInstance().sendMsg(id,msg.build());
        sendOnlineFarmInfo(id,EHomeType.UPDATE_REASON_BUILD);

        EntityManager.getEntityAsync(Human2.class, id, (res) -> {
            if (res.failed()) {
                Log.game.error("无法获取玩家Human2数据humanID={}", id);
                return;
            }
            Human2 human = res.result();
            if (human == null) {
                Log.game.error("更新建筑属性计算失败，找不到玩家数据，id={}", id);
                return;
            }
            Event.fire(EventKey.UPGRADE_FINISH, "type", HumanManager.Upgrade_Finish_Farm, "humanId", id, "guildId", human.getGuildId(), "subType", buildVo.cfgId);
        });
    }


    @Override
    public int compareTo(FarmVo other) {
        return Long.compare(this.nextCheckTime, other.nextCheckTime);
    }

    @Override
    public void writeTo(OutputStream out) throws IOException {
        out.write(id);
        out.write(landMap);
        out.write(buildMap);
        out.write(nextCheckTime);
        out.write(state);
        out.write(stateEndTime);
    }

    @Override
    public void readFrom(InputStream in) throws IOException {
        id = in.read();
        landMap = in.read();
        buildMap = in.read();
        nextCheckTime = in.read();
        state = in.read();
        stateEndTime = in.read();
    }
}
