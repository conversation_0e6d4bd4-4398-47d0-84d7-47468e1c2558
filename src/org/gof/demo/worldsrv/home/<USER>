package org.gof.demo.worldsrv.home;

import com.alibaba.fastjson.JSONObject;
import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.interfaces.ISerilizable;
import org.gof.demo.worldsrv.msg.Define;

import java.io.IOException;

public class StatueVo implements ISerilizable {
    public int pos;
    public int quality;
    public int attrId;
    public long value;
    public int isLock;

    public String toJsonString() {
        JSONObject json = new JSONObject();
        json.put("p", pos);
        json.put("q", quality);
        json.put("a", attrId);
        json.put("v", value);
        json.put("l", isLock);
        return json.toJSONString();
    }
    public void fromJsonString(String string) {
        JSONObject json = JSONObject.parseObject(string);
        pos = json.getIntValue("p");
        quality = json.getIntValue("q");
        attrId = json.getIntValue("a");
        value = json.getLongValue("v");
        isLock = json.getIntValue("l");
    }
    public Define.p_farm_statue_attr build() {
        Define.p_farm_statue_attr.Builder builder = Define.p_farm_statue_attr.newBuilder();
        builder.setPos(pos);
        builder.setQuality(quality);
        builder.setAttrId(attrId);
        builder.setValue(value);
        builder.setIsLock(isLock);
        return builder.build();
    }
    @Override
    public void writeTo(OutputStream out) throws IOException {
        out.write(pos);
        out.write(quality);
        out.write(attrId);
        out.write(value);
        out.write(isLock);
    }

    @Override
    public void readFrom(InputStream in) throws IOException {
        pos = in.read();
        quality = in.read();
        attrId = in.read();
        value = in.read();
        isLock = in.read();
    }
}
