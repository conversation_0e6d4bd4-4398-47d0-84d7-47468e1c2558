package org.gof.demo.worldsrv.home;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.msg.Define;

import java.util.Map;

public class BlockVo {
    public int sn = 0;
    public int count = 0;//可以敲击的次数
    public int isReward = 0;//无奖励
    public BlockVo() {
    }

    public BlockVo(int sn, int count, int isReward) {
        this.sn = sn;
        this.count = count;
        this.isReward = isReward;
    }

    public String toJsonString() {
        JSONArray jsonArray = new JSONArray();
        jsonArray.add(sn);
        jsonArray.add(count);
        jsonArray.add(isReward);
        return jsonArray.toJSONString();
    }

    public void fromJsonString(String jsonString) {
        JSONArray jsonArray = Utils.toJSONArray(jsonString);
        sn = jsonArray.getIntValue(0);
        count = jsonArray.getIntValue(1);
        isReward = jsonArray.getIntValue(2);
    }

    public Define.p_mine_block.Builder build(int id) {
        Define.p_mine_block.Builder builder = Define.p_mine_block.newBuilder();
        builder.setId(id);
        builder.setConfigId(sn);
        builder.setCount(count);
        builder.setIsReward(isReward);
        builder.setX(id % 100);
        builder.setY(id / 100);
        return builder;
    }

    static public String mapToJsonString(Map<Integer, BlockVo> map) {
        JSONObject jsonObject = new JSONObject();
        for (Map.Entry<Integer, BlockVo> entry : map.entrySet()) {
            jsonObject.put(entry.getKey().toString(), entry.getValue().toJsonString());
        }
        return jsonObject.toJSONString();
    }

    static public Map<Integer, BlockVo> mapFromJsonString(String jsonString) {
        JSONObject jsonObject = JSONObject.parseObject(jsonString);
        Map<Integer, BlockVo> map = new java.util.HashMap<>();
        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
            BlockVo blockVo = new BlockVo();
            blockVo.fromJsonString(entry.getValue().toString());
            map.put(Integer.parseInt(entry.getKey()), blockVo);
        }
        return map;
    }
}


