# 钓鱼玩法系统实现

## 概述

本钓鱼系统按照需求文档实现了完整的钓鱼玩法，包括核心钓鱼、图鉴系统、渔具升级、鱼塘管理、自动钓鱼等功能。数据构造采用简洁的JSON格式，避免冗余数据传输。

## 核心文件结构

```
src/org/gof/demo/worldsrv/home/<USER>/
├── HomeFishManager.java        # 核心业务逻辑管理器
├── HomeFishMsgHandler.java     # 消息处理器
├── FishData.java              # 会话缓存数据
├── FishDetail.java            # 鱼类详情数据结构
├── EFishType.java             # 常量定义
├── FishMessageHelper.java     # 消息构建辅助类
├── FishTestHelper.java        # 测试辅助类
└── README.md                  # 本文档
```

## 主要功能实现

### 1. 系统初始化
- **触发条件**: 监听功能开启事件 (ID=96)
- **初始化内容**: 
  - 创建FishData和HomeFish实例
  - 设置初始等级和数据
  - 解锁默认渔场和鱼塘槽位
  - 初始化下一渔场解锁任务

### 2. 核心钓鱼流程
- **抛竿阶段** (`handleFishCastC2S`):
  - 验证玩家状态、鱼饵数量、渔场权限
  - 执行钓鱼逻辑生成鱼类
  - 缓存待结算数据
  - 返回第一条鱼用于客户端表现

- **收竿阶段** (`handleFishReelC2S`):
  - 逃跑率计算和判定
  - 奖励发放和经验增加
  - 图鉴数据更新
  - 统计数据累积

### 3. 钓鱼算法
- **鱼类组随机**: 基于渔场配置、渔具属性、鱼饵加成、月卡加成
- **长度级别随机**: 基于渔具等级限制、丰饵属性、鱼饵长度加成
- **逃跑率计算**: 基础逃跑率 / [(1+渔具稳固) * (1-小鱼稳固)]

### 4. 图鉴系统
- **数据更新**: 破纪录检测、经验累积、等级提升
- **评分计算**: 基础评分 * 等级评分加成 (珍贵鱼除外)
- **升级机制**: 单个升级和一键升级

### 5. 渔具系统
- **升级验证**: 前置条件检查、消耗验证
- **属性计算**: 稳固、力度、专注、效率、丰饵、沉钩、幸运

### 6. 鱼塘系统
- **槽位解锁**: 按顺序解锁、消耗验证
- **装备管理**: 类型匹配、重复检查、属性重算

### 7. 自动钓鱼
- **开始阶段**: 参数验证、状态设置、统计清零
- **结算阶段**: 时间计算、批量模拟、数据汇总

## 数据简洁性设计

### JSON数据优化策略
1. **零值过滤**: 使用 `Utils.putIfNonZero()` 避免传输0值
2. **空值检查**: 使用 `Utils.isEmptyJSONString()` 避免传输空JSON
3. **按需包含**: 只在有数据时才添加到响应中
4. **结构化分组**: 相关数据分组减少字段数量

### 示例数据格式
```json
{
  "fisherLv": 5,
  "fisherExp": 100,
  "albumLv": 2,
  "totalScore": 1000,
  "fishTools": {"1": 3, "2": 2},
  "houseList": {"1": 1, "2": 0, "3": 2},
  "stats": {
    "totalCast": 50,
    "totalSucc": 35,
    "totalSlipped": 15
  }
}
```

## 配置表依赖

系统依赖以下配置表：
- `ConfFishConfig`: 钓鱼基础配置
- `ConfFishBase`: 鱼类基础数据
- `ConfFishGroup`: 鱼类组配置
- `ConfFishBait`: 鱼饵配置
- `ConfFishTool`: 渔具配置
- `ConfFishHouse`: 鱼塘配置
- `ConfFishGround`: 渔场配置
- `ConfFishLevel`: 鱼类升级配置

## 测试使用

### 创建测试数据
```java
FishTestHelper.createTestFishData(humanObj);
```

### 运行完整测试
```java
FishTestHelper.runFullTest(humanObj);
```

### 单独功能测试
```java
FishTestHelper.testFishInfo(humanObj);
FishTestHelper.testFishCast(humanObj, 1, 1, 1);
FishTestHelper.testFishReel(humanObj, 1);
```

## 扩展接口

### 待实现的系统集成
1. **道具系统**: 鱼饵消耗、货币扣除
2. **任务系统**: 渔场解锁任务生成
3. **属性系统**: 战斗属性重算
4. **月卡系统**: 幸运加成获取
5. **战令系统**: 经验奖励发放

### 消息协议扩展
当前使用简化的JSON格式，可根据需要扩展为完整的protobuf协议。

## 注意事项

1. **数据持久化**: HomeFish实体需要正确的数据库映射
2. **并发安全**: 自动钓鱼状态需要考虑并发访问
3. **性能优化**: 批量结算时注意内存使用
4. **错误处理**: 完善的异常处理和日志记录
5. **配置验证**: 启动时验证配置表完整性

## 版本信息

### v5.4 (2025-08-18) - 最新版本
- **数据保存机制优化**: 所有save方法传入HomeFish对象，直接调用homeFish.set方法
- **单独保存支持**: 提供针对特定数据的单独保存方法，提升性能
- **精确数据更新**: 只更新变更的数据字段，避免不必要的数据库操作
- **保存方法统一**: 统一保存接口，便于维护和扩展
- **性能优化**: 减少数据序列化和数据库写入次数

### v5.3 (2025-08-18)
- **一级一级升级逻辑**: 一键升级时一级一级检查货币，货币不足就跳过继续下一个鱼类
- **独立升级处理**: 每个鱼类独立尝试升级，某个失败不影响其他鱼类
- **智能跳过机制**: 货币不足、经验不足、已达最大等级时自动跳过

### v5.2 (2025-08-18)
- **鱼类升级系统重构**: 使用`checkAndCostItem`方法统一处理货币检查和消耗
- **单级升级支持**: 支持指定鱼类升级一级，协议改为`uint32 fish_type`
- **一键升级优化**: 一键升级按ID排序处理，每个鱼类升级到不能升级为止

### v5.1 (2025-08-18)
- **自动钓鱼逻辑修正**: 每次结算处理所有可结算轮次，不是只结算一轮
- **鱼饵按顺序消耗**: 10连钓按顺序消耗鱼饵，每轮消耗10个，支持不同鱼饵ID
- **返回最后轮次鱼**: 返回最后结算轮次的第一条鱼信息用于展示

### v1.0 (2025-08-18) - 基础版本
- **实现特点**: 数据简洁、功能完整、易于扩展

## 数据保存机制

### 单独保存方法
```java
// 只保存图鉴数据
fishData.saveAlbumDetailMapToHomeFish();

// 只保存渔具数据
fishData.saveFishToolsToHomeFish();

// 只保存鱼塘槽位数据
fishData.saveHouseListToHomeFish();

// 只保存任务数据
fishData.saveDailyTasksToHomeFish();
fishData.saveFishGroundTasksToHomeFish();

// 只保存鱼塘装扮数据
fishData.saveHouseDesignToHomeFish();
```

### 使用示例
```java
// 鱼类升级后只保存图鉴数据
if (hasUpgrade) {
    fishData.saveAlbumDetailMapToHomeFish();
    homeFish.update();
}

// 渔具升级后只保存渔具数据
toolMap.put(toolType, nextLevel);
fishData.saveFishToolsToHomeFish();
homeFish.update();

// 鱼塘装备后只保存槽位数据
houseMap.put(locationId, fishGroupSn);
fishData.saveHouseListToHomeFish();
homeFish.update();
```

### 性能优势
- **减少序列化**: 只序列化变更的数据
- **减少数据库写入**: 只更新特定字段
- **提升响应速度**: 避免全量数据处理
- **降低内存使用**: 减少不必要的对象创建

## 测试验证

### 保存机制测试
```java
// 完整保存机制测试
FishSaveTest.runAllSaveTests(humanObj);

// 单独数据保存测试
FishSaveTest.testAlbumDetailMapSave(humanObj);
FishSaveTest.testFishToolsSave(humanObj);
FishSaveTest.testHouseListSave(humanObj);

// 性能对比测试
FishSaveTest.testSavePerformance(humanObj);
```
