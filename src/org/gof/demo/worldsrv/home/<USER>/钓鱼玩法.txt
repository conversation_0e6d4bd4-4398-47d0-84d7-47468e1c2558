  0. 系统初始化与玩家数据模型
  数据模型 (HomeFish.java):
  钓鱼佬等级: fisherLv
  钓鱼经验: fishExp
  图鉴等级: albumLv
  图鉴经验: albumExp
  图鉴总分：totalScore
  图鉴详情: albumDetailMap (Map<Integer, FishDetail>)，其中 FishDetail 为 {fishSn, len, level, exp}
  领取的图鉴评分奖励：albumScoreRewards List<Integer>
  渔具类型等级: fishTools (Map<Integer, Integer>)
  鱼塘: houseList (Map<Integer_tab,Map<Integer, Integer>>) Integer_tab:代表方案,为了扩展以后的多套方案,现在写死Integer_tab=1
  每日任务: dailyTasks, dailyTaskGroupSn, dailyTaskRecv
  渔场解锁: fishGroundTasks, maxUnlockedGround
  自动钓鱼:没有抛竿，默认客户端每次抛竿成功
  fishGround://钓鱼的渔场
  autoFishStartTime: long
  参与自动钓鱼的鱼饵数量: autoFishBaitsSnNum: List<List<Integer>>，客户端指定的鱼饵,鱼饵数量顺序列表,结算同时要扣除
  autoFishCastNum: int //自动钓鱼每次几钓 1或者10
  autoFishSettleCount:   //自动钓鱼的已经结算次数
  totalAlbumFishes: Map<Integer_/鱼类组表的sn, p_home_fish_detail> - 只保存破纪录的鱼，用于最终展示。
  //鱼册数据 fish_type_sn就是fish_group_sn
  message p_home_fish_detail {
    uint32 fish_type_sn = 1;//鱼类组表的sn
    uint32 fish_sn = 2;//鱼类基础表的sn
    uint32 len = 3;
    uint32 lv = 4;
    uint32 exp = 5;
  }
  totalSellFishes: Map<Integer_fishTypeSn, Integer_count> - 累积所有卖出的鱼的数量。
  totalCastCnt: int
  totalReelSuccCnt: int
  totalSlippedCnt: int
  totalRewards: Map<Integer_currencyId, Long_amount> - 累积所有获得的货币。

  会话缓存(放在FishData) - 非持久化:
  pendingGroundSn: int - 手动钓鱼待结算的渔场SN。
  pendingFishSns: List<Integer> - 手动钓鱼待结算的鱼SN列表。
  pendingBaits: List<Integer> - 手动钓鱼待消耗的鱼饵SN列表。
  pendingCastNum: int - 手动钓鱼的次数 (1或10)。
  autoFishLastSettleTime: long - 上次自动钓鱼结算的时间戳。
  1. 功能解锁与初始化
  触发: 监听全局功能开启事件 @Listener(EventKey.FUNCTION_OPEN, id=96)。
  处理逻辑:
  初始化数据: 为玩家创建 FishData 实例，所有数值置为0或空。
  设置初始等级: fisher_lv = 1。
  解锁默认渔场: player.max_unlock_fish_ground = 1。
  解锁槽位：FishHouse_鱼塘表的cost，如果解锁不消耗道具，就默认解锁这个sn的槽位
  初始化下一渔场任务:
  读取 FishGround_渔场表 中 sn = max_unlock_fish_ground 的记录的 FishGround_渔场表中unlock_ground是下一关解锁的渔场的记录
  二维数组unlock 字段，为玩家在 fish_ground_tasks 中生成具体的解锁任务。走通用的任务系统
  2. 核心钓鱼
  接口: handle(home_fish_cast_c2s)
  处理逻辑:
  前置验证:
  检查玩家状态:是否正在自动钓鱼
  检查鱼饵 bait_sn 数量是否 >= cast_num。只处理1和10的情况
  若 cast_num = 10，检查玩家是否解锁此功能 (等级或月卡) 等级配在FishConfig_钓鱼配置表的sn=CONF_FISH_10_CAST_LV 的parameter字段parameter[0][0]。
  检查渔场是否可以用这些个鱼饵，FishGround_渔场表 的int[] usable_bait代表这个渔场可用的鱼饵
  清空旧缓存
  获取bait_sn列表的第一个鱼饵。
  仅调用一次ExecuteFishing逻辑的第一阶段 (Determine Fish)。
  结果: 获得第一条鱼的first_fish_sn。
  更新缓存
  客服端抛竿成功后，客户端会调用handle(home_fish_reel_c2s)接口，cast只处理第一条鱼和缓存第一条鱼，只处理home_fish_reel_c2s 的state是成功的情况，失败直接返回。
  消耗鱼饵:
  读取 FishBait_鱼饵表 中对应 bait_sn 的 sn 字段。
  循环 cast_num 次，每次独立计算：若 sn == BAIT_LOSSLESS (概率不消耗)，则根据 一维数组parameter[0] 字段进行一次随机判定万分比，成功则本次不扣除鱼饵， Utils.random(10000) 是否小于等于这个值。
  随机鱼 (循环 cast_num 次，第一只鱼是缓存这个鱼):
  ExecuteFishing：
  A. 随机鱼类组 (Fish Group):
  读取当前渔场的 FishGround_渔场表 记录，获取 fish_show (鱼类组ID及其基础权重)。
  收集加成:
  渔具: 获取玩家渔具总属性 沉钩 和 幸运。
  鱼饵: 读取 FishBait_鱼饵表，获取对各鱼类组的权重加成。
  月卡: 获取月卡对“珍贵鱼”的幸运加成。
  计算权重: 遍历 fish_show 中的每个鱼类组，应用公式计算其最终权重。
  珍贵鱼最终权重 = 基础权重 * [1 + 渔具幸运(FishTool_渔具表的lucky值) * (1 + 月卡加成*0.01) * 0.01] + 鱼饵加成权重(珍贵鱼饵: 加成珍贵鱼概率parameter[0]) FishBase_鱼类基础表的fish_group_id 找到ConfFishGroup_鱼类组表的type是鱼类型
  其他鱼类组最终权重 = 基础权重 * (1 + 渔具权重加成*0.01) + 鱼饵权重加成(不同的渔饵加成不同鱼类型的概率parameter[0])FishBase_鱼类基础表的fish_group_id 找到ConfFishGroup_鱼类组表的type是鱼类型
  随机: 根据所有鱼类组的最终权重，随机出一个 fish_group_id。
  B. 随机长度级别 (grade):
  读取 FishBase_鱼类基础表，筛选出 fish_group_id 等于上一步结果、且 need_tool <= 玩家渔具总等级 的所有记录。
  收集加成: 获取渔具 丰饵 属性和鱼饵对长度的加成。丰饵：ConfFishTool.get(toolType, toolLv).size_factor 所有渔具这个值加起来加起来
  计算权重: 最终权重 = 基础权重 + 渔具丰饵 + 鱼饵长度加成(加成更长的鱼概率类型的鱼饵parameter[0])。
  随机: 根据所有符合条件的长度级别 (grade) 的最终权重，随机出一个 grade。
  C. 结果暂存: 将最终确定的 FishBase_鱼类基础表 的 sn，存入玩家的 session_cache.pending_fish_sns 列表中。
  响应: 发送 home_fish_cast_s2c，fish_id 为暂存列表中的第一条鱼的 sn (用于客户端表现)。
  接口: handle(home_fish_reel_c2s)
  处理逻辑:
  获取上下文: 从 session_cache.pending_fish_sns 取出所有待结算的鱼 sn。
  清空缓存: session_cache.pending_fish_sns.clear()。
  初始化结算数据: 创建临时的 reel_succ_cnt, slipped_cnt, reward_list, album_fishes_update 等变量。
  循环结算每一条鱼:
  权威逃跑判定:
  计算逃跑率: 读取 FishConfig, FishBase, 玩家渔具属性，应用 逃跑率=基础逃跑率（表配置为万分比）*0.0001/[（1+渔具稳固*0.01）*（1-小鱼稳固*0.01）]
  IF 逃跑:
  slipped_cnt++。
  ELSE 成功:
  reel_succ_cnt++。
  A. 奖励发放:
  读取 FishGroup.sell，累加货币到 reward_list。FishBase_鱼类基础表的fish_group_id 找到ConfFishGroup_鱼类组表
  读取 FishGroup.exp，调用 AddFisherExp(exp)。
  B. 图鉴数据更新:
  在 FishBase.len_range 内随机一个长度。Utils.random(fishBase.len_range[0], fishBase.len_range[1])
  对比玩家 album_detail 中的历史记录，若破纪录，则更新 max_len 和 max_len_grade，并标记为"新纪录"。
  累积该鱼类的经验值 album_detail[fish_type_sn].exp++。
  将被更新的图鉴详情加入 album_fishes_update 列表。
  累加钓鱼者经验FishGroup_鱼类组表 的exp AddFisherExp(exp)
  最大长度的变更导致FishBase的sn变更导致属性的变化 读取FishBase_鱼类基础表 attr_base 变更属性 updatePorpCalcPower(HumanObject humanObj),
  响应: 发送 home_fish_reel_s2c，填充所有汇总后的结算数据。
  3. 核心养成系统 (Progression Systems)
  接口: handle(home_fish_group_level_up_c2s)
  处理逻辑:如果参数是0，就是一键升级，所有的鱼都升级到最大等级
  验证: 获取 fish_type_sn，检查 album_detail 中该鱼的经验是否足够。
  获取消耗: 读取 FishLevel_鱼类升级表 中 level = 当前鱼等级 + 1 的 cost。
  验证消耗: 检查玩家货币是否足够。
  执行:
  扣除货币。
  提升鱼的等级 album_detail[fish_type_sn].level++。扣除经验
  如果一键对所有鱼升级到最大等级
  使用 while 循环检查是否可升级：
    读取 Fisher_钓鱼等级表 中 sn = player.fisher_lv + 1 的 need 经验。
    若 player.fisher_exp >= need，则 fisher_lv++，fisher_exp -= need，并派发等级提升事件。
    否则跳出循环。
  更新总评分:珍贵鱼以外的其他鱼会拥有“评分”，每个鱼的评分=基础评分*鱼类等级评分加成,基础评分读取FishBase_鱼类基础表的【base】,鱼类等级评分加成读取FishLevel_鱼类升级表的【score_mult】,珍贵鱼没有鱼雷登长度概念，不保留，获得时自动出售
  重新计算这条鱼的新评分，更新 total_score。
  检查渔册升级:
  响应: 发送 home_fish_group_level_up_s2c，包含所有变更后的数据。
  内部接口: private CheckAlbumLevelUp()
  处理逻辑:
  累加 album_detail 中所有鱼的等级，得到 total_fish_level。
  读取 FishBook_渔册表，检查 total_fish_level 是否满足下一级 need。
  若满足，则提升 album_lv。
  触发: total_score 发生变化时。
  处理属性变化public void FishBook_渔册表的attr updatePorpCalcPower(HumanObject humanObj),

  4. 周边辅助系统 (Ancillary Systems)
  接口: handle(home_fish_unlock_house_slot_c2s)槽位的解锁要按顺序解锁,消耗FishHouse_鱼塘表的cost解锁
  处理逻辑:

  接口: handle(home_fish_house_equip_c2s)
  处理逻辑:
  验证:
  获取 location_id 和 fish_group_sn。
  若装备（fish_type_sn > 0），则读取 FishHouse_鱼塘表 检查该槽位是否允许放入该类型的鱼。检查该鱼是否已在其他槽位装备。
  执行:
  更新 ：house_list 中对应 location_id 的值。解锁就加入，没装备就fish_group_sn就是0，装备就是fish_group_sn，没解锁就不存在
  属性重算:
  重新计算所有已装备鱼提供的“展示属性”总和，并更新玩家的战斗属性。
  响应: 发送 home_fish_house_equip_s2c。
  接口: handle(home_fish_tool_lv_up_c2s)
  处理逻辑:
  验证:
  获取 tool_type，读取 FishTool_渔具表 中当前等级+1的记录。
  检查 condition 字段，确认其他渔具是否满足等级前置条件。
  检查 cost 字段，确认货币是否足够。
  执行: 扣除货币，提升 player.fish_tools[tool_type] 的等级。
  属性重算: 重新计算玩家渔具提供的所有钓鱼加成属性。
  响应: 发送 home_fish_tool_lv_up_s2c。
  5. 自动化与批量系统 (Automation & Batch)
  接口: handle(home_fish_start_auto_c2s) 和 handle(home_fish_fin_auto_c2s)。
  处理逻辑 (fin_auto):
  计算次数: 根据 start_time 和当前时间，以及 FishConfig 中的间隔，计算总钓鱼次数。若 multiple=10 则乘以10。
  模拟消耗: 根据 bait_sn_num 列表模拟消耗，确定实际可进行的钓鱼次数。
  批量结算 (核心):
  在内存中循环 实际钓鱼次数:
  完全复用“核心钓鱼循环”的随机和判定逻辑，得到单次结果（成功/失败，获得的鱼）。
  将单次结果（奖励、图鉴更新等）累加到临时的汇总变量中。
  应用变更: 循环结束后，将汇总变量中的所有数据（总经验、总货币、所有图鉴更新）一次性应用到 PlayerFishData 上。
  响应: 发送 home_fish_fin_auto_s2c，包含完整的汇总结果。
  6. 日常与周期性系统 (Recurring Systems)
  每日任务:
  刷新 (每日0点):
  根据 max_unlock_fish_ground 读取 FishTaskGroup_钓鱼任务组表，按权重随机一组新任务。
  进度更新: 更新任务计数。
  战令/月卡:
  提供接口用于领取每日奖励。


