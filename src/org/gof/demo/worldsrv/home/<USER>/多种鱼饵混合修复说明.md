# 多种鱼饵混合使用修复说明

## 修复的核心问题

### 原有问题
**每轮钓鱼只能使用一种鱼饵**：
- 原来的逻辑：每轮10连钓只使用一种鱼饵，如果鱼饵1只有6个，那么这轮只能钓6次
- 用户期望：每轮10连钓应该按顺序消耗多种鱼饵，鱼饵1用完6个后自动使用鱼饵2的4个

### 修复后的逻辑
**每轮钓鱼支持多种鱼饵混合**：
- 新逻辑：每轮10连钓会按顺序消耗多种鱼饵，直到凑够10个或鱼饵用完
- 例如：鱼饵1有6个，鱼饵2有8个 → 第1轮使用6个鱼饵1 + 4个鱼饵2

## 核心修复方法

### 1. getCurrentBaitInfosForRound 方法
```java
/**
 * 获取指定轮次的鱼饵消耗信息（支持多种鱼饵混合）
 */
private List<BaitConsumptionInfo> getCurrentBaitInfosForRound(HomeFish homeFish, int roundOffset) {
    // 计算本轮需要的鱼饵数量
    int needThisRound = multiple; // 10连钓 = 10个，1连钓 = 1个
    
    List<BaitConsumptionInfo> roundBaits = new ArrayList<>();
    
    // 按顺序分配鱼饵，可能需要多种鱼饵
    for (List<Integer> baitInfo : baitSnNumList) {
        if (needThisRound <= 0) break;
        
        int baitSn = baitInfo.get(0);
        int available = calculateAvailableBait(baitInfo, totalConsumed);
        int useThisBait = Math.min(available, needThisRound);
        
        if (useThisBait > 0) {
            roundBaits.add(new BaitConsumptionInfo(baitSn, useThisBait));
            needThisRound -= useThisBait;
        }
    }
    
    return roundBaits;
}
```

### 2. executeOneRoundWithMultipleBaits 方法
```java
/**
 * 执行一轮自动钓鱼（支持多种鱼饵混合）
 */
private AutoFishResult executeOneRoundWithMultipleBaits(HumanObject humanObj, List<BaitConsumptionInfo> baitInfos) {
    // 按鱼饵顺序执行钓鱼
    for (BaitConsumptionInfo baitInfo : baitInfos) {
        int baitSn = baitInfo.baitSn;
        int fishCount = baitInfo.actualConsumeCount;
        
        // 使用当前鱼饵执行钓鱼
        for (int i = 0; i < fishCount; i++) {
            // 生成鱼类、逃跑判定、奖励处理、图鉴更新等
            executeSingleFish(humanObj, groundSn, baitSn, result);
        }
    }
    
    return result;
}
```

### 3. 修改主执行流程
```java
// 第一步：预先收集所有轮次的鱼饵消耗信息（支持多种鱼饵混合）
List<List<BaitConsumptionInfo>> allRoundBaitInfos = new ArrayList<>();

for (int round = 0; round < rounds; round++) {
    List<BaitConsumptionInfo> roundBaitInfos = getCurrentBaitInfosForRound(homeFish, round);
    if (roundBaitInfos.isEmpty()) {
        break; // 鱼饵不足
    }
    allRoundBaitInfos.add(roundBaitInfos);
    
    // 收集本轮所有鱼饵消耗
    for (BaitConsumptionInfo baitInfo : roundBaitInfos) {
        baitCollector.addConsumption(baitInfo.baitSn, baitInfo.actualConsumeCount);
    }
}
```

## 使用场景示例

### 场景1：10连钓混合鱼饵
**配置**：鱼饵1有6个，鱼饵2有8个
```
第1轮(10连钓): 6个鱼饵1 + 4个鱼饵2 = 10次钓鱼
第2轮(10连钓): 4个鱼饵2 = 4次钓鱼（余数处理）
```

### 场景2：复杂多种鱼饵
**配置**：鱼饵1有3个，鱼饵2有7个，鱼饵3有12个
```
第1轮(10连钓): 3个鱼饵1 + 7个鱼饵2 = 10次钓鱼
第2轮(10连钓): 10个鱼饵3 = 10次钓鱼  
第3轮(10连钓): 2个鱼饵3 = 2次钓鱼（余数处理）
```

### 场景3：1倍钓鱼
**配置**：鱼饵1有2个，鱼饵2有3个
```
第1轮(1连钓): 1个鱼饵1 = 1次钓鱼
第2轮(1连钓): 1个鱼饵1 = 1次钓鱼
第3轮(1连钓): 1个鱼饵2 = 1次钓鱼
第4轮(1连钓): 1个鱼饵2 = 1次钓鱼
第5轮(1连钓): 1个鱼饵2 = 1次钓鱼
```

## 数据结构变化

### BaitConsumptionInfo 保持不变
```java
static class BaitConsumptionInfo {
    public int baitSn;              // 鱼饵SN
    public int actualConsumeCount;  // 实际消耗数量
}
```

### 新增：每轮多种鱼饵支持
```java
// 原来：每轮一种鱼饵
BaitConsumptionInfo baitInfo = getCurrentBaitInfoForRound(homeFish, round);

// 现在：每轮多种鱼饵
List<BaitConsumptionInfo> roundBaitInfos = getCurrentBaitInfosForRound(homeFish, round);
```

## 批量处理优化保持

### 鱼饵收集逻辑
```java
// 收集本轮所有鱼饵消耗
for (BaitConsumptionInfo baitInfo : roundBaitInfos) {
    baitCollector.addConsumption(baitInfo.baitSn, baitInfo.actualConsumeCount);
}
```

### 批量扣除逻辑
```java
// 批量扣除所有鱼饵（一次性操作）
batchConsumeBaits(humanObj, baitCollector.getTotalConsumption());
```

## 兼容性保证

### 1. 向后兼容
- 保留 `getCurrentBaitInfoForRound` 方法作为兼容接口
- 现有的单鱼饵逻辑仍然可以正常工作
- 客户端无需修改

### 2. 数据格式兼容
- 鱼饵列表格式不变：`[[baitSn1, count1], [baitSn2, count2]]`
- 统计数据格式不变
- 响应消息格式不变

## 性能影响

### 计算复杂度
- **时间复杂度**：O(轮次数 × 鱼饵种类数) - 与原来相同
- **空间复杂度**：O(轮次数 × 鱼饵种类数) - 略有增加（存储多种鱼饵信息）

### 实际性能
- **数据库操作**：仍然是批量处理，性能优化保持
- **内存使用**：每轮多存储几个鱼饵对象，影响很小
- **执行效率**：钓鱼逻辑本身没有变化，效率相同

## 测试覆盖

### MultipleBaitTest.java 测试类
1. **testSingleRoundMultipleBaits**：测试单轮多种鱼饵混合
2. **testComplexMultipleBaitScenario**：测试复杂的多轮多种鱼饵场景
3. **testSingleFishingMultipleBaits**：测试1倍钓鱼的多种鱼饵场景
4. **testExactBaitAmount**：测试边界情况（鱼饵刚好够用）

### 测试场景覆盖
- ✅ 10连钓混合多种鱼饵
- ✅ 1连钓顺序消耗鱼饵
- ✅ 余数鱼饵处理
- ✅ 鱼饵刚好用完的边界情况
- ✅ 复杂的多种鱼饵组合

## 日志输出示例

```
使用鱼饵钓鱼: baitSn=1, count=6, humanId=12345
使用鱼饵钓鱼: baitSn=2, count=4, humanId=12345
第1轮鱼饵分配:
  鱼饵SN=1, 数量=6
  鱼饵SN=2, 数量=4
第1轮鱼饵分配正确，总数=10
第1轮鱼饵分配完全正确: 6个鱼饵1 + 4个鱼饵2
```

## 用户体验提升

### 修复前
- 鱼饵1有6个，10连钓只能钓6次，浪费了4次机会
- 需要手动管理鱼饵数量，确保每种鱼饵都是10的倍数

### 修复后  
- 鱼饵1有6个，鱼饵2有足够数量，10连钓可以钓满10次
- 自动按顺序消耗多种鱼饵，用户体验更流畅
- 最大化利用所有鱼饵，不浪费任何钓鱼机会

## 注意事项

1. **鱼饵顺序**：严格按照客户端发送的鱼饵列表顺序消耗
2. **无损鱼饵**：每种鱼饵的无损概率独立计算
3. **统计准确性**：确保多种鱼饵的统计数据正确累积
4. **错误处理**：如果某种鱼饵配置错误，不影响其他鱼饵的使用

这个修复让自动钓鱼系统更加智能和用户友好，真正实现了"按需混合使用多种鱼饵"的功能。
