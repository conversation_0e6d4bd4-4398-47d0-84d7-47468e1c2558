package org.gof.demo.worldsrv.home.Fish;

import org.gof.core.support.observer.MsgReceiver;
import org.gof.demo.seam.msg.MsgParam;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.msg.MsgHome;

/**
 * 钓鱼消息处理器
 */
public class HomeFishMsgHandler {
    
    /**
     * 钓鱼数据查询
     */
    @MsgReceiver(MsgHome.home_fish_data_c2s.class)
    public void home_fish_data_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        HomeFishManager.inst().handleFishDataC2S(humanObj);
    }

    /**
     * 领取总评分奖励
     */
    @MsgReceiver(MsgHome.home_fish_get_album_reward_c2s.class)
    public void home_fish_get_album_reward_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgHome.home_fish_get_album_reward_c2s msg = param.getMsg();
        HomeFishManager.inst().handleGetAlbumRewardC2S(humanObj, msg.getScoreTaskSn());
    }

    /**
     * 领取每日任务奖励
     */
    @MsgReceiver(MsgHome.home_fish_daily_task_reward_c2s.class)
    public void home_fish_daily_task_reward_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        HomeFishManager.inst().handleDailyTaskRewardC2S(humanObj);
    }

    /**
     * 渔场解锁
     */
    @MsgReceiver(MsgHome.home_fish_ground_unlock_c2s.class)
    public void home_fish_ground_unlock_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgHome.home_fish_ground_unlock_c2s msg = param.getMsg();
        HomeFishManager.inst().handleGroundUnlockC2S(humanObj, msg.getFishGroundSn());
    }
    
    /**
     * 抛竿钓鱼
     * fish_ground 渔场SN
     * bait_sn 鱼饵SN
     * cast_num 抛竿次数(1或10)
     */
    @MsgReceiver(MsgHome.home_fish_cast_c2s.class)
    public void home_fish_cast_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgHome.home_fish_cast_c2s msg = param.getMsg();
        HomeFishManager.inst().handleFishCastC2S(humanObj, msg.getFishGround(), msg.getBaitSnList(), msg.getCastNum());
    }
    
    /**
     * 收竿结算
     * state 收竿状态(成功/失败)
     */
    @MsgReceiver(MsgHome.home_fish_reel_c2s.class)
    public void home_fish_reel_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgHome.home_fish_reel_c2s msg = param.getMsg();
        HomeFishManager.inst().handleFishReelC2S(humanObj, msg.getStatus());
    }
    
    /**
     * 鱼类升级
     * fish_type 鱼类组SN (0表示一键升级)
     */
    @MsgReceiver(MsgHome.home_fish_group_level_up_c2s.class)
    public void home_fish_group_level_up_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgHome.home_fish_group_level_up_c2s msg = param.getMsg();
        HomeFishManager.inst().handleFishGroupLevelUpC2S(humanObj, msg.getFishType());
    }
    
    /**
     * 解锁鱼塘槽位
     * location_id 槽位ID
     */
    @MsgReceiver(MsgHome.home_fish_unlock_house_slot_c2s.class)
    public void home_fish_unlock_house_slot_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgHome.home_fish_unlock_house_slot_c2s msg = param.getMsg();
        HomeFishManager.inst().handleFishUnlockHouseSlotC2S(humanObj, msg.getLocationId());
    }

    /**
     * 鱼塘装扮解锁升级
     * sn
     */
    @MsgReceiver(MsgHome.home_fish_house_design_level_up_c2s.class)
    public void home_fish_house_design_level_up_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgHome.home_fish_house_design_level_up_c2s msg = param.getMsg();
        HomeFishManager.inst().handleFishHouseDesignLevelUpC2S(humanObj, msg.getSn());
    }

    /**
     * 鱼塘装扮使用
     * sn
     */
    @MsgReceiver(MsgHome.home_fish_house_set_use_design_c2s.class)
    public void home_fish_house_set_use_design_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgHome.home_fish_house_set_use_design_c2s msg = param.getMsg();
        HomeFishManager.inst().handleFishHouseSetUseDesignC2S(humanObj, msg.getSn());
    }
    
    /**
     * 鱼塘装备鱼
     * location_id 槽位ID
     * fish_group_sn 鱼类组SN (0表示卸下)
     */
    @MsgReceiver(MsgHome.home_fish_house_equip_c2s.class)
    public void home_fish_house_equip_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgHome.home_fish_house_equip_c2s msg = param.getMsg();
        HomeFishManager.inst().handleFishHouseEquipC2S(humanObj, msg.getLocationId(), msg.getFishTypeSn());
    }

    /**
     * 鱼塘一键装备
     */
    @MsgReceiver(MsgHome.home_fish_house_equip_one_click_c2s.class)
    public void home_fish_house_equip_one_click_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgHome.home_fish_house_equip_one_click_c2s msg = param.getMsg();
        HomeFishManager.inst().handleFishHouseEquipOneClickC2S(humanObj, msg.getEquipListList());
    }
    
    /**
     * 渔具升级
     * tool_type 渔具类型
     */
    @MsgReceiver(MsgHome.home_fish_tool_lv_up_c2s.class)
    public void home_fish_tool_lv_up_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgHome.home_fish_tool_lv_up_c2s msg = param.getMsg();
        HomeFishManager.inst().handleFishToolLvUpC2S(humanObj, msg.getToolType());
    }
    
    /**
     * 开始自动钓鱼
     * ground_sn 渔场SN
     * bait_sn_num 鱼饵SN和数量列表
     * cast_num 每次抛竿数(1或10)
     */
    @MsgReceiver(MsgHome.home_fish_start_auto_c2s.class)
    public void home_fish_start_auto_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgHome.home_fish_start_auto_c2s msg = param.getMsg();
        HomeFishManager.inst().handleFishStartAutoC2S(humanObj, msg.getFishGround(), msg.getBaitSnNumList(), msg.getMultiple());
    }
    
    /**
     * 结束自动钓鱼
     */
    @MsgReceiver(MsgHome.home_fish_fin_auto_c2s.class)
    public void home_fish_fin_auto_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        HomeFishManager.inst().handleFishFinAutoC2S(humanObj);
    }

    /**
     * 自动钓鱼结算
     */
    @MsgReceiver(MsgHome.home_fish_auto_settle_c2s.class)
    public void home_fish_auto_settle_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
//        if (!humanObj.isMsgIdCD(org.gof.demo.worldsrv.msg.MsgIds.home_fish_auto_settle_c2s, 2)) {
//            return;
//        }
        HomeFishManager.inst().handleFishAutoSettleC2S(humanObj);
    }
}
