package org.gof.demo.worldsrv.redis;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

import io.vertx.core.Future;
import io.vertx.core.json.JsonArray;
import io.vertx.redis.client.Response;
import org.gof.core.Port;
import org.gof.core.dbsrv.redis.AsyncActionResult;
import org.gof.core.dbsrv.redis.AwaitUtil;
import org.gof.core.dbsrv.redis.EntityManager;
import org.gof.core.dbsrv.redis.RedisTools;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.config.ConfGlobal;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.human.HumanData;
import org.gof.demo.worldsrv.rank.RankManager;
import org.gof.demo.worldsrv.rank.RankParamKey;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.StringZipUtils;
import org.gof.demo.worldsrv.support.enumKey.ConfGlobalKey;
import org.gof.demo.worldsrv.team.TeamInfo;
import org.gof.demo.worldsrv.team.TeamMember;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.Transaction;

/**
 * redis客户端管理器
 */
public class RedisManager {

	public static void init(){
	}

	/**
	 * 获取锁
	 * @param conn
	 * @param lockName
	 * @return
	 */
	public String acquireLock(Jedis conn, String lockName){
		return acquireLock(conn,lockName,5000);
	}

	/**
	 * 获取锁
	 * @param conn
	 * @param lockName
	 * @param timeOut
	 * @return
	 */
	public String acquireLock(Jedis conn, String lockName, int timeOut){
		String uuid = UUID.randomUUID().toString();
		long time = System.currentTimeMillis();
		long endTime = time+timeOut;
		String lock_name = "lock:"+lockName;
		while(time<endTime){
			if(conn.setnx(lock_name, uuid)==0){
				conn.expire(lock_name, timeOut);
				return uuid;
			}else if(conn.ttl(lock_name)<0){
				conn.expire(lock_name, timeOut);
			}else{
				return uuid;
			}
			try {
				Thread.sleep(100);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
			time = System.currentTimeMillis();
		}
		return null;
	}

	/**
	 * 释放锁
	 * @param conn
	 * @param lockName
	 */
	public boolean releaseLock(Jedis conn, String lockName, String lock) {
		String lock_name = "lock:" + lockName;
		conn.watch(lock_name);
		try {
			if (conn.get(lock_name).equals(lock)) {
				Transaction trans = conn.multi();
				trans.del(lock_name);
				if (trans.exec()!=null) {
					return true;
				}
			}
		} finally {
			conn.unwatch();
		}
		return false;
	}

	public static List<TeamMember> getHumanRedis(TeamInfo teamInfoTemp, int level, int num, int serverId){
		List<TeamMember> jsonList = new ArrayList<>();
//		if(num <= 0){
//			return jsonList;
//		}
//		List<Long> idList = new ArrayList<>();
//		idList.addAll(teamInfoTemp.getMemberIds());
//		int matchIndex = teamInfoTemp.getMatchIndex();
//		int levelAdd = 0;
//		if(matchIndex == 0){
//			// 初始按随机概率匹配
//			int index =Utils.getRandRange(GlobalConfVal.teamPro);
//			if(index < 0 || index >= GlobalConfVal.teamMatchNum.length){
//				Log.temp.error("===组队匹配配置出错，index ={}, 数组超过范围 teamMatchNum={}", index, Utils.intArrToList(GlobalConfVal.teamMatchNum));
//			} else {
//				levelAdd = GlobalConfVal.teamMatchNum[index];
//			}
//		} else {
//
//			if(matchIndex >= GlobalConfVal.teamMatch.length){
//				matchIndex = GlobalConfVal.teamMatch.length - 1;
//			}
//			levelAdd = GlobalConfVal.teamMatch[matchIndex];
//		}
//		// 根据次数变化
//		matchIndex++;
//		teamInfoTemp.setMatchIndex(matchIndex);
//
//		int minLv = level - levelAdd;
//		minLv = minLv < 1 ? 1 : minLv;
//		int maxLv = level + levelAdd;
//		maxLv = maxLv > GlobalConfVal.MAX_LEVEL ? GlobalConfVal.MAX_LEVEL : maxLv;
//
//		int tempLv = 0;
//		String redisKey = RankManager.inst().getRedisRankTypeKey(RankParamKey.rankTypeLevel_1001, serverId);
//
//		RedisTools.getRankListByScore(EntityManager.redisClient, redisKey, minLv, maxLv, false, (ret)->{
//			if(!ret.succeeded()) {
//				Log.temp.error("获取排行榜失败, rankSn={}", RankParamKey.rankTypeArena_1004);
//				return;
//			}
//			JsonArray json = ret.result();
//			for(int i = 0; i < json.getList().size(); i++) {
//				long id = Utils.longValue(json.getList().get(i));
//				if (idList.contains(id)) {
//					continue;
//				}
//				HumanData.getHumanDataAsync(id, result->{
//					if(!result.succeeded()){
//						Log.temp.error("获取玩家数据失败，humanId={}, {}", id, result.cause());
//						return;
//					}
//					HumanData humanData = result.result();
//					TeamMember teamMember = new TeamMember(humanData);
//					jsonList.add(teamMember);
//					idList.add(id);
//
//				});
//			}
//		});
//
//		List<String> strList = Utils.getRedisZrangebyscore(redisKey, String.valueOf(minLv), String.valueOf(maxLv));
//
//		for(int i = 0; i < num; i++) {
//			boolean added = false;
//			for (String str : strList) {
//				long id = Utils.longValue(str);
//				if (idList.contains(id)) {
//					continue;
//				}
//				HumanData humanData = HumanData.getHumanData(id);
//				if (humanData != null) {
//					TeamMember teamMember = new TeamMember(humanData);
//					jsonList.add(teamMember);
//					idList.add(id);
//					added = true;
//					break;
//				}
//			}
//			if (added) {
//				continue;
//			}
//			for(; tempLv > 0; tempLv--){
//				List<String> strListTemp = Utils.getRedisZrangebyscore(redisKey, String.valueOf(tempLv), String.valueOf(tempLv));
//				for (String str : strListTemp) {
//					long id = Utils.longValue(str);
//					if (idList.contains(id)) {
//						continue;
//					}
//					HumanData humanData = HumanData.getHumanData(id);
//					if (humanData != null) {
//						TeamMember teamMember = new TeamMember(humanData);
//						jsonList.add(teamMember);
//						idList.add(id);
//						added = true;
//						break;
//					}
//				}
//			}
//			if (added) {
//				continue;
//			}
//			if (jsonList.size() >= num) {
//				return jsonList;
//			}
//		}
		return jsonList;
	}

	public static List<String> getHumanRedisLvList(int num, int serverId){
		List<String> jsonList = new ArrayList<>();
		if(num <= 0){
			return jsonList;
		}
		List<String> keyList = new ArrayList<>();
		keyList.add(RankManager.inst().getRedisRankTypeKey(RankParamKey.rankTypeLevel_1001, serverId));
		keyList.add("0");
		keyList.add(String.valueOf(num));
		try {
			Port port = Port.getCurrent();
			Response response = AwaitUtil.awaitResult(handler -> {
				EntityManager.redisClient.zrange(keyList, r -> {
					if (r.succeeded()) {
						AsyncActionResult.success(port, handler, r.result());
					} else {
						AsyncActionResult.fail(port,handler,r.cause());
					}
				});
			});
			Iterator itrs = response.iterator();
			while(itrs.hasNext()) {
				String key = String.valueOf(itrs.next());
				jsonList.add(key);
			}
		} catch (Exception e) {
			Log.temp.error("===e = {}", e);
		}
		// 输出结果
//		Log.temp.info("===keyList={}, jsonList={}, +{}", keyList, jsonList);
		return jsonList;
	}


}
