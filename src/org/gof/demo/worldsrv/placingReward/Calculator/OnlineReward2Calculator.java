package org.gof.demo.worldsrv.placingReward.Calculator;

import io.vertx.core.Handler;
import org.gof.core.support.Param;
import org.gof.core.support.ParamKey;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.entity.Capture;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.instance.ConfChapterVO;
import org.gof.demo.worldsrv.placingReward.PlacingRewardVo;
import org.gof.demo.worldsrv.support.Log;

import java.util.HashMap;
import java.util.Map;

public class OnlineReward2Calculator extends AbstractReward2Calculator {
    private static OnlineReward2Calculator instance = new OnlineReward2Calculator();

    public static OnlineReward2Calculator getInstance(HumanObject humanObj, int type) {
        instance.setCalculator(humanObj, type);
        return instance;
    }

    @Override
    protected Map<Integer, Integer> getTenKDropMap() {
        Capture capture = humanObj.operation.capture;
        if(capture == null){
            Log.game.error("calculateReward2:===capture is null, humanId={}", humanObj.id);
            return new HashMap<>();
        }
        return Utils.jsonToMapIntInt(capture.getOnlineDropTenKMap());
    }

    @Override
    protected void saveTenKDropMap(Map<Integer, Integer> map) {
        Capture capture = humanObj.operation.capture;
        if(capture == null){
            Log.game.error("calculateReward2:===capture is null, humanId={}", humanObj.id);
            return;
        }
        capture.setOnlineDropTenKMap(Utils.mapIntIntToJSON(map));
        capture.update();
    }

    @Override
    public int[][] getConfRewardRes(int repSn) {
        return GlobalConfVal.getCalculateReward(repSn, ParamKey.rep_online_reward1);
    }

    @Override
    public int[][] getConfDropItem(int repSn) {
        return GlobalConfVal.getCalculateReward(repSn, ParamKey.rep_online_reward2);
    }

    public void handleReward2Async(PlacingRewardVo placingRewardVo, int timeNowSec, boolean isSave, Handler<Boolean> handler) {
        if(humanObj.operation.capture == null){
            return;
        }
        int[] oldTime = calculateTime(placingRewardVo.placingTime, type);
        placingRewardVo.addPlacingTime(timeNowSec - placingRewardVo.lastGetTime, getMaxTime());
        int[] newTime = calculateTime(placingRewardVo.placingTime, type);

        if (oldTime[0] == newTime[0] && oldTime[1] == newTime[1] && placingRewardVo.coinSpeed != 0) {
            handler.handle(true);
            placingRewardVo.lastGetTime = timeNowSec;
            return;
        }

        calculateRewardAsync(placingRewardVo, newTime[0], oldTime[0], newTime[1], oldTime[1], ar -> {
            if (ar.succeeded()) {
                checkAndResetPlacingReward(placingRewardVo, timeNowSec, placingRewardVo.lastGetTime, humanObj.getHuman2().getRepSn());
                placingRewardVo.lastGetTime = timeNowSec;
                humanObj.operation.placingRewardMap.put(type, placingRewardVo);
                if (isSave) {
                    savePlacingRewardVo();
                }
                handler.handle(true);
            } else {
                // 处理失败的情况
                handler.handle(true); // 或者可以选择不调用handler
            }
        });
    }

}
