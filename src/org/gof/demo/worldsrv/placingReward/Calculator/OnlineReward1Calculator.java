package org.gof.demo.worldsrv.placingReward.Calculator;

import org.gof.core.support.Param;
import org.gof.core.support.ParamKey;
import org.gof.core.support.Utils;
import org.gof.demo.battlesrv.support.PropKey;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.ConfGlobal;
import org.gof.demo.worldsrv.entity.Capture;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.instance.ConfChapterVO;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.enumKey.ConfGlobalKey;

import java.util.Map;

public class OnlineReward1Calculator extends AbstractReward1Calculator {
    private static OnlineReward1Calculator instance = new OnlineReward1Calculator();

    public static OnlineReward1Calculator getInstance(HumanObject humanObj,int type) {
        instance.setCalculator(humanObj,type);
        return instance;
    }

    @Override
    public int[][] getConfRewardRes(int repSn) {
        int[][] baseReward = GlobalConfVal.getCalculateReward(repSn, ParamKey.rep_online_reward1);//online_reward1;
        int attrRate = getAttrRate();
        
        int[][] finalReward = new int[baseReward.length][2];
        for (int i = 0; i < baseReward.length; i++) {
            finalReward[i][0] = baseReward[i][0];
            finalReward[i][1] = baseReward[i][1] + (int)(baseReward[i][1] * attrRate / 10000.0);
        }
        
        return finalReward;
    }


    //万倍
    public int[][] getConfDropItem(int repSn) {
        int[][] baseReward = GlobalConfVal.getCalculateReward(repSn, ParamKey.rep_online_reward2);//online_reward2;
        int attrRate = getAttrRate();
        
        int[][] finalReward = new int[baseReward.length][2];
        for (int i = 0; i < baseReward.length; i++) {
            finalReward[i][0] = baseReward[i][0];
            finalReward[i][1] = baseReward[i][1] * (10000 + attrRate);
        }
        
        return finalReward;
    }

    @Override
    protected Map<Integer, Integer> getTenKDropMap() {
        return Utils.jsonToMapIntInt(humanObj.getHumanExtInfo().getOnlineDropTenKMap());
    }

    @Override
    protected void saveTenKDropMap(Map<Integer, Integer> map) {
        humanObj.getHumanExtInfo().setOnlineDropTenKMap(Utils.mapIntIntToJSON(map));
//        humanObj.getHumanExtInfo().update();
    }

    private int getAttrRate(){
        return humanObj.getPropPlus().getBigDecimal(PropKey.online_reward.getAttributeSn()).intValue();
    }
}
