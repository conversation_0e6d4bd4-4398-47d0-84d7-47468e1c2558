package org.gof.demo.worldsrv.placingReward.Calculator;

import org.gof.core.support.Param;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.ConfGlobal;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.instance.ConfChapterVO;
import org.gof.demo.worldsrv.placingReward.PlacingRewardVo;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.enumKey.ConfGlobalKey;

import java.util.HashMap;
import java.util.Map;

/**
 * 在线离线放置奖励
 */
public abstract class AbstractReward1Calculator extends AbstractCalculator{

    @Override
    public void calculateReward(PlacingRewardVo placingRewardVo, int newMinute, int oldMinute, int newtenMinute, int oldtenMinute) {
        int repSn = humanObj.getHuman2().getRepSn()-1;
        if(repSn <= 0){
            Log.temp.error("===ConfChapter1 配表错误, not find sn={}", repSn);
            return ;
        }
        if(newMinute > oldMinute){
            placingRewardVo.addRewardRes(getConfRewardRes(repSn), newMinute - oldMinute);
        }
        //积累掉落，万倍掉落，大于10000给掉落
        Map<Integer,Integer> itemDropMap = getTenKDropMap();
        int[][] dropItems = getConfDropItem(repSn);
        for (int i = oldtenMinute; i < newtenMinute; i++) {
            for (int[] drop : dropItems) {
                int dropId = drop[0];
                int dropCount = drop[1];
                int totalCount = dropCount + itemDropMap.getOrDefault(dropId, 0);
                int actualCount = totalCount / 10000;
                int remainCount = totalCount % 10000;
                for (int j = 0; j < actualCount; j++){
                    Map<Integer,Integer> dropItem = ProduceManager.inst().getDropMap(dropId);
                    placingRewardVo.addRewardItem(dropItem);
                }
                itemDropMap.put(dropId, remainCount);
            }
        }
        saveTenKDropMap(itemDropMap);
    }

}
