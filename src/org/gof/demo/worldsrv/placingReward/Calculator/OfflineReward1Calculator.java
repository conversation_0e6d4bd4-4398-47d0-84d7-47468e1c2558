package org.gof.demo.worldsrv.placingReward.Calculator;

import org.gof.core.support.ParamKey;
import org.gof.core.support.Utils;
import org.gof.demo.battlesrv.support.PropCalc;
import org.gof.demo.battlesrv.support.PropKey;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.ConfGlobal;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.enumKey.ConfGlobalKey;

import java.util.Map;


public class OfflineReward1Calculator extends AbstractReward1Calculator{
    private static OfflineReward1Calculator instance = new OfflineReward1Calculator();

    public static OfflineReward1Calculator getInstance(HumanObject humanObj, int type) {
        instance.setCalculator(humanObj, type);
        return instance;
    }
    @Override
    public int[][] getConfRewardRes(int repSn) {
        int[][] baseReward = GlobalConfVal.getCalculateReward(repSn, ParamKey.rep_offline_reward3); // offline_reward1;
        int attrRate = getAttrRate();
        
        int[][] finalReward = new int[baseReward.length][2];
        for (int i = 0; i < baseReward.length; i++) {
            finalReward[i][0] = baseReward[i][0];
            finalReward[i][1] = baseReward[i][1] + (int)(baseReward[i][1] * attrRate / 10000.0);
        }
        
        return finalReward;
    }

    //万倍
    @Override
    public int[][] getConfDropItem(int repSn) {
        int[][] baseReward = GlobalConfVal.getCalculateReward(repSn, ParamKey.rep_offline_reward4); //.offline_reward2;
        int attrRate = getAttrRate();
        
        int[][] finalReward = new int[baseReward.length][2];
        for (int i = 0; i < baseReward.length; i++) {
            finalReward[i][0] = baseReward[i][0];
            finalReward[i][1] = baseReward[i][1] * (10000 + attrRate);
        }
        
        return finalReward;
    }

    private int getAttrRate() {
        return humanObj.getPropPlus().getBigDecimal(PropKey.offline_reward.getAttributeSn()).intValue();
    }

    public int getMaxTime(){
        PropCalc propPlus = humanObj.getPropPlus();
        int attr = propPlus.getBigDecimal(PropKey.offline_reward_time.getAttributeSn()).intValue();
        return ConfGlobal.get(ConfGlobalKey.online_reward_maxtime).value + attr;
    }

    @Override
    protected Map<Integer, Integer> getTenKDropMap() {
        return Utils.jsonToMapIntInt(humanObj.getHumanExtInfo().getOfflineDropTenKMap());
    }

    @Override
    protected void saveTenKDropMap(Map<Integer, Integer> map) {
        humanObj.getHumanExtInfo().setOfflineDropTenKMap(Utils.mapIntIntToJSON(map));
//        humanObj.getHumanExtInfo().update();
    }

}
