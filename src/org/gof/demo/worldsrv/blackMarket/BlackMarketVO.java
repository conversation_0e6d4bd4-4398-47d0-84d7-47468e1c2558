package org.gof.demo.worldsrv.blackMarket;

import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.interfaces.ISerilizable;

import java.io.IOException;

public class BlackMarketVO implements ISerilizable {
    public int sn = 0;
    public int goodsIndex = 0;
    public int buyNum = 0;
    public long openPreTime = 0L;// 预购的开奖时间
    public int preNum = 0;

    @Override
    public void writeTo(OutputStream out) throws IOException {
        out.write(sn);
        out.write(goodsIndex);
        out.write(buyNum);
        out.write(openPreTime);
        out.write(preNum);
    }

    @Override
    public void readFrom(InputStream in) throws IOException {
        sn = in.read();
        goodsIndex = in.read();
        buyNum = in.read();
        openPreTime = in.read();
        preNum = in.read();
    }
}
