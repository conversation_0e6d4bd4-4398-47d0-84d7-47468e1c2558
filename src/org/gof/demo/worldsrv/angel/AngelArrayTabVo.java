package org.gof.demo.worldsrv.angel;

import java.util.HashMap;
import java.util.Map;

import com.alibaba.fastjson.JSONObject;
import org.gof.core.support.Utils;

/**
 * 天使阵容页签信息
 * 包含页签名称和对应位置的天使信息
 */
public class AngelArrayTabVo {
    /** 页签名称 */
    private String name;
    
    /** 位置对应的天使ID Map<位置, 天使ID> */
    private Map<Integer, Long> positionMap;
    
    public AngelArrayTabVo() {
        this.name = "";
        this.positionMap = new HashMap<>();
    }

    public AngelArrayTabVo(JSONObject json){
        this.name = json.getString("n");
        JSONObject mapJo = json.getJSONObject("map");
        this.positionMap = new HashMap<>();
        for (String key : mapJo.keySet()) {
            Integer k = Utils.intValue(key);
            Long v = mapJo.getLong(key);
            positionMap.put(k, v);
        }
    }
    
    public AngelArrayTabVo(String name) {
        this.name = name;
        this.positionMap = new HashMap<>();
    }

    public JSONObject toJSONObj(){
        JSONObject json = new JSONObject();
        json.put("n", name);
        // 将positionMap转换为JSONObj
        JSONObject mapJo = new JSONObject();
        for (Map.Entry<Integer, Long> entry : this.positionMap.entrySet()) {
            mapJo.put(entry.getKey().toString(), entry.getValue());
        }
        json.put("map", positionMap);
        return json;

    }

    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public Map<Integer, Long> getPositionMap() {
        return positionMap;
    }
    
    public void setPositionMap(Map<Integer, Long> positionMap) {
        this.positionMap = positionMap;
    }
    
    /**
     * 将页签集合转换为JSON字符串
     * @param tabMap 页签集合 Map<页签ID, 页签信息>
     * @return JSON字符串
     */
    public static String tabMapToJson(Map<Integer, AngelArrayTabVo> tabMap) {
        if (tabMap == null || tabMap.isEmpty()) {
            return "{}";
        }
        JSONObject json = new JSONObject();
        for (Map.Entry<Integer, AngelArrayTabVo> entry : tabMap.entrySet()) {
            Integer key = entry.getKey();
            AngelArrayTabVo value = entry.getValue();
            json.put(key.toString(), value.toJSONObj());
        }
        return json.toJSONString();
    }
    
    /**
     * 将JSON字符串转换为页签集合
     * @param json JSON字符串
     * @return 页签集合 Map<页签ID, 页签信息>
     */
    public static Map<Integer, AngelArrayTabVo> jsonToTabMap(String json) {
        if (json == null || json.isEmpty()) {
            return new HashMap<>();
        }
        Map<Integer, AngelArrayTabVo> tabMap = new HashMap<>();
        JSONObject jsonObj = JSONObject.parseObject(json);
        for (String key : jsonObj.keySet()) {
            Integer k = Utils.intValue(key);
            JSONObject value = jsonObj.getJSONObject(key);
            AngelArrayTabVo tabVo = new AngelArrayTabVo(value);
            tabMap.put(k, tabVo);
        }
        return tabMap;
    }
} 