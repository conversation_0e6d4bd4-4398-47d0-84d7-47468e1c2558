package org.gof.demo.worldsrv.crossWar.obj;

import com.alibaba.fastjson.JSONObject;
import com.google.protobuf.Message;
import io.vertx.core.AsyncResult;
import io.vertx.core.Handler;
import org.gof.core.Port;
import org.gof.core.dbsrv.redis.AsyncActionResult;
import org.gof.core.dbsrv.redis.EntityManager;
import org.gof.core.dbsrv.redis.RedisTools;
import org.gof.core.support.Config;
import org.gof.core.support.ParamKey;
import org.gof.core.support.Time;
import org.gof.core.support.Utils;
import org.gof.demo.battlesrv.battle.*;
import org.gof.demo.battlesrv.battle.enumVo.AttribDefine;
import org.gof.demo.battlesrv.support.Vector2D;
import org.gof.demo.distr.DistrKit;
import org.gof.demo.distr.cross.CrossHumanLoader;
import org.gof.demo.worldsrv.config.*;
import org.gof.demo.worldsrv.crossWar.CrossWarConst;
import org.gof.demo.worldsrv.crossWar.CrossWarService;
import org.gof.demo.worldsrv.crossWar.CrossWarUtils;
import org.gof.demo.worldsrv.crossWar.scene.CrossWarScene;
import org.gof.demo.worldsrv.crossWar.scene.Grid;
import org.gof.demo.worldsrv.entity.HumanBrief;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.guild.HumanBriefVO;
import org.gof.demo.worldsrv.human.HumanBriefLoadType;
import org.gof.demo.worldsrv.human.HumanGlobalServiceProxy;
import org.gof.demo.worldsrv.human.HumanManager;
import org.gof.demo.worldsrv.inform.ErrorTip;
import org.gof.demo.worldsrv.instance.InstanceConstants;
import org.gof.demo.worldsrv.msg.*;
import org.gof.demo.worldsrv.redis.RedisKeys;
import org.gof.demo.worldsrv.support.Log;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

public class CrossWarPlayer extends CrossWarSceneObj {
    // 所在跨服的服务器id
    private int m_serverId = 0;
    // 自己的服务器id
    private int m_myServerId = 0;
    // 职业
    private int m_job = 0;
    // 是否首次入侵
    private boolean m_isFirstInvade = false;
    // 入侵结束时间
    private long m_invadeEndTime = 0;
    // 当前大地图视野的sceneBaseId
    private int m_bigMapViewSceneBaseId = 0;
    // 下一次可传送时间戳
    private long m_transferTime = 0;
    // 攻击距离
    private int m_atkDistance = 300;
    // 视野范围
    private int m_viewRange = 750;
    // 基础移动速度
    private int m_baseSpeed = 100;
    // 移动开始时间
    private long m_startMoveTime = 0;
    // 移动tick计数
    private int m_moveTickNum = 0;
    // 移动所需时间，毫秒
    private float m_moveSpanTime = 0;
    // 移动方向
    private Vector2D m_dir;
    // 移动目标id
    private long m_moveTargetId = 0;
    // 移动目标类型
    private int m_moveTargetType = 0;
    // 加速buff结束时间戳
    private long m_speedUpBuffEndTime = 0;
    // 下一次可加速时间戳
    private long m_speedTime = 0;
    // 下一次可复活时间戳
    private long m_reviveTime = 0;
    // 累计复活次数
    private int m_reviveNum = 0;
    // 本次杀普通怪的积分
    private int m_baseScore = 0;
    // 本次杀玩家或光明之子的积分
    private int m_extraScore = 0;
    // 本次击杀玩家数
    private int m_killPlayerNum = 0;
    // 本次击杀怪物数
    private int m_killMonsterNum = 0;
    // 玩家简要信息
    private HumanBriefVO m_humanBriefVO;
    // 当前战斗id
    private long m_battleId = 0;
    // 当前战斗敌方id
    private long m_battleTargetId = 0;
    // 下一次可攻击玩家时间戳
    private long m_atkPlayerTime = 0;
    // 下一次可攻击怪物时间戳
    private long m_atkMonsterTime = 0;
    // 下一次可攻击光明之子时间戳
    private long m_atkSonOfLightTime = 0;
    // 当前视野范围内的对象
    private Set<Long> m_visibleObjIds = new HashSet<>();
    // 大地图视野范围内的对象
    private Set<Long> m_bigMapVisibleObjIds = new HashSet<>();
    // 停止视野更新
    private boolean m_stoppingUpdateView = false;
    // 返回的sceneBaseId
    private int m_backSceneBaseId = 0;
    // 返回的坐标
    private Vector2D m_backPos = new Vector2D(0, 0);
    // 在我视野中离开场景的玩家集合
    private final Set<Long> m_leavingSceneInViewPlayerIds = new HashSet<>();

    public CrossWarPlayer(CrossWarService crossWarService, long humanId, int serverId, int myServerId, int job, boolean isFirstInvade) {
        super(crossWarService, humanId, CrossWarConst.OBJ_TYPE_PLAYER);
        m_serverId = serverId;
        m_myServerId = myServerId;
        m_job = job;
        m_isFirstInvade = isFirstInvade;
        if(isInvade()){
            ConfCrossWarKv confKv = ConfCrossWarKv.get(CrossWarConst.crossWarKvSn_3);
            m_invadeEndTime = Port.getTime() + (confKv != null ? confKv.value[0] : 600) * Time.SEC;
        }
        ConfCrossWarJob confJob = ConfCrossWarJob.get(this.m_job);
        if(confJob != null){
            m_baseSpeed = confJob.speed;
            m_atkDistance = confJob.attack_distance;
            m_viewRange = confJob.sight_list == 1 ? 750 : 1200;
        }
    }

    @Override
    public boolean isPlayer(){return true;}

    public void initPlayer(Handler<AsyncResult<Void>> onComplete){
        Port port = Port.getCurrent();
        CrossHumanLoader.getHumanBrief(m_objId, HumanBriefLoadType.BATTLE_INFO, res -> {
            if (!res.succeeded()) {
                Log.crossWar.error("获取玩家数据失败，m_objId={}, {}", m_objId, res.cause());
                AsyncActionResult.fail(port, onComplete, res.cause());
                return;
            }
            HumanBrief humanBrief = res.result();
            if (humanBrief == null) {
                Log.crossWar.error("获取玩家数据失败，m_objId={}, {}", m_objId, res.cause());
                AsyncActionResult.fail(port, onComplete, res.cause());
                return;
            }
            m_humanBriefVO = new HumanBriefVO(humanBrief);
            if(!initAttr()){
                AsyncActionResult.fail(port, onComplete, new Exception("initAttr fail"));
                return;
            }
            AsyncActionResult.success(port, onComplete, null);
        });
    }

    /**
     * 初始化属性
     */
    @Override
    protected boolean initAttr(){
        try{
           Define.p_battle_role.parseFrom(m_humanBriefVO.getCurrentBattleRole()).toBuilder();
        }catch (Exception e){
            Log.crossWar.error("获取玩家属性失败，m_objId={}, {}", m_objId, e);
            return false;
        }
        // 万分比
        m_hpMax = 10000;
        m_hp = getHpMax();
        return true;
    }

    public HumanBriefVO getHumanBriefVO(){
        return m_humanBriefVO;
    }

    @Override
    public Define.p_battle_role.Builder getBattleRole(int targetType) {
        try{
            Define.p_battle_role.Builder pBattleRole = Define.p_battle_role.parseFrom(m_humanBriefVO.getCurrentBattleRole()).toBuilder();
            Map<Long, Long> totalAttrBonus = new HashMap<>();
            Map<Integer, Map<Long, Long>> attrBonusMap = GlobalConfVal.crossWarAttrBonusMap.getOrDefault(m_job, new HashMap<>());
            // 给自己的固定加成，遍历 fixedAttrBonus 并累加到 totalAttrBonus
            Map<Long, Long> fixedAttrBonus = attrBonusMap.getOrDefault(0, new HashMap<>());
            for (Map.Entry<Long, Long> entry : fixedAttrBonus.entrySet()) {
                totalAttrBonus.put(entry.getKey(),
                        totalAttrBonus.getOrDefault(entry.getKey(), 0L) + entry.getValue());
            }
            // 根据战斗目标给的加成，遍历 basedOnTargetAttrBonus 并累加到 totalAttrBonus
            Map<Long, Long> basedOnTargetAttrBonus = attrBonusMap.getOrDefault(targetType, new HashMap<>());
            for (Map.Entry<Long, Long> entry : basedOnTargetAttrBonus.entrySet()) {
                totalAttrBonus.put(entry.getKey(),
                        totalAttrBonus.getOrDefault(entry.getKey(), 0L) + entry.getValue());
            }
            if(!totalAttrBonus.isEmpty()){
                List<Define.p_key_value> attrList = new ArrayList<>();
                for (Define.p_key_value attr : pBattleRole.getAttrListList()) {
                    long key = attr.getK();
                    long value = attr.getV();
                    Long bonus = totalAttrBonus.get(key);
                    if(bonus != null){
                        value = new BigDecimal(value).multiply(BigDecimal.valueOf(10000+bonus)).divide(BigDecimal.valueOf(10000), 2, RoundingMode.HALF_UP ).longValue();
                        attrList.add(Define.p_key_value.newBuilder().setK(key).setV(value).build());
                    }
                    else{
                        attrList.add(Define.p_key_value.newBuilder(attr).build());
                    }
                }
                pBattleRole.clearAttrList();
                pBattleRole.addAllAttrList(attrList);
            }
            pBattleRole.addExt(HumanManager.inst().to_p_key_value(ParamKey.extPosType_2, getHp()));
            // 每次重算battleRole属性时，设置战斗时的最大血量
            m_battleMaxHp = BattleDataFill.getAttrib(AttribDefine.hp.getValue(), pBattleRole.getAttrListList());
//        Log.crossWar.info("{} targetType={} totalAttrBonus={} m_battleMaxHp={}", m_objId, targetType, totalAttrBonus, m_battleMaxHp);
            return pBattleRole;
        }
        catch (Exception e){
            Log.crossWar.error("获取玩家属性失败，m_objId={}, {}", m_objId, e);
            return null;
        }
    }

    public List<Define.p_key_value> getBaseAttr() {
        try{
            Define.p_battle_role.Builder pBattleRole = Define.p_battle_role.parseFrom(m_humanBriefVO.getCurrentBattleRole()).toBuilder();
            return pBattleRole.getAttrListList();
        }
        catch (Exception e){
            Log.crossWar.error("获取玩家属性失败，m_objId={}, {}", m_objId, e);
            return null;
        }
    }

    public boolean isInvade() {
        return m_myServerId != m_serverId;
    }

    public int getServerId() {
        return m_serverId;
    }

    @Override
    public int getMyServerId() {
        return m_myServerId;
    }

    public int getJob() {
        return m_job;
    }

    /**
     * 获取本周指定serverId的跨服战唯一id
     * @return
     */
    public String getCurrentWeekUniqueId(){
        return m_crossWarService.getCurrentWeekUniqueId(m_myServerId);
    }

    public long getStartMoveTime() {
        return m_startMoveTime / Time.SEC;
    }

    public int getSpeed() {
        if(Port.getTime() < m_speedUpBuffEndTime){
            ConfCrossWarKv confKv15 = ConfCrossWarKv.get(CrossWarConst.crossWarKvSn_15);
            int SPEED_UP_VALUE = confKv15 != null ? confKv15.value[0] : 100;
            if(Config.DATA_DEBUG){
                SPEED_UP_VALUE = 1000;
            }
            return m_baseSpeed + SPEED_UP_VALUE;
        }
        return m_baseSpeed;
    }

    public void setBattleData(long battleId, long targetId){
        m_battleId = battleId;
        m_battleTargetId = targetId;
    }

    public long getBattleId() {
        return m_battleId;
    }

    @Override
    public int getLevel(){
        return m_humanBriefVO.level;
    }

    /**
     * 添加加速效果
     */
    protected void addSpeedUpEffect(){
        ConfCrossWarKv confKv16 = ConfCrossWarKv.get(CrossWarConst.crossWarKvSn_16);
        long SPEED_UP_TIME = (confKv16 != null ? confKv16.value[0] : 5) * Time.SEC;
        if(Config.DATA_DEBUG){
            SPEED_UP_TIME = Time.DAY;
        }
        m_speedUpBuffEndTime = Port.getTime() + SPEED_UP_TIME ;
    }

    public void stop(Vector2D pos){
        setPos(pos);
        if(isMoving()){
            // 正在移动时被停止，发给自己一个moveType=1的move_s2c到当前点
            MsgScene.scene_obj_move_s2c.Builder msg = MsgScene.scene_obj_move_s2c.newBuilder();
            msg.setObjId(m_objId);
            msg.setSpeed(getSpeed());
            msg.setStartMoveTime(Port.getTime()/Time.SEC);
            msg.setPos(to_p_pos(pos));
            Define.p_move_info.Builder moveInfo = Define.p_move_info.newBuilder();
            // 发给自己，moveType=1
            moveInfo.setType(CrossWarConst.OBJ_MOVE_TYPE_CLIENT);
            moveInfo.setPos(to_p_pos(pos));
            msg.setMoveInfo(moveInfo);
            sendMsg(msg.build());
//            Log.crossWar.info("stop: objId={} moveType={} targetPos={}", m_objId, moveInfo.getType(), pos);
        }
        m_moveSpanTime = 0;
        m_startMoveTime = 0;
    }

    @Override
    public String toString(){
        JSONObject jo = Utils.toJSONObject(super.toString());
        Utils.putIfNonZero(jo, "serverId", m_serverId);
        Utils.putIfNonZero(jo, "job", m_job);
        Utils.putIfNonZero(jo, "speed", getSpeed());
        Utils.putIfNonZero(jo, "invadeEndTime", m_invadeEndTime);
        Utils.putIfNonZero(jo, "transferTime", m_transferTime);
        Utils.putIfNonZero(jo, "speedTime", m_speedTime);
        Utils.putIfNonZero(jo, "reviveTime", m_reviveTime);
        Utils.putIfNonZero(jo, "reviveCount", m_reviveNum);
        return jo.toJSONString();
    }

    @Override
    public void pulse(){
        super.pulse();
        long timeNow = Port.getTime();
        if(m_invadeEndTime > 0 && timeNow > m_invadeEndTime){
            // 入侵结束，退出本次跨服战
            exitCrossWar();
            return;
        }
        // 先处理移动
        pulseMove();
        // 再更新视野
        updateView();
    }

    /**
     * 处理移动
     */
    private void pulseMove(){
        if(m_scene == null){
            return;
        }
        if(m_startMoveTime <= 0){
           return;
        }
        Vector2D oldPos = getNowPos();
        float time = Port.getTime() - m_startMoveTime;
        if(time < 0 || time >= m_moveSpanTime){
            // 移动结束
            stop(m_destPos);
            // 更新网格
            m_scene.gridManager.updateObjectGrid(this, oldPos);
            if(m_moveTargetId > 0){
                // 移动结束后才进入战斗
                enterBattle();
            }
            return;
        }
        // 移动中
        Vector2D offset = m_dir.mul(m_moveSpanTime - time);
        Vector2D nowPos = m_destPos.sub(offset);
        setPos(nowPos);
        if(m_moveTickNum++ % 5 == 0){
            // 广播给其他人移动包，100ms一次
            MsgScene.scene_obj_move_s2c.Builder msg = MsgScene.scene_obj_move_s2c.newBuilder();
            msg.setObjId(m_objId);
            msg.setSpeed(getSpeed());
            msg.setStartMoveTime(Port.getTime()/Time.SEC);
            msg.setPos(to_p_pos(oldPos));
            Define.p_move_info.Builder moveInfo = Define.p_move_info.newBuilder();
            moveInfo.setType(CrossWarConst.OBJ_MOVE_TYPE_SYNC);
            moveInfo.setPos(to_p_pos(nowPos));
            msg.setMoveInfo(moveInfo);
//            Log.crossWar.info("broadcast move: objId={} moveType={} targetPos={}", m_objId, moveInfo.getType(), nowPos);
            sendMsg2Around(msg.build(), false);
        }
        // 更新网格
        m_scene.gridManager.updateObjectGrid(this, oldPos);
        if(m_moveTargetId > 0){
            // 移动过程中，目标达到攻击范围内，直接停止移动进入战斗
            CrossWarSceneObj targetObj = m_scene.getSceneObj(m_moveTargetId, m_moveTargetType);
            if(targetObj == null){
                Log.crossWar.info("移动过程中目标丢失，立即停止移动 m_objId={} m_moveTargetId={}", m_objId, m_moveTargetId);
                stop(getNowPos());
                // 清除移动目标
                m_moveTargetId = 0;
                m_moveTargetType = 0;
                return;
            }
            double distance = targetObj.getNowPos().distance(getNowPos());
            // 距离 < 攻击距离+目标模型半径半径
            if(distance < m_atkDistance + targetObj.getModelRadius()){
                // 停止移动直接进入战斗
//                Log.crossWar.info("停止移动直接进入战斗 costTime={} ms", Port.getTime()-m_startMoveTime);
                stop(getNowPos());
                enterBattle();
            }
        }
    }

    /**
     * 更新视野范围
     */
    public void updateView() {
        if(m_scene == null || m_stoppingUpdateView){
            return;
        }
        Set<Long> newView = new HashSet<>();
        List<CrossWarSceneObj> addedObjects = new ArrayList<>();
        List<Long> removedObjectIds = new ArrayList<>();

        // 获取视野范围内的网格
        Vector2D myPos = getNowPos();
        List<Grid> gridsInRange = m_scene.gridManager.getGridsInRange(myPos, m_viewRange);
        if(gridsInRange == null){
            // 位置不合法，不更新视野
            return;
        }
        for (Grid grid : gridsInRange) {
            for (CrossWarSceneObj obj : grid.objects) {
                long objId = obj.getObjId();
                if (obj.getNowPos().distance(myPos) <= m_viewRange) {
                    newView.add(objId);
                    if (!m_visibleObjIds.contains(objId)) {
                        // 新增对象
                        addedObjects.add(obj);
                        if(objId != m_objId){
                            // 标记对象被我看见
                            obj.m_visibleByPlayerIds.add(m_objId);
                        }
                    }
                    if (m_leavingSceneInViewPlayerIds.contains(objId)) {
                        if(objId != m_objId){
                            Log.crossWar.info("玩家={}在玩家={}的视野中离开场景，立即再次被看见", objId, m_objId);
                            // 标记对象被我看见
                            obj.m_visibleByPlayerIds.add(m_objId);
                        }
                    }
                }
            }
        }
        // 如果进入的是光明主城，把所有光明之子加入玩家的视野中
        if(m_scene.getSceneType() == CrossWarConst.SCENE_TYPE_LIGHT_CITY){
            for (CrossWarSceneObj obj : m_scene.getMonsterMap().values()) {
                if (obj.getObjType() != CrossWarConst.OBJ_TYPE_SONS_OF_LIGHT) {
                    continue;
                }
                long objId = obj.getObjId();
                newView.add(objId);
                if (!m_visibleObjIds.contains(objId)) {
                    // 新增对象
                    addedObjects.add(obj);
                    Log.crossWar.info("m_objId={} 视野增加光明之子={}", m_objId, objId);
                    // 标记对象被我看见
                    obj.m_visibleByPlayerIds.add(m_objId);
                }
            }
        }
        // 计算离开视野的对象
        for (long objId : m_visibleObjIds) {
            if (!newView.contains(objId)) {
                removedObjectIds.add(objId);
                CrossWarSceneObj obj = m_scene.getSceneObj(objId);
                if(obj != null){
                    // 标记对象已不被我看见
                    obj.m_visibleByPlayerIds.remove(m_objId);
                }
            }
        }
        // 更新视野并通知客户端
        m_visibleObjIds = newView;
        // 每个tick清空在我视野中离开场景的玩家集合
        m_leavingSceneInViewPlayerIds.clear();
        if(!addedObjects.isEmpty()){
            MsgScene.scene_obj_add_s2c.Builder msgAdd = CrossWarUtils.to_scene_obj_add_s2c(addedObjects);
//            Log.crossWar.info("addedObjects={}", addedObjects.size());
            sendMsg(msgAdd.build());
        }
       if(!removedObjectIds.isEmpty()){
           MsgScene.scene_obj_del_s2c.Builder msgDel = CrossWarUtils.to_scene_obj_del_s2c(removedObjectIds);
//           Log.crossWar.info("removedObjectIds={}", removedObjectIds.size());
           sendMsg(msgDel.build());
       }
    }

    /**
     * 更新大地图视野
     * @param viewPos
     */
    public void updateBigMapView(Vector2D viewPos) {
        CrossWarScene viewScene = m_crossWarService.getScene(m_serverId, m_bigMapViewSceneBaseId);
        if(viewScene == null){
            return;
        }
        Set<Long> newView = new HashSet<>();
        List<CrossWarSceneObj> addedObjects = new ArrayList<>();
        List<Long> removedObjectIds = new ArrayList<>();

        double bigMapViewXRange = 5000;
        Vector2D correctedPos = viewScene.gridManager.correctPos(viewPos, bigMapViewXRange);
        // 获取视野范围内的网格
        List<Grid> gridsInRange = viewScene.gridManager.getGridsInXRange(correctedPos, bigMapViewXRange);
        if(gridsInRange == null){
            // 位置不合法，不更新视野
            return;
        }
        for (Grid grid : gridsInRange) {
            for (CrossWarSceneObj obj : grid.objects) {
                if(!obj.isShowInBigMap()){
                    continue;
                }
                long objId = obj.getObjId();
                // 距离只考查x
                double distance = Math.abs(obj.getNowPos().x - correctedPos.x);
                if (distance <= bigMapViewXRange) {
                    newView.add(objId);
                    if (!m_bigMapVisibleObjIds.contains(objId)) {
                        // 新增对象
                        addedObjects.add(obj);
                    }
                }
            }
        }
        // 计算离开视野的对象
        for (long objId : m_bigMapVisibleObjIds) {
            if (!newView.contains(objId)) {
                removedObjectIds.add(objId);
            }
        }
        // 更新大地图视野并通知客户端
        m_bigMapVisibleObjIds = newView;
        if(!addedObjects.isEmpty()){
            MsgScene.scene_obj_static_s2c.Builder msgAdd = CrossWarUtils.to_scene_obj_static_s2c(addedObjects);
            sendMsg(msgAdd.build());
        }
        if(!removedObjectIds.isEmpty()){
            MsgScene.scene_obj_del_s2c.Builder msgDel = CrossWarUtils.to_scene_obj_del_s2c(removedObjectIds);
            sendMsg(msgDel.build());
        }
    }

    public void sendMsg(Message msg){
        HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance(DistrKit.getWorldNodeID(m_myServerId));
//        Log.crossWar.info("player:{} sendMsg {}", m_objId, msg.getClass());
        proxy.sendMsg(m_objId, msg);
    }

    public void sendMsg_error(int sn){
        sendMsg_error(sn, "");
    }

    public void sendMsg_error(int sn, String str) {
        MsgError.error_info_s2c.Builder msg = MsgError.error_info_s2c.newBuilder();
        msg.setCode(sn);
        msg.setMsg(str);
        sendMsg(msg.build());
    }

    /**
     * 发送玩家当前场景的信息
     */
    public void sendSceneInfo(){
        if(m_scene == null){
            sendSceneInfo(0, null);
            return;
        }
        sendSceneInfo(m_scene.getSceneBaseId(), getNowPos());
    }

    /**
     * 发送玩家的场景信息
     * @param sceneBaseId
     * @param pos
     */
    public void sendSceneInfo(int sceneBaseId, Vector2D pos){
        MsgCrossWar.cross_war_scene_info_s2c.Builder msg = MsgCrossWar.cross_war_scene_info_s2c.newBuilder();
        msg.setServId(Utils.getServerIdTo(m_serverId));
        msg.setSceneBase(sceneBaseId);
        msg.setEndTime((int) (m_invadeEndTime/Time.SEC));
        if(pos != null){
            msg.setPos(to_p_pos(pos));
        }
        msg.setScore(getScore());
        msg.setIsFirst(m_isFirstInvade ? 1:0);
        msg.setSpeedTime((int) (m_speedTime /Time.SEC));
        msg.setTransferTime((int) (m_transferTime /Time.SEC));
//        Log.crossWar.info("player:{} sendSceneInfo={}", m_objId, msg);
        sendMsg(msg.build());
    }

    /**
     * 退出本次跨服战
     */
    public void exitCrossWar(){
        // 离开场景
        leaveScene(true);
        // 移除跨服玩家
        m_crossWarService.removePlayer(this);
        if(isInvade()){
            crossWarInvadeEnd();
        }
        clearBeforeExit();
        // 清除信息后发送scene_info
        sendSceneInfo();
    }

    private void clearBeforeExit(){
        m_serverId = 0;
        m_baseScore = 0;
        m_extraScore = 0;
        m_invadeEndTime = 0;
        m_transferTime = 0;
        m_speedTime = 0;
    }

    /**
     * 跨服战单次入侵结束
     */
    public void crossWarInvadeEnd() {
        // 更新个人积分榜
        String uniqueId = getCurrentWeekUniqueId();
        String personalKey = RedisKeys.cross_war_1031_rank + uniqueId;
        String serverKey = RedisKeys.cross_war_1032_rank + uniqueId;
        int score = getScore();
        RedisTools.getMyScore(EntityManager.redisClient, personalKey, m_objId, ret->{
            if (!ret.succeeded()) {
                Log.crossWar.error("获取排行榜失败, rankSn={}", CrossWarConst.rankType_1031);
                return;
            }
            int oldScore = Utils.intValue(ret.result());
            int scoreOffset = score - oldScore;
            if(scoreOffset <= 0){
                return;
            }
            // 更新个人积分，单次入侵最高积分
            RedisTools.updateRankWithTimestamp(EntityManager.redisClient, personalKey, m_objId, scoreOffset, res->{
                if(res.failed()){
                    return;
                }
                RedisTools.expire(EntityManager.redisClient, personalKey, CrossWarConst.crossWarRank_expireTime);
            });
            // 更新服务器积分，所有玩家的最高积分的和
            RedisTools.updateRankWithTimestamp(EntityManager.redisClient, serverKey, m_myServerId, scoreOffset, res->{
                if(res.failed()){
                    return;
                }
                RedisTools.expire(EntityManager.redisClient, serverKey, CrossWarConst.crossWarRank_expireTime);
            });
        });
        // 入侵退出，发送战报
        MsgCrossWar.cross_war_battle_report_s2c.Builder msg = MsgCrossWar.cross_war_battle_report_s2c.newBuilder();
        msg.setPoint(score);
        msg.setKillRole(m_killPlayerNum);
        msg.setKillMonster(m_killMonsterNum);
        sendMsg(msg.build());
        Log.crossWar.info("跨服战单次入侵结束 {}", m_objId);
        // 跨服战单次入侵结束
        HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance(DistrKit.getWorldNodeID(m_myServerId));
        proxy.crossWarInvadeEnd(m_objId, score);
    }


    /**
     * 进入大地图，如果已经在场景中则离开
     * @param sceneBaseId
     * @param backPos
     */
    public void enterBigMap(int sceneBaseId, Vector2D backPos){
//        Log.crossWar.info("enterBigMap ={} {}", sceneBaseId, backPos);
        // 记录返回的sceneBaseId和Pos
        backupSceneBaseIdAndPos();
        // 记录当前大地图视野的sceneBaseId
        m_bigMapViewSceneBaseId = sceneBaseId;
        // 清空大地图视野
        m_bigMapVisibleObjIds.clear();
        // 更新大地图视野, enter时以0,0为视野观察点
        updateBigMapView(new Vector2D(0, 0));
        // 如果已经在场景中则离开
        leaveScene(false);
        // 发送玩家的场景信息
        sendSceneInfo(m_bigMapViewSceneBaseId, m_backPos);
    }

    /**
     *  记录返回的sceneBaseId和Pos
     */
    public void backupSceneBaseIdAndPos(){
        m_backSceneBaseId = m_scene != null ? m_scene.getSceneBaseId() : 0;
        m_backPos = m_scene != null ? getNowPos() : null;
    }

    /**
     * 清空当前视野，并停止视野更新
     * 退出地图 或 看大地图时触发
     */
    public void clearAndStopUpdateView(){
        m_stoppingUpdateView = true;
        // 标记所有视野内对象不被我看见
        for (long objId : m_visibleObjIds) {
            CrossWarSceneObj obj = m_scene.getSceneObj(objId);
            if(obj != null){
                // 标记对象已不被我看见
                obj.m_visibleByPlayerIds.remove(m_objId);
            }
        }
        // 清空视野
        m_visibleObjIds.clear();
    }

    /**
     * 添加在我视野中离开场景的玩家
     * @param playerId
     */
    public void addLeavingSceneInViewPlayer(long playerId){
        m_leavingSceneInViewPlayerIds.add(playerId);
    }

    /**
     * 清空被哪些玩家看见的列表
     */
    @Override
    public void clearVisibleByPlayerIds(){
        // 标记所有能看见我的玩家，我正在他们视野中离开场景
        for (long objId : m_visibleByPlayerIds) {
            CrossWarPlayer player = m_scene.getPlayer(objId);
            if(player != null){
                player.addLeavingSceneInViewPlayer(m_objId);
            }
        }
        m_visibleByPlayerIds.clear();
    }

    /**
     * 大地图滑动
     * @param viewPos
     */
    public void bigMapSlide(Vector2D viewPos){
        // 更新大地图视野
        updateBigMapView(viewPos);
    }

    /**
     * 尝试进入场景
     * @param enterType
     * @param pos
     */
    public void tryEnterScene(int enterType, Vector2D pos) {
        if(enterType == CrossWarConst.ENTER_SCENE_TYPE_ENTER){
            if(Port.getTime() < m_transferTime){
                Log.crossWar.info("===transfer cd. 还需{}ms后可传送", m_transferTime -Port.getTime());
                return;
            }
//            Log.crossWar.info("enter sceneBaseId={} pos={}", m_bigMapViewSceneBaseId, pos);
            tryEnterSceneByBaseId(m_bigMapViewSceneBaseId, pos);
            // 增加传送cd
            increaseTransferCd();
        }
        else if(enterType == CrossWarConst.ENTER_SCENE_TYPE_REENTER){
            Log.crossWar.info("reEnter sceneBaseId={} pos={}", m_backSceneBaseId, m_backPos);
            tryEnterSceneByBaseId(m_backSceneBaseId, m_backPos);
        }
    }

    /**
     * 尝试进入场景
     * @param sceneBaseId
     * @param pos
     */
    public void tryEnterSceneByBaseId(int sceneBaseId, Vector2D pos) {
        if(sceneBaseId == 0){
            Log.crossWar.warn("===sceneBaseId==0");
            return;
        }
        if(pos == null){
            Log.crossWar.warn("===pos is null");
            return;
        }
        if(!Config.DATA_DEBUG && CrossWarUtils.isHolyCity(sceneBaseId) && !CrossWarUtils.isHolyOpen()){
            Log.crossWar.info("无法进入地图，光明主城未开放");
            return;
        }
//        Log.crossWar.info("enterSceneByBaseId = {} {}", sceneBaseId, pos);
        enterScene(sceneBaseId, pos);
    }

    /**
     * 增加传送cd
     */
    private void increaseTransferCd(){
        ConfCrossWarKv confKv = ConfCrossWarKv.get(CrossWarConst.crossWarKvSn_13);
        m_transferTime = Port.getTime() + (confKv != null ? confKv.value[0] : 30) * Time.SEC;
        sendSceneInfo();
    }


    /**
     * 进入场景
     * @param sceneBaseId
     * @param pos
     */
    public void enterScene(int sceneBaseId, Vector2D pos) {
        CrossWarScene newScene = m_crossWarService.getScene(m_serverId, sceneBaseId);
        if(newScene == null){
            return;
        }
        // 先离开当前场景
        leaveScene(false);
        // 再进入新场景
        newScene.doEnterScene(this, pos);
    }

    /**
     * 进入场景
     * @param pos
     */
    @Override
    public void onEnterScene(Vector2D pos) {
        if(m_scene == null){
            return;
        }
        // 进入场景时，修正玩家位置
        pos = fixPosOnEnterScene(pos);
        super.onEnterScene(pos);
        // 每次进入场景，恢复视野更新
        m_stoppingUpdateView = false;
        // 通知玩家进入场景
        MsgScene.scene_enter_s2c.Builder msg = MsgScene.scene_enter_s2c.newBuilder();
        msg.setSceneBase(m_scene.getSceneBaseId());
        msg.setPos(to_p_pos(getNowPos()));
        msg.setType(m_scene.getSceneType());
        sendMsg(msg.build());
        // 加载场景
        MsgScene.scene_load_s2c.Builder loadMsg = MsgScene.scene_load_s2c.newBuilder();
        loadMsg.setSceneBase(m_scene.getSceneBaseId());
        sendMsg(loadMsg.build());
        // 进入场景时，玩家如果在战斗中，则结束战斗
        if(m_battleId > 0){
            Log.crossWar.warn("===玩家={} onEnterScene={} endBattle! m_battleId={} m_battleTargetId={}", m_objId, getSceneId(), m_battleId, m_battleTargetId);
            endBattle();
        }
        Log.crossWar.info("=== onEnterScene:{} {}", getSceneId(), this);
    }

    /**
     * 进入场景时，修正玩家位置
     * @param pos
     */
    private Vector2D fixPosOnEnterScene(Vector2D pos){
        if(m_scene.getSceneType() != CrossWarConst.SCENE_TYPE_LIGHT_CITY){
            return pos;
        }
        // 当玩家进入光明之子身体里，修正到(0,0)
        for (CrossWarSceneObj obj : m_scene.getMonsterMap().values()) {
            if (obj.getObjType() != CrossWarConst.OBJ_TYPE_SONS_OF_LIGHT) {
                continue;
            }
            long range = obj.getModelRadius();
            if (obj.getNowPos().distance(pos) <= range) {
                Log.crossWar.info("===玩家{}，由于进入场景时在光明之子身体里，被移动到{}的(0,0)点", m_objId, getSceneId());
                return new Vector2D(0, 0);
            }
        }
        return pos;
    }


    /**
     * 离开场景
     *
     * @param bSend
     */
    public void leaveScene(boolean bSend) {
        if(m_scene == null){
            return;
        }
        if(bSend){
            // 通知玩家离开场景
            MsgScene.scene_leave_s2c.Builder msg = MsgScene.scene_leave_s2c.newBuilder();
            msg.setType(m_scene.getSceneType());
            sendMsg(msg.build());
            Log.crossWar.info("scene_leave_s2c {}", m_objId);
        }
        m_scene.doLeaveScene(this);
    }

    /**
     * 离开场景
     */
    @Override
    public void onLeaveScene() {
        if(m_scene == null){
            return;
        }
        // 清空移动相关变量
        m_moveSpanTime = 0;
        m_startMoveTime = 0;
        m_moveTargetId = 0;
        m_moveTargetType = 0;
        // 清空当前视野，并停止视野更新
        clearAndStopUpdateView();
        // 离开场景时，玩家如果在战斗中，则结束战斗
        if(m_battleId > 0){
            Log.crossWar.warn("===玩家={} endBattle! onLeaveScene={} m_battleId={} m_battleTargetId={}", m_objId, getSceneId(), m_battleId, m_battleTargetId);
            endBattle();
        }
        // 在所有广播协议都发出后，最后再调用父类的onLeaveScene，清空被哪些玩家看见的列表
        super.onLeaveScene();
        Log.crossWar.info("=== onLeaveScene:{} {}", getSceneId(), this);
    }

    @Override
    public Define.p_scene_obj.Builder to_p_scene_obj() {
        Define.p_scene_obj.Builder builder = super.to_p_scene_obj();
        Define.p_scene_role.Builder role = Define.p_scene_role.newBuilder();
        role.setServId(Utils.getServerIdTo(getMyServerId()));
        builder.setSpeed(getSpeed());
        builder.setStartMoveTime(getStartMoveTime());
        role.setName(m_humanBriefVO.name);
        role.setPower(Utils.longValue(m_humanBriefVO.topCombat));
        Define.p_head.Builder head = Define.p_head.newBuilder();
        head.setId(m_humanBriefVO.headSn);
        head.setFrameId(m_humanBriefVO.currentHeadFrameSn);
        head.setUrl("");
        role.setHead(head);
        role.setReviveTime((int) (m_reviveTime/Time.SEC));
        role.setReviveCount(m_reviveNum);
        role.setJob(m_job);
        builder.setRole(role);
        return builder;
    }

    @Override
    public MsgScene.scene_obj_update_elem_s2c.Builder to_scene_obj_update_elem_s2c(){
        MsgScene.scene_obj_update_elem_s2c.Builder builder = super.to_scene_obj_update_elem_s2c();
        Define.p_scene_obj_update_elem.Builder reviveTimeBuilder = Define.p_scene_obj_update_elem.newBuilder();
        reviveTimeBuilder.setKey(CrossWarConst.UPDATE_ELEM_TYPE_REVIVE_TIME);
        reviveTimeBuilder.setVal((int) (m_reviveTime/Time.SEC));
        builder.addUpdateList(reviveTimeBuilder.build());
        Define.p_scene_obj_update_elem.Builder reviveNumBuilder = Define.p_scene_obj_update_elem.newBuilder();
        reviveNumBuilder.setKey(CrossWarConst.UPDATE_ELEM_TYPE_REVIVE_NUM);
        reviveNumBuilder.setVal(m_reviveNum);
        builder.addUpdateList(reviveNumBuilder.build());
        Define.p_scene_obj_update_elem.Builder speedBuilder = Define.p_scene_obj_update_elem.newBuilder();
        speedBuilder.setKey(CrossWarConst.UPDATE_ELEM_TYPE_SPEED);
        speedBuilder.setVal(getSpeed());
        builder.addUpdateList(speedBuilder.build());
        return builder;
    }

    public Define.p_cross_war_kill.Builder to_p_cross_war_kill(){
        Define.p_cross_war_kill.Builder builder = Define.p_cross_war_kill.newBuilder();
        builder.setRoleId(m_objId);
        builder.setServId(Utils.getServerIdTo(getMyServerId()));
        builder.setSceneBase(getSceneBaseId());
        builder.setName(m_humanBriefVO.name);
        Define.p_head.Builder head = Define.p_head.newBuilder();
        head.setId(m_humanBriefVO.headSn);
        head.setFrameId(m_humanBriefVO.currentHeadFrameSn);
        head.setUrl("");
        builder.setHead(head);
        builder.setKillNum(m_killPlayerNum);
        return builder;
    }

    /**
     * 移动
     * @param moveInfo
     * @return
     */
    public void doMoveTo(Define.p_move_info moveInfo) {
        if(m_scene == null) {
            return;
        }
        Define.p_pos pos = moveInfo.getPos();
        Vector2D targetPos = new Vector2D(pos.getX(), pos.getY());
        if(moveInfo.getType() == CrossWarConst.OBJ_MOVE_TYPE_BATTLE){
            int targetType = moveInfo.getTargetType();
            long targetId = moveInfo.getTargetId();
            CrossWarSceneObj targetObj = m_scene.getSceneObj(targetId, targetType);
            if(targetObj == null){
                return;
            }
            targetPos = targetObj.getNowPos();
            m_moveTargetId = targetId;
            m_moveTargetType = targetType;
            // 修正moveInfo内的pos为targetPos
            Define.p_move_info.Builder builder = Define.p_move_info.newBuilder(moveInfo);
            builder.setPos(to_p_pos(targetPos));
            moveInfo = builder.build();
        }
        moveTo(targetPos.x, targetPos.y);
        // 发给自己移动包
        MsgScene.scene_obj_move_s2c.Builder msg = MsgScene.scene_obj_move_s2c.newBuilder();
        msg.setObjId(m_objId);
        msg.setSpeed(getSpeed());
        msg.setStartMoveTime(getStartMoveTime());
        msg.setPos(to_p_pos(getNowPos()));
        msg.setMoveInfo(moveInfo);
//        Log.crossWar.info("moveTo: objId={} moveType={} nowPos={} targetPos={} m_moveSpanTime={}", m_objId, moveInfo.getType(), getNowPos(), targetPos, m_moveSpanTime);
        if(moveInfo.getType() != CrossWarConst.OBJ_MOVE_TYPE_SYNC){
            sendMsg(msg.build());
        }
    }

    /**
     * 移动
     *
     * @param posX
     * @param posY
     */
    public void moveTo(double posX, double posY) {
        Vector2D startPos = getNowPos();
        m_destPos = new Vector2D(posX, posY);
        if(m_destPos.equals(startPos)){
            Log.crossWar.warn("moveTo m_destPos={} equals startPos={}", m_destPos, startPos);
            return;
        }
        m_startMoveTime = Port.getTime();
        m_moveTickNum = 0;
        int moveSpeed = getSpeed();

        m_dir = m_destPos.sub(startPos);
        float distance = Utils.floatValue(m_dir.Normalize());
        // 每毫秒移动速度
        float speedMi = moveSpeed / 1000.0f;
        try{
            m_dir.Multiply(speedMi);
            m_moveSpanTime = distance / speedMi;
        } catch (Exception e) {
            Log.crossWar.error("moveTo error! m_dir={} speedMi={} startPos={} m_destPos={} distance={}",
                    m_dir, speedMi, startPos, m_destPos, distance, e);
        }
    }

    public boolean isMoving(){
        if(m_moveSpanTime <= 0){
            return false;
        }
        float time = Port.getTime() - m_startMoveTime;
        return time >= 0 && time < m_moveSpanTime;
    }

    /**
     * 进入战斗
     */
    public void enterBattle() {
        if(m_scene == null){
            return;
        }
        if(getState() == CrossWarConst.OBJ_STATE_BATTLE || m_battleId > 0){
            Log.crossWar.warn("===进入战斗失败 自己={} 已经在战斗中", m_objId);
            return;
        }
        CrossWarSceneObj targetObj = m_scene.getSceneObj(m_moveTargetId, m_moveTargetType);
        if(targetObj == null){
            return;
        }
        m_moveTargetId = 0;
        m_moveTargetType = 0;
        long targetId = targetObj.getObjId();
        int targetType = targetObj.getObjType();
        if(targetObj.getState() == CrossWarConst.OBJ_STATE_DEAD){
            Log.crossWar.warn("===进入战斗失败 目标对象={}已经死亡", targetId);
            return;
        }
        if(targetObj.getMyServerId() == getMyServerId()){
            if(targetObj.getObjType() == CrossWarConst.OBJ_TYPE_SONS_OF_LIGHT){
                Log.crossWar.warn("===进入战斗失败 与目标对象={}是同阵营", targetId);
                sendMsg_error(ErrorTip.CrossWarSelfLightCantAttack);
                return;
            }
            // 在任何地图，都不能攻击本服的玩家和机器人
            if(targetObj.getObjType() == CrossWarConst.OBJ_TYPE_PLAYER){
                Log.crossWar.warn("===进入战斗失败 与目标对象={}是同阵营", targetId);
                return;
            }
        }
        if(targetObj.getState() == CrossWarConst.OBJ_STATE_BATTLE && targetType != CrossWarConst.OBJ_TYPE_SONS_OF_LIGHT){
            // 光明之子可以被多人同时攻击
            Log.crossWar.warn("===进入战斗失败 目标对象={}已经在战斗中", targetId);
            sendMsg_error(ErrorTip.CrossWarTargetInBattle);
            return;
        }
        double distance = targetObj.getNowPos().distance(getNowPos());
        if(distance > m_atkDistance + targetObj.getModelRadius()){
            Log.crossWar.warn("===进入战斗失败 和目标对象={}的距离={} > 攻击距离+半径={}", targetId, distance, m_atkDistance+targetObj.getModelRadius());
            return;
        }
        long atkTime = getBattleCd(targetType);
        if(Port.getTime() < atkTime){
            Log.crossWar.warn("===battle with type {} cd. 还需{}ms后可攻击", targetType, atkTime -Port.getTime());
            sendMsg_error(ErrorTip.CrossWarObjTypeInBattleCD);
            return;
        }
        increaseBattleCd(targetType);
        Log.crossWar.info("===进入战斗!! 和目标对象={}的距离={} 攻击距离+半径={}", targetId, distance, m_atkDistance+targetObj.getModelRadius());
        long battleId = Port.getTime();
        // 设置自己进入战斗状态，广播更新属性，发送进入战斗协议
        setState(CrossWarConst.OBJ_STATE_BATTLE);
        broadcastUpdateElem();
        setBattleData(battleId, targetObj.getObjId());
        sendEnterBattleMsg(targetObj, true);
        // 设置目标进入战斗状态，广播更新属性
        targetObj.setState(CrossWarConst.OBJ_STATE_BATTLE);
        targetObj.broadcastUpdateElem();
        if(targetObj.isPlayer()){
            // 目标是玩家，发送进入战斗协议
            CrossWarPlayer targetPlayer = (CrossWarPlayer) targetObj;
            targetPlayer.setBattleData(battleId, getObjId());
            targetPlayer.sendEnterBattleMsg(this, false);
        }
        // 进行战斗，计算并缓存战斗结果
        doBattle(targetObj);
    }

    /**
     * 发送进入战斗协议
     * @param targetObj
     * @param isAttacker
     */
    public void sendEnterBattleMsg(CrossWarSceneObj targetObj, boolean isAttacker){
        MsgScene.scene_battle_s2c.Builder builder = MsgScene.scene_battle_s2c.newBuilder();
        builder.setRandomSeed(m_battleId/Time.SEC);
        Define.p_battle_role.Builder myBattleRole = getBattleRole(targetObj.getObjType());
        if(myBattleRole == null){
            Log.crossWar.warn("===sendEnterBattleMsg myBattleRole == null，m_objId={}", m_objId);
            return;
        }
        if(targetObj.getObjType() == CrossWarConst.OBJ_TYPE_PLAYER){
            int chapterId = targetObj.getObjType();
            builder.setId(chapterId);
            builder.setType(InstanceConstants.KFWARCHAPTER_17);
            Define.p_battle_role.Builder targetBattleRole = targetObj.getBattleRole(getObjType());
            if(targetBattleRole == null){
                Log.crossWar.warn("===sendEnterBattleMsg targetBattleRole == null，m_objId={} targetId={}", m_objId, targetObj.getObjId());
                return;
            }
            if(isAttacker){
                builder.addRoles(myBattleRole);
                builder.addRoles(targetBattleRole);
            }
            else{
                builder.addRoles(targetBattleRole);
                builder.addRoles(myBattleRole);
            }
        }
        else {
            int chapterId = targetObj.getObjType();
            builder.setId(chapterId);
            builder.setType(InstanceConstants.KFWARCHAPTERMONSTER_18);
            Define.p_battle_monster.Builder targetBattleMonster = targetObj.getBattleMonster(this);
            if(targetBattleMonster == null){
                Log.crossWar.warn("===sendEnterBattleMsg targetBattleMonster == null，m_objId={} targetId={}", m_objId, targetObj.getObjId());
                return;
            }
            builder.addMonsters(targetBattleMonster);
            builder.addRoles(myBattleRole);
        }
        sendMsg(builder.build());
    }

    /**
     * 进行战斗
     * @param targetObj
     */
    public void doBattle(CrossWarSceneObj targetObj){
        long winId = -1;
        long targetHarmValue = 0;
        if(targetObj.getObjType() == CrossWarConst.OBJ_TYPE_PLAYER){
            Log.crossWar.info("玩家={} 和玩家战斗 target={}", m_objId, targetObj.getObjId());
            long[] battleResult = doBattleWithPlayerOrRobot(targetObj);
            winId = battleResult[0];
            targetHarmValue = battleResult[1];
        }
        else{
            Log.crossWar.info("玩家={} 和怪物战斗 target={}", m_objId, targetObj.getObjId());
            long[] battleResult = doBattleWithMonster(targetObj);
            winId = battleResult[0];
            targetHarmValue = battleResult[1];
        }
        if(winId < 0){
            return;
        }
        if(winId == m_objId){
            battleWin(targetObj, targetHarmValue);
            targetObj.battleLose(this);
        }
        else{
            battleLose(targetObj);
            targetObj.battleWin(this, targetHarmValue);
        }
    }

    /**
     * 和玩家或机器人进行战斗
     * @param targetObj
     */
    public long[] doBattleWithPlayerOrRobot(CrossWarSceneObj targetObj) {
        long humanIdAtk = getObjId();
        long humanIdDef = targetObj.getObjId();
        Define.p_battle_role.Builder atkBattle = getBattleRole(targetObj.getObjType());
        if(atkBattle == null){
            return new long[]{-1, 0};
        }
        Define.p_battle_role.Builder defBattle = targetObj.getBattleRole(this.getObjType());
        if(defBattle == null){
            return new long[]{-1, 0};
        }
        long seed = m_battleId/Time.SEC;
        try {
            long beforeAtkHpPercent = getHp();
            long beforeDefHpPercent = targetObj.getHp();
            long beforeAtkHp = beforeAtkHpPercent * getBattleMaxHp();
            long beforeDefHp = beforeDefHpPercent * targetObj.getBattleMaxHp();
            Log.crossWar.info("====战斗前校验血量，humanIdAtk={}:{}--{}‱，humanIdDef={}:{}--{}‱",
                    humanIdAtk, beforeAtkHp, beforeAtkHpPercent, humanIdDef, beforeDefHp, beforeDefHpPercent);

            int chapterId = targetObj.getObjType();
            MsgBattle.battle_result_c2s battleResult = BattleLib.doCrossWarPvpBattle(seed, chapterId, atkBattle.build(), defBattle.build());
            long afterAtkHp = battleResult.getExt(0).getV();
            long afterDefHp = battleResult.getExt(1).getV();
            long afterAtkHpPercent = afterAtkHp * 10000 / getBattleMaxHp();
            long afterDefHpPercent = afterDefHp * 10000 / targetObj.getBattleMaxHp();
            long winHumanId = afterAtkHp >= afterDefHp ? humanIdAtk : humanIdDef;
            Log.crossWar.info("====战斗结果，humanIdAtk={}:{}--{}‱，humanIdDef={}:{}--{}‱, winner is {}",
                    humanIdAtk, afterAtkHp, afterAtkHpPercent, humanIdDef, afterDefHp, afterDefHpPercent, winHumanId);
            long atkChangeHpPercent = afterAtkHpPercent - beforeAtkHpPercent;
            updateHp(targetObj, atkChangeHpPercent);
            long defChangeHpPercent = afterDefHpPercent - beforeDefHpPercent;
            targetObj.updateHp(this, defChangeHpPercent);
            return new long[]{winHumanId, -defChangeHpPercent};
        } catch (Exception e) {
            Log.crossWar.error("====战斗出错，humanIdAtk={}，humanIdDef={},{}", humanIdAtk, humanIdDef, e.getStackTrace());
            return new long[]{-1, 0};
        }
    }

    /**
     * 和怪物进行战斗
     * @param targetObj
     */
    public long[] doBattleWithMonster(CrossWarSceneObj targetObj) {
        long humanIdAtk = getObjId();
        long humanIdDef = targetObj.getObjId();
        Define.p_battle_role.Builder atkBattle = getBattleRole(targetObj.getObjType());
        if(atkBattle == null){
            return new long[]{-1, 0};
        }
        Define.p_battle_monster.Builder defBattle = targetObj.getBattleMonster(this);
        if(defBattle == null){
            return new long[]{-1, 0};
        }
        long seed = m_battleId/Time.SEC;
        try {
            long beforeAtkHpPercent = getHp();
            long beforeDefHpPercent = targetObj.getHp();
            long beforeAtkHp = beforeAtkHpPercent * getBattleMaxHp();
            long beforeDefHp = beforeDefHpPercent * targetObj.getBattleMaxHp();
            Log.crossWar.info("====战斗前校验血量，humanIdAtk={}:{}--{}‱，monsterIdDef={}:{}--{}‱",
                    humanIdAtk, beforeAtkHp, beforeAtkHpPercent, humanIdDef, beforeDefHp, beforeDefHpPercent);

            int chapterId = targetObj.getObjType();
            MsgBattle.battle_result_c2s battleResult = BattleLib.doCrossWarPveBattle(seed, chapterId, atkBattle.build(), defBattle.build());
            long afterAtkHp = battleResult.getExt(0).getV();
            long afterDefHp = battleResult.getExt(1).getV();
            long afterAtkHpPercent = afterAtkHp * 10000 / getBattleMaxHp();
            long afterDefHpPercent = afterDefHp * 10000 / targetObj.getBattleMaxHp();
            long winHumanId = afterAtkHp >= afterDefHp ? humanIdAtk : humanIdDef;
            if(targetObj.getObjType() == CrossWarConst.OBJ_TYPE_SONS_OF_LIGHT){
                // 与光明之子的战斗，玩家总是获胜
                winHumanId = humanIdAtk;
            }
            Log.crossWar.info("====战斗结果，humanIdAtk={}:{}--{}‱，monsterIdDef={}:{}--{}‱, winner is {}",
                    humanIdAtk, afterAtkHp, afterAtkHpPercent, humanIdDef, afterDefHp, afterDefHpPercent, winHumanId);
            long atkChangeHpPercent = afterAtkHpPercent - beforeAtkHpPercent;
            updateHp(targetObj, atkChangeHpPercent);
            // 怪物存血量万分比
            long defChangeHpPercent = afterDefHpPercent - beforeDefHpPercent;
            targetObj.updateHp(this, defChangeHpPercent);
            return new long[]{winHumanId, -defChangeHpPercent};
        } catch (Exception e) {
            Log.crossWar.error("====战斗出错，humanIdAtk={}，monsterIdDef={},{}", humanIdAtk, humanIdDef, e.getStackTrace());
            return new long[]{-1, 0};
        }
    }

    @Override
    public void battleWin(CrossWarSceneObj targetObj, long targetHarmValue){
        Log.crossWar.info("===battleWin {} {}", m_objId, targetObj.getObjId());
        String uniqueId = getCurrentWeekUniqueId();
        HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance(DistrKit.getWorldNodeID(m_myServerId));
        if(targetObj.getObjType() == CrossWarConst.OBJ_TYPE_PLAYER){
            m_killPlayerNum++;
            proxy.crossWarKillPlayer(m_objId, isInvade());
            if(isInvade()){
                // 入侵则加积分
                int scoreValue = calcScore(targetObj, targetHarmValue);
                Log.crossWar.info("{}在与玩家{}的入侵战斗中获胜，增加积分={}", m_objId, targetObj.getObjId(), scoreValue);
                addScore(scoreValue, true);
                // 延长入侵时间
                ConfCrossWarKv confKv3 = ConfCrossWarKv.get(CrossWarConst.crossWarKvSn_3);
                int extendTime = (confKv3 != null ? confKv3.value[1] : 180);
                int checkPlayerNum = (confKv3 != null ? confKv3.value[2] : 3);
                if(m_killPlayerNum == checkPlayerNum){
                    extendInvadeTime(extendTime);
                }
                ConfCrossWarKv confKv26 = ConfCrossWarKv.get(CrossWarConst.crossWarKvSn_26);
                int checkKillerListNum = (confKv26 != null ? confKv26.value[0] : 5);
                if(m_killPlayerNum == checkKillerListNum){
                    m_crossWarService.addToKillerList(this);
                }
            }
            else{
                // 防御，更新御敌榜
                String key = RedisKeys.cross_war_1028_rank + uniqueId;
                RedisTools.updateRankWithTimestamp(EntityManager.redisClient, key, m_objId, 1, res->{
                    if(res.failed()){
                        return;
                    }
                    RedisTools.expire(EntityManager.redisClient, key, CrossWarConst.crossWarRank_expireTime);
                });
            }
        }
        else if(targetObj.getObjType() == CrossWarConst.OBJ_TYPE_MONSTER){
            m_killMonsterNum++;
            proxy.crossWarKillMonster(m_objId, isInvade());
            if(isInvade()){
                // 入侵则加积分
                int scoreValue = calcScore(targetObj, targetHarmValue);
                Log.crossWar.info("{}在与怪物{}的入侵战斗中获胜，增加积分={}", m_objId, targetObj.getObjId(), scoreValue);
                addScore(scoreValue, false);
                // 入侵，更新除魔榜
                String key = RedisKeys.cross_war_1042_rank + uniqueId;
                RedisTools.updateRankWithTimestamp(EntityManager.redisClient, key, m_objId, 1, res->{
                    if(res.failed()){
                        return;
                    }
                    RedisTools.expire(EntityManager.redisClient, key, CrossWarConst.crossWarRank_expireTime);
                });
            }
        }
        else if(targetObj.getObjType() == CrossWarConst.OBJ_TYPE_SONS_OF_LIGHT){
            int scoreValue = calcScore(targetObj, targetHarmValue);
            Log.crossWar.info("{}在与光明之子{}的入侵战斗中获胜，增加积分={}", m_objId, targetObj.getObjId(), scoreValue);
            addScore(scoreValue, true);
        }
    }

    /**
     * 结束战斗
     */
    public void endBattle(){
        if(m_battleId == 0 || m_battleTargetId == 0){
            Log.crossWar.warn("===无战斗id或无战斗敌方id，无法结束战斗");
            return;
        }
        Log.crossWar.info("===玩家={}与对象={} 战斗结束", m_objId, m_battleTargetId);
        if(m_state == CrossWarConst.OBJ_STATE_DEAD){
            // 结束战斗时为死亡状态，则设置可复活时间戳
            int interval = calcReviveInterval();
            m_reviveTime = Port.getTime() + interval * Time.SEC;
            Log.crossWar.info("===玩家={}战斗结束时死亡，设置复活时间为{}秒后", m_objId, interval);
        }
        else{
            setState(CrossWarConst.OBJ_STATE_IDLE);
        }
        // 广播更新自己的属性
        broadcastUpdateElem();
        CrossWarSceneObj targetObj = m_scene.getSceneObj(m_battleTargetId);
        if(targetObj != null && !targetObj.isPlayer()){
            // 如果敌方不是玩家，同时改变其状态
            if(targetObj.getState() == CrossWarConst.OBJ_STATE_DEAD){
                // 结束战斗时为死亡状态，则设置重生时间戳
                targetObj.setRebornTime();
            }
            else{
                targetObj.setState(CrossWarConst.OBJ_STATE_IDLE);
                // 广播更新敌方状态
                targetObj.broadcastUpdateElem();
            }
        }
        m_battleId = 0;
    }

    /**
     * 计算复活间隔
     * @return
     */
    private int calcReviveInterval(){
        int time = 10;
        if(!isInvade()){
            // 本服固定10秒
            return time;
        }
        ConfCrossWarKv confKv = ConfCrossWarKv.get(CrossWarConst.crossWarKvSn_8);
        if(confKv != null){
            int index = Math.min(confKv.value.length-1, m_reviveNum);
            time = confKv.value[index];
        }
        return time;
    }

    @Override
    public void onDead(CrossWarSceneObj killer) {
        super.onDead(killer);
    }

    /**
     * 复活
     * @param type
     * @param costItemEnough
     */
    public boolean revive(int type, boolean costItemEnough){
        if(m_scene == null){
            Log.crossWar.warn("===当前所在场景为空，无法复活");
            return false;
        }
        if(m_state != CrossWarConst.OBJ_STATE_DEAD){
            Log.crossWar.warn("===当前不在死亡状态，无法复活");
            return false;
        }
        if(Port.getTime() < m_reviveTime){
            Log.crossWar.info("===revive cd. 还需{}ms后可复活", m_reviveTime -Port.getTime());
            sendMsg_error(ErrorTip.CrossWarInCD);
            return false;
        }
        if(type == CrossWarConst.REVIVE_TYPE_TRANSFER){
            // 入侵时传送复活需要消耗钻石，本服防守时不需要
            if(isInvade() && !costItemEnough){
                sendMsg_error(ErrorTip.ItemNotEnough);
                return false;
            }
            int[] randomPos = m_scene.getRandomPos(new Random());
            Vector2D newPos = new Vector2D(randomPos[0], randomPos[1]);
            Log.crossWar.info("===玩家={}重生在{}", getObjId(), newPos);
            enterScene(m_scene.getSceneBaseId(), newPos);
        }
        m_reviveNum++;
        m_hp = getHpMax();
        setState(CrossWarConst.OBJ_STATE_IDLE);
        broadcastUpdateElem();
        MsgCrossWar.cross_war_revive_s2c.Builder msg = MsgCrossWar.cross_war_revive_s2c.newBuilder();
        msg.setType(type);
        sendMsg(msg.build());
        return true;
    }

    /**
     * 延长入侵时间
     * @param extendTime
     */
    public void extendInvadeTime(int extendTime) {
        m_invadeEndTime += (extendTime * Time.SEC);
        sendSceneInfo();
    }

    /*
     * 加速
     */
    public void speedUp(){
        if(getScene() == null){
            Log.crossWar.warn("===玩家不在场景中，无法加速");
            return;
        }
        if(Port.getTime() < m_speedTime){
            Log.crossWar.warn("===speedUp cd. 还需{}ms后可加速", m_speedTime -Port.getTime());
            sendMsg_error(ErrorTip.CrossWarInCD);
            return;
        }
        // 添加加速效果
        addSpeedUpEffect();
        // 增加加速cd
        increaseSpeedCd();
        Log.crossWar.info("===开始加速 player={}", this);
    }

    /**
     * 增加加速cd
     */
    private void increaseSpeedCd(){
        ConfCrossWarKv confKv = ConfCrossWarKv.get(CrossWarConst.crossWarKvSn_14);
        m_speedTime = Port.getTime() + (confKv != null ? confKv.value[0] : 30) * Time.SEC;
        sendSceneInfo();
    }

    /**
     * 增加战斗cd
     */
    private void increaseBattleCd(int targetType){
        int cdValue = 0;
        if(targetType == CrossWarConst.OBJ_TYPE_SONS_OF_LIGHT){
            ConfCrossWarKv confKv = ConfCrossWarKv.get(CrossWarConst.crossWarKvSn_107);
            cdValue = confKv != null ? confKv.value[0] : 60;
        }
        else{
            ConfCrossWarKv confKv = ConfCrossWarKv.get(CrossWarConst.crossWarKvSn_108);
            cdValue = confKv != null ? confKv.value[0] : 30;
        }
        setBattleCd(targetType, cdValue);
    }

    private void setBattleCd(int targetType, int cdValue){
        long nextTime = Port.getTime() + cdValue * Time.SEC;
        switch (targetType){
            case CrossWarConst.OBJ_TYPE_PLAYER:
                m_atkPlayerTime = nextTime;
                break;
            case CrossWarConst.OBJ_TYPE_MONSTER:
                m_atkMonsterTime = nextTime;
                break;
            case CrossWarConst.OBJ_TYPE_SONS_OF_LIGHT:
                m_atkSonOfLightTime = nextTime;
                break;
            default:
                break;
        }
        // 发送玩家攻击cd
        sendObjAtkCd(targetType);
    }

    /**
     * 发送玩家攻击cd
     */
    public void sendObjAtkCd(int targetType){
        MsgScene.scene_obj_attack_cd_s2c.Builder msg = MsgScene.scene_obj_attack_cd_s2c.newBuilder();
        Define.p_key_value.Builder builder = Define.p_key_value.newBuilder();
        builder.setK(targetType);
        builder.setV(getBattleCd(targetType)/Time.SEC);
        msg.addAttackCd(builder);
        sendMsg(msg.build());
    }

    private long getBattleCd(int targetType){
        switch (targetType){
            case CrossWarConst.OBJ_TYPE_PLAYER:
                return m_atkPlayerTime;
            case CrossWarConst.OBJ_TYPE_MONSTER:
                return m_atkMonsterTime;
            case CrossWarConst.OBJ_TYPE_SONS_OF_LIGHT:
                return m_atkSonOfLightTime;
            default:
                break;
        }
        return Port.getTime();
    }

    /**
     * 增加积分
     * @param score
     * @param isExtra
     */
    public void addScore(int score, boolean isExtra){
        if(isExtra){
            m_extraScore += score;
        }
        else{
            ConfCrossWarKv confKv = ConfCrossWarKv.get(CrossWarConst.crossWarKvSn_24);
            int maxBaseScore = confKv != null ? confKv.value[0] : 20000;
            m_baseScore = Math.min(maxBaseScore, m_baseScore+score);
        }
        sendSceneInfo();
    }

    public int getScore(){
        return m_baseScore + m_extraScore;
    }

    public int calcScore(CrossWarSceneObj targetObj, long targetHarmValue){
        if(targetObj.getObjType() == CrossWarConst.OBJ_TYPE_MONSTER){
            CrossWarMonster targetMonster = (CrossWarMonster)targetObj;
            Log.crossWar.info("calcScore monsterId={}", targetMonster.getMonsterId());
            return GlobalConfVal.crossWarMonsterScoreMap.getOrDefault(targetMonster.getMonsterId(), 0);
        }
        if(targetObj.getObjType() == CrossWarConst.OBJ_TYPE_SONS_OF_LIGHT){
            int point = GlobalConfVal.crossWarSonOfLightScoreInfo[2];
            Log.crossWar.info("calcScore sonOfLight targetHarmValue={} point={}", targetHarmValue, point);
            return point * 10000 * Utils.intValue(targetHarmValue) * GlobalConfVal.crossWarSonOfLightScoreInfo[1] / GlobalConfVal.crossWarSonOfLightScoreInfo[0];
        }
        int targetLevel = targetObj.getLevel();
        Log.crossWar.info("calcScore player level={}", targetLevel);
        return CrossWarUtils.getCrossWarPlayerAddScore(targetLevel);
    }
}
