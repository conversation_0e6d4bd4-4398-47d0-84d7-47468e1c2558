package org.gof.demo.worldsrv.god;

import org.gof.core.Port;
import org.gof.core.support.ManagerBase;
import org.gof.core.support.Param;
import org.gof.core.support.Utils;
import org.gof.core.support.observer.Listener;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.*;
import org.gof.demo.worldsrv.entity.God;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.human.FuncOpenType;
import org.gof.demo.worldsrv.inform.ErrorTip;
import org.gof.demo.worldsrv.inform.Inform;
import org.gof.demo.worldsrv.instance.InstanceConstants;
import org.gof.demo.worldsrv.instance.InstanceManager;
import org.gof.demo.worldsrv.item.ItemManager;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgGod;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.produce.ProduceVo;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.ReasonResult;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;
import org.gof.demo.worldsrv.support.observer.EventKey;

import java.util.*;

public class GodManager extends ManagerBase {
    
    /**
     * 获取实例
     */
    public static GodManager inst() {
        return inst(GodManager.class);
    }
    
    /**
     * 初始化天使数据
     */
    public void initGodData(HumanObject humanObj, God god) {
        if (god == null) {
            return;
        }
        
        GodData godData = new GodData(god);
        humanObj.operation.godData = godData;
    }
    
    /**
     * 处理天使信息请求
     */
    public void handleGodInfoC2S(HumanObject humanObj) {
        GodData godData = humanObj.operation.godData;
        if (godData == null) {
            return;
        }

        MsgGod.god_info_s2c.Builder response = MsgGod.god_info_s2c.newBuilder();

        // 添加天使列表
        for (GodVo godVo : godData.getGodsMap().values()) {
            Define.p_god.Builder pGod = Define.p_god.newBuilder();
            pGod.setId(godVo.getId());
            pGod.setSn(godVo.getSn());
            pGod.setLevel(godVo.getLevel());
            pGod.setExp(godVo.getExp());
            pGod.setIsLocked(godVo.isLocked());
            response.addGods(pGod.build());
        }

        // 添加当前方案的阵容配置（只发送已解锁的槽位）
        Map<Integer, Long> currentFormation = godData.getCurrentFormation();
        List<Integer> unlockedPos = godData.getUnlockedPos();
        for (Integer pos : unlockedPos) {
            Long godId = currentFormation.getOrDefault(pos, 0L);
            Define.p_key_value.Builder kv = Define.p_key_value.newBuilder();
            kv.setK(pos);
            kv.setV(godId);
            response.addFormation(kv.build());
        }

        // 添加已拥有的皮肤
        for (Map.Entry<Integer, Integer> entry : godData.getOwnedSkinsMap().entrySet()) {
            Define.p_key_value.Builder kv = Define.p_key_value.newBuilder();
            kv.setK(entry.getKey());
            kv.setV(entry.getValue());
            response.addOwnedSkins(kv.build());
        }

        // 添加已装备的皮肤
        for (Map.Entry<Long, Integer> entry : godData.getEquippedSkinsMap().entrySet()) {
            response.addEquipSkins(entry.getValue());
        }

        // 添加已激活的图鉴
        for (Map.Entry<Integer, Integer> entry : godData.getActiveCodexMap().entrySet()) {
            Define.p_key_value.Builder kv = Define.p_key_value.newBuilder();
            kv.setK(entry.getKey());
            kv.setV(entry.getValue());
            response.addActiveCodex(kv.build());
        }

        // 添加可激活的图鉴
        for (Map.Entry<Integer, Integer> entry : godData.getAvailCodexMap().entrySet()) {
            Define.p_key_value.Builder kv = Define.p_key_value.newBuilder();
            kv.setK(entry.getKey());
            kv.setV(entry.getValue());
            response.addAvailCodex(kv.build());
        }

        // 添加已激活的羁绊
        response.addAllActiveCamps(godData.getActiveCamps());

        // 添加可激活的羁绊
        response.addAllAvailCamps(godData.getAvailCamps());

        // 添加抽卡信息
        God god = godData.getGod();
        response.setDrawRewardSn(god.getDrawRewardSn());
        response.setDrawExp(god.getDrawExp());

        humanObj.sendMsg(response.build());
    }
    
    /**
     * 功能解锁监听
     */
    @Listener(EventKey.FUNCTION_OPEN)
    public void unlock(Param params) {
        HumanObject humanObj = params.get("humanObj");
        if (humanObj.operation.godData != null) {
            return;
        }
        
        List<Integer> snList = Utils.getParamValue(params, "openList", new ArrayList<>());
        if (!snList.contains(FuncOpenType.FUNC_GOD)) {
            return;
        }
        
        createGod(humanObj);
    }
    
    /**
     * 创建天使数据
     */
    public void createGod(HumanObject humanObj) {
        God god = new God();
        god.setId(humanObj.id);

        humanObj.operation.godData = new GodData(god);
        
        // 初始化默认解锁主战位
        GodData godData = humanObj.operation.godData;
        Map<Integer, Long> formationMap = new HashMap<>();
        formationMap.put(0, 0L); // 主战位默认解锁但未装备
        List<Integer> unlockedPos = godData.getUnlockedPos();
        unlockedPos.add(0);
        godData.setUnlockedPos(unlockedPos);
        Map<Integer, Map<Integer, Long>> formationTab = godData.getFormationTab();
        formationTab.put(1, formationMap);
        godData.setFormationTab(formationTab);
        
        // 初始化初始箱子
        initDrawReward(humanObj);

        god.persist();
    }

    /**
     * 初始化抽卡奖励箱子
     */
    private void initDrawReward(HumanObject humanObj) {
        GodData godData = humanObj.operation.godData;
        if (godData == null) {
            return;
        }

        // 查找初始箱子
        for (ConfDrawCumulativeReward conf : ConfDrawCumulativeReward.findAll()) {
            if (conf.initial == EGodType.INITIAL_BOX) {
                godData.getGod().setDrawRewardSn(conf.sn);
                godData.getGod().setDrawExp(0);
                break;
            }
        }
    }

    /**
     * 处理布阵请求
     */
    public void handleGodSetFormationC2S(HumanObject humanObj, long godId, int pos) {
        GodData godData = humanObj.operation.godData;
        if (godData == null) {
            return;
        }

        // 检查位置是否合法
        if (pos < 0 || pos > 4) {
            Log.game.error("天使布阵位置错误，pos={}", pos);
            return;
        }

        // 检查位置是否解锁
        int currentTab = godData.getCurrentTab();
        Map<Integer, Long> formationMap = godData.getFormationMap(currentTab);
        if (pos > 0 && !formationMap.containsKey(pos)) {
            // 检查解锁条件
            ConfGodPosition confPos = ConfGodPosition.get(pos);
            if (confPos == null || !checkPositionUnlock(humanObj, confPos)) {
                Inform.sendMsg_error(humanObj, ErrorTip.SystemDefault);
                return;
            }
            // 解锁位置
            formationMap.put(pos, 0L);
        }

        // 如果godId为0，表示下阵
        if (godId == 0) {
            formationMap.put(pos, 0L);
        } else {
            // 检查天使是否存在
            GodVo godVo = godData.getGodVo(godId);
            if (godVo == null) {
                Log.game.error("天使不存在，godId={}", godId);
                return;
            }

            // 检查天使是否已在其他位置
            for (Map.Entry<Integer, Long> entry : formationMap.entrySet()) {
                if (entry.getValue() == godId && entry.getKey() != pos) {
                    formationMap.put(entry.getKey(), 0L);
                    break;
                }
            }

            formationMap.put(pos, godId);
        }

        godData.saveFormationTab();

        // 发送响应
        MsgGod.god_set_formation_s2c.Builder response = MsgGod.god_set_formation_s2c.newBuilder();
        for (Map.Entry<Integer, Long> entry : formationMap.entrySet()) {
            Define.p_key_value.Builder kv = Define.p_key_value.newBuilder();
            kv.setK(entry.getKey());
            kv.setV(entry.getValue());
            response.addPosList(kv.build());
        }
        humanObj.sendMsg(response.build());
    }

    /**
     * 检查位置解锁条件
     */
    private boolean checkPositionUnlock(HumanObject humanObj, ConfGodPosition confPos) {
        if (confPos.lockCondition == null || confPos.lockCondition.length < 3) {
            return true;
        }

        GodData godData = humanObj.operation.godData;
        int godSn = confPos.lockCondition[0];
        int minLevel = confPos.lockCondition[1];
        int count = confPos.lockCondition[2];

        // 统计满足条件的天使数量
        int validCount = 0;
        for (GodVo godVo : godData.getGodsMap().values()) {
            ConfGod confGod = ConfGod.get(godVo.getSn());
            if (confGod != null && confGod.identification == godSn && godVo.getLevel() >= minLevel) {
                validCount++;
            }
        }

        return validCount >= count;
    }

    /**
     * 处理天使升级请求
     */
    public void handleGodLevelUpC2S(HumanObject humanObj, long godId, int type) {
        GodData godData = humanObj.operation.godData;
        if (godData == null) {
            return;
        }

        GodVo godVo = godData.getGodVo(godId);
        if (godVo == null) {
            Log.game.error("天使不存在，godId={}", godId);
            return;
        }

        ConfGod confGod = ConfGod.get(godVo.getSn());
        if (confGod == null) {
            Log.game.error("天使配置不存在，sn={}", godVo.getSn());
            return;
        }

        // 检查等级上限
        if (godVo.getLevel() >= confGod.lvMax) {
            Inform.sendMsg_error(humanObj, ErrorTip.SystemDefault);
            return;
        }

        // 计算升级次数
        int targetLevel = type == EGodType.LEVEL_UP_ONE ? godVo.getLevel() + 1 : Math.min(godVo.getLevel() + 10, confGod.lvMax);

        // 计算所需经验
        int needExp = 0;
        for (int lv = godVo.getLevel(); lv < targetLevel; lv++) {
            ConfGodLv_0 confLv = ConfGodLv_0.get(confGod.lvAttrGroup, lv + 1);
            if (confLv == null) {
                break;
            }
            needExp = confLv.expend[1];
        }

        needExp -= godVo.getExp();

        if (needExp <= 0) {
            return;
        }

        // 检查并扣除经验道具
        ConfGlobal confGlobal = ConfGlobal.get("God_exp_item");
        if (confGlobal == null || confGlobal.intArray == null || confGlobal.intArray.length < 2) {
            Log.game.error("天使经验道具配置错误");
            return;
        }

        int expItemSn = confGlobal.intArray[0];
        int expPerItem = confGlobal.intArray[1];
        int needItemCount = (needExp + expPerItem - 1) / expPerItem;

        ReasonResult rr = ProduceManager.inst().checkAndCostItem(humanObj, expItemSn, needItemCount, MoneyItemLogKey.天使升级);
        if (!rr.success) {
            Inform.sendMsg_error(humanObj, ErrorTip.ItemNotEnough);
            return;
        }

        // 升级
        int totalExp = godVo.getExp() + needItemCount * expPerItem;
        int currentLevel = godVo.getLevel();

        while (currentLevel < targetLevel) {
            ConfGodLv_0 confLv = ConfGodLv_0.get(confGod.lvAttrGroup, currentLevel + 1);
            if (confLv == null || totalExp < confLv.expend[1]) {
                break;
            }
            totalExp -= confLv.expend[1];
            currentLevel++;
        }

        godVo.setLevel(currentLevel);
        godVo.setExp(totalExp);
        godData.saveGodsList();

        // 发送响应
        MsgGod.god_level_up_s2c.Builder response = MsgGod.god_level_up_s2c.newBuilder();
        response.setGodId(godId);
        response.setLevel(currentLevel);
        response.setExp(totalExp);
        humanObj.sendMsg(response.build());
    }

    /**
     * 处理天使等级重置请求
     */
    public void handleGodLevelResetC2S(HumanObject humanObj, long godId) {
        GodData godData = humanObj.operation.godData;
        if (godData == null) {
            return;
        }

        GodVo godVo = godData.getGodVo(godId);
        if (godVo == null) {
            Log.game.error("天使不存在，godId={}", godId);
            return;
        }

        ConfGodLv_0 confGodLv = ConfGodLv_0.get(godVo.getSn());
        if (confGodLv == null) {
            Log.game.error("天使配置不存在，sn={}", godVo.getSn());
            return;
        }

        // 扣除重置消耗
        if (confGodLv.resetCost != null && confGodLv.resetCost.length >= 2) {
            ReasonResult rr = ProduceManager.inst().checkAndCostItem(humanObj, confGodLv.resetCost, MoneyItemLogKey.天使等级重置);
            if (!rr.success) {
                Inform.sendMsg_error(humanObj, ErrorTip.ItemNotEnough);
                return;
            }
        }

        // 重置等级
        godVo.setLevel(1);
        godVo.setExp(0);
        godData.saveGodsList();

        // 发放返还道具
        ProduceManager.inst().produceAdd(humanObj, confGodLv.resetGoods, MoneyItemLogKey.天使等级重置);

        // 发送响应
        MsgGod.god_level_reset_s2c.Builder response = MsgGod.god_level_reset_s2c.newBuilder();
        response.setGodId(godId);

        Define.p_reward.Builder reward = Define.p_reward.newBuilder();
        reward.setGtid(confGodLv.resetGoods[0][0]);
        reward.setNum(confGodLv.resetGoods[0][1]);
        response.setReturnedItems(reward.build());

        humanObj.sendMsg(response.build());
    }

    /**
     * 处理天使升星请求
     */
    public void handleGodStarUpC2S(HumanObject humanObj, List<Define.p_god_star_up> materialList) {
        GodData godData = humanObj.operation.godData;
        if (godData == null) {
            return;
        }

        if (materialList == null || materialList.isEmpty()) {
            return;
        }

        // 获取主天使
        Define.p_god_star_up mainMaterial = materialList.get(0);
        long mainGodId = mainMaterial.getGodId();
        GodVo mainGod = godData.getGodVo(mainGodId);
        if (mainGod == null) {
            Log.game.error("主天使不存在，godId={}", mainGodId);
            return;
        }

        ConfGod confGod = ConfGod.get(mainGod.getSn());
        if (confGod == null) {
            Log.game.error("天使配置不存在，sn={}", mainGod.getSn());
            return;
        }

        // 检查是否可以升星
        ConfGod nextConfGod = ConfGod.get(confGod.sn + 1);
        if (nextConfGod == null || nextConfGod.identification != confGod.identification || nextConfGod.star != confGod.star + 1) {
            Inform.sendMsg_error(humanObj, ErrorTip.SystemDefault);
            return;
        }

        // 检查材料
        List<Long> materialGodIds = new ArrayList<>();
        Map<Integer, Integer> materialItems = new HashMap<>();

        for (int i = 0; i < confGod.upCostType.length; i++) {
            int costType = confGod.upCostType[i];
            int costSn = confGod.upCost[i];
            int costValue = confGod.upCostValue[i];

            if (costType == EGodType.UP_COST_TYPE_SAME_SN) {
                // 消耗指定SN的本体天使
                int count = 0;
                for (Define.p_god_star_up material : materialList) {
                    if (material.getGodId() == mainGodId) continue;
                    GodVo godVo = godData.getGodVo(material.getGodId());
                    if (godVo != null && godVo.getSn() == costSn) {
                        materialGodIds.add(material.getGodId());
                        count++;
                    }
                }
                if (count < costValue) {
                    int needMore = costValue - count;

                    Inform.sendMsg_error(humanObj, ErrorTip.ItemNotEnough);
                    return;
                }
            } else if (costType == EGodType.UP_COST_TYPE_SAME_STAR || costType == EGodType.UP_COST_TYPE_SAME_TYPE) {
                // 消耗同星级的任意天使或同星级同type的天使
                int count = 0;
                for (Define.p_god_star_up material : materialList) {
                    if (material.getGodId() == mainGodId) continue;
                    GodVo godVo = godData.getGodVo(material.getGodId());
                    if (godVo != null) {
                        ConfGod matConfGod = ConfGod.get(godVo.getSn());
                        if (matConfGod != null && matConfGod.star == confGod.star) {
                            if (costType == EGodType.UP_COST_TYPE_SAME_STAR || matConfGod.type == confGod.type) {
                                materialGodIds.add(material.getGodId());
                                count++;
                            }
                        }
                    }
                }

                // 检查道具材料
                if (count < costValue) {
                    int needMore = costValue - count;
                    // 遍历玩家拥有的道具材料
                    for (Define.p_god_star_up material : materialList) {
                        if (material.getGodId() == mainGodId) continue;
                        if (material.getItemSn() == 0) continue;

                        ConfGoods goods = ConfGoods.get(material.getItemSn());
                        if (goods == null) continue;

                        if (goods.type == EGodType.ITEM_TYPE_GOD_MATERIAL && goods.star == confGod.star) {
                            // SUBTYPE_ALL_TYPE 可以替代任何类型
                            if (goods.subtype == EGodType.SUBTYPE_NO_TYPE || goods.subtype == EGodType.SUBTYPE_ALL_TYPE ||
                                (costType == EGodType.UP_COST_TYPE_SAME_TYPE && goods.subtype == confGod.type)) {
                                int hasCount = ItemManager.inst().getItemNum(humanObj, goods.sn);
                                int useCount = Math.min(hasCount, needMore);
                                if (useCount > 0) {
                                    materialItems.put(goods.sn, materialItems.getOrDefault(goods.sn, 0) + useCount);
                                    needMore -= useCount;
                                    if (needMore <= 0) break;
                                }
                            }
                        }
                    }
                }

                if (count + materialItems.values().stream().mapToInt(Integer::intValue).sum() < costValue) {
                    Inform.sendMsg_error(humanObj, ErrorTip.ItemNotEnough);
                    return;
                }
            }
        }

        // 扣除道具材料
        if (!materialItems.isEmpty()) {
            ReasonResult rr = ProduceManager.inst().checkAndCostItem(humanObj, materialItems, MoneyItemLogKey.天使升星);
            if (!rr.success) {
                Inform.sendMsg_error(humanObj, ErrorTip.ItemNotEnough);
                return;
            }
        }

        // 返还材料天使的养成资源
        Map<Integer, Integer> returnItems = new HashMap<>();
        Map<Integer, Integer> returnCost = new HashMap<>();
        List<GodVo> deletedGods = new ArrayList<>();
        int currentTab = godData.getCurrentTab();
        for (long matGodId : materialGodIds) {
            GodVo matGod = godData.getGodVo(matGodId);
            if (matGod == null) continue;

            // 检查是否锁定或上阵
            if (matGod.isLocked()) {
                Inform.sendMsg_error(humanObj, ErrorTip.SystemDefault);
                return;
            }

            boolean inFormation = godData.getFormationMap(currentTab).values().contains(matGodId);
            if (inFormation) {
                Inform.sendMsg_error(humanObj, ErrorTip.SystemDefault);
                return;
            }

            // 计算返还经验
            ConfGod matConfGod = ConfGod.get(matGod.getSn());
            if (matConfGod != null && matConfGod.resetGoods != null && matConfGod.resetGoods.length >= 2) {
                returnItems = Utils.intArrToIntMap(returnItems,matConfGod.resetGoods);

                returnCost = Utils.intArrToIntMap(returnCost,matConfGod.resetCost);
            }

            deletedGods.add(matGod);
        }

        // 删除材料天使
        for (GodVo god : deletedGods) {
            godData.removeGodVo(god.getId());
        }

        // 升星
        mainGod.setSn(nextConfGod.sn);
        mainGod.setLevel(1);
        mainGod.setExp(0);
        godData.saveGodsList();

        // 发放返还道具
        ProduceManager.inst().produceAdd(humanObj, returnItems, MoneyItemLogKey.天使升星);

        // 检查图鉴羁绊
        checkAndUpdateCodexAndCamps(humanObj, nextConfGod.sn);

        // 发送响应
        MsgGod.god_star_up_s2c.Builder response = MsgGod.god_star_up_s2c.newBuilder();

        Define.p_god.Builder pGod = Define.p_god.newBuilder();
        pGod.setId(mainGod.getId());
        pGod.setSn(mainGod.getSn());
        pGod.setLevel(mainGod.getLevel());
        pGod.setExp(mainGod.getExp());
        pGod.setIsLocked(mainGod.isLocked());
        response.addUpdateAngel(pGod.build());

        for (GodVo god : deletedGods) {
            response.addDeleteList(god.getId());
        }

        for (Map.Entry<Integer, Integer> entry : returnItems.entrySet()) {
            Define.p_reward.Builder reward = Define.p_reward.newBuilder();
            reward.setGtid(entry.getKey());
            reward.setNum(entry.getValue());
            response.addReturnedItems(reward.build());
        }

        humanObj.sendMsg(response.build());
    }

    /**
     * 处理天使星级重置请求
     */
    public void handleGodStarResetC2S(HumanObject humanObj, long godId) {
        GodData godData = humanObj.operation.godData;
        if (godData == null) {
            return;
        }

        GodVo godVo = godData.getGodVo(godId);
        if (godVo == null) {
            Log.game.error("天使不存在，godId={}", godId);
            return;
        }

        ConfGod confGod = ConfGod.get(godVo.getSn());
        if (confGod == null) {
            Log.game.error("天使配置不存在，sn={}", godVo.getSn());
            return;
        }

        // 查找1星配置
        ConfGod oneStarConf = null;
        for (ConfGod conf : ConfGod.findAll()) {
            if (conf.identification == confGod.identification && conf.star == 1) {
                oneStarConf = conf;
                break;
            }
        }

        if (oneStarConf == null) {
            Log.game.error("找不到1星配置，identification={}", confGod.identification);
            return;
        }

        // 扣除重置消耗
        if (confGod.resetCost != null && confGod.resetCost.length >= 2) {
            ReasonResult rr = ProduceManager.inst().checkAndCostItem(humanObj, confGod.resetCost, MoneyItemLogKey.天使星级重置);
            if (!rr.success) {
                Inform.sendMsg_error(humanObj, ErrorTip.ItemNotEnough);
                return;
            }
        }

        // 检查是否上阵，如果上阵则下阵
        int currentTab = godData.getCurrentTab();
        Map<Integer, Long> formationMap = godData.getFormationMap(currentTab);
        for (Map.Entry<Integer, Long> entry : formationMap.entrySet()) {
            if (entry.getValue() == godId) {
                formationMap.put(entry.getKey(), 0L);
            }
        }
        godData.saveFormationTab();

        // 重置星级
        godVo.setSn(oneStarConf.sn);
        godVo.setLevel(1);
        godVo.setExp(0);
        godData.saveGodsList();

        // 发放返还道具
        ProduceManager.inst().produceAdd(humanObj, confGod.resetGoods, MoneyItemLogKey.天使星级重置);

        // 发送响应
        MsgGod.god_star_reset_s2c.Builder response = MsgGod.god_star_reset_s2c.newBuilder();
        response.setGodId(godId);

        response.addAllReturnedItems(InstanceManager.inst().to_p_rewardList(confGod.resetGoods));

        humanObj.sendMsg(response.build());
    }

    /**
     * 处理天使锁定请求
     */
    public void handleGodSetLockStateC2S(HumanObject humanObj, long godId, boolean isLocked) {
        GodData godData = humanObj.operation.godData;
        if (godData == null) {
            return;
        }

        GodVo godVo = godData.getGodVo(godId);
        if (godVo == null) {
            Log.game.error("天使不存在，godId={}", godId);
            return;
        }

        godVo.setLocked(isLocked);
        godData.saveGodsList();

        // 发送响应
        MsgGod.god_set_lock_state_s2c.Builder response = MsgGod.god_set_lock_state_s2c.newBuilder();
        response.setGodId(godId);
        response.setIsLocked(isLocked);
        humanObj.sendMsg(response.build());
    }

    /**
     * 处理激活图鉴请求
     */
    public void handleGodActivateCodexC2S(HumanObject humanObj, int identification) {
        GodData godData = humanObj.operation.godData;
        if (godData == null) {
            return;
        }

        Map<Integer, Integer> availCodexMap = godData.getAvailCodexMap();
        Map<Integer, Integer> activeCodexMap = godData.getActiveCodexMap();

        Integer availStar = availCodexMap.get(identification);
        if (availStar == null) {
            Inform.sendMsg_error(humanObj, ErrorTip.SystemDefault);
            return;
        }

        Integer activeStar = activeCodexMap.get(identification);
        if (activeStar != null && activeStar >= availStar) {
            Inform.sendMsg_error(humanObj, ErrorTip.SystemDefault);
            return;
        }

        // 查找对应星级的配置
        ConfGod confGod = null;
        for (ConfGod conf : ConfGod.findAll()) {
            if (conf.identification == identification && conf.star == availStar) {
                confGod = conf;
                break;
            }
        }

        if (confGod == null) {
            Log.game.error("找不到天使配置，identification={}, star={}", identification, availStar);
            return;
        }

        // 发放图鉴奖励
        ProduceManager.inst().produceAdd(humanObj, confGod.IllustratedReward, MoneyItemLogKey.天使图鉴激活);
        // 激活图鉴
        activeCodexMap.put(identification, availStar);
        availCodexMap.remove(identification);
        godData.setActiveCodexMap(activeCodexMap);
        godData.setAvailCodexMap(availCodexMap);

        // 发送响应
        MsgGod.god_activate_codex_s2c.Builder response = MsgGod.god_activate_codex_s2c.newBuilder();
        response.setIdentification(identification);
        response.setStarLevel(availStar);
        humanObj.sendMsg(response.build());
    }


    /**
     * 检查羁绊条件
     * requirements是天使SN列表，需要拥有所有列出的天使才能激活羁绊
     */
    private boolean checkCampRequirement(HumanObject humanObj, ConfGodCamp conf) {
        if (conf.requirements == null || conf.requirements.length == 0) {
            return false;
        }

        GodData godData = humanObj.operation.godData;

        // 检查是否拥有所有要求的天使
        for (int requiredSn : conf.requirements) {
            boolean found = false;
            for (GodVo godVo : godData.getGodsMap().values()) {
                if (godVo.getSn() == requiredSn) {
                    found = true;
                    break;
                }
            }

            if (!found) {
                return false;
            }
        }

        return true;
    }

    /**
     * 处理激活羁绊请求
     */
    public void handleGodActivateCampC2S(HumanObject humanObj, int campSn) {
        GodData godData = humanObj.operation.godData;
        if (godData == null) {
            return;
        }

        List<Integer> availCamps = godData.getAvailCamps();
        List<Integer> activeCamps = godData.getActiveCamps();

        if (!availCamps.contains(campSn)) {
            Inform.sendMsg_error(humanObj, ErrorTip.SystemDefault);
            return;
        }

        ConfGodCamp conf = ConfGodCamp.get(campSn);
        if (conf == null) {
            Log.game.error("羁绊配置不存在，sn={}", campSn);
            return;
        }

        // 激活羁绊
        activeCamps.add(campSn);
        availCamps.remove((Integer) campSn);
        godData.setActiveCamps(activeCamps);
        godData.setAvailCamps(availCamps);

        // 发送响应
        MsgGod.god_activate_camp_s2c.Builder response = MsgGod.god_activate_camp_s2c.newBuilder();
        response.setCampSn(campSn);
        humanObj.sendMsg(response.build());
    }

    /**
     * 处理装备皮肤请求
     */
    public void handleGodEquipSkinC2S(HumanObject humanObj, int skinId, boolean isEquip) {
        GodData godData = humanObj.operation.godData;
        if (godData == null) {
            return;
        }

        ConfGodSkin confSkin = ConfGodSkin.get(skinId);
        if (confSkin == null) {
            Log.game.error("皮肤配置不存在，skinId={}", skinId);
            return;
        }

        // 查找对应的天使
        long targetGodId = 0;
        for (GodVo godVo : godData.getGodsMap().values()) {
            ConfGod confGod = ConfGod.get(godVo.getSn());
            if (confGod != null && confGod.identification == confSkin.godId) {
                targetGodId = godVo.getId();
                break;
            }
        }

        if (targetGodId == 0) {
            Inform.sendMsg_error(humanObj, ErrorTip.SystemDefault);
            return;
        }

        GodVo godVo = godData.getGodVo(targetGodId);
        if (godVo == null) {
            return;
        }

        Map<Long, Integer> equippedSkinsMap = godData.getEquippedSkinsMap();

        if (isEquip) {
            // 检查是否拥有皮肤（默认皮肤不需要检查）
            if (confSkin.initialSkin != 1) {
                Map<Integer, Integer> ownedSkinsMap = godData.getOwnedSkinsMap();
                if (!ownedSkinsMap.containsKey(skinId)) {
                    Inform.sendMsg_error(humanObj, ErrorTip.SystemDefault);
                    return;
                }
            }

            // 装备皮肤
            equippedSkinsMap.put(targetGodId, skinId);
            godVo.setEquippedSkin(skinId);
        } else {
            // 卸下皮肤
            equippedSkinsMap.remove(targetGodId);
            godVo.setEquippedSkin(0);
        }

        godData.setEquippedSkinsMap(equippedSkinsMap);
        godData.saveGodsList();

        // 发送响应
        MsgGod.god_equip_skin_s2c.Builder response = MsgGod.god_equip_skin_s2c.newBuilder();
        response.setSkinId(skinId);
        response.setIsEquip(isEquip);
        humanObj.sendMsg(response.build());
    }

    /**
     * 处理皮肤升级请求
     */
    public void handleGodUpgradeSkinC2S(HumanObject humanObj, int skinId) {
        GodData godData = humanObj.operation.godData;
        if (godData == null) {
            return;
        }

        Map<Integer, Integer> ownedSkinsMap = godData.getOwnedSkinsMap();
        int currentLevel = ownedSkinsMap.getOrDefault(skinId, 0);
        int targetLevel = currentLevel + 1;

        ConfGodSkinlevel confLevel = ConfGodSkinlevel.get(skinId, targetLevel);
        if (confLevel == null) {
            Inform.sendMsg_error(humanObj, ErrorTip.SystemDefault);
            return;
        }

        // 检查并扣除消耗
        if (confLevel.expend != null && confLevel.expend.length >= 2) {
            ReasonResult rr = ProduceManager.inst().checkAndCostItem(humanObj, confLevel.expend[0], confLevel.expend[1], MoneyItemLogKey.天使皮肤升级);
            if (!rr.success) {
                Inform.sendMsg_error(humanObj, ErrorTip.ItemNotEnough);
                return;
            }
        }

        // 升级皮肤
        ownedSkinsMap.put(skinId, targetLevel);
        godData.setOwnedSkinsMap(ownedSkinsMap);

        // 发送响应
        MsgGod.god_upgrade_skin_s2c.Builder response = MsgGod.god_upgrade_skin_s2c.newBuilder();
        response.setSkinId(skinId);
        response.setLevel(targetLevel);
        humanObj.sendMsg(response.build());
    }

    /**
     * 处理抽卡请求
     */
    public void handleGodDrawC2S(HumanObject humanObj, int drawSn, int num) {
        GodData godData = humanObj.operation.godData;
        if (godData == null) {
            return;
        }

        ConfDrawType confDraw = ConfDrawType.get(drawSn);
        if (confDraw == null) {
            Log.game.error("抽卡配置不存在，drawSn={}", drawSn);
            return;
        }

        // 检查并扣除消耗
        if (confDraw.treasure != null && confDraw.treasure.length >= 2) {
            int needCount = confDraw.treasure[1] * num;
            ReasonResult rr = ProduceManager.inst().checkAndCostItem(humanObj, confDraw.treasure[0], needCount, MoneyItemLogKey.天使抽卡);
            if (!rr.success) {
                Inform.sendMsg_error(humanObj, ErrorTip.ItemNotEnough);
                return;
            }
        }
        Map<Integer,Integer> rewards = new HashMap<>();
        List<Define.p_reward> rewardList = new ArrayList<>();
        if(confDraw.poolProbability == null){
            for (int i = 0; i < num; i++) {
                Map<Integer, Integer> dropMap = ProduceManager.inst().getDropMap(confDraw.prob);
                rewards = Utils.mergeMap(rewards,dropMap);
                rewardList.addAll(ProduceManager.inst().to_p_rewardList(dropMap));
                Log.god.debug("angel draw rewards size={} , index={}", rewards.size(), i+1);
            }

        }else {

            Map<Integer,GodDrawVo> drawDataMap = GodDrawVo.mapFromJsonString(godData.getGod().getDrawInfo());
            GodDrawVo drawVo = drawDataMap.getOrDefault(drawSn, new GodDrawVo());
            for (int i = 0; i < num; i++) {
                Map<Integer, Integer> dropMap = executeSingleDraw(humanObj, confDraw, drawVo);
                rewards = Utils.mergeMap(rewards, dropMap);
                rewardList.addAll(ProduceManager.inst().to_p_rewardList(dropMap));
                Log.god.debug("angel draw rewards size={} , index={}", rewards.size(), i+1);
            }
            drawDataMap.put(drawSn, drawVo);
            godData.getGod().setDrawInfo(GodDrawVo.mapToJsonString(drawDataMap));
        }

        // 增加抽卡经验
        if (confDraw.pt > 0) {
            int currentExp = godData.getGod().getDrawExp();
            godData.getGod().setDrawExp(currentExp + confDraw.pt * num);
        }

        // 发放奖励
        ProduceManager.inst().produceAdd(humanObj, rewards, MoneyItemLogKey.天使抽卡);

        // 发送响应
        MsgGod.god_draw_s2c.Builder response = MsgGod.god_draw_s2c.newBuilder();
        response.setDrawSn(drawSn);
        response.addAllReward(rewardList);
        response.setDrawExp(godData.getGod().getDrawExp());
        humanObj.sendMsg(response.build());
    }

    /**
     * 执行单次抽卡
     */
    private Map<Integer, Integer> executeSingleDraw(HumanObject humanObj, ConfDrawType confDraw, GodDrawVo drawVo) {
        // 如果poolProbability为空，直接掉落prob
        if (confDraw.poolProbability == null || confDraw.poolProbability.length == 0) {
            return ProduceManager.inst().getDropMap(confDraw.prob);
        }
        drawVo.addDrawCount(1);
        // 检查是否触发大奖概率
        boolean hitBigPrize = false;
        int currentDrawCount = drawVo.getDrawCount();

        for (int[] pool : confDraw.poolProbability) {
            if (pool.length >= 2 && currentDrawCount >= pool[0]) {
                int rate = pool[1]; // 万分比
                if (Utils.random(10000) < rate) {
                    hitBigPrize = true;
                    break;
                }
            }
        }

        // 如果没中大奖，走普通掉落
        if (!hitBigPrize) {
            return ProduceManager.inst().getDropMap(confDraw.prob);
        }

        // 中了大奖，重置抽卡次数
        drawVo.resetDrawCount();

        // 增加保底计数
        drawVo.addGuaranteedCount(1);

        // 检查guaranteedprob保底
        if (confDraw.guaranteedprob == null || confDraw.guaranteedprob.length == 0) {
            return ProduceManager.inst().getDropMap(confDraw.prob);
        }


        // 遍历guaranteedprob，找到符合条件的保底组
        List<Integer> dropGroupList = new ArrayList<>();
        for (int i = 0; i < confDraw.guaranteedprob.length; i++) {
            int[] guaranteed = confDraw.guaranteedprob[i];
            int guaranteedCount = drawVo.getGuaranteedCount(i);
            if (guaranteed.length < 3) {
                continue;
            }

            int minCount = guaranteed[0];  // 保底下限
            int maxCount = guaranteed[1];  // 保底上限
            int dropGroup = guaranteed[2]; // 保底掉落组

            // 检查是否在保底范围内
            if(guaranteedCount == maxCount){
                drawVo.resetGuaranteedCount(i);
                return ProduceManager.inst().getDropMap(dropGroup);
            }
            if (guaranteedCount >= minCount && guaranteedCount <= maxCount) {
                dropGroupList.add(dropGroup);
            }
        }
        // 如果有多个保底组，随机选择一个
        if (!dropGroupList.isEmpty()) {
            int randomIndex = Utils.random(dropGroupList.size());
            drawVo.resetGuaranteedCount(dropGroupList.get(randomIndex));
            return ProduceManager.inst().getDropMap(dropGroupList.get(randomIndex));
        }

        // 如果没有匹配的保底组，返回普通掉落
        return ProduceManager.inst().getDropMap(confDraw.prob);
    }

    /**
     * 处理领取积分宝箱请求
     * 一次性领取多个宝箱，直到经验不足为止
     */
    public void handleGodClaimDrawRewardC2S(HumanObject humanObj) {
        GodData godData = humanObj.operation.godData;
        if (godData == null) {
            return;
        }

        God god = godData.getGod();
        int currentRewardSn = god.getDrawRewardSn();
        int currentExp = god.getDrawExp();

        // 检查第一个宝箱是否可以领取
        ConfDrawCumulativeReward firstConf = ConfDrawCumulativeReward.get(currentRewardSn);
        if (firstConf == null) {
            Log.game.error("累计奖励配置不存在，sn={}", currentRewardSn);
            return;
        }

        // 检查经验是否足够领取第一个
        if (currentExp < firstConf.totalPt) {
            Inform.sendMsg_error(humanObj, ErrorTip.SystemDefault);
            return;
        }

        // 用于收集所有奖励
        Map<Integer, Integer> allRewardsMap = new HashMap<>();
        List<Define.p_reward> allRewardsList = new ArrayList<>();
        int claimedCount = 0;

        // 循环领取宝箱，直到经验不足
        while (true) {
            ConfDrawCumulativeReward conf = ConfDrawCumulativeReward.get(currentRewardSn);
            if (conf == null) {
                Log.game.error("累计奖励配置不存在，sn={}", currentRewardSn);
                break;
            }

            // 检查经验是否足够
            if (currentExp < conf.totalPt) {
                break;
            }

            // 扣除经验
            currentExp -= conf.totalPt;
            claimedCount++;

            // 收集奖励到Map中
            if (conf.reward != null) {
                for (int[] rewardItem : conf.reward) {
                    if (rewardItem.length >= 2) {
                        int itemSn = rewardItem[0];
                        int itemNum = rewardItem[1];
                        allRewardsMap.put(itemSn, allRewardsMap.getOrDefault(itemSn, 0) + itemNum);
                    }
                }
            }

            // 随机下一个箱子
            int nextRewardSn = currentRewardSn;
            if (conf.prob != null && conf.prob.length > 0) {
                int totalWeight = 0;
                for (int[] p : conf.prob) {
                    if (p.length >= 2) {
                        totalWeight += p[1];
                    }
                }

                int rand = Utils.random(totalWeight);
                int currentWeight = 0;
                for (int[] p : conf.prob) {
                    if (p.length >= 2) {
                        currentWeight += p[1];
                        if (rand < currentWeight) {
                            nextRewardSn = p[0];
                            break;
                        }
                    }
                }
            }

            currentRewardSn = nextRewardSn;
        }

        // 如果没有领取任何宝箱，直接返回
        if (claimedCount == 0) {
            Inform.sendMsg_error(humanObj, ErrorTip.SystemDefault);
            return;
        }

        // 统一发放所有奖励
        if (!allRewardsMap.isEmpty()) {
            ProduceManager.inst().produceAdd(humanObj, allRewardsMap, MoneyItemLogKey.天使抽卡累计奖励);
            // 显示奖励
            InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, InstanceConstants.showType_0, allRewardsMap);
        }

        // 更新玩家数据
        god.setDrawRewardSn(currentRewardSn);
        god.setDrawExp(currentExp);

        // 发送响应
        MsgGod.god_claim_draw_reward_s2c.Builder response = MsgGod.god_claim_draw_reward_s2c.newBuilder();
        response.setDrawRewardSn(currentRewardSn);
        response.setDrawExp(currentExp);
        response.addAllReward(allRewardsList);
        humanObj.sendMsg(response.build());
    }

    public void giveGod(HumanObject humanObj, List<ProduceVo> voList) {
        GodData godData = humanObj.operation.godData;
        if (godData == null) {
            createGod(humanObj);
            godData = humanObj.operation.godData;
        }

        List<GodVo> newGods = new ArrayList<>();
        List<Integer> godSnList = new ArrayList<>();

        for (ProduceVo vo : voList) {
            ConfGoods confGoods = ConfGoods.get(vo.itemSn);
            if (confGoods == null || confGoods.effect == null || confGoods.effect.length == 0) {
                Log.game.error("===giveGod error, humanId={}, itemSn={}", humanObj.id, vo.itemSn);
                continue;
            }

            int itemNum = vo.num;
            int godSn = confGoods.effect[0][0];

            for (int i = 0; i < itemNum; i++) {
                long godId = humanObj.idGen();
                GodVo godVo = new GodVo(godId, godSn);
                godData.addGodVo(godVo);
                newGods.add(godVo);

                ConfGod confGod = ConfGod.get(godSn);
                if (confGod != null && !godSnList.contains(godSn)) {
                    godSnList.add(godSn);
                }
            }
        }
        godData.saveGodsList();

        // 一次性检查所有新增天使的图鉴和羁绊
        if (!godSnList.isEmpty()) {
            checkAndUpdateCodexAndCamps(humanObj, godSnList);
        }

        if (!newGods.isEmpty()) {
            MsgGod.god_update_s2c.Builder builder = MsgGod.god_update_s2c.newBuilder();
            builder.setUpdateState(EGodType.UPDATE_STATE_ADD);

            for (GodVo godVo : newGods) {
                Define.p_god.Builder pGod = Define.p_god.newBuilder();
                pGod.setId(godVo.getId());
                pGod.setSn(godVo.getSn());
                pGod.setLevel(godVo.getLevel());
                pGod.setExp(godVo.getExp());
                pGod.setIsLocked(godVo.isLocked());
                builder.addGodList(pGod.build());
            }

            humanObj.sendMsg(builder.build());
        }
    }

    /**
     * 检查并更新图鉴和羁绊（合并方法）- 单个godSn
     */
    private void checkAndUpdateCodexAndCamps(HumanObject humanObj, int godSn) {
        List<Integer> godSnList = new ArrayList<>();
        godSnList.add(godSn);
        checkAndUpdateCodexAndCamps(humanObj, godSnList);
    }

    /**
     * 检查并更新图鉴和羁绊（合并方法）- 多个godSn
     */
    private void checkAndUpdateCodexAndCamps(HumanObject humanObj, List<Integer> godSnList) {
        if (godSnList == null || godSnList.isEmpty()) {
            return;
        }

        GodData godData = humanObj.operation.godData;
        if (godData == null) {
            return;
        }

        boolean hasUpdate = false;
        Map<Integer, Integer> oldAvailCodex = new HashMap<>(godData.getAvailCodexMap());
        List<Integer> oldAvailCamps = new ArrayList<>(godData.getAvailCamps());

        Map<Integer, Integer> availCodexMap = godData.getAvailCodexMap();
        Map<Integer, Integer> activeCodexMap = godData.getActiveCodexMap();
        List<Integer> availCamps = godData.getAvailCamps();
        List<Integer> activeCamps = godData.getActiveCamps();

        // 收集所有需要检查的羁绊SN（使用Set去重）
        Set<Integer> campSnSet = new HashSet<>();

        // 遍历所有传入的godSn
        for (int godSn : godSnList) {
            ConfGod confGod = ConfGod.get(godSn);
            if (confGod == null) {
                Log.game.error("===checkAndUpdateCodexAndCamps error, humanId={}, godSn={}", humanObj.id, godSn);
                continue;
            }

            int identification = confGod.identification;
            int star = confGod.star;

            // 检查图鉴 - 只检查当前godSn对应的图鉴
            Integer activeStar = activeCodexMap.get(identification);
            Integer availStar = availCodexMap.get(identification);

            if ((activeStar == null || activeStar < star) &&
                (availStar == null || availStar < star)) {
                availCodexMap.put(identification, star);
                hasUpdate = true;
            }

            // 收集当前godSn相关的羁绊
            List<Integer> campSnList = GlobalConfVal.getConfGodCampSnList(godSn);
            if (campSnList != null && !campSnList.isEmpty()) {
                campSnSet.addAll(campSnList);
            }
        }

        // 检查羁绊 - 检查所有收集到的羁绊
        for (Integer campSn : campSnSet) {
            if (activeCamps.contains(campSn) || availCamps.contains(campSn)) {
                continue;
            }

            ConfGodCamp conf = ConfGodCamp.get(campSn);
            if (conf == null) {
                continue;
            }

            if (checkCampRequirement(humanObj, conf)) {
                availCamps.add(campSn);
                hasUpdate = true;
            }
        }

        // 如果有更新，保存并推送
        if (hasUpdate) {
            godData.setAvailCodexMap(availCodexMap);
            godData.setAvailCamps(availCamps);

            // 推送更新消息
            MsgGod.god_avail_codex_camps_update_s2c.Builder builder = MsgGod.god_avail_codex_camps_update_s2c.newBuilder();

            for (Map.Entry<Integer, Integer> entry : availCodexMap.entrySet()) {
                if (!oldAvailCodex.containsKey(entry.getKey()) ||
                    !oldAvailCodex.get(entry.getKey()).equals(entry.getValue())) {
                    Define.p_key_value.Builder kv = Define.p_key_value.newBuilder();
                    kv.setK(entry.getKey());
                    kv.setV(entry.getValue());
                    builder.addAvailCodex(kv.build());
                }
            }

            for (Integer campSn : availCamps) {
                if (!oldAvailCamps.contains(campSn)) {
                    builder.addAvailCamps(campSn);
                }
            }

            humanObj.sendMsg(builder.build());
        }
    }

    /**
     * 更新天使属性和战力
     */
    public void updateGodPropCalcPower(HumanObject humanObj) {
        GodData godData = humanObj.operation.godData;
        if (godData == null) {
            return;
        }

        // TODO: 实现天使属性计算和战力计算
        // 参考HomeFishManager的updatePorpCalcPower实现
    }

    /**
     * 更新阵容属性和战力
     */
    public void updateFormationPropCalcPower(HumanObject humanObj) {
        GodData godData = humanObj.operation.godData;
        if (godData == null) {
            return;
        }

        // TODO: 实现阵容属性计算和战力计算
        // 参考HomeFishManager的updateEquipPorpCalcPower实现
    }

    /**
     * 检查并解锁槽位
     */
    private void checkAndUnlockPos(HumanObject humanObj) {
        GodData godData = humanObj.operation.godData;
        if (godData == null) {
            return;
        }

        List<Integer> unlockedPos = godData.getUnlockedPos();
        if (unlockedPos.size() >= 5) {
            return; // 所有槽位都已解锁
        }

        Map<Integer, Long> currentFormation = godData.getCurrentFormation();
        boolean hasUpdate = false;

        for (int pos = 0; pos < 5; pos++) {
            if (unlockedPos.contains(pos)) {
                continue;
            }

            ConfGodPosition confPos = ConfGodPosition.get(pos);
            if (confPos == null) {
                continue;
            }

            if (checkPositionUnlock(humanObj, confPos)) {
                unlockedPos.add(pos);
                currentFormation.put(pos, 0L);
                hasUpdate = true;
            }
        }

        if (hasUpdate) {
            godData.setUnlockedPos(unlockedPos);
            godData.saveFormationTab();
        }
    }

    public void delGod(HumanObject humanObj, long godId) {
        //下阵再删
        GodData godData = humanObj.operation.godData;
        if (godData == null) {
            return;
        }
        Map<Integer, Long> currentFormation = godData.getCurrentFormation();
        for (Map.Entry<Integer, Long> entry : currentFormation.entrySet()) {
            if (entry.getValue() == godId) {
                currentFormation.put(entry.getKey(), 0L);
            }
        }
        godData.saveFormationTab();

        godData.removeGodVo(godId);
        MsgGod.god_update_s2c.Builder deleteMsg = MsgGod.god_update_s2c.newBuilder();
        deleteMsg.setUpdateState(EGodType.UPDATE_STATE_DELETE);
        Define.p_god.Builder deletePGod = Define.p_god.newBuilder();
        deletePGod.setId(godId);
        deleteMsg.addGodList(deletePGod.build());
        humanObj.sendMsg(deleteMsg.build());
    }
}
