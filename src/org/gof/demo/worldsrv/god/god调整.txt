1,协议加了一条:
message god_avail_codex_camps_update_s2c {
	option (msgid) = 18203;
	repeated p_key_value avail_codex = 1;	// 可激活/升级的图鉴 k: 天使sn, v: 最大等级
	repeated uint32 avail_camps = 2;		// 可激活羁绊
} 如果新增了可激活的羁绊和图鉴需要推送这个协议

2,修改了数据结构增加方案的支持, 请加unlockedPos存储槽位的解锁是记录,功能解锁把0号加上,布阵请求要判断槽位是不是在unlockedPos里面,上阵完再检测槽位的解锁,解锁了加入unlockedPos,如果都解锁了就不用再检测了,一共5个槽位
如果上阵的在其它槽位需要其它下阵(value=0),需要告诉客户端
不能上阵相同天使ID标识的天使
如果有槽位没全部解锁,天使升级如果在整容中需要检测是否解锁,解锁加入unlockedPos
改了数据结构FormationMap帮我对应的改成formationTab当前阵容配置 Map<方案,Map<位置ID, 天使实例ID>> 加了当前方案currentTab,功能解锁后当前方案是1,目前所有的操作都是针对当前方案的,协议没有变化依然是已经解锁没上阵加入pos,0没解锁不加
帮我支持这一套
3,星级的重置,如果上阵了这个天使需要下阵,重置要删除这个天使并通知客户端god_update_s2c,confGod.resetGoods里面会配一个重置后的天使
4,在升星的时候材料会被重置当时材料重置也会消耗重置的道具,这个需要检测扣除,所有的扣除需要检测所有的材料是不是都够再扣除,否则会导致前面检测扣除了后面检查条件不满足返回了,请避免这个问题,道具的扣除尽量统一扣除
5,checkAndUpdateCodex 和 checkAndUpdateCamps 两个方法合成一个方法,只用推送一条god_avail_codex_camps_update_s2c协议
6,领取积分宝箱要求一次领完一个一个罐子的随,直到经验不足,道具统一给,当前只能领一个
7,属性,和技能没有实现,参考HomeFishManager的updatePorpCalcPower和updateEquipPorpCalcPower 对应更新天使的属性 和天使阵容属性,其中的有些技能需要转属性,在god.txt里面有说明,请帮我补全,并在变更的时候调用他们

