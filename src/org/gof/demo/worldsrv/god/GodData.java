package org.gof.demo.worldsrv.god;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.entity.God;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgGod;

import java.util.*;

/**
 * 天使数据内存缓存类
 * 用于存储从God实体中解析出的数据，避免重复解析JSON和proto
 */
public class GodData {
    private God god;
    
    // 天使实例列表 <实例ID, 天使实例>
    private Map<Long, GodVo> godsMap;
    
    // 当前阵容配置 <方案, <位置ID, 天使实例ID>>
    private Map<Integer, Map<Integer, Long>> formationTab;
    
    // 当前方案
    private int currentTab;
    
    // 已解锁的槽位列表
    private List<Integer> unlockedPos;
    
    // 已激活的图鉴 <天使identification, 星级>
    private Map<Integer, Integer> activeCodexMap;
    
    // 可激活的图鉴 <天使identification, 星级>
    private Map<Integer, Integer> availCodexMap;
    
    // 已激活的羁绊列表
    private List<Integer> activeCamps;
    
    // 可激活的羁绊列表
    private List<Integer> availCamps;
    
    // 已拥有的皮肤 <皮肤SN, 等级>
    private Map<Integer, Integer> ownedSkinsMap;
    
    // 已穿戴的皮肤 <天使实例ID, 皮肤SN>
    private Map<Long, Integer> equippedSkinsMap;
    
    public GodData(God god) {
        this.god = god;
        this.godsMap = new HashMap<>();
        this.formationTab = new HashMap<>();
        this.currentTab = god.getCurrentTab();
        this.unlockedPos = Utils.strToIntList(god.getUnlockedPos());
        this.activeCodexMap = Utils.jsonToMapIntInt(god.getActiveCodexMap());
        this.availCodexMap = Utils.jsonToMapIntInt(god.getAvailCodex());
        this.activeCamps = Utils.strToIntList(god.getActiveCamps());
        this.availCamps = Utils.strToIntList(god.getAvailCamps());
        this.ownedSkinsMap = Utils.jsonToMapIntInt(god.getOwnedSkinsMap());
        this.equippedSkinsMap = Utils.jsonToMapLongInt(god.getEquippedSkinsMap());

        // 解析天使列表
        parseGodsList();
        
        // 解析阵容配置
        parseFormationTab();
    }
    
    /**
     * 从proto数据解析天使列表
     */
    private void parseGodsList() {
        try {
            byte[] data = god.getGodsList();
            if (data == null || data.length == 0) {
                return;
            }
            
            MsgGod.god_update_s2c msg = MsgGod.god_update_s2c.parseFrom(data);
            for (Define.p_god pGod : msg.getGodListList()) {
                GodVo godVo = new GodVo();
                godVo.setId(pGod.getId());
                godVo.setSn(pGod.getSn());
                godVo.setLevel(pGod.getLevel());
                godVo.setExp(pGod.getExp());
                godVo.setLocked(pGod.getIsLocked());
                godsMap.put(godVo.getId(), godVo);
            }
        } catch (Exception e) {
            // 解析失败，使用空列表
        }
    }
    
    /**
     * 保存天使列表到proto数据
     */
    public void saveGodsList() {
        MsgGod.god_update_s2c.Builder builder = MsgGod.god_update_s2c.newBuilder();
        builder.setUpdateState(0);
        
        for (GodVo godVo : godsMap.values()) {
            Define.p_god.Builder pGod = Define.p_god.newBuilder();
            pGod.setId(godVo.getId());
            pGod.setSn(godVo.getSn());
            pGod.setLevel(godVo.getLevel());
            pGod.setExp(godVo.getExp());
            pGod.setIsLocked(godVo.isLocked());
            builder.addGodList(pGod.build());
        }
        
        god.setGodsList(builder.build().toByteArray());
    }
    
    /**
     * 解析阵容配置
     */
    private void parseFormationTab() {
        try {
            String formationTabJson = god.getFormationTab();
            if (formationTabJson == null || formationTabJson.isEmpty() || "{}".equals(formationTabJson)) {
                return;
            }
            
            JSONObject tabObj = JSONObject.parseObject(formationTabJson);
            for (String tabKey : tabObj.keySet()) {
                int tab = Integer.parseInt(tabKey);
                JSONObject posObj = tabObj.getJSONObject(tabKey);
                Map<Integer, Long> posMap = new HashMap<>();
                for (String posKey : posObj.keySet()) {
                    int pos = Integer.parseInt(posKey);
                    long godId = posObj.getLongValue(posKey);
                    posMap.put(pos, godId);
                }
                formationTab.put(tab, posMap);
            }
        } catch (Exception e) {
            // 解析失败，使用空数据
        }
    }

    /**
     * 保存阵容配置
     */
    public void saveFormationTab() {
        try {
            JSONObject tabObj = new JSONObject();
            for (Map.Entry<Integer, Map<Integer, Long>> tabEntry : formationTab.entrySet()) {
                JSONObject posObj = new JSONObject();
                for (Map.Entry<Integer, Long> posEntry : tabEntry.getValue().entrySet()) {
                    posObj.put(String.valueOf(posEntry.getKey()), posEntry.getValue());
                }
                tabObj.put(String.valueOf(tabEntry.getKey()), posObj);
            }
            god.setFormationTab(tabObj.toJSONString());
        } catch (Exception e) {
            // 保存失败
        }
    }

    // Getters and Setters
    public God getGod() {
        return god;
    }

    public Map<Long, GodVo> getGodsMap() {
        return godsMap;
    }

    public Map<Integer, Map<Integer, Long>> getFormationTab() {
        return formationTab;
    }

    public void setFormationTab(Map<Integer, Map<Integer, Long>> formationTab) {
        this.formationTab = formationTab;
        saveFormationTab();
    }

    public int getCurrentTab() {
        return currentTab;
    }

    public void setCurrentTab(int currentTab) {
        this.currentTab = currentTab;
        god.setCurrentTab(currentTab);
    }

    public List<Integer> getUnlockedPos() {
        return unlockedPos;
    }

    public void setUnlockedPos(List<Integer> unlockedPos) {
        this.unlockedPos = unlockedPos;
        god.setUnlockedPos(Utils.toJSONString(unlockedPos));
    }

    /**
     * 获取当前方案的阵容
     */
    public Map<Integer, Long> getCurrentFormation() {
        Map<Integer, Long> formation = formationTab.get(currentTab);
        if (formation == null) {
            formation = new HashMap<>();
            formationTab.put(currentTab, formation);
        }
        return formation;
    }

    public Map<Integer, Integer> getActiveCodexMap() {
        return activeCodexMap;
    }

    public void setActiveCodexMap(Map<Integer, Integer> activeCodexMap) {
        this.activeCodexMap = activeCodexMap;
        god.setActiveCodexMap(Utils.mapIntIntToJSON(activeCodexMap));
    }

    public Map<Integer, Integer> getAvailCodexMap() {
        return availCodexMap;
    }

    public void setAvailCodexMap(Map<Integer, Integer> availCodexMap) {
        this.availCodexMap = availCodexMap;
        god.setAvailCodex(Utils.mapIntIntToJSON(availCodexMap));
    }

    public List<Integer> getActiveCamps() {
        return activeCamps;
    }

    public void setActiveCamps(List<Integer> activeCamps) {
        this.activeCamps = activeCamps;
        god.setActiveCamps(Utils.toJSONString(activeCamps));
    }

    public List<Integer> getAvailCamps() {
        return availCamps;
    }

    public void setAvailCamps(List<Integer> availCamps) {
        this.availCamps = availCamps;
        god.setAvailCamps(Utils.toJSONString(availCamps));
    }

    public Map<Integer, Integer> getOwnedSkinsMap() {
        return ownedSkinsMap;
    }

    public void setOwnedSkinsMap(Map<Integer, Integer> ownedSkinsMap) {
        this.ownedSkinsMap = ownedSkinsMap;
        god.setOwnedSkinsMap(Utils.mapIntIntToJSON(ownedSkinsMap));
    }

    public Map<Long, Integer> getEquippedSkinsMap() {
        return equippedSkinsMap;
    }

    public void setEquippedSkinsMap(Map<Long, Integer> equippedSkinsMap) {
        this.equippedSkinsMap = equippedSkinsMap;
        god.setEquippedSkinsMap(Utils.mapLongIntToJSON(equippedSkinsMap));
    }

    /**
     * 获取天使实例
     */
    public GodVo getGodVo(long godId) {
        return godsMap.get(godId);
    }

    /**
     * 添加天使实例
     */
    public void addGodVo(GodVo godVo) {
        godsMap.put(godVo.getId(), godVo);
        saveGodsList();
    }

    /**
     * 删除天使实例
     */
    public void removeGodVo(long godId) {
        godsMap.remove(godId);
        saveGodsList();
    }


    public Map<Integer, Long> getFormationMap(int currentTab) {
        return formationTab.get(currentTab);
    }
}

