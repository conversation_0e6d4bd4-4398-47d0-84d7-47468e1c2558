package org.gof.demo.worldsrv.god;

import com.alibaba.fastjson.JSONObject;
import org.gof.core.support.Utils;

import java.util.HashMap;
import java.util.Map;

/**
 * 抽卡保底数据
 */
public class GodDrawVo {
    private int drawCount;           // 当前抽卡次数（用于poolProbability保底）
    private Map<Integer, Integer> guaranteedMap; // 保底计数 <大奖index, 计数>

    public GodDrawVo() {
        this.drawCount = 0;
        this.guaranteedMap = new HashMap<>();
    }

    public static Map<Integer, GodDrawVo> mapFromJsonString(String jsonString) {
        JSONObject jsonObject = Utils.toJSONObject(jsonString);
        Map<Integer, GodDrawVo> map = new HashMap<>();
        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
            JSONObject object = (JSONObject) entry.getValue();
            GodDrawVo drawVo = new GodDrawVo();
            drawVo.setDrawCount(object.getIntValue("dc"));
            drawVo.setGuaranteedMap(Utils.jsonToMapIntInt(object.getString("gm")));
            map.put(Integer.parseInt(entry.getKey()), drawVo);
        }
        return map;
    }

    private void setGuaranteedMap(Map<Integer, Integer> gm) {
        this.guaranteedMap = gm;
    }

    public static String mapToJsonString(Map<Integer, GodDrawVo> map) {
        JSONObject jsonObject = new JSONObject();
        for (Map.Entry<Integer, GodDrawVo> entry : map.entrySet()) {
            JSONObject object = new JSONObject();
            object.put("dc", entry.getValue().getDrawCount());
            object.put("gm", Utils.mapIntIntToJSON(entry.getValue().getGuaranteedMap()));
            jsonObject.put(entry.getKey().toString(), object);
        }
        return jsonObject.toJSONString();
    }

    public int getDrawCount() {
        return drawCount;
    }
    
    public void setDrawCount(int drawCount) {
        this.drawCount = drawCount;
    }
    
    public Map<Integer, Integer> getGuaranteedMap() {
        return guaranteedMap;
    }
    

    
    /**
     * 增加抽卡次数
     */
    public void addDrawCount(int count) {
        this.drawCount += count;
    }
    
    /**
     * 重置抽卡次数
     */
    public void resetDrawCount() {
        this.drawCount = 0;
    }

    public void addGuaranteedCount(int i) {
        for (Map.Entry<Integer, Integer> entry : guaranteedMap.entrySet()) {
            entry.setValue(entry.getValue() + i);
        }
    }

    public int getGuaranteedCount(int i) {
        return guaranteedMap.getOrDefault(i, 0);
    }

    public void resetGuaranteedCount(int i) {
        guaranteedMap.put(i, 0);
    }
}

