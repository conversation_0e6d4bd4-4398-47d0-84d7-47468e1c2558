# God系统修改总结

## 已完成的修改

### 1. GodData.java - 完全重写 ✅
- 添加了`formationTab`支持多方案阵容
- 添加了`currentTab`当前方案
- 添加了`unlockedPos`已解锁槽位列表
- 修改了所有相关的getter/setter方法
- 添加了`getCurrentFormation()`获取当前方案阵容
- 添加了`saveFormationTab()`保存阵容配置

### 2. GodManager.java - 添加了辅助方法 ✅
- `checkAndUpdateCodexAndCamps()` - 合并的图鉴和羁绊检查方法，会推送god_avail_codex_camps_update_s2c
- `updateGodPropCalcPower()` - 天使属性计算（待实现）
- `updateFormationPropCalcPower()` - 阵容属性计算（待实现）
- `checkAndUnlockPos()` - 检查并解锁槽位

## 需要修改的方法列表

### 1. createGod() - 初始化方法
```java
public void createGod(HumanObject humanObj) {
    God god = new God();
    god.setId(humanObj.id);
    god.setCurrentTab(1); // 默认方案1
    god.persist();
    
    humanObj.operation.godData = new GodData(god);
    
    // 初始化默认解锁0号槽位
    GodData godData = humanObj.operation.godData;
    List<Integer> unlockedPos = godData.getUnlockedPos();
    unlockedPos.add(0);
    godData.setUnlockedPos(unlockedPos);
    
    // 初始化当前方案的阵容
    Map<Integer, Long> formation = godData.getCurrentFormation();
    formation.put(0, 0L);
    godData.saveFormationTab();
    
    initDrawReward(humanObj);
}
```

### 2. handleGodInfoC2S() - 信息查询
修改点：
- 使用`getCurrentFormation()`获取当前方案阵容
- 只发送`unlockedPos`中的槽位信息

### 3. handleGodSetFormationC2S() - 布阵
修改点：
- 检查槽位是否在`unlockedPos`中
- 不能上阵相同identification的天使
- 如果天使在其他槽位，需要下阵（value=0）并通知客户端
- 上阵后检查是否解锁新槽位，调用`checkAndUnlockPos()`
- 使用`getCurrentFormation()`操作当前方案
- 调用`updateFormationPropCalcPower()`更新阵容属性

### 4. handleGodLevelUpC2S() - 升级
修改点：
- 如果天使在阵容中，升级后调用`checkAndUnlockPos()`
- 调用`updateGodPropCalcPower()`更新天使属性
- 如果在阵容中，调用`updateFormationPropCalcPower()`

### 5. handleGodStarUpC2S() - 升星
修改点：
- 统一检查所有材料（天使+道具）是否足够
- 统一扣除所有材料
- 材料天使重置时需要检查并扣除resetCost
- 调用`checkAndUpdateCodexAndCamps()`替代原来的两个方法
- 调用`updateGodPropCalcPower()`和`updateFormationPropCalcPower()`

### 6. handleGodStarResetC2S() - 星级重置
修改点：
- 如果天使在阵容中，需要下阵
- 删除旧天使，通过god_update_s2c通知（update_state=1）
- 从confGod.resetGoods中获取重置后的天使SN并创建新天使
- 通过god_update_s2c通知新天使（update_state=0）

### 7. handleGodClaimDrawRewardC2S() - 领取积分宝箱
修改点：
- 循环领取直到经验不足
- 统一发放所有道具

### 8. giveGod() - 获得天使
修改点：
- 调用`checkAndUpdateCodexAndCamps()`替代原来的两个方法

## 属性和技能系统（待实现）

### 需要实现的方法：

1. **updateGodPropCalcPower(HumanObject humanObj, long godId)**
   - 计算单个天使的属性
   - 来源：天使基础属性 + 等级属性 + 皮肤属性 + 图鉴属性
   - 技能转属性（参考god.txt）
   - 计算战力

2. **updateFormationPropCalcPower(HumanObject humanObj)**
   - 计算阵容总属性
   - 来源：所有上阵天使属性 + 羁绊属性
   - 计算总战力

### 技能转属性规则（god.txt）：
- 需要查看god.txt中的具体说明
- 某些技能需要转换为属性加成

## 调用时机总结

### 需要调用checkAndUpdateCodexAndCamps()的地方：
- giveGod() - 获得新天使时
- handleGodStarUpC2S() - 升星成功后

### 需要调用updateGodPropCalcPower()的地方：
- handleGodLevelUpC2S() - 升级后
- handleGodStarUpC2S() - 升星后
- handleGodEquipSkinC2S() - 装备/卸下皮肤后
- handleGodUpgradeSkinC2S() - 皮肤升级后
- handleGodActivateCodexC2S() - 激活图鉴后

### 需要调用updateFormationPropCalcPower()的地方：
- handleGodSetFormationC2S() - 布阵后
- handleGodLevelUpC2S() - 升级后（如果在阵容中）
- handleGodStarUpC2S() - 升星后（如果在阵容中）
- handleGodActivateCampC2S() - 激活羁绊后
- checkAndUnlockPos() - 解锁新槽位后

### 需要调用checkAndUnlockPos()的地方：
- handleGodSetFormationC2S() - 上阵后
- handleGodLevelUpC2S() - 升级后（如果在阵容中）

## 注意事项

1. 所有涉及道具扣除的地方，必须先统一检查，再统一扣除
2. 阵容操作都针对当前方案（currentTab）
3. 槽位0-4共5个，功能解锁时0号槽位自动解锁
4. 不能上阵相同identification的天使
5. 星级重置会删除旧天使并创建新天使
6. 图鉴和羁绊更新合并为一个方法，只推送一条协议

