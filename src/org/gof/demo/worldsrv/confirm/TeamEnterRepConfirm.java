package org.gof.demo.worldsrv.confirm;


public class TeamEnterRepConfirm extends ConfirmBase {
    public TeamEnterRepConfirm(long sender, String senderName, int confirmType) {
        super(sender, senderName, confirmType);
        setTimeout(15000);
    }

    public void onAccept(long humanId) {
        confirmEnterRep(humanId);
    }

    public void onReject() {

    }

    public void onTimeout() {

    }

    private void confirmEnterRep(long humanId){
        long leader = getSenderId();
//        TeamManager.inst().doEnterRep(leader, humanId);
    }
}
