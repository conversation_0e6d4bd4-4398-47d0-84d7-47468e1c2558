package org.gof.demo.worldsrv.rank;

import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.interfaces.ISerilizable;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.entity.Artifact;
import org.gof.demo.worldsrv.entity.Human;
import org.gof.demo.worldsrv.entity.Mount;
import org.gof.demo.worldsrv.entity.Wing;
import org.gof.demo.worldsrv.equip.EquipInfo;
import org.gof.demo.worldsrv.equip.EquipManager;
import org.gof.demo.worldsrv.human.HumanManager;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.support.MapUtil;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class HumanRankInfoVo implements ISerilizable {

    // p_rank_info 相关
    public int rank = 0;
    public String name;
    public String guildName;
    public long humanId;
    public long score = 0;
    // p_head 头像相关
    public long avatarSn;
    public long frameId;
    public String headUrl;
    // p_role_figure 相关
    public Map<Long, Long> equipMap = new HashMap<>();
    public int hairFigure = 0;
    public int jobFigure = 0;
    public int mountFigure = 0;
    public int artifactFigure = 0;
    public int gender = 0;
    public Map<Long, Long> skinMap = new HashMap<>();
    public int currnetTitle = 0;
    // 剩下其他的
    public Map<Long, Long> extraMap = new HashMap<>();
    public Map<Long, String> extraStrMap = new HashMap<>();
    public Map<Integer, Long> guildFlagValueMap = new HashMap<>();
    public Map<Integer, String> guildFlagStrMap = new HashMap<>();
    public int serverId;

    public HumanRankInfoVo() {
    }



    public Define.p_rank_info.Builder toRankInfoMsg() {
        Define.p_rank_info.Builder msg = Define.p_rank_info.newBuilder();
        msg.setRank(rank);
        msg.setName(name);
        msg.setGuildName(guildName);
        msg.setBelongId(humanId);
        msg.setScore(score);
        // 组装p_head
        Define.p_head.Builder headMsg = Define.p_head.newBuilder();
        headMsg.setId(avatarSn);
        headMsg.setFrameId(frameId);
        headMsg.setUrl(headUrl);
        msg.setHead(headMsg);
        // 组装 p_figure
        Define.p_role_figure.Builder roleFigureMsg = Define.p_role_figure.newBuilder();
        roleFigureMsg.addAllEquipList(MapUtil.toKeyValueMsg(equipMap));
        roleFigureMsg.setHairFigure(hairFigure);
        roleFigureMsg.setJobFigure(jobFigure);
        roleFigureMsg.setMountFigure(mountFigure);
        roleFigureMsg.setArtifactFigure(artifactFigure);
        roleFigureMsg.setGender(gender);
        roleFigureMsg.addAllSkinList(MapUtil.toKeyValueMsg(skinMap));
        roleFigureMsg.setCurrentTitle(currnetTitle);
        msg.setFigure(roleFigureMsg);
        // 组装 p_key_value
        if (extraMap.size() != 0) {
            msg.addAllExtra(MapUtil.toKeyValueMsg(extraMap));
        }
        // 组装 p_key_string
        if (extraStrMap.size() != 0) {
            msg.addAllExtraStr(MapUtil.toKeyStringMsg(extraStrMap));
        }
        if (guildFlagValueMap.size() != 0) {
            // 组装 p_key_value_string
            for (int key : guildFlagValueMap.keySet()) {
                Define.p_key_value_string.Builder kvsMsg = Define.p_key_value_string.newBuilder();
                kvsMsg.setK(key);
                kvsMsg.setV(guildFlagValueMap.get(key));
                kvsMsg.setS(guildFlagStrMap.getOrDefault(key, ""));
            }
        }
        msg.setServId(Utils.getServerIdTo(serverId));
        return msg;
    }

    @Override
    public void writeTo(OutputStream out) throws IOException {
        out.write(rank);
        out.write(name);
        out.write(guildName);
        out.write(humanId);
        out.write(score);
        out.write(avatarSn);
        out.write(frameId);
        out.write(headUrl);
        out.write(equipMap);
        out.write(hairFigure);
        out.write(jobFigure);
        out.write(mountFigure);
        out.write(artifactFigure);
        out.write(gender);
        out.write(skinMap);
        out.write(currnetTitle);
        out.write(extraMap);
        out.write(extraStrMap);
        out.write(guildFlagValueMap);
        out.write(guildFlagStrMap);
        out.write(serverId);
    }

    @Override
    public void readFrom(InputStream in) throws IOException {
        rank = in.read();
        name = in.read();
        guildName = in.read();
        humanId = in.read();
        score = in.read();
        avatarSn = in.read();
        frameId = in.read();
        headUrl = in.read();
        equipMap = in.read();
        hairFigure = in.read();
        jobFigure = in.read();
        mountFigure = in.read();
        artifactFigure = in.read();
        gender = in.read();
        skinMap = in.read();
        currnetTitle = in.read();
        extraMap = in.read();
        extraStrMap = in.read();
        guildFlagValueMap = in.read();
        guildFlagStrMap = in.read();
        serverId = in.read();
    }
}
