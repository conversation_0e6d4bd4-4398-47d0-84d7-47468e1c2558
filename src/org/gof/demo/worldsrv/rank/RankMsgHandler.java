package org.gof.demo.worldsrv.rank;

import org.gof.core.support.Utils;
import org.gof.core.support.observer.MsgReceiver;
import org.gof.demo.seam.msg.MsgParam;
import org.gof.demo.worldsrv.character.HumanDailyResetInfo;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgRank;
import org.gof.demo.worldsrv.support.enumKey.DailyResetTypeKey;

import java.util.Map;

public class RankMsgHandler {
    /**
     * 请求排行榜数据列表
     * @param request 排行榜请求列表
     */
    @MsgReceiver(MsgRank.rank_data_list_c2s.class)
    public void rank_data_list_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgRank.rank_data_list_c2s msg = param.getMsg();
        RankManager.inst().handleRankDataListC2S(humanObj,msg.getRequestList());
    }

    /**
     * 请求排行榜服务器列表
     * @param type RankType表sn
     */
    @MsgReceiver(MsgRank.rank_serv_list_c2s.class)
    public void rank_serv_list_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgRank.rank_serv_list_c2s msg = param.getMsg();
        RankManager.inst()._msg_rank_serv_list_c2s(humanObj, msg.getType());
    }

    /**
     * 请求跨服排行榜状态
     */
    @MsgReceiver(MsgRank.rank_cross_status_c2s.class)
    public void rank_cross_status_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        RankManager.inst()._msg_rank_cross_status_c2s(humanObj);
    }

    /**
     * 排行榜点赞信息
     */
    @MsgReceiver(MsgRank.rank_like_info_c2s.class)
    public void _msg_rank_like_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgRank.rank_like_info_c2s msg = param.getMsg();
        RankManager.inst()._msg_rank_like_info_c2s(humanObj, msg.getRankSn());
    }

    /**
     * 排行榜点赞
     */
    @MsgReceiver(MsgRank.rank_like_c2s.class)
    public void _msg_rank_like_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgRank.rank_like_c2s msg = param.getMsg();
        RankManager.inst()._msg_rank_like_c2s(humanObj, msg.getRankSn(), msg.getLikeRank());
    }

}
