package org.gof.demo.worldsrv.rank;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.dbsrv.redis.EntityManager;
import org.gof.core.interfaces.ISerilizable;
import org.gof.core.support.ParamKey;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.entity.GuildMember;
import org.gof.demo.worldsrv.entity.Human;
import org.gof.demo.worldsrv.entity.Human2;
import org.gof.demo.worldsrv.entity.HumanBrief;
import org.gof.demo.worldsrv.equip.EquipManager;
import org.gof.demo.worldsrv.human.HumanManager;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.support.Log;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.gof.demo.worldsrv.equip.EquipInfo.face;
import static org.gof.demo.worldsrv.equip.EquipInfo.weapon;

public class RankInfo implements ISerilizable {
    public long humanId = 0;
    public long param = 0;// 参数：战力/伤害
    public int serverId = 0;
    public Define.p_rank_info rank_info;
    public int rank;
    public List<Define.p_key_value> equipList = new ArrayList<>();
    public List<Define.p_key_value> skinList = new ArrayList<>();

    public RankInfo(){
    }

    public RankInfo(HumanObject humanObj, long param){
        this(humanObj.getHuman(), humanObj.getHuman2(), param);
    }

    public RankInfo(long humanId, long param, int serverId){
        this(humanId, param);
        this.serverId = serverId;
    }

    public RankInfo(long humanId, long param2){
        this.humanId = humanId;
        this.param = param2;
        if(humanId > 0) {
            Human human = EntityManager.getEntity(Human.class, humanId);
            Human2 human2 = EntityManager.getEntity(Human2.class, humanId);
            setRankInfo(human, human2, param2);
        }
    }

    public RankInfo(Human human, Human2 human2, long param2){
        setRankInfo(human, human2, param2);
    }

    public void setRankInfo(Human human, Human2 human2, long param) {
        this.param = param;
//        this.human = human;
//        this.human2 = human2;
        this.serverId = human.getServerId();
        humanId = human.getId();
        Define.p_rank_info.Builder dInfo = Define.p_rank_info.newBuilder();
        dInfo.setScore(param);
        dInfo.setName(human.getName());
        dInfo.setServId(Utils.getServerIdTo(human.getServerId()));
        dInfo.setFigure(HumanManager.inst().to_p_role_figure(human, human2));
        Define.p_head.Builder head = Define.p_head.newBuilder();
        head.setId(human.getHeadSn());
        head.setFrameId(human.getCurrentHeadFrameSn());
        head.setUrl("");
        dInfo.setHead(head);
        dInfo.setRank(rank);
        dInfo.setGuildName(human2.getGuildName());
        dInfo.setBelongId(human.getId());

        Map<Integer,Integer> figureMap = Utils.jsonToMapIntInt(human2.getEquipFigureMap());
        //遍历weapon到face的部位不存在或者value=0就去默认取
        for(int i = weapon; i <= face; ++i){
            int sn = figureMap.getOrDefault(i, EquipManager.FIGURE_DEFAULT);
            if(sn > 0){
                equipList.add(HumanManager.inst().to_p_key_value(i,sn).build());
            }
        }
        // 翅膀
        equipList.add(HumanManager.inst().to_p_key_value(HumanManager.wing, human2.getWingUse()).build());
        // 皮肤
        skinList.add(HumanManager.inst().to_p_key_value(2, human2.getCurrentSkin()).build());
        rank_info = dInfo.build();
    }

    public Define.p_rank_info toRankInfo(HumanBrief humanBrief, int rank, int score) {
        return toRankInfo(humanBrief, rank, score, 3);
    }

    public Define.p_rank_info toRankInfo(HumanBrief humanBrief, int rank, int score, int topNum) {
        try{
            int serverId = Utils.getServerIdTo(humanBrief.getServerId());
            if(humanBrief.getLevel() == 1 || humanBrief.getName() == null || humanBrief.getName().isEmpty()){
                Log.temp.error("===玩家等级和名字可能存在问题。 humanId={}, name={}, level={}", humanBrief.getId(), humanBrief.getName(), humanBrief.getLevel());
            }
            Define.p_rank_info.Builder dInfo = Define.p_rank_info.newBuilder();
            dInfo.setScore(score);
            dInfo.setName(humanBrief.getName());
            dInfo.setServId(serverId);
            try{
                if(topNum > 0 && rank <= topNum){
                    Define.p_role_figure.Builder figure = Define.p_role_figure.parseFrom(humanBrief.getRoleFigure()).toBuilder();
                    List<Define.p_key_value> extList = figure.getEquipListList();
                    List<Define.p_key_value> extListNew = new ArrayList<>();
                    Map<Integer, Integer> mapNew = new HashMap<>();
                    mapNew.put(ParamKey.figureEquip_1, 0);
                    mapNew.put(ParamKey.figureEquip_2, 0);
                    mapNew.put(ParamKey.figureEquip_3, 0);
                    mapNew.put(ParamKey.figureEquip_4, 0);
                    mapNew.put(ParamKey.figureEquip_5, 0);
                    for(Define.p_key_value kv : extList){
                        mapNew.put(Utils.intValue(kv.getK()), Utils.intValue(kv.getV()));
                    }
                    figure.clearEquipList();
                    for(Map.Entry<Integer, Integer> entry : mapNew.entrySet()){
                        extListNew.add(HumanManager.inst().to_p_key_value(entry.getKey(), entry.getValue()).build());
                    }
                    figure.addAllEquipList(extListNew);
                    dInfo.setFigure(figure);
                } else {
                    Define.p_role_figure.Builder figure = Define.p_role_figure.newBuilder();
                    figure.addEquipList(HumanManager.inst().to_p_key_value(ParamKey.figureEquip_1, 0));
                    figure.addEquipList(HumanManager.inst().to_p_key_value(ParamKey.figureEquip_2, 0));
                    figure.addEquipList(HumanManager.inst().to_p_key_value(ParamKey.figureEquip_3, 0));
                    figure.addEquipList(HumanManager.inst().to_p_key_value(ParamKey.figureEquip_4, 0));
                    figure.addEquipList(HumanManager.inst().to_p_key_value(ParamKey.figureEquip_5, 0));
                    dInfo.setFigure(figure);
                }
                Define.p_head.Builder dHead = Define.p_head.newBuilder();
                dHead.setId(humanBrief.getHeadSn());
                dHead.setFrameId(humanBrief.getCurrentHeadFrameSn());
                dHead.setUrl("");
                dInfo.setHead(dHead);
            } catch (Exception e) {
                Log.temp.error("to_p_common_role error", e);
            }
            dInfo.setRank(rank);
            dInfo.setGuildName(humanBrief.getGuildName());
            dInfo.setBelongId(humanBrief.getId());
            rank_info = dInfo.build();
            return dInfo.build();
        } catch (Exception e){
            Log.game.error("getRankInfo error", e);
        }
        return rank_info;
    }

    @Override
    public void writeTo(OutputStream out) throws IOException {
        out.write(humanId);
        out.write(param);
        out.write(serverId);
        out.write(rank_info);
//        out.write(human);
//        out.write(human2);
        out.write(equipList);
        out.write(skinList);
        out.write(rank);
    }

    @Override
    public void readFrom(InputStream in) throws IOException {
        humanId = in.read();
        param = in.read();
        serverId = in.read();
        rank_info = in.read();
//        human = in.read();
//        human2 = in.read();
        equipList = in.read();
        skinList = in.read();
        rank = in.read();
    }


    public String toJSON(){
        JSONObject jo = new JSONObject();
        jo.put("humanId", humanId);
        jo.put("param", param);
//        jo.put("rank_info", rank_info);

        jo.put("equipList", to_p_key_value(equipList));
        jo.put("paramInfo", ""); // 这里需要修改为human和human2的相关信息
        jo.put("skinL", to_p_key_value(skinList));
        return jo.toJSONString();
    }

    public JSONArray to_p_key_value(List<Define.p_key_value> list){
        JSONArray ja = new JSONArray();
        for(Define.p_key_value dInfo : list){
            JSONObject jo = new JSONObject();
            jo.put("k", (long)dInfo.getK());
            jo.put("v", (long)dInfo.getV());
            ja.add(jo);
        }
        return ja;
    }

    public Define.p_guild_rank_info to_p_guild_rank_info(int rank, GuildMember member){
        Define.p_guild_rank_info.Builder dInfo = Define.p_guild_rank_info.newBuilder();
        dInfo.setRoleId(member.getHumanId());
        dInfo.setValue(param);
        dInfo.setRank(rank);
        dInfo.setRoleName(member.getName());
        JSONObject roleHeadJsonObj = Utils.toJSONObject(member.getRoleHead());
        Define.p_head.Builder pHeadMsg = Define.p_head.newBuilder();
        pHeadMsg.setId(roleHeadJsonObj.getIntValue("headSn"));
        pHeadMsg.setFrameId(roleHeadJsonObj.getIntValue("frameId"));
        pHeadMsg.setUrl(roleHeadJsonObj.getString("url"));
        dInfo.setRoleHead(pHeadMsg);
//        dInfo.setFigure()
        return dInfo.build();
    }

    public Define.p_guild_rank_info to_p_guild_rank_info(int rank, Human human){
        Define.p_guild_rank_info.Builder dInfo = Define.p_guild_rank_info.newBuilder();
        if(human == null){
            Log.game.error("to_p_guild_rank_info human is null humanId={}", humanId);
            return dInfo.build();
        }
        dInfo.setRoleId(humanId);
        dInfo.setValue(param);
        dInfo.setRank(rank);
        dInfo.setRoleName(human.getName());
        Define.p_head.Builder pHeadMsg = Define.p_head.newBuilder();
        pHeadMsg.setId(human.getHeadSn());
        pHeadMsg.setFrameId(human.getCurrentHeadFrameSn());
        pHeadMsg.setUrl("");
        dInfo.setRoleHead(pHeadMsg);
//        dInfo.setFigure()
        return dInfo.build();
    }
}