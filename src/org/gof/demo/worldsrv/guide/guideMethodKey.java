package org.gof.demo.worldsrv.guide;

/**
 * 玩家指南途径
 * <AUTHOR>
 *
 */
public class guideMethodKey {
	public static final int World_Boss = 1;				//世界boss
	public static final int Competition = 2;			//竞技场
	public static final int Tower = 3;			//爬塔
	public static final int INSTANCE_TYPE_TEAM_REP = 4;				//组队副本
	public static final int Faction_Donate = 5;				//帮派捐献
	public static final int Tech_Donate = 6;				//内政捐献
	public static final int INSTANCE_TYPE_TEAM_FACTION_PROTECT = 7;				//帮派卫士
	public static final int Quest_Faction = 8;				//帮派任务
	public static final int Faction_Salary = 9; //帮会工资
	public static final int Quest_Tumo = 10; //屠魔令任务
	public static final int Quest_Camp = 11;				//势力任务
	public static final int Money_Stree = 12;				//摇钱树
	public static final int Item_Practice = 13;				//修炼丹使用次数
	public static final int Wine = 14;				//喝酒
	public static final int INSTANCE_TYPE_TEAM_DRAGON_3 = 15;				//登风的小把戏
	public static final int INSTANCE_TYPE_TEAM_DRAGON_1 = 16;				//千里追击
	public static final int INSTANCE_TYPE_TEAM_DRAGON_2 = 17;				//智取金同福
	public static final int Question = 18;				//答题
	public static final int PremierTreasure = 19;				//初级酒馆
	public static final int GreatTreasure = 20;				//中级酒馆
	public static final int INSTANCE_TYPE_TEAM_TICKET = 21;				//门票副本
	public static final int Gather = 22;				//净灵


}
