package org.gof.demo.worldsrv.artifact;

import com.alibaba.fastjson.JSONObject;
import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.interfaces.ISerilizable;
import org.gof.core.support.Utils;
import org.gof.demo.battlesrv.support.PropCalc;
import org.gof.demo.worldsrv.msg.Define;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class ArtifactGemVo implements ISerilizable {
    public long id; //宝石ID
    public int quality; //宝石品质
    public int pos; //宝石位置
    public int suit; //宝石套装
    public int lv; //宝石等级
    public int exp; //宝石经验
    public int isRed; //宝石红点
    public int isLock; //宝石锁定
    public PropCalc baseAttr = new PropCalc();//基础属性
    public PropCalc randAttr = new PropCalc();//随机属性

    public static String mapJsonString(Map<Long, ArtifactGemVo> gemMap) {
        JSONObject json = new JSONObject();
        for (Map.Entry<Long, ArtifactGemVo> entry : gemMap.entrySet()) {
            json.put(String.valueOf(entry.getKey()), entry.getValue().toJsonString());
        }
        return json.toJSONString();
    }

    public static Map<Long, ArtifactGemVo> fromMapJsonString(String jsonString) {
        Map<Long, ArtifactGemVo> gemMap = new HashMap<>();
        JSONObject json = Utils.toJSONObject(jsonString);
        for (Map.Entry<String, Object> entry : json.entrySet()) {
            ArtifactGemVo gem = new ArtifactGemVo();
            gem.fromJsonString((String) entry.getValue());
            gemMap.put(Long.valueOf(entry.getKey()), gem);
        }
        return gemMap;
    }

    public String toJsonString() {
        JSONObject json = new JSONObject();
        json.put("id", id);
        json.put("qlt", quality);
        json.put("pos", pos);
        json.put("s", suit);
        json.put("lv", lv);
        json.put("exp", exp);
        json.put("red", isRed);
        json.put("lk", isLock);
        json.put("b", baseAttr.toJSONStr());
        json.put("r", randAttr.toJSONStr());
        return json.toJSONString();
    }

    public ArtifactGemVo fromJsonString(String jsonString) {
        JSONObject json = Utils.toJSONObject(jsonString);
        id = json.getLongValue("id");
        quality = json.getIntValue("qlt");
        pos = json.getIntValue("pos");
        suit = json.getIntValue("s");
        lv = json.getIntValue("lv");
        exp = json.getIntValue("exp");
        isRed = json.getIntValue("red");
        isLock = json.getIntValue("lk");
        baseAttr.plus(json.getString("b"));
        randAttr.plus(json.getString("r"));
        return this;
    }

    public Define.p_artifact_gem.Builder toProtobuf() {
        Define.p_artifact_gem.Builder builder = Define.p_artifact_gem.newBuilder();
        builder.setId(id);
        builder.setQuality(quality);
        builder.setPos(pos);
        builder.setSuit(suit);
        builder.setLv(lv);
        builder.setExp(exp);
        builder.setIsRed(isRed);
        builder.setIsLock(isLock);
        builder.addAllBaseAttr(baseAttr.dPropList());
        builder.addAllRandAttr(randAttr.dPropList());
        return builder;
    }

    @Override
    public void writeTo(OutputStream out) throws IOException {
        out.write(id);
        out.write(quality);
        out.write(pos);
        out.write(suit);
        out.write(lv);
        out.write(exp);
        out.write(isRed);
        out.write(isLock);
        out.write(baseAttr);
        out.write(randAttr);
    }

    @Override
    public void readFrom(InputStream in) throws IOException {
        id = in.read();
        quality = in.read();
        pos = in.read();
        suit = in.read();
        lv = in.read();
        exp = in.read();
        isRed = in.read();
        isLock = in.read();
        baseAttr = in.read();
        randAttr = in.read();
    }
}
