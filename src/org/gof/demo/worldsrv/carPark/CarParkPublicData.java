package org.gof.demo.worldsrv.carPark;

import org.gof.demo.worldsrv.entity.CarParkPublic;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class CarParkPublicData {
    public CarParkPublic carParkPublic; //原始数据
    public List<ParkSpaceVo> parkSpaceList; //车位列表，索引对应车位号

    public CarParkPublicData(CarParkPublic carParkPublic) {
        this.carParkPublic = carParkPublic;
        this.parkSpaceList = new ArrayList<>(Collections.nCopies(CarParkType.PUBLIC_SPACE_NUM + 1, null));

        // 初始化parkSpaceList
        for(int i = 1; i <= CarParkType.PUBLIC_SPACE_NUM; i++) {
            String spaceJson = CarParkManager.inst().getPlace(carParkPublic, i);
            if(spaceJson != null && !spaceJson.equals("{}")) {
                parkSpaceList.set(i, new ParkSpaceVo(spaceJson));
            }
        }
    }
    /**
     * 设置指定车位号的车位信息
     * @param spaceNo 车位号
     * @param parkSpaceVo 车位信息
     */
    public void setSpace(int spaceNo, ParkSpaceVo parkSpaceVo) {
        if (spaceNo <= 0 || spaceNo > CarParkType.PUBLIC_SPACE_NUM) {
            return;
        }
        // 更新缓存数据
        parkSpaceList.set(spaceNo, parkSpaceVo);
        
        // 更新实体数据
        String jsonStr = parkSpaceVo == null ? "{}" : parkSpaceVo.toJsonStr();
        CarParkManager.inst().setPlace(carParkPublic, spaceNo, jsonStr);
    }

    /**
     * 获取指定车位号的车位信息
     * @param spaceNo 车位号
     * @return 车位信息，如果车位号无效则返回null
     */
    public ParkSpaceVo getSpace(int spaceNo) {
        if (spaceNo <= 0 || spaceNo > CarParkType.PUBLIC_SPACE_NUM) {
            return null;
        }
        return parkSpaceList.get(spaceNo);
    }
}
