package org.gof.demo.worldsrv.carPark;

import com.alibaba.fastjson.JSONObject;
import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.interfaces.ISerilizable;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.msg.Define;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class ParkSkinVo implements ISerilizable {
    public int sn; //sn
    public int pos;//位置
    public int x; //x坐标
    public int y; //y坐标

    public ParkSkinVo() {
    }

    public ParkSkinVo(int sn) {
        this.sn = sn;
    }
    public ParkSkinVo(int sn, int pos, int x, int y) {
        this.sn = sn;
        this.pos = pos;
        this.x = x;
        this.y = y;
    }

    public ParkSkinVo(String str) {
        String[] strs = str.split(",");
        sn = Integer.parseInt(strs[0]);
        pos = Integer.parseInt(strs[1]);
        x = Integer.parseInt(strs[2]);
        y = Integer.parseInt(strs[3]);

    }

    public String toStr(){
        return sn + "," + pos + "," + x + "," + y;
    }

    public Define.p_car_park_skin.Builder build(){
        Define.p_car_park_skin.Builder builder = Define.p_car_park_skin.newBuilder();
        builder.setSkinId(sn);
        builder.setPos(pos);
        builder.setX(x);
        builder.setY(y);
        return builder;
    }

    public static Map<Integer,ParkSkinVo> mapFromJsonStr(String jsonStr){
        Map<Integer,ParkSkinVo> map = new HashMap<>();
        JSONObject json = JSONObject.parseObject(jsonStr);
        for (Map.Entry<String, Object> entry : json.entrySet()) {
            ParkSkinVo vo = new ParkSkinVo(entry.getValue().toString());
            map.put(Utils.intValue(entry.getKey()), vo);
        }
        return map;
    }

    public static String mapToJsonStr(Map<Integer,ParkSkinVo> map){
        JSONObject json = new JSONObject();
        for (Map.Entry<Integer, ParkSkinVo> entry : map.entrySet()) {
            json.put(entry.getKey().toString(), entry.getValue().toStr());
        }
        return json.toJSONString();
    }

    @Override
    public void writeTo(OutputStream out) throws IOException {
        out.write(sn);
        out.write(pos);
        out.write(x);
        out.write(y);
    }

    @Override
    public void readFrom(InputStream in) throws IOException {
        sn = in.read();
        pos = in.read();
        x = in.read();
        y = in.read();
    }
}
