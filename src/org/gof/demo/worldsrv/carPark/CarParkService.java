package org.gof.demo.worldsrv.carPark;

import com.alibaba.fastjson.JSONObject;
import io.vertx.core.AsyncResult;
import io.vertx.core.Handler;
import org.gof.core.Port;
import org.gof.core.dbsrv.redis.AsyncActionResult;
import org.gof.core.dbsrv.redis.EntityManager;
import org.gof.core.gen.proxy.DistrClass;
import org.gof.core.gen.proxy.DistrMethod;
import org.gof.core.scheduler.ScheduleMethod;
import org.gof.core.support.*;
import org.gof.demo.worldsrv.character.HumanObjectServiceProxy;
import org.gof.demo.worldsrv.common.DataResetService;
import org.gof.demo.worldsrv.common.GamePort;
import org.gof.demo.worldsrv.common.GameServiceBase;
import org.gof.demo.worldsrv.config.*;
import org.gof.demo.worldsrv.entity.*;
import org.gof.demo.worldsrv.human.*;
import org.gof.demo.worldsrv.item.ItemConstants;
import org.gof.demo.worldsrv.mail.MailManager;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgCarPark;
import org.gof.demo.worldsrv.pocketLine.Pocket;
import org.gof.demo.worldsrv.pocketLine.PocketLineEventSubKey;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.support.C;
import org.gof.demo.worldsrv.support.D;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.Util;
import org.gof.demo.worldsrv.support.enumKey.ConfGlobalKey;

import java.util.*;

import static org.gof.demo.worldsrv.carPark.CarParkType.*;


@DistrClass(servId = D.SERV_CAR_PARK, importClass = {ParkSpaceVo.class, List.class, Define.p_role_change.class, Define.p_role_figure.class, Map.class})
public class CarParkService extends GameServiceBase {

    /**在线和离线缓存玩家停车场**/
    private Map<Long, CarParkData> carParkMap = new HashMap<>();
    /**公共停车场**/
    private Map<Long, CarParkPublicData> publicParkMap = new HashMap<>();
    private List<Integer> serverIdListNow = new ArrayList<>();

    /**离线玩家的最后访问时间 key:humanId, value:lastAccessTime**/
    private LinkedHashMap<Long, Long> offlineAccessTimeMap = new LinkedHashMap<>();

    private static final int MAX_OFFLINE_CACHE = 30; // 最大离线玩家缓存数量
    private static final long CACHE_MIN_TIME = 100000; // 缓存最小时间（毫秒）

    // 新增战斗状态记录
    private Map<String, long[]> combatStateMap = new HashMap<>();  // key: type_pos, value: [humanId, startTime]
    private static final int COMBAT_TIMEOUT = 10; // 战斗超时时间（秒）

    private TickTimer checkServerIdTT = new TickTimer(5 * Time.MIN);//调度的处理

    public CarParkService(GamePort port) {
        super(port);
    }

    @Override
    public void pulseOverride() {
        long nowTime = Port.getTime();
        if(checkServerIdTT.isPeriod(nowTime)){
            checkServerId();
        }
    }

    @DistrMethod
    public void loadServer(List<Integer> serverIds){
        //取serverList里面没有的serverIds的serverId，加到serverList里面
        for (Integer serverId : serverIds) {
            if(!serverIdListNow.contains(serverId)){
                continue;
            }
            serverIdListNow.add(serverId);
            loadPublicCarPark(serverId);
        }
    }

    @Override
    protected void init() {
        checkServerId();
    }

    private void loadPublicCarPark(int serverId){
        List<Long> parkIdList = new ArrayList<>();
        for (int i = 1; i <= PUBLIC_NUM; i++){
            parkIdList.add(CarParkManager.inst().getPublicParkCengIdBy(serverId, i));
        }
        Log.carPark.error("开始加载公共停车场数据：{}", Utils.listToString(parkIdList));
        EntityManager.batchGetEntity(CarParkPublic.class, parkIdList, batchRes -> {
            if(batchRes.failed()){
                Log.game.error("loadPublicCarPark failed!");
                return;
            }
            List<CarParkPublic> parkPublics = batchRes.result();
            if(parkPublics == null){
                Log.carPark.error("加载公共停车场数据是空");
                parkPublics = new ArrayList<>();
            }else {
                Log.carPark.error("加载公共停车场数据成功返回数量:{}",parkPublics.size());
            }

            // 创建Set记录已存在的parkId
            Set<Long> existingParkIds = new HashSet<>();
            for(CarParkPublic carParkPublic : parkPublics) {
                if(carParkPublic != null) {
                    existingParkIds.add(carParkPublic.getId());
                    CarParkPublicData parkPublicData = new CarParkPublicData(carParkPublic);
                    // 使用publicPark.getId()作为key
                    publicParkMap.put(carParkPublic.getId(), parkPublicData);
                    Log.carPark.error("成功加载公共停车场数据: {}", carParkPublic.getId());
                }
            }

            // 对于不存在的parkId创建新实体
            for(long parkId : parkIdList) {
                if(!existingParkIds.contains(parkId)) {
                    CarParkPublic newPark = createPublicPark(parkId);
                    CarParkPublicData parkPublicData = new CarParkPublicData(newPark);
                    // 使用publicPark.getId()作为key
                    publicParkMap.put(parkId, parkPublicData);
                    Log.carPark.error("加载数据失败新建了实体{}", parkId);
                }
            }

            // 处理所有车位
            List<Long> allHumanIds = new ArrayList<>();
            
            for(long parkId : parkIdList) {
                CarParkPublicData parkPublicData = publicParkMap.get(parkId);
                
                for(int j = 1; j <= PUBLIC_SPACE_NUM; j++) {
                    String parkSpaceStr = CarParkManager.inst().getPlace(parkPublicData.carParkPublic, j);
                    if(!Utils.isEmptyJSONString(parkSpaceStr)) {
                        ParkSpaceVo parkSpaceVo = new ParkSpaceVo(parkSpaceStr);
                        allHumanIds.add(parkSpaceVo.humanId);
                        // 使用新的setSpace方法
                        parkPublicData.setSpace(j, parkSpaceVo);
                    }
                }
            }

            // 批量异步获取Human和Human2数据
            HumanData.getList(allHumanIds, HumanManager.humanClasses2, res -> {
                if(res.failed()) {
                    Log.game.error("获取停车场玩家数据失败");
                    return;
                }

                List<HumanData> humanDataList = res.result();
                int humanDataIndex = 0;

                for(long parkId : parkIdList) {
                    CarParkPublicData parkPublicData = publicParkMap.get(parkId);

                    for(int j = 1; j <= PUBLIC_SPACE_NUM; j++) {
                        ParkSpaceVo parkSpaceVo = parkPublicData.getSpace(j);
                        if(parkSpaceVo != null) {
                            HumanData humanData = humanDataList.get(humanDataIndex++);
                            if(humanData != null) {
                                parkSpaceVo.setpRoleChangeAndpRoleFigure(humanData.human, humanData.human2);
                            }
                        }
                    }
                }
            });
        });
    }

    private CarParkPublic createPublicPark(long id) {
        CarParkPublic carParkPublic = new CarParkPublic();
        carParkPublic.setId(id);
        carParkPublic.persist();
        return carParkPublic;
    }

    public void saveAll() {
        // 保存公共停车场数据
        for (CarParkPublicData publicData : publicParkMap.values()) {
            if (publicData != null && publicData.carParkPublic != null) {
                publicData.carParkPublic.update();
            }
        }

        // 保存并清理玩家停车场数据
        Iterator<Map.Entry<Long, CarParkData>> iterator = carParkMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<Long, CarParkData> entry = iterator.next();
            CarParkData parkData = entry.getValue();
            if (parkData != null && parkData.carPark != null) {
                parkData.carPark.update();
            }
            iterator.remove();
        }
        Log.game.error("保存停车场数据完成");
    }

    private void checkServerId(){
        List<Integer> serverList = Util.getServerTagList(Config.SERVER_ID);
        if(!serverList.contains(Config.SERVER_ID)){
            serverList.add(Config.SERVER_ID);
        }
        if(serverList.isEmpty()){
            Log.temp.error("===没有获取到serverId列表，请检查配置,{}", C.GAME_SERVER_ID);
            return;
        }
        for(Integer serverId : serverList){
            if(!serverIdListNow.contains(serverId)){
                serverIdListNow.add(serverId);
                loadPublicCarPark(serverId);
            }
        }
    }

    @DistrMethod
    public void onHumanLogin(long humanId){
        CarParkData data = carParkMap.get(humanId);
        if (data != null) {
            data.isOnline = true;
            data.lastAccessTime = Port.getTime();
            // 从离线缓存中移除
            offlineAccessTimeMap.remove(humanId);
            
            // 检查是否需要重置
            if (!Utils.isToday(data.carPark.getDailyResetTime())) {
                resetMountParkTime(data.carPark, Port.getTime());
            }
            port.returns("income",data.resetIncome());
            return;
        }
        long pid = Port.getCurrent().createReturnAsync();
        EntityManager.getEntityAsync(CarPark.class, humanId, res -> {
            if(res.failed()) {
                Log.game.error("onHumanLogin:获取CarPark数据失败, humanId={}", humanId);
                port.returnsAsync(pid, "income",0);
                return;
            }
            CarPark carPark = res.result();
            if(carPark == null) {
                port.returnsAsync(pid, "income",0);
                return;
            }

            // 检查是否需要重置
            if (!Utils.isToday(carPark.getDailyResetTime())) {
                resetMountParkTime(carPark, Port.getTime());
            }

            // 创建并加入缓存
            CarParkData carParkData = new CarParkData(carPark,true);

            // 处理所有车位
            List<Long> allHumanIds = new ArrayList<>();
            for(int i = 1; i <= HUMAN_SPACE_NUM; i++) {
                String parkSpaceStr = CarParkManager.inst().getHumanPlace(carPark, i);
                if(!Utils.isEmptyJSONString(parkSpaceStr)) {
                    ParkSpaceVo parkSpaceVo = new ParkSpaceVo(parkSpaceStr);
                    allHumanIds.add(parkSpaceVo.humanId);
                    carParkData.parkSpaceList.set(i, parkSpaceVo);
                }
            }

            // 如果有车位数据，批量获取Human和Human2数据
            if(!allHumanIds.isEmpty()) {
                HumanData.getList(allHumanIds, new Class[]{Human.class, Human2.class}, humanRes -> {
                    if(humanRes.failed()) {
                        Log.game.error("获取停车场玩家数据失败");
                        return;
                    }

                    List<HumanData> humanDataList = humanRes.result();
                    int humanDataIndex = 0;

                    for(int i = 1; i <= HUMAN_SPACE_NUM; i++) {
                        ParkSpaceVo parkSpaceVo = carParkData.parkSpaceList.get(i);
                        if(parkSpaceVo != null) {
                            HumanData humanData = humanDataList.get(humanDataIndex++);
                            if(humanData != null) {
                                parkSpaceVo.setpRoleChangeAndpRoleFigure(humanData.human, humanData.human2);
                            }
                        }
                    }
                    // 安全地放入carParkMap
                    CarParkData finalCarParkData = safelyPutCarParkData(humanId, carParkData);
                    finalCarParkData.isOnline = true;
                    finalCarParkData.lastAccessTime = Port.getTime();
                    // 从离线缓存中移除
                    offlineAccessTimeMap.remove(humanId);
                    port.returnsAsync(pid, "income",finalCarParkData.resetIncome());
                });
            }else {
                CarParkData finalCarParkData = safelyPutCarParkData(humanId, carParkData);
                finalCarParkData.isOnline = true;
                finalCarParkData.lastAccessTime = Port.getTime();
                // 从离线缓存中移除
                offlineAccessTimeMap.remove(humanId);
                port.returnsAsync(pid, "income",finalCarParkData.resetIncome());
            }
        });
    }

    private void giveIncome(CarParkData parkData, ParkSpaceVo parkSpaceVo){
        long humanId = parkData.carPark.getId();
        if(humanId == parkSpaceVo.humanId){
            return;
        }
        int income = (int)(parkSpaceVo.carCoin * 0.05);
        if(income <= 0){
            return;
        }
        if(!parkData.isOnline){
            parkData.addIncome(income);
            return;
        }

        HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance();
        proxy.getInfo(parkData.carPark.getId());
        proxy.listenResult(this::_result_giveIncome,
                "humanId", parkData.carPark.getId(),
                "income", income);
    }

    private void _result_giveIncome(Param results, Param context){
        long humanId = context.getLong("humanId");
        int income = context.getInt("income");
        HumanGlobalInfo humanInfo = results.get();
        if (humanInfo != null) {
            // 玩家在线,通过HumanObjectServiceProxy发送
            HumanObjectServiceProxy humanPrx = HumanObjectServiceProxy
                    .newInstance(humanInfo.nodeId, humanInfo.portId, humanInfo.id);
            humanPrx.addCarParkIncome(income);
        } else {
            Log.carPark.error("放停车奖励停车收入玩家离线humanId={},income", humanId, income);
        }
    }

    @DistrMethod
    public void onHumanLogout(long humanId){
        CarParkData data = carParkMap.get(humanId);
        if (data != null) {
            data.isOnline = false;
            data.lastAccessTime = Port.getTime();
            addToOfflineCache(humanId, data.lastAccessTime);
        }
    }

    @DistrMethod
    public void onCarParkFix(long humanId, int type, Param param){
        CarParkData data = carParkMap.get(humanId);
        if(data != null){
            CarPark carPark = data.carPark;
            if(type == FixVersionType.CAR_PARK_210_SKIN) {
                Map<Integer,Integer> skinLvMap = Utils.jsonToMapIntInt(carPark.getSkinLvMap());
                port.returns("skinLvMap",skinLvMap);
            }
            return;
        }

        long pid = Port.getCurrent().createReturnAsync();
        EntityManager.getEntityAsync(CarPark.class, humanId, res -> {
            if(res.failed()) {
                Log.game.error("onHumanLogin:获取CarPark数据失败, humanId={}", humanId);
                return;
            }
            CarPark carPark = res.result();
            if(carPark == null) {
                return;
            }
            // 创建并加入缓存
            CarParkData carParkData = new CarParkData(carPark,true);
            carParkMap.put(humanId, carParkData);

            // 处理所有车位
            List<Long> allHumanIds = new ArrayList<>();
            for(int i = 1; i <= HUMAN_SPACE_NUM; i++) {
                String parkSpaceStr = CarParkManager.inst().getHumanPlace(carPark, i);
                if(!Utils.isEmptyJSONString(parkSpaceStr)) {
                    ParkSpaceVo parkSpaceVo = new ParkSpaceVo(parkSpaceStr);
                    allHumanIds.add(parkSpaceVo.humanId);
                    carParkData.parkSpaceList.set(i, parkSpaceVo);
                }
            }

            // 如果有车位数据，批量获取Human和Human2数据
            if(!allHumanIds.isEmpty()) {
                HumanData.getList(allHumanIds, new Class[]{Human.class, Human2.class}, humanRes -> {
                    if(humanRes.failed()) {
                        Log.game.error("获取停车场玩家数据失败");
                        return;
                    }

                    List<HumanData> humanDataList = humanRes.result();
                    int humanDataIndex = 0;

                    for(int i = 1; i <= HUMAN_SPACE_NUM; i++) {
                        ParkSpaceVo parkSpaceVo = carParkData.parkSpaceList.get(i);
                        if(parkSpaceVo != null) {
                            HumanData humanData = humanDataList.get(humanDataIndex++);
                            if(humanData != null) {
                                parkSpaceVo.setpRoleChangeAndpRoleFigure(humanData.human, humanData.human2);
                            }
                        }
                    }
                });
            }

            if(type == FixVersionType.CAR_PARK_210_SKIN) {
                Map<Integer,Integer> skinLvMap = Utils.jsonToMapIntInt(carPark.getSkinLvMap());
                port.returnsAsync(pid, "skinLvMap",skinLvMap);
            }
        });
    }

    @DistrMethod
    public void backMethod1(String param) {

    }

    @DistrMethod
    public void backMethod2(String param) {

    }

    @DistrMethod
    public void getPublicCarParkInfo(long humanId, int serverId, int ceng) {
        // 修改为使用parkId
        long parkId = CarParkManager.inst().getPublicParkCengIdBy(serverId, ceng);
        CarParkPublicData publicData = publicParkMap.get(parkId);
        if(publicData != null) {
            // 构建消息并返回
            MsgCarPark.car_park_info_s2c.Builder msg = buildPublicCarParkMsg(humanId, publicData, ceng);
            port.returnsImmutable(msg);
            return;
        }
        port.returnsImmutable();
    }

    @DistrMethod
    public void getHumanCarParkInfo(long humanId, long masterId, boolean isCollected) {
        CarParkData data = carParkMap.get(masterId);
        if (data != null) {
            data.lastAccessTime = Port.getTime();
            if (!data.isOnline) {
                addToOfflineCache(masterId, data.lastAccessTime);
            }
            long pid = Port.getCurrent().createReturnAsync();
            buildHumanCarParkMsg(humanId, data, isCollected, res -> {
                if(res.failed()) {
                    port.returnsImmutableAsync(pid);
                    return;
                }
                MsgCarPark.car_park_info_s2c.Builder msgBuilder = res.result();
                port.returnsImmutableAsync(pid, msgBuilder);
            });
            return;
        }

        long pid = Port.getCurrent().createReturnAsync();
        // 从数据库异步获取
        getCarParkDataList(Collections.singletonList(masterId), res -> {
            if(res.failed()) {
                Log.carPark.error("getHumanCarParkInfo failed, masterId={}", masterId);
                port.returnsImmutableAsync(pid);
                return;
            }

            List<CarParkData> carParks = res.result();
            if(carParks.isEmpty() || carParks.get(0) == null) {
                port.returnsImmutableAsync(pid);
                return;
            }

            CarParkData parkData = carParks.get(0);
            CarParkData finalCarParkData = safelyPutCarParkData(masterId, parkData);
            // 构建返回消息
            buildHumanCarParkMsg(humanId, finalCarParkData, isCollected, msgRes -> {
                if(msgRes.failed()) {
                    port.returnsImmutableAsync(pid);
                    return;
                }
                port.returnsImmutableAsync(pid, msgRes.result());
            });
        });
    }

    /**
     * 批量异步获取CarParkData
     * @param masterIdList 要获取的玩家ID列表
     * @param handler 异步回调处理器
     */
    public void getCarParkDataList(List<Long> masterIdList, Handler<AsyncResult<List<CarParkData>>> handler) {
        Port port = Port.getCurrent();
        List<CarParkData> resultList = new ArrayList<>();
        List<Long> needQueryIds = new ArrayList<>();

        // 先从缓存中获取
        for(Long masterId : masterIdList) {
            CarParkData data = carParkMap.get(masterId);
            if(data != null) {
                // 更新访问时间
                data.lastAccessTime = Port.getTime();
                if(!data.isOnline) {
                    addToOfflineCache(masterId, data.lastAccessTime);
                }
                resultList.add(data);
            } else {
                needQueryIds.add(masterId);
            }
        }

        // 如果都在缓存中找到了,直接返回
        if(needQueryIds.isEmpty()) {
            AsyncActionResult.success(port, handler, resultList);
            return;
        }

        // 批量从数据库获取
        EntityManager.batchGetEntity(CarPark.class, needQueryIds, res -> {
            if(res.failed()) {
                Log.carPark.error("getCarParkDataList failed, ids={}", needQueryIds);
                AsyncActionResult.fail(port, handler, res.cause());
                return;
            }

            List<CarPark> carParks = res.result();
            if(carParks == null || carParks.isEmpty()) {
                // 合并结果并返回
                AsyncActionResult.success(port, handler, resultList);
                return;
            }

            // 收集所有需要查询的玩家ID
            List<Long> allHumanIds = new ArrayList<>();
            Map<Long, CarParkData> tempDataMap = new HashMap<>();

            for(CarPark carPark : carParks) {
                if(carPark == null) continue;

                // 创建CarParkData
                CarParkData parkData = new CarParkData(carPark, false);
                tempDataMap.put(carPark.getId(), parkData);

                // 处理所有车位
                for(int i = 1; i <= HUMAN_SPACE_NUM; i++) {
                    String parkSpaceStr = CarParkManager.inst().getHumanPlace(carPark, i);
                    if(!Utils.isEmptyJSONString(parkSpaceStr)) {
                        ParkSpaceVo parkSpaceVo = new ParkSpaceVo(parkSpaceStr);
                        allHumanIds.add(parkSpaceVo.humanId);
                        parkData.parkSpaceList.set(i, parkSpaceVo);
                    }
                }
            }

            // 如果没有需要查询的玩家信息
            if(allHumanIds.isEmpty()) {
                // 将临时map中的数据添加到结果列表
                for(Long masterId : needQueryIds) {
                    CarParkData parkData = tempDataMap.get(masterId);
                    if(parkData != null) {
                        resultList.add(parkData);
                        carParkMap.put(masterId, parkData);
                        if(!parkData.isOnline) {
                            addToOfflineCache(masterId, parkData.lastAccessTime);
                        }
                    }
                }
                AsyncActionResult.success(port, handler, resultList);
                return;
            }

            // 批量获取玩家信息
            HumanData.getList(allHumanIds, HumanManager.humanClasses2, humanRes -> {
                if(humanRes.failed()) {
                    Log.carPark.error("getCarParkDataList: get humans failed");
                    AsyncActionResult.fail(port, handler, humanRes.cause());
                    return;
                }

                List<HumanData> humanDataList = humanRes.result();
                Map<Long, HumanData> humanDataMap = new HashMap<>();
                for(HumanData humanData : humanDataList) {
                    if(humanData != null) {
                        humanDataMap.put(humanData.human.getId(), humanData);
                    }
                }

                // 更新车位玩家信息
                for(CarParkData parkData : tempDataMap.values()) {
                    for(int i = 1; i <= HUMAN_SPACE_NUM; i++) {
                        ParkSpaceVo parkSpaceVo = parkData.parkSpaceList.get(i);
                        if(parkSpaceVo != null) {
                            HumanData humanData = humanDataMap.get(parkSpaceVo.humanId);
                            if(humanData != null) {
                                parkSpaceVo.setpRoleChangeAndpRoleFigure(humanData.human, humanData.human2);
                            }
                        }
                    }
                }

                // 将临时map中的数据添加到结果列表和缓存
                for(Long masterId : needQueryIds) {
                    CarParkData parkData = tempDataMap.get(masterId);
                    if(parkData != null) {
                        resultList.add(parkData);
                        if(!parkData.isOnline) {
                            addToOfflineCache(masterId, parkData.lastAccessTime);
                        }
                    }
                }

                // 返回最终结果
                AsyncActionResult.success(port, handler, resultList);
            });
        });
    }

    /**
     * 构建公共停车场消息
     */
    private MsgCarPark.car_park_info_s2c.Builder buildPublicCarParkMsg(long humanId, CarParkPublicData publicData, int ceng) {
        MsgCarPark.car_park_info_s2c.Builder msg = MsgCarPark.car_park_info_s2c.newBuilder();
        msg.setType(PUBLIC_SPOT);
        msg.setMasterId(publicData.carParkPublic.getId());
        msg.setCeng(ceng);
        msg.setShowCar(0);
        msg.setSpaceNum(PUBLIC_SPACE_NUM);
        msg.setName("");
        msg.setInfoList(Define.p_role_change.newBuilder().build());
        msg.setProtect(Define.p_car_park_protect.newBuilder()
                .setIsOpen(0)
                .setProtectType(0)
                .setProtectRatio(0)
                .build());
        addExtInfo(msg, false, humanId);

        // 添加车位信息
        for(int i = 1; i <= PUBLIC_SPACE_NUM; i++) {
            ParkSpaceVo parkSpaceVo = publicData.getSpace(i);
            if(parkSpaceVo != null && parkSpaceVo.humanId != 0) {
                // 重新计算奖励
                int oldCarCoin = parkSpaceVo.carCoin;
                CarParkManager.inst().reCaculateCarReward(CarParkManager.inst().getAttrPublicCarPark(), parkSpaceVo);

                // 如果奖励有变化,更新数据
                if(oldCarCoin != parkSpaceVo.carCoin) {
                    publicData.setSpace(i, parkSpaceVo);
                }

                // 检查是否超时
                if((Port.getTime()/ Time.SEC - parkSpaceVo.startTime) >= ConfGlobal.get(ConfGlobalKey.park_auto_time).value*60*60) {
                    Define.p_car_park_record record = buildCarParkRecordAutoGetReward(publicData.carParkPublic.getId(), PUBLIC_SPOT, parkSpaceVo, null);
                    // 发放奖励
                    handleMountVoAndReceiveReward(parkSpaceVo, record, 1.0f);
                    publicData.setSpace(i,null);
                } else {
                    // 添加这里：从pRoleChange中获取玩家名字
                    String masterName = "";
                    if(parkSpaceVo.pRoleChange != null && parkSpaceVo.pRoleChange.getKsCount() > 0) {
                        masterName = parkSpaceVo.pRoleChange.getKs(0).getS();
                    }
                    Define.p_car_park_space.Builder spaceBuilder = CarParkManager.inst().to_p_car_park_space(i, parkSpaceVo);
                    spaceBuilder.setCarMasterName(masterName);
                    msg.addSpaceList(spaceBuilder);
                }
            }
        }

        return msg;
    }

    /**
     * 构建玩家停车场消息
     */
    private void buildHumanCarParkMsg(long humanId, CarParkData parkData, boolean isCollected, Handler<AsyncResult<MsgCarPark.car_park_info_s2c.Builder>> handler) {
        // 创建消息构建器
        MsgCarPark.car_park_info_s2c.Builder msg = MsgCarPark.car_park_info_s2c.newBuilder();
        msg.setType(HUMAM_SPOT);
        msg.setMasterId(parkData.carPark.getId());
        msg.setCeng(0);
        msg.setShowCar(parkData.carPark.getShowCar());
        msg.setSpaceNum(HUMAN_SPACE_NUM);
        msg.setName(parkData.carPark.getName());

        addExtInfo(msg, isCollected, humanId);

        parkData.getHumanBaseInfo(humanRes->{
            if(humanRes.failed()) {
                return;
            }
            Define.p_role_change.Builder infoBuilder = humanRes.result();
            msg.setInfoList(infoBuilder);


            // 添加车位信息
            for(int i = 1; i <= HUMAN_SPACE_NUM; i++) {
                ParkSpaceVo parkSpaceVo = parkData.parkSpaceList.get(i);
                if(parkSpaceVo != null && parkSpaceVo.humanId != 0) {
                    // 重新计算奖励
                    int oldCarCoin = parkSpaceVo.carCoin;
                    CarParkManager.inst().reCaculateCarReward(
                            CarParkManager.inst().getAttrHumanCarPark(parkData.carPark),
                            parkSpaceVo);

                    if(oldCarCoin != parkSpaceVo.carCoin) {
                        parkData.setSpace(i, parkSpaceVo);
                    }

                    // 检查是否超时
                    if((Port.getTime()/Time.SEC - parkSpaceVo.startTime) >=
                            ConfGlobal.get(ConfGlobalKey.park_auto_time).value*60*60) {
                        Define.p_car_park_record record = buildCarParkRecordAutoGetReward(parkData.carPark.getId(),HUMAM_SPOT, parkSpaceVo, infoBuilder.build());
                        handleMountVoAndReceiveReward(parkSpaceVo, record, 1.0f, false);
                        Log.carPark.info("玩家{}自动收车，清除了坐骑{}信息",parkSpaceVo.humanId,parkSpaceVo.mountId);
                        giveIncome(parkData, parkSpaceVo);
                        parkData.setSpace(i, null);
                    } else {
                        // 修改这里：从pRoleChange中获取玩家名字
                        String masterName = "";
                        if(parkSpaceVo.pRoleChange != null && parkSpaceVo.pRoleChange.getKsCount() > 0) {
                            masterName = parkSpaceVo.pRoleChange.getKs(0).getS();
                        }
                        Define.p_car_park_space.Builder spaceBuilder = CarParkManager.inst().to_p_car_park_space(i, parkSpaceVo);
                        spaceBuilder.setCarMasterName(masterName);
                        msg.addSpaceList(spaceBuilder);
                    }
                }
            }

            // 添加皮肤列表
            Map<Integer, Integer> skinLvMap = Utils.jsonToMapIntInt(parkData.carPark.getSkinLvMap());
            Map<Integer,Integer> skinTypeSnMap = Utils.jsonToMapIntInt(parkData.carPark.getSkinTypeSnMap());
            Map<Integer,ParkSkinVo> skinDecSnVoMap = ParkSkinVo.mapFromJsonStr(parkData.carPark.getSkinDecSnVoMap());

            for(Map.Entry<Integer, Integer> entry : skinLvMap.entrySet()){
                ParkSkinVo vo = new ParkSkinVo();
                vo.sn = entry.getKey();
                ConfParkingDesign_0 conf = ConfParkingDesign_0.get(entry.getKey(), entry.getValue());
                if(conf == null){
                    Log.carPark.error("sendHumanCarParkInfo:找不到皮肤配置sn={},lv={}", entry.getKey(), entry.getValue());
                    continue;
                }
                if(conf.position == TYPE_DEC){
                    vo = skinDecSnVoMap.getOrDefault(entry.getKey(),new ParkSkinVo(entry.getKey()));
                }else {
                    int typeSn = skinTypeSnMap.getOrDefault(conf.position, 0);
                    if(typeSn == entry.getKey()){
                        vo.pos = conf.position;
                    }
                }
                Define.p_car_park_skin.Builder skin = vo.build();
                skin.setSkinLev(entry.getValue());
                msg.addSkinList(skin);
            }

            // 添加保护信息
            Define.p_car_park_protect.Builder protect = Define.p_car_park_protect.newBuilder();
            protect.setIsOpen(parkData.carPark.getIsProtect());
            protect.setProtectType(parkData.carPark.getProtectType());
            protect.setProtectRatio(parkData.carPark.getProtectRatio());
            msg.setProtect(protect);

            // 返回构建好的消息
            AsyncActionResult.success(Port.getCurrent(), handler, msg);
        });
    }

    private void addExtInfo(MsgCarPark.car_park_info_s2c.Builder msg, boolean isCollected, long humanId) {
        CarParkData carParkData = carParkMap.get(humanId);
        if(carParkData == null || carParkData.carPark == null) {
            Log.carPark.error("addExtInfo:找不到停车场数据, humanId={}", humanId);
            return;
        }
        CarPark carPark = carParkData.carPark;
        // K 2: ParkPvpDebuff num
        List<Integer> atkbuff = CarParkManager.inst().getReCaculateBuff(carPark, BUFF_ATTACK, false);
        int buffNum = atkbuff.get(0);
        msg.addExt(Define.p_key_value.newBuilder()
                .setK(CarParkType.EXT_PVP_DEBUFF_SN)
                .setV(buffNum)
                .build());

        // K 1: 是否收藏标志
        msg.addExt(Define.p_key_value.newBuilder()
                .setK(CarParkType.EXT_IS_COLLECTED)
                .setV(isCollected ? 1 : 0)
                .build());

        if(buffNum > 0){
            // K 3: Debuff是否开启标志
            msg.addExt(Define.p_key_value.newBuilder()
                    .setK(CarParkType.EXT_PVP_DEBUFF_SUCC_TIME)
                    .setV(atkbuff.get(1))
                    .build());
        }else {
            // K 4: Debuff减益时间
            msg.addExt(Define.p_key_value.newBuilder()
                    .setK(CarParkType.EXT_PVP_DEBUFF_FAIL_TIME)
                    .setV(atkbuff.get(1))
                    .build());
        }


    }

    /**
     * 添加到离线缓存
     * @param humanId 玩家ID
     * @param accessTime 访问时间
     */
    private void addToOfflineCache(long humanId, long accessTime) {
        // 先移除旧的记录（如果存在）
        offlineAccessTimeMap.remove(humanId);

        // 添加新记录
        offlineAccessTimeMap.put(humanId, accessTime);

        // 如果超出最大缓存数量，需要清理,小心死循环
        while (offlineAccessTimeMap.size() > MAX_OFFLINE_CACHE) {
            // 获取当前时间
            long now = Port.getTime();

            // 获取最早的记录
            Map.Entry<Long, Long> firstEntry = offlineAccessTimeMap.entrySet().iterator().next();

            // 如果最早的记录未达到最小缓存时间，直接返回
            if (now - firstEntry.getValue() < CACHE_MIN_TIME) {
                return;
            }

            // 已经超过最小缓存时间，移除这条记录
            Long humanIdToRemove = firstEntry.getKey();
            CarParkData data = carParkMap.get(humanIdToRemove);
            if(data != null && data.carPark != null){
                data.carPark.update();
            }
            offlineAccessTimeMap.remove(humanIdToRemove);
            carParkMap.remove(humanIdToRemove);

            Log.carPark.debug("从离线缓存中移除玩家数据：humanId={}, cacheTime={}ms",
                    humanIdToRemove, (now - firstEntry.getValue()));
        }
    }

    @DistrMethod
    public void getCarParkCarInfo(long humanId) {
        CarParkData data = carParkMap.get(humanId);
        if (data != null) {
            long pid = Port.getCurrent().createReturnAsync();
            buildCarParkCarMsg(data, res -> {
                if(res.failed()) {
                    port.returnsAsync(pid);
                    return;
                }
                port.returnsImmutableAsync(pid, res.result());
            });
        } else {
            port.returnsImmutable();
        }
    }

    private void buildCarParkCarMsg(CarParkData parkData, Handler<AsyncResult<MsgCarPark.car_park_car_info_s2c.Builder>> handler) {
        if (parkData.carPark == null) {
            AsyncActionResult.success(Port.getCurrent(), handler, null);
            return;
        }

        MsgCarPark.car_park_car_info_s2c.Builder msg = MsgCarPark.car_park_car_info_s2c.newBuilder();
        Map<Integer,MountVo> parkSpaceMap = MountVo.mapFromJsonStr(parkData.carPark.getMountMap());
        List<Long> humanParkIds = new ArrayList<>();

        // 先处理非人类停车场的数据
        for (Map.Entry<Integer, MountVo> entry : parkSpaceMap.entrySet()) {
            MountVo vo = entry.getValue();
            if(vo.parkType == PUBLIC_SPOT){
                // 直接使用vo.masterId作为key
                CarParkPublicData publicData = publicParkMap.get(vo.masterId);
                if(publicData == null){
                    Log.carPark.error("找不到公共停车场数据，masterId={}，humanId={}", vo.masterId, parkData.carPark.getId());
                    continue;
                }
                int pos = (vo.pos-1) % PUBLIC_SPACE_NUM+1;
                ParkSpaceVo parkSpaceVo = publicData.getSpace(pos);
                if(parkSpaceVo != null && parkSpaceVo.humanId == parkData.carPark.getId()){
                    int oldCarCoin = parkSpaceVo.carCoin;
                    CarParkManager.inst().reCaculateCarReward(CarParkManager.inst().getAttrPublicCarPark(), parkSpaceVo);

                    // 如果奖励有变化,更新数据
                    if(oldCarCoin != parkSpaceVo.carCoin) {
                        publicData.setSpace(pos, parkSpaceVo);
                    }
                    // 检查是否超时
                    if((Port.getTime()/Time.SEC - parkSpaceVo.startTime) >= ConfGlobal.get(ConfGlobalKey.park_auto_time).value*60*60) {
                        // 发放奖励
                        boolean isUpgrade = processRewardedMountVo(entry.getKey(), entry.getValue(), parkSpaceVo.carExp, parkSpaceVo.lastGetTime, parkSpaceVo.mountPlacingTime);
                        Define.p_car_park_record p_record = buildCarParkRecordAutoGetReward(vo.masterId, PUBLIC_SPOT, parkSpaceVo,null);
                        CarParkManager.inst().receiveReward(parkSpaceVo, 1.0f, isUpgrade, isUpgrade ? parkSpaceMap : null, p_record, false);
                        publicData.setSpace(pos, null);
                        vo.clear(); // 清除停车信息
                        parkData.carPark.setMountMap(MountVo.mapToJsonStr(parkSpaceMap)); // 保存更新
                    } else {
                        msg.addCarList(CarParkManager.inst().to_p_car_park_car(entry.getKey(), vo, parkSpaceVo));
                    }
                }else {
                    Log.carPark.error("公共停车场数据错误，masterId={}，humanId={},pos={},mountId={}", vo.masterId, parkData.carPark.getId(), vo.pos, entry.getKey());
                    vo.clear();
                }
            } else if(vo.parkType == CROSS_SPOT){
//                CarParkCross carParkCross = (CarParkCross) EntityManager.getEntity(CarParkCross.class, vo.masterId);
//                ParkSpaceVo parkSpaceVo = new ParkSpaceVo(CarParkManager.inst().getPlace(carParkCross, vo.pos));
//                if(parkSpaceVo.humanId != 0){
//                    CarParkManager.inst().reCaculateCarReward(CarParkManager.inst().getAttrCrossCarPark(), parkSpaceVo);
//
//                    // 检查是否超时
//                    if((Port.getTime()/Time.SEC - parkSpaceVo.startTime) >= ConfGlobal.get(ConfGlobalKey.park_auto_time).value*60*60) {
//                        // 发放奖励
//                        boolean isUpgrade = processRewardedMountVo(entry.getKey(), entry.getValue(), parkSpaceVo.carExp, parkSpaceVo.lastGetTime);
//                        Define.p_car_park_record p_record = buildCarParkRecordAutoGetReward(vo.masterId,CROSS_SPOT,parkSpaceVo,null);
//                        CarParkManager.inst().receiveReward(parkSpaceVo, 1.0f, isUpgrade, isUpgrade ? parkSpaceMap : null, p_record);
//                        CarParkManager.inst().setPlace(carParkCross, vo.pos, "{}");
//                        vo.clear(); // 清除停车信息
//                    } else {
//                        msg.addCarList(CarParkManager.inst().to_p_car_park_car(entry.getKey(), vo, parkSpaceVo));
//                    }
//                }
            } else if(vo.parkType == HUMAM_SPOT && vo.masterId != 0){
                humanParkIds.add(vo.masterId);
            }else {
                msg.addCarList(CarParkManager.inst().to_p_car_park_car(entry.getKey(), vo, null));
            }
        }

        // 如果有人类停车场需要处理
        if(!humanParkIds.isEmpty()) {
            getCarParkDataList(humanParkIds, res -> {
                if(res.failed()) {
                    Log.carPark.error("获取人类停车场数据失败");
                    AsyncActionResult.fail(Port.getCurrent(), handler, res.cause());
                    return;
                }

                List<CarParkData> carParkDataList = res.result();
                if(carParkDataList == null) {
                    AsyncActionResult.success(Port.getCurrent(), handler, msg);
                    return;
                }

                // 创建停车场数据Map便于查找
                Map<Long, CarParkData> carParkDataMap = new HashMap<>();
                for(CarParkData carParkData : carParkDataList) {
                    if(carParkData != null && carParkData.carPark != null) {
                        CarParkData finalCarParkData = safelyPutCarParkData(carParkData.carPark.getId(), carParkData);
                        carParkDataMap.put(carParkData.carPark.getId(), finalCarParkData);

                    }
                }

                // 遍历所有mount数据
                for(Map.Entry<Integer, MountVo> mountEntry : parkSpaceMap.entrySet()) {
                    int mountId = mountEntry.getKey();
                    MountVo mountVo = mountEntry.getValue();
                    
                    CarParkData carParkData = carParkDataMap.get(mountVo.masterId);
                    if(carParkData == null) continue;
                    
                    ParkSpaceVo parkSpaceVo = carParkData.parkSpaceList.get(mountVo.pos);
                    if(parkSpaceVo != null && parkSpaceVo.humanId == parkData.carPark.getId()) {
                        int oldCarCoin = parkSpaceVo.carCoin;
                        CarParkManager.inst().reCaculateCarReward(
                                CarParkManager.inst().getAttrHumanCarPark(carParkData.carPark),
                                parkSpaceVo);

                        // 如果奖励有变化,更新数据
                        if(oldCarCoin != parkSpaceVo.carCoin) {
                            carParkData.setSpace(mountVo.pos, parkSpaceVo);
                        }
                        // 检查是否超时
                        if((Port.getTime()/Time.SEC - parkSpaceVo.startTime) >=
                                ConfGlobal.get(ConfGlobalKey.park_auto_time).value*60*60) {
                            // 发放奖励
                            boolean isUpgrade = processRewardedMountVo(mountId, mountVo,
                                    parkSpaceVo.carExp, parkSpaceVo.lastGetTime, parkSpaceVo.mountPlacingTime);

                            // 异步获取玩家基本信息
                            carParkData.getHumanBaseInfo(infoRes -> {
                                if(infoRes.failed()) {
                                    Log.carPark.error("获取玩家基本信息失败");
                                    return;
                                }

                                Define.p_role_change.Builder infoBuilder = infoRes.result();
                                Define.p_car_park_record p_record = buildCarParkRecordAutoGetReward(
                                        carParkData.carPark.getId(), HUMAM_SPOT, parkSpaceVo, infoBuilder.build());
                                CarParkManager.inst().receiveReward(parkSpaceVo, 1.0f, isUpgrade,
                                        parkSpaceMap, p_record, parkData.carPark.getId() != parkSpaceVo.humanId);
                            });
                            giveIncome(carParkData, parkSpaceVo);
                            carParkData.setSpace(mountVo.pos, null);
                            mountVo.clear(); // 清除停车信息
                        }
                        msg.addCarList(CarParkManager.inst().to_p_car_park_car(
                                mountId, mountVo, parkSpaceVo));
                    }else {
                        Log.carPark.error("找不到停车位数据，humanId={},mountId={},masterId={},pos={}", parkData.carPark.getId(), mountId, mountVo.masterId, mountVo.pos);
                        mountVo.clear();
                    }
                }

                // 更新并返回消息
                parkData.carPark.setMountMap(MountVo.mapToJsonStr(parkSpaceMap));
                AsyncActionResult.success(Port.getCurrent(), handler, msg);
            });
            return;
        }

        // 如果没有人类停车场要处理,直接返回消息
        parkData.carPark.setMountMap(MountVo.mapToJsonStr(parkSpaceMap));
        AsyncActionResult.success(Port.getCurrent(), handler, msg);
    }

    // 自动领取收益
    private Define.p_car_park_record buildCarParkRecordAutoGetReward(long masterId, int parkType, ParkSpaceVo parkSpaceVo, Define.p_role_change pRoleChange){
        Define.p_lang_info.Builder builder = Define.p_lang_info.newBuilder();
        builder.setId(CarParkType.PARK_LOG_AUTO_GET_REWARD);
        
        // 添加参数:坐骑ID
        builder.addArgList(Define.p_key_value_name.newBuilder()
                .setK(Utils.intValue(MailManager.MAIL_K_10))
                .setV(parkSpaceVo.mountId));
        
        // 添加参数:玩家名称
        if(pRoleChange != null && pRoleChange.getKsList().size() > 1) {
            builder.addArgList(Define.p_key_value_name.newBuilder()
                    .setK(Utils.intValue(MailManager.MAIL_K_3))
                    .setName(pRoleChange.getKs(0).getS()));
        }
        if(parkType == PUBLIC_SPOT) {
            builder.addArgList(Define.p_key_value_name.newBuilder()
                    .setK(Utils.intValue(MailManager.MAIL_K_1))
                    .setV(PUBLIC_PARK_LANG));
        }
        
        // 添加参数:停车时长(分钟)
        int parkingMinutes = (parkSpaceVo.lastGetTime - parkSpaceVo.startTime) / 60;
        int maxTime = ConfGlobal.get(ConfGlobalKey.park_time_limit).value;
        parkingMinutes = parkingMinutes > maxTime ? maxTime : parkingMinutes;
        builder.addArgList(Define.p_key_value_name.newBuilder().setK(Utils.intValue(MailManager.MAIL_K_0)).setV(parkingMinutes));
        
        // 添加参数:获得的魔灵币
        builder.addArgList(Define.p_key_value_name.newBuilder().setK(Utils.intValue(MailManager.MAIL_K_0)).setV(parkSpaceVo.carCoin));

        return buildCarParkRecord(masterId, parkType, CarParkType.PARK_LOG_AUTO_GET_REWARD, parkSpaceVo.lastGetTime, ProduceManager.inst().to_p_rewardList(parkSpaceVo.rewardItemMap), pRoleChange, builder);
    }

    // 单人战斗胜利
    private Define.p_car_park_record buildCarParkRecordBeatSolo(long masterId, int parkType, int carCoin, Define.p_role_change pRoleChange, String robbedName) {
        Define.p_lang_info.Builder builder = Define.p_lang_info.newBuilder();
        builder.setId(CarParkType.PARK_LOG_BEAT_SOLO);

        //您在%s的通道抢夺了%s，获得%s个魔灵币
        // 添加参数:玩家名称
        if(pRoleChange != null && pRoleChange.getKsList().size() > 1) {
            builder.addArgList(Define.p_key_value_name.newBuilder()
                    .setK(Utils.intValue(MailManager.MAIL_K_3))
                    .setName(pRoleChange.getKs(0).getS()));
        }
        if(parkType == PUBLIC_SPOT) {
            builder.addArgList(Define.p_key_value_name.newBuilder()
                    .setK(Utils.intValue(MailManager.MAIL_K_1))
                    .setV(PUBLIC_PARK_LANG));
        }
        builder.addArgList(Define.p_key_value_name.newBuilder().setK(Utils.intValue(MailManager.MAIL_K_3)).setName(robbedName));
        // 添加参数:获得的魔灵币
        builder.addArgList(Define.p_key_value_name.newBuilder().setK(Utils.intValue(MailManager.MAIL_K_0)).setV(carCoin));

        return buildCarParkRecord(masterId, parkType, CarParkType.PARK_LOG_BEAT_SOLO, (int)(Port.getTime()/Time.SEC),
                null, pRoleChange, builder);
    }

    // 多人战斗胜利
    private Define.p_car_park_record buildCarParkRecordBeatMuti(long masterId, int parkType, Define.p_role_change pRoleChange, String partnerName, String robbedName, int carCoinPerPerson) {
        Define.p_lang_info.Builder builder = Define.p_lang_info.newBuilder();
        builder.setId(CarParkType.PARK_LOG_BEAT_MUTI);
        // 添加参数:合作伙伴名称
        builder.addArgList(Define.p_key_value_name.newBuilder()
                .setK(Utils.intValue(MailManager.MAIL_K_3))
                .setName(partnerName));
        // 添加停车场类型参数
        if(pRoleChange != null && pRoleChange.getKsList().size() > 1){
            builder.addArgList(Define.p_key_value_name.newBuilder().setK(Utils.intValue(MailManager.MAIL_K_3)).setName(pRoleChange.getKs(0).getS()));
        }
        if(parkType == PUBLIC_SPOT) {
            builder.addArgList(Define.p_key_value_name.newBuilder()
                    .setK(Utils.intValue(MailManager.MAIL_K_1))
                    .setV(PUBLIC_PARK_LANG));
        }
        // 添加被抢夺者名称
        builder.addArgList(Define.p_key_value_name.newBuilder()
                .setK(Utils.intValue(MailManager.MAIL_K_3))
                .setName(robbedName));
        // 添加每人获得的魔灵币数量
        builder.addArgList(Define.p_key_value_name.newBuilder()
                .setK(Utils.intValue(MailManager.MAIL_K_0))
                .setV(carCoinPerPerson));
        return buildCarParkRecord(masterId, parkType, CarParkType.PARK_LOG_BEAT_MUTI, (int)(Port.getTime()/Time.SEC),
                null, pRoleChange, builder);
    }

    // 获取奖励
    private Define.p_car_park_record buildCarParkRecordGetReward(long masterId, int parkType, ParkSpaceVo parkSpaceVo, Define.p_role_change pRoleChange) {
        Define.p_lang_info.Builder builder = Define.p_lang_info.newBuilder();
        builder.setId(CarParkType.PARK_LOG_GET_REWARD);

        // 添加参数:坐骑ID
        builder.addArgList(Define.p_key_value_name.newBuilder()
                .setK(Utils.intValue(MailManager.MAIL_K_10))
                .setV(parkSpaceVo.mountId));
        // 添加参数:玩家名称
        if(pRoleChange != null && pRoleChange.getKsList().size() > 1) {
            builder.addArgList(Define.p_key_value_name.newBuilder()
                    .setK(Utils.intValue(MailManager.MAIL_K_3))
                    .setName(pRoleChange.getKs(0).getS()));
        }
        if(parkType == PUBLIC_SPOT) {
            builder.addArgList(Define.p_key_value_name.newBuilder()
                    .setK(Utils.intValue(MailManager.MAIL_K_1))
                    .setV(PUBLIC_PARK_LANG));
        }
        // 添加参数:停车时长(分钟)
        int parkingMinutes = (parkSpaceVo.lastGetTime - parkSpaceVo.startTime) / 60;
        int maxTime = ConfGlobal.get(ConfGlobalKey.park_time_limit).value;
        parkingMinutes = parkingMinutes > maxTime ? maxTime : parkingMinutes;
        builder.addArgList(Define.p_key_value_name.newBuilder().setK(Utils.intValue(MailManager.MAIL_K_0)).setV(parkingMinutes));
        // 添加参数:获得的魔灵币
        builder.addArgList(Define.p_key_value_name.newBuilder().setK(Utils.intValue(MailManager.MAIL_K_0)).setV(parkSpaceVo.carCoin));

        return buildCarParkRecord(masterId, parkType, CarParkType.PARK_LOG_GET_REWARD, parkSpaceVo.lastGetTime,
                ProduceManager.inst().to_p_rewardList(parkSpaceVo.rewardItemMap), pRoleChange, builder);
    }

    // 获取奖励2
    private Define.p_car_park_record buildCarParkRecordGetReward2(long masterId, int parkType, ParkSpaceVo parkSpaceVo, int managerFee, Define.p_role_change pRoleChange) {
        Define.p_lang_info.Builder builder = Define.p_lang_info.newBuilder();
        builder.setId(CarParkType.PARK_LOG_GET_REWARD2);
        // 添加参数:坐骑ID
        builder.addArgList(Define.p_key_value_name.newBuilder()
                .setK(Utils.intValue(MailManager.MAIL_K_10))
                .setV(parkSpaceVo.mountId));
        // 添加参数1: 玩家名称 (%s)
        if (pRoleChange != null && pRoleChange.getKsList().size() > 1) {
            builder.addArgList(Define.p_key_value_name.newBuilder()
                    .setK(Utils.intValue(MailManager.MAIL_K_3))
                    .setName(pRoleChange.getKs(0).getS()));
        }
        // 添加参数2: 停车场类型 (%s)
        if (parkType == PUBLIC_SPOT) {
            builder.addArgList(Define.p_key_value_name.newBuilder()
                    .setK(Utils.intValue(MailManager.MAIL_K_1))
                    .setV(PUBLIC_PARK_LANG));
        }
        // 添加参数3: 停车时间(分钟) (%s)
        int parkingMinutes = (parkSpaceVo.lastGetTime - parkSpaceVo.startTime) / 60;
        int maxTime = ConfGlobal.get(ConfGlobalKey.park_time_limit).value;
        parkingMinutes = parkingMinutes > maxTime ? maxTime : parkingMinutes;
        builder.addArgList(Define.p_key_value_name.newBuilder()
                .setK(Utils.intValue(MailManager.MAIL_K_0))
                .setV(parkingMinutes));
        // 添加参数4: 获得的魔灵币 (%s)
        builder.addArgList(Define.p_key_value_name.newBuilder()
                .setK(Utils.intValue(MailManager.MAIL_K_0))
                .setV(parkSpaceVo.carCoin));
        // 添加参数5: 管理费 (%s)
        builder.addArgList(Define.p_key_value_name.newBuilder()
                .setK(Utils.intValue(MailManager.MAIL_K_0))
                .setV(managerFee));
        return buildCarParkRecord(masterId, parkType, CarParkType.PARK_LOG_GET_REWARD2, Utils.getTimeSec(),
                ProduceManager.inst().to_p_rewardList(parkSpaceVo.rewardItemMap), pRoleChange, builder);

    }

    /**
     * 构建帮助收车的记录
     */
    public Define.p_car_park_record buildCarParkRecordHelpGetReward(long parkId, int type, ParkSpaceVo vo, Define.p_role_change info) {
        Define.p_lang_info.Builder builder = Define.p_lang_info.newBuilder();
        builder.setId(CarParkType.PARK_LOG_HELP_GET_REWARD);

        // 添加参数1: 您的坐骑名称 (%s)
        builder.addArgList(Define.p_key_value_name.newBuilder()
                .setK(Utils.intValue(MailManager.MAIL_K_10))
                .setV(vo.mountId));
        // 添加参数2: 帮助者名称 (%s)
        String helperName = "";
        if(info != null && info.getKsCount() > 0) {
            helperName = info.getKs(0).getS();
        }
        builder.addArgList(Define.p_key_value_name.newBuilder()
                .setK(Utils.intValue(MailManager.MAIL_K_3))
                .setName(helperName));
        // 添加参数3: 帮助者名称 (%s)
        builder.addArgList(Define.p_key_value_name.newBuilder()
                .setK(Utils.intValue(MailManager.MAIL_K_3))
                .setName(helperName));

        // 添加参数4: 魔灵币数量 (%s)
        builder.addArgList(Define.p_key_value_name.newBuilder()
                .setK(Utils.intValue(MailManager.MAIL_K_0))
                .setV(vo.carCoin));

        // 添加参数5: 魔源之力数量 (%s)
        builder.addArgList(Define.p_key_value_name.newBuilder()
                .setK(Utils.intValue(MailManager.MAIL_K_0))
                .setV(vo.carExp));

        return buildCarParkRecord(parkId, type, CarParkType.PARK_LOG_HELP_GET_REWARD, 
                (int)(Port.getTime()/Time.SEC),
                ProduceManager.inst().to_p_rewardList(vo.rewardItemMap), 
                info, 
                builder);
    }

    // 被他人抢夺
    private Define.p_car_park_record buildCarParkRecordGetBeaten(long masterId, int parkType, ParkSpaceVo parkSpaceVo, Define.p_role_change pRoleChange,String robName, int robbedCoin){
        Define.p_lang_info.Builder builder = Define.p_lang_info.newBuilder();
        builder.setId(CarParkType.PARK_LOG_GET_BEATEN);
        // 添加参数:坐骑ID
        builder.addArgList(Define.p_key_value_name.newBuilder()
                .setK(Utils.intValue(MailManager.MAIL_K_10))
                .setV(parkSpaceVo.mountId));
        // 添加参数:玩家名称
        if(pRoleChange != null && pRoleChange.getKsList().size() > 1) {
            builder.addArgList(Define.p_key_value_name.newBuilder()
                    .setK(Utils.intValue(MailManager.MAIL_K_3))
                    .setName(pRoleChange.getKs(0).getS()));
        }
        if(parkType == PUBLIC_SPOT) {
            builder.addArgList(Define.p_key_value_name.newBuilder()
                    .setK(Utils.intValue(MailManager.MAIL_K_1))
                    .setV(PUBLIC_PARK_LANG));
        }
        // 添加参数:停放时间(分钟)
        int parkingMinutes = (parkSpaceVo.lastGetTime - parkSpaceVo.startTime) / 60;
        builder.addArgList(Define.p_key_value_name.newBuilder()
                .setK(Utils.intValue(MailManager.MAIL_K_0))
                .setV(parkingMinutes));
        // 添加参数:被抢夺
        builder.addArgList(Define.p_key_value_name.newBuilder()
                .setK(Utils.intValue(MailManager.MAIL_K_3))
                .setName(robName));
        // 添加参数:损失
        builder.addArgList(Define.p_key_value_name.newBuilder()
                .setK(Utils.intValue(MailManager.MAIL_K_0))
                .setV(robbedCoin));
        // 添加参数:最终获得
        int managerFee = 0;
        int finalCoin = parkSpaceVo.carCoin;
        if (parkSpaceVo.protectRatio > 0) {
            managerFee = parkSpaceVo.carCoin * parkSpaceVo.protectRatio / 100;
            finalCoin = parkSpaceVo.carCoin - managerFee;
        }
        builder.addArgList(Define.p_key_value_name.newBuilder()
                .setK(Utils.intValue(MailManager.MAIL_K_0))
                .setV(finalCoin));
        return buildCarParkRecord(masterId, parkType, CarParkType.PARK_LOG_GET_BEATEN, parkSpaceVo.lastGetTime,
                ProduceManager.inst().to_p_rewardList(parkSpaceVo.rewardItemMap), pRoleChange, builder);
    }

    private Define.p_car_park_record buildCarParkRecord(long masterId, int parkType, int actionId, int time, List<Define.p_reward> rewards, Define.p_role_change pRoleChange, Define.p_lang_info.Builder content){
        ConfParkingLog confParkingLog = ConfParkingLog.get(actionId);
        if(confParkingLog == null){
            return null;
        }
        Define.p_car_park_record.Builder builder = Define.p_car_park_record.newBuilder();
        builder.setNewId(CommonUid.getId());
        if(pRoleChange != null){
            builder.setInfoList(pRoleChange);
        }
        builder.setActionId(actionId);
        builder.setType(parkType);
        builder.setMasterId(masterId);
        builder.setTime(time);
        builder.setIsRead(0);
        if(rewards != null){
            builder.addAllRewardList(rewards);
        }
        builder.setContent(content);
        return builder.build();
    }

    private void handleMountVoAndReceiveReward(ParkSpaceVo parkSpaceVo, Define.p_car_park_record record, float rate) {
        handleMountVoAndReceiveReward(parkSpaceVo, record, rate, true);
    }
    /**
     * 处理坐骑奖励和经验更新
     * @param parkSpaceVo 停车位信息
     */
    private void handleMountVoAndReceiveReward(ParkSpaceVo parkSpaceVo, Define.p_car_park_record record, float rate, boolean refresh) {
        // 从缓存获取玩家停车场数据
        CarParkData carParkData = carParkMap.get(parkSpaceVo.humanId);
        if(carParkData != null && carParkData.carPark != null) {
            // 获取坐骑信息
            Map<Integer,MountVo> mountMap = MountVo.mapFromJsonStr(carParkData.carPark.getMountMap());
            MountVo mountVo = mountMap.get(parkSpaceVo.mountId);
            if(mountVo != null) {
                //加成
                Map<Integer, Integer> rewardAdd = Utils.jsonToMapIntInt(carParkData.carPark.getRewardAddMap());
                // 处理坐骑数据并获取是否升级
                int carExpAdd = rewardAdd.getOrDefault(ItemConstants.goods_菇车经验, 0);
                boolean isUpgrade = processRewardedMountVo(parkSpaceVo.mountId, mountVo, (int)(parkSpaceVo.carExp * (1+carExpAdd/10000.0)), parkSpaceVo.lastGetTime, parkSpaceVo.mountPlacingTime);

                // 处理奖励,如果升级则传入mountMap
                int carCoinAdd = rewardAdd.getOrDefault(ItemConstants.goods_菇车币, 0);
                CarParkManager.inst().receiveReward(parkSpaceVo, rate+carCoinAdd/10000f, isUpgrade, isUpgrade ? mountMap : null, record, refresh);
                if(parkSpaceVo.masterId == mountVo.masterId){
                    mountVo.clear();
                }else {
                    Log.carPark.error("玩家收车数据对不上，humanId={},masterId={},mountId={},mountVo.masterId={}",parkSpaceVo.humanId,parkSpaceVo.masterId,parkSpaceVo.mountId,mountVo.masterId);
                }
                Log.carPark.info("{}缓存中获取了玩家数据，清除了坐骑{}信息",parkSpaceVo.humanId,parkSpaceVo.mountId);
                carParkData.carPark.setMountMap(MountVo.mapToJsonStr(mountMap));
            }
            return;
        }

        // 从数据库异步加载
//        long pid = Port.getCurrent().createReturnAsync();
        EntityManager.getEntityAsync(CarPark.class, parkSpaceVo.humanId, res -> {
            if(res.failed()) {
                Log.carPark.error("handleMountVoAndReceiveReward:获取CarPark数据失败, humanId={}", parkSpaceVo.humanId);
                // 返回构建好的消息
//                port.returnsAsync(pid, null); // 添加异步返回
                return;
            }

            CarPark carPark = res.result();
            if(carPark == null) {
//                port.returnsAsync(pid, null); // 添加异步返回
                return;
            }

            // 获取坐骑信息
            Map<Integer,MountVo> mountMap = MountVo.mapFromJsonStr(carPark.getMountMap());
            MountVo mountVo = mountMap.get(parkSpaceVo.mountId);
            if(mountVo != null) {
                //加成
                Map<Integer, Integer> rewardAdd = Utils.jsonToMapIntInt(carPark.getRewardAddMap());
                // 处理坐骑数据并获取是否升级
                int carExpAdd = rewardAdd.getOrDefault(ItemConstants.goods_菇车经验, 0);
                boolean isUpgrade = processRewardedMountVo(parkSpaceVo.mountId, mountVo, (int)(parkSpaceVo.carExp * (1+carExpAdd/10000.0)), parkSpaceVo.lastGetTime, parkSpaceVo.mountPlacingTime);
                // 处理奖励,如果升级则传入mountMap
                int carCoinAdd = rewardAdd.getOrDefault(ItemConstants.goods_菇车币, 0);
                CarParkManager.inst().receiveReward(parkSpaceVo, 1.0f+carCoinAdd/10000f, isUpgrade, isUpgrade ? mountMap : null, record, true);
                if(parkSpaceVo.masterId == mountVo.masterId){
                    mountVo.clear();
                }else {
                    Log.carPark.error("玩家收车数据对不上，humanId={},masterId={},mountId={},mountVo.masterId={}",parkSpaceVo.humanId,parkSpaceVo.masterId,parkSpaceVo.mountId,mountVo.masterId);
                }
                Log.carPark.info("{}redis中获取了玩家数据，清除了坐骑{}信息",parkSpaceVo.humanId,parkSpaceVo.mountId);
                carPark.setMountMap(MountVo.mapToJsonStr(mountMap));
                carPark.update();
            }
        });
    }

    private boolean processRewardedMountVo(int mountSn, MountVo mountVo, int carExp, int lastGetTime, int mountPlacingTime) {
        if(mountVo == null) {
            return false;
        }
        boolean isLevelUp = false;
        // 处理经验和等级
        mountVo.exp += carExp;
        while (true) {
            ConfParkingMount_0 confNext = ConfParkingMount_0.get(mountSn, mountVo.lv + 1);
            if (confNext == null) {
                break;
            }
            if (mountVo.exp >= confNext.expend) {
                mountVo.lv = confNext.level;
                isLevelUp = true;
            } else {
                break;
            }
        }

        // 处理停车时间
        if(Utils.isToday(lastGetTime*Time.SEC)) {
            mountVo.parkTime = mountPlacingTime;
        }else {
            mountVo.parkTime = 0;
        }
        return isLevelUp;
    }

    @DistrMethod
    public void carParkCarUp(long humanId, int mountId, int costNum) {
        CarParkData carParkData = carParkMap.get(humanId);
        if (carParkData == null) {
            port.returns();
            return;
        }
        CarPark carPark = carParkData.carPark;
        if (carPark == null) {
            port.returns();
            return;
        }

        Map<Integer, MountVo> mountVoMap = MountVo.mapFromJsonStr(carPark.getMountMap());
        MountVo vo = mountVoMap.get(mountId);
        if (vo == null) {
            port.returns();
            return;
        }

        ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.park_exp_book_num);
        int rewardExp = costNum * confGlobal.intArray[1];
        vo.exp += rewardExp;

        boolean isLevelUp = false;
        // 循环检查是否可以升级
        while (true) {
            ConfParkingMount_0 confNext = ConfParkingMount_0.get(mountId, vo.lv + 1);
            if (confNext == null) {
                break;
            }
            if (vo.exp >= confNext.expend) {
                vo.lv = confNext.level;
                isLevelUp = true;
            } else {
                break;
            }
        }

        carPark.setMountMap(MountVo.mapToJsonStr(mountVoMap));

        // 返回消息和数据
        port.returns("mountId", mountId, "vo", vo, "mountVoMap", isLevelUp ? mountVoMap : null, "actualCostNum", costNum);
    }

    @DistrMethod
    public void carParkParkingStart(long humanId, int type, long masterId, long mountId, int pos, int isProtect, int isReplace,
                     Define.p_role_change roleChange, Define.p_role_figure roleFigure) {
        masterId = masterId == 0 ? humanId : masterId;

        // 获取玩家停车场数据
        CarParkData carParkData = carParkMap.get(humanId);
        if(carParkData == null) {
            // 从数据库加载
            port.returns("errCode", 235);//找不到停车场
            return;
        }

        Map<Integer,MountVo> mountVoMap = MountVo.mapFromJsonStr(carParkData.carPark.getMountMap());
        MountVo vo = mountVoMap.get((int)mountId);
        if(vo == null){
            Log.carPark.error("玩家停车场是坐骑有误，humanID = {}, masterId={},mountId={},pos={}",humanId, masterId, mountId, pos);
            vo = new MountVo(1);
            mountVoMap.put((int)mountId, vo);
        }

        if(vo != null && vo.masterId != 0 && vo.pos != 0) {
            //车位检验，防止坏号
            if(vo.parkType == PUBLIC_SPOT){
                CarParkPublicData publicData = publicParkMap.get(vo.masterId);
                int spaceNo = (pos-1) % PUBLIC_SPACE_NUM + 1;
                ParkSpaceVo parkSpaceVo = publicData.getSpace(spaceNo);
                if(parkSpaceVo != null && parkSpaceVo.humanId == humanId && parkSpaceVo.mountId == mountId){
                    port.returns("errCode", 247);//车位已经停了其他车
                    return;
                }else {
                    Log.carPark.error("玩家停车公共车位是坐骑有误，humanID = {}, masterId={},mountId={},pos={}",humanId, masterId, mountId, pos);
                    vo.clear();
                }
            }else if(vo.parkType == HUMAM_SPOT){
                CarParkData parkData = carParkMap.get(humanId);
                ParkSpaceVo parkSpaceVo = parkData.getSpace(vo.pos);
                if(parkSpaceVo != null && parkSpaceVo.humanId == humanId && parkSpaceVo.mountId == mountId){
                    port.returns("errCode", 247);//车位已经停了其他车
                    return;
                }else {
                    Log.carPark.error("玩家私人车位是坐骑有误，humanID = {}, masterId={},mountId={},pos={}",humanId, masterId, mountId, pos);
                }
            }else {
                port.returns("errCode", 276);//正在驻守其他停车场
                return;
            }
        }

        // 检查停车数量限制
        if(getMountStopNum(mountVoMap) >= ConfGlobal.get(ConfGlobalKey.park_mount_limit).value) {
            port.returns("errCode", 254);//已达到停车数量上限
            return;
        }

        int protectAttr = CarParkManager.inst().getAttrHumanCarPark(carParkData.carPark)[CarParkType.SKIN_ATTR_PROTECT];
        ParkSpaceVo parkSpaceVo = null;

        if(type == PUBLIC_SPOT) {
            // 处理公共停车场
            parkSpaceVo = handlePublicSpot(humanId, masterId, mountId, pos, vo, protectAttr, roleChange, roleFigure);
        } else if(type == HUMAM_SPOT) {
            long pid = Port.getCurrent().createReturnAsync();
            // 处理玩家停车场
            long finalMasterId = masterId;
            MountVo finalVo = vo;
            handleHumanSpot(humanId, masterId, mountId, pos, vo, isProtect, isReplace, protectAttr,
                    roleChange, roleFigure, mountVoMap, res -> {
                        if(res.failed()) {
                            port.returnsAsync(pid, "errCode", 235);
                            return;
                        }

                        ParkSpaceVo parkSpaceHumanVo = res.result();
                        if(parkSpaceHumanVo == null) {
                            port.returnsAsync(pid, "errCode", 247);//找不到停车场
                            return;
                        }

                        // 更新玩家mount数据
                        finalVo.parkType = type;
                        finalVo.pos = pos;
                        finalVo.masterId = finalMasterId;
                        carParkData.carPark.setMountMap(MountVo.mapToJsonStr(mountVoMap));

                        // 构建并返回消息
                        MsgCarPark.car_park_parking_start_s2c.Builder msg =
                                MsgCarPark.car_park_parking_start_s2c.newBuilder();
                        msg.setType(type);
                        msg.setMasterId(finalMasterId);
                        msg.setSpace(CarParkManager.inst().to_p_car_park_space(pos, parkSpaceHumanVo));
                        port.returnsImmutableAsync(pid, "msg", msg.build());
                    });
            return;
        }

        if(parkSpaceVo == null) {
            port.returns("errCode", 247);//找不到车位
            return;
        }

        // 更新玩家mount数据
        vo.parkType = type;
        vo.pos = pos;
        vo.masterId = masterId;
        carParkData.carPark.setMountMap(MountVo.mapToJsonStr(mountVoMap));
        carParkData.carPark.update();

        Log.carPark.error("玩家停车成功:humanId={},mountId={},masterId={},pos={}", humanId, mountId, masterId, pos);

        // 构建并返回消息
        MsgCarPark.car_park_parking_start_s2c.Builder msg = MsgCarPark.car_park_parking_start_s2c.newBuilder();
        msg.setType(type);
        msg.setMasterId(masterId);
        msg.setSpace(CarParkManager.inst().to_p_car_park_space(pos, parkSpaceVo));
        port.returnsImmutable("msg", msg.build());
    }

    private int getMountStopNum(Map<Integer,MountVo> parkSpaceMap) {
        int num = 0;
        for (Map.Entry<Integer, MountVo> entry : parkSpaceMap.entrySet()) {
            MountVo vo = entry.getValue();
            if(vo.masterId == 0 || vo.pos == 0){
                continue;
            }
            if(vo.parkType == HUMAM_SPOT || vo.parkType == PUBLIC_SPOT){
                num++;
            }
        }
        return num;
    }

    private ParkSpaceVo handlePublicSpot(long humanId, long masterId, long mountId, int pos, MountVo vo, int protectAttr, Define.p_role_change roleChange, Define.p_role_figure roleFigure) {
        // 直接使用masterId作为key
        CarParkPublicData publicData = publicParkMap.get(masterId);
        if(publicData == null) {
            return null;
        }

        // 检查CD
        if(publicData.parkSpaceList.stream().anyMatch(space -> space != null && space.humanId == humanId)) {
            return null;
        }
        pos = (pos-1) % PUBLIC_SPACE_NUM+1;//客户端发来的pos可能是大于10的根据层数
        ParkSpaceVo parkSpaceVo = publicData.getSpace(pos);
        if(parkSpaceVo != null && parkSpaceVo.humanId != 0) {
            return null;
        }

        // 创建新的车位数据
        parkSpaceVo = CarParkManager.inst().createParkSpaceVo(masterId, humanId, (int)mountId, vo,
                CarParkManager.inst().getAttrPublicCarPark(), protectAttr,roleChange, roleFigure);

        // 替换为
        publicData.setSpace(pos, parkSpaceVo);

        return parkSpaceVo;
    }

    private void handleHumanSpot(long humanId, long masterId, long mountId, int pos, MountVo vo,
                                 int isProtect, int isReplace, int protectAttr, Define.p_role_change roleChange,
                                 Define.p_role_figure roleFigure, Map<Integer,MountVo> parkSpaceMap,
                                 Handler<AsyncResult<ParkSpaceVo>> handler) {

        CarParkData targetParkData = masterId == humanId ? carParkMap.get(humanId) : carParkMap.get(masterId);
        if(targetParkData != null) {
            // 处理缓存中的数据
            ParkSpaceVo parkSpaceVo = processHumanSpot(targetParkData, humanId, masterId, mountId, pos, vo,
                    isProtect, isReplace, protectAttr, roleChange, roleFigure, parkSpaceMap);
            AsyncActionResult.success(Port.getCurrent(), handler, parkSpaceVo);
            return;
        }

        // 从数据库异步获取
        EntityManager.getEntityAsync(CarPark.class, masterId, res -> {
            if(res.failed()) {
                Log.carPark.error("handleHumanSpot failed, masterId={}", masterId);
                AsyncActionResult.fail(Port.getCurrent(), handler, res.cause());
                return;
            }

            CarPark carPark = res.result();
            if(carPark == null) {
                AsyncActionResult.success(Port.getCurrent(), handler, null);
                return;
            }

            // 创建并缓存数据
            CarParkData parkData = new CarParkData(carPark, false);
            carParkMap.put(masterId, parkData);

            if(!parkData.isOnline) {
                addToOfflineCache(masterId, parkData.lastAccessTime);
            }

            // 处理停车逻辑并返回结果
            ParkSpaceVo parkSpaceVo = processHumanSpot(parkData, humanId, masterId, mountId, pos, vo,
                    isProtect, isReplace, protectAttr, roleChange, roleFigure, parkSpaceMap);
            AsyncActionResult.success(Port.getCurrent(), handler, parkSpaceVo);
        });
    }

    // 修改processHumanSpot方法返回ParkSpaceVo
    private ParkSpaceVo processHumanSpot(CarParkData targetParkData, long humanId, long masterId,
                                         long mountId, int pos, MountVo vo, int isProtect, int isReplace,
                                         int protectAttr, Define.p_role_change roleChange,
                                         Define.p_role_figure roleFigure, Map<Integer,MountVo> parkSpaceMap) {

        ParkSpaceVo parkSpaceVo = targetParkData.getSpace(pos);
        if(parkSpaceVo != null && parkSpaceVo.humanId != 0) {
            return null;
        }

        // 创建新的车位数据
        parkSpaceVo = CarParkManager.inst().createHumanParkSpaceVo(targetParkData.carPark, humanId,
                (int)mountId, vo, isProtect, isReplace, protectAttr, roleChange, roleFigure);
        targetParkData.setSpace(pos,parkSpaceVo);
        return parkSpaceVo;
    }

    @DistrMethod(argsImmutable = true)
    public void carParkParkingStop(long humanId, long mountId, Define.p_role_change roleChange, Define.p_role_figure roleFigure) {
        // 获取缓存数据
        CarParkData carParkData = carParkMap.get(humanId);
        if(carParkData == null) {
            port.returns();
            return;
        }

        // 获取坐骑信息
        Map<Integer, MountVo> mountMap = MountVo.mapFromJsonStr(carParkData.carPark.getMountMap());
        MountVo mountVo = mountMap.get((int)mountId);
        if(mountVo == null || mountVo.masterId == 0) {
            port.returns();
            return;
        }

        // 根据不同停车类型处理
        if(mountVo.parkType == HUMAM_SPOT) {
            // 处理玩家停车场
            CarParkData targetParkData = carParkMap.get(mountVo.masterId);
            long pid = Port.getCurrent().createReturnAsync();
            if(targetParkData != null) {
                processHumanSpotStop(humanId, mountId, mountVo, mountMap, carParkData, targetParkData, pid);
                return;
            }
            // 从数据库异步获取
            EntityManager.getEntityAsync(CarPark.class, mountVo.masterId, res -> {
                if(res.failed()) {
                    Log.carPark.error("carParkParkingStop failed, masterId={}", mountVo.masterId);
                    port.returnsAsync(pid);
                    return;
                }

                CarPark targetPark = res.result();
                if(targetPark == null) {
                    port.returnsAsync(pid);
                    return;
                }

                // 创建并缓存数据
                CarParkData parkData = new CarParkData(targetPark, false);
                
                // 获取所有车位上玩家的信息
                List<Long> humanIdList = new ArrayList<>();
                for(ParkSpaceVo parkSpaceVo : parkData.parkSpaceList) {
                    if(parkSpaceVo != null && parkSpaceVo.humanId != 0) {
                        humanIdList.add(parkSpaceVo.humanId);
                    }
                }
                
                if(!humanIdList.isEmpty()) {
                    HumanData.getList(humanIdList, HumanManager.humanClasses2, humanRes -> {
                        if(!humanRes.failed()) {
                            List<HumanData> humanDataList = humanRes.result();
                            if(humanDataList != null) {
                                Map<Long, HumanData> humanDataMap = new HashMap<>();
                                for(HumanData humanData : humanDataList) {
                                    humanDataMap.put(humanData.human.getId(), humanData);
                                }
                                for(ParkSpaceVo parkSpaceVo : parkData.parkSpaceList) {
                                    if(parkSpaceVo != null && parkSpaceVo.humanId != 0) {
                                        HumanData humanData = humanDataMap.get(parkSpaceVo.humanId);
                                        if(humanData != null && humanData.human != null) {
                                            parkSpaceVo.setpRoleChangeAndpRoleFigure(humanData.human,humanData.human2);
                                        }
                                    }
                                }
                            }
                        }
                        
                        carParkMap.put(mountVo.masterId, parkData);

                        if(!parkData.isOnline) {
                            addToOfflineCache(mountVo.masterId, parkData.lastAccessTime);
                        }
                        processHumanSpotStop(humanId, mountId, mountVo, mountMap, carParkData, parkData, pid);
                    });
                } else {
                    carParkMap.put(mountVo.masterId, parkData);

                    if(!parkData.isOnline) {
                        addToOfflineCache(mountVo.masterId, parkData.lastAccessTime);
                    }
                    processHumanSpotStop(humanId, mountId, mountVo, mountMap, carParkData, parkData, pid);
                }
            });

        } else if(mountVo.parkType == PUBLIC_SPOT) {
            // 处理公共停车场
            CarParkPublicData publicData = publicParkMap.get(mountVo.masterId);
            if(publicData == null) {
                port.returns();
                return;
            }

            processPublicSpotStop(humanId, mountId, mountVo, mountMap, carParkData, publicData);
        } else {
            port.returns();
        }
    }

    private void processPublicSpotStop(long humanId, long mountId, MountVo mountVo, Map<Integer, MountVo> mountMap,
                                       CarParkData carParkData, CarParkPublicData publicData) {
        ParkSpaceVo parkSpaceVo = publicData.getSpace((mountVo.pos-1)%PUBLIC_SPACE_NUM+1);
        if(parkSpaceVo.humanId != humanId) {
            port.returns();
            return;
        }

        //加成
        Map<Integer, Integer> rewardAdd = Utils.jsonToMapIntInt(carParkData.carPark.getRewardAddMap());
        int addExpPpt = rewardAdd.getOrDefault(ItemConstants.goods_菇车经验, 0);
        int addCoinPpt = rewardAdd.getOrDefault(ItemConstants.goods_菇车币, 0);
        // 计算奖励但不直接发送
        CarParkManager.inst().reCaculateCarReward(CarParkManager.inst().getAttrPublicCarPark(), parkSpaceVo);
        Map<Integer, Integer> rewards = parkSpaceVo.rewardItemMap;
        rewards.put(ItemConstants.goods_菇车币, parkSpaceVo.carCoin * (10000 + addCoinPpt) / 10000);

        // 清除车位
        publicData.setSpace((mountVo.pos-1)%PUBLIC_SPACE_NUM+1, null);

        // 保存原始mountVo信息
        MountVo oldMountVo = new MountVo(mountVo);

        // 更新坐骑经验和等级
        updateMountExpAndLevel(mountVo, parkSpaceVo, mountId, addExpPpt);

        // 清除停车信息
        if(mountVo.masterId == parkSpaceVo.masterId) {
            mountVo.clear();
        }else {
            Log.carPark.error("玩家收车数据对不上，humanId={},masterId={},mountId={},mountVo.masterId={}",parkSpaceVo.humanId,parkSpaceVo.masterId,parkSpaceVo.mountId,mountVo.masterId);
        }

        carParkData.carPark.setMountMap(MountVo.mapToJsonStr(mountMap));

        Define.p_car_park_record record = buildCarParkRecordGetReward(publicData.carParkPublic.getId(), PUBLIC_SPOT, parkSpaceVo, null);
        sendCarParkRecord(humanId, false, null, null, record);

        // 返回结果
        port.returns("rewards", rewards, "propUpdate", true, "oldMountVo", oldMountVo);
    }

    private void updateMountExpAndLevel(MountVo mountVo, ParkSpaceVo parkSpaceVo, long mountId, int addExpPpt) {
        // 更新坐骑经验
        mountVo.exp += (parkSpaceVo.carExp * (10000 + addExpPpt)/10000);
        while (true) {
            ConfParkingMount_0 confNext = ConfParkingMount_0.get(mountId, mountVo.lv + 1);
            if (confNext == null) {
                break;
            }
            if (mountVo.exp >= confNext.expend) {
                mountVo.lv = confNext.level;
            } else {
                break;
            }
        }
        if(Utils.isToday(parkSpaceVo.lastGetTime*Time.SEC)) {
            mountVo.parkTime = parkSpaceVo.mountPlacingTime;
        }
    }

    private void processHumanSpotStop(long humanId, long mountId, MountVo mountVo, Map<Integer, MountVo> mountMap, CarParkData carParkData, CarParkData targetParkData, long pid) {
        // 获取车位信息
        ParkSpaceVo parkSpaceVo = targetParkData.getSpace(mountVo.pos);
        if (parkSpaceVo.humanId != humanId) {
            port.returnsAsync(pid);
            return;
        }

        // 计算奖励但不直接发送
        CarParkManager.inst().reCaculateCarReward(CarParkManager.inst().getAttrHumanCarPark(targetParkData.carPark), parkSpaceVo);
        Map<Integer, Integer> rewards = parkSpaceVo.rewardItemMap;

        // 处理管理费
        int managerFee;
        if (parkSpaceVo.protectRatio > 0) {
            managerFee = parkSpaceVo.carCoin * parkSpaceVo.protectRatio / 100;
            parkSpaceVo.carCoin -= managerFee;
            sendManagerFeeMail(targetParkData.carPark.getId(), managerFee, parkSpaceVo);
        } else {
            managerFee = 0;
        }
        Map<Integer, Integer> rewardAddMap = Utils.jsonToMapIntInt(carParkData.carPark.getRewardAddMap());
        int addCoinPpt = rewardAddMap.getOrDefault(ItemConstants.goods_菇车币, 0);
        int addExpPpt = rewardAddMap.getOrDefault(ItemConstants.goods_菇车经验, 0);
        rewards.put(ItemConstants.goods_菇车币, (int)(parkSpaceVo.carCoin * (10000f + addCoinPpt) / 10000f));

        // 清除车位
        giveIncome(targetParkData, parkSpaceVo);
        targetParkData.setSpace(mountVo.pos, null);

        // 保存原始mountVo信息
        MountVo oldMountVo = new MountVo(mountVo);

        // 更新坐骑经验和等级
        updateMountExpAndLevel(mountVo, parkSpaceVo, mountId, addExpPpt);

        // 清除停车信息
        if(mountVo.masterId == parkSpaceVo.masterId) {
            mountVo.clear();
        }else {
            Log.carPark.error("玩家收车数据对不上，humanId={},masterId={},mountId={},mountVo.masterId={}",parkSpaceVo.humanId,parkSpaceVo.masterId,parkSpaceVo.mountId,mountVo.masterId);
        }
        carParkData.carPark.setMountMap(MountVo.mapToJsonStr(mountMap));
        targetParkData.getHumanBaseInfo(humanRes->{
            if(humanRes.failed()) {
                return;
            }
            Define.p_role_change.Builder infoBuilder = humanRes.result();
            if(infoBuilder == null){
                return;
            }
            if(parkSpaceVo.protectRatio == 0){
                Define.p_car_park_record record = buildCarParkRecordGetReward(targetParkData.carPark.getId(), HUMAM_SPOT, parkSpaceVo, infoBuilder.build());
                sendCarParkRecord(humanId, false, null, null, record);
            }else {
                Define.p_car_park_record record = buildCarParkRecordGetReward2(targetParkData.carPark.getId(), HUMAM_SPOT, parkSpaceVo, managerFee, infoBuilder.build());
                sendCarParkRecord(humanId, false, null, null, record);
            }

        });


        // 返回结果
        port.returnsAsync(pid,"rewards", rewards, "propUpdate", true, "mountMap", mountMap, "oldMountVo", oldMountVo);
    }

    private void sendManagerFeeMail(long humanId, int managerFee, ParkSpaceVo parkSpaceVo) {
        // 如果管理费为0,不发送邮件
        if(managerFee <= 0) {
            return;
        }
        JSONObject jo = new JSONObject();

        // 构建MAIL_PARAM_1
        JSONObject joTemp1 = new JSONObject();
        String name = "";
        if(parkSpaceVo.pRoleChange!=null && parkSpaceVo.pRoleChange.getKsCount()>1){
            name = parkSpaceVo.pRoleChange.getKs(0).getS();
        }
        joTemp1.put(MailManager.MAIL_K_3, name);
        jo.put(MailManager.MAIL_PARAM_1, joTemp1);

        // 构建MAIL_PARAM_2
        JSONObject joTemp2 = new JSONObject();
        joTemp2.put(MailManager.MAIL_K_10, parkSpaceVo.mountId);
        jo.put(MailManager.MAIL_PARAM_2, joTemp2);

        // 构建MAIL_PARAM_3
        JSONObject joTemp3 = new JSONObject();
        joTemp3.put(MailManager.MAIL_K_0, (parkSpaceVo.lastGetTime - parkSpaceVo.startTime) / 60);
        jo.put(MailManager.MAIL_PARAM_3, joTemp3);

        // 构建MAIL_PARAM_4
        JSONObject joTemp4 = new JSONObject();
        joTemp4.put(MailManager.MAIL_K_4, parkSpaceVo.carCoin);
        jo.put(MailManager.MAIL_PARAM_4, joTemp4);

        // 构建MAIL_PARAM_5
        JSONObject joTemp5 = new JSONObject();
        joTemp5.put(MailManager.MAIL_K_4, managerFee);
        jo.put(MailManager.MAIL_PARAM_5, joTemp5);

        // 构建物品JSON
        JSONObject itemJSON = new JSONObject();
        itemJSON.put(Integer.toString(ItemConstants.goods_菇车币), managerFee);

        MailManager.inst().sendMail(humanId, MailManager.SYS_SENDER, 10149, "", jo.toJSONString(), itemJSON.toJSONString(), new Param());
    }

    @DistrMethod
    public void carParkCombat(long humanId, int type, long masterId, int pos) {
        // 检查战斗状态
        String stateKey = getCombatStateKey(type, masterId, pos);
        if (checkAndCleanCombatState(stateKey)) {
            port.returns("success", false, "errorCode", 235);
            return;
        }

        // 记录新的战斗状态
        combatStateMap.put(stateKey, new long[]{humanId, Port.getTime() / Time.SEC});

        try {
            // 获取玩家停车场数据
            CarParkData carParkData = carParkMap.get(humanId);
            if (carParkData == null) {
                port.returns("success", false, "errorCode", 235); // 找不到停车场
                combatStateMap.remove(stateKey);
                return;
            }

            // 检查buff状态
            List<Integer> buff = CarParkManager.inst().getReCaculateBuff(carParkData.carPark, BUFF_ATTACK, true);
            if (buff != null && buff.get(0) == 0 && buff.get(1) > 0) {
                port.returns("success", false, "errorCode", 281); // buff限制
                combatStateMap.remove(stateKey);
                return;
            }

            ParkSpaceVo parkSpaceVo = null;
            String propBonus = null;

            // 处理不同类型的停车场
            if (type == PUBLIC_SPOT) {
                CarParkPublicData publicData = publicParkMap.get(masterId);
                if (publicData != null) {
                    int spaceNo = (pos-1) % PUBLIC_SPACE_NUM + 1;
                    parkSpaceVo = publicData.getSpace(spaceNo);
                }
            } else if (type == HUMAM_SPOT) {
                CarParkData targetParkData = carParkMap.get(masterId);
                if (targetParkData != null) {
                    parkSpaceVo = targetParkData.getSpace(pos);
                }
                propBonus  = targetParkData.getSkinPvpPropJson();
            }

            if (parkSpaceVo == null) {
                port.returns("success", false, "errorCode", 241); // 无法找到车车位
                combatStateMap.remove(stateKey);
                return;
            }

            // 检查车位是否为空
            if (parkSpaceVo.humanId == 0) {
                port.returns("success", false, "errorCode", 237); // 找不到车位
                combatStateMap.remove(stateKey);
                return;
            }

            // 检查保护时间
            if (parkSpaceVo.protectEndTime > Port.getTime()/Time.SEC) {
                port.returns("success", false, "errorCode", 263); // 停车保护中,不能抢夺
                combatStateMap.remove(stateKey);
                return;
            }

            // 检查是否已经抢占过
            if (parkSpaceVo.roberList.contains(humanId)) {
                port.returns("success", false, "errorCode", 239); // 挑战次数不足
                combatStateMap.remove(stateKey);
                return;
            }

            // 检查保护比率
            if (parkSpaceVo.protectRatio > 0 && masterId == humanId) {
                port.returns("success", false, "errorCode", 240); // 对方未开启保护费缴纳
                combatStateMap.remove(stateKey);
                return;
            }

            // 检查是否攻击自己的车位
            if (humanId == parkSpaceVo.humanId) {
                port.returns("success", false, "errorCode", 238); // 不能挑战自己的车
                combatStateMap.remove(stateKey);
                return;
            }

            int atkDebuffSkillId = 0;
            int atkBuffNum = CarParkManager.inst().getReCaculateBuff(carParkData.carPark, BUFF_ATTACK, false).get(0);
            ConfParkPvpDebuff_0 confParkPvpDebuff0 = ConfParkPvpDebuff_0.get(atkBuffNum, BUFF_ATTACK);
            if(confParkPvpDebuff0 != null){
                atkDebuffSkillId = confParkPvpDebuff0.skill_id;
            }

            // 所有检查通过,返回成功结果
            port.returns("success", true, "targetHumanId", parkSpaceVo.humanId, "isReplace", parkSpaceVo.isReplace,
                    "hp", parkSpaceVo.hp, "propBonus", propBonus,
                    "defDebuff", getDebufSkillId(parkSpaceVo), "atkDebuff",atkDebuffSkillId);

        } catch (Exception e) {
            Log.carPark.error("carParkCombat error", e);
            combatStateMap.remove(stateKey);
            port.returns("success", false, "errorCode", 242); // 挑战冷却中
        }
    }

    private int getDebufSkillId(ParkSpaceVo parkSpaceVo){
        parkSpaceVo.resetBuffSn();
        int defDebuffSkillId = 0;
        ConfParkPvpDebuff_0 confParkPvpDebuff = ConfParkPvpDebuff_0.get(parkSpaceVo.buffSn, BUFF_DEFEND);
        {
            if(confParkPvpDebuff != null && confParkPvpDebuff.skill_id > 0) {
                defDebuffSkillId = confParkPvpDebuff.skill_id;
            }
        }
        return defDebuffSkillId;
    }


    @DistrMethod
    public void carParkResult(long humanId, long winId, long hp, int type, long masterId, int pos, long attackPower) {
        // 获取战斗数据
        String stateKey = getCombatStateKey(type, masterId, pos);
        if (!checkAndCleanCombatState(stateKey)) {
            Log.carPark.error("carParkResult: combat state timeout or not exist, humanId={}", humanId);
            port.returns();
            return;
        }

        long[] combatState = combatStateMap.get(stateKey);
        if (combatState[0] != humanId) {
            Log.carPark.error("carParkResult: invalid combat humanId, expected={}, actual={}", combatState[0], humanId);
            port.returns();
            return;
        }

        try {
            // 处理胜利者ID
            winId = winId == 0 ? humanId : winId;

            Param result = null;
            // 处理不同类型的停车场
            if (type == PUBLIC_SPOT) {
                result = handlePublicCombatResult(humanId, winId, hp, masterId, pos, attackPower);
            } else if (type == HUMAM_SPOT) {
                result = handleHumanCombatResult(humanId, winId, hp, masterId, pos, attackPower);
            }
            port.returns(result);

        } finally {
            // 清除战斗状态
            combatStateMap.remove(stateKey);
        }
    }

    private Param handlePublicCombatResult(long humanId, long winId, long hp, long masterId, int pos, long attackPower) {
    // 直接使用masterId作为key
    CarParkPublicData publicData = publicParkMap.get(masterId);
    if(publicData == null) {
        Log.carPark.error("找不到公共停车场id={}", masterId);
        return null;
    }

    int spaceNo = (pos-1) % PUBLIC_SPACE_NUM + 1;
    ParkSpaceVo parkSpaceVo = publicData.getSpace(spaceNo);
    if(parkSpaceVo == null) {
        String parkSpaceStr = CarParkManager.inst().getPlace(publicData.carParkPublic, spaceNo);
        parkSpaceVo = Utils.isEmptyJSONString(parkSpaceStr) ? new ParkSpaceVo() : new ParkSpaceVo(parkSpaceStr);
    }

    if(parkSpaceVo.humanId == 0) {
        Log.carPark.error("战斗结果时停车位没有玩家, masterId={}, spaceNo={}", masterId, spaceNo);
        return null;
    }

    if(humanId == winId) {
        // 抢占成功
        publicData.setSpace(spaceNo, null);
        // 给攻击者添加debuff
        CarParkData attackerData = carParkMap.get(humanId);
        if(masterId != humanId && attackerData != null) {
            CarParkManager.inst().addBuff(attackerData.carPark, BUFF_ATTACK);
        }
        return processRobbery(humanId, PUBLIC_SPOT, parkSpaceVo, masterId, null);
    } else {
        // 抢占失败
        parkSpaceVo.roberList.add(humanId);
        int oldHp = parkSpaceVo.hp;
        parkSpaceVo.hp = (int)hp;
        // 计算防守方debuff
        float powerLimit = ConfGlobal.get(ConfGlobalKey.park_defend_power_limit).floatArray[0];

        long defensePower = 0;
        if(parkSpaceVo.pRoleChange.getKvList().size() > 2){
            defensePower = parkSpaceVo.pRoleChange.getKv(2).getV();
        }
        if(defensePower != 0 && attackPower > defensePower * powerLimit) {
            List<Integer> buffList = CarParkManager.inst().getRecalculateBuff(parkSpaceVo.buffSn, parkSpaceVo.buffEndTime, BUFF_DEFEND);
            CarParkManager.inst().addBuff(buffList, BUFF_DEFEND);
            parkSpaceVo.buffSn = buffList.get(0);
            parkSpaceVo.buffEndTime = buffList.get(1);
        }
        publicData.setSpace(spaceNo, parkSpaceVo);
        publicData.carParkPublic.update();

        // 给攻击者添加debuff
        CarParkData attackerData = carParkMap.get(humanId);
        if(masterId != humanId && attackerData != null) {
            CarParkManager.inst().addLoseBuff(attackerData.carPark, BUFF_ATTACK);
        }

        int atkDebuffSkillId = 0;
        int atkBuffNum = CarParkManager.inst().getReCaculateBuff(attackerData.carPark, BUFF_ATTACK, false).get(0);
        ConfParkPvpDebuff_0 confParkPvpDebuff0 = ConfParkPvpDebuff_0.get(atkBuffNum, BUFF_ATTACK);
        if(confParkPvpDebuff0 != null){
            atkDebuffSkillId = confParkPvpDebuff0.skill_id;
        }

        // 返回结果
        Param result = new Param();
        result.put("winId", winId);
        result.put("hp", oldHp);
        result.put("isReplace", parkSpaceVo.isReplace);
        result.put("targetHumanId", parkSpaceVo.humanId);
        result.put("atkDebuff", atkDebuffSkillId);
        result.put("defDebuff", getDebufSkillId(parkSpaceVo));
        return result;
    }
}

private Param handleHumanCombatResult(long humanId, long winId, long hp, long masterId, int pos, long attackPower) {
    CarParkData parkData = carParkMap.get(masterId);
    if(parkData == null) {
        return null;
    }

    ParkSpaceVo parkSpaceVo = parkData.getSpace(pos);
    if(parkSpaceVo.humanId == 0) {
        return null;
    }

    if(humanId == winId) {
        // 抢占成功
        giveIncome(parkData, parkSpaceVo);
        parkData.setSpace(pos, null);
        // 给攻击者添加debuff
        CarParkData attackerData = carParkMap.get(humanId);
        if(attackerData != null && masterId != humanId) {
            CarParkManager.inst().addBuff(attackerData.carPark, BUFF_ATTACK);
        }
        return processRobbery(humanId, HUMAM_SPOT, parkSpaceVo, masterId, parkData.getSkinPvpPropJson());
    } else {
        // 抢占失败
        parkSpaceVo.roberList.add(humanId);
        int oldHp = parkSpaceVo.hp;
        parkSpaceVo.hp = (int)hp;
        // 计算防守方debuff
        if(humanId != masterId){
            float powerLimit = ConfGlobal.get(ConfGlobalKey.park_defend_power_limit).floatArray[0];
            long defensePower = 0;
            if(parkSpaceVo.pRoleChange.getKvList().size() > 2){
                defensePower = parkSpaceVo.pRoleChange.getKv(2).getV();
            }
            if(defensePower != 0 && attackPower > defensePower * powerLimit) {
                List<Integer> buffList = CarParkManager.inst().getRecalculateBuff(parkSpaceVo.buffSn, parkSpaceVo.buffEndTime, BUFF_DEFEND);
                CarParkManager.inst().addBuff(buffList, BUFF_DEFEND);
                parkSpaceVo.buffSn = buffList.get(0);
                parkSpaceVo.buffEndTime = buffList.get(1);
            }
        }
        parkData.setSpace(pos, parkSpaceVo);

        // 从内存中获取玩家停车场数据
        CarParkData attackerData = carParkMap.get(humanId);
        if(masterId != humanId && attackerData != null) {
            CarParkManager.inst().addLoseBuff(attackerData.carPark, BUFF_ATTACK);
        }

        int atkDebuffSkillId = 0;
        int atkBuffNum = CarParkManager.inst().getReCaculateBuff(attackerData.carPark, BUFF_ATTACK, false).get(0);
        ConfParkPvpDebuff_0 confParkPvpDebuff0 = ConfParkPvpDebuff_0.get(atkBuffNum, BUFF_ATTACK);
        if(confParkPvpDebuff0 != null){
            atkDebuffSkillId = confParkPvpDebuff0.skill_id;
        }

        // 返回结果
        Param result = new Param();
        result.put("winId", winId);
        result.put("hp", (int)oldHp);
        result.put("isReplace", parkSpaceVo.isReplace);
        result.put("targetHumanId", parkSpaceVo.humanId);
        result.put("propBonus", attackerData.getSkinPvpPropJson());
        result.put("atkDebuff", atkDebuffSkillId);
        result.put("defDebuff", getDebufSkillId(parkSpaceVo));
        return result;
    }
}

    public Param processRobbery(long humanId, int parkType, ParkSpaceVo parkSpaceVo, long masterId, String propBonus) {
        float robRate = ConfGlobal.get(ConfGlobalKey.park_steal_rate).floatArray[0];
        int robberNum = parkSpaceVo.roberList.size() + 1;
        int robbedCoinNum =(int) (parkSpaceVo.carCoin * robRate);
        int carCoinNumPer = robbedCoinNum / robberNum;
        parkSpaceVo.carCoin -= robbedCoinNum;

        // 构建奖励信息
        Param result = new Param();
        result.put("carCoinNum", carCoinNumPer);
        result.put("robRate", robRate);
        result.put("winId", humanId);
        result.put("hp", 0);
        result.put("isReplace", parkSpaceVo.isReplace);
        result.put("targetHumanId", parkSpaceVo.humanId);
        result.put("propBonus",propBonus);
//        Log.capture.info("抢劫成功，winId = {}, 抢到金币：{}", humanId, carCoinNumPer);

        // 收集需要查询的玩家ID
        List<Long> queryHumanIds = new ArrayList<>();
        queryHumanIds.add(humanId);  // 抢占成功的玩家
        queryHumanIds.add(masterId);  // 停车场主人
        queryHumanIds.add(parkSpaceVo.humanId);  // 车位所有者
        queryHumanIds.addAll(parkSpaceVo.roberList);  // 其他抢占者

        // 异步查询所有相关玩家的数据
        HumanData.getList(queryHumanIds, new Class[]{Human.class}, res -> {
            if(res.failed()) {
                Log.carPark.error("获取玩家数据失败");
                return;
            }

            Map<Long, String> humanNames = new HashMap<>();
            List<HumanData> humanDataList = res.result();
            for(HumanData humanData : humanDataList) {
                if(humanData != null && humanData.human != null) {
                    humanNames.put(humanData.human.getId(), humanData.human.getName());
                }
            }

            // 发送停车费邮件
            if (masterId != parkSpaceVo.humanId) {
                String masterName = humanNames.getOrDefault(masterId, "");
                CarParkManager.inst().sendParkFeeMail(
                        masterName,
                        parkSpaceVo.humanId,
                        parkSpaceVo
                );
            }

            // 给其他抢占者发邮件
            String winnerName = humanNames.getOrDefault(humanId, "");
            String masterName = humanNames.getOrDefault(masterId, "");
            String ownerName = humanNames.getOrDefault(parkSpaceVo.humanId, "");
            String firstRobName = "";
            if(parkSpaceVo.roberList != null && !parkSpaceVo.roberList.isEmpty()){
                firstRobName = humanNames.getOrDefault(parkSpaceVo.roberList.get(0), "");
            }

            for (long robberId : parkSpaceVo.roberList) {
                if (robberId == humanId) {
                    continue;
                }
                sendRobberyMail(robberId, winnerName, masterName, ownerName, carCoinNumPer);
            }

            // 区分公共车位和私人车位的处理
            if (parkType == PUBLIC_SPOT) {
                // 公共车位直接处理奖励
                Define.p_car_park_record record = buildCarParkRecordGetBeaten(masterId, parkType, parkSpaceVo, null, winnerName, robbedCoinNum);
                handleMountVoAndReceiveReward(parkSpaceVo, record, 1);

                // 发送战斗记录
                if(parkSpaceVo.roberList.size() == 0) {
                    Define.p_car_park_record recordSingle = buildCarParkRecordBeatSolo(masterId, parkType, carCoinNumPer, null, ownerName);
                    sendCarParkRecord(humanId, false, null, null, recordSingle);
                } else {
                    // 给所有参与者发送多人战斗记录
                    for(long robberId : parkSpaceVo.roberList) {
                        Define.p_car_park_record recordMuti = buildCarParkRecordBeatMuti(masterId, parkType, null, winnerName, ownerName, carCoinNumPer);
                        sendCarParkRecord(robberId, false, null, null, recordMuti);
                    }
                    // 给最后一击的玩家也发送记录
                    Define.p_car_park_record recordMuti = buildCarParkRecordBeatMuti(masterId, parkType, null, firstRobName, ownerName, carCoinNumPer);
                    sendCarParkRecord(humanId, false, null, null, recordMuti);
                }
            } else {
                // 私人车位需要获取车库主人信息
                HumanData masterHumanData = humanDataList.get(1);
                if(masterHumanData != null && masterHumanData.human != null) {
                    Define.p_role_change.Builder roleChange = CarParkManager.inst().to_p_role_change(masterHumanData.human,"");
                    Define.p_car_park_record record = buildCarParkRecordGetBeaten(masterId, parkType, parkSpaceVo, roleChange.build(), winnerName, robbedCoinNum);
                    handleMountVoAndReceiveReward(parkSpaceVo, record, 1);

                    if(parkSpaceVo.roberList.size() == 0) {
                        Define.p_car_park_record recordSingle = buildCarParkRecordBeatSolo(masterId, parkType, carCoinNumPer, roleChange.build(), ownerName);
                        sendCarParkRecord(humanId, false, null, null, recordSingle);
                    } else {
                        // 给所有参与者发送多人战斗记录
                        for(long robberId : parkSpaceVo.roberList) {
                            Define.p_car_park_record recordMuti = buildCarParkRecordBeatMuti(masterId, parkType, roleChange.build(), winnerName, ownerName, carCoinNumPer);
                            sendCarParkRecord(robberId, false, null, null, recordMuti);
                        }
                        // 给最后一击的玩家也发送记录
                        Define.p_car_park_record recordMuti = buildCarParkRecordBeatMuti(masterId, parkType, roleChange.build(), firstRobName, ownerName, carCoinNumPer);
                        sendCarParkRecord(humanId, false, null, null, recordMuti);
                    }
                }
            }
        });

        return result;
    }

    private void sendRobberyMail(long robberId, String winnerName, String masterName, String ownerName, int carCoinNum) {
        JSONObject jo = new JSONObject();

        JSONObject joTemp1 = new JSONObject();
        joTemp1.put(MailManager.MAIL_K_3, winnerName);
        jo.put(MailManager.MAIL_PARAM_1, joTemp1);

        JSONObject joTemp2 = new JSONObject();
        joTemp2.put(MailManager.MAIL_K_3, masterName);
        jo.put(MailManager.MAIL_PARAM_2, joTemp2);

        JSONObject joTemp3 = new JSONObject();
        joTemp3.put(MailManager.MAIL_K_3, ownerName);
        jo.put(MailManager.MAIL_PARAM_3, joTemp3);

        JSONObject joTemp4 = new JSONObject();
        joTemp4.put(MailManager.MAIL_K_4, carCoinNum);
        jo.put(MailManager.MAIL_PARAM_4, joTemp4);

        JSONObject itemJSON = new JSONObject();
        itemJSON.put(Integer.toString(ItemConstants.goods_菇车币), carCoinNum);

        MailManager.inst().sendMail(robberId, MailManager.SYS_SENDER, 10148, "",
                jo.toJSONString(), itemJSON.toJSONString(), new Param());
    }

    private String getCombatStateKey(int type, long masterId, int pos) {
        return type == PUBLIC_SPOT ?
            Utils.createStr("{}_{}",type, pos) :
            Utils.createStr("{}_{}_{}",type, masterId, pos);
    }

    private boolean checkAndCleanCombatState(String stateKey) {
        long[] existingState = combatStateMap.get(stateKey);
        if (existingState != null) {
            long now = Port.getTime() / Time.SEC;
            if (now - existingState[1] >= COMBAT_TIMEOUT) {
                // 超时，清理状态
                combatStateMap.remove(stateKey);
                return false;
            }
            return true; // 战斗状态有效
        }
        return false; // 无战斗状态
    }

    @ScheduleMethod(DataResetService.CRON_DAY_ZERO)
    public void schdDayZeroReset() {
        long now = Port.getTime();


        // 处理内存中的数据
        for (CarParkData parkData : carParkMap.values()) {
            if (parkData != null && parkData.carPark != null) {
                resetMountParkTime(parkData.carPark, now);
            }
        }
    }

    private void resetMountParkTime(CarPark carPark, long resetTime) {
        Map<Integer, MountVo> mountMap = MountVo.mapFromJsonStr(carPark.getMountMap());
        boolean needUpdate = false;

        for (Map.Entry<Integer, MountVo> entry : mountMap.entrySet()) {
            MountVo vo = entry.getValue();
            if (vo.parkTime != 0) {
                vo.parkTime = 0;
                needUpdate = true;
            }
        }

        if (needUpdate) {
            carPark.setMountMap(MountVo.mapToJsonStr(mountMap));
        }
        carPark.setDailyResetTime(resetTime);
    }

    @DistrMethod
    public void getPublicCarParkNullNum(int serverId) {
        // 创建结果Map，key为层数，value为空位数量
        Map<Integer, Integer> nullNumMap = new HashMap<>();

        // 遍历所有层
        for (Map.Entry<Long, CarParkPublicData> entry : publicParkMap.entrySet()) {
            long parkId = entry.getKey();
            CarParkPublicData publicData = entry.getValue();

            if (publicData == null || publicData.carParkPublic == null) {
                continue;
            }

            // 计算该层的空位数量
            int nullNum = 0;
            for (int j = 1; j <= PUBLIC_SPACE_NUM; j++) {
                List<ParkSpaceVo> spaceList = publicData.parkSpaceList;
                ParkSpaceVo parkSpaceVo = spaceList.get(j);
                if (parkSpaceVo == null || parkSpaceVo.masterId == 0) {
                    nullNum++;
                }
            }

            nullNumMap.put((int)parkId, nullNum);
        }

        port.returns(nullNumMap);
    }

    @DistrMethod
    public void carParkProtect(long humanId, int isOpen, int protectType, int protectRatio) {
        // 获取玩家停车场数据
        CarParkData data = carParkMap.get(humanId);
        if(data == null || data.carPark == null) {
            port.returns();
            return;
        }

        // 参数验证
        if(protectType < 0 || protectType > 3) {
            port.returns();
            return;
        }
        if(protectRatio < 5 || protectRatio > 20) {
            port.returns();
            return;
        }

        // 更新保护设置
        data.carPark.setIsProtect(isOpen);
        data.carPark.setProtectType(protectType);
        data.carPark.setProtectRatio(protectRatio);
        data.carPark.update();

        // 返回成功
        Param result = new Param();
        result.put("success", true);
        port.returns(result);
    }

    @DistrMethod
    public void carParkSkinUse(long humanId, int type, int skinId, int isUse, int x, int y) {
        // 先从缓存获取
        CarParkData carParkData = carParkMap.get(humanId);
        CarPark carPark = carParkData != null ? carParkData.carPark : null;

        if(carPark == null) {
            Port.getCurrent().returns();
            return;
        }

        Map<Integer, Integer> skinLvMap = Utils.jsonToMapIntInt(carPark.getSkinLvMap());
        if(!skinLvMap.containsKey(skinId)){
            return;
        }

        ConfParkingDesign_0 conf = ConfParkingDesign_0.get(skinId, skinLvMap.get(skinId));
        if(conf == null){
            Log.carPark.error("carParkSkinUse:找不到停车场装扮配置id={},lv={}", skinId, skinLvMap.get(skinId));
            Port.getCurrent().returns();
            return;
        }

        List<Define.p_car_park_skin> skinList = new ArrayList<>();
        if(conf.position == TYPE_DEC) {
            Map<Integer,ParkSkinVo> skinDecSnVoMap = ParkSkinVo.mapFromJsonStr(carPark.getSkinDecSnVoMap());
            if(isUse == 1){
                skinDecSnVoMap.put(skinId, new ParkSkinVo(skinId, TYPE_DEC,  x,y));
                Define.p_car_park_skin.Builder skinUse = Define.p_car_park_skin.newBuilder();
                skinUse.setSkinId(skinId);
                skinUse.setSkinLev(skinLvMap.get(skinId));
                skinUse.setPos(TYPE_DEC);
                skinUse.setX(x);
                skinUse.setY(y);
                skinList.add(skinUse.build());
            }else {
                skinDecSnVoMap.remove(skinId);
                Define.p_car_park_skin.Builder skinUse = Define.p_car_park_skin.newBuilder();
                skinUse.setSkinId(skinId);
                skinUse.setSkinLev(skinLvMap.get(skinId));
                skinUse.setPos(0);
                skinList.add(skinUse.build());
            }
            carPark.setSkinDecSnVoMap(ParkSkinVo.mapToJsonStr(skinDecSnVoMap));
        } else {
            Map<Integer,Integer> skinTypeSnMap = Utils.jsonToMapIntInt(carPark.getSkinTypeSnMap());
            Define.p_car_park_skin.Builder skinUse = Define.p_car_park_skin.newBuilder();
            Define.p_car_park_skin.Builder skinRemove = Define.p_car_park_skin.newBuilder();

            int removeId = skinTypeSnMap.getOrDefault(conf.position, 0);
            skinRemove.setSkinId(removeId);
            skinRemove.setPos(0);
            skinRemove.setSkinLev(skinLvMap.get(removeId));
            skinList.add(skinRemove.build());

            if(isUse == 1){
                skinTypeSnMap.put(conf.position, skinId);
                skinUse.setPos(conf.position);
                skinUse.setSkinId(skinId);
                skinUse.setSkinLev(skinLvMap.get(skinId));
            }else {
                ConfParkingDesign_0 confDefault = ConfParkingDesign_0.getBy(ConfParkingDesign_0.K.position, conf.position,ConfParkingDesign_0.K.if_initial,1);
                if(confDefault == null){
                    Log.carPark.error("carParkSkinUse:找不到停车场装扮默认配置position={}", conf.position);
                    Port.getCurrent().returns();
                    return;
                }
                skinTypeSnMap.put(conf.position, confDefault.id);
                skinUse.setSkinId(confDefault.id);
                skinUse.setSkinLev(skinLvMap.get(confDefault.id));
                skinUse.setPos(conf.position);
            }
            carPark.setSkinTypeSnMap(Utils.mapIntIntToJSON(skinTypeSnMap));
            skinList.add(skinUse.build());
        }

        // 返回结果
        Port.getCurrent().returns("position", conf.position, "skinList", skinList);
    }

    @DistrMethod
    public void carParkMountId(long humanId, int mountId) {
        // 从缓存获取
        CarParkData carParkData = carParkMap.get(humanId);
        CarPark carPark = carParkData != null ? carParkData.carPark : null;
        
        if(carPark == null) {
            port.returns();
            return;
        }

        Map<Integer,MountVo> mountMap = MountVo.mapFromJsonStr(carPark.getMountMap());
        MountVo mountVo = mountMap.get(mountId);
        if(mountVo == null && mountId != 0) {
            port.returns();
            return;
        }

        carPark.setShowCar(mountId);
        
        // 返回成功结果
        port.returns("success", true);
    }

    @DistrMethod
    public void carParkRename(long humanId, String name) {
        // 从缓存获取
        CarParkData carParkData = carParkMap.get(humanId);
        CarPark carPark = carParkData != null ? carParkData.carPark : null;

        if(carPark == null) {
            port.returns();
            return;
        }

        // 更新名称
        carPark.setName(name);

        // 返回成功结果
        port.returns("success", true);
    }

    @DistrMethod
    public void getSkinLevel(long humanId, int skinId) {
        // 从缓存获取
        CarParkData carParkData = carParkMap.get(humanId);
        CarPark carPark = carParkData != null ? carParkData.carPark : null;

        if(carPark == null) {
            port.returns();
            return;
        }

        Map<Integer, Integer> skinLvMap = Utils.jsonToMapIntInt(carPark.getSkinLvMap());
        int lv = skinLvMap.getOrDefault(skinId, 0);

        port.returns("level", lv);
    }

    @DistrMethod
    public void carParkSkinUp(long humanId, int type, int skinId, int currentLevel) {
        // 从缓存获取
        CarParkData carParkData = carParkMap.get(humanId);
        CarPark carPark = carParkData != null ? carParkData.carPark : null;

        if(carPark == null) {
            port.returns();
            return;
        }

        Map<Integer, Integer> skinLvMap = Utils.jsonToMapIntInt(carPark.getSkinLvMap());
        // 验证当前等级是否匹配
        if(skinLvMap.getOrDefault(skinId, 0) != currentLevel) {
            port.returns();
            return;
        }

        ConfParkingDesign_0 conf = ConfParkingDesign_0.get(skinId, currentLevel);
        if(conf == null || conf.expend == null || conf.expend.length != 2){
            Log.carPark.error("on_car_park_skin_up_c2s:找不到停车场装扮配置id={},lv={}", skinId, skinLvMap.get(skinId));
            return;
        }
        // 更新等级
        skinLvMap.put(skinId, currentLevel + 1);
        carPark.setSkinLvMap(Utils.mapIntIntToJSON(skinLvMap));

        port.returnsImmutable("skinList", CarParkManager.inst().to_p_car_park_skin_List(conf.position, skinLvMap,
                ParkSkinVo.mapFromJsonStr(carPark.getSkinDecSnVoMap()),
                Utils.jsonToMapIntInt(carPark.getSkinTypeSnMap())), "skinLvMap", skinLvMap);
    }

    @DistrMethod
    public void addMountVo(long humanId, int unlock) {
        // 检查配置
        ConfParkingMount_0 conf = ConfParkingMount_0.get(unlock, 1);
        if(conf == null){
            Log.carPark.error("addMountVo:找不到车辆配置id={},lv={}", unlock, 1);
            port.returns();
            return;
        }

        CarParkData data = carParkMap.get(humanId);
        if(data == null || data.carPark == null){
            long pid = Port.getCurrent().createReturnAsync();
            EntityManager.getEntityAsync(CarPark.class, humanId, res -> {
                if(res.failed()) {
                    Log.carPark.error("onHumanLogin:获取CarPark数据失败, humanId={}", humanId);
                    return;
                }
                CarPark carPark = res.result();
                if(carPark == null) {
                    return;
                }

                // 检查是否需要重置
                if (!Utils.isToday(carPark.getDailyResetTime())) {
                    resetMountParkTime(carPark, Port.getTime());
                }

                // 创建并加入缓存
                CarParkData carParkData = new CarParkData(carPark, true);
                carParkMap.put(humanId, carParkData);

                // 更新坐骑数据
                Map<Integer,MountVo> mountMap = MountVo.mapFromJsonStr(carPark.getMountMap());
                MountVo vo = new MountVo(1);
                mountMap.put(unlock, vo);
                Log.carPark.info("addMountVo:玩家id={},解锁坐骑成功id={}", humanId, unlock);
                carPark.setMountMap(MountVo.mapToJsonStr(mountMap));
                port.returnsAsync(pid, "mountId", unlock, "vo", vo, "mountVoMap", mountMap, "actualCostNum", 0);
            });
            return;
        }
        // 更新坐骑数据
        Map<Integer,MountVo> mountMap = MountVo.mapFromJsonStr(data.carPark.getMountMap());
        MountVo vo = new MountVo(1);
        mountMap.put(unlock, vo);
        data.carPark.setMountMap(MountVo.mapToJsonStr(mountMap));
        port.returns("mountId", unlock, "vo", vo, "mountVoMap", mountMap, "actualCostNum", 0);
    }

    @DistrMethod
    public void getMountMap(long humanId) {
        CarParkData data = carParkMap.get(humanId);
        if(data == null){
            port.returns();
            return;
        }
        port.returns(data.carPark.getMountMap());
    }

    @DistrMethod
    public void isExitInCache(long roleId){

        CarParkData data = carParkMap.get(roleId);
        if(data == null){
            port.returns("isExit", false);
            return;
        }
        port.returns("isExit", true);
    }

    @DistrMethod
    public void gm(long humanId, String funStr, Param param){
        if(funStr.equals("preParkTime")){
            int mountId = param.get("mountId");
            int min = param.get("min");
            CarParkData parkData = carParkMap.get(humanId);
            if(parkData == null){
                Log.carPark.error("gm找不到玩家数据，玩家id={}", humanId);
                return;
            }
            Map<Integer, MountVo> mountVoMap = MountVo.mapFromJsonStr(parkData.carPark.getMountMap());
            MountVo mountVo = mountVoMap.get(mountId);
            if(mountVo == null){
                Log.carPark.error("gm找不到玩家坐骑数据，玩家id={}", humanId);
                return;
            }
            if(mountVo.masterId == 0){
                Log.carPark.error("gm找不到玩家坐骑没停车，玩家id={}", humanId);
                return;
            }
            ParkSpaceVo parkSpaceVo = null;
            if(mountVo.parkType == HUMAM_SPOT){
                CarParkData carParkData = carParkMap.get(mountVo.masterId);
                if(carParkData == null){
                    Log.carPark.error("gm时玩家的车库不在缓存id={}", mountVo.masterId);
                    return;
                }
                parkSpaceVo = carParkData.getSpace(mountVo.pos);
                if(parkSpaceVo != null){
                    parkSpaceVo.startTime -= min * Time.SEC/1000;
                    parkSpaceVo.lastGetTime -= min * Time.SEC/1000;
                    parkSpaceVo.protectEndTime -= min * Time.SEC/1000;
                    parkSpaceVo.roberList.clear();
                }
                int oldCarCoin = parkSpaceVo.carCoin;
                CarParkManager.inst().reCaculateCarReward(CarParkManager.inst().getAttrHumanCarPark(carParkData.carPark),parkSpaceVo);
                if(oldCarCoin != parkSpaceVo.carCoin || min > 0) {
                    carParkData.setSpace(mountVo.pos, parkSpaceVo);
                }
            }else if(mountVo.parkType == PUBLIC_SPOT){
                CarParkPublicData carParkPublicData = publicParkMap.get(mountVo.masterId);
                if(carParkPublicData == null){
                    Log.carPark.error("gm时玩家的车库不在缓存id={}", mountVo.masterId);
                    return;
                }
                parkSpaceVo = carParkPublicData.getSpace((mountVo.pos-1)%PUBLIC_SPACE_NUM+1);
                if(parkSpaceVo != null){
                    parkSpaceVo.startTime -= min * Time.SEC/1000;
                    parkSpaceVo.lastGetTime -= min * Time.SEC/1000;
                    parkSpaceVo.protectEndTime -= min * Time.SEC/1000;
                    parkSpaceVo.roberList.clear();
                }
                int oldCarCoin = parkSpaceVo.carCoin;
                CarParkManager.inst().reCaculateCarReward(CarParkManager.inst().getAttrPublicCarPark(),parkSpaceVo);
                if(oldCarCoin != parkSpaceVo.carCoin || min > 0) {
                    carParkPublicData.setSpace(mountVo.pos, parkSpaceVo);
                }
            }
        }else if (funStr.equals("clearParkCd")){
            CarParkData parkData = carParkMap.get(humanId);
            if(parkData == null){
                Log.carPark.error("gm找不到玩家数据，玩家id={}", humanId);
                return;
            }
            parkData.carPark.setAtkBuff("");
            parkData.carPark.setDefBuff("");
        }
    }

    @DistrMethod
    public void update1(String json) {
        int serverId = Utils.intValue(json);
        loadPublicCarPark(serverId);
        Log.game.error("update1:更新公共车库数据成功,serverId={}", serverId);
    }

    @DistrMethod
    public void update2(Object... objs) {

    }

    @DistrMethod
    public void update3(Param param) {

    }

    @DistrMethod
    public void update4(String json) {

    }

    /**
     * 发送停车场记录到玩家
     * @param humanId 玩家ID
     * @param record 停车场记录
     */
    private void sendCarParkRecord(long humanId, boolean isUpgraded, Map<Integer, MountVo> mountMap, Map<Integer, Integer> reward, Define.p_car_park_record record) {
        if(record == null) {
            return;
        }
        // 通过HumanGlobal获取玩家信息
        HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance();
        proxy.getInfo(humanId);
        proxy.listenResult(this::_result_sendCarParkRecord, 
                "humanId", humanId,
                "isUpgraded", false,
                "mountMap", mountMap,
                "reward", reward,
                "record", record);
    }

    private void _result_sendCarParkRecord(Param results, Param context) {
        long humanId = context.get("humanId");
        Define.p_car_park_record record = context.get("record");
        boolean isUpgraded = context.get("isUpgraded");
        Map<Integer, MountVo> mountMap = context.get("mountMap");
        Map<Integer, Integer> reward = context.get("reward");

        HumanGlobalInfo humanInfo = results.get();
        if (humanInfo != null) {
            // 玩家在线,通过HumanObjectServiceProxy发送
            HumanObjectServiceProxy humanPrx = HumanObjectServiceProxy
                    .newInstance(humanInfo.nodeId, humanInfo.portId, humanInfo.id);
            Map<Integer, Integer> rewardCopy = null;
            if(reward != null){
                 rewardCopy = new HashMap<>(reward);
            }
            Map<Integer, MountVo> mountMapCopy = new HashMap<>();
            if(mountMap != null) {
                for(Map.Entry<Integer, MountVo> entry : mountMap.entrySet()) {
                    mountMapCopy.put(entry.getKey(), new MountVo(entry.getValue()));
                }
            }
            humanPrx.receiveCarParkReward(rewardCopy, isUpgraded, mountMapCopy, record, true);
        } else {
            // 玩家离线,加入待办
            JSONObject jo = new JSONObject();
            if(reward != null){
                jo.put("reward",Utils.mapIntIntToJSON(reward));
            }
            jo.put("propUpdate",isUpgraded);
            jo.put("record",Utils.toProtoString(record));
            Pocket.add(humanId, PocketLineEventSubKey.CAR_PARK_REWARD, jo.toJSONString());
        }
    }

    @DistrMethod
    public void helpCollectCarPark(long humanId, long target, int pos) {
        CarParkData carParkData = carParkMap.get(humanId);
        if (carParkData == null || carParkData.carPark == null) {
            port.returns("errCode", 235);
            return;
        }

        ParkSpaceVo parkSpaceVo = carParkData.getSpace(pos);
        if (parkSpaceVo == null || parkSpaceVo.humanId == target) {
            port.returns("errCode", 237);//找不到魔法通道
            return;
        }

        // 检查是否可以帮助收车
        if (parkSpaceVo.protectRatio <= 0) {
            port.returns("errCode", 244);//对方未开启保护费缴纳
            return;
        }

        // 检查时间是否达到8小时
        if ((Port.getTime()/Time.SEC - parkSpaceVo.startTime) < 
            ConfGlobal.get(ConfGlobalKey.park_time_limit).value*60) {
            port.returns("errCode", 222);
            return;
        }

        // 重新计算奖励
        CarParkManager.inst().reCaculateCarReward(
            CarParkManager.inst().getAttrHumanCarPark(carParkData.carPark),
            parkSpaceVo);

        // 获取玩家基本信息并处理奖励
        carParkData.getHumanBaseInfo(infoRes -> {
            if(infoRes.failed()) {
                Log.carPark.error("帮助领取奖励获取玩家基本信息失败, humanId={}", humanId);
                return;
            }

            Define.p_role_change.Builder infoBuilder = infoRes.result();
            // 构建获取奖励的记录
            Define.p_car_park_record p_record = buildCarParkRecordHelpGetReward(
                    humanId,
                HUMAM_SPOT, 
                parkSpaceVo,
                infoBuilder.build());
            int managerFee = parkSpaceVo.carCoin * parkSpaceVo.protectRatio / 100;
            parkSpaceVo.carCoin -= managerFee;
            sendManagerFeeMail(humanId, managerFee, parkSpaceVo);
            handleMountVoAndReceiveReward(parkSpaceVo, p_record, 1.0f);
        });
        giveIncome(carParkData, parkSpaceVo);
        carParkData.setSpace(pos, null);
        port.returns();
    }

    /**
     * 安全地将CarParkData放入carParkMap
     * 如果map中已存在该humanId的数据，则不覆盖
     * @param humanId 玩家ID
     * @param newData 新的CarParkData
     * @return 最终使用的CarParkData（可能是已存在的或新的）
     */
    private CarParkData safelyPutCarParkData(long humanId, CarParkData newData) {
        // 再次检查map中是否已存在数据
        CarParkData existingData = carParkMap.get(humanId);
        if (existingData != null) {
            // 已存在数据，使用已存在的数据
            return existingData;
        }
        
        // 不存在数据，放入新数据
        carParkMap.put(humanId, newData);
        return newData;
    }

    @DistrMethod
    public void updateRewardAdd(long humanId, Map<Integer, Integer> rewardAddMap){
        CarParkData carParkData = carParkMap.get(humanId);
        if(carParkData == null){
            Log.carPark.error("updateRewardAdd:找不到玩家停车场数据，玩家id={}", humanId);
            return;
        }
        carParkData.carPark.setRewardAddMap(Utils.mapIntIntToJSON(rewardAddMap));
    }
}
