package org.gof.demo.worldsrv.carPark;

import org.gof.core.support.ParamKey;
import org.gof.core.support.observer.MsgReceiver;
import org.gof.demo.seam.msg.MsgParam;
import org.gof.demo.worldsrv.arena.ArenaManager;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.msg.MsgCarPark;

public class CarParkMsgHandler {
    /**
     * 车库信息C2S消息
     */
    @MsgReceiver(MsgCarPark.car_park_info_c2s.class)
    public void car_park_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgCarPark.car_park_info_c2s msg = param.getMsg();
        CarParkManager.inst().on_car_park_info_c2s(humanObj, msg.getType(), msg.getMasterId(), msg.getCeng());
    }

    /**
     * 车辆信息C2S消息
     */
    @MsgReceiver(MsgCarPark.car_park_car_info_c2s.class)
    public void car_park_car_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgCarPark.car_park_car_info_c2s msg = param.getMsg();
        CarParkManager.inst().on_car_park_car_info_c2s(humanObj);
    }

    /**
     * 车辆升级C2S消息
     */
    @MsgReceiver(MsgCarPark.car_park_car_up_c2s.class)
    public void car_park_car_up_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgCarPark.car_park_car_up_c2s msg = param.getMsg();
        CarParkManager.inst().on_car_park_car_up_c2s(humanObj, msg.getMountId(), msg.getCostNum());
    }

    /**
     * 停车开始C2S消息
     */
    @MsgReceiver(MsgCarPark.car_park_parking_start_c2s.class)
    public void car_park_parking_start_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgCarPark.car_park_parking_start_c2s msg = param.getMsg();
        CarParkManager.inst().on_car_park_parking_start_c2s(humanObj, msg.getType(), msg.getMasterId(), msg.getMountId(), msg.getPos(), msg.getIsProtect(), msg.getIsReplace());
    }

    /**
     * 停车停止C2S消息
     */
    @MsgReceiver(MsgCarPark.car_park_parking_stop_c2s.class)
    public void car_park_parking_stop_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgCarPark.car_park_parking_stop_c2s msg = param.getMsg();
        CarParkManager.inst().on_car_park_parking_stop_c2s(humanObj, msg.getMountId());
    }

    /**
     * 停车战斗C2S消息
     */
    @MsgReceiver(MsgCarPark.car_park_combat_c2s.class)
    public void car_park_combat_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgCarPark.car_park_combat_c2s msg = param.getMsg();
        CarParkManager.inst().on_car_park_combat_c2s(humanObj, msg.getType(), msg.getMasterId(), msg.getPos());
    }

    /**
     * 处理车库战斗结果C2S消息
     */
    @MsgReceiver(MsgCarPark.car_park_result_c2s.class)
    public void car_park_result_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgCarPark.car_park_result_c2s msg = param.getMsg();
        CarParkManager.inst().on_car_park_result_c2s(humanObj, msg.getWinId(), msg.getHp());
    }

    /**
     * 处理车库搜索C2S消息
     */
    @MsgReceiver(MsgCarPark.car_park_search_c2s.class)
    public void car_park_search_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgCarPark.car_park_search_c2s msg = param.getMsg();
        CarParkManager.inst().on_car_park_search_c2s(humanObj, msg.getType(), msg.getParkName());
    }

    /**
     * 处理车库保护C2S消息
     */
    @MsgReceiver(MsgCarPark.car_park_protect_c2s.class)
    public void car_park_protect_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgCarPark.car_park_protect_c2s msg = param.getMsg();
        CarParkManager.inst().on_car_park_protect_c2s(humanObj, msg.getProtect());
    }

    /**
     * 处理车库使用皮肤C2S消息
     */
    @MsgReceiver(MsgCarPark.car_park_skin_use_c2s.class)
    public void car_park_skin_use_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgCarPark.car_park_skin_use_c2s msg = param.getMsg();
        CarParkManager.inst().on_car_park_skin_use_c2s(humanObj, msg.getType(), msg.getSkinId(), msg.getIsUse(), msg.getPos());
    }

    /**
     * 处理车库历史记录查询C2S消息
     */
    @MsgReceiver(MsgCarPark.car_park_history_c2s.class)
    public void car_park_history_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        CarParkManager.inst().on_car_park_history_c2s(humanObj);
    }

    /**
     * 处理车库视频播放C2S消息
     */
    @MsgReceiver(MsgCarPark.car_park_video_play_c2s.class)
    public void car_park_video_play_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgCarPark.car_park_video_play_c2s msg = param.getMsg();
        ArenaManager.inst().sendHistoryVideoProto(humanObj, msg.getVid(), ParamKey.historyType_carPark);
    }

    /**
     * 处理车库查看C2S消息
     */
    @MsgReceiver(MsgCarPark.car_park_look_c2s.class)
    public void car_park_look_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgCarPark.car_park_look_c2s msg = param.getMsg();
        //CarParkManager.inst().on_car_park_look_c2s(humanObj, msg.getVidListList());
    }

    /**
     * 处理车库装备ID C2S消息
     */
    @MsgReceiver(MsgCarPark.car_park_mount_id_c2s.class)
    public void car_park_mount_id_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgCarPark.car_park_mount_id_c2s msg = param.getMsg();
        CarParkManager.inst().on_car_park_mount_id_c2s(humanObj, msg.getMountId());
    }

    /**
     * 处理车库重命名 C2S消息
     */
    @MsgReceiver(MsgCarPark.car_park_rename_c2s.class)
    public void car_park_rename_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgCarPark.car_park_rename_c2s msg = param.getMsg();
        CarParkManager.inst().on_car_park_rename_c2s(humanObj, msg.getName());
    }

    /**
     * 处理车库收集 C2S消息
     */
    @MsgReceiver(MsgCarPark.car_park_collect_c2s.class)
    public void car_park_collect_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgCarPark.car_park_collect_c2s msg = param.getMsg();
        CarParkManager.inst().on_car_park_collect_c2s(humanObj, msg.getCode(), msg.getRoleId());
    }

    /**
     * 处理车库皮肤升级 C2S消息
     */
    @MsgReceiver(MsgCarPark.car_park_skin_up_c2s.class)
    public void car_park_skin_up_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgCarPark.car_park_skin_up_c2s msg = param.getMsg();
        CarParkManager.inst().on_car_park_skin_up_c2s(humanObj, msg.getType(), msg.getSkinId());
    }

    /**
     * 处理车库读取 C2S消息
     */
    @MsgReceiver(MsgCarPark.car_park_read_c2s.class)
    public void car_park_read_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgCarPark.car_park_read_c2s msg = param.getMsg();
        CarParkManager.inst().on_car_park_read_c2s(humanObj);
    }

    /**
     * 处理车库空间更新没用的
     */
    @MsgReceiver(MsgCarPark.car_park_space_update_c2s.class)
    public void car_park_space_update_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
//        CarParkManager.inst().on_car_park_space_update_c2s(humanObj);
    }

    /**
     * 处理车库战报C2S消息
     */
    @MsgReceiver(MsgCarPark.car_park_report_c2s.class)
    public void car_park_report_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        CarParkManager.inst().handleBattleReportListC2S(humanObj);
    }

    /**
     * 处理车库求助停车C2S消息
     */
    @MsgReceiver(MsgCarPark.car_park_parking_help_c2s.class)
    public void car_park_parking_help_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgCarPark.car_park_parking_help_c2s msg = param.getMsg();
        CarParkManager.inst().on_car_park_parking_help_c2s(humanObj, msg.getCarRoleId(), msg.getPos());
    }

    /**
     * 处理跨服车库排行榜C2S消息
     */
    @MsgReceiver(MsgCarPark.cross_car_park_rank_c2s.class)
    public void cross_car_park_rank_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
//        CarParkManager.inst().on_cross_car_park_rank_c2s(humanObj);
    }

    /**
     * 处理跨服车库预览C2S消息
     */
    @MsgReceiver(MsgCarPark.cross_car_park_preview_c2s.class)
    public void cross_car_park_preview_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
//        CarParkManager.inst().cross_car_park_preview_c2s(humanObj);
    }

    /**
     * 处理跨服车库信息C2S消息
     */
    @MsgReceiver(MsgCarPark.cross_car_park_info_c2s.class)
    public void cross_car_park_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgCarPark.cross_car_park_info_c2s msg = param.getMsg();
//        CarParkManager.inst().on_cross_car_park_info_c2s(humanObj, msg.getId());
    }

    /**
     * 处理跨服车库开始停车C2S消息
     */
    @MsgReceiver(MsgCarPark.cross_car_park_parking_start_c2s.class)
    public void cross_car_park_parking_start_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgCarPark.cross_car_park_parking_start_c2s msg = param.getMsg();
//        CarParkManager.inst().on_cross_car_park_parking_start_c2s(humanObj, msg.getId(), msg.getMountId(), msg.getPos());
    }

    /**
     * 处理跨服车库停车结束C2S消息
     */
    @MsgReceiver(MsgCarPark.cross_car_park_parking_stop_c2s.class)
    public void cross_car_park_parking_stop_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgCarPark.cross_car_park_parking_stop_c2s msg = param.getMsg();
//        CarParkManager.inst().on_cross_car_park_parking_stop_c2s(humanObj, msg.getMountId());
    }

    /**
     * 处理跨服车库战斗队列C2S消息
     */
    @MsgReceiver(MsgCarPark.cross_car_park_battle_queue_c2s.class)
    public void cross_car_park_battle_queue_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgCarPark.cross_car_park_battle_queue_c2s msg = param.getMsg();
//        CarParkManager.inst().on_cross_car_park_battle_queue_c2s(humanObj, msg.getType(), msg.getId());
    }

    /**
     * 处理跨服车库队列加入C2S消息
     */
    @MsgReceiver(MsgCarPark.cross_car_park_queue_join_c2s.class)
    public void cross_car_park_queue_join_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgCarPark.cross_car_park_queue_join_c2s msg = param.getMsg();
//        CarParkManager.inst().on_cross_car_park_queue_join_c2s(humanObj, msg.getType(), msg.getAction(), msg.getId());
    }

    /**
     * 处理跨服车库队列置顶C2S消息
     */
    @MsgReceiver(MsgCarPark.cross_car_park_queue_stick_c2s.class)
    public void cross_car_park_queue_stick_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgCarPark.cross_car_park_queue_stick_c2s msg = param.getMsg();
//        CarParkManager.inst().on_cross_car_park_queue_stick_c2s(humanObj, msg.getId());
    }

    /**
     * 处理跨服车库查看战斗信息
     */
    @MsgReceiver(MsgCarPark.cross_car_park_look_fighting_c2s.class)
    public void cross_car_park_look_fighting_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgCarPark.cross_car_park_look_fighting_c2s msg = param.getMsg();
//        CarParkManager.inst().on_cross_car_park_look_fighting_c2s(humanObj, msg.getId());
    }

    /**
     * 处理跨服车库战报列表C2S消息
     */
    @MsgReceiver(MsgCarPark.cross_car_park_report_list_c2s.class)
    public void cross_car_park_report_list_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgCarPark.cross_car_park_report_list_c2s msg = param.getMsg();
//        CarParkManager.inst().on_cross_car_park_report_list_c2s(humanObj, msg.getId());
    }

    /**
     * 处理跨服车库角色状态C2S消息
     */
    @MsgReceiver(MsgCarPark.cross_car_park_role_state_c2s.class)
    public void cross_car_park_role_state_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgCarPark.cross_car_park_role_state_c2s msg = param.getMsg();
//        CarParkManager.inst().on_cross_car_park_role_state_c2s(humanObj, msg.getType());todo：耗时待优化
    }

    /**
     * 处理跨服车库视频播放C2S消息
     */
    @MsgReceiver(MsgCarPark.cross_car_park_video_play_c2s.class)
    public void cross_car_park_video_play_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgCarPark.cross_car_park_video_play_c2s msg = param.getMsg();
        //CarParkManager.inst().on_cross_car_park_video_play_c2s(humanObj, msg.getVid());
    }

    /**
     * 处理跨服车库下一场开启时间C2S消息
     */
    @MsgReceiver(MsgCarPark.cross_car_park_next_time_c2s.class)
    public void cross_car_park_next_time_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
//        CarParkManager.inst().on_cross_car_park_next_time_c2s(humanObj);
    }

}
