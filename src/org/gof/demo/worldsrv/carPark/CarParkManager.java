package org.gof.demo.worldsrv.carPark;

import com.alibaba.fastjson.JSONObject;
import io.vertx.core.AsyncResult;
import io.vertx.core.Future;
import io.vertx.core.Handler;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import org.gof.core.Port;
import org.gof.core.dbsrv.redis.AsyncActionResult;
import org.gof.core.dbsrv.redis.AwaitUtil;
import org.gof.core.dbsrv.redis.EntityManager;
import org.gof.core.dbsrv.redis.RedisTools;
import org.gof.core.support.*;
import org.gof.core.support.observer.Listener;
import org.gof.demo.battlesrv.manager.CombatChangeLog;
import org.gof.demo.battlesrv.manager.PropManager;
import org.gof.demo.battlesrv.support.PropCalc;
import org.gof.demo.battlesrv.support.PropKey;
import org.gof.demo.worldsrv.arena.ArenaManager;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.character.HumanObjectServiceProxy;
import org.gof.demo.worldsrv.config.*;
import org.gof.demo.worldsrv.entity.*;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.guild.HumanBriefVO;
import org.gof.demo.worldsrv.human.*;
import org.gof.demo.worldsrv.instance.InstanceConstants;
import org.gof.demo.worldsrv.instance.InstanceManager;
import org.gof.demo.worldsrv.item.ItemConstants;
import org.gof.demo.worldsrv.item.ItemManager;
import org.gof.demo.worldsrv.mail.MailManager;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgCarPark;
import org.gof.demo.worldsrv.msg.MsgError;
import org.gof.demo.worldsrv.name.NameManager;
import org.gof.demo.worldsrv.pocketLine.Pocket;
import org.gof.demo.worldsrv.pocketLine.PocketLineEventSubKey;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.redis.RedisKeys;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.ReasonResult;
import org.gof.demo.worldsrv.support.Util;
import org.gof.demo.worldsrv.support.enumKey.ConfGlobalKey;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;
import org.gof.demo.worldsrv.support.observer.EventKey;
import org.gof.demo.worldsrv.task.TaskConditionTypeKey;
import org.gof.demo.worldsrv.team.TeamMember;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.*;

import static org.gof.demo.worldsrv.carPark.CarParkType.*;


public class CarParkManager extends ManagerBase {
    public static CarParkManager inst() {
        return inst(CarParkManager.class);
    }
    private static final int MAX_RECORD_SIZE = 20; // 最大保存战报数量

    /**
     * 车库信息C2S消息
     */
    public void on_car_park_info_c2s(HumanObject humanObj, int type, long masterId, int ceng) {
        if(humanObj.operation.carPark2 == null && humanObj.isModUnlock(77)){
            createCarPark(humanObj);
        }

        if(masterId == 0 && type == HUMAM_SPOT) {
            masterId = humanObj.id;
        }
        if(type == PUBLIC_SPOT) {
            // 获取公共车库信息
            CarParkServiceProxy proxy = CarParkServiceProxy.newInstance();
            if(ceng==0 || masterId > humanObj.getHuman().getServerId()){
                //日志会用到masterId
                ceng = (int)(masterId % 100);
            }
            proxy.getPublicCarParkInfo(humanObj.id, humanObj.getHuman().getServerId(), ceng);
            proxy.listenResult(this::_result_getCarParkMsg, "humanObj",humanObj, "type",type, "masterId",masterId);
        } else if(type == HUMAM_SPOT) {
            // 获取玩家车库信息
            CarParkServiceProxy proxy = CarParkServiceProxy.newInstance();
            // 检查是否在收藏列表中
            boolean isCollected = false;
            if (humanObj.operation.carPark2 != null) {
                List<Long> collectList = Utils.strToLongList(humanObj.operation.carPark2.getCollectList());
                isCollected = collectList != null && collectList.contains(masterId);
            }
            proxy.getHumanCarParkInfo(humanObj.id, masterId, isCollected);
            proxy.listenResult(this::_result_getCarParkMsg, "humanObj", humanObj, "type", type, "masterId", masterId);
        }
    }

    private void _result_getCarParkMsg(Param results, Param context){
        HumanObject humanObj = context.get("humanObj");
        int type = context.get("type");
        long masterId = context.get("masterId");

        MsgCarPark.car_park_info_s2c.Builder msg = results.get();
        if(msg == null){
            return;
        }
        // 获取CD信息
        CarPark2 carPark2 = humanObj.operation.carPark2;
        if(carPark2 != null) {
            Map<Long, Integer> cdMap = Utils.jsonToMapLongInt(carPark2.getCdMap());
            int cdEndTime = 0;

            if(type == PUBLIC_SPOT) {
                // 公共车库CD
                cdEndTime = cdMap.getOrDefault((long)PUBLIC_SPOT, 0);
            } else if(type == HUMAM_SPOT) {
                // 玩家车库CD
                cdEndTime = cdMap.getOrDefault(masterId, 0);
            }
            // 添加CD时间到消息扩展字段
            msg.addExt(Define.p_key_value.newBuilder()
                    .setK(CarParkType.EXT_PROTECT_TIME)
                    .setV(cdEndTime)
                    .build());
        }
        humanObj.sendMsg(msg.build());
    }

    public String getHumanPlace(CarPark carPark, int index) {
        switch (index) {
            case 1:
                return carPark.getSpace1();
            case 2:
                return carPark.getSpace2();
            case 3:
                return carPark.getSpace3();
            case 4:
                return carPark.getSpace4();
        }
        return null;
    }

    public void setHumanPlace(CarPark carPark, int index, String jsonStr) {
        switch (index) {
            case 1:
                carPark.setSpace1(jsonStr);
                break;
            case 2:
                carPark.setSpace2(jsonStr);
                break;
            case 3:
                carPark.setSpace3(jsonStr);
                break;
            case 4:
                carPark.setSpace4(jsonStr);
                break;
        }
    }

    public String getPlace(CarPartAbstract carPartAbstract, int index) {
        int modIndex = index % 10;
        if (modIndex == 0) {
            modIndex = 10;
        }
        switch (modIndex) {
            case 1:
                return carPartAbstract.getSpace1();
            case 2:
                return carPartAbstract.getSpace2();
            case 3:
                return carPartAbstract.getSpace3();
            case 4:
                return carPartAbstract.getSpace4();
            case 5:
                return carPartAbstract.getSpace5();
            case 6:
                return carPartAbstract.getSpace6();
            case 7:
                return carPartAbstract.getSpace7();
            case 8:
                return carPartAbstract.getSpace8();
            case 9:
                return carPartAbstract.getSpace9();
            case 10:
                return carPartAbstract.getSpace10();
        }
        return null;
    }

    public void setPlace(CarPartAbstract carPartAbstract, int index, String jsonStr) {
        int modIndex = index % 10;
        if (modIndex == 0) {
            modIndex = 10;
        }
        switch (modIndex) {
            case 1:
                carPartAbstract.setSpace1(jsonStr);
                break;
            case 2:
                carPartAbstract.setSpace2(jsonStr);
                break;
            case 3:
                carPartAbstract.setSpace3(jsonStr);
                break;
            case 4:
                carPartAbstract.setSpace4(jsonStr);
                break;
            case 5:
                carPartAbstract.setSpace5(jsonStr);
                break;
            case 6:
                carPartAbstract.setSpace6(jsonStr);
                break;
            case 7:
                carPartAbstract.setSpace7(jsonStr);
                break;
            case 8:
                carPartAbstract.setSpace8(jsonStr);
                break;
            case 9:
                carPartAbstract.setSpace9(jsonStr);
                break;
            case 10:
                carPartAbstract.setSpace10(jsonStr);
                break;
        }
    }

    public void clearCrossCarPark(CarParkCross carParkCross){
        for(int i = 1; i <= CROSS_SPACE_NUM; i++){
            setPlace(carParkCross, i, "{}");
        }
    }

    private void updateCrossPlace(CarParkCross carPartCross, int pos, ParkSpaceVo vo) {
        String lockKey = Utils.createStr("{}.{}.{}",RedisKeys.crossParkLock,carPartCross.getId(),pos);
        if(!RedisTools.lock(EntityManager.redisClient,lockKey,"1")){
            return;
        }
        setPlace(carPartCross, pos%CROSS_SPACE_NUM, vo.toJsonStr());
        carPartCross.update();
        carPartCross.reset();
        RedisTools.unLock(EntityManager.redisClient,lockKey);
    }

    public long getPublicParkCengId(long humanId, int ceng){
        return Utils.getServerIdByHumanId(humanId)*100 + ceng;
    }

    public long getPublicParkCengIdBy(int serverId, int ceng){
        return serverId*100 + ceng;
    }

    public long getCrossParkCengId(int zone, int groupIndex, int ceng){
        return (long) (zone * Math.pow(10, 8) + groupIndex * Math.pow(10, 2) + ceng);
    }

    public Define.p_car_park_space.Builder to_p_car_park_space(int pos, ParkSpaceVo vo, Human human, Human2 human2) {
        Define.p_car_park_space.Builder space = Define.p_car_park_space.newBuilder();
        space.setPos(pos);
        space.setRoleId(vo.humanId);
        space.setMountId(vo.mountId);
        space.setMountLev(vo.mountLv);
        space.setStartTime(vo.startTime);
        if(human != null){
            space.setInfoList(to_p_role_change(human,human2.getGuildName()));
            space.setFigure(HumanManager.inst().to_p_role_figure(human, human2));
            space.addAllExt(to_p_key_value_list(vo));
        }
        space.setCarMasterName(human == null ? "" : human.getName());

        return space;
    }

    public Define.p_car_park_space.Builder to_p_car_park_space(int pos, ParkSpaceVo vo) {
        Define.p_car_park_space.Builder space = Define.p_car_park_space.newBuilder();
        space.setPos(pos);
        space.setRoleId(vo.humanId);
        space.setMountId(vo.mountId);
        space.setMountLev(vo.mountLv);
        space.setStartTime(vo.startTime);
        space.setInfoList(vo.pRoleChange);
        space.setFigure(vo.pRoleFigure);
        space.addAllExt(to_p_key_value_list(vo));
        space.setCarMasterName("");

        return space;
    }

    public Define.p_role_change.Builder to_p_role_change(Human human, String guildName){
        Define.p_role_change.Builder builder = Define.p_role_change.newBuilder();
        builder.addKs(Define.p_key_string.newBuilder().setK(RoleInfoKey.ROLE_ATTR_NAME.getKey()).setS(human.getName()));
        builder.addKv(Define.p_key_value.newBuilder().setK(RoleInfoKey.ROLE_ATTR_LVL.getKey()).setV(human.getLevel()));
        builder.addKv(Define.p_key_value.newBuilder().setK(RoleInfoKey.ROLE_ATTR_POWER_SHOW.getKey()).setV(new BigDecimal(human.getCombat()).longValue()));
        builder.addKv(Define.p_key_value.newBuilder().setK(RoleInfoKey.ROLE_ATTR_HEAD_ID.getKey()).setV(human.getHeadSn()));
        builder.addKs(Define.p_key_string.newBuilder().setK(RoleInfoKey.ROLE_ATTR_HEAD_URL.getKey()).setS(""));
        builder.addKv(Define.p_key_value.newBuilder().setK(RoleInfoKey.ROLE_ATTR_HEAD_FRAME_ID.getKey()).setV(human.getCurrentHeadFrameSn()));
        builder.addKs(Define.p_key_string.newBuilder().setK(RoleInfoKey.ROLE_ATTR_GUILD_NAME.getKey()).setS(guildName));
        builder.addKv(Define.p_key_value.newBuilder().setK(RoleInfoKey.ROLE_ATTR_SERVER.getKey()).setV(Utils.getOldServerId(human.getId()) % Utils.intValue(Config.GAME_SERVER_PREFIX)));
        //只能往后面加不能插入
        return builder;
    }

    public List<Define.p_key_value> to_p_key_value_list(ParkSpaceVo vo){
        List<Define.p_key_value> list = new ArrayList<>();
        list.add(Define.p_key_value.newBuilder().setK(EXT2_CAR_HP).setV(vo.hp).build());
        list.add(Define.p_key_value.newBuilder().setK(EXT2_CAR_MAX_HP).setV(10000).build());
        list.add(Define.p_key_value.newBuilder().setK(EXT2_CAN_ASK_HELP).setV(vo.protectRatio).build());
        vo.resetBuffSn();
        list.add(Define.p_key_value.newBuilder().setK(EXT2_PVP_DEBUFF_TIME).setV(vo.buffEndTime).build());
        list.add(Define.p_key_value.newBuilder().setK(EXT2_PVP_DEBUFF_SN).setV(vo.buffSn).build());
        list.add(Define.p_key_value.newBuilder().setK(EXT2_EARN_PER_MIN).setV(vo.carCoinSpeed).build());
        list.add(Define.p_key_value.newBuilder().setK(EXT2_REWARD).setV(vo.carCoin).build());
        list.add(Define.p_key_value.newBuilder().setK(EXT2_PROTECT_TIME).setV(vo.protectEndTime).build());
        return list;
    }

    /**
     * 停车的车辆信息
     */
    public void on_car_park_car_info_c2s(HumanObject humanObj) {
        CarParkServiceProxy proxy = CarParkServiceProxy.newInstance();
        proxy.getCarParkCarInfo(humanObj.id);
        proxy.listenResult(this::_result_getCarParkCarInfoMsg,"humanObj", humanObj);
    }

    private void _result_getCarParkCarInfoMsg(Param results, Param context){
        HumanObject humanObj = context.get("humanObj");
        MsgCarPark.car_park_car_info_s2c.Builder msg = results.get();
        if(msg == null){
            Mount mount = humanObj.operation.mount;
            if(mount == null) {
                return;
            }
            msg = MsgCarPark.car_park_car_info_s2c.newBuilder();
            Map<Integer,Integer> skinLvMap = Utils.jsonToMapIntInt(mount.getSkinLvMap());
            for (Map.Entry<Integer, Integer> entry : skinLvMap.entrySet()) {
                msg.addCarList(to_p_car_park_car(entry.getKey(), new MountVo(1), null));
            }
        }
        humanObj.sendMsg(msg);
    }

    public Define.p_car_park_car.Builder to_p_car_park_car(int mountId, MountVo vo, ParkSpaceVo parkSpaceVo) {
        Define.p_car_park_car.Builder car = Define.p_car_park_car.newBuilder();
        car.setMountId(mountId);
        car.setCarLev(vo.lv);
        car.setCarExp(vo.exp);
        car.setMinute(vo.parkTime/60);
        if(parkSpaceVo == null){
            Define.p_car_park_parking.Builder parking = Define.p_car_park_parking.newBuilder();
            parking.setType(0);
            parking.setMasterId(0);
            parking.setPos(0);
            parking.setStartTime(0);
            parking.setProtectRatio(0);
            parking.setIsReplace(0);
            parking.addRewardList(Define.p_reward.newBuilder().setGtid(ItemConstants.goods_菇车币).setNum(0));
            parking.addRewardList(Define.p_reward.newBuilder().setGtid(ItemConstants.goods_菇车经验).setNum(0));
            car.setParkingData(parking);
            return car;
        }
        Define.p_car_park_parking.Builder parking = Define.p_car_park_parking.newBuilder();
        parking.setType(vo.parkType);
        parking.setMasterId(parkSpaceVo.masterId);
        parking.setPos(vo.pos);
        parking.setStartTime(parkSpaceVo.startTime);
        parking.setProtectRatio(parkSpaceVo.protectRatio);
        parking.setIsReplace(parkSpaceVo.isReplace);
        parking.addAllRewardList(ProduceManager.inst().to_p_rewardList(parkSpaceVo.rewardItemMap));
        parking.addRewardList(Define.p_reward.newBuilder().setGtid(ItemConstants.goods_菇车币).setNum(parkSpaceVo.carCoin));
        parking.addRewardList(Define.p_reward.newBuilder().setGtid(ItemConstants.goods_菇车经验).setNum(parkSpaceVo.carExp));
        parking.addAllExt(to_p_key_value_list3(parkSpaceVo));
        if(parkSpaceVo.pRoleChange != null){
            parking.setInfoList(parkSpaceVo.pRoleChange);
        }else {
            Log.carPark.error("parkSpaceVo.pRoleChange is null,mountId={},mountvo.masterID={},spaceVo.masterId={},spaceVo.humanId={}",mountId,vo.masterId,parkSpaceVo.masterId,parkSpaceVo.humanId);
        }
        car.setParkingData(parking);
        return car;
    }

    private List<Define.p_key_value> to_p_key_value_list3(ParkSpaceVo vo){
        List<Define.p_key_value> list = new ArrayList<>();
        list.add(Define.p_key_value.newBuilder().setK(EXT3_CAR_HP).setV(vo.hp).build());
        list.add(Define.p_key_value.newBuilder().setK(EXT3_CAR_MAX_HP).setV(10000).build());
        list.add(Define.p_key_value.newBuilder().setK(EXT3_EARN_PER_MIN1).setV(vo.carCoinSpeed).build());
        list.add(Define.p_key_value.newBuilder().setK(EXT3_EARN_PER_MIN2).setV(vo.carExpSpeed).build());
        vo.resetBuffSn();
        list.add(Define.p_key_value.newBuilder().setK(EXT3_PVP_DEBUFF_SN).setV(vo.buffSn).build());
        list.add(Define.p_key_value.newBuilder().setK(EXT3_PVP_DEBUFF_TIME).setV(vo.buffEndTime).build());
        list.add(Define.p_key_value.newBuilder().setK(EXT3_PROTECT_TIME).setV(vo.protectEndTime).build());
        return list;
    }

    public void addMountVo(HumanObject humanObj, int unlock) {
        // 调用Service处理
        Log.carPark.info("addMountVo:unlock={},humanId={}",unlock,humanObj.id);
        CarParkServiceProxy proxy = CarParkServiceProxy.newInstance();
        proxy.addMountVo(humanObj.id, unlock);
        proxy.listenResult(this::_result_carParkCarUpMsg,
                "humanObj", humanObj,
                "costSn", 0,
                "costNum", 0);
    }

    /**
     * 车辆升级C2S消息
     */
    public void on_car_park_car_up_c2s(HumanObject humanObj, int mountId, int costNum) {
        // 先获取配置
        ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.park_exp_book_num);
        int costSn = confGlobal.intArray[0];

        // 先在Manager层扣除道具
        if(!ProduceManager.inst().checkAndCostItem(humanObj, costSn, costNum, MoneyItemLogKey.坐骑改装升级).success) {
            return;
        }

        // 调用Service处理
        CarParkServiceProxy proxy = CarParkServiceProxy.newInstance();
        proxy.carParkCarUp(humanObj.id, mountId, costNum);
        proxy.listenResult(this::_result_carParkCarUpMsg,
            "humanObj", humanObj,
            "costSn", costSn,
            "costNum", costNum);
    }

    private void _result_carParkCarUpMsg(Param results, Param context) {
        HumanObject humanObj = context.get("humanObj");
        int costSn = context.get("costSn");
        int costNum = context.get("costNum");

        if(results.get("mountId") == null) {
            // Service处理失败,回滚道具
            ProduceManager.inst().produceAdd(humanObj, costSn, costNum, MoneyItemLogKey.坐骑改装升级);
            return;
        }

        int mountId = results.get("mountId");
        MountVo vo = results.get("vo");

        int actualCostNum = results.get("actualCostNum");

        // 如果实际使用的数量小于扣除的数,回滚差额
        if(actualCostNum < costNum) {
            int returnNum = costNum - actualCostNum;
            ProduceManager.inst().produceAdd(humanObj, costSn, returnNum, MoneyItemLogKey.坐骑改装升级);
        }
        // 更新属性
        Map<Integer,MountVo> mountVoMap = results.get("mountVoMap");
        if(mountVoMap!=null){
            updatePorpCalcPower(humanObj, mountVoMap);
        }

        // 发送消息
        MsgCarPark.car_park_car_up_s2c.Builder msg = MsgCarPark.car_park_car_up_s2c.newBuilder();
        msg.setUpdateCar(to_p_car_park_car(mountId, vo, null));
        humanObj.sendMsg(msg);
    }


    /**
     * 更新属性
     */
    private void updatePorpCalcPower(HumanObject humanObj, Map<Integer,MountVo> parkSpaceMap) {
        PropCalc propCalc = new PropCalc();
        int power = 0;
        for (Map.Entry<Integer, MountVo> entry : parkSpaceMap.entrySet()) {
            MountVo vo = entry.getValue();
            ConfParkingMount_0 conf = ConfParkingMount_0.get(entry.getKey(), vo.lv);
            if(conf == null) {
                Log.game.error("on_car_park_car_up_c2s:找不到车辆配置id={},lv={}", entry.getKey(), vo.lv);
                continue;
            }
            propCalc.plus(conf.own_attrs);
            power += conf.power;
        }
        humanObj.dataPers.unitPropPlus.setFieldRead(UnitPropPlus.K.carPark, propCalc.toJSONStr());
        HumanManager.inst().updatePowerPar(humanObj, EModule.carPart, power);
        PropManager.inst().propCalc(humanObj, CombatChangeLog.停车改装升级);
    }

    public void on_car_park_parking_start_c2s(HumanObject humanObj, int type, long masterId, long mountId, int pos, int isProtect, int isReplace) {
        // 基础检查
        CarPark2 carPark2 = humanObj.operation.carPark2;
        if (carPark2 == null) {
            return;
        }
        Map<Long,Integer> cdMap = clearParkCdMap(carPark2);
        if(type == PUBLIC_SPOT){
            if(cdMap.containsKey((long)PUBLIC_SPOT)){
                humanObj.sendMsg(MsgError.error_info_s2c.newBuilder().setCode(248));//通道停放冷却中
                Log.carPark.error("on_car_park_parking_start_c2s:停车位cd未过期，type={}", type);
                return;
            }
        }else if(type == HUMAM_SPOT){
            //cd检查
            if(masterId != humanObj.id && cdMap.containsKey(masterId)){
                humanObj.sendMsg(MsgError.error_info_s2c.newBuilder().setCode(248));//通道停放冷却中
                Log.carPark.error("on_car_park_parking_start_c2s:停车位cd未过期，type={}", type);
                return;
            }
            if(Utils.getServerIdByHumanId(masterId) != Utils.getServerIdByHumanId(humanObj.id)){
                humanObj.sendMsg(MsgError.error_info_s2c.newBuilder().setCode(280));//跨服停车位
                Log.carPark.error("on_car_park_parking_start_c2s:跨服停车位，type={}", type);
                return;
            }
        }
        //判断坐骑是否存在
        Mount mount = humanObj.operation.mount;
        if(mount == null) {
            humanObj.sendMsg(MsgError.error_info_s2c.newBuilder().setCode(246)); // 坐骑不存在
            Log.carPark.error("on_car_park_parking_start_c2s:坐骑不存在，humanId={}", humanObj.id);
            return;
        }
        Map<Integer,Integer> skinLvMap = Utils.jsonToMapIntInt(mount.getSkinLvMap());
        if(!skinLvMap.containsKey((int)mountId)) {
            humanObj.sendMsg(MsgError.error_info_s2c.newBuilder().setCode(247)); // 指定坐骑不存在
            Log.carPark.error("on_car_park_parking_start_c2s:指定坐骑不存在，humanId={}, mountId={}", humanObj.id, mountId);
            return;
        }

        // 构造角色信息
        Define.p_role_change roleChange = to_p_role_change(humanObj.getHuman(),humanObj.getHuman2().getGuildName()).build();
        Define.p_role_figure roleFigure = HumanManager.inst().to_p_role_figure(humanObj.getHuman(), humanObj.getHuman2()).build();
        // 调用Service处理
        CarParkServiceProxy proxy = CarParkServiceProxy.newInstance();
        proxy.carParkParkingStart(humanObj.id, type, masterId, mountId, pos, isProtect, isReplace, roleChange, roleFigure);
        proxy.listenResult(this::_result_carParkParkingStart,"humanObj", humanObj, "type",type, "masterId",masterId);
    }

    private void _result_carParkParkingStart(Param results, Param context) {
        HumanObject humanObj = context.get("humanObj");
        int type = context.get("type");
        long masterId = context.get("masterId");
        
        // 先获取消息对象
        MsgCarPark.car_park_parking_start_s2c msg = results.get("msg");
        if (msg == null) {
            // 获取错误码
            Integer errCode = results.get("errCode");
            if(errCode != null && errCode > 0){
                humanObj.sendMsg(MsgError.error_info_s2c.newBuilder().setCode(errCode));
            }
            return;
        }
        humanObj.sendMsg(msg);
        on_car_park_car_info_c2s(humanObj);
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_回归任务, TaskConditionTypeKey.TASK_TYPE_94, 1);
    }

    public ParkSpaceVo createParkSpaceVo(long masterId, long id, int mountId, MountVo vo, int[] attr, int protectAttr, Define.p_role_change roleChange, Define.p_role_figure roleFigure) {
        int now = (int)(Port.getTime()/Time.SEC);
        ParkSpaceVo parkSpaceVo = new ParkSpaceVo();
        parkSpaceVo.masterId = masterId;
        parkSpaceVo.humanId = id;
        parkSpaceVo.mountId = mountId;
        parkSpaceVo.hp = 10000;
        parkSpaceVo.startTime = now;
        parkSpaceVo.lastGetTime = parkSpaceVo.startTime;
        parkSpaceVo.mountLv = vo.lv;
        parkSpaceVo.mountPlacingTime = vo.parkTime;
        parkSpaceVo.protectEndTime = now + (ConfGlobal.get(ConfGlobalKey.park_pixel).value+protectAttr) * 60;
        reCaculateCarReward(attr, parkSpaceVo);
        parkSpaceVo.pRoleChange = roleChange;
        parkSpaceVo.pRoleFigure = roleFigure;
        return parkSpaceVo;
    }

    public ParkSpaceVo createHumanParkSpaceVo(CarPark carPark,long humanId, int mountId, MountVo mountVo, int isProtect, int isReplace, int protectAttr,
                 Define.p_role_change roleChange, Define.p_role_figure roleFigure) {
        int now = (int)(Port.getTime()/Time.SEC);
        ParkSpaceVo vo = new ParkSpaceVo();
        vo.masterId = carPark.getId();
        vo.humanId = humanId;
        vo.mountId = mountId;
        if(isProtect!=0 && carPark.getIsProtect()!=0){
            vo.protectRatio = carPark.getProtectRatio();
        }
        vo.hp = 10000;
        vo.isReplace = isReplace;
        vo.startTime = now;
        vo.lastGetTime = vo.startTime;
        vo.mountLv = mountVo.lv;
        vo.mountPlacingTime = mountVo.parkTime;
        vo.protectEndTime = now+(ConfGlobal.get(ConfGlobalKey.park_pixel).value+protectAttr) * 60;
        reCaculateCarReward(getAttrHumanCarPark(carPark), vo);
        vo.pRoleChange = roleChange;
        vo.pRoleFigure = roleFigure;
        return vo;
    }

    public void reCaculateCarReward(int[] attr, ParkSpaceVo vo) {
        long now = Port.getTime();
        long lastGetTimeMilli = vo.lastGetTime * Time.SEC;
        
        if(!Utils.isToday(lastGetTimeMilli)){
            long zeroTime = Utils.getTomorrowZeroTime(vo.lastGetTime*Time.SEC);
            reCaculateCarReward(attr, vo, (int)(zeroTime/Time.SEC));
            vo.mountPlacingTime = 0;
            reCaculateCarReward(attr, vo, (int)(now/Time.SEC));
        }else {
            reCaculateCarReward(attr, vo, (int)(now/Time.SEC));
        }
        reCaculateSpaceBuff(vo);
    }

    private int getCarExpConfig(){
        return ConfGlobal.get(ConfGlobalKey.park_exp_rate).intArray[1];
    }

    private void reCaculateCarReward(int[] attr, ParkSpaceVo vo, int timeStamp) {
        // 确保时间戳合法
        if (timeStamp < vo.lastGetTime) {
            Log.game.error("reCaculateCarReward: 时间戳错误 lastGetTime={}, timeStamp={}", 
                vo.lastGetTime, timeStamp);
            return;
        }
        
        int placingMaxTime = ConfGlobal.get(ConfGlobalKey.park_time_limit).value * 60;
        int endTime = vo.startTime + placingMaxTime;
        if(vo.lastGetTime >= endTime) {
            return;
        }
        if (timeStamp >= endTime) {
            timeStamp = endTime;
        }
        int timeDiff = timeStamp - vo.lastGetTime;
        int MAX_LIMIT_TIME = ConfGlobal.get(ConfGlobalKey.park_mount_limit_time).value * 60;
        int limitTime = MAX_LIMIT_TIME-vo.mountPlacingTime;
        limitTime = limitTime > 0 ? limitTime : 0;
        if (timeDiff < limitTime) {
            reCaculateCarReward(attr, vo, timeStamp, false);
        } else {
            if(limitTime > 0){
                int timeStamp1 = timeStamp-timeDiff+limitTime;
                reCaculateCarReward(attr, vo, timeStamp1, false);
            }
            reCaculateCarReward(attr, vo, timeStamp, true);
        }
        vo.mountPlacingTime += timeDiff;
    }

    private void reCaculateCarReward(int[] attr, ParkSpaceVo vo, int timeStamp, boolean isDecay) {
        int oldMinutes = (vo.lastGetTime - vo.startTime)/60;
        int newMinutes = (timeStamp - vo.startTime)/60;
        
        // 确保时间间隔合法
        if (newMinutes < oldMinutes) {
            Log.game.error("reCaculateCarReward: 分钟计算错误 oldMinutes={}, newMinutes={}, lastGetTime={}, timeStamp={}", 
                oldMinutes, newMinutes, vo.lastGetTime, timeStamp);
            return;
        }
        
        ConfParkingMount_0 conf = ConfParkingMount_0.get(vo.mountId, vo.mountLv);
        if(conf == null) {
            Log.game.error("reCaculateCarReward:找不到车辆配置id={},lv={}", vo.mountId, vo.mountLv);
            return;
        }
        int addCarCoin = 0;
        int addCarExp = 0;
        int addTenK = 0;
        List<ConfParkingTime> confParkingTimes = GlobalConfVal.parkingTimeList;
        for(ConfParkingTime confParkingTime : confParkingTimes){
            ConfParkingTime confPre = ConfParkingTime.get(confParkingTime.sn-1);
            if(confPre == null){
                continue;
            }
            if(oldMinutes >= confParkingTime.interval){
                continue;
            }
            if(newMinutes >= confParkingTime.interval){
                int duration = confParkingTime.interval - oldMinutes;
                vo.carCoinSpeed = (int)Math.round(conf.rate * confPre.rate_alter / 10000.0 * (attr[SKIN_ATTR_COIN]+10000) / 10000.0);
                if(isDecay){
                    vo.carCoinSpeed = vo.carCoinSpeed * ConfGlobal.get(ConfGlobalKey.park_mount_limit_rate).value / 100;
                    vo.carExpSpeed = 0;
                }else {
                    vo.carExpSpeed = (int)Math.round(getCarExpConfig() * confPre.rate_alter / 10000.0 * (attr[SKIN_ATTR_EXP]+10000) / 10000.0);
                    addTenK += confParkingTime.drop_num * (attr[SKIN_ATTR_SPEC]+10000);
                }
                addCarCoin += vo.carCoinSpeed * duration;
                addCarExp += vo.carExpSpeed * duration;
                oldMinutes = confParkingTime.interval;

            }else {
                int duration = newMinutes - oldMinutes;
                vo.carCoinSpeed = (int)Math.round(conf.rate * confPre.rate_alter / 10000.0 * (attr[SKIN_ATTR_COIN]+10000) / 10000.0);
                if(isDecay) {
                    vo.carCoinSpeed = vo.carCoinSpeed * ConfGlobal.get(ConfGlobalKey.park_mount_limit_rate).value / 100;
                    vo.carExpSpeed = 0;
                }else {
                    vo.carExpSpeed = (int)Math.round(getCarExpConfig() * confPre.rate_alter / 10000.0 * (attr[SKIN_ATTR_EXP]+10000) / 10000.0);
                }
                addCarCoin += vo.carCoinSpeed * duration;
                addCarExp += vo.carExpSpeed * duration;
                break;
            }
        }
        vo.carCoin += addCarCoin;
        vo.carExp += addCarExp;
        vo.dropTenK += addTenK;

        int num = vo.dropTenK / 10000;
        for (int i = 0; i < num; i++) {
            vo.addRewardItem(ProduceManager.inst().getDropMap(conf.drop_id));
        }
        vo.dropTenK = vo.dropTenK % 10000;

        int partTimeLimit = vo.startTime + ConfGlobal.get(ConfGlobalKey.park_time_limit).value*60;
        vo.lastGetTime = timeStamp < partTimeLimit ? timeStamp : partTimeLimit;
    }

    private void reCaculateSpaceBuff(ParkSpaceVo vo) {
        int now = (int)(Port.getTime()/Time.SEC);
        if(vo.protectEndTime > 0 && vo.protectEndTime < now) {
            vo.protectEndTime = 0;
        }
        if(vo.buffEndTime > 0) {
            ConfParkPvpDebuff_0 conf = ConfParkPvpDebuff_0.get(vo.buffSn, BUFF_DEFEND);
            if(conf == null) {
                Log.game.error("reCaculateSpaceBuff:找不到PVP减益buff配置sn={},type={}", vo.buffSn, BUFF_DEFEND);
                return;
            }
            //每过conf.time分，buffSn-1直到buffSn==0
            int duration = (now - vo.buffEndTime) / (conf.time*60);
            vo.buffEndTime += duration * conf.time*60;
            vo.buffSn -= duration;
            if(vo.buffSn <= 0) {
                vo.buffSn = 0;
                vo.buffEndTime = 0;
            }
        }
    }

    public int[] getAttrHumanCarPark(CarPark carPark) {
        Map<Integer, Integer> skinLvMap = Utils.jsonToMapIntInt(carPark.getSkinLvMap());
        return getAttrHumanCarPark(skinLvMap);
    }

    public int[] getAttrHumanCarPark(Map<Integer, Integer> skinLvMap) {
        int[] attr = new int[4];
        for (Map.Entry<Integer, Integer> entry : skinLvMap.entrySet()) {
            int sn = entry.getKey();
            int lv = entry.getValue();
            ConfParkingDesign_0 conf = ConfParkingDesign_0.get(sn, lv);
            if (conf == null) {
                Log.game.error("getAttrHumanCarPark:找不到停车场装扮配置id={},lv={}", sn, lv);
                continue;
            }
            String[] effects = conf.effect.split("\\|");
            attr = getAttrFromEffects(effects, attr);
        }
        return attr;
    }

    public int[] getAttrPublicCarPark() {
        int[] attr = new int[4];
        ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.park_public_buff);
        String[] effects = confGlobal.strValue.split("\\|");
        attr = getAttrFromEffects(effects, attr);
        return attr;
    }

    public int[] getAttrCrossCarPark() {
        int[] attr = new int[4];
        ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.park_cross_buff);
        String[] effects = confGlobal.strValue.split("\\|");
        attr = getAttrFromEffects(effects, attr);
        return attr;
    }

    private int[] getAttrFromEffects(String[] effects, int[] attr) {
        if(effects.length == 0||effects[0].equals("")) {
            return attr;
        }
        for (String effect : effects) {
            String[] strs = effect.split(",");
            if (strs.length != 2) {
                Log.game.error("getAttrFromEffects:配置effect字段错误{}", effect);
                continue;
            }
            if (strs[0].equals("coin")) {
                attr[0] += Utils.intValue(strs[1]);
            } else if (strs[0].equals("exp")) {
                attr[1] += Utils.intValue(strs[1]);
            } else if (strs[0].equals("spec")) {
                attr[2] += Utils.intValue(strs[1]);
            } else if (strs[0].equals("protect")) {
                attr[3] += Utils.intValue(strs[1]);
            }
        }
        return attr;
    }

    /**
     * 处理停车停止C2S消息
     */
    public void on_car_park_parking_stop_c2s(HumanObject humanObj, long mountId) {
        // 基础检查
        CarPark2 carPark2 = humanObj.operation.carPark2;
        if (carPark2 == null) {
            return;
        }

        // 构造角色信息
        Define.p_role_change roleChange = to_p_role_change(humanObj.getHuman(),humanObj.getHuman2().getGuildName()).build();
        Define.p_role_figure roleFigure = HumanManager.inst().to_p_role_figure(humanObj.getHuman(), humanObj.getHuman2()).build();

        // 调用Service处理
        CarParkServiceProxy proxy = CarParkServiceProxy.newInstance();
        proxy.carParkParkingStop(humanObj.id, mountId, roleChange, roleFigure);
        proxy.listenResult(this::_result_carParkParkingStop, "humanObj", humanObj);
    }

    /**
     * 处理停车停止返回结果
     */
    private void _result_carParkParkingStop(Param results, Param context) {
        HumanObject humanObj = context.get("humanObj");
        if (results == null || results.isEmpty()) {
            return;
        }

        // 获取奖励信息
        Map<Integer, Integer> rewards = results.get("rewards");
        if (rewards != null && !rewards.isEmpty() && !rewards.values().isEmpty() &&
                rewards.values().stream().anyMatch(value -> value != 0)) {
            // 发放奖励
            ProduceManager.inst().produceAdd(humanObj, rewards, MoneyItemLogKey.停车奖励);
        }

        // 更新属性
        boolean propUpdate = results.get("propUpdate");
        if (propUpdate) {
            // 获取坐骑信息用于更新属性
            Map<Integer, MountVo> mountMap = results.get("mountMap");
            if (mountMap != null) {
                updatePorpCalcPower(humanObj, mountMap);
            }
            PropManager.inst().propCalc(humanObj, CombatChangeLog.停车改装升级);
        }

        // 更新CD
        CarPark2 carPark2 = humanObj.operation.carPark2;
        MountVo oldMountVo = results.get("oldMountVo");
        Map<Long, Integer> cdMap = Utils.jsonToMapLongInt(carPark2.getCdMap());
        if(oldMountVo.parkType == PUBLIC_SPOT){
            cdMap.put((long)PUBLIC_SPOT, (int)(Port.getTime()/Time.SEC) + ConfGlobal.get(ConfGlobalKey.park_interval_limit).value*60);
        }else if(oldMountVo.parkType == HUMAM_SPOT){
            if(oldMountVo.masterId != humanObj.id){
                cdMap.put(oldMountVo.masterId, (int)(Port.getTime()/Time.SEC) + ConfGlobal.get(ConfGlobalKey.park_interval_limit).value*60);
            }
        }
        carPark2.setCdMap(Utils.mapLongIntToJSON(cdMap));

        // 发消息通知客户端

        MsgCarPark.car_park_parking_stop_s2c.Builder msg = MsgCarPark.car_park_parking_stop_s2c.newBuilder();
        msg.setType(oldMountVo.parkType);
        msg.setMasterId(oldMountVo.masterId);
        if (rewards != null && !rewards.isEmpty() && !rewards.values().isEmpty() &&
                rewards.values().stream().anyMatch(value -> value != 0)) {
            msg.addAllRewardList(ProduceManager.inst().to_p_rewardList(rewards));
        }
        msg.setIsDie(0);
        Define.p_car_park_space.Builder p_car_park_space = Define.p_car_park_space.newBuilder();
        p_car_park_space.setPos(oldMountVo.pos);
        msg.setSpace(p_car_park_space);
        humanObj.sendMsg(msg);

        on_car_park_car_info_c2s(humanObj);
    }


    public void claimReward(HumanObject humanObj, ParkSpaceVo vo, long parkId){
        CarPark carPark = humanObj.operation.carPark;
        if(carPark == null) {
            return;
        }
        Map<Integer,MountVo> mountMap = MountVo.mapFromJsonStr(carPark.getMountMap());
        MountVo mountVo = mountMap.get(vo.mountId);
        if(mountVo == null) {
            return;
        }
        if(vo.protectRatio > 0){
            //管理费
            int managerFee = vo.carCoin * vo.protectRatio / 100;
            vo.carCoin = vo.carCoin * (1 - vo.protectRatio / 100);
            JSONObject jo = new JSONObject();

            JSONObject joTemp1 = new JSONObject();
            joTemp1.put(MailManager.MAIL_K_3, humanObj.getHuman().getName());
            jo.put(MailManager.MAIL_PARAM_1, joTemp1);

            JSONObject joTemp2 = new JSONObject();
            joTemp2.put(MailManager.MAIL_K_2, vo.mountId);
            jo.put(MailManager.MAIL_PARAM_2, joTemp2);

            JSONObject joTemp3 = new JSONObject();
            joTemp3.put(MailManager.MAIL_K_5,  (vo.lastGetTime - vo.startTime)/60);
            jo.put(MailManager.MAIL_PARAM_3, joTemp3);

            JSONObject joTemp4 = new JSONObject();
            joTemp4.put(MailManager.MAIL_K_4, vo.carCoin);
            jo.put(MailManager.MAIL_PARAM_4, joTemp4);

            JSONObject joTemp5 = new JSONObject();
            joTemp5.put(MailManager.MAIL_K_4, managerFee);
            jo.put(MailManager.MAIL_PARAM_5, joTemp5);

            JSONObject itemJSON = new JSONObject();
            itemJSON.put(Integer.toString(ItemConstants.goods_菇车币), managerFee);
            MailManager.inst().sendMail(parkId, MailManager.SYS_SENDER, 10149, "", jo.toJSONString(),itemJSON.toJSONString(), new Param());
        }
        if(parkId != 0 && parkId!=humanObj.id){
            sendParkFeeMail(humanObj.getHuman().getName(), parkId, vo);
        }
        //给玩家奖励
        vo.rewardItemMap.put(ItemConstants.goods_菇车币, vo.carCoin);
        ProduceManager.inst().produceAdd(humanObj, vo.rewardItemMap, MoneyItemLogKey.停车奖励);
        //给车辆经验
        mountVo.exp += vo.carExp;
        //如果停车结束时间是今天
        if(Utils.isToday(vo.lastGetTime*Time.SEC)){
            mountVo.parkTime = vo.mountPlacingTime;
        }
        ConfParkingMount_0 conf = ConfParkingMount_0.get(vo.mountId, mountVo.lv);
        if(conf == null) {
            Log.game.error("claimReward:找不到车辆配置id={},lv={}", vo.mountId, mountVo.lv);
            return;
        }
        if(mountVo.exp >= conf.expend) {
            mountVo.lv = conf.level;
        }
        updatePorpCalcPower(humanObj, mountMap);
        if (mountVo.parkType != CROSS_SPOT){
            MsgCarPark.car_park_parking_stop_s2c.Builder msg = MsgCarPark.car_park_parking_stop_s2c.newBuilder();
            msg.setType(mountVo.parkType);
            msg.setMasterId(mountVo.masterId);
            msg.addAllRewardList(ProduceManager.inst().to_p_rewardList(vo.rewardItemMap));
            msg.setIsDie(0);
            vo = new ParkSpaceVo();
            int pos = mountVo.pos;
            mountVo.clear();
            carPark.setMountMap(MountVo.mapToJsonStr(mountMap));
            carPark.update();
            msg.setSpace(to_p_car_park_space(pos, vo, humanObj.getHuman(),humanObj.getHuman2()));
            humanObj.sendMsg(msg);
        }else {
            MsgCarPark.cross_car_park_parking_stop_s2c.Builder msg = MsgCarPark.cross_car_park_parking_stop_s2c.newBuilder();
            msg.setId(mountVo.masterId);
            msg.addAllRewardList(ProduceManager.inst().to_p_rewardList(vo.rewardItemMap));
            vo = new ParkSpaceVo();
            int pos = mountVo.pos;
            mountVo.clear();
            carPark.setMountMap(MountVo.mapToJsonStr(mountMap));
            carPark.update();
            msg.setSpace(to_p_car_park_space(pos, vo, humanObj.getHuman(), humanObj.getHuman2()));
            humanObj.sendMsg(msg);
        }
    }

    public void on_car_park_combat_c2s(HumanObject humanObj, int type, long masterId, int pos) {
        //不是同一个服的不让抢
        if(!Util.isSameZone(humanObj.id,masterId)){
            humanObj.sendMsg(MsgError.error_info_s2c.newBuilder().setCode(1100002));//跨服停车位
            return;
        }
        // 设置战斗参数
        humanObj.combatType = type;
        humanObj.combatId = masterId;
        humanObj.combatForId = masterId;
        humanObj.combatParam = pos;

        // 调用Service处理
        CarParkServiceProxy proxy = CarParkServiceProxy.newInstance();
        proxy.carParkCombat(humanObj.id, type, masterId, pos);
        proxy.listenResult(this::_result_carParkCombat,
                "humanObj", humanObj,
                "type", type,
                "masterId", masterId,
                "pos", pos);
    }

    private void _result_carParkCombat(Param results, Param context) {
        HumanObject humanObj = context.get("humanObj");
        if (results.get("success") == null || (boolean)results.get("success") == false) {
            // 处理失败情况
            int errorCode = results.get("success") != null ? results.get("errorCode") : 0;
            humanObj.sendMsg(MsgError.error_info_s2c.newBuilder().setCode(errorCode));
            send_err_car_park_combat_s2c(humanObj, errorCode);
            return;
        }

        // 构建战斗消息
        MsgCarPark.car_park_combat_s2c.Builder msg = MsgCarPark.car_park_combat_s2c.newBuilder();
        msg.setCode(0);
        // 设置战斗种子
        humanObj.combatSeed = Utils.getTimeSec();
        msg.setSeed(humanObj.combatSeed);
        msg.setEid(results.get("targetHumanId"));
        // 攻击debuff技能ID
        int atkDebuffSkillId = results.get("atkDebuff") == null ? 0 : results.get("atkDebuff");
        // 防守debuff技能ID
        int defDebuffSkillId = results.get("defDebuff") == null ? 0 : results.get("defDebuff");

        // 设置攻击方数据
        int planType = HumanManager.inst().getPlanType(humanObj.getHumanExtInfo().getDefaultPlanArr(), HumanManager.PLAN_TYPE_HOME_BATTLE);
        Define.p_battle_role.Builder atkbuilder = humanObj.to_p_battle_role_builder(planType);
        if(atkDebuffSkillId > 0){
            atkbuilder.addExt(Define.p_key_value.newBuilder().setK(ParamKey.extPosType_5).setV(atkDebuffSkillId));
        }
        msg.setAtkData(atkbuilder);

        // 获取防守方ID
        long defHumanId;
        if ((int)results.get("isReplace") == 1) {
            defHumanId = context.get("masterId");
        } else {
            defHumanId = results.get("targetHumanId");
        }
        humanObj.combatId = defHumanId;

        HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance();
        prx.getHumanBrief2(defHumanId);
        prx.listenResult((Boolean timeOut, Param results1, Param ctx) -> {
            HumanBrief brief = results1.get("humanBrief");
            if (brief == null) {
                return;
            }
            int hp = results.get("hp");
            String propBouns = results.get("propBonus");
            PropCalc propCalc = new PropCalc(propBouns);

            HumanBriefVO humanBriefVO = new HumanBriefVO(brief);
            byte[] battleRole = humanBriefVO.getBattleRoleByPlanType(HumanManager.PLAN_TYPE_HOME_BATTLE);
            try{
                Define.p_battle_role.Builder defBuilder = PropManager.inst().recalculateAndUpdateBattleRole(Define.p_battle_role.parseFrom(battleRole), propCalc);

                humanObj.combatMaxHp = getMaxHp(defBuilder.getAttrListList());
                defBuilder.addExt(Define.p_key_value.newBuilder().setK(ParamKey.extPosType_2).setV(hp));
                if(defDebuffSkillId > 0){
                    defBuilder.addExt(Define.p_key_value.newBuilder().setK(ParamKey.extPosType_5).setV(defDebuffSkillId));
                }
                Define.p_battle_role defRole = defBuilder.build();
                msg.setDefData(defRole);
                humanObj.sendMsg(msg);
                humanObj.combatBriefVo = humanBriefVO;
            } catch (Exception e) {
                Log.carPark.error("p_battle_role error,humanId={},defHumanId={},e={}", humanObj.id, defHumanId, e.getMessage());
            }

        });
    }

    private long getMaxHp(List<Define.p_key_value> attrList){
        for (int i = 0; i < attrList.size(); i++) {
            Define.p_key_value attr = attrList.get(i);
            if (attr.getK() == PropKey.total_hp.getAttributeSn()) {
                return attr.getV();
            }
        }
        return 0;
    }

    private void send_err_car_park_combat_s2c(HumanObject humanObj, int code) {
        MsgCarPark.car_park_combat_s2c.Builder msg = MsgCarPark.car_park_combat_s2c.newBuilder();
        msg.setCode(code);
        msg.setEid(0);
        msg.setSeed(0);
        humanObj.sendMsg(msg);
    }

    public List<Integer> getReCaculateBuff(CarPark carPark, int type, boolean isSave) {
        List<Integer> buffList = new ArrayList<>();
        List<Integer> initialBuffList = new ArrayList<>(); // 记录初始状态
        // 根据类型获取buff
        switch (type) {
            case BUFF_DEFEND:
                buffList = Utils.strToIntList(carPark.getDefBuff());
                break;
            case BUFF_ATTACK:
                buffList = Utils.strToIntList(carPark.getAtkBuff());
                break;
            case BUFF_CROSS_DEFEND:
                buffList = Utils.strToIntList(carPark.getCrossDefbuff());
                break;
            case BUFF_CROSS_ATTACK:
                buffList = Utils.strToIntList(carPark.getCrossAtkbuff());
                break;
        }
        initialBuffList.addAll(buffList);
        // 确保buffList有两个元素
        if (buffList.size() != 2) {
            buffList.clear();
            buffList.add(0);
            buffList.add(0);
        }
        // 复用getRecalculateBuff的逻辑
        List<Integer> recalculatedBuff = getRecalculateBuff(buffList.get(0), buffList.get(1), type);

        // 如果buffList发生了变化且需要保存,则更新carPark
        if (isSave && !recalculatedBuff.equals(initialBuffList)) {
            setBuff(carPark, recalculatedBuff, type);
        }
        return recalculatedBuff;
    }

    /**
     * 重新计算buff值
     * @param buffSn buff等级
     * @param buffEndTime buff结束时间
     * @param type buff类型
     * @return List<Integer> - get(0)为当前buffSn，get(1)为当前buffEndTime
     */
    public List<Integer> getRecalculateBuff(int buffSn, int buffEndTime, int type) {
        List<Integer> buffList = new ArrayList<>();
        buffList.add(buffSn);
        buffList.add(buffEndTime);

        int now = (int)(Port.getTime() / Time.SEC);
        if(buffList.get(1) > 0) {
            // 如果是失败buff(buffSn=0)
            if(buffList.get(0) == 0) {
                if(buffList.get(1) < now) {
                    buffList.set(0, 0);
                    buffList.set(1, 0);
                }
                return buffList;
            }

            // 正常buff的处理
            ConfParkPvpDebuff_0 conf = ConfParkPvpDebuff_0.get(buffList.get(0), type);
            if(conf == null || conf.time == 0) {
                Log.game.error("recalculateBuff:找不到PVP减益buff配置sn={},type={}", buffList.get(0), type);
                return buffList;
            }

            // 计算已经过去了多少个时间周期
            int timePassed = Math.max(0, now - buffList.get(1));
            int duration = timePassed / (conf.time * 60);

            // 如果有时间周期过去
            if(duration > 0) {
                // 减少buff等级
                int newBuffSn = buffList.get(0) - duration;
                if(newBuffSn <= 0) {
                    // buff已结束
                    buffList.set(0, 0);
                    buffList.set(1, 0);
                } else {
                    // 更新buff等级和结束时间
                    buffList.set(0, newBuffSn);
                    // 计算新的结束时间,基于原始结束时间
                    int originalEndTime = buffList.get(1);
                    int newEndTime = originalEndTime + conf.time * 60;
                    buffList.set(1, newEndTime);
                }
            }
            if(buffList.get(1) < now) {
                buffList.set(0, 0);
                buffList.set(1, 0);
            }
        }
        return buffList;
    }



        private void setBuff(CarPark carPark, List<Integer> buff, int type){
        switch (type) {
            case BUFF_DEFEND:
                carPark.setDefBuff(Utils.listToString(buff));
                break;
            case BUFF_ATTACK:
                carPark.setAtkBuff(Utils.listToString(buff));
                break;
            case BUFF_CROSS_DEFEND:
                carPark.setCrossDefbuff(Utils.listToString(buff));
                break;
            case BUFF_CROSS_ATTACK:
                carPark.setCrossAtkbuff(Utils.listToString(buff));
                break;
        }
    }


    public void on_car_park_result_c2s(HumanObject humanObj, long winId, long hp) {
        // 调用Service处理
//        Log.capture.info("赢了={},最大hp={},剩余hp={}",humanObj.id == winId,humanObj.combatMaxHp,hp);
        CarParkServiceProxy proxy = CarParkServiceProxy.newInstance();
        long hpPerMyriad = hp * 10000 / humanObj.combatMaxHp ;
        hpPerMyriad = hpPerMyriad > 10000 ? 10000 : hpPerMyriad;
        proxy.carParkResult(humanObj.id, winId, hpPerMyriad, humanObj.combatType, humanObj.combatForId, (int)humanObj.combatParam, new BigDecimal(humanObj.getHuman().getCombat()).longValue());
        proxy.listenResult(this::_result_carParkResult, "humanObj", humanObj);
    }

    private void _result_carParkResult(Param results, Param context) {
        HumanObject humanObj = context.get("humanObj");
        Param pm = results.get();
        if(pm == null || pm.isEmpty()){
            return;
        }
        long winId = pm.get("winId");
        if(winId == humanObj.id){
            int carCoinNum = pm.get("carCoinNum");
            // 给玩家添加奖励
            ProduceManager.inst().produceAdd(humanObj, ItemConstants.goods_菇车币, carCoinNum, MoneyItemLogKey.停车抢占奖励);
            InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, InstanceConstants.showType_1001009, ItemConstants.goods_菇车币, carCoinNum);
        }
        int atkDebuff = pm.get("atkDebuff") == null ? 0 : pm.get("atkDebuff");
        int defDebuff = pm.get("defDebuff") == null ? 0 : pm.get("defDebuff");

        // 确定真正的防守方ID
        long defHumanId;
        defHumanId = humanObj.combatId;
        HumanBriefVO defHumanBriefVo = humanObj.combatBriefVo;
        PropCalc propCalc = new PropCalc((String)pm.get("propBonus"));
        ArenaManager.inst().createHistoryProto(humanObj, defHumanId, RedisKeys.carParkBattleHistoryList,
                winId==humanObj.id, humanObj.combatSeed, pm.get("hp"), propCalc, atkDebuff, defDebuff);
        humanObj.combatBriefVo = null;
        // 构建结果消息
        MsgCarPark.car_park_result_s2c.Builder msg = MsgCarPark.car_park_result_s2c.newBuilder();
        msg.setCode(0);
        msg.setVid(winId);
        msg.setIsWin(winId == humanObj.id ? 1 : 0);
        msg.setEName(defHumanBriefVo.name);

        // 构建头像信息
        Define.p_head.Builder headBuilder = Define.p_head.newBuilder();
        headBuilder.setId(defHumanBriefVo.headSn);
        headBuilder.setFrameId(defHumanBriefVo.currentHeadFrameSn);
        msg.setEHead(headBuilder);

        msg.setEServId(defHumanBriefVo.serverId);

        // 发送消息
        humanObj.sendMsg(msg);
    }


    public void receiveReward(ParkSpaceVo parkSpaceVo, float rate, boolean isUpgraded, Map<Integer, MountVo> mountMap, Define.p_car_park_record p_record, boolean refresh) {
        HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance();
        proxy.getInfo(parkSpaceVo.humanId);
        proxy.listenResult(this::receiveReward, "parkSpaceVo", parkSpaceVo, "rate", rate, "propUpdate", isUpgraded, "mountMap", mountMap, "p_record", p_record, "refresh", refresh);
    }

    private void receiveReward(Param results, Param context) {
        ParkSpaceVo parkSpaceVo = context.get("parkSpaceVo");
        boolean isUpgraded = context.get("propUpdate");
        Map<Integer, MountVo> mountMap = context.get("mountMap");
        Define.p_car_park_record p_record = context.get("p_record");
        float rate = context.get("rate");
        boolean refresh = context.get("refresh");
        Map<Integer,Integer> reward = parkSpaceVo.rewardItemMap;
        reward.put(ItemConstants.goods_菇车币, (int) (parkSpaceVo.carCoin * rate));
        HumanGlobalInfo humanInfo = results.get();
        if (humanInfo != null) {
            // 创建mountMap的深拷贝
            Map<Integer, MountVo> mountMapCopy = new HashMap<>();
            if(mountMap != null) {
                for(Map.Entry<Integer, MountVo> entry : mountMap.entrySet()) {
                    mountMapCopy.put(entry.getKey(), new MountVo(entry.getValue()));
                }
            }
            
            // 使用拷贝的mountMap进行调用
            HumanObjectServiceProxy humanPrx = HumanObjectServiceProxy
                    .newInstance(humanInfo.nodeId, humanInfo.portId,
                            humanInfo.id);
            humanPrx.receiveCarParkReward(reward,isUpgraded,mountMapCopy,p_record, refresh);
        } else {
            JSONObject jo = new JSONObject();
            jo.put("reward",Utils.mapIntIntToJSON(reward));
            jo.put("propUpdate",isUpgraded);
            jo.put("record",Utils.toProtoString(p_record));
            Pocket.add(parkSpaceVo.humanId, PocketLineEventSubKey.CAR_PARK_REWARD, jo.toJSONString());
        }
    }

    @Listener(value = EventKey.POCKET_LINE_HANDLE_ONE, subStr = PocketLineEventSubKey.CAR_PARK_REWARD)
    public void pocketLine_REWARD(Param param) {
        HumanObject humanObj = param.get("humanObj");
        PocketLine p = param.get("pocketLine");
        JSONObject jo = Utils.toJSONObject(p.getParam());
        Map<Integer, Integer> reward = Utils.jsonToMapIntInt(jo.getString("reward"));
        boolean isUpgraded = jo.getBoolean("propUpdate");
        Define.p_car_park_record p_record = Utils.fromProtoString(jo.getString("record"), Define.p_car_park_record.class);
        receiveCarParkReward(humanObj, reward, isUpgraded,null,p_record,false);
    }

    public void receiveCarParkReward(HumanObject humanObj, Map<Integer, Integer> rewards, boolean isUpgraded, Map<Integer, MountVo> mountMap, Define.p_car_park_record p_record, boolean refresh){
        // 发放奖励
        if (rewards != null && !rewards.isEmpty()) {
            ProduceManager.inst().produceAdd(humanObj, rewards, MoneyItemLogKey.停车奖励);
            InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, InstanceConstants.showType_1001010, rewards);
        }
        // 更新属性
        if (isUpgraded) {
            // 获取坐骑信息用于更新属性
            if (mountMap != null) {
                updatePorpCalcPower(humanObj, mountMap);
                PropManager.inst().propCalc(humanObj, CombatChangeLog.停车改装升级);
            }else {
                CarParkServiceProxy proxy = CarParkServiceProxy.newInstance();
                proxy.getMountMap(humanObj.id);
                proxy.listenResult(this::_result_receiveCarParkReward, "humanObj", humanObj);

            }
        }
        if(p_record != null){
            CarPark2 carPark2 = humanObj.operation.carPark2;
            addRecord(carPark2,p_record);

            int id = p_record.getActionId();
            if(refresh && (id == CarParkType.PARK_LOG_GET_BEATEN || id == CarParkType.PARK_LOG_AUTO_GET_REWARD || id == CarParkType.PARK_LOG_HELP_GET_REWARD)){
                on_car_park_car_info_c2s(humanObj);
                on_car_park_info_c2s(humanObj, HUMAM_SPOT, humanObj.id,0);
            }
        }
    }

    private void _result_receiveCarParkReward(Param results, Param context) {
        HumanObject humanObj = context.get("humanObj");
        String mountMapStr = results.get();
        if (mountMapStr == null || mountMapStr.isEmpty()) {
            //待办玩家坐骑数据还没加载，取不到坐骑数据。不做处理
//            Log.carPark.error("领取停车奖励取不到玩家坐骑数据：humanID={}",humanObj.id);
            return;
        }
        Map<Integer, MountVo> mountMap = MountVo.mapFromJsonStr(mountMapStr);
        updatePorpCalcPower(humanObj, mountMap);
        PropManager.inst().propCalc(humanObj, CombatChangeLog.停车改装升级);
    }


    public void sendParkFeeMail(String name, long parkId, ParkSpaceVo vo) {
        ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.park_owner_reward);
        int ownerReward = (int)(vo.carCoin * confGlobal.value / 100.0);

        JSONObject jo = new JSONObject();

        JSONObject joTemp1 = new JSONObject();
        joTemp1.put(MailManager.MAIL_K_3, name);
        jo.put(MailManager.MAIL_PARAM_1, joTemp1);

        JSONObject joTemp2 = new JSONObject();
        joTemp2.put(MailManager.MAIL_K_2, vo.mountId);
        jo.put(MailManager.MAIL_PARAM_2, joTemp2);

        JSONObject joTemp3 = new JSONObject();
        joTemp3.put(MailManager.MAIL_K_5, (vo.lastGetTime - vo.startTime)/60);
        jo.put(MailManager.MAIL_PARAM_3, joTemp3);

        JSONObject joTemp4 = new JSONObject();
        joTemp4.put(MailManager.MAIL_K_4, ownerReward);
        jo.put(MailManager.MAIL_PARAM_4, joTemp4);

        JSONObject ownerItemJSON = new JSONObject();
        ownerItemJSON.put(Integer.toString(ItemConstants.goods_菇车币), ownerReward);
        MailManager.inst().sendMail(parkId, MailManager.SYS_SENDER, 10152, "", jo.toJSONString(),ownerItemJSON.toJSONString(), new Param());
    }

    public List<Integer> addBuff(CarPark carPark, int type) {
        List<Integer> buff = getReCaculateBuff(carPark, type,false);
        addBuff(buff, type);
        setBuff(carPark, buff, type);
        carPark.update();
        return buff;
    }

    public List<Integer> addBuff(List<Integer> buff, int type) {
        //加一层攻击buff
        ConfParkPvpDebuff_0 conf = ConfParkPvpDebuff_0.get(buff.get(0)+1, type);
        if(conf == null) {
            return buff;
        }
        buff.set(0, buff.get(0)+1);
        buff.set(1, (int) (Port.getTime() / Time.SEC) + conf.time*60);
        return buff;
    }

    public List<Integer> addLoseBuff(CarPark carPark, int type) {
        List<Integer> buff = getReCaculateBuff(carPark, type,false);
        ConfParkPvpDebuff_0 conf = ConfParkPvpDebuff_0.get(buff.get(0), type);
        if(conf == null) {
            return Arrays.asList(0, 0);
        }
        buff.set(0, 0);
        buff.set(1, (int) (Port.getTime() / Time.SEC) + conf.lose_cd*60);
        setBuff(carPark, buff, type);
        return buff;
    }

    public void onHumanLogin(HumanObject humanObj) {
        if(humanObj.operation.carPark2 == null){
            return;
        }
        CarParkServiceProxy proxy = CarParkServiceProxy.newInstance();
        proxy.onHumanLogin(humanObj.id);
        proxy.listenResult(this::_result_onHumanLogin, "humanObj", humanObj);
    }

    private void _result_onHumanLogin(Param results, Param context) {
        HumanObject humanObj = context.get("humanObj");
        int income = results.get("income");
        if(income <= 0){
            return;
        }
        ProduceManager.inst().produceAdd(humanObj, ItemConstants.goods_菇车币,income, MoneyItemLogKey.停车场收益);
    }

    @Listener(EventKey.HUMAN_LOGOUT)
    public void onHumanLogout(Param param) {
        HumanObject humanObj = param.get("humanObj");
        CarParkServiceProxy.newInstance().onHumanLogout(humanObj.id);
    }

    public void on_car_park_search_c2s(HumanObject humanObj, int type, String parkName) {
        CarPark2 carPark2 = humanObj.operation.carPark2;
        if(carPark2 == null) {
            return;
        }
        if(type == SEARCH_TYPE_FRIEND){
            Friend friend = humanObj.operation.friend;
            if(friend == null){
                return;
            }
            sendCarParkList(humanObj, type, Utils.strToLongList(friend.getFriendList()), null);
        }else if(type == SEARCH_TYPE_RANDOM){
            if(!humanObj.isMsgIdCD(11015,3)){
                return;
            }
            String redisKey = Utils.createStr("{}.{}",RedisKeys.serverParkHumanSet, humanObj.getHuman().getServerId());
            int num = ConfGlobal.get(ConfGlobalKey.capture_slave_research).intArray[1];
            RedisTools.getRandSet(EntityManager.redisClient, redisKey, String.valueOf(num+1), res->{
                if (res.failed()) {
                    Log.carPark.error("on_car_park_search_c2s getRandSet error {}", res.cause().getMessage());
                    return;
                }

                JsonArray jsonArray = res.result();
                List<Long> humanList = new ArrayList<>();

                if (jsonArray != null) {
                    for (Object obj : jsonArray) {
                        long id = Utils.longValue(obj.toString());
                        if (id != humanObj.id) {
                            humanList.add(id);
                        }
                        if (humanList.size() >= num) {
                            break;
                        }
                    }
                }
                sendCarParkList(humanObj, type, humanList, Utils.strToLongList(carPark2.getCollectList()));
            });
        }else if(type == SEARCH_TYPE_PUBLIC){
            sendPublicCarParkList(humanObj);
        }
    }

    private void sendCarParkList(HumanObject humanObj, int type, List<Long> nullList, List<Long> collectList) {
        Map<Long, Integer> cdMap = clearParkCdMap(humanObj.operation.carPark2);
        MsgCarPark.car_park_search_s2c.Builder msg = MsgCarPark.car_park_search_s2c.newBuilder();

        // 合并两个列表进行一次批量查询
        List<Long> allIds = new ArrayList<>();
        if (nullList != null) {
            allIds.addAll(nullList);
        }
        if (collectList != null) {
            allIds.addAll(collectList);
        }

        if (allIds.isEmpty()) {
            humanObj.sendMsg(msg);
            return;
        }

        // 异步批量获取玩家数据
        HumanData.getList(allIds, new Class[]{Human.class}, (result) -> {
            if (result.failed()) {
                Log.carPark.error("批量获取玩家数据失败, humanId={}, error={}", humanObj.id, result.cause());
                return;
            }

            List<HumanData> humanDataList = result.result();

            // 处理nullList
            if (nullList != null && !nullList.isEmpty()) {
                for (int i = 0; i < nullList.size(); i++) {
                    HumanData humanData = humanDataList.get(i);
                    if (humanData == null || humanData.human == null
                            || humanData.human.getHasCarPark() == 0
                            || humanData.human.getServerId()!= humanObj.getHuman().getServerId()) {
                        continue;
                    }
                    Define.p_car_park_null.Builder nullBuilder = buildCarParkNull(humanData.human, cdMap.getOrDefault(nullList.get(i), 0));
                    if (nullBuilder != null) {
                        msg.addNullSpace(nullBuilder);
                    }
                }
            }

            // 处理collectList
            if (collectList != null && !collectList.isEmpty()) {
                for (int i = nullList.size(); i < allIds.size(); i++) {
                    HumanData humanData = humanDataList.get(i);
                    if (humanData == null || humanData.human == null
                            || humanData.human.getHasCarPark() == 0
                            || humanData.human.getServerId() != humanObj.getHuman().getServerId()) {
                        continue;
                    }
                    Define.p_car_park_null.Builder nullBuilder = buildCarParkNull(humanData.human, cdMap.getOrDefault(collectList.get(i - nullList.size()), 0));
                    if (nullBuilder != null) {
                        msg.addCollectSpace(nullBuilder);
                    }
                }
            }

            humanObj.sendMsg(msg);
        });
    }

    // 新增辅助方法,构建car_park_null消息
    private Define.p_car_park_null.Builder buildCarParkNull(Human human, int cdTime) {
        Define.p_car_park_null.Builder nullBuilder = Define.p_car_park_null.newBuilder();
        nullBuilder.setParkType(HUMAM_SPOT);
        nullBuilder.setMasterId(human.getId());
        nullBuilder.setNullNum(HUMAN_SPACE_NUM); // 空位数由Human的carParkAttr计算
        nullBuilder.setInfoList(to_p_role_change(human,""));
        nullBuilder.addAllSkinPlus(to_p_key_value(human.getCarParkAttr())); // 使用Human中的carParkAttr
        if (cdTime != 0) {
            nullBuilder.addExt(Define.p_key_value.newBuilder().setK(1).setV(cdTime));
        }
        nullBuilder.setCeng(0);
        return nullBuilder;
    }

    private void sendPublicCarParkList(HumanObject humanObj) {
        CarParkServiceProxy proxy = CarParkServiceProxy.newInstance();
        proxy.getPublicCarParkNullNum(humanObj.getHuman().getServerId());
        proxy.listenResult(this::_result_getPublicCarParkNullNum, "humanObj", humanObj);
    }

    private void _result_getPublicCarParkNullNum(Param results, Param context){
        HumanObject humanObj = context.get("humanObj");
        if (results == null || results.isEmpty()) {
            return;
        }
        Map<Integer, Integer> nullNumMap = results.get();
        Map<Long, Integer> cdMap = clearParkCdMap(humanObj.operation.carPark2);
        
        // 构建消息
        MsgCarPark.car_park_search_s2c.Builder msg = MsgCarPark.car_park_search_s2c.newBuilder();
        for (int i = 1; i <= PUBLIC_NUM; i++) {
            long parkId = getPublicParkCengId(humanObj.id, i);
            // 将 long 类型的 parkId 转换为 int 类型来匹配 map 的 key
            int parkIdInt = (int)parkId;

            Define.p_car_park_null.Builder nullBuilder = Define.p_car_park_null.newBuilder();
            nullBuilder.setParkType(PUBLIC_SPOT);
            nullBuilder.setMasterId(parkId);
            // 使用转换后的 parkIdInt 获取值
            nullBuilder.setNullNum(nullNumMap.getOrDefault(parkIdInt, 0));
            nullBuilder.setCeng(i);
            msg.addNullSpace(nullBuilder);
        }

        // 发送消息
        humanObj.sendMsg(msg);
    }

    private Map<Long, Integer> clearParkCdMap(CarPark2 carPark2) {
        Map<Long, Integer> cdMap = Utils.jsonToMapLongInt(carPark2.getCdMap());
        int now = (int)(Port.getTime()/Time.SEC);
        boolean isChange = false;
        Iterator<Map.Entry<Long, Integer>> it = cdMap.entrySet().iterator();
        while (it.hasNext()) {
            Map.Entry<Long, Integer> entry = it.next();
            if(entry.getValue() < now) {
                it.remove();
                isChange = true;
            }
        }
        if(isChange){
            carPark2.setCdMap(Utils.mapLongIntToJSON(cdMap));
            carPark2.update();
        }
        return cdMap;
    }

    private List<Define.p_key_value> to_p_key_value(String attrStr){
        List<Define.p_key_value> list = new ArrayList<>();
        int[] attr = Utils.strToIntArray(attrStr);
        if (attr == null || attr.length < SKIN_ATTR_PROTECT) {
            return list;
        }
        Define.p_key_value.Builder builder = Define.p_key_value.newBuilder();
        builder.setK(1);
        builder.setV(attr[SKIN_ATTR_COIN]);
        list.add(builder.build());
        builder.setK(2);
        builder.setV(attr[SKIN_ATTR_EXP]);
        list.add(builder.build());
        builder.setK(3);
        builder.setV(attr[SKIN_ATTR_SPEC]);
        list.add(builder.build());
//        builder.setK(SKIN_ATTR_PROTECT);
//        builder.setV(attr[SKIN_ATTR_PROTECT]);
//        list.add(builder.build());
        return list;
    }


    public void on_car_park_protect_c2s(HumanObject humanObj, Define.p_car_park_protect protect) {
        // 调用Service处理
        CarParkServiceProxy proxy = CarParkServiceProxy.newInstance();
        proxy.carParkProtect(humanObj.id,
                protect.getIsOpen(),
                protect.getProtectType(),
                protect.getProtectRatio());
        proxy.listenResult(this::_result_carParkProtect,
                "humanObj", humanObj,
                "protect", protect);
    }

    private void _result_carParkProtect(Param results, Param context) {
        HumanObject humanObj = context.get("humanObj");
        Define.p_car_park_protect protect = context.get("protect");

        if (results == null || results.isEmpty()) {
            return;
        }

        // 发送响应消息
        MsgCarPark.car_park_protect_s2c.Builder msg = MsgCarPark.car_park_protect_s2c.newBuilder();
        msg.setProtect(protect.toBuilder());
        humanObj.sendMsg(msg);
    }

    public void on_car_park_skin_use_c2s(HumanObject humanObj, int type, int skinId, int isUse, Define.p_pos pos) {
        // 调用Service处理
        CarParkServiceProxy proxy = CarParkServiceProxy.newInstance();
        proxy.carParkSkinUse(humanObj.id, type, skinId, isUse, pos.getX(), pos.getY());
        proxy.listenResult(this::_result_carParkSkinUse,
                "humanObj", humanObj, "type", type);
    }

    private void _result_carParkSkinUse(Param results, Param context) {
        HumanObject humanObj = context.get("humanObj");
        int type = context.get("type");
        if (results == null || results.isEmpty()) {
            return;
        }

        // 构建响应消息
        MsgCarPark.car_park_skin_use_s2c.Builder msg = MsgCarPark.car_park_skin_use_s2c.newBuilder();
        msg.setType(type);

        // 添加皮肤列表
        List<Define.p_car_park_skin> skinList = results.get("skinList");
        msg.addAllSkinList(skinList);

        // 发送消息
        humanObj.sendMsg(msg);
    }

    public void on_car_park_mount_id_c2s(HumanObject humanObj, int mountId) {
        // 调用Service处理
        CarParkServiceProxy proxy = CarParkServiceProxy.newInstance();
        proxy.carParkMountId(humanObj.id, mountId);
        proxy.listenResult(this::_result_carParkMountId,
            "humanObj", humanObj,
            "mountId", mountId);
    }

    private void _result_carParkMountId(Param results, Param context) {
        HumanObject humanObj = context.get("humanObj");
        int mountId = context.get("mountId");

        if (results == null || results.isEmpty()) {
            return;
        }

        // 发送响应消息
        MsgCarPark.car_park_mount_id_s2c.Builder msg = MsgCarPark.car_park_mount_id_s2c.newBuilder();
        msg.setMountId(mountId);
        humanObj.sendMsg(msg);
    }

    public void on_car_park_rename_c2s(HumanObject humanObj, String name) {
        if (name == null || name.length() > NAME_MAX) {
            return;
        }
        ReasonResult result = NameManager.inst().checkName(name);
        if(!result.success){
            humanObj.sendMsg(MsgError.error_info_s2c.newBuilder().setCode(result.code));
            return;
        }

        // 调用Service处理
        CarParkServiceProxy proxy = CarParkServiceProxy.newInstance();
        proxy.carParkRename(humanObj.id, name);
        proxy.listenResult(this::_result_carParkRename,
                "humanObj", humanObj,
                "name", name);
    }

    private void _result_carParkRename(Param results, Param context) {
        HumanObject humanObj = context.get("humanObj");
        String name = context.get("name");

        if (results == null || results.isEmpty()) {
            return;
        }

        // 发送响应消息
        MsgCarPark.car_park_rename_s2c.Builder msg = MsgCarPark.car_park_rename_s2c.newBuilder();
        msg.setName(name);
        humanObj.sendMsg(msg);
    }


    public void on_car_park_collect_c2s(HumanObject humanObj, long code, long roleId) {
        CarPark2 carPark2 = humanObj.operation.carPark2;
        if(carPark2 == null) {
            return;
        }
        List<Long> collectList = Utils.strToLongList(carPark2.getCollectList());
        if(code == 0){
            collectList.remove(roleId);
            carPark2.setCollectList(Utils.listToString(collectList));
            MsgCarPark.car_park_collect_s2c.Builder msg = MsgCarPark.car_park_collect_s2c.newBuilder();
            msg.setCode(code);
            msg.setRoleId(roleId);
            humanObj.sendMsg(msg);
        }else {
            if(collectList.contains(roleId)){
                return;
            }
            CarParkServiceProxy proxy = CarParkServiceProxy.newInstance();
            proxy.isExitInCache(roleId);
            proxy.listenResult(this::_result_isExitInCache, "humanObj", humanObj, "code", code, "roleId", roleId, "collectList",collectList);
        }
    }

    private void _result_isExitInCache(Param results, Param context){
        HumanObject humanObj = context.get("humanObj");
        long roleId = context.get("roleId");
        List<Long> collectList = context.get("collectList");
        long code = context.get("code");
        if (results == null || results.isEmpty()) {
            return;
        }
        boolean isExit = results.get("isExit");
        if(!isExit){
            return;
        }
        collectList.add(roleId);
        humanObj.operation.carPark2.setCollectList(Utils.listToString(collectList));
        MsgCarPark.car_park_collect_s2c.Builder msg = MsgCarPark.car_park_collect_s2c.newBuilder();
        msg.setCode(code);
        msg.setRoleId(roleId);
        humanObj.sendMsg(msg);
    }

    public void on_car_park_skin_up_c2s(HumanObject humanObj, int type, int skinId) {
        // 第一步: 调用Service查询等级
        CarParkServiceProxy proxy = CarParkServiceProxy.newInstance();
        proxy.getSkinLevel(humanObj.id, skinId);
        proxy.listenResult(this::_result_getSkinLevel,
                "humanObj", humanObj,
                "type", type,
                "skinId", skinId);
    }

    private void _result_getSkinLevel(Param results, Param context) {
        HumanObject humanObj = context.get("humanObj");
        int type = context.get("type");
        int skinId = context.get("skinId");

        if (results == null || results.isEmpty()) {
            return;
        }

        int lv = results.get("level");

        // 获取配置
        ConfParkingDesign_0 conf = ConfParkingDesign_0.get(skinId, lv);
        if(conf == null || conf.expend == null || conf.expend.length != 2){
            Log.carPark.error("_result_getSkinLevel:找不到停车场装扮配置id={},lv={}", skinId, lv);
            return;
        }

        // 扣除道具
        if(!ProduceManager.inst().checkAndCostItem(humanObj, conf.expend[0], conf.expend[1], MoneyItemLogKey.装扮升级).success){
            return;
        }

        // 第二步: 调用Service升级
        CarParkServiceProxy proxy = CarParkServiceProxy.newInstance();
        proxy.carParkSkinUp(humanObj.id, type, skinId, lv);
        proxy.listenResult(this::_result_carParkSkinUp,
                "humanObj", humanObj,
                "type", type,
                "itemId", conf.expend[0],
                "itemCount", conf.expend[1]);
    }

    private void _result_carParkSkinUp(Param results, Param context) {
        HumanObject humanObj = context.get("humanObj");
        int type = context.get("type");
        int itemId = context.get("itemId");
        int itemCount = context.get("itemCount");

        if (results == null || results.isEmpty()) {
            // 升级失败,返还道具
            ProduceManager.inst().produceAdd(humanObj, itemId, itemCount, MoneyItemLogKey.装扮升级返还);
            return;
        }

        // 更新属性和战力
        List<Define.p_car_park_skin> skinList = results.get("skinList");
        Map<Integer, Integer> skinLvMap = results.get("skinLvMap");
        updateSkinPropCalcPower(humanObj, skinLvMap);

        // 构建响应消息
        MsgCarPark.car_park_skin_up_s2c.Builder msg = MsgCarPark.car_park_skin_up_s2c.newBuilder();
        msg.setType(type);

        // 添加皮肤列表

        msg.addAllSkinList(skinList);

        // 发送消息
        humanObj.sendMsg(msg);
    }

    public List<Define.p_car_park_skin> to_p_car_park_skin_List(int type, Map<Integer,Integer> skinLvMap, Map<Integer,ParkSkinVo> skinDecSnVoMap, Map<Integer,Integer> skinTypeSnMap){
        List<Define.p_car_park_skin> list = new ArrayList<>();
        for (Map.Entry<Integer,Integer> entry : skinLvMap.entrySet()){
            int skinId = entry.getKey();
            int lv = entry.getValue();
            ConfParkingDesign_0 conf = ConfParkingDesign_0.get(skinId, lv);
            if(conf == null){
                Log.carPark.error("to_p_car_park_skin_List:找不到停车场装扮配置id={},lv={}", skinId, lv);
                continue;
            }
            int skinType = conf.position;
            if(skinType!=type){
                continue;
            }
            Define.p_car_park_skin.Builder skinUse = Define.p_car_park_skin.newBuilder();
            skinUse.setSkinId(skinId);
            skinUse.setSkinLev(lv);
            if(skinType == TYPE_DEC){
                ParkSkinVo vo = skinDecSnVoMap.getOrDefault(skinId,new ParkSkinVo());
                skinUse.setPos(vo.pos);
                skinUse.setX(vo.x);
                skinUse.setY(vo.y);
            }else {
                int useSkinId = skinTypeSnMap.getOrDefault(skinType, 0);
                skinType = useSkinId == skinId ? skinType : 0;
                skinUse.setPos(skinType);
                skinUse.setX(0);
                skinUse.setY(0);
            }
            list.add(skinUse.build());
        }
        return list;
    }

    /**
     * 更新属性
     */
    private void updateSkinPropCalcPower(HumanObject humanObj, Map<Integer,Integer> skinLvMap) {
        PropCalc propCalc = new PropCalc();
        int power = 0;
        for (Map.Entry<Integer,Integer> entry : skinLvMap.entrySet()){
            ConfParkingDesign_0 conf = ConfParkingDesign_0.get(entry.getKey(), entry.getValue());
            if(conf == null){
                Log.carPark.error("updatePorpCalcPower:找不到停车场装扮配置id={},lv={}", entry.getKey(), entry.getValue());
                continue;
            }
            power += conf.power;
            propCalc.plus(conf.own_attrs);
        }
        humanObj.dataPers.unitPropPlus.setFieldRead(UnitPropPlus.K.carParkSkin, propCalc.toJSONStr());
        HumanManager.inst().updatePowerPar(humanObj, EModule.carParkSkin, power);
        PropManager.inst().propCalc(humanObj, CombatChangeLog.停车皮肤升级);
        int [] attr = getAttrHumanCarPark(skinLvMap);
        humanObj.getHuman().setCarParkAttr(Utils.arrayIntToStr(attr));
    }

    @Listener(EventKey.FUNCTION_OPEN)
    public void onFunctionOpen(Param param) {
        HumanObject humanObj = param.get("humanObj");
        if(humanObj.operation.carPark2 != null){
            return;
        }
        if(!humanObj.isModUnlock(77)){
            return;
        }
        createCarPark(humanObj);
    }

    private void createCarPark(HumanObject humanObj) {
        createCarPark1(humanObj);

        CarPark2 carPark2 = new CarPark2();
        carPark2.setId(humanObj.id);
        carPark2.persist();
        humanObj.operation.carPark2 = carPark2;

        //加入serverParkHumanSet中
        int serverId = humanObj.getHuman().getServerId();
        String keyStr = Utils.createStr("{}.{}",RedisKeys.serverParkHumanSet, serverId);
        List<String> params = Arrays.asList(keyStr, Long.toString(humanObj.id));
        EntityManager.redisClient.sadd(params,r -> {
            if(!r.succeeded()){
                Log.carPark.error("addToCrossServerParkSet:{}",params);
            }
        });
    }

    private void createCarPark1(HumanObject humanObj){
        CarPark carPark = new CarPark();
        initCarPark1(humanObj, carPark);
        carPark.persist();
        onHumanLogin(humanObj);
    }
    private void initCarPark1(HumanObject humanObj, CarPark carPark){
        carPark.setId(humanObj.id);
        humanObj.getHuman().setHasCarPark(1);
        //坐骑初始化
        Mount mount = humanObj.operation.mount;
        if(mount != null){
            Map<Integer,Integer> mountMap = Utils.jsonToMapIntInt(mount.getSkinLvMap());
            Map<Integer,MountVo> mountVoMap = new HashMap<>();
            for (Map.Entry<Integer,Integer> entry : mountMap.entrySet()){
                MountVo vo = new MountVo(1);
                mountVoMap.put(entry.getKey(), vo);
            }
            carPark.setMountMap(MountVo.mapToJsonStr(mountVoMap));
        }
        //皮肤初始化
        Map<Integer,Integer> skinLvMap = new HashMap<>();
        Map<Integer,Integer> skinTypeSnMap = new HashMap<>();
        List<ConfParkingDesign_0> confList = GlobalConfVal.parkingDesignInitialList;
        for (ConfParkingDesign_0 conf : confList){
            skinLvMap.put(conf.id, conf.level);
            skinTypeSnMap.put(conf.position, conf.id);
        }
        carPark.setSkinTypeSnMap(Utils.mapIntIntToJSON(skinTypeSnMap));
        carPark.setSkinLvMap(Utils.mapIntIntToJSON(skinLvMap));
    }

    public void fixCarPark(HumanObject humanObj){
        if(humanObj.operation.carPark2 == null){
            return;
        }
        EntityManager.getEntityAsync(CarPark.class, humanObj.id, res->{
            if(res.failed()){
                Log.carPark.error("fixCarPark:{},humanID={}",res.cause(),humanObj.id);
                return;
            }
            CarPark carPark = res.result();
            if(carPark == null){
                createCarPark1(humanObj);
                Log.carPark.error("fixCarPark:carPark is null,humanID={}",humanObj.id);
            }else {
                int serverId = humanObj.getHuman().getServerId();
                String keyStr = Utils.createStr("{}.{}",RedisKeys.serverParkHumanSet, serverId);
                List<String> params = Arrays.asList(keyStr, Long.toString(humanObj.id));
                EntityManager.redisClient.sadd(params,r -> {
                    if(!r.succeeded()){
                        Log.carPark.error("addToCrossServerParkSet:{}",params);
                    }
                });
                //初始化车辆
                initCarPark1(humanObj, carPark);
                Log.carPark.error("fixCarPark:carPark is not null,humanID={}",humanObj.id);
                carPark.update();
            }
        });
    }

    public void fixCarPark210(HumanObject humanObj){
        CarParkServiceProxy proxy = CarParkServiceProxy.newInstance();
        proxy.onCarParkFix(humanObj.id, FixVersionType.CAR_PARK_210_SKIN, null);
        proxy.listenResult(this::_result_fixCarPark, "humanObj", humanObj);
    }

    private void _result_fixCarPark(Param results, Param context){
        HumanObject humanObj = context.get("humanObj");
        // 获取商城购买记录(道具ID -> 购买数量)
        Map<Integer, Map<Integer, Integer>> typeBuyNumMap = Utils.jsonToIntMapIntInt(humanObj.operation.mall.getMallBuyNumMap());
        Map<Integer, Integer> snBuyNumMap = typeBuyNumMap.getOrDefault(11, new HashMap<>()); //11是停车场装饰皮肤
        // 转换商品sn为道具id
        Map<Integer, Integer> itemBuyNumMap = new HashMap<>();
        for (Map.Entry<Integer, Integer> entry : snBuyNumMap.entrySet()){
            //sn映射到道具id
            ConfMall confMall = ConfMall.get(entry.getKey());
            if(confMall == null || confMall.goods == null || confMall.goods.length == 0){
                Log.carPark.error("fixCarPark210:找不到商城配置sn={}",entry.getKey());
                continue;
            }
            int itemId = ConfMall.get(entry.getKey()).goods[0];
            itemBuyNumMap.put(itemId, entry.getValue());
        }
        snBuyNumMap = itemBuyNumMap;
        Map<Integer, Integer> skinLvMap = results.get("skinLvMap");
        if(skinLvMap == null) {
            Log.game.error("fixCarPark210:skinLvMap is null,humanID={}",humanObj.id);
            return;
        }

        // 需要检查的装饰ID集合
        Set<Integer> skinIdSet = new HashSet<>(Arrays.asList(
                10001, 10002, 10003, 10004, 10005,
                20001,
                30001, 30002, 30004, 30005,
                40001, 40003, 40004, 40005,
                50015, 50017, 50018, 50019, 50020, 50021, 50022
        ));

        // 按道具ID统计已使用的数量
        Map<Integer, Integer> usedItemMap = new HashMap<>();
        // 按道具ID统计购买的总数量
        Map<Integer, Integer> buyItemMap = new HashMap<>();
        // 按道具ID统计背包现有数量
        Map<Integer, Integer> bagItemMap = new HashMap<>();

        // 遍历所有需要检查的装饰ID
        for(Integer skinId : skinIdSet) {
            // 获取当前等级
            int currentLevel = skinLvMap.getOrDefault(skinId, 0);

            // 获取0级配置，检查升级所需道具ID
            ConfParkingDesign_0 confLv0 = ConfParkingDesign_0.get(skinId, 0);
            if(confLv0 == null || confLv0.expend == null || confLv0.expend.length != 2){
                continue;
            }

            // 获取升级所需的道具ID
            int itemId = confLv0.expend[0];

            // 如果购买记录中有这个道具
            if(snBuyNumMap.containsKey(itemId)) {
                // 累加购买的道具数量
                buyItemMap.merge(itemId, snBuyNumMap.get(itemId), Integer::sum);

                // 获取背包中该道具的数量
                bagItemMap.putIfAbsent(itemId, ItemManager.inst().getItemNum(humanObj, itemId));

                // 计算升级到当前等级需要的道具数量
                for(int lv = 0; lv < currentLevel; lv++) {
                    ConfParkingDesign_0 conf = ConfParkingDesign_0.get(skinId, lv);
                    if(conf == null || conf.expend == null || conf.expend.length != 2) {
                        Log.carPark.error("fixCarPark210:找不到停车场装扮配置id={},lv={}", skinId, lv);
                        continue;
                    }
                    usedItemMap.merge(itemId, conf.expend[1], Integer::sum);
                }
            }
        }

        // 计算需要补偿的道具
        Map<Integer, Integer> compensateMap = new HashMap<>();
        for(Map.Entry<Integer, Integer> entry : buyItemMap.entrySet()) {
            int itemId = entry.getKey();
            int buyNum = entry.getValue(); // 购买的总数量
            int usedNum = usedItemMap.getOrDefault(itemId, 0); // 已使用的数量
            int bagNum = bagItemMap.getOrDefault(itemId, 0); // 背包现有数量

            // 需要补偿的数量 = 购买数量 - (背包现有数量 + 已使用数量)
            int compensateNum = buyNum - (bagNum + usedNum);
            if(compensateNum > 0) {
                compensateMap.put(itemId, compensateNum);
            }
        }

        if(!compensateMap.isEmpty()) {
            // 补偿道具给玩家
            ProduceManager.inst().produceAdd(humanObj, compensateMap, MoneyItemLogKey.测试接口);
            Log.carPark.info("fixCarPark210:humanId={},补偿道具={}", humanObj.id, compensateMap);
        }
    }

    public void cross_car_park_preview_c2s(HumanObject humanObj) {
        if(!humanObj.isModUnlock(77)){
            return;
        }
        int serverId = humanObj.getHuman().getServerId();
        long timeWeekOne = Utils.getTimeOfWeek(Port.getTime(), 1, 0);
        String dateStr = Utils.formatTime(timeWeekOne, "yyyy-MM-dd");
        int zone = GlobalConfVal.getZone(serverId);
        String dateServerKey = RedisKeys.admin_server_group + dateStr + serverId;
        Utils.getRedisStrValue(dateServerKey, res->{
            MsgCarPark.cross_car_park_preview_s2c.Builder msg = MsgCarPark.cross_car_park_preview_s2c.newBuilder();
            if(res.failed()){
                Log.carPark.error("cross_car_park_preview_c2s:查询redis出错，humanId={}, dateServerKey={}", humanObj.id, dateServerKey);
                humanObj.sendMsg(msg);
                return;
            }
            int groupId = Utils.intValue(res.result());
            if(groupId == 0){
                Log.carPark.error("isExitRank:找不到组id,serverId={}", serverId);
                humanObj.sendMsg(msg);
                return;
            }
            String groupKey = Utils.createStr("{}{}{}{}", RedisKeys.admin_server_date_zone_group, dateStr, zone, groupId);
            Utils.getRedisStrValue(groupKey,groupRes->{
                if(groupRes.failed()){
                    Log.carPark.error("cross_car_park_preview_c2s:查询redis出错，humanId={}, groupKey={}", humanObj.id, groupKey);
                    humanObj.sendMsg(msg);
                    return;
                }
                String serverStr = groupRes.result();
                List<Integer> serverList = Utils.strToIntList(serverStr);
                for (int server : serverList){
                    getServerParkIdSet(dateStr, server, parkIdListRes->{
                        if(parkIdListRes.failed()){
                            Log.carPark.error("cross_car_park_preview_c2s:查询redis pardIdListRes出错，humanId={}, server={}, dateStr={}", humanObj.id, server, dateStr);
                            return;
                        }
                        List<String> parkIdList = parkIdListRes.result();
                        List<Long> parkIds = new ArrayList<>();
                        for (String parkIdStr : parkIdList){
                            long parkId = Utils.longValue(parkIdStr);
                            parkIds.add(parkId);
                        }
                        EntityManager.batchGetEntity(CarParkCross.class, parkIds, batchParkRes->{
                            if(batchParkRes.failed()){
                                Log.carPark.error("cross_car_park_preview_c2s:批量查询redis.parkIdListRes出错，humanId={}, server={}, dateStr={}", humanObj.id, server, dateStr);
                                humanObj.sendMsg(msg);
                                return;
                            }
                            List<CarParkCross> parks = batchParkRes.result();
                            for(CarParkCross carParkCross:parks) {
                                if (carParkCross == null) {
                                    continue;
                                }
                                Define.p_cross_car_park_preview.Builder crossBuilder = Define.p_cross_car_park_preview.newBuilder();
                                long parkId = carParkCross.getId();
                                crossBuilder.setId(parkId);
                                crossBuilder.setServerId(server);
                                List<String> battleList = Utils.getRedisList(Utils.createStr("{}.{}.{}", RedisKeys.crossParkBattleList, dateStr, parkId));
                                List<String> robList = Utils.getRedisList(Utils.createStr("{}.{}.{}", RedisKeys.crossParkRobList, dateStr, parkId));
                                List<String> guardList = Utils.getRedisList(Utils.createStr("{}.{}.{}", RedisKeys.crossParkGuardList, dateStr, parkId));
                                crossBuilder.setAtkNum(robList.size() + battleList.size());
                                crossBuilder.setDefNum(guardList.size() + battleList.size());
                                crossBuilder.setProtectEnd(carParkCross.getProtectTime());
                                int emptyCount = 0;
                                for (int i = 1; i <= CROSS_SPACE_NUM; ++i) {
                                    if (Utils.isEmptyJSONString(getPlace(carParkCross, i))) {
                                        emptyCount++;
                                    }
                                }
                                crossBuilder.addExt(Define.p_key_value.newBuilder().setK(EXT4_REMAIN_SPACE).setV(emptyCount));
                                msg.addParkList(crossBuilder);
                            }
                            humanObj.sendMsg(msg);
                        });

                    });
                }
            });
        });

    }

    public List<String> getServerParkIdSet(String dateStr, int serverId){
        String keyStr = Utils.createStr("{}.{}.{}",RedisKeys.crossServerParkSet, dateStr, serverId);
        return Utils.getRedisSet(keyStr);
    }

    public void getServerParkIdSet(String dateStr, int serverId, Handler<AsyncResult<List<String>>> handler){
        String keyStr = Utils.createStr("{}.{}.{}",RedisKeys.crossServerParkSet, dateStr, serverId);
        Utils.getRedisSet(keyStr, handler);
    }

    public List<Integer> getGroupServerList(int serverId, String dateStr, int groupId) {
        try {
            String serverList = AwaitUtil.awaitResult(handler -> {
                RedisTools.get(EntityManager.getRedisClient(), RedisKeys.admin_server_date_zone_group + dateStr + GlobalConfVal.getZone(serverId)  + groupId, r->{
                    if (r.succeeded()) {
                        handler.handle(Future.succeededFuture(r.result()));
                    } else {
                        handler.handle(Future.failedFuture(r.cause()));
                    }
                });
            });
            return Utils.strToIntList(serverList);
        } catch (Exception e) {
            Log.temp.error("===e = {}", e);
        }
        return new ArrayList<>();
    }

    public boolean isCrossParkTime(int type) {
        ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.park_cross_open_time);
        String[] strArray = confGlobal.strValue.split("\\|");
        String str = strArray[type - 1];
        String[] strArray1 = str.split(",");
        String[] strArray2 = strArray1[1].split(";");

        int[] startTime = parseTime(strArray2[0]);
        int[] endTime = parseTime(strArray2[1]);

        long now = Port.getTime();
        long timeWeekStart = Utils.getTimeOfWeek(Port.getTime(), startTime[0], startTime[1])+startTime[2]*Time.MIN;
        long timeWeekEnd = Utils.getTimeOfWeek(Port.getTime(), endTime[0], startTime[1])+endTime[2]*Time.MIN;

        if(now < timeWeekStart || now > timeWeekEnd){
            return false;
        }

        if(type == CROSS_ATK_TIME){
            ConfGlobal confGlobal1 = ConfGlobal.get(ConfGlobalKey.park_cross_night_limit);
            String[] strArray3 = confGlobal1.strValue.split("\\|");
            String[] strStart = strArray3[0].split(",");
            String[] strEnd = strArray3[1].split(",");
            long nightStart = Utils.getTimeBeginOfToday(now)+Utils.intValue(strStart[0])*Time.HOUR+Utils.intValue(strStart[1])*Time.MIN;
            long nightEnd = Utils.getTimeBeginOfToday(now)+Utils.intValue(strEnd[0])*Time.HOUR+Utils.intValue(strEnd[1])*Time.MIN;
            if(now >= nightStart && now <= nightEnd){
                return false;
            }
        }
        return true;
    }

    public long getStartTimeStamp(int type) {
        ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.park_cross_open_time);
        String[] strArray = confGlobal.strValue.split("\\|");
        String str = strArray[type - 1];
        String[] strArray1 = str.split(",");
        String[] strArray2 = strArray1[1].split(";");

        int[] startTime = parseTime(strArray2[0]);

        // Get the current date
        Calendar calendar = Calendar.getInstance();
        // Set the week day, hour, minute and second
        calendar.set(Calendar.DAY_OF_WEEK, startTime[0]);
        calendar.set(Calendar.HOUR_OF_DAY, startTime[1]);
        calendar.set(Calendar.MINUTE, startTime[2]);
        calendar.set(Calendar.SECOND, startTime[3]);

        // Return the timestamp
        return calendar.getTimeInMillis();
    }

    private int[] parseTime(String timeStr) {
        String[] timeArray = timeStr.split("_");
        if (timeArray.length != 4) {
            Log.carPark.error("canCrossParkTime:解析错误str={}", timeStr);
        }
        return new int[]{
                Utils.intValue(timeArray[0]),
                Utils.intValue(timeArray[1]),
                Utils.intValue(timeArray[2]),
                Utils.intValue(timeArray[3])
        };
    }

    public void updateRank(String redisKey, String dateStr, int groupId, int serverId, long id, int addValue) {
        String keyStr = Utils.createStr("{}.{}.{}.{}",redisKey,dateStr,GlobalConfVal.getZone(serverId),groupId);
        EntityManager.redisClient.zincrby(keyStr, Integer.toString(addValue),Long.toString(id),r -> {
            if(!r.succeeded()){
                Log.carPark.error("updateRank:{},addValue={},id={}",keyStr,addValue,id);
            }
        });
    }

    public void removeRank(String redisKey, String dateStr, int groupId, int serverId, long id) {
        String keyStr = Utils.createStr("{}.{}.{}.{}",redisKey,dateStr,GlobalConfVal.getZone(serverId),groupId);
        EntityManager.redisClient.zrem(Arrays.asList(keyStr,Long.toString(id)),r -> {
            if(!r.succeeded()){
                Log.carPark.error("removeRank:{}",keyStr);
            }
        });
    }

    public boolean isMemberInRank(String redisKey, String dateStr, int groupId, int serverId) {
        Port port = Port.getCurrent();
        String keyStr = Utils.createStr("{}.{}.{}",redisKey,dateStr,GlobalConfVal.getZone(serverId),groupId);
        try {
            return AwaitUtil.awaitResult(handler -> {
                EntityManager.redisClient.zrank(keyStr, Integer.toString(serverId), r -> {
                    if (r.succeeded()) {
                        AsyncActionResult.success(port, handler, r.result() != null);
                    } else {
                        AsyncActionResult.fail(port,handler,r.cause());
                    }
                });
            });
        } catch (Exception e) {
            Log.carPark.error("isExitRank:{}",keyStr);
        }
        return true;
    }

    public void on_cross_car_park_info_c2s(HumanObject humanObj, long id) {
//        if(!ActivityManager.inst().isActivityOpen(humanObj, ActivityControlType.Activity_Control_Type_74)){
//            return;
//        }
        CarParkCross carParkCross = (CarParkCross) EntityManager.getEntity(CarParkCross.class, id);
        if(carParkCross == null){
            return;
        }
        int belongServerId = carParkCross.getBelongServerId();
        MsgCarPark.cross_car_park_info_s2c.Builder msg = MsgCarPark.cross_car_park_info_s2c.newBuilder();
        Define.p_cross_car_park.Builder info = Define.p_cross_car_park.newBuilder();
        info.setId(id);
        info.setServerId(belongServerId);
        long timeWeekOne = Utils.getTimeOfWeek(Port.getTime(), 1, 0);
        String dateStr = Utils.formatTime(timeWeekOne, "yyyy-MM-dd");
        List<String> robList = Utils.getRedisList(Utils.createStr("{}.{}.{}",RedisKeys.crossParkRobList, dateStr, id));
        List<String> guardList = Utils.getRedisList(Utils.createStr("{}.{}.{}",RedisKeys.crossParkGuardList, dateStr, id));
        List<String> battleList = Utils.getRedisList(Utils.createStr("{}.{}.{}",RedisKeys.crossParkBattleList, dateStr, id));
        //车位信息
        for(int i = 1; i <= CROSS_SPACE_NUM; i++) {
            ParkSpaceVo vo = new ParkSpaceVo(getPlace(carParkCross, i));
            if(vo.humanId != 0){
                int oldCarCoin = vo.carCoin;
                reCaculateCarReward(getAttrCrossCarPark(), vo);
                if(oldCarCoin != vo.carCoin){
                    updateCrossPlace(carParkCross, i, vo);
                }
                if(vo.humanId != 0 && (Port.getTime()/Time.SEC - vo.startTime) >= ConfGlobal.get(ConfGlobalKey.park_cross_auto_time).value*60){
                    String lockKey = Utils.createStr("{}.{}.{}",RedisKeys.humanParkLock,carParkCross.getId(),i);
                    if(RedisTools.lock(EntityManager.redisClient, lockKey,"1")){
//                        receiveReward(vo, 1.0f);todo：接口改了
                        int serverId = Utils.getServerIdByHumanId(vo.humanId);
                        int groupId = Utils.intValue(Utils.getRedisStrValue(RedisKeys.admin_server_group + dateStr + serverId));
                        updateRank(RedisKeys.crossCarCoinRank, dateStr, groupId, serverId, serverId, vo.carCoin);
                        setPlace(carParkCross,i,"{}");
                        carParkCross.update();
                        RedisTools.unLock(EntityManager.redisClient, lockKey);
                    }
                }else {
                    Human spaceHuman = (Human)EntityManager.getEntity(Human.class, vo.humanId);
                    Human2 spaceHuman2 = (Human2)EntityManager.getEntity(Human2.class, vo.humanId);
                    if(spaceHuman == null){
                        Log.carPark.error("on_cross_car_park_info_c2s:找不到玩家id={}", vo.humanId);
                        continue;
                    }
                    info.addSpaceList(to_p_car_park_space(i, vo, spaceHuman, spaceHuman2));
                }
            }
        }
        //战斗列表。加入到攻击和防御列表中发送
        int battleIndex = 1;
        for (String voStr : battleList){
            BattleHumanVo vo = new BattleHumanVo(voStr);
            Human battleHuman = (Human) EntityManager.getEntity(Human.class, vo.id);
            Human2 battleHuman2 = (Human2) EntityManager.getEntity(Human2.class, vo.id);
            if(battleHuman == null){
                continue;
            }
            info.addAtkList(to_p_cross_car_park_queue_role(battleHuman,battleHuman2,battleIndex, belongServerId, vo));
            info.addDefList(to_p_cross_car_park_queue_role(battleHuman,battleHuman2,battleIndex, belongServerId, vo));
            ++battleIndex;
        }

        //防御列表
        int index = battleIndex;
        for (String voStr : guardList) {
            BattleHumanVo vo = new BattleHumanVo(voStr);
            Human guardHuman = (Human) EntityManager.getEntity(Human.class, vo.id);
            if (guardHuman == null) {
                continue;
            }
            Human2 guardHuman2 = (Human2) EntityManager.getEntity(Human2.class, vo.id);
            if (guardHuman2 == null) {
                continue;
            }
            info.addDefList(to_p_cross_car_park_queue_role(guardHuman,guardHuman2, index, belongServerId, vo)); // 使用计数器作为参数
            index++;
        }
        //攻击列表
        index = battleIndex;
        for (String voStr : robList) {
            BattleHumanVo vo = new BattleHumanVo(voStr);
            Human robHuman = (Human) EntityManager.getEntity(Human.class, vo.id);
            if (robHuman == null) {
                continue;
            }
            Human2 robHuman2 = (Human2) EntityManager.getEntity(Human2.class, vo.id);
            if (robHuman2 == null) {
                continue;
            }
            info.addAtkList(to_p_cross_car_park_queue_role(robHuman,robHuman2, index, belongServerId, vo));
            index++;
        }
        info.addExt(Define.p_key_value.newBuilder().setK(EXT_IS_COLLECTED).setV(0));
        msg.setParkInfo(info);
        humanObj.sendMsg(msg);
    }

    /**
     * 自动领取跨服车位所有奖励
     */
    public void autoClaimCrossCarParkReward(CarParkCross carParkCross) {
        for(int i = 1; i <= CROSS_SPACE_NUM; i++){
            ParkSpaceVo vo = new ParkSpaceVo(getPlace(carParkCross, i));
            if(vo.humanId == 0){
                continue;
            }
            Human human = (Human) EntityManager.getEntity(Human.class, vo.humanId);
            if(human == null){
                Log.carPark.error("autoClaimCrossCarParkReward:找不到玩家id={}", vo.humanId);
                continue;
            }
            reCaculateCarReward(getAttrCrossCarPark(), vo);
            int serverId = Utils.getServerIdByHumanId(vo.humanId);
            int groupId = Utils.intValue(Utils.getRedisStrValue(RedisKeys.admin_server_group + Utils.formatTime(Utils.getTimeOfWeek(Port.getTime(), 1, 0), "yyyy-MM-dd") + serverId));
            updateRank(RedisKeys.crossCarCoinRank, Utils.formatTime(Utils.getTimeOfWeek(Port.getTime(), 1, 0), "yyyy-MM-dd"), groupId, serverId, serverId, vo.carCoin);
            //receiveReward(vo, 1.0f);//todo:接口改了
        }
        for (int i = 0; i < CROSS_SPACE_NUM; i++){
            setPlace(carParkCross, i, "{}");
        }
        carParkCross.update();
    }

    /**
     *计算战斗结果
     */
    public boolean calculateBattleResult(BattleHumanVo atkVo, BattleHumanVo defVo) {
        Param battleParam = new Param();
        int atkSkillSn = ConfParkPvpDebuff_0.get(atkVo.buffSn, BUFF_ATTACK).skill_id;
        if(atkSkillSn!=0){
            battleParam.put(ParamKey.battleParam_atk_buffSn, atkVo.buffSn);
        }
        int defSkillSn = ConfParkPvpDebuff_0.get(defVo.buffSn, BUFF_DEFEND).skill_id;
        if(defSkillSn!=0){
            battleParam.put(ParamKey.battleParam_def_buffSn, defVo.buffSn);
        }
        battleParam.put(ParamKey.battleParam_atk_hp, atkVo.hp);
        battleParam.put(ParamKey.battleParam_def_hp, defVo.hp);
        Param param = ArenaManager.inst().getParamBattle(InstanceConstants.PARKCROSSPVPCHAPTER_26, InstanceConstants.parkChapterSn, atkVo.id, defVo.id, battleParam);
        boolean isWin = Utils.longValue(param.get("winId"))==atkVo.id;
        if(isWin){
            atkVo.hp = param.get("atkHp");
            defVo.hp = 0;
        }else {
            atkVo.hp = 0;
            defVo.hp = param.get("defHp");
        }

        if(isWin){
            CarPark carParkAtk = (CarPark) EntityManager.getEntity(CarPark.class, atkVo.id);
            if(carParkAtk == null){
                Log.carPark.error("calculateBattleResult:找不到车位id={}", atkVo.id);
                return true;
            }
            if(atkVo.lv > defVo.lv+ConfGlobal.get(ConfGlobalKey.park_cross_parking_level_limit).value){
                return true;
            }
            List<Integer> buff = addBuff(carParkAtk, BUFF_CROSS_ATTACK);
            atkVo.buffSn = buff.get(0);
            atkVo.endTime = buff.get(1);
            atkVo.atkNum++;
            CarPark carParkDef = (CarPark) EntityManager.getEntity(CarPark.class, defVo.id);
            if(carParkDef == null){
                Log.carPark.error("calculateBattleResult:找不到车位id={}", defVo.id);
                return true;
            }
            addLoseBuff(carParkDef, BUFF_CROSS_DEFEND);
        }else {
            CarPark carParkDef = (CarPark) EntityManager.getEntity(CarPark.class, defVo.id);
            if(carParkDef == null){
                Log.carPark.error("calculateBattleResult:找不到车位id={}", defVo.id);
                return false;
            }
            if(defVo.lv > atkVo.lv+ConfGlobal.get(ConfGlobalKey.park_cross_parking_level_limit).value){
                return false;
            }
            List<Integer> buff = addBuff(carParkDef, BUFF_CROSS_DEFEND);
            defVo.buffSn = buff.get(0);
            defVo.endTime = buff.get(1);

            CarPark carParkAtk = (CarPark) EntityManager.getEntity(CarPark.class, atkVo.id);
            if(carParkAtk == null){
                Log.carPark.error("calculateBattleResult:找不到车位id={}", atkVo.id);
                return false;
            }
        }
        return isWin;
    }



    private Define.p_cross_car_park_queue_role.Builder to_p_cross_car_park_queue_role(Human human, Human2 human2, int order, int serverId, BattleHumanVo vo) {
        Define.p_cross_car_park_queue_role.Builder role = Define.p_cross_car_park_queue_role.newBuilder();
        role.setInfoList(to_p_role_change(human,human2.getGuildName()));
        role.setOrder(order);
        //todo:填啥
        role.setStatus(0);
        role.setServerId(serverId);
        role.setFigure(HumanManager.inst().to_p_role_figure(human, human2));
        role.addExt(Define.p_key_value.newBuilder().setK(EXT5_HP).setV(vo.hp));
        role.addExt(Define.p_key_value.newBuilder().setK(EXT5_PVP_DEBUFF_SN).setV(vo.buffSn));
        role.addExt(Define.p_key_value.newBuilder().setK(EXT5_PVP_DEBUFF_TIME).setV(vo.endTime));
        return role;
    }

    public void on_cross_car_park_parking_start_c2s(HumanObject humanObj, long id, long mountId, int pos) {
//        if(!ActivityManager.inst().isActivityOpen(humanObj, ActivityControlType.Activity_Control_Type_74)){
//            return;
//        }
        CarPark carPark = (CarPark) EntityManager.getEntity(CarPark.class, humanObj.id);
        if(carPark == null) {
            return;
        }
        humanObj.operation.carPark = carPark;
        CarPark2 carPark2 = humanObj.operation.carPark2;
        if(carPark2 == null) {
            return;
        }
        Map<Long, Integer> cdMap = clearParkCdMap(carPark2);
        if(cdMap.containsKey((long)CROSS_SPOT)){
            return;
        }
        Map<Integer,MountVo> parkSpaceMap = MountVo.mapFromJsonStr(carPark.getMountMap());
        MountVo vo = parkSpaceMap.get((int)mountId);
        if(vo == null) {
            return;
        }
        if(vo.masterId != 0) {
            return;
        }
        //只能停一个
        for (Map.Entry<Integer,MountVo> entry : parkSpaceMap.entrySet()){
            if(entry.getValue().parkType == CROSS_SPOT){
                return;
            }
        }

        int protectAttr = getAttrHumanCarPark(carPark)[SKIN_ATTR_PROTECT];
        CarParkCross carParkCross = (CarParkCross) EntityManager.getEntity(CarParkCross.class, id);
        if(carParkCross == null) {
            return;
        }
        //车位归属
        if(humanObj.getHuman().getServerId() != carParkCross.getBelongServerId()){
            return;
        }
        ParkSpaceVo parkSpaceVo = new ParkSpaceVo(getPlace(carParkCross, (pos-1)%CROSS_SPACE_NUM+1));
        if(parkSpaceVo.humanId != 0) {
            return;
        }
        String lockKey = Utils.createStr("{}.{}.{}",RedisKeys.crossParkLock,id,pos);
        if(!RedisTools.lock(EntityManager.redisClient,lockKey,"1")){
            return;
        }
        parkSpaceVo = createParkSpaceVo(id, humanObj.id, (int)mountId, vo, getAttrCrossCarPark(), protectAttr,null,null);//todo:这个交接口加两参数了
        setPlace(carParkCross, (pos-1)%CROSS_SPACE_NUM+1, parkSpaceVo.toJsonStr());
        carParkCross.update();
        RedisTools.unLock(EntityManager.redisClient,lockKey);
        vo.parkType = CROSS_SPOT;
        vo.pos = pos;
        vo.masterId = id;
        carPark.setMountMap(MountVo.mapToJsonStr(parkSpaceMap));
        carPark.update();

        MsgCarPark.cross_car_park_parking_start_s2c.Builder msg = MsgCarPark.cross_car_park_parking_start_s2c.newBuilder();
        msg.setId(id);
        msg.setSpace(to_p_car_park_space(pos, parkSpaceVo, humanObj.getHuman(), humanObj.getHuman2()));
        humanObj.sendMsg(msg);
    }

    public void on_cross_car_park_parking_stop_c2s(HumanObject humanObj, long mountId) {
        CarPark carPark = (CarPark) EntityManager.getEntity(CarPark.class, humanObj.id);
        if(carPark == null) {
            return;
        }
        humanObj.operation.carPark = carPark;
        Map<Integer,MountVo> parkSpaceMap = MountVo.mapFromJsonStr(carPark.getMountMap());
        MountVo vo = parkSpaceMap.get((int)mountId);
        if(vo == null) {
            return;
        }
        if(vo.masterId == 0) {
            return;
        }
        long parkId = vo.masterId;
        String lockKey = Utils.createStr("{}.{}.{}", RedisKeys.crossParkLock, parkId, vo.pos);
        if (!RedisTools.lock(EntityManager.redisClient, lockKey, "1")) {
            return;
        }
        CarParkCross carParkCross = (CarParkCross) EntityManager.getEntity(CarParkCross.class, parkId);
        if (carParkCross == null) {
            RedisTools.unLock(EntityManager.redisClient, lockKey);
            return;
        }
        ParkSpaceVo parkSpaceVo = new ParkSpaceVo(getPlace(carParkCross, (vo.pos-1) % CROSS_SPACE_NUM+1));
        if (parkSpaceVo.humanId != humanObj.id) {
            RedisTools.unLock(EntityManager.redisClient, lockKey);
            return;
        }
        reCaculateCarReward(getAttrCrossCarPark(), parkSpaceVo);

        int serverId = Utils.getServerIdByHumanId(parkSpaceVo.humanId);
        String dateStr = Utils.formatTime(Utils.getTimeOfWeek(Port.getTime(), 1, 0), "yyyy-MM-dd");
        int groupId = Utils.intValue(Utils.getRedisStrValue(RedisKeys.admin_server_group + dateStr + serverId));
        updateRank(RedisKeys.crossCarCoinRank, dateStr, groupId, serverId, serverId, parkSpaceVo.carCoin);

        claimReward(humanObj, parkSpaceVo, 0);
        setPlace(carParkCross, (vo.pos-1) % CROSS_SPACE_NUM+1, "{}");
        RedisTools.unLock(EntityManager.redisClient, lockKey);

        CarPark2 carPark2 = humanObj.operation.carPark2;
        Map<Long, Integer> cdMap = Utils.jsonToMapLongInt(carPark2.getCdMap());
        cdMap.put((long)CROSS_SPOT, (int)(Port.getTime()/Time.SEC) + ConfGlobal.get(ConfGlobalKey.park_interval_limit).value*60);
        carPark2.setCdMap(Utils.mapLongIntToJSON(cdMap));
        carPark2.update();
    }


    /**
     * 处理跨服车库战斗队列
     */
    public void on_cross_car_park_battle_queue_c2s(HumanObject humanObj, int type, long id) {
        CarParkCross carParkCross = (CarParkCross) EntityManager.getEntity(CarParkCross.class, id);
        if (carParkCross == null) {
            return;
        }
        long timeWeekOne = Utils.getTimeOfWeek(Port.getTime(), 1, 0);
        String dateStr = Utils.formatTime(timeWeekOne, "yyyy-MM-dd");
        MsgCarPark.cross_car_park_battle_queue_s2c.Builder msg = MsgCarPark.cross_car_park_battle_queue_s2c.newBuilder();
        msg.setId(id);
        msg.setType(type);

        List<String> battleList = Utils.getRedisList(Utils.createStr("{}.{}.{}", RedisKeys.crossParkBattleList, dateStr, id));
        List<Define.p_cross_car_park_queue_role> atkList = new ArrayList<>();
        List<Define.p_cross_car_park_queue_role> defList = new ArrayList<>();
        int order = 1;
        for (String voStr : battleList) {
            BattleHumanVo atkVo = new BattleHumanVo();
            BattleHumanVo defVo = new BattleHumanVo();
            BattleHumanVo.fromBattleDataStr(voStr, atkVo, defVo);
            if (atkVo == null || defVo == null) {
                continue;
            }
            Human atkHuman = (Human) EntityManager.getEntity(Human.class, atkVo.id);
            if (atkHuman == null) {
                continue;
            }
            Human2 atkHuman2 = (Human2) EntityManager.getEntity(Human2.class, atkVo.id);
            if (atkHuman2 == null) {
                continue;
            }
            Human defHuman = (Human) EntityManager.getEntity(Human.class, defVo.id);
            if (defHuman == null) {
                continue;
            }
            Human2 defHuman2 = (Human2) EntityManager.getEntity(Human2.class, defVo.id);
            if (defHuman2 == null) {
                continue;
            }
            atkList.add(to_p_cross_car_park_queue_role(atkHuman, atkHuman2, order, carParkCross.getBelongServerId(), atkVo).build());
            defList.add(to_p_cross_car_park_queue_role(defHuman, atkHuman2, order, carParkCross.getBelongServerId(), defVo).build());
            order++;
        }

        if (type == BATTLE_GUARD) {
            List<String> guardList = Utils.getRedisList(Utils.createStr("{}.{}.{}", RedisKeys.crossParkGuardList, dateStr, id));
            for (String voStr : guardList) {
                BattleHumanVo vo = new BattleHumanVo(voStr);
                Human guardHuman = (Human) EntityManager.getEntity(Human.class, vo.id);
                if (guardHuman == null) {
                    continue;
                }
                Human2 guardHuman2 = (Human2) EntityManager.getEntity(Human2.class, vo.id);
                if (guardHuman2 == null) {
                    continue;
                }
                msg.addList(to_p_cross_car_park_queue_role(guardHuman, guardHuman2, order, carParkCross.getBelongServerId(), vo));
                order++;
            }
            msg.addAllList(defList);
        } else if (type == BATTLE_ROB) {
            List<String> robList = Utils.getRedisList(Utils.createStr("{}.{}.{}", RedisKeys.crossParkRobList, dateStr, id));
            for (String voStr : robList) {
                BattleHumanVo vo = new BattleHumanVo(voStr);
                Human guardHuman = (Human) EntityManager.getEntity(Human.class, vo.id);
                if (guardHuman == null) {
                    continue;
                }
                Human2 guardHuman2 = (Human2) EntityManager.getEntity(Human2.class, vo.id);
                if (guardHuman == null) {
                    continue;
                }
                msg.addList(to_p_cross_car_park_queue_role(guardHuman, guardHuman2, order, carParkCross.getBelongServerId(), vo));
                order++;
            }
            msg.addAllList(atkList);
        } else if (type == BATTLE) {
            msg.addAllList(atkList);
            msg.addAllList(defList);
        }
        humanObj.sendMsg(msg);
    }

    /**
     * 处理跨服车库队列加入
     */
    public void on_cross_car_park_queue_join_c2s(HumanObject humanObj, int type, int action, long id) {
        CarPark carPark = (CarPark) EntityManager.getEntity(CarPark.class, humanObj.id);
        if(carPark == null){
            return;
        }
        CarParkCross carParkCross = (CarParkCross) EntityManager.getEntity(CarParkCross.class, id);
        if(carParkCross == null){
            return;
        }
        long timeWeekOne = Utils.getTimeOfWeek(Port.getTime(), 1, 0);
        String dateStr = Utils.formatTime(timeWeekOne, "yyyy-MM-dd");
        if(type == BATTLE_GUARD){
            List<String> guardList = Utils.getRedisList(Utils.createStr("{}.{}.{}", RedisKeys.crossParkGuardList, dateStr, id));
            if(action == 0){
                //取消驻守
                //todo:正在战斗不可取消

                for (String voStr : guardList) {
                    BattleHumanVo vo = new BattleHumanVo(voStr);
                    if (vo.id == humanObj.id) {
                        long delNum = Utils.removeRedisList(Utils.createStr("{}.{}.{}", RedisKeys.crossParkGuardList, dateStr, id), voStr);
                        if(delNum == 0){
                            return;
                        }
                        carPark.setGuardCrossId(0);
                        addLoseBuff(carPark,BUFF_CROSS_DEFEND);
                        return;
                    }
                }

            }else if(action == 1){
                //确定
                if(carParkCross.getBelongServerId() != humanObj.getHuman().getServerId()){
                    return;
                }
                if (!isCrossParkTime(CROSS_DEF_TIME)) {
                    return;
                }
                for (String voStr : guardList) {
                    BattleHumanVo vo = new BattleHumanVo(voStr);
                    if (vo.id == humanObj.id) {
                        return;
                    }
                }
                if(carPark.getGuardCrossId()!=0){
                    return;
                }
                List<Integer> buffTime = getReCaculateBuff(carPark,BUFF_CROSS_DEFEND,false);
                if(buffTime.get(1) > 0){
                    return;
                }
                BattleHumanVo vo = new BattleHumanVo(humanObj.id, humanObj.getHuman().getLevel(), buffTime.get(0), buffTime.get(1));
                RedisTools.pushToList(EntityManager.redisClient, Utils.createStr("{}.{}.{}", RedisKeys.crossParkGuardList, dateStr, id), vo.toJsonStr());
                carPark.setGuardCrossId(id);
                carPark.update();
            }
        }else if(type == BATTLE_ROB) {
            List<String> robList = Utils.getRedisList(Utils.createStr("{}.{}.{}", RedisKeys.crossParkRobList, dateStr, id));
            if(action == 0){
                //取消抢占s
                //todo:正在战斗不可取消
                for (String voStr : robList) {
                    BattleHumanVo vo = new BattleHumanVo(voStr);
                    if (vo.id == humanObj.id) {
                        long delNum = Utils.removeRedisList(Utils.createStr("{}.{}.{}", RedisKeys.crossParkRobList, dateStr, id), voStr);
                        if(delNum == 0){
                            return;
                        }
                        carPark.setRobCrossId(0);
                        addLoseBuff(carPark,BUFF_CROSS_ATTACK);
                        return;
                    }
                }

            }else if(action == 1){
                //确定
                if(humanObj.getHuman().getServerId() == carParkCross.getBelongServerId()){
                    return;
                }
                if (!isCrossParkTime(CROSS_ATK_TIME)) {
                    return;
                }
                if(carParkCross.getProtectTime() > Port.getTime()/Time.SEC){
                    return;
                }
                for (String voStr : robList) {
                    BattleHumanVo vo = new BattleHumanVo(voStr);
                    if (vo.id == humanObj.id) {
                        return;
                    }
                }
                if(carPark.getRobCrossId()!=0){
                    return;
                }
                List<Integer> buffTime = getReCaculateBuff(carPark,BUFF_CROSS_ATTACK,false);
                BattleHumanVo vo = new BattleHumanVo(humanObj.id, humanObj.getHuman().getLevel(), buffTime.get(0), buffTime.get(1));
                RedisTools.pushToList(EntityManager.redisClient, Utils.createStr("{}.{}.{}", RedisKeys.crossParkRobList, dateStr, id), vo.toJsonStr());
                carPark.setRobCrossId(id);
                carPark.update();
            }
        }
        on_cross_car_park_battle_queue_c2s(humanObj, type, id);
    }

    /**
     * 处理跨服车库队列置顶
     */
    public void on_cross_car_park_queue_stick_c2s(HumanObject humanObj, long id) {
        CarParkCross carParkCross = (CarParkCross) EntityManager.getEntity(CarParkCross.class, id);
        if (carParkCross == null) {
            return;
        }
        long timeWeekOne = Utils.getTimeOfWeek(Port.getTime(), 1, 0);
        String dateStr = Utils.formatTime(timeWeekOne, "yyyy-MM-dd");
        List<String> robList = Utils.getRedisList(Utils.createStr("{}.{}.{}", RedisKeys.crossParkRobList, dateStr, id));
        List<String> guardList = Utils.getRedisList(Utils.createStr("{}.{}.{}", RedisKeys.crossParkGuardList, dateStr, id));
        for (String voStr : robList) {
            BattleHumanVo vo = new BattleHumanVo(voStr);
            if (vo.id == humanObj.id) {
                RedisTools.delToList(EntityManager.redisClient, Utils.createStr("{}.{}.{}", RedisKeys.crossParkRobList, dateStr, id), voStr);
                RedisTools.pushToListHead(EntityManager.redisClient, Utils.createStr("{}.{}.{}", RedisKeys.crossParkRobList, dateStr, id), voStr);
                return;
            }
        }
        for (String voStr : guardList) {
            BattleHumanVo vo = new BattleHumanVo(voStr);
            if (vo.id == humanObj.id) {
                RedisTools.delToList(EntityManager.redisClient, Utils.createStr("{}.{}.{}", RedisKeys.crossParkGuardList, dateStr, id), voStr);
                RedisTools.pushToListHead(EntityManager.redisClient, Utils.createStr("{}.{}.{}", RedisKeys.crossParkGuardList, dateStr, id), voStr);
                return;
            }
        }
    }

    /**
     * 处理跨服车库查看战斗信息
     */
    public void on_cross_car_park_look_fighting_c2s(HumanObject humanObj, long id) {
        CarParkCross carParkCross = (CarParkCross) EntityManager.getEntity(CarParkCross.class, id);
        if (carParkCross == null) {
            return;
        }
        long timeWeekOne = Utils.getTimeOfWeek(Port.getTime(), 1, 0);
        String dateStr = Utils.formatTime(timeWeekOne, "yyyy-MM-dd");
        List<String> battleList = Utils.getRedisList(Utils.createStr("{}.{}.{}", RedisKeys.crossParkBattleList, dateStr, id));
        MsgCarPark.cross_car_park_look_fighting_s2c.Builder msg = MsgCarPark.cross_car_park_look_fighting_s2c.newBuilder();
        msg.setDefNum(battleList.size());
        msg.setAtkNum(battleList.size());
        for (String voStr : battleList) {
            BattleHumanVo atkVo = new BattleHumanVo();
            BattleHumanVo defVo = new BattleHumanVo();
            int endTime = BattleHumanVo.fromBattleDataStr(voStr, atkVo, defVo);
            if (atkVo == null || defVo == null) {
                continue;
            }
            Human atkHuman = (Human) EntityManager.getEntity(Human.class, atkVo.id);
            if (atkHuman == null) {
                continue;
            }
            Human defHuman = (Human) EntityManager.getEntity(Human.class, defVo.id);
            if (defHuman == null) {
                continue;
            }
            Define.p_cross_car_park_fighting.Builder fighting = Define.p_cross_car_park_fighting.newBuilder();
            fighting.setEndTime(endTime);
            fighting.setAtkInfoList(to_p_role_change(atkHuman,""));
            fighting.setDefInfoList(to_p_role_change(defHuman,""));
            fighting.addAtkDataList(Define.p_key_value.newBuilder().setK(EXT5_HP).setV(atkVo.hp));
            fighting.addAtkDataList(Define.p_key_value.newBuilder().setK(EXT5_PVP_DEBUFF_SN).setV(atkVo.buffSn));
            fighting.addAtkDataList(Define.p_key_value.newBuilder().setK(EXT5_PVP_DEBUFF_TIME).setV(atkVo.endTime));
            fighting.addDefDataList(Define.p_key_value.newBuilder().setK(EXT5_HP).setV(defVo.hp));
            fighting.addDefDataList(Define.p_key_value.newBuilder().setK(EXT5_PVP_DEBUFF_SN).setV(defVo.buffSn));
            fighting.addDefDataList(Define.p_key_value.newBuilder().setK(EXT5_PVP_DEBUFF_TIME).setV(defVo.endTime));
        }
        humanObj.sendMsg(msg);
    }

    public void on_cross_car_park_rank_c2s(HumanObject humanObj) {
        long timeWeekOne = Utils.getTimeOfWeek(Port.getTime(), 1, 0);
        String dateStr = Utils.formatTime(timeWeekOne, "yyyy-MM-dd");
        int serverId = humanObj.getHuman().getServerId();
        int groupId = Utils.intValue(Utils.getRedisStrValue(RedisKeys.admin_server_group + dateStr + serverId));
        if(groupId == 0){
            Log.carPark.error("isExitRank:找不到组id,serverId={}", serverId);
            return;
        }
        String keyAtk = Utils.createStr("{}.{}.{}.{}",RedisKeys.crossParkAtkRank,dateStr,GlobalConfVal.getZone(serverId),groupId);
        String[] atkRankScore = Utils.getMyRankAndScore(keyAtk,humanObj.id);
        String keyDef = Utils.createStr("{}.{}.{}.{}",RedisKeys.crossParkDefRank,dateStr,GlobalConfVal.getZone(serverId),groupId);
        String[] defRankScore = Utils.getMyRankAndScore(keyDef,humanObj.id);
        List<String> serverCoinRankStr = Utils.getRedisZaddStrValueList(Utils.createStr("{}.{}.{}.{}",RedisKeys.crossCarCoinRank, dateStr, GlobalConfVal.getZone(serverId),groupId), 0, -1,true);

        MsgCarPark.cross_car_park_rank_s2c.Builder msg = MsgCarPark.cross_car_park_rank_s2c.newBuilder();
        msg.setAtkKill(Utils.intValue(atkRankScore[1]));
        msg.setAtkRank(Utils.intValue(atkRankScore[0]));
        msg.setDefKill(Utils.intValue(defRankScore[1]));
        msg.setDefRank(Utils.intValue(defRankScore[0]));
        for(int i = 0; i < serverCoinRankStr.size(); i+=2){
            Define.p_cross_car_park_rank.Builder rank = Define.p_cross_car_park_rank.newBuilder();
            rank.setServerId(Utils.intValue(serverCoinRankStr.get(i)));
            rank.setRank(i/2+1);
            rank.setCoinValue(Utils.longValue(serverCoinRankStr.get(i+1)));
            //todo:旗子，ext
//            rank.addGuildFlag(Define.p_key_value_string.newBuilder().setK(1).setV(0).setS(""));
//            rank.addGuildFlag(Define.p_key_value_string.newBuilder().setK(2).setV(0).setS(""));
//            rank.addGuildFlag(Define.p_key_value_string.newBuilder().setK(3).setV(0).setS(""));
//            rank.addGuildFlag(Define.p_key_value_string.newBuilder().setK(4).setV(0).setS(""));
//            rank.addGuildFlag(Define.p_key_value_string.newBuilder().setK(5).setV(0).setS(""));
//            rank.addGuildFlag(Define.p_key_value_string.newBuilder().setK(6).setV(0).setS(""));
            msg.addRankList(rank);
        }
        humanObj.sendMsg(msg);
    }

    /**
     * 处理跨服车库下一场开启时间
     */
    public void on_cross_car_park_next_time_c2s(HumanObject humanObj) {
        //开服大于等于21天后的周一开启。以后的周一开启
        ConfActivityControl confActivityControl = ConfActivityControl.get(CROSS_ACTIVITY_SN);
        long openServerTime = Util.getOpenServerTime(humanObj.getHuman().getServerId());
        long canOpenDayTime = openServerTime + confActivityControl.openTime*Time.DAY;
        long activityOpenTime = Utils.getTimeOfWeek(canOpenDayTime, 1, 0) == canOpenDayTime ? canOpenDayTime : Utils.getTimeOfWeek(canOpenDayTime + Time.WEEK, 1, 0);
        long now = Port.getTime();
        MsgCarPark.cross_car_park_next_time_s2c.Builder msg = MsgCarPark.cross_car_park_next_time_s2c.newBuilder();
        if(now >= activityOpenTime){
            long timeWeekOne = Utils.getTimeOfWeek(Port.getTime(), 1, 0);
            if(isCrossParkTime(CROSS_END_TIME)){
                msg.setNextTime((int)(timeWeekOne/Time.SEC));
            }else {
                msg.setNextTime((int)(timeWeekOne+Time.WEEK/Time.SEC));
            }
        }else {
            msg.setNextTime((int)(activityOpenTime/Time.SEC));
        }
        humanObj.sendMsg(msg);
    }

    public void on_cross_car_park_role_state_c2s(HumanObject humanObj, int type) {
        CarPark carPark = (CarPark) EntityManager.getEntity(CarPark.class, humanObj.id);
        if(carPark == null){
            return;
        }
        MsgCarPark.cross_car_park_role_state_s2c.Builder msg = MsgCarPark.cross_car_park_role_state_s2c.newBuilder();
        msg.setType(type);
        if(type == BATTLE_GUARD) {
            List<Integer> buffTime = getReCaculateBuff(carPark, BUFF_CROSS_DEFEND, false);
            msg.setParkId(carPark.getGuardCrossId());
            if(buffTime.get(0) == 0){
                msg.setFreezeTime(buffTime.get(1));
            }else {
                msg.setFreezeTime(0);
            }
            //todo:data_list
        }else{
            List<Integer> buffTime = getReCaculateBuff(carPark, BUFF_CROSS_ATTACK, false);
            msg.setParkId(carPark.getRobCrossId());
            if(buffTime.get(0) == 0){
                msg.setFreezeTime(buffTime.get(1));
            }else {
                msg.setFreezeTime(0);
            }
            //todo:data_list
        }
        humanObj.sendMsg(msg);
    }

    // 添加日志
    public void addRecord(CarPark2 carPark2, Define.p_car_park_record record) {
        MsgCarPark.car_park_history_s2c.Builder historyBuilder;
        String compressedStr = carPark2.getParkReport();
        
        if(Utils.isNullOrEmptyJSONString(compressedStr)) {
            historyBuilder = MsgCarPark.car_park_history_s2c.newBuilder();
        } else {
            // 解压并解析之前的记录
            historyBuilder = Utils.decompressProtoLZ4(compressedStr,MsgCarPark.car_park_history_s2c.parser()).toBuilder();
        }
        
        // 在开头添加新记录
        historyBuilder.addRecordList(0, record);
        
        // 超出上限则删除最老的记录
        while(historyBuilder.getRecordListCount() > MAX_RECORD_SIZE) {
            historyBuilder.removeRecordList(historyBuilder.getRecordListCount() - 1);
        }
        
        // 压缩后保存到数据库
        carPark2.setParkReport(Utils.compressProtoLZ4(historyBuilder.build()));
    }

    // 获取日志列表
    public MsgCarPark.car_park_history_s2c getRecordList(CarPark2 carPark2) {
        String compressedStr = carPark2.getParkReport();
        if(Utils.isNullOrEmptyJSONString(compressedStr)) {
            return MsgCarPark.car_park_history_s2c.newBuilder().build();
        }
        // 解压并获取记录列表
        MsgCarPark.car_park_history_s2c msg = Utils.decompressProtoLZ4(compressedStr,MsgCarPark.car_park_history_s2c.parser());
        return msg;
    }

    public void on_car_park_history_c2s(HumanObject humanObj) {
        CarPark2 carPark2 = humanObj.operation.carPark2;
        if(carPark2 == null){
            return;
        }
        humanObj.sendMsg(getRecordList(carPark2));
    }

    public void on_car_park_read_c2s(HumanObject humanObj) {
        CarPark2 carPark2 = humanObj.operation.carPark2;
        if(carPark2 == null){
            return;
        }
        String compressedStr = carPark2.getParkReport();
        if(compressedStr.isEmpty()) {
            return ;
        }
        MsgCarPark.car_park_history_s2c.Builder historyBuilder =  Utils.decompressProtoLZ4(compressedStr,MsgCarPark.car_park_history_s2c.parser()).toBuilder();

        List<Long> changeList = new ArrayList<>();
        for(int i = 0; i < historyBuilder.getRecordListCount(); i++) {
            if(historyBuilder.getRecordList(i).getIsRead() == 0) {
                historyBuilder.setRecordList(i,
                        historyBuilder.getRecordList(i).toBuilder()
                                .setIsRead(1)
                                .build()
                );
                changeList.add(historyBuilder.getRecordList(i).getNewId());
            }
        }
        if(!changeList.isEmpty()) {
            carPark2.setParkReport(Utils.compressProtoLZ4(historyBuilder.build()));
            MsgCarPark.car_park_look_s2c.Builder builder = MsgCarPark.car_park_look_s2c.newBuilder();
            builder.addAllVidList(changeList);
            humanObj.sendMsg(builder);
        }
    }


    /**
     * 战报列表
     */
    public void handleBattleReportListC2S(HumanObject humanObj) {
        MsgCarPark.car_park_report_s2c.Builder msg = MsgCarPark.car_park_report_s2c.newBuilder();
        String redisKey = Utils.createStr("{}{}", RedisKeys.carParkBattleHistoryList, humanObj.id);
        RedisTools.getListRange(EntityManager.getRedisClient(), redisKey, 0, -1, res -> {
            if(res.failed()){
                return;
            }
            JsonArray jsonArray = res.result();
            if(jsonArray == null || jsonArray.isEmpty()){
                humanObj.sendMsg(msg);
                return;
            }

            long currentTime = Port.getTime() / Time.SEC;
            int validCount = jsonArray.size();

            // 处理每条战报数据
            for (int i = 0; i < jsonArray.size(); i++) {
                try {
                    String protoBuf = jsonArray.getString(i);
                    Define.p_battle_video report = Define.p_battle_video.parseFrom(
                            protoBuf.getBytes(StandardCharsets.ISO_8859_1));

                    // 检查是否过期
                    if (currentTime - report.getTime() > ParamKey.historyExpireTime) {
                        validCount = i;
                        break;
                    }
                    msg.addHistoryList(report);
                } catch (Exception e) {
                    Log.temp.error("解析战报数据失败, index={}: {}", i, e.getMessage());
                }
            }

            // 如果有过期数据,清理Redis中的过期数据
            if (validCount < jsonArray.size()) {
                if (validCount > 0) {
                    // 保留有效的数据
                    RedisTools.ltrim(EntityManager.getRedisClient(), redisKey, 0, validCount-1);
                } else {
                    // 如果所有数据都过期了，清空整个列表
                    RedisTools.del(EntityManager.getRedisClient(), redisKey);
                }
            }

            humanObj.sendMsg(msg);
        });
    }

    // 添加新的消息处理方法
    public void on_car_park_parking_help_c2s(HumanObject humanObj, long targetId, int pos) {
        CarParkServiceProxy proxy = CarParkServiceProxy.newInstance();
        proxy.helpCollectCarPark(humanObj.id, targetId, pos);
        proxy.listenResult(this::_result_helpCollectCarPark, 
            "humanObj", humanObj,
            "pos", pos);
    }

    private void _result_helpCollectCarPark(Param results, Param context) {
        if (results == null || results.isEmpty()) {
            // 刷新车库信息
            MsgCarPark.car_park_parking_help_s2c.Builder msg = MsgCarPark.car_park_parking_help_s2c.newBuilder();
            HumanObject humanObj = context.get("humanObj");
            humanObj.sendMsg(msg);
            on_car_park_info_c2s(humanObj, HUMAM_SPOT, humanObj.id, 0);
            return;
        }

        int errCode = results.get("errCode");
        if (errCode > 0) {
            HumanObject humanObj = context.get("humanObj");
            humanObj.sendMsg(MsgError.error_info_s2c.newBuilder().setCode(errCode));
        }
    }

    public void addCarParkIncome(HumanObject humanObj, int income) {
        ProduceManager.inst().produceAdd(humanObj, ItemConstants.goods_菇车币, income, MoneyItemLogKey.停车场收益);
    }
    public void updateRewardAdd(HumanObject humanObj, Map<Integer, Integer> rewardAddMap) {
        CarParkServiceProxy.newInstance().updateRewardAdd(humanObj.id, rewardAddMap);
    }
}
