package org.gof.demo.worldsrv.back;


import com.alibaba.fastjson.JSONArray;
import org.gof.core.Port;
import org.gof.core.support.Time;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.activity.ActivityManager;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.ConfBackCheckin;
import org.gof.demo.worldsrv.config.ConfBackMall;
import org.gof.demo.worldsrv.config.ConfGlobal;
import org.gof.demo.worldsrv.entity.Human;
import org.gof.demo.worldsrv.entity.Human2;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.support.Util;
import org.gof.demo.worldsrv.support.enumKey.ConfGlobalKey;
import org.gof.demo.worldsrv.task.type.TaskVO;

import java.util.ArrayList;
import java.util.List;

public class BackUtils {
    /**
     * 获取回归开始结束时间
     * @param humanObj
     */
    public static long[] getBackStartAndEndTime(HumanObject humanObj) {
        Human2 human2 = humanObj.getHuman2();
        long backStartTime = human2.getBackStartTime();
        long triggerBackNum = human2.getTriggerBackNum();
        if(triggerBackNum == 0) {
            return new long[]{0, 0};
        }
        long duration = Time.DAY_7_SEC;
        ConfGlobal conf = ConfGlobal.get(triggerBackNum == 1 ? ConfGlobalKey.back_time1 : ConfGlobalKey.back_time2);
        if(conf != null) {
            duration = conf.value * Time.DAY;
        }
        long backEndTime = backStartTime + duration;
        return new long[]{backStartTime, backEndTime};
    }

    /**
     * 回归活动是否开放
     * @param humanObj
     */
    public static boolean isBackActivityOpen(HumanObject humanObj) {
        long[] timeInfo = getBackStartAndEndTime(humanObj);
        long timeNow = Port.getTime();
        return timeNow >= timeInfo[0] && timeNow < timeInfo[1];
    }

    /**
     * 是否触发回归活动
     * @param humanObj
     */
    public static boolean isTriggerBackActivity(HumanObject humanObj) {
        int triggerBackNum = humanObj.getHuman2().getTriggerBackNum();
        if(triggerBackNum >= 2) {
            // 回归一共只能触发2次
            return false;
        }
        Human human = humanObj.getHuman();
        long timeLogin = human.getTimeLogin();
        long timeLogout = human.getTimeLogout();
        int lossDay = Utils.getDaysBetween(timeLogin, timeLogout);
        int openDay = Utils.getDaysBetween(timeLogin, Util.getOpenServerTime(human.getServerId())) + 1;
        int needOpenDay = 30;
        int needLevel = 30;
        int needLossDay = 15;
        ConfGlobal conf = ConfGlobal.get(ConfGlobalKey.back_leave);
        if(conf != null && conf.intArray != null && conf.intArray.length == 3) {
            needOpenDay = conf.intArray[0];
            needLevel = conf.intArray[1];
            needLossDay = conf.intArray[2];
            if(triggerBackNum > 0){
                ConfGlobal confTime3 = ConfGlobal.get(ConfGlobalKey.back_time3);
                if(confTime3 != null) {
                    needLossDay = Utils.intValue(confTime3.strValue);
                }
            }
        }
        return humanObj.getHuman().getLevel() >= needLevel && lossDay >= needLossDay && openDay >= needOpenDay;
    }

    /**
     * 获取回归登录奖励sn集合
     * @param humanObj
     * @return
     */
    public static List<Integer> getBackCheckInConfArray(HumanObject humanObj) {
        int openDay = humanObj.getHuman2().getBackOpenDay();
        int lossDay = humanObj.getHuman2().getBackLossDay();
        List<Integer> snList = new ArrayList<>();
        for(ConfBackCheckin conf : ConfBackCheckin.findAll()){
            if(conf.open_day != null && conf.open_day.length == 2 && openDay >= conf.open_day[0] && openDay <= conf.open_day[1]
                    && conf.lost_day != null && conf.lost_day.length == 2 && lossDay >= conf.lost_day[0] && lossDay <= conf.lost_day[1]){
                snList.add(conf.sn);
            }
        }
        return snList;
    }

    /**
     * 获取回归登录奖励配置
     * @param humanObj
     * @param day
     * @return
     */
    public static ConfBackCheckin getBackCheckInConf(HumanObject humanObj, int day) {
        int openDay = humanObj.getHuman2().getBackOpenDay();
        int lossDay = humanObj.getHuman2().getBackLossDay();
        for(ConfBackCheckin conf : ConfBackCheckin.findAll()){
            if(conf.open_day != null && conf.open_day.length == 2 && openDay >= conf.open_day[0] && openDay <= conf.open_day[1]
                    && conf.lost_day != null && conf.lost_day.length == 2 && lossDay >= conf.lost_day[0] && lossDay <= conf.lost_day[1]
                    && conf.day == day){
                return conf;
            }
        }
        return null;
    }

    public static Define.p_act_task.Builder to_p_act_task(TaskVO vo){
        Define.p_act_task.Builder dInfo = Define.p_act_task.newBuilder();
        dInfo.setTaskId(vo.taskSn);
        dInfo.setState(ActivityManager.inst().getTaskState(vo.getStatus()));
        dInfo.setCount(vo.getPlan());
        dInfo.setGroupId(vo.getType());
        return dInfo;
    }

    /**
     * 获取回归商城商品列表
     * @param humanObj
     * @return
     */
    public static List<Integer> getMallTypeBackSnList(HumanObject humanObj) {
        int openDay = humanObj.getHuman2().getBackOpenDay();
        int lossDay = humanObj.getHuman2().getBackLossDay();
        List<Integer> snList = new ArrayList<>();
        for(ConfBackMall conf : ConfBackMall.findAll()){
            if(conf.open_day != null && conf.open_day.length == 2 && openDay >= conf.open_day[0] && openDay <= conf.open_day[1]
                    && conf.lost_day != null && conf.lost_day.length == 2 && lossDay >= conf.lost_day[0] && lossDay <= conf.lost_day[1]){
                snList.add(conf.sn);
            }
        }
        return snList;
    }

    /**
     * 是否有可领取的回归登录奖励
     * @param receivedInfo
     * @param maxDay
     * @return
     */
    public static boolean hasBackCheckInRewardToReceive(List<Integer> receivedInfo, int maxDay){
        if (receivedInfo == null || receivedInfo.isEmpty()) {
            return true;
        }
        for (int day = 1; day <= maxDay; day++) {
            if (!receivedInfo.contains(day)) {
                return true;
            }
        }
        return false;
    }
}
