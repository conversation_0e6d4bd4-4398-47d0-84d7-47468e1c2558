package org.gof.demo.worldsrv.common;

import org.gof.core.gen.proxy.DistrClass;
import org.gof.core.scheduler.ScheduleMethod;
import org.gof.demo.worldsrv.support.D;

@DistrClass(
	servId = D.SERV_DATA_RESET
)
/**
 * 天（星期）（1~7 1=SUN 或 SUN，MON，TUE，WED，THU，FRI，SAT）
 * <AUTHOR>
 * @Date 2024/7/5
 * @Param
 */
public class DataResetService extends GameServiceBase {
	//每日0时重置
	public static final String CRON_DAY_ZERO = "1 0 0 * * ?";
	//每日5时重置
	public static final String CRON_DAY_FIVE = "1 0 5 * * ?";
	//每日12时重置
	public static final String CRON_DAY_12ST = "1 0 12 * * ?";
	//每日14时1秒
	public static final String CRON_DAY_14ST = "1 0 14 * * ?";
	//每日18时重置
	public static final String CRON_DAY_18ST = "1 0 18 * * ?";
	//每日21时重置
	public static final String CRON_DAY_21ST = "1 0 21 * * ?";
	//每日23:55分
	public static final String CRON_DAY_23_55ST = "1 55 23 * * ?";
	//每日23:30分
	public static final String CRON_DAY_23_30ST = "1 30 23 * * ?";
	// 每日每时30分
	public static final String CRON_DAY_HOUR_30ST = "1 30 * * * ?";
	
	//每周零时重置
	public static final String CRON_WEEK_ZERO = "1 0 0 ? * MON";
	//每周五时重置
	public static final String CRON_WEEK_FIVE = "1 0 5 ? * MON";

	//每小时执行一次
	public static final String CRON_DAY_HOUR = "1 0 0/1 * * ?";
	// 每半小时执行一次
	public static final String CRON_DAY_MIN_30 = "0 0/30 * * * ?";
	//每周一三五零时重置
	public static final String CRON_WEEK_135 = "1 0 0 ? * MON,WED,FRI";
	//每周四,周一零时5分重置
	public static final String CRON_WEEK_4_1_ZERO = "1 5 0 ? * 2,5";

	//每周四,五点时重置
	public static final String CRON_WEEK_4_HOUR_5 = "1 0 5 ? * 5";

	public static final String CRON_WEEK_4_1_30_MIN = "1 30 0 ? * 2,5";
	//每周五,周一1时重置
	public static final String CRON_WEEK_5_1_huor_1 = "1 0 1 ? * 2,6";

	//每周五,周一2时重置
	public static final String CRON_WEEK_5_1_huor_2 = "1 0 2 ? * 2,6";

	public static final String CRON_WEEK_5_1_huor_ZERO = "1 0 0 ? * 2,6";
	// 每周一0点
	public static final String CRON_WEEK_1_0 = "1 0 0 ? * MON";

	// 每周日5点25分
	public static final String CRON_WEEK_7_ST_5_25 = "1 25 5 ? * 1";
	// 每周日23点35分
	public static final String CRON_WEEK_7_ST_23_35 = "1 35 23 ? * 1";

	// 每周日23点45分
	public static final String CRON_WEEK_7_ST_23_45 = "1 45 23 ? * 1";

	// 每周日23点50分
	public static final String CRON_WEEK_7_ST_23_50 = "1 50 23 ? * 1";

	// 每周 五 22点
	public static final String CRON_WEEK_5_hour_22 = "1 0 22 ? * 6";
	// 每周 三 四 五 22点
	public static final String CRON_WEEK_345_hour_22 = "1 0 22 ? * 4,5,6";
	// 每周一1点30分
	public static final String CRON_WEEK_1_1hour_30m = "1 30 1 ? * MON";

	// 每周一5点
	public static final String CRON_WEEK_MON_FIVE_HOUR = "1 0 5 ? * MON";
	public static final String CRON_WEEK_1_5ST = "1 0 5 ? * 2";

	//每月零点重置
	public static final String CRON_MONTH_1_1ST = "1 0 0 1 * ?";

	// 每秒执行
	public static final String CRON_EVERY_SECOUND = "0/1 * * * * ?";

	public DataResetService(GamePort port) {
		super(port);
	}
	@ScheduleMethod(DataResetService.CRON_DAY_HOUR)
	@Override
	protected void init() {
		
	}
}
