package org.gof.demo.worldsrv.common;

import com.alibaba.fastjson.JSONObject;
import org.gof.core.gen.proxy.DistrMethod;
import org.gof.demo.worldsrv.support.Log;

import java.util.HashMap;
import java.util.Map;

/**
 * 中心服用的Service父类，后台访问该Service的子类，由子类再转发给各个游戏服务对应的Service
 * 为什么抽这个类出来，是为了方便中心服的Service拿到各个游戏服的nodeId，要不我每个中心服的Service就要自己去维护游戏服id和nodeid的映射
 *
 * @Author: chenkeyi
 * @Date: 2023/7/13 16:45
 */
public abstract class AdminServiceBase extends GameServiceBase {

    /**
     * 游戏服id和节点id的映射，设置静态，不需要每个子类自己维护，
     * 正好现在游戏服上的CheckWorldService启动时会主动注册游戏服信息到中心服的CheckAdminService(已调整成继承AdminServiceBase)
     */
    protected static Map<String, String> remoteIdAddrMap = new HashMap<>();

    public static Map<String, String> remoteIdHttpAddrMap = new HashMap<>();

    public AdminServiceBase(GamePort port) {
        super(port);
    }

    @Override
    protected void init() {
        // 直接实现，这些中心服的Service初始化一般不需要做啥，如果有需要，子类再自己重写
    }

    @DistrMethod
    public void register(String serverId, String nodeId) {
        Log.game.error("serverId={}，nodeId={} 注册到中心服！", serverId, nodeId);
        remoteIdAddrMap.put(serverId, nodeId);
    }

    @DistrMethod
    public void registerHttpUrl(String json) {
        JSONObject jo = JSONObject.parseObject(json);
        String serverId = jo.getString("serverId");
        String httpUrl = jo.getString("httpUrl");
        Log.game.error("serverId={}，httpUrl={} 注册到中心服！", serverId, httpUrl);
        remoteIdHttpAddrMap.put(serverId, httpUrl);
    }
}
