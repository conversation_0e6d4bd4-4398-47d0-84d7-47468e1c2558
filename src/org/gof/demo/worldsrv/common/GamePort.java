package org.gof.demo.worldsrv.common;

import org.gof.core.Port;
import org.gof.core.Service;
import org.gof.core.support.SysException;
import org.gof.core.support.idAllot.IdAllotPoolBase;
import org.gof.demo.seam.id.IdAllotPool;
import org.gof.demo.seam.main.ServCheck;

import java.util.HashMap;
import java.util.Map;

public class GamePort extends Port {



	private Map<String,Boolean> serviceStatus = new HashMap<>();


	public GamePort(String name) {
		super(name);
	}

//	public void initServiceStatus(List<String> servs){
//		for(String serv: servs){
//			serviceStatus.put(serv,false);
//		}
//	}
	/**
	 * 覆写父类函数
	 * 增加防ID重复的判断机制
	 */
	@Override
	public void addService(Service service) {
		//先检查一下此ID之前是否已经添加过 避免由于ID冲突造成隐藏的BUG
		Service serv = getServices(service.getId());
		if(serv != null) {
			throw new SysException("PortCommon添加下属服务时发送重复的号码：ID={}", service.getId());
		}
		
		super.addService(service);

		ServCheck.servStarted(service.getId().toString());

//		boolean startAll = true;
//		for(Boolean started: serviceStatus.values()){
//			if(!started){
//				startAll = false;
//				break;
//			}
//		}
//		if(startAll){
//			getNode().
//		}

	}

	@Override
	protected IdAllotPoolBase initIdAllotPool() {
		return new IdAllotPool(this);
	}
}
