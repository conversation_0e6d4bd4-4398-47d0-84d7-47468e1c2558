package org.gof.demo.worldsrv.accumulatedRecharge;

public class AccumulatedRechargeConst {
    // 领奖类型
    public static final int REWARD_TYPE_FREE = 1;           // 免费礼包
    public static final int REWARD_TYPE_DAILY = 2;          // 每日充值礼包
    public static final int REWARD_TYPE_ACCUMULATED = 3;    // 累充礼包

    // 礼包状态
    public static final int REWARD_STATE_UNABLE_TO_CLAIM = 1;       // 无法领取
    public static final int REWARD_STATE_CAN_BE_CLAIMED = 2;        // 可领取
    public static final int REWARD_STATE_RECEIVED = 3;              // 已领取

    // 累充奖励补发
    public static final int REWARD_MAIL_ID = 90001;
}
