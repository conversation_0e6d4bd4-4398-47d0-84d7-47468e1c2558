package org.gof.demo.worldsrv.accumulatedRecharge;

import org.gof.core.support.ManagerBase;
import org.gof.core.support.Param;
import org.gof.core.support.Utils;
import org.gof.core.support.observer.Listener;
import org.gof.demo.worldsrv.character.HumanDailyResetInfo;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.ConfDailyRecharge;
import org.gof.demo.worldsrv.config.ConfDailyRechargeRound;
import org.gof.demo.worldsrv.config.ConfGlobal;
import org.gof.demo.worldsrv.entity.Human2;
import org.gof.demo.worldsrv.human.FuncOpenType;
import org.gof.demo.worldsrv.instance.InstanceConstants;
import org.gof.demo.worldsrv.instance.InstanceManager;
import org.gof.demo.worldsrv.mail.MailManager;
import org.gof.demo.worldsrv.msg.MsgAccumulatedRecharge;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.enumKey.ConfGlobalKey;
import org.gof.demo.worldsrv.support.enumKey.DailyResetTypeKey;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;
import org.gof.demo.worldsrv.support.observer.EventKey;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AccumulatedRechargeManager extends ManagerBase {
    /**
     * 获取实例
     */
    public static AccumulatedRechargeManager inst() {
        return inst(AccumulatedRechargeManager.class);
    }

    private boolean isAccumulatedRechargeNotOpen(HumanObject humanObj) {
        return !humanObj.isModUnlock(FuncOpenType.FUNC_ACCUMULATED_RECHARGE);
    }

    /**
     * 累充信息总览
     * @param humanObj
     */
    public void _msg_accumulated_recharge_info_c2s(HumanObject humanObj) {
        if(isAccumulatedRechargeNotOpen(humanObj)){
            return;
        }
        MsgAccumulatedRecharge.accumulated_recharge_info_s2c.Builder msg = MsgAccumulatedRecharge.accumulated_recharge_info_s2c.newBuilder();
        Human2 human2 = humanObj.getHuman2();
        List<Integer> receivedInfo = Utils.toJSONArray(human2.getAccumulatedRechargeReceivedInfo()).toJavaList(Integer.class);
        msg.setRound(human2.getAccumulatedRechargeRound());
        msg.setRechargeDay(human2.getAccumulatedRechargeDay());
        HumanDailyResetInfo freeInfo = humanObj.getDailyResetInfo(DailyResetTypeKey.dailyRechargeFreeReceiveNum.getType());
        msg.setFreeState(freeInfo.getValue() > 0 ? AccumulatedRechargeConst.REWARD_STATE_RECEIVED : AccumulatedRechargeConst.REWARD_STATE_CAN_BE_CLAIMED);
        HumanDailyResetInfo dailyInfo = humanObj.getDailyResetInfo(DailyResetTypeKey.dailyRechargeReceiveNum.getType());
        HumanDailyResetInfo ptInfo = humanObj.getDailyResetInfo(DailyResetTypeKey.dailyRechargePt.getType());
        if(ptInfo.getValue() > 0){
            msg.setDailyState(dailyInfo.getValue() > 0 ? AccumulatedRechargeConst.REWARD_STATE_RECEIVED : AccumulatedRechargeConst.REWARD_STATE_CAN_BE_CLAIMED);
        }
        else{
            msg.setDailyState(AccumulatedRechargeConst.REWARD_STATE_UNABLE_TO_CLAIM);
        }
        msg.addAllReceivedInfo(receivedInfo);
        humanObj.sendMsg(msg);
//        Log.accumulatedRecharge.info("===累充信息总览 humanId={} msg={}", humanObj.id, msg);
    }

    /**
     * 领取累充奖励
     * @param humanObj
     * @param type
     * @param day
     */
    public void _msg_accumulated_recharge_reward_c2s(HumanObject humanObj, int type, int day) {
        if(isAccumulatedRechargeNotOpen(humanObj)){
            return;
        }
        switch (type) {
            case AccumulatedRechargeConst.REWARD_TYPE_FREE:
                claimFreeReward(humanObj);
                break;
            case AccumulatedRechargeConst.REWARD_TYPE_DAILY:
                claimDailyReward(humanObj);
                break;
            case AccumulatedRechargeConst.REWARD_TYPE_ACCUMULATED:
                claimAccumulatedReward(humanObj, day);
                break;
            default:
                Log.accumulatedRecharge.warn("===领取累充奖励 humanId={} type={} error", humanObj.id, type);
                break;
        }
    }

    public void claimFreeReward(HumanObject humanObj) {
        HumanDailyResetInfo freeInfo = humanObj.getDailyResetInfo(DailyResetTypeKey.dailyRechargeFreeReceiveNum.getType());
        if(freeInfo.getValue() > 0){
            Log.accumulatedRecharge.warn("===今日已领取累充免费奖励 humanId={}", humanObj.id);
            return;
        }
        int round = humanObj.getHuman2().getAccumulatedRechargeRound();
        ConfDailyRechargeRound conf = ConfDailyRechargeRound.get(round);
        if(conf == null || conf.free_reward == null || conf.free_reward.length == 0){
            Log.accumulatedRecharge.warn("===领取累充免费奖励失败 配置不存在");
            return;
        }
        // 给奖励
        int[] rewards = conf.free_reward;
        ProduceManager.inst().produceAdd(humanObj, rewards, MoneyItemLogKey.累充免费奖励);
        InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, InstanceConstants.showType_0, rewards);
        // 标记已领取
        freeInfo.setValue(1);
        humanObj.saveDailyResetRecord();

        MsgAccumulatedRecharge.accumulated_recharge_reward_s2c.Builder msg = MsgAccumulatedRecharge.accumulated_recharge_reward_s2c.newBuilder();
        msg.setType(AccumulatedRechargeConst.REWARD_TYPE_FREE);
        humanObj.sendMsg(msg);
        Log.accumulatedRecharge.info("===领取累充免费奖励 humanId={}", humanObj.id);
    }

    public void claimDailyReward(HumanObject humanObj) {
        HumanDailyResetInfo receiveInfo = humanObj.getDailyResetInfo(DailyResetTypeKey.dailyRechargeReceiveNum.getType());
        if(receiveInfo.getValue() > 0){
            Log.accumulatedRecharge.warn("===今日已领取累充每日奖励 humanId={}", humanObj.id);
            return;
        }
        HumanDailyResetInfo canReceiveInfo = humanObj.getDailyResetInfo(DailyResetTypeKey.dailyRechargeCanReceiveNum.getType());
        if(canReceiveInfo.getValue() == 0){
            Log.accumulatedRecharge.warn("===累充每日奖励可领取次数不足 humanId={}", humanObj.id);
            return;
        }
        int day = humanObj.getHuman2().getAccumulatedRechargeDay();
        ConfDailyRecharge conf = AccumulatedRechargeUtils.getAccumulatedRechargeConf(humanObj, day);
        if(conf == null || conf.daily_reward == null){
            Log.accumulatedRecharge.warn("===领取累充每日奖励失败 humanId={} day={}的奖励配置不存在", humanObj.id, day);
            return;
        }
        // 给奖励
        int[][] rewards = conf.daily_reward;
        ProduceManager.inst().produceAdd(humanObj, rewards, MoneyItemLogKey.累充每日奖励);
        InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, InstanceConstants.showType_0, rewards);
        // 标记已领取
        receiveInfo.setValue(1);
        humanObj.saveDailyResetRecord();

        MsgAccumulatedRecharge.accumulated_recharge_reward_s2c.Builder msg = MsgAccumulatedRecharge.accumulated_recharge_reward_s2c.newBuilder();
        msg.setType(AccumulatedRechargeConst.REWARD_TYPE_DAILY);
        humanObj.sendMsg(msg);
        Log.accumulatedRecharge.info("===领取累充每日奖励 humanId={} day={}", humanObj.id, day);
    }

    public void claimAccumulatedReward(HumanObject humanObj, int day) {
        Human2 human2 = humanObj.getHuman2();
        int maxDay = human2.getAccumulatedRechargeDay();
        if(day > maxDay){
            Log.accumulatedRecharge.warn("===领取累充奖励失败 humanId={} day={} > maxDay={}", humanObj.id, day, maxDay);
            return;
        }
        List<Integer> receivedInfo = Utils.toJSONArray(human2.getAccumulatedRechargeReceivedInfo()).toJavaList(Integer.class);
        if(receivedInfo.contains(day)){
            Log.accumulatedRecharge.warn("===领取累充奖励失败 humanId={} 已领取过day={}的奖励", humanObj.id, day);
            return;
        }
        ConfDailyRecharge conf = AccumulatedRechargeUtils.getAccumulatedRechargeConf(humanObj, day);
        if(conf == null || conf.reward == null){
            Log.accumulatedRecharge.warn("===领取累充奖励失败 humanId={} day={}的奖励配置不存在", humanObj.id, day);
            return;
        }
        // 给奖励
        int[][] rewards = conf.reward;
        ProduceManager.inst().produceAdd(humanObj, rewards, MoneyItemLogKey.累充奖励);
        InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, InstanceConstants.showType_0, rewards);
        // 添加已领取的天数
        receivedInfo.add(day);
        human2.setAccumulatedRechargeReceivedInfo(receivedInfo.toString());

        MsgAccumulatedRecharge.accumulated_recharge_reward_s2c.Builder msg = MsgAccumulatedRecharge.accumulated_recharge_reward_s2c.newBuilder();
        msg.setType(AccumulatedRechargeConst.REWARD_TYPE_ACCUMULATED);
        msg.addAllReceivedInfo(receivedInfo);
        humanObj.sendMsg(msg);
        Log.accumulatedRecharge.info("===领取累充奖励 humanId={} day={} receivedInfo={}", humanObj.id, day, receivedInfo);
    }

    @Listener(EventKey.HUMAN_LOGIN_FINISH)
    public void _on_HUMAN_LOGIN_FINISH(Param param) {
        HumanObject humanObj = param.get("humanObj");
        // 登录刷新累充轮次
        refreshAccumulatedRechargeRound(humanObj);
        // 推累充info
        _msg_accumulated_recharge_info_c2s(humanObj);
    }

    /**
     * 刷新累充轮次
     * @param humanObj
     */
    public void refreshAccumulatedRechargeRound(HumanObject humanObj) {
        HumanDailyResetInfo canReceiveInfo = humanObj.getDailyResetInfo(DailyResetTypeKey.dailyRechargeCanReceiveNum.getType());
        if(canReceiveInfo.getValue() > 0){
            // 今日已获得充值可领取次数，则直接跳过
            return;
        }
        ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.Daily_Recharge_Close);
        // 累充关闭
        boolean isClosed = confGlobal != null && confGlobal.value == 1;
        Human2 human2 = humanObj.getHuman2();
        int day = human2.getAccumulatedRechargeDay();
        int round = human2.getAccumulatedRechargeRound();
        if(round == 0){
            if(isClosed){
                Log.accumulatedRecharge.info("===humanId={} accumulatedRecharge isClosed", humanObj.id);
                return;
            }
            // 初始化累充轮次
            int initRound = AccumulatedRechargeUtils.getAccumulatedRechargeInitRound(humanObj);
            human2.setAccumulatedRechargeRound(initRound);
            human2.setAccumulatedRechargeDay(0);
            Log.accumulatedRecharge.info("===humanId={} initRound={}", humanObj.id, initRound);
            return;
        }
        int maxDay = AccumulatedRechargeUtils.getMaxDayByRound(round);
        if(maxDay == 0){
            Log.accumulatedRecharge.warn("配置错误 round={} maxDay={}", round, maxDay);
            return;
        }
        if(day < maxDay){
            return;
        }
        // 计算本轮未领取的奖励
        List<Integer> receivedInfo = Utils.toJSONArray(human2.getAccumulatedRechargeReceivedInfo()).toJavaList(Integer.class);
        Map<Integer, Integer> unclaimedRewards = new HashMap<>();
        for(ConfDailyRecharge conf : ConfDailyRecharge.findAll()){
            if(conf.round == round){
                if(conf.reward != null && !receivedInfo.contains(conf.day)){
                    unclaimedRewards = Utils.intArrToIntMap(unclaimedRewards, conf.reward);
                }
            }
        }
        if(!unclaimedRewards.isEmpty()){
            // 邮件发送未领取奖励
            MailManager.inst().sendMail(humanObj.id, MailManager.SYS_SENDER, AccumulatedRechargeConst.REWARD_MAIL_ID, "", "", Utils.mapIntIntToJSON(unclaimedRewards), null);
        }
        // 切换到下一轮
        int nextRound = isClosed ? 0 : AccumulatedRechargeUtils.getAccumulatedRechargeNextRound(humanObj);
        human2.setAccumulatedRechargeRound(nextRound);
        human2.setAccumulatedRechargeDay(0);
        human2.setAccumulatedRechargeReceivedInfo("[]");
        Log.accumulatedRecharge.info("===humanId={} unclaimedRewards={} day={} setAccumulatedRechargeRound from={} to={}",
                humanObj.id, unclaimedRewards, day, round, nextRound);
    }
}
