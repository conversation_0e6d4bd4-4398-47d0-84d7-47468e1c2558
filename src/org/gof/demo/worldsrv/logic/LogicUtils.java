package org.gof.demo.worldsrv.logic;

import com.google.protobuf.Message;
import io.vertx.core.AsyncResult;
import io.vertx.core.Handler;
import org.gof.core.Port;
import org.gof.core.RemoteNode;
import org.gof.core.dbsrv.redis.AsyncActionResult;
import org.gof.core.support.Utils;
import org.gof.demo.distr.DistrKit;
import org.gof.demo.distr.cross.CrossHumanLoader;
import org.gof.demo.worldsrv.entity.HumanBrief;
import org.gof.demo.worldsrv.human.HumanBriefLoadType;
import org.gof.demo.worldsrv.human.HumanGlobalServiceProxy;
import org.gof.demo.worldsrv.human.HumanManager;
import org.gof.demo.worldsrv.msg.MsgError;
import org.gof.demo.worldsrv.msg.MsgRole;
import org.gof.demo.worldsrv.support.Log;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

public class LogicUtils {
    static public void sendMsg(long humanId, Message msg){
        HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance(DistrKit.getWorldNodeID(Utils.getServerIdByHumanId(humanId)));
//        Log.logic.info("humanId:{} sendMsg {}", humanId, msg.getClass());
        proxy.sendMsg(humanId, msg);
    }

    static public void sendMsg_error(long humanId, int sn) {
        MsgError.error_info_s2c.Builder msg = MsgError.error_info_s2c.newBuilder();
        msg.setCode(sn);
//		msg.setMsg();
        sendMsg(humanId, msg.build());
    }

    /**
     * 更新玩家红点记录
     * @param humanId      玩家对象
     * @param system        系统模块
     * @param module        系统对应的功能
     * @param redNum        红点数量
     */
    static public void updateSystemModuleRedNum(long humanId, int system, int module, int redNum) {
        if(humanId == 0){
            return;
        }
        // 跨服红点，只通知，不存到humanObj上，因为没有意义，每次都要到跨服取数据
        Map<Integer, Integer> systemRedMap = new HashMap<>();
        systemRedMap.put(module, redNum);
        MsgRole.role_red_point_change_s2c.Builder msg = MsgRole.role_red_point_change_s2c.newBuilder();
        msg.setRedPointInfo(HumanManager.inst().to_p_red_point(systemRedMap, system, new int[]{module}));
        sendMsg(humanId, msg.build());
    }

    /**
     * 加载玩家的humanBrief
     * 如果跨服redis里没有数据，则取游戏服拉取，游戏服没有则用humanData构造
     * 尽全力保证返回一个非空的humanBrief
     * @param humanId
     * @param type
     * @param onComplete
     */
    public static void loadHumanBrief(long humanId, HumanBriefLoadType type, Handler<AsyncResult<HumanBrief>> onComplete) {
        Port port = Port.getCurrent();
        CrossHumanLoader.getHumanBrief(humanId, type, res -> {
            if (res.failed() || res.result() == null) {
                Log.logic.error("获取玩家数据失败，humanId={}, {}", humanId, res.cause());
                fetchHumanBriefFromGameServer(humanId, onComplete);
                return;
            }
            AsyncActionResult.success(port, onComplete, res.result());
        });
    }

    /**
     * 批量加载玩家的humanBrief
     * 如果跨服redis里没有数据，则取游戏服拉取，游戏服没有则用humanData构造
     * 尽全力保证返回非空的humanBriefList
     * @param humanIdList
     * @param type
     * @param onComplete
     */
    public static void loadHumanBriefList(List<Long> humanIdList, HumanBriefLoadType type, Handler<AsyncResult<List<HumanBrief>>> onComplete) {
        Port port = Port.getCurrent();
        CrossHumanLoader.getList(humanIdList, type, res -> {
            if(res.failed()){
                AsyncActionResult.fail(port, onComplete, res.cause());
                return;
            }
            List<HumanBrief> humanBriefList = res.result();
            AtomicInteger loadNum = new AtomicInteger(0);
            for(int index=0; index<humanBriefList.size(); index++){
                HumanBrief humanBrief = humanBriefList.get(index);
                long humanId = humanIdList.get(index);
                if(humanId > 0 && humanBrief == null){
                    loadNum.incrementAndGet();
                    int finalIndex = index;
                    fetchHumanBriefFromGameServer(humanId, res2->{
                        if(res2.failed()){
                            AsyncActionResult.fail(port, onComplete, res2.cause());
                            return;
                        }
                        humanBriefList.set(finalIndex, res2.result());
                        loadNum.decrementAndGet();
                        if(loadNum.get() <= 0){
                            AsyncActionResult.success(port, onComplete, humanBriefList);
                        }
                    });
                    continue;
                }
            }
            if(loadNum.get() <= 0){
                AsyncActionResult.success(port, onComplete, humanBriefList);
            }
        });
    }

    private static void fetchHumanBriefFromGameServer(long humanId, Handler<AsyncResult<HumanBrief>> onComplete) {
        Port port = Port.getCurrent();
        String worldNodeId = DistrKit.getWorldNodeID(Utils.getServerIdByHumanId(humanId));
        RemoteNode rn = port.getNode().getRemoteNode(worldNodeId);
        if(rn == null) {
            Log.logic.warn("去游戏服获取玩家数据失败 worldNodeId={}未连接", worldNodeId);
            AsyncActionResult.fail(port, onComplete, new Exception("去游戏服获取玩家数据失败，worldNodeId未连接"));
            return;
        }
        HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance(worldNodeId);
        proxy.getHumanBrief(humanId);
        proxy.listenResult((results, context) -> {
            HumanBrief humanBrief = results.get("humanBrief");
            if (humanBrief == null) {
                Log.logic.error("去游戏服获取玩家数据失败，brief为空，humanId={}", humanId);
                AsyncActionResult.fail(port, onComplete, new Exception("去游戏服获取玩家数据失败，brief为空"));
                return;
            }
            Log.logic.info("去游戏服获取玩家数据成功，humanId={}", humanId);
            AsyncActionResult.success(port, onComplete, humanBrief);
        });
    }

    /**
     * 蛇形分组：确保排名靠前的队伍不会全部集中在同一组
     * @param allTeamInfo 所有队伍信息（从 Redis zset 取出，已经有序）
     * @param groupMaxNum
     * @return 分组结果
     */
    public static List<List<Long>> snakeGrouping(List<Long> allTeamInfo, int groupMaxNum) {
        // 直接使用传入的 List，保持顺序
        int numTeams = allTeamInfo.size();
        int numGroups = numTeams / groupMaxNum;
        List<List<Long>> groups = new ArrayList<>(numGroups);
        for (int i = 0; i < numGroups; i++) {
            groups.add(new ArrayList<>(groupMaxNum));
        }
        // 蛇形分组
        for (int i = 0; i < numTeams; i++) {
            int groupIndex = i % numGroups;
            if ((i / numGroups) % 2 == 1) {
                groupIndex = numGroups - 1 - groupIndex; // 反向填充
            }
            groups.get(groupIndex).add(allTeamInfo.get(i));
        }
        return groups;
    }

    /**
     * 生成每轮的比赛对阵
     * @param group 当前组的队伍
     * @param round 当前轮次
     * @return 每轮的比赛对阵（队伍索引对）
     */
    public static List<int[]> generateRoundCompetitions(List<Long> group, int round) {
        List<int[]> competitions = new ArrayList<>();
        int numTeams = group.size();
        // 轮转法生成对阵
        for (int i = 0; i < numTeams / 2; i++) {
            int team1 = (round - 1 + i) % (numTeams - 1);
            int team2 = (numTeams - 1 - i + round - 1) % (numTeams - 1);
            if (team1 == team2) {
                team2 = numTeams - 1;
            }
            competitions.add(new int[]{team1, team2});
        }
        return competitions;
    }
}
