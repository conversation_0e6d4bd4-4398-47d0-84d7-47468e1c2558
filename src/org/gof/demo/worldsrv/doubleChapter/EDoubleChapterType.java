package org.gof.demo.worldsrv.doubleChapter;

/**
 * 双人副本相关枚举
 */
public class EDoubleChapterType {
    // 错误码
    public static final int CODE_SUCCESS = 0;          // 成功
    public static final int CODE_SUCCESS_1 = 1;          // 成功
    public static final int CODE_INVALID_SETTING = 3;  // 无效设置
    public static final int CODE_ALREADY_IN_LIST = 4; // 已在列表中

    public static final int MAX_ROB_TEAMMATE_ID = 100;
    
    // 邀请设置类型
    public static final int SETTING_ALLOW_INVITE = 1;      // 默认开启允许被邀请
    public static final int SETTING_PRIVATE_INVITE = 2;    // 默认关闭私聊邀请
    public static final int SETTING_INVITE_CONDITION = 3;  // 默认不限条件
    public static final int SETTING_AUTO_ACCEPT = 4;       // 默认关闭自动接受

    //设置开关
    public static final int SETTING_ON = 0;          // 开启
    public static final int SETTING_OFF = 1;         // 关闭

    public static final int SETTING_NONE = 1;          // 不限
    public static final int SETTING_FAMILY_ONLY = 2;   // 仅家族
    public static final int SETTING_FRIEND_ONLY = 3;   // 仅好友
    public static final int SETTING_FAMILY_AND_FRIEND = 4; // 家族且好友
    
    // 错误码
    public static final int ERR_CODE_5 = 5;      // 战友未找到
    public static final int ERR_CODE_391 = 391;     // 对方正在组队中，无法取消助战
    public static final int ERR_CODE_392 = 392;     // 对方申请列表已满
    public static final int ERR_CODE_393 = 393;     // 对方已是您的战友，不能重复申请
    public static final int ERR_CODE_394 = 394;     // 已发送过申请，请耐心等待
    public static final int ERR_CODE_395 = 395;     // 您的战友数量已达上限

    // 助战行装id
    public static final int HELP_PLAN_ID = 0;

    public static final int ERR_CODE_396 = 396;
    public static final int ERR_CODE_397 = 397;

    public static final int MAX_HELP_TIMES = 20;

    //作战位置
    public static final int POSITION_1 = 1;
    public static final int POSITION_2 = 2;
    public static final int POSITION_3 = 3;
    public static final int POSITION_4 = 4;
    public static final int POSITION_5 = 5;
    //作战单位
    public static final int UNIT_SELF = 1;
    public static final int UNIT_TEAMMATE = 2;

    //关卡类型
    public static final int PART_TYPE_0 = 0;//普通关卡
    public static final int PART_TYPE_1 = 1;//最后一关

    //最大血量
    public static final int MAX_HP = 10000;

    // 战斗结果扩展字段
    public static final int EXT_SELF_AFTER_HP = 1;    // 自身战后血量
    public static final int EXT_SELF_BEFORE_HP = 2;   // 自身战前血量
    public static final int EXT_SELF_MAX_HP = 3;      // 自身最大血量
    public static final int EXT_TEAMMATE_AFTER_HP = 4;  // 队友战后血量
    public static final int EXT_TEAMMATE_BEFORE_HP = 5; // 队友战前血量
    public static final int EXT_TEAMMATE_MAX_HP = 6;    // 队友最大血量

    //最小，最大策略sn
    public static final int MIN_STRATEGIC_SN = 1;
    public static final int MAX_STRATEGIC_SN = 9;


}
