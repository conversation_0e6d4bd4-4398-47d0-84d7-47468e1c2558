package org.gof.demo.worldsrv.doubleChapter;

import com.alibaba.fastjson.JSONObject;
import com.pwrd.op.LogOp;
import io.vertx.core.AsyncResult;
import io.vertx.core.Handler;
import org.gof.core.Port;
import org.gof.core.dbsrv.redis.AsyncActionResult;
import org.gof.core.dbsrv.redis.EntityManager;
import org.gof.core.support.*;
import org.gof.core.support.observer.Listener;
import org.gof.demo.battlesrv.msgHandler.SkillManager;
import org.gof.demo.seam.account.AccountManager;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.character.HumanObjectServiceProxy;
import org.gof.demo.worldsrv.config.ConfDoubleLadderAssistReward;
import org.gof.demo.worldsrv.config.ConfDoubleLadderChapter;
import org.gof.demo.worldsrv.config.ConfDoubleLadderStrategic;
import org.gof.demo.worldsrv.config.ConfGlobal;
import org.gof.demo.worldsrv.entity.DoubleChapter;
import org.gof.demo.worldsrv.entity.HumanBrief;
import org.gof.demo.worldsrv.friend.FriendManager;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.guild.GuildServiceProxy;
import org.gof.demo.worldsrv.human.*;
import org.gof.demo.worldsrv.inform.Inform;
import org.gof.demo.worldsrv.inform.InformManager;
import org.gof.demo.worldsrv.inform.LanguageKey;
import org.gof.demo.worldsrv.instance.InstanceConstants;
import org.gof.demo.worldsrv.instance.InstanceManager;
import org.gof.demo.worldsrv.mail.MailManager;
import org.gof.demo.worldsrv.msg.*;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.enumKey.ConfGlobalKey;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;
import org.gof.demo.worldsrv.support.observer.EventKey;

import java.math.BigDecimal;
import java.util.*;

public class DoubleChapterManager extends ManagerBase {
    /**
     * 获取实例
     *
     * @return
     */
    public static DoubleChapterManager inst() {
        return inst(DoubleChapterManager.class);
    }

    /**
     * 处理修改可选奖励
     * 
     * @param humanObj 玩家对象
     * @param chooseRewardList 选择的奖励列表
     */
    public void handleChangeOptionalReward(HumanObject humanObj, List<Define.p_key_value> chooseRewardList) {
        DoubleChapter doubleChapter = humanObj.operation.doubleChapter;
        if(doubleChapter == null){
            return;
        }
        ConfDoubleLadderChapter confMax = ConfDoubleLadderChapter.get(doubleChapter.getMaxChapter());
        if(confMax == null){
            Log.game.error("找不到配置章节信息，sn={}", doubleChapter.getMaxChapter());
            return;
        }
        int maxLevel = GlobalConfVal.getDoubleChapterMaxLevel(doubleChapter.getChapter());
        ConfDoubleLadderChapter confDoubleLadderChapter;
        if(0 == doubleChapter.getLevel()){
            confDoubleLadderChapter = GlobalConfVal.getConfDoubleLadderChapter(confMax.chapter,maxLevel);
        }else {
            confDoubleLadderChapter = GlobalConfVal.getConfDoubleLadderChapter(confMax.chapter-1,maxLevel);
        }
        if(confMax.sn == GlobalConfVal.getConfMaxDoubleLadderChapterSn()){
            confDoubleLadderChapter = confMax;
        }
        if(confDoubleLadderChapter == null){
            Log.game.error("找不到配置章节信息，chapter={}", doubleChapter.getChapter());
            return;
        }

        // 检查选择的奖励数量是否超过限制
        if (chooseRewardList.size() > confDoubleLadderChapter.final_reward_times) {
            Log.game.error("选择的奖励数量超过限制，playerId={}, choose={}, limit={}",
                    humanObj.getHumanId(), chooseRewardList.size(), confDoubleLadderChapter.final_reward_times);
            return;
        }

        // 检查选择的奖励是否有效
        Set<Integer> usedCombinations = new HashSet<>();
        List<Integer> rewardIndexList = new ArrayList<>();

        for (Define.p_key_value reward : chooseRewardList) {
            int chapterLv = (int)reward.getK();
            int index = (int)reward.getV();
            int combination = chapterLv * 100 + index;

            // 检查章节等级是否有效
            ConfDoubleLadderChapter conf = GlobalConfVal.getConfDoubleLadderChapter(chapterLv, GlobalConfVal.getDoubleChapterMaxLevel(chapterLv));
            if(conf == null || conf.final_reward_unlock == null || index > conf.final_reward_unlock.length) {
                Log.game.error("选择了无效的奖励，playerId={}, chapter={}, index={}",
                        humanObj.getHumanId(), chapterLv, index);
                return;
            }

            if(chapterLv > doubleChapter.getMaxChapter()) {
                Log.game.error("选择了未解锁的奖励，playerId={}, chapter={}, index={}， maxChapter={}",
                        humanObj.getHumanId(), chapterLv, index, doubleChapter.getMaxChapter());
                return;
            }

            // 检查是否重复选择
            if (!usedCombinations.add(combination)) {
                Log.game.error("选择了重复的奖励，playerId={}, chapter={}, index={}",
                        humanObj.getHumanId(), chapterLv, index);
                return;
            }

            rewardIndexList.add(combination);
        }

        // 更新玩家选择的奖励
        doubleChapter.setChooseRewardList(Utils.arrayIntToStr(rewardIndexList));

        // 发送更新成功的消息给客户端
        MsgDoubleChapter.double_change_optional_reward_s2c.Builder msg =
                MsgDoubleChapter.double_change_optional_reward_s2c.newBuilder();
        msg.addAllChooseReward(chooseRewardList);
        msg.setCode(0);
        humanObj.sendMsg(msg);
    }


    /**
     * 处理获取队友信息
     * 
     * @param humanObj 玩家对象
     */
    public void handleGetTeammateInfo(HumanObject humanObj) {
        DoubleChapter doubleChapter = humanObj.operation.doubleChapter;
        if(doubleChapter == null){
            return;
        }
        MsgDoubleChapter.double_chapter_get_teammate_info_s2c.Builder msg = MsgDoubleChapter.double_chapter_get_teammate_info_s2c.newBuilder();
        long useTeammate = doubleChapter.getUseTeammate();
        List<Define.p_double_chapter_teammate> teammates = Utils.deserializeProtoList(doubleChapter.getTeammates(), Define.p_double_chapter_teammate.parser());

        msg.setId(useTeammate);
        msg.addAllAllTeammate(teammates);
        msg.setMaxTeammateNum(ConfGlobal.get(ConfGlobalKey.double_ladder_partner_num).value);
        if(useTeammate != 0){
            List<Define.p_double_chapter_teammate_detail> teammateDetails = Utils.deserializeProtoList(doubleChapter.getTeammatesDetail(), Define.p_double_chapter_teammate_detail.parser());
            for (Define.p_double_chapter_teammate_detail teammateDetail : teammateDetails){
                if(teammateDetail.getRoleId() == useTeammate){
                    msg.setTeammateDetail(teammateDetail);
                    break;
                }
            }
        }
        humanObj.sendMsg(msg);
    }

    /**
     * 处理获取单个队友信息
     * 
     * @param humanObj 玩家对象
     * @param id 队友ID
     */
    public void handleSingleTeammateInfo(HumanObject humanObj, long id) {
        DoubleChapter doubleChapter = humanObj.operation.doubleChapter;
        if(doubleChapter == null){
            return;
        }
        
        // 构建消息
        MsgDoubleChapter.double_chapter_single_teammate_info_s2c.Builder msg = 
            MsgDoubleChapter.double_chapter_single_teammate_info_s2c.newBuilder();
        msg.setId(id);
        
        // 首先检查是否是有效的队友
        List<Define.p_double_chapter_teammate> teammates = 
            Utils.deserializeProtoList(doubleChapter.getTeammates(), Define.p_double_chapter_teammate.parser());
        
        boolean isValidTeammate = false;
        for(Define.p_double_chapter_teammate teammate : teammates) {
            if(teammate.getRoleId() == id) {
                isValidTeammate = true;
                break;
            }
        }
        
        if(!isValidTeammate) {
            Log.game.error("请求无效的队友信息 humanId={} teammateId={}", humanObj.getHumanId(), id);
            return;
        }
        
        // 获取队友详细信息
        List<Define.p_double_chapter_teammate_detail> teammateDetails = 
            Utils.deserializeProtoList(doubleChapter.getTeammatesDetail(), Define.p_double_chapter_teammate_detail.parser());
        
        boolean found = false;
        for (Define.p_double_chapter_teammate_detail teammateDetail : teammateDetails) {
            if(teammateDetail.getRoleId() == id) {
                msg.setTeammateDetail(teammateDetail);
                found = true;
                break;
            }
        }
        
        if(!found) {
            Log.game.error("找不到队友详细信息 humanId={} teammateId={}", humanObj.getHumanId(), id);
            return;
        }
        
        humanObj.sendMsg(msg);
    }

    /**
     * 处理使用队友
     * 
     * @param humanObj 玩家对象
     * @param id 队友ID
     */
    public void handleUseTeammate(HumanObject humanObj, long id) {
        DoubleChapter doubleChapter = humanObj.operation.doubleChapter;
        if(doubleChapter == null){
            return;
        }
        
        // 验证是否是有效的队友
        List<Define.p_double_chapter_teammate> teammates = 
            Utils.deserializeProtoList(doubleChapter.getTeammates(), Define.p_double_chapter_teammate.parser());
        
        boolean isValidTeammate = false;
        for (Define.p_double_chapter_teammate teammate : teammates) {
            if (teammate.getRoleId() == id || id == 0) {
                isValidTeammate = true;
                break;
            }
        }
        
        if(!isValidTeammate) {
            Log.game.error("使用无效的队友 humanId={} teammateId={}", humanObj.getHumanId(), id);
            return;
        }
        
        // 设置战斗位置
        List<Define.p_key_value> battlePos = new ArrayList<>();
        battlePos.add(Define.p_key_value.newBuilder()
                .setK(EDoubleChapterType.POSITION_1)
                .setV(EDoubleChapterType.UNIT_SELF).build());
        battlePos.add(Define.p_key_value.newBuilder()
                .setK(EDoubleChapterType.POSITION_3)
                .setV(EDoubleChapterType.UNIT_TEAMMATE).build());
        doubleChapter.setBattlePosList(Utils.serializeProtoList(battlePos));
        
        // 设置使用的队友
        doubleChapter.setUseTeammate(id);
        
        // 发送消息
        MsgDoubleChapter.double_chapter_use_teammate_s2c.Builder msg = 
            MsgDoubleChapter.double_chapter_use_teammate_s2c.newBuilder();
        msg.setId(id);
        msg.setCode(0);
        humanObj.sendMsg(msg);
        
        // 重置关卡
        handleRestartLevel(humanObj, 1);
    }

    /**
     * 处理放弃队友
     *
     * @param humanObj 玩家对象
     * @param id 队友ID
     */
    public void handleAbandonTeammate(HumanObject humanObj, long id) {
        // 机器人战友不可放弃
        if(id <= EDoubleChapterType.MAX_ROB_TEAMMATE_ID){
            return;
        }
        DoubleChapter dc = humanObj.operation.doubleChapter;
        if(dc == null) return;

        if(dc.getUseTeammate() == id){
            sendErrorCode(humanObj, EDoubleChapterType.ERR_CODE_391);
        }

        // 删除队友基本信息
        List<Define.p_double_chapter_teammate> teammates = Utils.deserializeProtoList(
                dc.getTeammates(), Define.p_double_chapter_teammate.parser());
        if (teammates != null) {
            teammates.removeIf(teammate -> teammate.getRoleId() == id);
            dc.setTeammates(Utils.serializeProtoList(teammates));
        }

        // 删除队友详细信息
        List<Define.p_double_chapter_teammate_detail> teammateDetails = Utils.deserializeProtoList(
                dc.getTeammatesDetail(), Define.p_double_chapter_teammate_detail.parser());
        if (teammateDetails != null) {
            teammateDetails.removeIf(detail -> detail.getRoleId() == id);
            dc.setTeammatesDetail(Utils.serializeProtoList(teammateDetails));
        }

        // 删除战斗角色信息
        List<Define.p_battle_role> battleRoles = Utils.deserializeProtoList(
                dc.getTeammatesBattleRole(), Define.p_battle_role.parser());
        if (battleRoles != null) {
            battleRoles.removeIf(role -> role.getId() == id);
            dc.setTeammatesBattleRole(Utils.serializeProtoList(battleRoles));
        }

        Define.p_double_chapter_help_player help = Define.p_double_chapter_help_player.newBuilder()
                .setRoleId(humanObj.id)
                .build();
        updateDoubleChapterHelpList(id, help, false);
        sendAbandonSuccess(humanObj, id);
    }

    private void updateDoubleChapterHelpList(long humanId, Define.p_double_chapter_help_player help ,boolean isAdd){
        HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance();
        prx.getInfo(humanId); // 获取双人本数据
        prx.listenResult((Param results, Param ctx) -> {
            HumanGlobalInfo targetInfo = results.get();
            if(targetInfo != null) {
                // 玩家在线，通过服务调用
                HumanObjectServiceProxy humanPrx = HumanObjectServiceProxy
                        .newInstance(targetInfo.nodeId, targetInfo.portId, targetInfo.id);
                humanPrx.updateDoubleChapterHelpList(help, isAdd);
            } else {
                // 玩家不在线，异步获取实体
                EntityManager.getEntityAsync(DoubleChapter.class, humanId, res -> {
                    if(res.succeeded()) {
                        DoubleChapter targetDc = res.result();
                        if(targetDc != null) {
                            updateHelpList(targetDc, help, isAdd);
                            targetDc.update();
                        }
                    }
                });
            }
        });

    }
    /**
     * 处理获取协助管理信息
     * 
     * @param humanObj 玩家对象
     */
    public void handleGetHelpManageInfo(HumanObject humanObj) {
        DoubleChapter dc = humanObj.operation.doubleChapter;
        if(dc == null) return;

        // 构建协议消息
        MsgDoubleChapter.double_chapter_get_help_manage_info_s2c.Builder msg = 
            MsgDoubleChapter.double_chapter_get_help_manage_info_s2c.newBuilder();
        
        // 设置最大协助次数（从配置表获取）
        int maxHelp = ConfGlobal.get(ConfGlobalKey.double_ladder_max_help_num).value;
        msg.setMaxHelpNum(maxHelp);

        // 获取帮助列表数据
        List<Define.p_double_chapter_help_player> helpList = Utils.deserializeProtoList(
            dc.getHelpHumanData(), Define.p_double_chapter_help_player.parser());
        msg.addAllHelpList(helpList);

        // 获取申请列表数据
        List<Define.p_double_chapter_help_player> applyList = Utils.deserializeProtoList(
            dc.getApplyList(), Define.p_double_chapter_help_player.parser());
        msg.addAllApplyList(applyList);

        humanObj.sendMsg(msg);
    }

    /**
     * 处理获取邀请协助设置信息
     * 
     * @param humanObj 玩家对象
     */
    public void handleGetInviteHelpSettingInfo(HumanObject humanObj) {
        DoubleChapter dc = humanObj.operation.doubleChapter;
        if(dc == null) return;

        MsgDoubleChapter.double_chapter_get_invite_help_setting_info_s2c.Builder msg = 
            MsgDoubleChapter.double_chapter_get_invite_help_setting_info_s2c.newBuilder();
        
        List<Define.p_key_value> settings = Utils.deserializeProtoList(
            dc.getSettingList(), Define.p_key_value.parser());
        
        // 默认设置处理（需要扩展）
        Map<Integer, Integer> defaultSettings = new HashMap<>();
        defaultSettings.put(EDoubleChapterType.SETTING_ALLOW_INVITE, 1);      // 默认开启允许被邀请
        defaultSettings.put(EDoubleChapterType.SETTING_PRIVATE_INVITE, 0);    // 默认关闭私聊邀请
        defaultSettings.put(EDoubleChapterType.SETTING_AUTO_ACCEPT, 0);       // 默认关闭自动接受
        defaultSettings.put(EDoubleChapterType.SETTING_INVITE_CONDITION, 1);  // 默认不限条件
        
        // 检查并补充缺失的设置项
        for(Map.Entry<Integer, Integer> entry : defaultSettings.entrySet()) {
            boolean exists = false;
            for(Define.p_key_value setting : settings) {
                if(setting.getK() == entry.getKey()) {
                    exists = true;
                    break;
                }
            }
            if(!exists) {
                settings.add(Define.p_key_value.newBuilder()
                    .setK(entry.getKey())
                    .setV(entry.getValue())
                    .build());
            }
        }
        
        msg.addAllSetting(settings);
        humanObj.sendMsg(msg);
    }

    /**
     * 处理修改邀请设置
     * 
     * @param humanObj 玩家对象
     * @param settingList 设置列表
     */
    public void handleChangeInviteSetting(HumanObject humanObj, List<Define.p_key_value> settingList) {
        DoubleChapter dc = humanObj.operation.doubleChapter;
        if(dc == null) {
            return;
        }

        // 获取现有设置
        List<Define.p_key_value> currentSettings = Utils.deserializeProtoList(
            dc.getSettingList(), Define.p_key_value.parser());
        Map<Integer, Define.p_key_value> settingMap = new HashMap<>();
        for (Define.p_key_value setting : currentSettings) {
            settingMap.put((int)setting.getK(), setting);
        }

        // 验证并更新设置
        boolean isValid = true;
        for (Define.p_key_value newSetting : settingList) {
            int key = (int)newSetting.getK();
            long value = newSetting.getV();

            // 验证设置项合法性
            switch(key) {
                case EDoubleChapterType.SETTING_ALLOW_INVITE:
                case EDoubleChapterType.SETTING_PRIVATE_INVITE:
                case EDoubleChapterType.SETTING_AUTO_ACCEPT:
                    if(value < 0 || value > 1) {
                        isValid = false;
                    }
                    break;
                case EDoubleChapterType.SETTING_INVITE_CONDITION:
                    if(!isValidCondition((int)value)) {
                        isValid = false;
                    }
                    break;
                default:
                    isValid = false; // 未知设置项
            }

            if(!isValid) break;

            // 更新或添加设置
            settingMap.put(key, Define.p_key_value.newBuilder()
                .setK(key)
                .setV(value)
                .build());
        }

        if(!isValid) {
            return;
        }

        // 保存更新后的设置
        dc.setSettingList(Utils.serializeProtoList(new ArrayList<>(settingMap.values())));

        sendSettingChangeResponse(humanObj, EDoubleChapterType.CODE_SUCCESS);
    }

    private boolean isValidCondition(int value) {
        return value >= EDoubleChapterType.SETTING_NONE
            && value <= EDoubleChapterType.SETTING_FAMILY_AND_FRIEND;
    }

    private void sendSettingChangeResponse(HumanObject humanObj, int code) {
        MsgDoubleChapter.double_chapter_change_invite_setting_s2c.Builder msg = 
            MsgDoubleChapter.double_chapter_change_invite_setting_s2c.newBuilder()
                .setCode(code);
        humanObj.sendMsg(msg);
    }

    /**
     * 处理同意邀请
     * 
     * @param humanObj 玩家对象
     * @param applyId 申请ID
     */
    public void handleAgreeInvite(HumanObject humanObj, long applyId) {
        DoubleChapter dc = humanObj.operation.doubleChapter;
        if(dc == null) {
            return;
        }

        // 获取申请列表
        List<Define.p_double_chapter_help_player> applyList = Utils.deserializeProtoList(
            dc.getApplyList(), Define.p_double_chapter_help_player.parser());
        
        // 查找对应申请
        Define.p_double_chapter_help_player apply = null;
        for(Define.p_double_chapter_help_player a : applyList) {
            if(a.getRoleId() == applyId) {
                apply = a;
                break;
            }
        }
        
        if(apply == null) {
            sendAgreeResponse(humanObj, applyId, EDoubleChapterType.ERR_CODE_5);
            return;
        }

        // 检查队友数量限制
        int maxHelp = ConfGlobal.get(ConfGlobalKey.double_ladder_max_help_num).value;
        List<Define.p_double_chapter_teammate> helpHumans = Utils.deserializeProtoList(
            dc.getHelpHumanData(), Define.p_double_chapter_teammate.parser());
        if(helpHumans.size() >= maxHelp) {
            sendAgreeResponse(humanObj, applyId, EDoubleChapterType.ERR_CODE_395);
            return;
        }

        // 更新双方数据
        updateBothPlayers(humanObj, applyId, apply);


        // 从申请列表移除
        applyList.removeIf(a -> a.getRoleId() == applyId);
        dc.setApplyList(Utils.serializeProtoList(applyList));
        dc.setHelpHumanData(Utils.serializeProtoList(helpHumans));
        sendAgreeResponse(humanObj, applyId, EDoubleChapterType.CODE_SUCCESS);

        MsgChat.chat_message_c2s.Builder msg = MsgChat.chat_message_c2s.newBuilder();
        msg.setChannel(Inform.私聊);
        msg.setContentType(Inform.ContentTypeMultiText);
        msg.setTargetId(applyId);
        msg.setContent(String.valueOf(LanguageKey.DoubleChapterAlreadyAgree));
        InformManager.inst()._msg_chat_message_c2s(humanObj, msg.build());
    }


    private Define.p_double_chapter_teammate_detail buildTeammateDetail(HumanObject humanObj) {
        List<Define.p_key_value> p_AttrList = HumanManager.inst().to_p_key_value_spList(humanObj.getPropPlus(),0);
        PlanVo planVo = humanObj.operation.planVoMap.getOrDefault(EDoubleChapterType.HELP_PLAN_ID, new PlanVo(0));
        int tabSkill = planVo.getTab(PlanVo.TAB_SKILL);
        List<Define.p_active_skill> activeSkills = SkillManager.inst().getAllActiveSkill(humanObj, tabSkill);
        return Define.p_double_chapter_teammate_detail.newBuilder()
            .setRoleId(humanObj.getHumanId())
            .setLevel(humanObj.getHuman().getLevel())
            .setName(humanObj.getHuman().getName())
            .setHead(Define.p_head.newBuilder().setId(humanObj.getHuman().getHeadSn()).setFrameId(humanObj.getHuman().getCurrentHeadFrameSn()))
            .setTeammateFigure(HumanManager.inst().to_p_role_figure(humanObj.getHuman(),humanObj.getHuman2()).build())
            .addAllAttrList(p_AttrList)
            .addAllSkillList(activeSkills)
            .addAllPlanTab(buildTeammatePlanInfo(humanObj))
            .build();
    }

    private List<Define.p_dc_teammate_plan_info> buildTeammatePlanInfo(HumanObject humanObj) {
        List<Define.p_dc_teammate_plan_info> planInfos = new ArrayList<>();
        PlanVo planVo = humanObj.operation.planVoMap.getOrDefault(0, new PlanVo(0));
        int wingSn = planVo.getTab(PlanVo.TAB_WING);
        int wingLv = 0;
        if(humanObj.operation.wing != null){
            Map<Integer, Integer> skinLvMap = Utils.jsonToMapIntInt(humanObj.operation.wing.getSkinLvMap());
            wingLv = skinLvMap.getOrDefault(wingSn, 0);
        }
        Define.p_dc_teammate_plan_info planInfo = Define.p_dc_teammate_plan_info.newBuilder()
                .setPlanType(PlanVo.TAB_WING)
                .setSkillInfo(Define.p_key_value.newBuilder().setK(wingSn).setV(wingLv)).build();
        planInfos.add(planInfo);

        int mountSn = planVo.getTab(PlanVo.TAB_MOUNT);
        int mountLv = 0;
        if(humanObj.operation.mount != null){
            Map<Integer, Integer> skinLvMap = Utils.jsonToMapIntInt(humanObj.operation.mount.getSkinLvMap());
            mountLv = skinLvMap.getOrDefault(mountSn, 0);
        }
        planInfo = Define.p_dc_teammate_plan_info.newBuilder()
                .setPlanType(PlanVo.TAB_MOUNT)
                .setSkillInfo(Define.p_key_value.newBuilder().setK(mountSn).setV(mountLv)).build();
        planInfos.add(planInfo);

        int atfSn = planVo.getTab(PlanVo.TAB_ARTF);
        int atfLv = 0;
        planInfo = Define.p_dc_teammate_plan_info.newBuilder()
                .setPlanType(PlanVo.TAB_ARTF)
                .setSkillInfo(Define.p_key_value.newBuilder().setK(atfSn).setV(atfLv)).build();
        planInfos.add(planInfo);

        return planInfos;
    }

    private void sendAgreeResponse(HumanObject human, long applyId, int code) {
        if(code == EDoubleChapterType.CODE_SUCCESS) {
            // 发送成功响应
            MsgDoubleChapter.double_chapter_agree_invite_s2c.Builder msg =
                MsgDoubleChapter.double_chapter_agree_invite_s2c.newBuilder()
                    .setApplyId(applyId)
                    .setCode(code);
            human.sendMsg(msg);
        } else {
            // 发送错误响应
            sendErrorCode(human, code);
        }
        MsgDoubleChapter.double_chapter_agree_invite_s2c.Builder msg = 
            MsgDoubleChapter.double_chapter_agree_invite_s2c.newBuilder()
                .setApplyId(applyId)
                .setCode(code);
        human.sendMsg(msg);
    }

    /**
     * 处理拒绝邀请
     * @param humanObj 玩家对象
     * @param applyId 申请ID
     */
    public void handleRefuseInvite(HumanObject humanObj, long applyId) {
        DoubleChapter dc = humanObj.operation.doubleChapter;
        if(dc == null) {
            return;
        }

        // 获取申请列表
        List<Define.p_double_chapter_help_player> applyList = Utils.deserializeProtoList(
            dc.getApplyList(), Define.p_double_chapter_help_player.parser());
        
        // 查找并移除对应申请
        boolean found = false;
        for(int i = 0; i < applyList.size(); i++) {
            if(applyList.get(i).getRoleId() == applyId) {
                applyList.remove(i);
                found = true;
                break;
            }
        }
        
        if(!found) {
            sendRefuseResponse(humanObj, applyId, EDoubleChapterType.ERR_CODE_5);
            return;
        }

        // 更新申请列表
        dc.setApplyList(Utils.serializeProtoList(applyList));

        // 发送成功响应
        sendRefuseResponse(humanObj, applyId, EDoubleChapterType.CODE_SUCCESS);
    }

    private void sendRefuseResponse(HumanObject human, long applyId, int code) {
        MsgDoubleChapter.double_chapter_refuse_invite_s2c.Builder msg = 
            MsgDoubleChapter.double_chapter_refuse_invite_s2c.newBuilder()
                .setApplyId(applyId)
                .setCode(code);
        human.sendMsg(msg);
    }

    /**
     * 处理取消协助
     * @param humanObj 玩家对象
     * @param helpId 协助ID
     */
    public void handleCancelHelp(HumanObject humanObj, long helpId) {
        DoubleChapter dc = humanObj.operation.doubleChapter;
        if(dc == null) {
            return;
        }

        // 获取帮助列表
        List<Define.p_double_chapter_help_player> helpList = Utils.deserializeProtoList(
                dc.getHelpHumanData(), Define.p_double_chapter_help_player.parser());
        if(helpList == null){
            return;
        }

        // 查找要取消的帮助玩家
        boolean found = false;
        Define.p_double_chapter_help_player targetHelp = null;
        for(int i = 0; i < helpList.size(); i++) {
            if(helpList.get(i).getRoleId() == helpId) {
                targetHelp = helpList.get(i);
                found = true;
                break;
            }
        }

        if(!found) {
            sendCancelHelpResponse(humanObj, helpId, EDoubleChapterType.ERR_CODE_5);
            return;
        }

        // 检查对方是否在线
        HumanGlobalServiceProxy globalProxy = HumanGlobalServiceProxy.newInstance();
        globalProxy.getInfo(helpId);
        globalProxy.listenResult((Param results, Param ctx) -> {
            HumanGlobalInfo targetInfo = results.get();
            if(targetInfo != null) {
                // 对方在线，通过服务调用
                HumanObjectServiceProxy humanPrx = HumanObjectServiceProxy
                        .newInstance(targetInfo.nodeId, targetInfo.portId, targetInfo.id);

                // 检查是否正在使用该队友并更新数据
                humanPrx.checkAndUpdateTeammate(humanObj.getHumanId());
                humanPrx.listenResult((res, ctx1) -> {
                    int code = Utils.intValue(res.get("code"));
                    if(code == EDoubleChapterType.ERR_CODE_391) {
                        // 正在被使用，不能取消
                        sendCancelHelpResponse(humanObj, helpId, EDoubleChapterType.ERR_CODE_391);
                        return;
                    }

                    // 从自己的帮助列表中移除
                    for(int i = 0; i < helpList.size(); i++) {
                        if(helpList.get(i).getRoleId() == helpId) {
                            dc.setHelpTimes(dc.getHelpTimes() + helpList.get(i).getHelpChapterNum());
                            helpList.remove(i);
                            break;
                        }
                    }
                    dc.setHelpHumanData(Utils.serializeProtoList(helpList));

                    // 发送成功响应
                    sendCancelHelpResponse(humanObj, helpId, EDoubleChapterType.CODE_SUCCESS);
                });
            } else {
                // 对方不在线，异步加载数据
                EntityManager.getEntityAsync(DoubleChapter.class, helpId, res -> {
                    if(res.succeeded()) {
                        DoubleChapter targetDc = res.result();
                        if(targetDc != null) {
                            // 检查是否正在使用该队友
                            if(targetDc.getUseTeammate() == humanObj.getHumanId()) {
                                sendCancelHelpResponse(humanObj, helpId, EDoubleChapterType.ERR_CODE_391);
                                return;
                            }

                            // 从对方的队友列表中移除
                            List<Define.p_double_chapter_teammate> teammates = Utils.deserializeProtoList(
                                    targetDc.getTeammates(), Define.p_double_chapter_teammate.parser());

                            for(int i = 0; i < teammates.size(); i++) {
                                if(teammates.get(i).getRoleId() == humanObj.getHumanId()) {
                                    teammates.remove(i);
                                    break;
                                }
                            }
                            targetDc.setTeammates(Utils.serializeProtoList(teammates));

                            // 从对方的队友详细信息中移除
                            List<Define.p_double_chapter_teammate_detail> teammatesDetail = Utils.deserializeProtoList(
                                    targetDc.getTeammatesDetail(), Define.p_double_chapter_teammate_detail.parser());

                            for(int i = 0; i < teammatesDetail.size(); i++) {
                                if(teammatesDetail.get(i).getRoleId() == humanObj.getHumanId()) {
                                    teammatesDetail.remove(i);
                                    break;
                                }
                            }
                            targetDc.setTeammatesDetail(Utils.serializeProtoList(teammatesDetail));

                            // 保存更新
                            targetDc.update();

                            // 从自己的帮助列表中移除
                            for(int i = 0; i < helpList.size(); i++) {
                                if(helpList.get(i).getRoleId() == helpId) {
                                    dc.setHelpTimes(dc.getHelpTimes() + helpList.get(i).getHelpChapterNum());
                                    helpList.remove(i);
                                    break;
                                }
                            }
                            dc.setHelpHumanData(Utils.serializeProtoList(helpList));

                            // 发送成功响应
                            sendCancelHelpResponse(humanObj, helpId, EDoubleChapterType.CODE_SUCCESS);
                        }
                    }
                });
            }
        });
    }

    /**
     * 检查并更新队友数据（在线玩家服务方法）
     */
    public void checkAndUpdateTeammate(HumanObject humanObj, long cancelerId) {
        DoubleChapter dc = humanObj.operation.doubleChapter;
        if(dc == null) {
            return;
        }

        // 检查是否正在使用该队友
        if(dc.getUseTeammate() == cancelerId) {
            Port.getCurrent().returns("code", EDoubleChapterType.ERR_CODE_391);
            return;
        }

        // 从队友列表中移除
        List<Define.p_double_chapter_teammate> teammates = Utils.deserializeProtoList(
                dc.getTeammates(), Define.p_double_chapter_teammate.parser());

        for(int i = 0; i < teammates.size(); i++) {
            if(teammates.get(i).getRoleId() == cancelerId) {
                teammates.remove(i);
                break;
            }
        }
        dc.setTeammates(Utils.serializeProtoList(teammates));

        // 从队友详细信息中移除
        List<Define.p_double_chapter_teammate_detail> teammatesDetail = Utils.deserializeProtoList(
                dc.getTeammatesDetail(), Define.p_double_chapter_teammate_detail.parser());

        for(int i = 0; i < teammatesDetail.size(); i++) {
            if(teammatesDetail.get(i).getRoleId() == cancelerId) {
                teammatesDetail.remove(i);
                break;
            }
        }
        dc.setTeammatesDetail(Utils.serializeProtoList(teammatesDetail));

        List<Define.p_battle_role> battleRoles = Utils.deserializeProtoList(dc.getTeammatesBattleRole(), Define.p_battle_role.parser());
        for(int i = 0; i < battleRoles.size(); i++) {
            if(battleRoles.get(i).getId() == cancelerId) {
                battleRoles.remove(i);
                break;
            }
        }
        dc.setTeammatesBattleRole(Utils.serializeProtoList(battleRoles));
        Port.getCurrent().returns("code", EDoubleChapterType.CODE_SUCCESS);
    }

    private void sendCancelHelpResponse(HumanObject human, long helpId, int code) {
        if(code > 0){
            // 发送错误响应
            sendErrorCode(human, code);
            return;
        }
        MsgDoubleChapter.double_chapter_cancel_help_s2c.Builder msg = 
            MsgDoubleChapter.double_chapter_cancel_help_s2c.newBuilder()
                .setHelpId(helpId)
                .setCode(code);
        human.sendMsg(msg);
    }

    /**
     * 玩家聊天邀请帮助
     * @param humanObj 玩家对象
     * @param targetId 目标id
     */
    public void chatInviteShare(HumanObject humanObj, long targetId, Handler<AsyncResult<Integer>> handler) {
        DoubleChapter dc = humanObj.operation.doubleChapter;
        Port port = Port.getCurrent();
        if(dc == null){
            AsyncActionResult.success(port, handler, LanguageKey.DoubleChapterFaild);
            return;
        }

        // 构建申请者信息
        Define.p_double_chapter_help_player applyInfo = Define.p_double_chapter_help_player.newBuilder()
                .setRoleId(humanObj.getHumanId())
                .setName(humanObj.getHuman().getName())
                .setLevel(humanObj.getHuman().getLevel())
                .setHead(Define.p_head.newBuilder()
                        .setId(humanObj.getHuman().getHeadSn())
                        .setFrameId(humanObj.getHuman().getCurrentHeadFrameSn()))
                .setHelpChapterNum(0)
                .setAchieveChapter(dc.getChapter()+1)
                .setPower(new BigDecimal(humanObj.getHuman().getCombat()).longValue())
                .build();

        // 获取目标玩家信息
        HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance();
        prx.getInfo(targetId);
        prx.listenResult((Param results, Param ctx) -> {
            HumanGlobalInfo targetInfo = results.get();
            if(targetInfo != null) {
                // 玩家在线，通过服务调用
                HumanObjectServiceProxy humanPrx = HumanObjectServiceProxy
                        .newInstance(targetInfo.nodeId, targetInfo.portId, targetInfo.id);
                humanPrx.handleInviteShare(applyInfo);
                humanPrx.listenResult((res, ctx1) -> {
                    int languageKey = Utils.intValue(res.get("languageKey"));
                    Define.p_double_chapter_teammate teammate = res.get("teammate");
                    Define.p_double_chapter_teammate_detail teammateDetail = res.get("teammateDetail");
                    Define.p_battle_role battleRole = res.get("battleRole");
                    if(teammate != null && teammateDetail != null && battleRole != null){
                        // 处理成功，更新数据
                        addDoubleChapterTeammate(humanObj, teammate, teammateDetail, battleRole);
                    }
                    AsyncActionResult.success(port, handler, languageKey);
                });
            } else {
                // 玩家不在线，异步获取实体
                EntityManager.getEntityAsync(DoubleChapter.class, targetId, res -> {
                    if(res.succeeded()) {
                        DoubleChapter targetDc = res.result();
                        if(targetDc == null){
                            sendInviteResponse(humanObj, targetId, EDoubleChapterType.ERR_CODE_396);
                            AsyncActionResult.success(port, handler, LanguageKey.DoubleChapterFaild);
                            return;
                        }

                        resetApplyHelpWeekly(targetDc);

                        // 检查邀请设置
                        InviteSettings settings = getInviteSettings(targetDc);

                        // 如果不允许邀请，直接返回
                        if(!settings.allowInvite){
                            sendInviteResponse(humanObj, targetId, EDoubleChapterType.ERR_CODE_396);
                            AsyncActionResult.success(port, handler, LanguageKey.DoubleChapterFaild);
                            return;
                        }

                        // 根据不同的邀请条件进行处理
                        switch(settings.inviteCondition) {
                            case EDoubleChapterType.SETTING_NONE:
                                processInvite(humanObj, targetId, targetDc, applyInfo, settings.autoAgree, handler);
                            break;

                            case EDoubleChapterType.SETTING_FRIEND_ONLY:
                                if(!FriendManager.inst().isFriend(humanObj, targetId)) {
                                    sendInviteResponse(humanObj, targetId, EDoubleChapterType.ERR_CODE_391);
                                    AsyncActionResult.success(port, handler, LanguageKey.DoubleChapterLimit);
                                    return;
                                }
                                processInvite(humanObj, targetId, targetDc, applyInfo, settings.autoAgree, handler);
                                break;

                            case EDoubleChapterType.SETTING_FAMILY_ONLY:
                                GuildServiceProxy prxGuild = GuildServiceProxy.newInstance();
                                prxGuild.getGuildId(applyInfo.getRoleId());
                                prxGuild.listenResult((Param results1, Param ctx1) -> {
                                    long inviterGuildId = results1.get("guildId");
                                    if(inviterGuildId != 0 && humanObj.getHuman2().getGuildId() == inviterGuildId) {
                                        processInvite(humanObj, targetId, targetDc, applyInfo, settings.autoAgree, handler);
                                    } else {
                                        AsyncActionResult.success(port, handler, LanguageKey.DoubleChapterLimit);
                                    }
                                });
                                break;

                            case EDoubleChapterType.SETTING_FAMILY_AND_FRIEND:
                                if(FriendManager.inst().isFriend(humanObj, applyInfo.getRoleId())) {
                                    processInvite(humanObj, targetId, targetDc, applyInfo, settings.autoAgree, handler);
                                } else {
                                    GuildServiceProxy prxGuild2 = GuildServiceProxy.newInstance();
                                    prxGuild2.getGuildId(applyInfo.getRoleId());
                                    prxGuild2.listenResult((Param results1, Param ctx1) -> {
                                        long inviterGuildId = results1.get("guildId");
                                        if(inviterGuildId != 0 && humanObj.getHuman2().getGuildId() == inviterGuildId) {
                                            processInvite(humanObj, targetId, targetDc, applyInfo, settings.autoAgree, handler);
                                        } else {
                                            AsyncActionResult.success(port, handler, LanguageKey.DoubleChapterLimit);
                                        }
                                    });
                                }
                                break;
                        }
                    }
                });
            }
        });
    }


    /**
     * 处理邀请逻辑
     */
    private void processInvite(HumanObject humanObj, long targetId, DoubleChapter targetDc,
                               Define.p_double_chapter_help_player applyInfo, boolean autoAgree, Handler<AsyncResult<Integer>> handler) {
        // 检查助战数量限制
        List<Define.p_double_chapter_help_player> helpList = Utils.deserializeProtoList(
                targetDc.getHelpHumanData(), Define.p_double_chapter_help_player.parser());
        if(helpList.size() >= ConfGlobal.get(ConfGlobalKey.double_ladder_max_help_num).value) {
            AsyncActionResult.success(null, handler, LanguageKey.DoubleChapterFullLimit);
            return;
        }

        List<Define.p_double_chapter_help_player> applyList = getApplyList(targetDc); // <申请列表>
        for (Define.p_double_chapter_help_player player : applyList) {
            if(player.getRoleId() == applyInfo.getRoleId()) {
                AsyncActionResult.success(null, handler, LanguageKey.DoubleChapterAlreadySend);
                return;
            }
        }


        // 获取申请者的HumanBrief信息
        HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance();
        prx.getHumanBrief2(targetId);
        prx.listenResult((Param results, Param ctx) -> {
            HumanBrief brief = results.get("humanBrief");
            if(brief == null) {
                AsyncActionResult.success(null, handler, LanguageKey.DoubleChapterFaild);
                return;
            }

            if(autoAgree) {
                // 自动同意 - 直接加入助战列表
                boolean isExist = false;
                for (Define.p_double_chapter_help_player player : helpList) {
                    if(player.getRoleId() == applyInfo.getRoleId()) {
                        isExist = true;
                        break;
                    }
                }
                if (!isExist) {
                    helpList.add(applyInfo);
                    targetDc.setHelpHumanData(Utils.serializeProtoList(helpList));
                }

                // 构建战友基本信息
                DoubleChapter humanDc = humanObj.operation.doubleChapter;
                List<Define.p_double_chapter_teammate> teammates = Utils.deserializeProtoList(
                        humanDc.getTeammates(), Define.p_double_chapter_teammate.parser());
                Define.p_double_chapter_teammate teammate = Define.p_double_chapter_teammate.newBuilder()
                        .setRoleId(brief.getId())
                        .setName(brief.getName())
                        .setLevel(brief.getLevel())
                        .setHead(Define.p_head.newBuilder()
                                .setId(brief.getHeadSn())
                                .setFrameId(brief.getCurrentHeadFrameSn()))
                        .setPower(new BigDecimal(brief.getTopCombat()).longValue())
                        .build();
                try {
                    Define.p_role_figure figure = Define.p_role_figure.parseFrom(brief.getRoleFigure());
                    Define.p_battle_role battleRole = Define.p_battle_role.parseFrom(targetDc.getBattleRole());
                    if(battleRole == null || battleRole.getId()==0){
                        battleRole = Define.p_battle_role.parseFrom(brief.getBattleRole());
                    }
                    // 构建战友详细信息
                    List<Define.p_double_chapter_teammate_detail> teammatesDetail = Utils.deserializeProtoList(
                            humanDc.getTeammatesDetail(), Define.p_double_chapter_teammate_detail.parser());
                    Define.p_double_chapter_teammate_detail detail = Define.p_double_chapter_teammate_detail.newBuilder()
                            .setRoleId(brief.getId())
                            .setName(brief.getName())
                            .setLevel(brief.getLevel())
                            .setHead(Define.p_head.newBuilder()
                                    .setId(brief.getHeadSn())
                                    .setFrameId(brief.getCurrentHeadFrameSn()))
                            .setTeammateFigure(figure)
                            .addAllAttrList(battleRole.getAttrListList())
                            .addAllSkillList(battleRole.getRoleSkill().getActiveSkillList())
                            .addAllPlanTab(buildTeammatePlanInfo(brief))
                            .build();
                    // 构建战斗角色信息 - 直接从brief获取
                    List<Define.p_battle_role> teammatesBattleRole = Utils.deserializeProtoList(
                            humanDc.getTeammatesBattleRole(), Define.p_battle_role.parser());
                    isExist = false;
                    for (Define.p_battle_role role : teammatesBattleRole) {
                        if(role.getId() == brief.getId()){
                            isExist = true;
                            break;
                        }
                    }
                    if(!isExist){
                        teammates.add(teammate);
                        humanDc.setTeammates(Utils.serializeProtoList(teammates));

                        teammatesDetail.add(detail);
                        humanDc.setTeammatesDetail(Utils.serializeProtoList(teammatesDetail));

                        teammatesBattleRole.add(battleRole);
                        humanDc.setTeammatesBattleRole(Utils.serializeProtoList(teammatesBattleRole));
                        AsyncActionResult.success(null, handler, LanguageKey.DoubleChapterAlreadyAgree);
                    }else {
                        AsyncActionResult.success(null, handler, LanguageKey.DoubleChapterAlreadyAdd);
                    }

                } catch (Exception e) {
                    Log.temp.error("===构建战友信息失败，humanId={}", brief.getId(), e);
                }
            } else {
                // 需要手动同意 - 添加到申请列表
                applyList.add(applyInfo);
                targetDc.setApplyList(Utils.serializeProtoList(applyList));
                AsyncActionResult.success(null, handler, 0);
            }
            targetDc.update();
            sendInviteResponse(humanObj, targetId, EDoubleChapterType.CODE_SUCCESS);
        });
    }

    /**
     * 发送邀请响应
     */
    private void sendInviteResponse(HumanObject humanObj, long targetId, int code) {
        MsgDoubleChapter.double_chapter_is_in_invite_list_s2c.Builder msg =
                MsgDoubleChapter.double_chapter_is_in_invite_list_s2c.newBuilder()
                        .setCheckId(targetId)
                        .setCode(code);
        humanObj.sendMsg(msg);
    }

    /**
     * 处理在线玩家收到的邀请
     * @param humanObj 被邀请的玩家对象
     * @param applyInfo 申请者信息
     */
    public void handleInviteShare(HumanObject humanObj, Define.p_double_chapter_help_player applyInfo) {
        DoubleChapter dc = humanObj.operation.doubleChapter;
        Port port = Port.getCurrent();
        
        // 基础检查
        if(dc == null) {
            port.returns("languageKey", LanguageKey.DoubleChapterFaild);
            return;
        }

        // 获取邀请设置
        InviteSettings settings = getInviteSettings(dc);
        if(!settings.allowInvite) {
            port.returns("languageKey", LanguageKey.DoubleChapterFaild);
            return;
        }

        // 检查队伍容量
        List<Define.p_double_chapter_help_player> helpList = getHelpPlayerList(dc);
        if(helpList.size() >= ConfGlobal.get(ConfGlobalKey.double_ladder_max_help_num).value) {
            port.returns("languageKey", LanguageKey.DoubleChapterFullLimit);
            return;
        }

        // 检查是否已经在助战列表或申请列表中
        if(isPlayerInList(helpList, applyInfo.getRoleId())) {
            port.returns("languageKey", LanguageKey.DoubleChapterAlreadyAdd);
            return;
        }

        List<Define.p_double_chapter_help_player> applyList = getApplyList(dc);
        if(isPlayerInList(applyList, applyInfo.getRoleId())) {
            port.returns("languageKey", LanguageKey.DoubleChapterAlreadySend);
            return;
        }

        // 根据不同邀请条件处理
        handleByInviteCondition(humanObj, dc, applyInfo, settings);
    }

    /**
     * 邀请设置类
     */
    private static class InviteSettings {
        boolean allowInvite = true;
        boolean autoAgree = false;
        boolean allowChat = false;
        int inviteCondition = EDoubleChapterType.SETTING_NONE;
    }

    /**
     * 获取邀请设置
     */
    private InviteSettings getInviteSettings(DoubleChapter dc) {
        InviteSettings settings = new InviteSettings();
        List<Define.p_key_value> settingList = Utils.deserializeProtoList(
                dc.getSettingList(), Define.p_key_value.parser());

        if(settingList != null) {
            for(Define.p_key_value setting : settingList) {
                int key = (int)setting.getK();
                long value = setting.getV();
                
                switch(key) {
                    case EDoubleChapterType.SETTING_ALLOW_INVITE:
                        settings.allowInvite = value == EDoubleChapterType.SETTING_ON;
                        break;
                    case EDoubleChapterType.SETTING_PRIVATE_INVITE:
                        settings.allowChat = value == EDoubleChapterType.SETTING_ON;
                        break;
                    case EDoubleChapterType.SETTING_AUTO_ACCEPT:
                        settings.autoAgree = value == EDoubleChapterType.SETTING_ON;
                        break;
                    case EDoubleChapterType.SETTING_INVITE_CONDITION:
                        settings.inviteCondition = (int)value;
                        break;
                }
            }
        }
        return settings;
    }

    /**
     * 获取助战列表
     */
    private List<Define.p_double_chapter_help_player> getHelpPlayerList(DoubleChapter dc) {
        List<Define.p_double_chapter_help_player> list = Utils.deserializeProtoList(
                dc.getHelpHumanData(), Define.p_double_chapter_help_player.parser());
        return list != null ? list : new ArrayList<>();
    }

    /**
     * 获取申请列表
     */
    private List<Define.p_double_chapter_help_player> getApplyList(DoubleChapter dc) {
        List<Define.p_double_chapter_help_player> list = Utils.deserializeProtoList(
                dc.getApplyList(), Define.p_double_chapter_help_player.parser());
        return list != null ? list : new ArrayList<>();
    }

    /**
     * 检查玩家是否在列表中
     */
    private boolean isPlayerInList(List<Define.p_double_chapter_help_player> list, long playerId) {
        for(Define.p_double_chapter_help_player player : list) {
            if(player.getRoleId() == playerId) {
                return true;
            }
        }
        return false;
    }

    /**
     * 根据邀请条件处理邀请
     */
    private void handleByInviteCondition(HumanObject humanObj, DoubleChapter dc, 
            Define.p_double_chapter_help_player applyInfo, InviteSettings settings) {
        
        Port port = Port.getCurrent();
        long inviterId = applyInfo.getRoleId();
        
        switch(settings.inviteCondition) {
            case EDoubleChapterType.SETTING_NONE:
                // 无条件限制
                processDirectInvite(humanObj, dc, applyInfo, settings);
                break;
                
            case EDoubleChapterType.SETTING_FRIEND_ONLY:
                // 仅好友可邀请
                if(!FriendManager.inst().isFriend(humanObj, inviterId)) {
                    port.returns("languageKey", LanguageKey.DoubleChapterLimit);
                    return;
                }
                processDirectInvite(humanObj, dc, applyInfo, settings);
                break;
                
            case EDoubleChapterType.SETTING_FAMILY_ONLY:
                // 仅公会成员可邀请
                handleGuildInvite(humanObj, dc, applyInfo, settings.autoAgree);
                break;
                
            case EDoubleChapterType.SETTING_FAMILY_AND_FRIEND:
                // 公会成员或好友可邀请
                if(FriendManager.inst().isFriend(humanObj, inviterId)) {
                    processDirectInvite(humanObj, dc, applyInfo, settings);
                } else {
                    handleGuildInvite(humanObj, dc, applyInfo, settings.autoAgree);
                }
                break;
        }
    }

    /**
     * 处理直接邀请（非公会验证）
     */
    private void processDirectInvite(HumanObject humanObj, DoubleChapter dc, 
            Define.p_double_chapter_help_player applyInfo, InviteSettings settings) {
        
        int result = processInviteOnline(humanObj, dc, applyInfo, settings.autoAgree);
        Port port = Port.getCurrent();
        
        if(result == LanguageKey.DoubleChapterAlreadyAdd || result == LanguageKey.DoubleChapterAlreadyAgree) {
            Define.p_double_chapter_teammate teammate = buildTeammate(humanObj);
            Define.p_double_chapter_teammate_detail teammateDetail = buildTeammateDetail(humanObj);
            Define.p_battle_role battleRole = humanObj.to_p_battle_role(0);
            Port.getCurrent().returnsImmutable(
                    "languageKey", result,
                    "teammate", teammate,
                    "teammateDetail", teammateDetail,
                    "battleRole", battleRole);
        } else {
            port.returns("languageKey", result);
        }
    }

    /**
     * 处理公会邀请
     */
    private void handleGuildInvite(HumanObject humanObj, DoubleChapter dc, 
            Define.p_double_chapter_help_player applyInfo, boolean autoAgree) {
        
        GuildServiceProxy prxGuild = GuildServiceProxy.newInstance();
        prxGuild.getGuildId(applyInfo.getRoleId());
        
        long pid = Port.getCurrent().createReturnAsync();
        
        prxGuild.listenResult((Param results, Param ctx) -> {
            long inviterGuildId = results.get("guildId");
            if(inviterGuildId != 0 && humanObj.getHuman2().getGuildId() == inviterGuildId) {
                // 同一公会成员
                int result = processInviteOnline(humanObj, dc, applyInfo, autoAgree);
                if(result == LanguageKey.DoubleChapterAlreadyAdd || result == LanguageKey.DoubleChapterAlreadyAgree) {
                    Define.p_double_chapter_teammate teammate = buildTeammate(humanObj);
                    Define.p_double_chapter_teammate_detail teammateDetail = buildTeammateDetail(humanObj);
                    Define.p_battle_role battleRole = humanObj.to_p_battle_role(0);
                    
                    Port.getCurrent().returnsImmutableAsync(pid, 
                        "languageKey", result,
                        "teammate", teammate,
                        "teammateDetail", teammateDetail,
                        "battleRole", battleRole);
                } else {
                    Port.getCurrent().returnsImmutableAsync(pid, "languageKey", result);
                }
            } else {
                // 不是同一公会
                Port.getCurrent().returnsImmutableAsync(pid, "languageKey", LanguageKey.DoubleChapterLimit);
            }
        });
    }

    /**
     * 处理在线玩家的邀请逻辑
     */
    private int processInviteOnline(HumanObject humanObj, DoubleChapter dc,
            Define.p_double_chapter_help_player applyInfo, boolean autoAgree) {

        List<Define.p_double_chapter_help_player> helpList = Utils.deserializeProtoList(
                dc.getHelpHumanData(), Define.p_double_chapter_help_player.parser());
        for (Define.p_double_chapter_help_player helpPlayer : helpList){
            if(helpPlayer.getRoleId() == applyInfo.getRoleId()){
                // 如果已经在助战列表中，直接返回
                return LanguageKey.DoubleChapterAlreadyAdd;
            }
        }
        
        if(autoAgree) {
            // 自动同意 - 直接加入助战列表
            helpList.add(applyInfo);
            dc.setHelpHumanData(Utils.serializeProtoList(helpList));
            return LanguageKey.DoubleChapterAlreadyAgree;
        } else {
            // 需要手动同意 - 添加到申请列表
            List<Define.p_double_chapter_help_player> applyList = Utils.deserializeProtoList(
                    dc.getApplyList(), Define.p_double_chapter_help_player.parser());
            for (Define.p_double_chapter_help_player helpPlayer : applyList){
                if(helpPlayer.getRoleId() == applyInfo.getRoleId()){
                    // 如果已经在申请列表中，直接返回
                    return LanguageKey.DoubleChapterAlreadySend;
                }
            }
            applyList.add(applyInfo);
            dc.setApplyList(Utils.serializeProtoList(applyList));
        }
        return 0;
    }

    /**
     * 构建队友基本信息
     */
    private Define.p_double_chapter_teammate buildTeammate(HumanObject humanObj) {
        return Define.p_double_chapter_teammate.newBuilder()
            .setRoleId(humanObj.getHumanId())
            .setName(humanObj.getHuman().getName())
            .setLevel(humanObj.getHuman().getLevel())
            .setHead(Define.p_head.newBuilder()
                .setId(humanObj.getHuman().getHeadSn())
                .setFrameId(humanObj.getHuman().getCurrentHeadFrameSn()))
            .setPower(new BigDecimal(humanObj.getHuman().getCombat()).longValue())
            .build();
    }

    /**
     * 处理获取队友技能冷却时间
     * @param humanObj 玩家对象
     * @param checkId 检查ID
     */
    public void handleGetTeammateSkillDelayTime(HumanObject humanObj, long checkId) {
        DoubleChapter dc = humanObj.operation.doubleChapter;
        if(dc == null) {
            return;
        }

        MsgDoubleChapter.double_chapter_get_teammate_skill_delay_time_s2c.Builder msg = 
            MsgDoubleChapter.double_chapter_get_teammate_skill_delay_time_s2c.newBuilder();

        if(checkId == humanObj.getHumanId()) {
            // 获取自己的技能列表
            List<Define.p_active_skill> skillList = SkillManager.inst().getAllActiveSkill(humanObj);
            msg.addAllSkillList(skillList);

            // 获取自己的技能延迟设置
            List<Define.p_key_value> skillDelay = Utils.deserializeProtoList(
                dc.getSkillDelay(), Define.p_key_value.parser());
            if(skillDelay != null && !skillDelay.isEmpty()) {
                msg.addAllSetDelayTime(skillDelay);
            }
        } else {
            // 从teammatesBattleRole中获取其他玩家的技能信息
            List<Define.p_battle_role> teammatesBattleRole = Utils.deserializeProtoList(
                dc.getTeammatesBattleRole(), Define.p_battle_role.parser());
            
            for(Define.p_battle_role battleRole : teammatesBattleRole) {
                if(battleRole.getId() == checkId) {
                    List<Define.p_active_skill> skillList = battleRole.getRoleSkill().getActiveSkillList();
                    msg.addAllSkillList(skillList);
                    for (Define.p_active_skill skill : skillList) {
                        Define.p_key_value.Builder kv = Define.p_key_value.newBuilder()
                            .setK(skill.getPosId())
                            .setV(skill.getDelayTime());
                        msg.addSetDelayTime(kv);
                    }
                        break;
                    }
                }
        }

        humanObj.sendMsg(msg);
    }

    /**
     * 处理设置队友技能冷却时间
     * @param humanObj 玩家对象
     * @param targetId 目标ID
     * @param setDelayTimeList 设置的冷却时间列表
     */
    public void handleSetTeammateSkillDelayTime(HumanObject humanObj, long targetId,
                                                List<Define.p_key_value> setDelayTimeList) {
        DoubleChapter dc = humanObj.operation.doubleChapter;
        if(dc == null) {
            return;
        }

        if(targetId == humanObj.getHumanId()) {
            // 设置自己的技能延迟
            dc.setSkillDelay(Utils.serializeProtoList(setDelayTimeList));
            dc.update();
        } else {
            // 更新teammatesBattleRole中的技能延迟
            List<Define.p_battle_role> teammatesBattleRole = Utils.deserializeProtoList(
                    dc.getTeammatesBattleRole(), Define.p_battle_role.parser());

            boolean found = false;
            for(int i = 0; i < teammatesBattleRole.size(); i++) {
                Define.p_battle_role battleRole = teammatesBattleRole.get(i);
                if(battleRole.getId() == targetId) {
                    // 获取原有的技能列表
                    Define.p_role_skill.Builder roleSkillBuilder = battleRole.getRoleSkill().toBuilder();
                    List<Define.p_active_skill> skillList = new ArrayList<>(
                            roleSkillBuilder.getActiveSkillList());

                    // 更新每个技能的delay_time
                    for(int j = 0; j < skillList.size(); j++) {
                        Define.p_active_skill skill = skillList.get(j);
                        for(Define.p_key_value delayTime : setDelayTimeList) {
                            if(delayTime.getK() == skill.getPosId()) {
                                Define.p_active_skill newSkill = skill.toBuilder()
                                        .setDelayTime((int)delayTime.getV())
                                        .build();
                                skillList.set(j, newSkill);
                                break;
                            }
                        }
                    }

                    // 创建新的battleRole，更新技能列表
                    Define.p_battle_role newBattleRole = battleRole.toBuilder()
                            .setRoleSkill(roleSkillBuilder.clearActiveSkill()
                                    .addAllActiveSkill(skillList))
                            .build();
                    teammatesBattleRole.set(i, newBattleRole);
                    found = true;
                    break;
                }
            }

            if(!found) {
                sendErrorCode(humanObj, EDoubleChapterType.ERR_CODE_5);
                return;
            }

            dc.setTeammatesBattleRole(Utils.serializeProtoList(teammatesBattleRole));
        }

        // 发送成功响应
        MsgDoubleChapter.double_chapter_set_teammate_skill_delay_time_s2c.Builder msg =
                MsgDoubleChapter.double_chapter_set_teammate_skill_delay_time_s2c.newBuilder()
                        .setCode(EDoubleChapterType.CODE_SUCCESS)
                        .setTargetId(targetId)
                        .addAllSetDelayTime(setDelayTimeList);
        humanObj.sendMsg(msg);
    }

    /**
     * 处理获取关卡信息
     * @param humanObj 玩家对象
     */
    public void handleGetLevelInfo(HumanObject humanObj) {
        DoubleChapter dc = humanObj.operation.doubleChapter;
        if(dc == null) {
            return;
        }
        
        // 构建响应消息
        MsgDoubleChapter.double_chapter_get_level_info_s2c.Builder msg = 
            MsgDoubleChapter.double_chapter_get_level_info_s2c.newBuilder();
        
        // 获取站位信息
        List<Define.p_key_value> battlePosList = Utils.deserializeProtoList(
            dc.getBattlePosList(), Define.p_key_value.parser());
        if(battlePosList != null && !battlePosList.isEmpty()) {
            msg.addAllBattlePos(battlePosList);
        }
        
        // 获取策略ID
        msg.setStrategyId(dc.getStrategyId());

        humanObj.sendMsg(msg);
    }

    /**
     * 处理修改站位信息
     * @param humanObj 玩家对象
     * @param battlePosList 战斗位置列表
     */
    public void handleChangePosInfo(HumanObject humanObj, List<Define.p_key_value> battlePosList) {
        DoubleChapter dc = humanObj.operation.doubleChapter;
        if(dc == null) {
            return;
        }
        
        // 保存新的站位信息
        dc.setBattlePosList(Utils.serializeProtoList(battlePosList));

        // 发送成功响应
        MsgDoubleChapter.double_chapter_change_pos_info_s2c.Builder msg = 
            MsgDoubleChapter.double_chapter_change_pos_info_s2c.newBuilder()
                .setCode(EDoubleChapterType.CODE_SUCCESS)
                .addAllBattlePos(battlePosList);
        humanObj.sendMsg(msg);
    }

    /**
     * 处理修改策略
     * @param humanObj 玩家对象
     * @param strategyId 策略ID
     */
    public void handleChangeStrategy(HumanObject humanObj, int strategyId) {
        DoubleChapter dc = humanObj.operation.doubleChapter;
        if(dc == null) {
            return;
        }
        
        // 检查策略ID是否有效
        if(strategyId < EDoubleChapterType.MIN_STRATEGIC_SN || strategyId > EDoubleChapterType.MAX_STRATEGIC_SN){
            Log.game.error("玩家：{}发来无效的策略修改：{}",humanObj.id, strategyId);
            return;
        }
        // 保存新的策略ID
        dc.setStrategyId(strategyId);

        // 发送成功响应
        MsgDoubleChapter.double_chapter_change_strategy_s2c.Builder msg = 
            MsgDoubleChapter.double_chapter_change_strategy_s2c.newBuilder()
                .setCode(EDoubleChapterType.CODE_SUCCESS)
                .setStrategyId(strategyId);
        humanObj.sendMsg(msg);
    }

    /**
     * 处理获取通关信息
     * @param humanObj 玩家对象
     * @param level 关卡等级
     */
    public void handleGetThroughLevelInfo(HumanObject humanObj, int level) {
        DoubleChapter dc = humanObj.operation.doubleChapter;
        if(dc == null) {
            return;
        }

        ConfDoubleLadderChapter confDoubleLadderChapter = ConfDoubleLadderChapter.get(level);
        if(confDoubleLadderChapter == null){
            return;
        }
        
        // 获取通关信息列表
        List<MsgDoubleChapter.double_chapter_get_through_level_info_s2c> throughList = Utils.deserializeProtoList(
            dc.getThroughLevelInfo(), MsgDoubleChapter.double_chapter_get_through_level_info_s2c.parser());
        
        MsgDoubleChapter.double_chapter_get_through_level_info_s2c.Builder msg = 
            MsgDoubleChapter.double_chapter_get_through_level_info_s2c.newBuilder();
        
        // 关卡索引从1开始，所以level-1作为列表索引
        if(confDoubleLadderChapter.index <= throughList.size()) {
            MsgDoubleChapter.double_chapter_get_through_level_info_s2c info = throughList.get(confDoubleLadderChapter.index - 1);
            msg.setMyMaxHp(info.getMyMaxHp())
               .setMyBfHp(info.getMyBfHp())
               .setMyAfHp(info.getMyAfHp())
               .setTeammateMaxHp(info.getTeammateMaxHp())
               .setTeammateBfHp(info.getTeammateBfHp())
               .setTeammateAfHp(info.getTeammateAfHp());
        }
        
        humanObj.sendMsg(msg);
    }

    /**
     * 处理重新开始关卡
     * @param humanObj 玩家对象
     * @param level 关卡等级
     */
    public void handleRestartLevel(HumanObject humanObj, int level) {
        DoubleChapter dc = humanObj.operation.doubleChapter;
        if(dc == null) {
            return;
        }

        ConfDoubleLadderChapter confDoubleLadderChapter = ConfDoubleLadderChapter.get(level);
        if(confDoubleLadderChapter == null){
            return;
        }
        
        // 检查关卡等级
        if(confDoubleLadderChapter.index > dc.getLevel()) {
            // 发送成功响应
            MsgDoubleChapter.double_chapter_restart_level_s2c.Builder msg =
                    MsgDoubleChapter.double_chapter_restart_level_s2c.newBuilder()
                            .setCode(EDoubleChapterType.CODE_SUCCESS);
            humanObj.sendMsg(msg);
            handleDungeonDcInfo(humanObj);
            return;
        }
        
        // 获取通关信息列表
        List<MsgDoubleChapter.double_chapter_get_through_level_info_s2c> throughList = Utils.deserializeProtoList(
            dc.getThroughLevelInfo(), MsgDoubleChapter.double_chapter_get_through_level_info_s2c.parser());
        
        // 只保留小于重置关卡的数据
        if(confDoubleLadderChapter.index <= throughList.size()) {
            throughList = throughList.subList(0, confDoubleLadderChapter.index - 1);
        }
        
        // 更新通关信息
        dc.setThroughLevelInfo(Utils.serializeProtoList(throughList));
        dc.setLevel(confDoubleLadderChapter.index-1);

        // 发送成功响应
        MsgDoubleChapter.double_chapter_restart_level_s2c.Builder msg = 
            MsgDoubleChapter.double_chapter_restart_level_s2c.newBuilder()
                .setCode(EDoubleChapterType.CODE_SUCCESS);
        humanObj.sendMsg(msg);

        handleDungeonDcInfo(humanObj);
    }

    /**
     * 处理重新开始章节
     * @param humanObj 玩家对象
     */
    public void handleRestartChapter(HumanObject humanObj) {
        DoubleChapter dc = humanObj.operation.doubleChapter;
        if(dc == null) {
            return;
        }
        
        // 清空通关信息
        dc.setThroughLevelInfo(Utils.serializeProtoList(new ArrayList<>()));
        dc.setLevel(0);

        // 发送成功响应
        MsgDoubleChapter.double_chapter_restart_chapter_s2c.Builder msg = 
            MsgDoubleChapter.double_chapter_restart_chapter_s2c.newBuilder()
                .setCode(EDoubleChapterType.CODE_SUCCESS);
        humanObj.sendMsg(msg);

        handleDungeonDcInfo(humanObj);
    }

    /**
     * 处理检查是否在邀请列表中
     * @param humanObj 玩家对象
     * @param checkId 检查ID
     */
    public void handleIsInInviteList(HumanObject humanObj, long checkId) {
        DoubleChapter dc = humanObj.operation.doubleChapter;
        if(dc == null) {
            return;
        }
        
        // 获取申请列表
        List<Define.p_double_chapter_help_player> applyList = Utils.deserializeProtoList(
            dc.getApplyList(), Define.p_double_chapter_help_player.parser());
        
        MsgDoubleChapter.double_chapter_is_in_invite_list_s2c.Builder msg = 
            MsgDoubleChapter.double_chapter_is_in_invite_list_s2c.newBuilder()
                .setCheckId(checkId);
            
        // 检查是否在申请列表中
        for(Define.p_double_chapter_help_player player : applyList) {
            if(player.getRoleId() == checkId) {
                msg.setCode(EDoubleChapterType.CODE_SUCCESS_1)
                   .setInviterInfo(player);
                humanObj.sendMsg(msg);
                return;
            }
        }
        
        // 不在申请列表中
        humanObj.sendMsg(msg);
    }

    /**
     * 功能解锁
     */
    @Listener(EventKey.FUNCTION_OPEN)
    public void unlock(Param param) {
        HumanObject humanObj = param.get("humanObj");
        if (humanObj.operation.doubleChapter != null) {
            return;
        }
        List<Integer> snList = Utils.getParamValue(param, "openList", new ArrayList<>());
        if (!snList.contains(FuncOpenType.FUNC_DOUBLE_LADDER)) {
            return;
        }
        createDoubleChapter(humanObj);
        handleDcEntranceInfo(humanObj);
    }

    public void createDoubleChapter(HumanObject humanObj) {
        DoubleChapter doubleChapter = new DoubleChapter();
        doubleChapter.setId(humanObj.id);
        doubleChapter.setStrategyId(1);
        int currentPlanId = humanObj.getHumanExtInfo().getPlan();
        if(!humanObj.operation.planVoMap.containsKey(0)){
            PlanVo defaultPlanVo = humanObj.operation.planVoMap.getOrDefault(currentPlanId, new PlanVo(0));
            humanObj.operation.planVoMap.put(0, new PlanVo(0,defaultPlanVo));
            humanObj.getHumanExtInfo().setPlanMap(PlanVo.mapToString(humanObj.operation.planVoMap));
            HumanManager.inst().handleRolePlanInfoC2S(humanObj);
        }
        humanObj.operation.doubleChapter = doubleChapter;
        resetChapterWeekly(humanObj, res->{
            doubleChapter.persist();
        });
    }

    // 新增帮助方法
    public void updateHelpList(DoubleChapter dc, Define.p_double_chapter_help_player helpPlayer, boolean isAdd) {
        List<Define.p_double_chapter_help_player> helpList = Utils.deserializeProtoList(
            dc.getHelpHumanData(), Define.p_double_chapter_help_player.parser());
        
        if(isAdd) {
            // 使用普通for循环通过索引修改
            for(int i = 0; i < helpList.size(); i++) {
                Define.p_double_chapter_help_player h = helpList.get(i);
                if(h.getRoleId() == helpPlayer.getRoleId()) {
                    // 创建新对象并替换列表中的元素
                    Define.p_double_chapter_help_player newH = h.toBuilder()
                        .setHelpChapterNum(h.getHelpChapterNum() + 1)
                        .setAchieveChapter(Math.max(h.getAchieveChapter(), helpPlayer.getAchieveChapter()))
                        .build();
                    helpList.set(i, newH);
                                break;
                            }
                        }
        } else {
            // 移除指定玩家的帮助记录
            Iterator<Define.p_double_chapter_help_player> iterator = helpList.iterator();
            while (iterator.hasNext()) {
                Define.p_double_chapter_help_player h = iterator.next();
                if (h.getRoleId() == helpPlayer.getRoleId()) {
                    // 更新帮助次数
                    dc.setHelpTimes(dc.getHelpTimes() + h.getHelpChapterNum());
                    // 移除该记录
                    iterator.remove();
                    break;
                }
            }
        }
        
        dc.setHelpHumanData(Utils.serializeProtoList(helpList));
    }

    private void sendAbandonSuccess(HumanObject humanObj, long id) {
        MsgDoubleChapter.double_chapter_abandon_teammate_s2c.Builder msg = 
            MsgDoubleChapter.double_chapter_abandon_teammate_s2c.newBuilder()
                .setId(id)
                .setCode(EDoubleChapterType.CODE_SUCCESS);
        humanObj.sendMsg(msg);
    }

    private void sendErrorCode(HumanObject humanObj, int code) {
        MsgError.error_info_s2c.Builder msg = MsgError.error_info_s2c.newBuilder();
        msg.setCode(code);
        humanObj.sendMsg(msg);
    }

    private void updateBothPlayers(HumanObject inviter, long applyId, 
            Define.p_double_chapter_help_player apply) {
        
        // 1. 更新邀请方(自己)的数据
        Define.p_double_chapter_teammate_detail teammateDetail = buildTeammateDetail(inviter);
        Define.p_battle_role battleRole = inviter.to_p_battle_role(0);
        inviter.operation.doubleChapter.setBattleRole(battleRole.toByteArray());
        
        // 2. 获取被同意方的数据并更新双方信息
        HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance();
        prx.getInfo(applyId);
        prx.listenResult(this::_result_updateBothPlayers, 
            "inviter", inviter,
            "apply", apply,
            "teammateDetail", teammateDetail,
            "battleRole", battleRole);
    }

    private void _result_updateBothPlayers(Param results, Param context) {
        HumanObject inviter = context.get("inviter");
        Define.p_double_chapter_help_player apply = context.get("apply");
        Define.p_double_chapter_teammate_detail teammateDetail = context.get("teammateDetail");
        Define.p_battle_role battleRole = context.get("battleRole");
        HumanGlobalInfo targetInfo = results.get();
        
        // 构建我方信息作为teammate
        Define.p_double_chapter_teammate inviterTeammate = buildTeammate(inviter);
            
        if (targetInfo != null) {
            // 在线处理
            HumanObjectServiceProxy humanPrx = HumanObjectServiceProxy
                .newInstance(targetInfo.nodeId, targetInfo.portId, targetInfo.id);
                
            // 更新对方的teammates列表
            humanPrx.addDoubleChapterTeammate(inviterTeammate, teammateDetail, battleRole);
            humanPrx.listenResult(this::_result_getDoubleChapterInfo,
                "humanObj", inviter,
                "apply", apply);
        } else {
            // 离线处理
            EntityManager.getEntityAsync(DoubleChapter.class, apply.getRoleId(), res -> {
                if (res.succeeded()) {
                    DoubleChapter targetDc = res.result();
                    if (targetDc != null) {
                        // 更新teammates列表
                        List<Define.p_double_chapter_teammate> teammates =
                                Utils.deserializeProtoList(targetDc.getTeammates(),
                                        Define.p_double_chapter_teammate.parser());
                        teammates.add(inviterTeammate);
                        targetDc.setTeammates(Utils.serializeProtoList(teammates));

                        // 更新teammatesDetail
                        List<Define.p_double_chapter_teammate_detail> teammatesDetail =
                                Utils.deserializeProtoList(targetDc.getTeammatesDetail(),
                                        Define.p_double_chapter_teammate_detail.parser());
                        teammatesDetail.add(teammateDetail);
                        targetDc.setTeammatesDetail(Utils.serializeProtoList(teammatesDetail));

                        // 更新teammatesBattleRole
                        List<Define.p_battle_role> teammatesBattleRole =
                                Utils.deserializeProtoList(targetDc.getTeammatesBattleRole(),
                                        Define.p_battle_role.parser());
                        teammatesBattleRole.add(battleRole);
                        targetDc.setTeammatesBattleRole(Utils.serializeProtoList(teammatesBattleRole));

                        // 使用最新的chapter数据更新helpHumans
                        updateHelpHumans(inviter, apply, targetDc.getMaxChapter());
                        
                        // 保存更新
                        targetDc.update();
                    }
                }
            });
        }
    }

    private void _result_getDoubleChapterInfo(Param results, Param context) {
        HumanObject humanObj = context.get("humanObj");
        Define.p_double_chapter_help_player apply = context.get("apply");
        int maxChapter = results.get("maxChapter");
        
        updateHelpHumans(humanObj, apply, maxChapter);
    }

    private void updateHelpHumans(HumanObject humanObj,
            Define.p_double_chapter_help_player apply, 
            int targetMaxChapter) {
        DoubleChapter inviterDc = humanObj.operation.doubleChapter;
        List<Define.p_double_chapter_help_player> helpHumans = Utils.deserializeProtoList(
            inviterDc.getHelpHumanData(), Define.p_double_chapter_help_player.parser());
        for (Define.p_double_chapter_help_player helpPlayer : helpHumans) {
            if (helpPlayer.getRoleId() == apply.getRoleId()) {
                //已经存在，直接返回
                return;
            }
        }
        // 使用apply的builder更新achieve_chapter
        Define.p_double_chapter_help_player helpPlayer = apply.toBuilder()
            .setAchieveChapter(targetMaxChapter)
            .build();
        
        helpHumans.add(helpPlayer);
        inviterDc.setHelpHumanData(Utils.serializeProtoList(helpHumans));
    }

    public void addDoubleChapterTeammate(HumanObject humanObj, Define.p_double_chapter_teammate inviterTeammate, Define.p_double_chapter_teammate_detail teammateDetail, Define.p_battle_role battleRole) {
        DoubleChapter dc = humanObj.operation.doubleChapter;

        // 1. 添加队友基本信息
        List<Define.p_double_chapter_teammate> teammates =
                Utils.deserializeProtoList(dc.getTeammates(), Define.p_double_chapter_teammate.parser());
        if(teammates == null) {
            teammates = new ArrayList<>();
        }

        // 检查是否已存在，避免重复添加
        boolean existsTeammate = false;
        for(int i = 0; i < teammates.size(); i++) {
            if(teammates.get(i).getRoleId() == inviterTeammate.getRoleId()) {
                teammates.set(i, inviterTeammate);  // 更新现有数据
                existsTeammate = true;
                break;
            }
        }
        if(!existsTeammate) {
            teammates.add(inviterTeammate);
        }
        dc.setTeammates(Utils.serializeProtoList(teammates));

        // 2. 添加队友详细信息
        List<Define.p_double_chapter_teammate_detail> teammatesDetail =
                Utils.deserializeProtoList(dc.getTeammatesDetail(), Define.p_double_chapter_teammate_detail.parser());
        if(teammatesDetail == null) {
            teammatesDetail = new ArrayList<>();
        }

        boolean existsDetail = false;
        Define.p_double_chapter_teammate_detail useTeammateDetail = null;
        for(int i = 0; i < teammatesDetail.size(); i++) {
            if(teammatesDetail.get(i).getRoleId() == teammateDetail.getRoleId()) {
                teammatesDetail.set(i, teammateDetail);  // 更新现有数据
                existsDetail = true;
            }
            if(teammatesDetail.get(i).getRoleId() == dc.getUseTeammate()) {
                useTeammateDetail = teammatesDetail.get(i);
            }
        }
        if(!existsDetail) {
            teammatesDetail.add(teammateDetail);
        }
        dc.setTeammatesDetail(Utils.serializeProtoList(teammatesDetail));

        // 3. 添加战斗角色数据
        List<Define.p_battle_role> battleRoles =
                Utils.deserializeProtoList(dc.getTeammatesBattleRole(), Define.p_battle_role.parser());
        if(battleRoles == null) {
            battleRoles = new ArrayList<>();
        }

        boolean existsBattleRole = false;
        for(int i = 0; i < battleRoles.size(); i++) {
            if(battleRoles.get(i).getId() == battleRole.getId()) {
                battleRoles.set(i, battleRole);  // 更新现有数据
                existsBattleRole = true;
                break;
            }
        }
        if(!existsBattleRole) {
            battleRoles.add(battleRole);
        }
        dc.setTeammatesBattleRole(Utils.serializeProtoList(battleRoles));

        //发送更新消息给客户端
        MsgDoubleChapter.double_chapter_get_teammate_info_s2c.Builder msg =
                MsgDoubleChapter.double_chapter_get_teammate_info_s2c.newBuilder();
        msg.setId(dc.getUseTeammate());
        if(useTeammateDetail != null){
            msg.setTeammateDetail(teammateDetail);
        }
        msg.setMaxTeammateNum(teammateDetail.getAttrListCount());
        msg.addAllAllTeammate(teammates);
        humanObj.sendMsg(msg);



    }

    /**
     * 重置每周章节数据
     */
    public void resetChapterWeekly(HumanObject humanObj, Handler<AsyncResult<Boolean>> onComplete) {
        DoubleChapter dc = humanObj.operation.doubleChapter;
        if(dc == null) {
            AsyncActionResult.fail(null, onComplete, "DoubleChapter not found");
            return;
        }
        long now = Port.getTime();
        if(now - dc.getLastResetTime() < Time.WEEK) {
            AsyncActionResult.success(null, onComplete, false);
            return;
        }
        // 1. 获取结算数据
        int beforeLevel = dc.getChapter();
        int decayedWeeks = (int)((now - dc.getLastResetTime())/Time.WEEK);
        int resetLevel = Math.max(0, beforeLevel - ConfGlobal.get(ConfGlobalKey.double_ladder_match_num).value * decayedWeeks);
        // 2. 重置助战数据
        resetApplyHelpWeekly(dc);

        int helpTimes = Math.min(EDoubleChapterType.MAX_HELP_TIMES, dc.getHelpTimes()); // 从重置的助战数据中获取帮助次数

        // 3. 获取结算奖励
        Map<Integer, Integer> chooseRewards = getChooseRewards(dc, resetLevel);
        Map<Integer, Integer> throughRewards = getThroughChapterReward(resetLevel);
        Map<Integer, Integer> helpRewards = new HashMap<>();

        // 获取助战奖励
        ConfDoubleLadderAssistReward confAssist = ConfDoubleLadderAssistReward.get(helpTimes);
        if(confAssist != null && confAssist.reward != null) {
            for(int[] reward : confAssist.reward) {
                helpRewards.put(reward[0], helpRewards.getOrDefault(reward[0], 0) + reward[1]);
            }
        }

        // 合并所有奖励
        Map<Integer, Integer> allRewards = new HashMap<>(chooseRewards);
        throughRewards.forEach((k, v) -> allRewards.merge(k, v, Integer::sum));
        helpRewards.forEach((k, v) -> allRewards.merge(k, v, Integer::sum));


        // 5. 重置玩家战斗数据
        Define.p_battle_role battleRole = humanObj.to_p_battle_role(0);
        dc.setBattleRole(battleRole.toByteArray());
        List<Define.p_double_chapter_teammate> teammates = new ArrayList<>();
        List<Define.p_double_chapter_teammate_detail> teammatesDetail = new ArrayList<>();
        List<Define.p_battle_role> teammatesBattleRole = new ArrayList<>();


        // 6. 获取随机机器人队友
        HumanManager.inst().getHumanBriefByRankRange(humanObj.getHumanId(), 3, 5, 5, res -> {
            if(res.succeeded()) {
                if(now - dc.getLastResetTime()< Time.WEEK) {
                    AsyncActionResult.success(null, onComplete, false);
                    return;
                }
                List<HumanBrief> robotList = res.result();

                int id = 0;
                for(HumanBrief robot : robotList) {
                    ++id;
                    String randomName = AccountManager.inst().defaultName();
                    try {
                        Define.p_role_figure figure = Define.p_role_figure.parseFrom(robot.getRoleFigure());
                        Define.p_battle_role battleRoleTeamate = Define.p_battle_role.parseFrom(robot.getBattleRole());
                        if(battleRoleTeamate != null && battleRoleTeamate.getId()!= 0){
                            Define.p_battle_role.Builder battleRoleBuilder = battleRoleTeamate.toBuilder();
                            battleRoleBuilder.setId(id);
                            battleRoleBuilder.setName(randomName);
                            battleRoleTeamate = battleRoleBuilder.build();
                        }
                        // 构建战友基本信息
                        Define.p_double_chapter_teammate teammate = Define.p_double_chapter_teammate.newBuilder()
                                .setRoleId(id)
                                .setName(randomName)
                                .setLevel(robot.getLevel())
                                .setHead(Define.p_head.newBuilder()
                                        .setId(robot.getHeadSn())
                                        .setFrameId(robot.getCurrentHeadFrameSn()))
                                .setPower(new BigDecimal(robot.getTopCombat()).longValue())
                                .build();

                        Define.p_double_chapter_teammate_detail detail = Define.p_double_chapter_teammate_detail.newBuilder()
                                .setRoleId(id)
                                .setName(randomName)
                                .setLevel(robot.getLevel())
                                .setHead(Define.p_head.newBuilder()
                                        .setId(robot.getHeadSn())
                                        .setFrameId(robot.getCurrentHeadFrameSn()))
                                .setTeammateFigure(figure)
                                .addAllAttrList(battleRoleTeamate.getAttrListList())
                                .addAllSkillList(battleRoleTeamate.getRoleSkill().getActiveSkillList())
                                .addAllPlanTab(buildTeammatePlanInfo(robot))
                                .build();
                        // 构建战斗角色信息 - 直接从brief获取

                        teammates.add(teammate);
                        dc.setTeammates(Utils.serializeProtoList(teammates));

                        teammatesDetail.add(detail);
                        dc.setTeammatesDetail(Utils.serializeProtoList(teammatesDetail));

                        teammatesBattleRole.add(battleRoleTeamate);
                        dc.setTeammatesBattleRole(Utils.serializeProtoList(teammatesBattleRole));

                    } catch (Exception e) {
                        Log.temp.error("===构建战友信息失败，humanId={}", robot.getId(), e);
                    }
                }
                // 重置其他数据
                if(robotList.size() > 0) {
                    dc.setChapter(resetLevel);
                    dc.setLevel(0);
                    dc.setThroughLevelInfo(new byte[0]);
                    dc.setLastResetTime(Utils.getMondayZero(now));
                    dc.setUseTeammate(0);
                    // 发送奖励邮件
                    if(!allRewards.isEmpty()){
                        sendWeeklyRewardMail(humanObj, beforeLevel, allRewards);
                        sendWeekRewardS2C(humanObj, beforeLevel, resetLevel, helpTimes, chooseRewards, throughRewards, helpRewards);
                    }
                    dc.setHelpTimes(0);
                    AsyncActionResult.success(null, onComplete, true);
                }
            }
        });
    }

    private List<Define.p_dc_teammate_plan_info> buildTeammatePlanInfo(HumanBrief brief) {
        List<Define.p_dc_teammate_plan_info> planInfoList = new ArrayList<>();
        if(brief == null){
            return planInfoList;
        }

        int wingSnLv = brief.getWingSkillSnLv();
        int mountSnLv = brief.getMountSkillSnLv();
        int atfSnLv = brief.getArtifactSkillSnLv();
        Define.p_dc_teammate_plan_info planInfo = Define.p_dc_teammate_plan_info.newBuilder()
                .setPlanType(PlanVo.TAB_WING)
                .setSkillInfo(Define.p_key_value.newBuilder().setK(wingSnLv/100).setV(wingSnLv%100).build())
                .build();
        planInfoList.add(planInfo);
        planInfo = Define.p_dc_teammate_plan_info.newBuilder()
                .setPlanType(PlanVo.TAB_MOUNT)
                .setSkillInfo(Define.p_key_value.newBuilder().setK(mountSnLv/100).setV(mountSnLv%100).build())
                .build();
        planInfoList.add(planInfo);
        planInfo = Define.p_dc_teammate_plan_info.newBuilder()
                .setPlanType(PlanVo.TAB_ARTF)
                .setSkillInfo(Define.p_key_value.newBuilder().setK(atfSnLv/100).setV(atfSnLv%100).build())
                .build();
        planInfoList.add(planInfo);
        return planInfoList;
    }

    private Map<Integer, Integer> getThroughChapterReward(int chapterLv) {
        Map<Integer, Integer> rewards = new HashMap<>();
        for (int i = 0; i <= chapterLv; ++i){
            ConfDoubleLadderChapter conf = GlobalConfVal.getConfDoubleLadderChapter(i, GlobalConfVal.getDoubleChapterMaxLevel(i));
            if(conf != null && conf.reward != null) {
                Utils.intArrToIntMap(rewards,conf.reward);
            }
        }
        return rewards;
    }

    private Map<Integer, Integer> getChooseRewards(DoubleChapter dc, int resetLevel) {
        Map<Integer, Integer> rewards = new HashMap<>();
        List<Integer> chooseList = Utils.strToIntList(dc.getChooseRewardList());

        if(chooseList == null || chooseList.isEmpty()) {
            return rewards;
        }

        for(Integer combination : chooseList) {
            int chapterLv = combination / 100;
            int index = combination % 100 - 1;

            ConfDoubleLadderChapter conf = GlobalConfVal.getConfDoubleLadderChapter(chapterLv, GlobalConfVal.getDoubleChapterMaxLevel(chapterLv));
            if(conf != null && conf.final_reward_unlock != null && index < conf.final_reward_unlock.length) {
                int[] reward = conf.final_reward_unlock[index];
                rewards.put(reward[0], rewards.getOrDefault(reward[0], 0) + reward[1]);
            }
        }

        int rewardSize = 0;
        ConfDoubleLadderChapter confDoubleLadderChapter = GlobalConfVal.getConfDoubleLadderChapter(resetLevel, GlobalConfVal.getDoubleChapterMaxLevel(resetLevel));
        if(confDoubleLadderChapter != null){
            rewardSize = confDoubleLadderChapter.final_reward_times;
        }
        if(chooseList.size() > rewardSize) {
            chooseList = chooseList.subList(0, rewardSize);
            dc.setChooseRewardList(Utils.arrayIntToStr(chooseList));
        }

        return rewards;
    }

    /**
     * 发送周结算奖励邮件
     */
    private void sendWeeklyRewardMail(HumanObject humanObj, int beforeLevel, Map<Integer, Integer> rewards) {
        // 构建邮件参数
        JSONObject jo = new JSONObject();

        // 添加之前的等级参数
        JSONObject jo1 = new JSONObject();
        jo1.put(MailManager.MAIL_K_4, beforeLevel);
        jo.put(MailManager.MAIL_PARAM_1, jo1);

        String chooseItemJSON = Utils.mapIntIntToJSON(rewards);
        MailManager.inst().sendMail(
                humanObj.getHumanId(),
                MailManager.SYS_SENDER,
                10182,
                "",
                jo.toJSONString(),
                chooseItemJSON,
                new Param()
        );
    }

    /**
     * 奖励结算弹窗
     */
    public void sendWeekRewardS2C(HumanObject humanObj, int beforeLevel, int resetLevel, int helpTimes, Map<Integer, Integer> chooseRewards, Map<Integer,Integer> throughRewards, Map<Integer, Integer> helpRewards) {
        // 构建奖励结算弹窗消息
        MsgDoubleChapter.double_chapter_week_reward_s2c.Builder msg = MsgDoubleChapter.double_chapter_week_reward_s2c.newBuilder();
        msg.setBeforeLevel(beforeLevel);
        msg.setResetLevel(resetLevel);
        msg.setHelpTimes(helpTimes);
        msg.addAllChooseReward(InstanceManager.inst().to_p_rewardList(chooseRewards));
        msg.addAllLevelReward(InstanceManager.inst().to_p_rewardList(throughRewards));
        msg.addAllHelpReward(InstanceManager.inst().to_p_rewardList(helpRewards));
        // 发送消息
        humanObj.sendMsg(msg);
    }
    /**
     * 重置每周助战数据
     */
    public void resetApplyHelpWeekly(DoubleChapter dc) {
        long now = Port.getTime();
        if(dc.getChapterHelpResetTime() > now) {
            return;
        }

        // 1. 统计帮助次数
        int helpTimes = 0;
        List<Define.p_double_chapter_help_player> helpHumans =
                Utils.deserializeProtoList(dc.getHelpHumanData(), Define.p_double_chapter_help_player.parser());
        if(helpHumans != null) {
            for (Define.p_double_chapter_help_player helpHuman : helpHumans) {
                helpTimes += Math.min(helpHuman.getHelpChapterNum(), ConfGlobal.get(ConfGlobalKey.double_ladder_partner_num).value);
            }
        }
        dc.setHelpTimes(dc.getHelpTimes() + helpTimes);

        // 2. 清空助战相关数据
        dc.setApplyList(Utils.serializeProtoList(new ArrayList<>()));
        dc.setHelpHumanData(Utils.serializeProtoList(new ArrayList<>()));

        // 3. 更新重置时间
        dc.setChapterHelpResetTime(Utils.getNextMondayZero(now));
    }

    @Listener(EventKey.HUMAN_RESET_WEEK_ZERO)
    public void onHumanResetWeekZero(Param param) {
        HumanObject humanObj = param.get("humanObj");
        Boolean isOnLine = param.get("online");
        if(isOnLine != null && isOnLine){
            handleDcEntranceInfo(humanObj);
        }
    }

    public void handleBattleStart(HumanObject humanObj, int level) {
        MsgDungeon.dungeon_battle_more_start_s2c.Builder msg = MsgDungeon.dungeon_battle_more_start_s2c.newBuilder();
        DoubleChapter dc = humanObj.operation.doubleChapter;
        if(dc == null){
            msg.setCode(1);
            humanObj.sendMsg(msg);
            return;
        }

        ConfDoubleLadderChapter confDoubleLadderChapter = ConfDoubleLadderChapter.get(level);
        if(confDoubleLadderChapter == null){
            msg.setCode(1);
            humanObj.sendMsg(msg);
            return;
        }
        // 获取站位信息
        List<Define.p_key_value> battlePosList = Utils.deserializeProtoList(
                dc.getBattlePosList(), Define.p_key_value.parser());

        int selfPos = 0;
        int teammatePos = 0;
        for(Define.p_key_value pos : battlePosList) {
            if(pos.getK() >= EDoubleChapterType.POSITION_1 &&
                    pos.getK() <= EDoubleChapterType.POSITION_5) {
                if(pos.getV() == EDoubleChapterType.UNIT_SELF){
                    selfPos = (int)pos.getK();
                }
                if(pos.getV() == EDoubleChapterType.UNIT_TEAMMATE) {
                    teammatePos = (int)pos.getK();
                }
            }
        }

        if((selfPos < EDoubleChapterType.POSITION_1 || selfPos >= EDoubleChapterType.POSITION_5)
                && (teammatePos < EDoubleChapterType.POSITION_1 || teammatePos >= EDoubleChapterType.POSITION_5)){
            Log.game.error("站位信息无效 humanId={}", humanObj.getHumanId());
            msg.setCode(1);
            humanObj.sendMsg(msg);
            return;
        }

        ConfDoubleLadderStrategic confStrategic = ConfDoubleLadderStrategic.get(dc.getStrategyId());
        if(confStrategic == null){
            Log.game.error("策略配置不存在 humanId={}", humanObj.getHumanId());
            return;
        }
        int[] selfEffect = null;
        int[] teammateEffect = null;
        if(isEffectiveStrategic(confStrategic, selfPos, teammatePos)){
            selfEffect = getStrategicEffect(confStrategic, selfPos);
            teammateEffect = getStrategicEffect(confStrategic, teammatePos);
        }

        int[][] selfSkill = getChapterSkill(confDoubleLadderChapter, selfPos);
        int[][] teammateSkill = getChapterSkill(confDoubleLadderChapter, teammatePos);

        if(confDoubleLadderChapter.index > dc.getLevel() + 1){
            msg.setCode(1);
            humanObj.sendMsg(msg);
            return;
        }
        int randomSeed = Utils.getTimeSec();

        List<Define.p_battle_role> roles = new ArrayList<>();

        // 在构建通关信息之前，获取前一关卡的信息
        MsgDoubleChapter.double_chapter_get_through_level_info_s2c prevLevelInfo =
                getPreviousLevelInfo(humanObj, dc.getThroughLevelInfo(), confDoubleLadderChapter.index-1);
        if(prevLevelInfo == null){
            msg.setCode(1);
            humanObj.sendMsg(msg);
            return;
        }

        // 1. 添加自己的战斗数据
        if(selfPos > 0 && selfPos < EDoubleChapterType.POSITION_5){
            Define.p_battle_role.Builder selfRole = humanObj.to_p_battle_role_builder(humanObj.getHumanExtInfo().getPlan());
            Define.p_role_skill.Builder roleSkill = selfRole.getRoleSkillBuilder();
            if(selfEffect != null && selfEffect.length == 2){
                Define.p_passive_skill.Builder skill = Define.p_passive_skill.newBuilder();
                skill.setSkillId(selfEffect[0]);
                skill.setSkillLv(selfEffect[1]);
                roleSkill.addPassiveSkill(skill);
            }

            if(selfSkill != null && selfSkill.length > 0){
                for(int[] skill : selfSkill){
                    Define.p_passive_skill.Builder skillMsg = Define.p_passive_skill.newBuilder();
                    skillMsg.setSkillId(skill[0]);
                    skillMsg.setSkillLv(skill[1]);
                    roleSkill.addPassiveSkill(skillMsg.build());
                }
            }

            if(prevLevelInfo != null){
                selfRole.addExt(Define.p_key_value.newBuilder().setK(ParamKey.extPosType_1).setV(selfPos).build());
                selfRole.addExt(Define.p_key_value.newBuilder().setK(ParamKey.extPosType_2).setV(prevLevelInfo.getMyAfHp()).build());
            }
            roles.add(selfRole.build());
        }else if(selfPos == EDoubleChapterType.POSITION_5){
            Define.p_dead_battle_role.Builder deadRole = Define.p_dead_battle_role.newBuilder();
            deadRole.setId(humanObj.getHumanId());
            deadRole.setName(humanObj.getHuman().getName());
            deadRole.setLev(humanObj.getHuman().getLevel());
            deadRole.setFigure(HumanManager.inst().to_p_role_figure(humanObj.getHuman(), humanObj.getHuman2()));
            msg.setDealRole(deadRole.build());
        }

        // 2. 添加队友的战斗数据
        if(teammatePos > 0  && teammatePos < EDoubleChapterType.POSITION_5){
            long useTeammate = dc.getUseTeammate();
            if(useTeammate > 0) {
                List<Define.p_battle_role> teammateRoles = Utils.deserializeProtoList(
                        dc.getTeammatesBattleRole(), Define.p_battle_role.parser());
                for(Define.p_battle_role role : teammateRoles) {
                    if(role.getId() == useTeammate) {
                        if(prevLevelInfo != null){
                            Define.p_battle_role.Builder roleBuilder = role.toBuilder();
                            Define.p_role_skill.Builder roleSkill = roleBuilder.getRoleSkillBuilder();
                            if (teammateEffect != null && teammateEffect.length == 2) {
                                Define.p_passive_skill.Builder skill = Define.p_passive_skill.newBuilder();
                                skill.setSkillId(teammateEffect[0]);
                                skill.setSkillLv(teammateEffect[1]);
                                roleSkill.addPassiveSkill(skill);
                            }

                            if (teammateSkill != null && teammateSkill.length > 0) {
                                for (int[] skill : teammateSkill) {
                                    Define.p_passive_skill.Builder skillMsg = Define.p_passive_skill.newBuilder();
                                    skillMsg.setSkillId(skill[0]);
                                    skillMsg.setSkillLv(skill[1]);
                                    roleSkill.addPassiveSkill(skillMsg.build());
                                }
                            }
                            roleBuilder.addExt(Define.p_key_value.newBuilder().setK(ParamKey.extPosType_1).setV(teammatePos).build());
                            roleBuilder.addExt(Define.p_key_value.newBuilder().setK(ParamKey.extPosType_2).setV(prevLevelInfo.getTeammateAfHp()).build());
                            role = roleBuilder.build();
                        }
                        roles.add(role);
                        break;
                    }
                }
            }
        }else if(teammatePos == EDoubleChapterType.POSITION_5){
            long useTeammate = dc.getUseTeammate();
            if(useTeammate > 0) {
                List<Define.p_battle_role> teammateRoles = Utils.deserializeProtoList(
                        dc.getTeammatesBattleRole(), Define.p_battle_role.parser());
                for(Define.p_battle_role role : teammateRoles) {
                    if(role.getId() == useTeammate) {
                        Define.p_dead_battle_role.Builder deadRole = Define.p_dead_battle_role.newBuilder();
                        deadRole.setId(role.getId());
                        deadRole.setName(role.getName());
                        deadRole.setLev(role.getLev());
                        deadRole.setFigure(role.getFigure());
                        msg.setDealRole(deadRole.build());
                        break;
                    }
                }
            }
        }

        // 构建返回消息
        msg.setCode(0);
        msg.setType(InstanceConstants.DOUBLELADDERCHAPTERCHAPTER_32);
        msg.setDungeonId(level);
        msg.setRandomSeed(randomSeed);
        msg.addAllRoles(roles);
        humanObj.sendMsg(msg);

        humanObj.combatType = InstanceConstants.DOUBLELADDERCHAPTERCHAPTER_32;
        humanObj.combatId = confDoubleLadderChapter.sn;
    }


    /**
     * 策略是否生效
     */
    private boolean isEffectiveStrategic(ConfDoubleLadderStrategic conf, int selfPos, int teammatePos) {
       if(conf.condition == null || conf.condition.length == 0){
           return false;
       }
       for (int[] condition : conf.condition) {
           if(condition.length != 2){
               continue;
           }
           if(condition[0] == selfPos && condition[1] == teammatePos){
               return true;
           }
           if(condition[0] == teammatePos && condition[1] == selfPos){
               return true;
           }
       }
         return false;
    }

    /**
     * 获取策略对应效果
     */
    private int[] getStrategicEffect(ConfDoubleLadderStrategic conf, int pos){
        if(pos == EDoubleChapterType.POSITION_1){
            return conf.effect1;
        }else if(pos == EDoubleChapterType.POSITION_2){
            return conf.effect2;
        }else if(pos == EDoubleChapterType.POSITION_3){
            return conf.effect3;
        }else if(pos == EDoubleChapterType.POSITION_4){
            return conf.effect4;
        }
        return null;
    }

    private int[][] getChapterSkill(ConfDoubleLadderChapter conf, int pos){
        if(pos == EDoubleChapterType.POSITION_1){
            return conf.skill1;
        }else if(pos == EDoubleChapterType.POSITION_2){
            return conf.skill2;
        }else if(pos == EDoubleChapterType.POSITION_3){
            return conf.skill3;
        }else if(pos == EDoubleChapterType.POSITION_4){
            return conf.skill4;
        }
        return null;
    }

    public void handleDoubleChapterBattleResult(HumanObject humanObj, int dungeonId, int result, 
        int manualOperators, List<Define.p_battle_operator> operatorsList, List<Define.p_key_value> argsList) {
        // 获取双人副本数据
        DoubleChapter dc = humanObj.operation.doubleChapter;
        if(dc == null) {
            Log.game.error("双人副本数据为空 humanId={}", humanObj.getHumanId());
            humanObj.combatType = 0;
            humanObj.combatId = 0;
            return;
        }
        
        MsgDoubleChapter.double_chapter_info_update_s2c.Builder msg = MsgDoubleChapter.double_chapter_info_update_s2c.newBuilder();
        MsgDungeon.dungeon_result_s2c.Builder resultMsg = MsgDungeon.dungeon_result_s2c.newBuilder();
        resultMsg.setCode(0);
        resultMsg.setResult(result);
        resultMsg.setType(InstanceConstants.DOUBLELADDERCHAPTERCHAPTER_32);

        dungeonId = (int)humanObj.combatId;
        if(humanObj.combatType != InstanceConstants.DOUBLELADDERCHAPTERCHAPTER_32){
            humanObj.combatType = 0;
            humanObj.combatId = 0;
            return;
        }

        ConfDoubleLadderChapter confDoubleLadderChapter = ConfDoubleLadderChapter.get(dungeonId);
        if(confDoubleLadderChapter.chapter != dc.getChapter()+1){
            humanObj.combatType = 0;
            humanObj.combatId = 0;
            return;
        }
        humanObj.combatType = 0;
        humanObj.combatId = 0;


        // 失败处理
        if(result == InstanceConstants.lose) {
            sendBattleResultFailed(humanObj, dungeonId, dc, msg, resultMsg);
            return;
        }

        // 从argsList中获取血量信息
        MsgDoubleChapter.double_chapter_get_through_level_info_s2c preThroughLevelInfo = getPreviousLevelInfo(humanObj, dc.getThroughLevelInfo(), confDoubleLadderChapter.index-1);
        if(preThroughLevelInfo == null){
            return;
        }
        int myHp = (int)preThroughLevelInfo.getMyAfHp();
        int teammateHp = (int)preThroughLevelInfo.getTeammateAfHp();
        for(Define.p_key_value kv : argsList) {
            if(kv.getK() == EDoubleChapterType.UNIT_SELF) {
                if(kv.getV() < 0 || kv.getV() > EDoubleChapterType.MAX_HP){
                    Log.game.error("血量异常 humanId={}, hp={}", humanObj.getHumanId(), kv.getV());
                    return;
                }
                myHp = (int)kv.getV();
            } else if(kv.getK() == EDoubleChapterType.UNIT_TEAMMATE) {
                if(kv.getV() < 0 || kv.getV() > EDoubleChapterType.MAX_HP){
                    Log.game.error("血量异常 humanId={}, hp={}", humanObj.getHumanId(), kv.getV());
                    return;
                }
                teammateHp = (int)kv.getV();
            }
        }

        // 获取站位信息，检查角色是否需要复活
        List<Define.p_key_value> battlePosList = Utils.deserializeProtoList(
            dc.getBattlePosList(), Define.p_key_value.parser());

        boolean hasSelf = false;
        boolean hasTeammate = false;

        // 检查站位中是否有自己和队友
        for(Define.p_key_value pos : battlePosList) {
            if(pos.getK() >= EDoubleChapterType.POSITION_1 &&
               pos.getK() <= EDoubleChapterType.POSITION_4) {
                if(pos.getV() == EDoubleChapterType.UNIT_SELF) {
                    hasSelf = true;
                } else if(pos.getV() == EDoubleChapterType.UNIT_TEAMMATE) {
                    hasTeammate = true;
                }
            }
        }

        // 修整位恢复50%的血
        if(!hasSelf) {
            myHp = myHp + EDoubleChapterType.MAX_HP / 2;
            myHp = Math.min(myHp, EDoubleChapterType.MAX_HP);
            if(teammateHp == 0){
                sendBattleResultFailed(humanObj, dungeonId, dc, msg, resultMsg);
                return;
            }
        }

        if(!hasTeammate) {
            teammateHp = teammateHp + EDoubleChapterType.MAX_HP / 2;
            teammateHp = Math.min(teammateHp, EDoubleChapterType.MAX_HP);
            if(myHp == 0){
                sendBattleResultFailed(humanObj, dungeonId, dc, msg, resultMsg);
                return;
            }
        }

        if(myHp == 0 && teammateHp == 0){
            sendBattleResultFailed(humanObj, dungeonId, dc, msg, resultMsg);
            return;
        }
        if(myHp == 0){
            Iterator<Define.p_key_value> iterator = battlePosList.iterator();
            while (iterator.hasNext()) {
                Define.p_key_value pos = iterator.next();
                if(pos.getV() == EDoubleChapterType.UNIT_SELF) {
                    iterator.remove();
                }
            }
            battlePosList.add(Define.p_key_value.newBuilder().setK(EDoubleChapterType.POSITION_5).setV(EDoubleChapterType.UNIT_SELF).build());
            dc.setBattlePosList(Utils.serializeProtoList(battlePosList));
            MsgDoubleChapter.double_chapter_change_pos_info_s2c.Builder msgPos =
                    MsgDoubleChapter.double_chapter_change_pos_info_s2c.newBuilder()
                            .setCode(EDoubleChapterType.CODE_SUCCESS)
                            .addAllBattlePos(battlePosList);
            humanObj.sendMsg(msgPos);
        }
        if(teammateHp == 0){
            Iterator<Define.p_key_value> iterator = battlePosList.iterator();
            while (iterator.hasNext()) {
                Define.p_key_value pos = iterator.next();
                if(pos.getV() == EDoubleChapterType.UNIT_TEAMMATE) {
                    iterator.remove();
                }
            }
            battlePosList.add(Define.p_key_value.newBuilder().setK(EDoubleChapterType.POSITION_5).setV(EDoubleChapterType.UNIT_TEAMMATE).build());
            dc.setBattlePosList(Utils.serializeProtoList(battlePosList));
            MsgDoubleChapter.double_chapter_change_pos_info_s2c.Builder msgPos =
                    MsgDoubleChapter.double_chapter_change_pos_info_s2c.newBuilder()
                            .setCode(EDoubleChapterType.CODE_SUCCESS)
                            .addAllBattlePos(battlePosList);
            humanObj.sendMsg(msgPos);
        }

        // 构建通关信息
        MsgDoubleChapter.double_chapter_get_through_level_info_s2c.Builder throughLevel =
            MsgDoubleChapter.double_chapter_get_through_level_info_s2c.newBuilder();
        throughLevel.setMyAfHp(myHp);
        throughLevel.setMyMaxHp(EDoubleChapterType.MAX_HP);
        throughLevel.setTeammateAfHp(teammateHp);
        throughLevel.setTeammateMaxHp(EDoubleChapterType.MAX_HP);
        resultMsg.addExt(Define.p_key_value.newBuilder().setK(EDoubleChapterType.EXT_SELF_AFTER_HP).setV(myHp).build());
        resultMsg.addExt(Define.p_key_value.newBuilder().setK(EDoubleChapterType.EXT_TEAMMATE_AFTER_HP).setV(teammateHp).build());
        resultMsg.addExt(Define.p_key_value.newBuilder().setK(EDoubleChapterType.EXT_SELF_MAX_HP).setV(EDoubleChapterType.MAX_HP).build());
        resultMsg.addExt(Define.p_key_value.newBuilder().setK(EDoubleChapterType.EXT_TEAMMATE_MAX_HP).setV(EDoubleChapterType.MAX_HP).build());
        if(preThroughLevelInfo == null){
            throughLevel.setMyBfHp(EDoubleChapterType.MAX_HP);
            throughLevel.setTeammateBfHp(EDoubleChapterType.MAX_HP);
            resultMsg.addExt(Define.p_key_value.newBuilder().setK(EDoubleChapterType.EXT_SELF_BEFORE_HP).setV(EDoubleChapterType.MAX_HP).build());
            resultMsg.addExt(Define.p_key_value.newBuilder().setK(EDoubleChapterType.EXT_TEAMMATE_BEFORE_HP).setV(EDoubleChapterType.MAX_HP).build());
        }else {
            throughLevel.setMyBfHp(preThroughLevelInfo.getMyAfHp());
            throughLevel.setTeammateBfHp(preThroughLevelInfo.getTeammateAfHp());
            resultMsg.addExt(Define.p_key_value.newBuilder().setK(EDoubleChapterType.EXT_SELF_BEFORE_HP).setV(preThroughLevelInfo.getMyAfHp()).build());
            resultMsg.addExt(Define.p_key_value.newBuilder().setK(EDoubleChapterType.EXT_TEAMMATE_BEFORE_HP).setV(preThroughLevelInfo.getTeammateAfHp()).build());
        }

        // 获取并更新通关信息
        byte[] throughLevelInfoData = dc.getThroughLevelInfo();
        List<MsgDoubleChapter.double_chapter_get_through_level_info_s2c> throughLevels = new ArrayList<>();
        if(throughLevelInfoData != null && throughLevelInfoData.length > 0) {
            throughLevels = Utils.deserializeProtoList(throughLevelInfoData,
                MsgDoubleChapter.double_chapter_get_through_level_info_s2c.parser());
        }

        // 更新当前关卡的通关信息，并清除后续关卡记录
        while(throughLevels.size() >= confDoubleLadderChapter.index) {
            throughLevels.remove(throughLevels.size() - 1);
        }
        throughLevels.add(throughLevel.build());

        // 保存更新后的通关信息
        dc.setThroughLevelInfo(Utils.serializeProtoList(throughLevels));

        // 检查是否是最后一关
        if(confDoubleLadderChapter.part_type == EDoubleChapterType.PART_TYPE_1) {
            // 发放通关奖励
            if(dc.getChapter() != confDoubleLadderChapter.chapter) {
                ProduceManager.inst().produceAdd(humanObj, confDoubleLadderChapter.reward, MoneyItemLogKey.双人本通关);
                resultMsg.addAllRewardList(InstanceManager.inst().to_p_rewardList(confDoubleLadderChapter.reward));
            }

            // 更新难度和清除关卡记录
            dc.setChapter(confDoubleLadderChapter.chapter);
            dc.setLevel(0);
            dc.setThroughLevelInfo(new byte[0]);

            // 处理队友助战记录
            long teammateId = dc.getUseTeammate();
            if(teammateId > 100) { // 真实玩家
                // 构建助战记录
                Define.p_double_chapter_help_player help = Define.p_double_chapter_help_player.newBuilder()
                        .setRoleId(humanObj.id)
                        .setName(humanObj.getHuman().getName())
                        .setLevel(humanObj.getHuman().getLevel())
                        .setHead(Define.p_head.newBuilder()
                                .setId(humanObj.getHuman().getHeadSn())
                                .setFrameId(humanObj.getHuman().getCurrentHeadFrameSn()))
                        .setHelpChapterNum(0)
                        .setAchieveChapter(dc.getChapter())
                        .setPower(new BigDecimal(humanObj.getHuman().getCombat()).longValue()).build();
                updateDoubleChapterHelpList(teammateId, help, true);
            }
            handleDungeonDcInfo(humanObj);
        }else {
            dc.setLevel(confDoubleLadderChapter.index);
        }

        // 更新最高难度记录
        if(confDoubleLadderChapter.sn > dc.getMaxChapter()) {
            dc.setMaxChapter(confDoubleLadderChapter.sn);
        }

        // 构建返回消息
        msg.setNowDcLevel(getBattleChapter(dungeonId));
        msg.setMaxDcLevel(getBattleChapter(dc.getMaxChapter()));
        msg.setLevel(humanObj.getHuman().getLevel());
        msg.setMyHp(myHp);
        msg.setMyMaxHp(EDoubleChapterType.MAX_HP);
        msg.setTeammateHp(teammateHp);
        msg.setTeammateMaxHp(EDoubleChapterType.MAX_HP);

        // 发送消息
        humanObj.sendMsg(msg);
        humanObj.sendMsg(resultMsg);

    }

    private void sendBattleResultFailed(HumanObject humanObj, int dungeonId, DoubleChapter dc, MsgDoubleChapter.double_chapter_info_update_s2c.Builder msg, MsgDungeon.dungeon_result_s2c.Builder resultMsg) {
        msg.setNowDcLevel(dungeonId);
        msg.setMaxDcLevel(dc.getMaxChapter());
        msg.setLevel(humanObj.getHuman().getLevel());
        humanObj.sendMsg(msg);
        humanObj.sendMsg(resultMsg);
    }

    private int getBattleChapter(int chapter){
        int battleChapter = chapter + 1;
        if(battleChapter >= GlobalConfVal.getConfMaxDoubleLadderChapterSn()){
            return GlobalConfVal.getConfMaxDoubleLadderChapterSn();
        }
        return battleChapter;
    }

    /**
     * 处理双人副本入口信息
     */
    public void handleDcEntranceInfo(HumanObject humanObj) {
        DoubleChapter dc = humanObj.operation.doubleChapter;
        MsgDungeon.dungeon_dc_entrance_info_s2c.Builder msg = 
            MsgDungeon.dungeon_dc_entrance_info_s2c.newBuilder();
        
        if(dc == null){
            msg.setIsOpen(0);
            humanObj.sendMsg(msg);
            return;
        }
        
        // 重置周数据
        resetChapterWeekly(humanObj,res->{
            // 构建可选奖励列表
            List<Define.p_key_value> chooseRewardList = new ArrayList<>();
            List<Integer> chooseList = Utils.strToIntList(dc.getChooseRewardList());
            for(Integer combination : chooseList) {
                int chapterLv = combination / 100;
                int index = combination % 100;
                Define.p_key_value.Builder kvBuilder = Define.p_key_value.newBuilder();
                kvBuilder.setK(chapterLv);
                kvBuilder.setV(index);
                chooseRewardList.add(kvBuilder.build());
            }

            // 获取当前难度可选奖励次数
            ConfDoubleLadderChapter conf = GlobalConfVal.getConfDoubleLadderChapter(dc.getChapter(),GlobalConfVal.getDoubleChapterMaxLevel(dc.getChapter()));
            int maxRewardNum = conf != null ? conf.final_reward_times : 0;

            msg.setIsOpen(1);
            msg.setStartTime((int)(dc.getLastResetTime()/Time.SEC));
            msg.setEndTime((int)(dc.getChapterHelpResetTime()/Time.SEC));
            msg.addAllChooseRewardList(chooseRewardList);
            msg.setMaxRewardNum(maxRewardNum);
            humanObj.sendMsg(msg);
        });
    }

    /**
     * 处理双人副本信息
     */
    public void handleDungeonDcInfo(HumanObject humanObj) {
        DoubleChapter dc = humanObj.operation.doubleChapter;
        MsgDungeon.dungeon_dc_info_s2c.Builder msg = 
            MsgDungeon.dungeon_dc_info_s2c.newBuilder();
        
        if(dc == null){
            humanObj.sendMsg(msg);
            return;
        }
        resetChapterWeekly(humanObj,res->{
            // 设置当前等级和最大等级
            int sendNowDcLevel = 0;
            ConfDoubleLadderChapter confDoubleLadderChapter = GlobalConfVal.getConfDoubleLadderChapter(dc.getChapter()+1, dc.getLevel()+1);
            if(confDoubleLadderChapter == null) {
                int maxDcSn = GlobalConfVal.getConfMaxDoubleLadderChapterSn();
                ConfDoubleLadderChapter confMax = ConfDoubleLadderChapter.get(maxDcSn);
                if(confMax.chapter == dc.getChapter() && 0 == dc.getLevel()){
                    confDoubleLadderChapter = confMax;
                    sendNowDcLevel = maxDcSn + 1;
                }
                if(confDoubleLadderChapter == null){
                    Log.game.error("双人本查看玩家数据找不到配置,chapter={},level={}", dc.getChapter(), dc.getLevel());
                    return;
                }
            }else {
                sendNowDcLevel = confDoubleLadderChapter.sn;
            }
            msg.setNowDcLevel(sendNowDcLevel);
            msg.setMaxDcLevel(getBattleChapter(dc.getMaxChapter()));
            msg.setLevel(humanObj.getHuman().getLevel());

            // 设置自己的信息
            msg.setMyFigure(HumanManager.inst().to_p_role_figure(humanObj.getHuman(), humanObj.getHuman2()));
            msg.setMyName(humanObj.getHuman().getName());

            // 获取血量信息
            MsgDoubleChapter.double_chapter_get_through_level_info_s2c lastLevel = getPreviousLevelInfo(humanObj, dc.getThroughLevelInfo(), dc.getLevel());
            if(lastLevel == null){
                return;
            }
            msg.setMyHp(lastLevel.getMyAfHp());
            msg.setMyMaxHp(lastLevel.getMyMaxHp());
            msg.setTeammateHp(lastLevel.getTeammateAfHp());
            msg.setTeammateMaxHp(lastLevel.getTeammateMaxHp());

            // 设置队友信息
            long useTeammate = dc.getUseTeammate();
            if(useTeammate > 0) {
                msg.setTeammateId(useTeammate);
                List<Define.p_double_chapter_teammate_detail> teammatesDetail =
                        Utils.deserializeProtoList(dc.getTeammatesDetail(), Define.p_double_chapter_teammate_detail.parser());

                for(Define.p_double_chapter_teammate_detail detail : teammatesDetail) {
                    if(detail.getRoleId() == useTeammate) {
                        msg.setTeammateFigure(detail.getTeammateFigure());
                        msg.setTeammateName(detail.getName());
                        msg.setTeammateLevel(detail.getLevel());
                        msg.setTeammateHead(detail.getHead());
                        break;
                    }
                }
            }

            humanObj.sendMsg(msg);
        });
    }

    /**
     * 获取指定关卡的前一关卡血量信息
     */
    private MsgDoubleChapter.double_chapter_get_through_level_info_s2c getPreviousLevelInfo(HumanObject humanObj,
            byte[] throughLevelInfoData, int currentLevel) {
        if(currentLevel <= 0){
            MsgDoubleChapter.double_chapter_get_through_level_info_s2c.Builder throughLevel = MsgDoubleChapter.double_chapter_get_through_level_info_s2c.newBuilder();
            throughLevel.setMyAfHp(EDoubleChapterType.MAX_HP);
            throughLevel.setMyMaxHp(EDoubleChapterType.MAX_HP);
            throughLevel.setMyBfHp(EDoubleChapterType.MAX_HP);
            throughLevel.setTeammateAfHp(EDoubleChapterType.MAX_HP);
            throughLevel.setTeammateMaxHp(EDoubleChapterType.MAX_HP);
            throughLevel.setTeammateBfHp(EDoubleChapterType.MAX_HP);
            return throughLevel.build();
        }
        if (throughLevelInfoData == null || throughLevelInfoData.length == 0) {
            Log.game.error("前一关卡信息为空 humanId={},chapter={},level={},getLevel={}",
                    humanObj.getHumanId(), humanObj.operation.doubleChapter.getChapter(), humanObj.operation.doubleChapter.getLevel(), currentLevel);
            return null;
        }
        List<MsgDoubleChapter.double_chapter_get_through_level_info_s2c> throughLevels =
            Utils.deserializeProtoList(throughLevelInfoData, 
                MsgDoubleChapter.double_chapter_get_through_level_info_s2c.parser());
        
        if (throughLevels.size() >= currentLevel) {
            return throughLevels.get(currentLevel - 1);
        }
        
        return null;
    }
}
