package org.gof.demo.worldsrv.guild.league;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import io.vertx.core.json.JsonArray;
import org.gof.core.Port;
import org.gof.core.dbsrv.redis.CrossRedis;
import org.gof.core.dbsrv.redis.EntityManager;
import org.gof.core.dbsrv.redis.RedisTools;
import org.gof.core.gen.proxy.DistrClass;
import org.gof.core.gen.proxy.DistrMethod;
import org.gof.core.scheduler.ScheduleMethod;
import org.gof.core.scheduler.ScheduleTask;
import org.gof.core.support.*;
import org.gof.demo.distr.DistrKit;
import org.gof.demo.distr.cross.CrossHumanLoader;
import org.gof.demo.distr.cross.domain.CrossGroupInfo;
import org.gof.demo.distr.cross.domain.CrossType;
import org.gof.demo.worldsrv.bridgeEntity.GuildLeagueSeason;
import org.gof.demo.worldsrv.bridgeEntity.*;
import org.gof.demo.worldsrv.common.DataResetService;
import org.gof.demo.worldsrv.common.GamePort;
import org.gof.demo.worldsrv.common.GameServiceBase;
import org.gof.demo.worldsrv.config.*;
import org.gof.demo.worldsrv.entity.*;
import org.gof.demo.worldsrv.family32.Family32Const;
import org.gof.demo.worldsrv.family32.Family32Utils;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.guild.*;
import org.gof.demo.worldsrv.guild.league.match.*;
import org.gof.demo.worldsrv.human.HumanBriefLoadType;
import org.gof.demo.worldsrv.human.HumanData;
import org.gof.demo.worldsrv.human.HumanGlobalServiceProxy;
import org.gof.demo.worldsrv.human.HumanManager;
import org.gof.demo.worldsrv.instance.InstanceManager;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgGvg;
import org.gof.demo.worldsrv.msg.MsgRank;
import org.gof.demo.worldsrv.rank.RankInfo;
import org.gof.demo.worldsrv.rank.RankManager;
import org.gof.demo.worldsrv.rank.RankParamKey;
import org.gof.demo.worldsrv.redis.RedisKeys;
import org.gof.demo.worldsrv.support.D;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.Util;
import org.gof.demo.worldsrv.support.enumKey.ConfGlobalKey;
import org.quartz.CronExpression;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

@DistrClass(servId = D.SERV_GUILD_LEAGUE, importClass = { List.class, Map.class, LeagueMatchGroup.class, HumanBriefVO.class }, localOnly = false)
public class GuildLeagueService extends GameServiceBase {
    // 组，赛季信息
    private Map<Integer, GuildLeagueSeason> guildSeasonMap = new HashMap<>();
    // serverId，赛季信息
    private Map<Integer, GuildLeagueSeasonWarmUp> guildSeasonWarmUpMap = new HashMap<>();
    // 宗门信息
    private Map<Long, GuildLeagueRecord> guildRecordMap = new HashMap<>();

    private GuildLeagueRegister register = new GuildLeagueRegister();

    private LeagueMatcher matcher = new LeagueMatcher();

    private TickTimer seasonTimer = new TickTimer();
    private TickTimer ttTimer10s = new TickTimer();
    private TickTimer ttMatch = new TickTimer();    // 开始匹配时间
    private TickTimer ttBattle = new TickTimer();   // 战斗时间，周三，四，五

    private TickTimer ttBattleEnd = new TickTimer();   // 战斗时间，周三，四，五

    // 发送单轮奖励定时器
    private TickTimer ttRank = new TickTimer();
    private TickTimer ttFailover = new TickTimer();

    private int step = ParamKey.guild_gvg_step_0;

    private TickTimer ttSecBattle = new TickTimer();
    private int battleIdex = 0;

    private boolean battle = false;
    private boolean isMatch = false;

    private TickTimer ttBattleGetData = null; // 战斗前获取数据

    private boolean isSettleDay = false;
    private boolean isWeekSend = false;
    private boolean isSeasonSend = false;

    private boolean isOpenGvg = false;

    public volatile transient AtomicInteger aiSettleHumanWeek = new AtomicInteger(0);
    public volatile transient AtomicInteger aiSettleGuildWeek = new AtomicInteger(0);

    private Map<Integer, Map<Long, LeagueVO>> serverSettleHumanInfoWeekMap = new ConcurrentHashMap<>();
    private Map<Integer, Map<Long, LeagueVO>> serverSettleGuildInfoWeekMap = new ConcurrentHashMap<>();
    public volatile transient AtomicInteger aiSettleHuman = new AtomicInteger(0);
    public volatile transient AtomicInteger aiSettleGuild = new AtomicInteger(0);
    private Map<Integer, Map<Long, LeagueVO>> serverSettleHumanInfoMap = new ConcurrentHashMap<>();
    private Map<Integer, Map<Long, LeagueVO>> serverSettleGuildInfoMap = new ConcurrentHashMap<>();

    private LinkedList<Long> humanIdJoinList = new LinkedList<>();// 参加乱斗的玩家
    private Set<Long> humanIdJoinSet = new TreeSet<>();

    private TickTimer ttSecCheck = new TickTimer(Time.SEC);
    private int checkJoinIndex = 0;


    public GuildLeagueService(GamePort port) { super(port); }

    @ScheduleMethod(DataResetService.CRON_WEEK_5_hour_22)
    public void _CRON_WEEK_5_hour_22() {
        // 本周结算
        weekSettle();
        // 赛季结算
        seasonSettle();
    }

    private void updateSetp(int step){
        boolean isLoad =false;
        if(this.step != step && (step == ParamKey.guild_gvg_step_7 || step == ParamKey.guild_gvg_step_9 || step == ParamKey.guild_gvg_step_13)){
            isLoad = true;
        }
        if(this.step != step && (step == ParamKey.guild_gvg_step_5 || step == ParamKey.guild_gvg_step_10 || step == ParamKey.guild_gvg_step_14)){
            Map<Integer, List<Long>> serverGuildIdMap = getServerGuildIdList();
            for(Map.Entry<Integer, List<Long>> entry : serverGuildIdMap.entrySet()){
                try{
                    GuildServiceProxy proxy = GuildServiceProxy.newInstance(DistrKit.getWorldNodeID(entry.getKey()));
                    proxy.sendMsg_guild_schedule_s2c(new ArrayList(entry.getValue()), 1);
                } catch (Exception e){
                    Log.temp.error("通知出错了", e);
                }
            }

        } else if(this.step != step && (step == ParamKey.guild_gvg_step_6 || step == ParamKey.guild_gvg_step_11 || step == ParamKey.guild_gvg_step_15)){
            Map<Integer, List<Long>> serverGuildIdMap = getServerGuildIdList();
            for(Map.Entry<Integer, List<Long>> entry : serverGuildIdMap.entrySet()){
                try{
                    GuildServiceProxy proxy = GuildServiceProxy.newInstance(DistrKit.getWorldNodeID(entry.getKey()));
                    proxy.sendMsg_guild_schedule_s2c(new ArrayList(entry.getValue()), 0);
                } catch (Exception e){
                    Log.temp.error("通知出错了", e);
                }
            }
        }
        this.step = step;
        for(GuildLeagueSeason season : guildSeasonMap.values()){
            if(season == null){
                continue;
            }
            season.setStep(step);
        }
        Set<Integer> serverIdSet = new HashSet<>();
        for(GuildLeagueRecord guildRecord : guildRecordMap.values()){
            serverIdSet.add(guildRecord.getServerId());
        }
        Set<Integer> servTagIdSet = new HashSet<>();
        for(int serverId : serverIdSet){
            int servTagId = Util.getServerTagId(serverId);
            if(servTagId > 0){
                servTagIdSet.add(servTagId);
            }
        }

        if(isLoad){
            matcher.guildBattleGetHumanData(guildRecordMap);
            this.ttBattleGetData = new TickTimer(2 * Time.SEC);
        }

        for(int servTagId : servTagIdSet){
            try {
                HumanGlobalServiceProxy.newInstance(DistrKit.getWorldNodeID(servTagId)).operation(ParamKey.operationType_1);
            } catch (Exception e) {
                Log.temp.error("===通知阶段改变", servTagId);
            }
        }
        Log.temp.error("===阶段step={}, stepTimeMap={}, stepEndTimeMap={}", step, GuildLeagueUtils.getStepTimeMap(), GuildLeagueUtils.getStepEndTimeMap());
    }

    private  Map<Integer, List<Long>> getServerGuildIdList(){
        int round = GuildManager.inst().guildLeagueRound();
        long timeNow = Port.getTime();
        Set<Long> guildIdSet = new HashSet<>();
        Map<Integer, List<Long>> serverGuildIdMap = new HashMap<>();
        for(GuildLeagueMatch match : matcher.matchMap.values()){
            if(match == null){
                continue;
            }
            if(!Utils.isSameWeek(match.getOpenTime(), timeNow)){
                continue;
            }
            Map<Integer, Map<Long, Long>> battleNumGuildIdMap = Utils.jsonToIntMapLongLong(match.getBattleNumGuildIdMap());
            if(battleNumGuildIdMap == null || battleNumGuildIdMap.isEmpty()){
                continue;
            }
            Map<Long, Long> guildIdMap = battleNumGuildIdMap.get(round);
            if(guildIdMap == null){
                continue;
            }
            for(Map.Entry<Long, Long> entry : guildIdMap.entrySet()){
                long guildId = entry.getKey();
                long guildIdRival = entry.getValue();

                if(guildIdRival <= 0){
                    continue;
                }
                if(guildIdSet.add(guildId)){
                    GuildLeagueRecord guildData = guildRecordMap.get(guildId);
                    if(guildData != null){
                        serverGuildIdMap.computeIfAbsent(guildData.getServerId(), k -> new ArrayList<>()).add(guildId);
                    }
                }

                if(guildIdSet.add(guildIdRival)){
                    GuildLeagueRecord guildData = guildRecordMap.get(guildIdRival);
                    if(guildData != null){
                        serverGuildIdMap.computeIfAbsent(guildData.getServerId(), k -> new ArrayList<>()).add(guildIdRival);
                    }
                }
            }
        }
        return serverGuildIdMap;
    }


    private void seasonSettle(){
        if(isSeasonSend){
            return;
        }
        isSeasonSend = true;
        for(Map.Entry<Integer, GuildLeagueSeason> entry : guildSeasonMap.entrySet()){
            int group = entry.getKey();
            GuildLeagueSeason season = entry.getValue();
            if(season.getWeek() != 4){
                continue;
            }
            // 个人
            sendSettleHumanReward(group, season.getSeason());
            // 工会
            sendSettleGuildReward(group, season.getSeason());
        }
    }

    private void sendSettleHumanReward(int group, int season){
        String redisKey = RedisKeys.bridge_pvp_group_human_rank + group;
        int maxNum = GlobalConfVal.getLeagueRankMaxNum(ConfFamiliyBrawl.get(1).self_rank_cross_reward);

        aiSettleHuman.incrementAndGet();
        CrossRedis.getRankListByIndex(redisKey, 0, maxNum, true, f -> {
            if (f.failed()) {
                Log.temp.error("===获取排行数据失败， redisKey={}", redisKey);
                return;
            }
            List<String> idList = (List<String>) f.result().getList();
            for(int i = 0; i < idList.size(); i+=2) {
                String idStr = idList.get(i);
                int score = Utils.intValue(idList.get(i+1));
                int rank = i / 2 + 1; // 计算排名
                long humanId = Utils.longValue(idStr);
                int familiySn = GlobalConfVal.getLeagueRankSn(ConfFamiliyBrawl.get(1).self_rank_cross_reward, rank);// 个人赛季排行榜
                if (familiySn <= 0) {
                    Log.temp.error("===排行榜发奖励结束，可能失败，rank={}, humanId={}, familiySn={}", rank, humanId, familiySn);
                    break;
                }
                ConfFamiliybrawlRankReward conf = ConfFamiliybrawlRankReward.get(familiySn);
                if(conf == null){
                    Log.temp.error("===ConfFamiliybrawlRankReward错误，rank={}, humanId={}, familiySn={}", rank, humanId, familiySn);
                    continue;
                }
                int serverId = Utils.getServerIdByHumanId(humanId);
                LeagueVO vo = new LeagueVO();
                vo.voId = humanId;
                vo.intValue = familiySn;
                vo.longValue = rank;
                vo.param.put("score", score);
                vo.param.put("mailSn", ParamKey.leangeRankType_9_mailSn);
                serverSettleHumanInfoMap.computeIfAbsent(serverId, k -> new ConcurrentHashMap<>()).put(humanId, vo);
            }
            aiSettleHuman.decrementAndGet();
            if(aiSettleHuman.get() == 0){
                toServerHumanReward();
                Log.temp.error("===season={}, 玩家赛季結算完毕", season);
            }
        });
        Log.temp.error("===season={}, 玩家赛季結算开始", season);
    }

    public void toServerHumanReward(){
        for(Map.Entry<Integer, Map<Long, LeagueVO>> entry : serverSettleHumanInfoMap.entrySet()){
            int serverId = entry.getKey();
            HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance(DistrKit.getWorldNodeID(serverId));
            proxy.sendSettleHumanReward(entry.getValue());
            proxy.listenResult(this::_result_sendSettleHumanReward, "serverId", serverId, "map", entry.getValue());
        }
    }

    private void _result_sendSettleHumanReward(boolean timeout, Param results, Param context){
        if(timeout){
            Log.temp.error("===超时了， context={}", context);
            return;
        }
        if(results.containsKey("result") && results.getBoolean("result")){
            return;
        }
        Log.temp.error("===结算奖励失败， context={}", context);

    }

    private void sendSettleGuildReward(int group, int season){
        String redisKey = RedisKeys.bridge_pvp_group_guild_rank + group;
        int maxNum = GlobalConfVal.getLeagueRankMaxNum(ConfFamiliyBrawl.get(1).family_rank_cross_reward);
        aiSettleGuild.incrementAndGet();
        CrossRedis.getRankListByIndex(redisKey, 0, maxNum, false, f -> {
            if (f.failed()) {
                Log.temp.error("===获取排行数据失败， redisKey={}", redisKey);
                return;
            }
            List<String> idList = (List<String>) f.result().getList();
            for(int rank = 1; rank <= idList.size(); rank++) {
                String idStr = idList.get(rank - 1);
                long guildId = Utils.longValue(idStr);
                int familiySn = GlobalConfVal.getLeagueRankSn(ConfFamiliyBrawl.get(1).family_rank_cross_reward, rank);// 公会赛季排行榜
                if (familiySn <= 0) {
                    Log.temp.error("===排行榜发奖励结束，可能失败，rank={}, guildId={}, familiySn={}", rank, guildId, familiySn);
                    break;// TODO 没有后续奖励了
                }
                ConfFamiliybrawlRankReward conf = ConfFamiliybrawlRankReward.get(familiySn);
                if(conf == null){
                    Log.temp.error("===ConfFamiliybrawlRankReward错误，rank={}, guildId={}, familiySn={}", rank, guildId, familiySn);
                    continue;
                }
                GuildLeagueRecord guildData = guildRecordMap.get(guildId);
                if(guildData == null){
                    Log.temp.error("===公会数据不存在, guildId={}", guildId);
                    continue;
                }
                int serverId = guildData.getServerId();
                LeagueVO vo = new LeagueVO();
                vo.voId = guildId;
                vo.intValue = familiySn;
                vo.longValue = rank;
                vo.param.put("mailSn", ParamKey.leangeRankType_10_mailSn);
                serverSettleGuildInfoMap.computeIfAbsent(serverId, k -> new ConcurrentHashMap<>()).put(guildId, vo);
            }
            aiSettleGuild.decrementAndGet();
            if(aiSettleGuild.get() == 0){
                toServerGuildReward();
                Log.temp.info("===season={}, 赛季公会結算完毕", season);
            }
        });
        // 在乱斗赛季结算时，根据赛季保存所有组的乱斗赛季榜，作为乱斗32强的入选依据
        String gvgSeasonRankKey = Family32Utils.getGvgSeasonRankKey(season, Utils.intValue(group));
        List<String> list = (List<String>) RedisTools.toPayload(gvgSeasonRankKey, 1, RedisKeys.bridge_pvp_group_guild_rank + group);
        EntityManager.redisClient.zunionstore(list, res->{
            if(res.failed()){
                return;
            }
            RedisTools.expire(EntityManager.redisClient, gvgSeasonRankKey, Family32Const.FAMILY32_REDIS_KEY_EXPIRE_TIME);
            Log.temp.error("===根据赛季保存所有组的乱斗赛季榜成功 season={} groupId={}", season, group);
        });
        Log.temp.info("===season={}, 赛季公会結算开始", season);
    }

    private void toServerGuildReward(){
        for(Map.Entry<Integer, Map<Long, LeagueVO>> entry : serverSettleGuildInfoMap.entrySet()){
            int serverId = entry.getKey();
            HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance(DistrKit.getWorldNodeID(serverId));
            proxy.sendSettleGuildReward(entry.getValue());
            proxy.listenResult(this::_result_sendSettleGuildReward, "serverId", serverId, "map", entry.getValue());
        }
    }
    private void _result_sendSettleGuildReward(boolean timeout, Param results, Param context){
        if(timeout){
            Log.temp.error("===超时了， context={}", context);
            return;
        }
        if(results.containsKey("result") && results.getBoolean("result")){
            return;
        }
        Log.temp.error("===结算奖励失败， context={}", context);
    }

    private void weekSettle(){
        if(isWeekSend){
            return;
        }
        isWeekSend = true;
        Log.temp.error("===本周结算开始");
        Set<Long> matchIdSet = new HashSet<>();
        matchIdSet.addAll(matcher.guildIdMatchIdMap.values());

        for(long matchId : matchIdSet){
            GuildLeagueMatch match = matcher.matchMap.get(matchId);
            if(match == null){
                continue;
            }
            try{
                // 个人
                sendSettleHumanRewardWeek(match.getGroupPvp(), match.getGradeType(), match.getSeasonSn(), matchId);
            }catch (Exception e){
                e.printStackTrace();
            }
            try{
                // 工会
                sendSettleGuildRewardWeek(match.getGroupPvp(), match.getGradeType(), match.getSeasonSn(), matchId);
            }catch (Exception e){
                e.printStackTrace();
            }
        }
    }

    private void sendSettleHumanRewardWeek(int group, int grade, int season, long matchId){
        String redisKey = RedisKeys.bridge_date_group_grade_human_rank + matchId;
        ConfFamiliyBrawlRank confRank = ConfFamiliyBrawlRank.get(grade);
        if(confRank == null){
            Log.temp.error("ConfFamiliyBrawlRank配表错误， not find Sn={}", grade);
            return;
        }
        int maxNum = GlobalConfVal.getLeagueRankMaxNum(confRank.self_rank_reward);

        aiSettleHumanWeek.incrementAndGet();
        CrossRedis.getRankListByIndex(redisKey, 0, maxNum, false, f -> {
            if (f.failed()) {
                Log.temp.error("===获取排行数据失败， redisKey={}", redisKey);
                return;
            }
            List<String> idList = (List<String>) f.result().getList();
            for(int i = 0; i < idList.size(); i++) {
                String idStr = idList.get(i);
                int rank = i + 1; // 计算排名
                long humanId = Utils.longValue(idStr);
                int familiySn = GlobalConfVal.getLeagueRankSn(confRank.self_rank_reward, rank);// 个人段位排行榜
                if (familiySn <= 0) {
                    Log.temp.error("===排行榜发奖励结束，可能失败，rank={}, humanId={}, familiySn={}", rank, humanId, familiySn);
                    break;
                }
                ConfFamiliybrawlRankReward conf = ConfFamiliybrawlRankReward.get(familiySn);
                if(conf == null){
                    Log.temp.error("===ConfFamiliybrawlRankReward错误，rank={}, humanId={}, familiySn={}", rank, humanId, familiySn);
                    continue;
                }
//                String itemJSON = Utils.mapIntIntToJSON(Utils.intArrToIntMap(new HashMap<>(), conf.rank_reward));
//                JSONObject jo = new JSONObject();
//                JSONObject joTemp = new JSONObject();
//                joTemp.put(MailManager.MAIL_K_4, rank);
//                jo.put(MailManager.MAIL_PARAM_1, joTemp);

                int serverId = Utils.getServerIdByHumanId(humanId);
                LeagueVO vo = new LeagueVO();
                vo.voId = humanId;
                vo.intValue = familiySn;
                vo.longValue = rank;
                vo.param.put("mailSn",  ParamKey.leangeRankType_11_mailSn);
                serverSettleHumanInfoWeekMap.computeIfAbsent(serverId, k -> new ConcurrentHashMap<>()).put(humanId, vo);
            }
            aiSettleHumanWeek.decrementAndGet();
            if(aiSettleHumanWeek.get() == 0){
                toServerHumanRewardWeek();
                Log.temp.error("===season={}, 玩家赛季結算完毕", season);
            }
        });
        Log.temp.error("===season={} 周个人結算开始, group={}", season, group);
    }

    public void toServerHumanRewardWeek(){
        for(Map.Entry<Integer, Map<Long, LeagueVO>> entry : serverSettleHumanInfoWeekMap.entrySet()){
            int serverId = entry.getKey();
            try {
                HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance(DistrKit.getWorldNodeID(serverId));
                proxy.sendSettleHumanReward(entry.getValue());
                proxy.listenResult(this::_result_sendSettleHumanReward, "serverId", serverId, "map", entry.getValue());
            } catch (Exception e){
                Log.temp.error("===serverId={} 玩家奖励发送失败", serverId, e);
            }
        }
    }

    private void sendSettleGuildRewardWeek(int group, int grade, int season, long matchId){
        String redisKey = RedisKeys.bridge_date_group_grade_guild_rank + matchId;
        ConfFamiliyBrawlRank confRank = ConfFamiliyBrawlRank.get(grade);
        if(confRank == null){
            Log.temp.error("ConfFamiliyBrawlRank配表错误， not find Sn={}", grade);
            return;
        }
        int maxNum = GlobalConfVal.getLeagueRankMaxNum(confRank.family_rank_reward);
        aiSettleGuildWeek.incrementAndGet();
        CrossRedis.getRankListByIndex(redisKey, 0, maxNum, false, f -> {
            if (f.failed()) {
                Log.temp.error("===获取排行数据失败， redisKey={}", redisKey);
                return;
            }
            List<String> idList = (List<String>) f.result().getList();
            for(int rank = 1; rank <= idList.size(); rank++) {
                String idStr = idList.get(rank - 1);
                long guildId = Utils.longValue(idStr);
                int familiySn = GlobalConfVal.getLeagueRankSn(confRank.family_rank_reward, rank);// 工会段位排行榜
                if (familiySn <= 0) {
                    Log.temp.error("===排行榜发奖励结束，可能失败，rank={}, guildId={}, familiySn={}", rank, guildId, familiySn);
                    break;// TODO 没有后续奖励了
                }
                ConfFamiliybrawlRankReward conf = ConfFamiliybrawlRankReward.get(familiySn);
                if(conf == null){
                    Log.temp.error("===ConfFamiliybrawlRankReward错误，rank={}, guildId={}, familiySn={}", rank, guildId, familiySn);
                    continue;
                }
                GuildLeagueRecord guildData = guildRecordMap.get(guildId);
                if(guildData == null){
                    Log.temp.error("===公会数据不存在, guildId={}", guildId);
                    continue;
                }
//                String itemJSON = Utils.mapIntIntToJSON(Utils.intArrToIntMap(new HashMap<>(), conf.rank_reward));
//                JSONObject jo = new JSONObject();
//                JSONObject joTemp = new JSONObject();
//                joTemp.put(MailManager.MAIL_K_4, rank);
//                jo.put(MailManager.MAIL_PARAM_1, joTemp);

                int serverId = guildData.getServerId();
                LeagueVO vo = new LeagueVO();
                vo.voId = guildId;
                vo.intValue = familiySn;
                vo.longValue = rank;
                vo.param.put("mailSn", ParamKey.leangeRankType_12_mailSn);
                serverSettleGuildInfoWeekMap.computeIfAbsent(serverId, k -> new ConcurrentHashMap<>()).put(guildId, vo);
                Log.temp.error("===season={}, 公会結算完毕, guildid={}, json={}", season, guildId, vo.toJSON());
            }
            aiSettleGuildWeek.decrementAndGet();
            if(aiSettleGuildWeek.get() == 0){
                toServerGuildRewardWeek();
                Log.temp.info("===season={}, 本周公会結算完毕", season);
            }
        });
    }

    private void toServerGuildRewardWeek(){
        for(Map.Entry<Integer, Map<Long, LeagueVO>> entry : serverSettleGuildInfoWeekMap.entrySet()){
            int serverId = entry.getKey();
            try {
                HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance(DistrKit.getWorldNodeID(serverId));
                proxy.sendSettleGuildReward(entry.getValue());
                proxy.listenResult(this::_result_sendSettleGuildReward, "serverId", serverId, "map", entry.getValue());
            } catch (Exception e){
                Log.temp.error("===serverId={} 公会奖励发送失败", serverId, e);
            }
        }
    }

    @ScheduleMethod(DataResetService.CRON_WEEK_1_0)
    public void _CRON_WEEK_1_0() {
        //
        GlobalConfVal.reloadCrossGroups();
        GuildLeagueUtils.initStepTime();
        initRedisInfo();
        initTtMatch();
        checkStep();

        Log.temp.error("===阶段step={}, stepTimeMap={}, stepEndTimeMap={}", step, GuildLeagueUtils.getStepTimeMap(), GuildLeagueUtils.getStepEndTimeMap());
        // 为了热更
        ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.familybattle_initial_time.SN);
        long openTime = Utils.formatTimeToLong(confGlobal.strValue);
        int sumWeek = (int)Math.ceil((Utils.getDaysBetween(Port.getTime(), openTime)) *1.D / 7);
        int seasonTemp = (int)Math.ceil(sumWeek / 4.D); // 赛季

        boolean isSeasonUp = false;
        Set<Long> groupSet = new HashSet<>();
        // 每周1重算积分段位。和检测赛季
        for(GuildLeagueSeason season : guildSeasonMap.values()) {
            if(season.getSeason() != seasonTemp){
                isSeasonUp = true;
            }
            checkSeason(season);
            groupSet.add(season.getId());
        }
        if(isSeasonUp){
            removeSeasonRank(groupSet);
        }
        checkGrade();
        Log.temp.info("===每周一0点， sumWeek={}， seasonTemp={}, isSeasonUp={}", sumWeek, seasonTemp, isSeasonUp);
    }

    @ScheduleMethod(DataResetService.CRON_WEEK_7_ST_5_25)
    public void _CRON_WEEK_7_ST_5_25() {
        // 清除redis数据
        reomveGuildLeague();
    }
    private void reomveGuildLeague() {
        // 每周报名数据清理数据
        register.removeAllWeek();
    }
    private void removeSeasonRank(Set<Long> groupSet ){
        List<String> removeList = new ArrayList<>();
        for(long group : groupSet){
            removeList.add(RedisKeys.bridge_pvp_group_guild_rank + group);
            removeList.add(RedisKeys.bridge_pvp_group_human_rank + group);
        }

        EntityManager.redisClient.del(removeList, h->{
            if(h.succeeded()){
                Log.temp.error("===删除赛季榜相关成功keyList={}", removeList);
            }
        });
    }

    private void checkGrade(){
        long timeNow = Port.getTime();
        Map<Integer, List<LeagueVO>> serverVoMap = new HashMap<>();

        for(GuildLeagueRecord guildData : guildRecordMap.values()){
            GuildLeagueSeason guildSeason =  guildSeasonMap.get(guildData.getGroupPvp());
            if(guildSeason == null){
                continue;
            }
            int season = guildSeason.getSeason();
            if(season != guildData.getSeason()){
                int grade = 1;
                int sumScore = guildData.getScore();
                if(guildData.getSeason() < GuildParamKey.scoreTypeGroup_4){
                    grade = GuildManager.inst().gradeSn(GuildParamKey.scoreTypeGroup_0, sumScore);
                } else {
                    grade = GuildManager.inst().gradeSn(GuildParamKey.scoreTypeGroup_4, sumScore);
                }
                ConfFamiliyBrawlRank conf = ConfFamiliyBrawlRank.get(grade);
                if(conf == null){
                    Log.temp.error("===ConfFamiliyBrawlRank配表错误， sn={}", grade);
                    continue;
                }
                guildData.setSeason(season);
                guildData.setScore(conf.familybattle_reset);
                guildData.setSettleSeasonTime(timeNow);

                if(sumScore > 0){
                    LeagueVO vo = new LeagueVO();
                    vo.voId = guildData.getGuildId();
                    vo.intValue = season;
                    vo.param.put("mailSn", ParamKey.guildLeagueSeasonResetMailSn);
                    vo.param.put("score", sumScore);
                    vo.param.put("grade", grade);
                    vo.param.put("resetScore", conf.familybattle_reset);

                    int resetGrade = 1;
                    if(season < GuildParamKey.scoreTypeGroup_4){
                        resetGrade = GuildManager.inst().gradeSn(GuildParamKey.scoreTypeGroup_0, guildData.getScore());
                    } else {
                        resetGrade = GuildManager.inst().gradeSn(GuildParamKey.scoreTypeGroup_4, guildData.getScore());
                    }
                    vo.param.put("resetGrade", resetGrade);
                    serverVoMap.computeIfAbsent(guildData.getServerId(), k -> new ArrayList<>()).add(vo);
                }
            }
            int grade = 1;
            if(season < GuildParamKey.scoreTypeGroup_4){
                grade = GuildManager.inst().gradeSn(GuildParamKey.scoreTypeGroup_0, guildData.getScore());
            } else {
                grade = GuildManager.inst().gradeSn(GuildParamKey.scoreTypeGroup_4, guildData.getScore());
            }
            Log.temp.info("===guildId={}, season={}, grade={}, score={}", guildData.getGuildId(), season, grade, guildData.getScore());
            guildData.setGrade(grade);
        }

        if(!serverVoMap.isEmpty()){
            for(Map.Entry<Integer, List<LeagueVO>> entry : serverVoMap.entrySet()){
                int serverId = entry.getKey();
                try {
                    GuildServiceProxy proxy = GuildServiceProxy.newInstance(DistrKit.getWorldNodeID(serverId));
                    proxy.sendSeasonResetMailSn(entry.getValue());
                } catch (Exception e){
                    Log.temp.error("===serverId={} 公会重置邮件发送失败", serverId, e);
                }
            }
        }
    }

    @ScheduleMethod(DataResetService.CRON_WEEK_1_1hour_30m)
    public void _CRON_WEEK_1_1hour_30m() {
    }

    @ScheduleMethod(DataResetService.CRON_WEEK_7_ST_23_50)
    public void _CRON_WEEK_7_ST_23_50() {
        // 清数据

    }

    private void initRedisInfo(){
//        guildSeasonMap.clear();
//        guildSeasonWarmUpMap.clear();
//        guildRecordMap.clear();
        register = new GuildLeagueRegister();
        matcher = new LeagueMatcher();

        Log.temp.error("===重置乱斗数据");

        Set<Integer> groupSet = new HashSet<>();

        String crossNodeId = D.NODE_CROSS_PREFIX+"0_"+(Config.SERVER_ID);

        String groupKey = CrossType.cross_chaos_battle.getType() + "_0";
        for(CrossGroupInfo info : GlobalConfVal.getCrossGroupSnInfoMap().values()) {
            if (!info.key.equals(groupKey)) {
                continue;
            }
            for(Map.Entry<Integer, String> entry : info.groupAtCrossNodes.entrySet()){
                if(crossNodeId.equals(entry.getValue())){
                    groupSet.add(entry.getKey());
                }
            }
        }
        Log.temp.info("===加载乱斗组groupSet={}", groupSet);
        for(int group : groupSet){
            redisLoadData(group);
        }

        initTtMatch();

        if(!ttBattle.isStarted()){
            ttBattleReset();
        }
    }

    private void checkSeason(GuildLeagueSeason guildSeason){
        if(guildSeason.getStep() != step){
            guildSeason.setStep(step);
        }

        ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.familybattle_initial_time.SN);
        long openTime = Utils.formatTimeToLong(confGlobal.strValue);
        int sumWeek = (int)Math.ceil((Utils.getDaysBetween(Port.getTime(), openTime)) *1.D / 7);
        int season = (int)Math.ceil(sumWeek / 4.D); // 赛季
        int week = sumWeek % 4 == 0 ? 4 : sumWeek % 4;

        long timeOpen = Utils.getTimeOfWeek(Port.getTime(), 1, 0) - (week - 1) * Time.WEEK;// 本周一0点
        long timeEnd = Utils.getTimeOfWeek(Port.getTime(), 1, 0) + (4-week + 1) * Time.WEEK;// 第5周周一0点

        Log.temp.info("===season={} 赛季检查开始, week={}, openTime={}, endTime={}, time={}", season, week, timeOpen, timeEnd, Port.getTime());

        if(guildSeason.getOpenTime() != timeOpen || guildSeason.getEndTime() != timeEnd){
            guildSeason.setOpenTime(timeOpen);
            guildSeason.setEndTime(timeEnd);
            Log.temp.info("===season={} 赛季时间改变，week={}, openTime={}, endTime={}", season, week, timeOpen, timeEnd);
        }

        if(guildSeason.getSeason() != season){
            guildSeason.setSeason(season);
            if(week != 1){
                Log.temp.error("===season={} 跳赛季了，week={}, oldWeek={}", season, week, guildSeason.getWeek());
            }
            guildSeason.setWeek(week);
            guildSeason.setOpenTime(timeOpen);
            guildSeason.setEndTime(timeEnd);
        } else if(week != guildSeason.getWeek()){
            guildSeason.setWeek(week);
        }
    }

    private void redisLoadData(int group){

        GuildLeagueSeason guildSeason = (GuildLeagueSeason) EntityManager.getEntity(GuildLeagueSeason.class, group);
        if(guildSeason == null){
            guildSeason = GuildManager.inst().createGuildLeagueSeason(group);
            guildSeasonMap.put(group, guildSeason);
        }
//        checkSeason(guildSeason);
        if(!guildSeasonMap.containsKey(group) || guildSeasonMap.get(group) == null){
            guildSeasonMap.put(group, guildSeason);
        }

        int season = guildSeason.getSeason();
        EntityManager.getEntityListAsync(GuildLeagueRecord.class, group, (res)-> {
            if (!res.succeeded()) {
                //加载失败
                Log.temp.error("===加载公会失败，group={}", group);
                return;
            }
            List<GuildLeagueRecord> guildLeagueRecordList = res.result();
            for (GuildLeagueRecord guildRecord : guildLeagueRecordList) {
                if(guildRecord == null){
                    continue;
                }
                if(!guildRecordMap.containsKey(guildRecord.getGuildId()) || guildRecordMap.get(guildRecord.getGuildId()) == null){
                    guildRecordMap.put(guildRecord.getGuildId(), guildRecord);
                }
            }
        });

        EntityManager.getEntityListAsync(GuildLeagueEnroll.class, group, (res)-> {
            if (!res.succeeded()) {
                //加载失败
                Log.temp.error("===加载公会失败，group={}", group);
                return;
            }
            List<GuildLeagueEnroll> modelEnrollList = res.result();
            for(GuildLeagueEnroll info : modelEnrollList){
                register.loadGuildLeagueEnroll(info);
                if(info.getSeason() != season){
                    continue;
                }
                register.addGroupGrade(group, info.getGuildId(), info.getGrade());

                if(!register.matchGroups.containsKey(info.getGuildId())){
                    LeagueMatchGroup infoGroup = new LeagueMatchGroup();
                    infoGroup.guildId = info.getGuildId();
                    infoGroup.serverId = info.getServerId();
                    infoGroup.name = "";
                    infoGroup.combat = 0;
                    infoGroup.grade = info.getGrade();
                    register.matchGroups.put(infoGroup.guildId, infoGroup);
                }
            }
        });




        EntityManager.getEntityListAsync(GuildLeagueMatch.class, group, (res)-> {
            if (!res.succeeded()) {
                //加载失败
                Log.temp.error("===加载公会失败，group={}", group);
                return;
            }
            List<GuildLeagueMatch> modelMatchList = res.result();
            for(GuildLeagueMatch match : modelMatchList) {
                if(!Utils.isSameWeek(match.getOpenTime(), Port.getTime())){
                    match.remove();
                    continue;
                }
                if (match.getSeasonSn() != season) {
                    Log.temp.error("===公会赛季不匹配，matchId={}, seasonSn={}, group={}, seasonNow={}, groupNow={}", match.getId(), match.getSeasonSn(), match.getGroupPvp(), season, group);
                    continue;
                }
                matcher.addMatch(match);
            }
        });
    }

    @Override
    protected void init() {
        Log.temp.info("===初始化跨服公会乱斗服务, serverId={}", Config.SERVER_ID);
        GuildLeagueUtils.initStepTime();
        initRedisInfo();
        checkStep();
        if(!ttBattle.isStarted()){
            ttBattleReset();
        }
    }


    private void scheduleLeagueSendRankAward() {
        String openCron ="";
        if(!CronExpression.isValidExpression(openCron)){
            return;
        }
        super.scheduleCron(new ScheduleTask("servId", getId()) {
            @Override
            public String getJobGroup() {
                return getId().toString();
            }
            @Override
            public void execute() {
                //
            }
        }, openCron);

    }

    @Override
    public void pulseOverride() {
        try {
            long nowTime = Port.getTime();
            if(ttMatch.isPeriod(nowTime)){
                ttMatch.start(nowTime,  Time.WEEK);
                match();
            }
            if(ttBattleGetData != null && ttBattleGetData.isPeriod(nowTime, true)){
                matcher.getDataBattleAllList();
                if(matcher.getBattleAllList().isEmpty() || matcher.getBattleAllList().size() <= matcher.getIndex){
                    ttBattleGetData = null;
                    Log.temp.info("拉人结束， battleAllList={}", matcher.getBattleAllList().size());
                }
            }

            if(ttBattle.isPeriod(nowTime)){
                ttBattleReset();
                ttSecBattle.start(Time.MIN);
                isOpenGvg = true;
                // 开始战斗
                guildBattle();

                ttBattleEnd.start(GuildManager.inst().getBattleIntervalTime());
            }

            if(isOpenGvg && ttSecBattle.isPeriod(nowTime, true)){
                battleIdex++;
                Log.temp.error("===开始第{}场战斗", battleIdex);
                boolean isEnd = matcher.pulseBattle(battleIdex);
                if(isEnd){
                    settleDay();
                }
            }
            if(ttBattleEnd.isPeriod(nowTime)){
                ttBattleEnd.stop();
                battleIdex = 0;
                ttSecBattle.stop();
                Log.temp.info("===战斗结束，若战斗未结束，则强制结束战斗");
                // 直接强制结束战斗
                int loopNum = 0;
                while (!isSettleDay && loopNum < 500){
                    loopNum++;
                    boolean isEnd = matcher.pulseBattle(loopNum);
                    if(isEnd){
                        settleDay();
                    }
                }
            }
            // 匹配分批次创建
            matcher.pulse();
            if(ttSecCheck.isPeriod(nowTime)){
                checkHumanExist();
            }
        }catch (Exception e){
            Log.game.error("pulseOverride error", e);
        }

    }

    public void settleDay(){
        if(isSettleDay){
            Log.temp.error("====今日战斗已结算过");
            return;
        }
        isSettleDay = true;
        Log.temp.info("====今日所有战斗结束");

        Map<Long, Integer> guildIdAddScoreAllMap  = matcher.getGuildIdAddScoreAllMap();
        List<Long> winGuildList = matcher.winGuildIdList;

//        long logTime = Utils.getDayTime(Port.getTime(), ParamKey.guildLeagueEnd22, 0,0);
        ConfFamiliyBrawl conf = ConfFamiliyBrawl.get(ParamKey.familiyBrawlSn);
        // 奖励
        JSONArray winArray = new JSONArray();
        for(int i = 0; i < conf.win_reward.length; i++){
            winArray.add(conf.win_reward[i][1]);
        }
        JSONArray loseArray = new JSONArray();
        for(int i = 0; i < conf.lose_reward.length; i++){
            loseArray.add(conf.lose_reward[i][1]);
        }


        for(Map.Entry<Long, Integer> entry : guildIdAddScoreAllMap.entrySet()){
            try{
                long guildId = entry.getKey();
                int addScore = entry.getValue();
                GuildLeagueRecord info = guildRecordMap.get(guildId);
                if(info == null){
                    Log.guild.error("公会记录为空，可能公会被删。guildId={}", guildId);
                    continue;
                }
                int score = info.getScore() + addScore;
                info.setScore(score);
                info.setEvents(info.getEvents() + 1);
                int group = GlobalConfVal.getCrossServerIdGroup(CrossType.cross_chaos_battle.getType(), info.getServerId());

                // 策划要求上赛季的公会重置积分要继承到这赛季
                CrossRedis.addRank(RedisKeys.bridge_pvp_group_guild_rank + group, guildId, score);
                Log.temp.info("===guildId={},公会{}得分增加{}，赛季{}，组{}， 最终积分={}", guildId, info.getGuildName(), addScore, info.getSeason(), group,  info.getScore());

                if(winGuildList.contains(guildId)){
                    info.setWinNum(info.getWinNum() + 1);
                } else {
                    info.setLoseNum(info.getLoseNum() + 1);
                }
            }catch (Exception e){
                Log.guild.error("结算出错，guildId={}", entry.getKey());
            }
        }
        ttSecBattle.stop();

        for(GuildLeagueSeason guildSeason : guildSeasonMap.values()){
            try {
                if(guildSeason == null){
                    continue;
                }
                CrossRedis.getRankLen(RedisKeys.bridge_pvp_group_guild_rank + guildSeason.getId(), ret -> {
                    if (ret.failed()) {
                        return;
                    }
                    int len = Utils.intValue(ret.result());
                    if (len == 0) {
                        Log.temp.info("==len={}", len);
                        return;
                    }
                    CrossRedis.getRankListByIndex(RedisKeys.bridge_date_group_grade_human_rank + guildSeason.getId(), 0, 0, true, f -> {
                        if (f.failed()) {
                            return;
                        }
                        JsonArray json = f.result();
                        if (json.getList() == null || json.getList().isEmpty()) {
                            return;
                        }
                        long humanId = Utils.longValue(json.getList().get(0));
                        int score = Utils.intValue(json.getList().get(1));
                        if (humanId <= 0) {
                            return;
                        }
                        CrossHumanLoader.getHumanBrief(humanId, HumanBriefLoadType.BATTLE_INFO, ret2 -> {
                            if (ret2.failed()) {
                                Log.game.error("加载玩家简要数据HumanBrief出错,humanId={}", humanId, ret2.cause());
                                return;
                            }
                            HumanBrief humanBriefData = ret2.result();
                            if (humanBriefData == null || humanBriefData.getBattleRole() == null || humanBriefData.getBattleRole().length == 0
                                    || humanBriefData.getRoleFigure() == null || humanBriefData.getRoleFigure().length == 0) {
                                Log.game.info("===玩家简要数据失败，humanBriefData=null，尝试回本服获取。id={}，PBattleRole={} PRoleFigure={}",
                                        humanId, humanBriefData.getBattleRole(), humanBriefData.getRoleFigure());
                                HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance(DistrKit.getWorldNodeID(Utils.getServerIdByHumanId(humanId)));
                                proxy.getHumanBrief(humanId);
                                proxy.listenResult(this::_get_human_brief_result, "season", guildSeason.getSeason(),
                                        "score", score, "week", guildSeason.getWeek(), "group", guildSeason.getId());
                            } else {
//                            addTopGuildLog(guildSeason.getSeason(), humanBriefData.getGuildId(), humanId, score,
//                                    new BigDecimal(humanBriefData.getTopCombat()).longValue(), guildSeason.getWeek(), guildSeason.getId());
                            }
                        });
                    });
                });
            }catch (Exception e){
                Log.temp.error("结算出错，为了后续继续执行 guildSeason={}", guildSeason.getId());
            }
        }
        Log.temp.error("===本日战斗完毕， {}", guildIdAddScoreAllMap);
    }

    private void _get_human_brief_result(Param results, Param context){
        HumanBrief humanBrief = results.get("humanBrief");
        if (humanBrief == null) {
            Log.temp.error("getGameHumanBrief error, results={}, context={}", results, context);
            return;
        }
        int season = context.getInt("season");
        int week = context.getInt("week");
        int score = context.getInt("score");
        long group = context.getLong("group");

        addTopGuildLog(season, humanBrief.getGuildId(), humanBrief.getId(), score,
                new BigDecimal(humanBrief.getTopCombat()).longValue(), week, group);
    }

    private void addTopGuildLog(int season, long guildId, long humanId, int score, long combat, int week, long group){
        if(humanId <= 0){
            return;
        }
        JSONObject jo = new JSONObject();
        jo.put("season", season);
        jo.put("humanId", humanId);
        jo.put("power", combat);
        jo.put("score", score);
        jo.put("round", week);

        JSONArray ja = Utils.toJSONArray(Utils.getRedisStrValue(RedisKeys.bridge_pvp_guild_top_log + guildId));
        ja.add(jo);
        if(ja.size() > ParamKey.guildTopLogNum){
            int num = ja.size() - ParamKey.guildTopLogNum;
            for(int i = 0; i < num; i++){
                ja.remove(0);
            }
        }
        CrossRedis.setAndExpire(RedisKeys.bridge_pvp_guild_top_log + group, ja.toJSONString(), Time.DAY_15_SEC);

    }

    private void initTtMatch(){
        long timeNow = Port.getTime();
        ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.familybattle_detail_time.SN);
        int[][] intArr = Utils.parseIntArray2(confGlobal.strValue);

        long weekTwoTime = Utils.getTimeOfWeek(timeNow, intArr[0][0], 0);
        long weekTwoTimeEnd = Utils.getTimeOfWeekSec(timeNow, intArr[0][0], intArr[0][1]);
        int hour = Utils.getHourOfTime(weekTwoTimeEnd) - Utils.getHourOfTime(weekTwoTime);
        if(hour > 1){
            weekTwoTime += Time.HOUR;// 固定一小时
        }
        if(weekTwoTime < timeNow && timeNow >= weekTwoTimeEnd){
            weekTwoTime += Time.WEEK;
        }
        long interval = weekTwoTime - timeNow;
        if(interval > 0){
            ttMatch.start(timeNow, interval);
        }

        Log.temp.error("===timeNow={}, interval={}，匹配开始时间{}", timeNow, interval,
                Utils.formatTime(ttMatch.getNextTime(), "yyyy-MM-dd HH:mm:ss"),
                Utils.formatTime(weekTwoTime, "yyyy-MM-dd HH:mm:ss"),
                Utils.formatTime(weekTwoTimeEnd, "yyyy-MM-dd HH:mm:ss"));
    }

    @ScheduleMethod(DataResetService.CRON_DAY_HOUR)
    public void _CRON_DAY_HOUR() {
        checkStep();
    }

    @ScheduleMethod(DataResetService.CRON_DAY_ZERO)
    public void _CRON_DAY_ZERO() {
        battle = false;
        isMatch = false;
        isSettleDay = false;
        isWeekSend = false;
        isSeasonSend = false;
        isOpenGvg = false;
        battleIdex = 0;
        checkStep();
        matcher.settleGuildIdList.clear();
        matcher.clear();

    }

    private void checkStep(){
        long timeNow = Port.getTime();
        for(int i = 1; i <= ParamKey.guild_gvg_step_15; i++){
            long openTime = GuildLeagueUtils.getStepStartTime(i);
            long endTime = GuildLeagueUtils.getStepEndTime(i);
            if(openTime <= timeNow && timeNow < endTime){
                if(i != step){
                    updateSetp(i);
                    break;
                }
            }
        }
    }

    /**
     * 乱斗开战
     * <AUTHOR>
     * @Date 2024/7/12
     * @Param
     */
    private void guildBattle(){
        if(battle){
            return;
        }
        battle = true;
		isSettleDay = false;
        Log.temp.error("===公会乱斗开始");

		try {
            // 不战斗放到19点处理
//        	matcher.guildBattleStart(guildRecordMap);
			// 处理轮空公会
            matcher.guildBattleStartBye(guildRecordMap);
			if (!ttSecBattle.isStarted()) {
                ttSecBattle.start(Time.MIN);
            }
		} catch (Exception e) {
            Log.temp.error("===公会乱斗异常", e);
        }
    }

    private void ttBattleReset(){
        long interval = 0;
        int week = Utils.getDayOfWeek() - 1;
        long timeNow = Port.getTime();
        if(week <= 3 && week != 0){
            long openTime = GuildManager.inst().getThreeBattleTime();
            if(openTime <= timeNow){
                openTime = GuildManager.inst().getFourBattleTime();
            }
            interval = openTime - timeNow;
        } else if(week == 4){
            long openTime = GuildManager.inst().getFourBattleTime();
            if(openTime <= timeNow){
                openTime = GuildManager.inst().getFiveBattleTime();
            }
            interval = openTime - timeNow;
        } else if(week >= 5 || week == 0){
            long openTime = GuildManager.inst().getFiveBattleTime();
            if(openTime <= timeNow){
                openTime = GuildManager.inst().getThreeBattleTime() + Time.WEEK - Port.getTime();
            }
            interval = openTime - timeNow;
        }
        if(interval <= 0){
            interval = GuildManager.inst().getBattleTime() - timeNow;
        }

        if(interval > 0){
            ttBattle.start(interval);
        }
        Log.temp.error("===timeNow={}, interval={}，开始战斗时间{}", timeNow, interval, Utils.formatTime(ttBattle.getNextTime(), "yyyy-MM-dd HH:mm:ss"));
    }


    private void match(){
        if(isMatch){
            return;
        }
        isMatch = true;

        Log.temp.error("===公会匹配开始, 当前匹配={}, 下次匹配={}", Utils.formatTime(ttMatch.getStartTime(), "yyyy-MM-dd HH:mm:ss"),
                Utils.formatTime(ttMatch.getNextTime(), "yyyy-MM-dd HH:mm:ss"));
        bridgeMatch();
        ttBattleReset();
    }

    public List<Long> guildIdListWarmUp(int type){
        List<Long> guildIdList = new ArrayList<>();

        int serverId = 0;// TODO
//        for(){}
        Map<Integer, Map<Integer, List<Long>>> map = register.getServerIdGradeGuildIdListMap();
        Map<Integer, List<Long>> typeListMap = map.get(serverId);
        List<Long> guildIdListNew = typeListMap.get(type);
        guildIdList.addAll(guildIdListNew);

        return guildIdList;
    }

    // 本服匹配
    private void matchWarmUp(){
        Map<Integer, Map<Integer, List<Long>>> serverIdGradeGuildIdListMap = register.getServerIdGradeGuildIdListMap();
        if(serverIdGradeGuildIdListMap == null){
            return;
        }
        for(Map.Entry<Integer, Map<Integer, List<Long>>> entryMap : serverIdGradeGuildIdListMap.entrySet()) {
            int serverId = entryMap.getKey();
            GuildLeagueSeasonWarmUp season = guildSeasonWarmUpMap.get(serverId);
            Map<Integer, List<Long>> gradeIdListMap = entryMap.getValue();
            for (Map.Entry<Integer, List<Long>> mapEntry : gradeIdListMap.entrySet()) {
                Integer key = mapEntry.getKey();//段位
                List<Long> value = new ArrayList<>(mapEntry.getValue());
                Collections.shuffle(value);

                matcher.matchWarmUp(season.getSeason(), season.getWeek(), key, value, serverId);
            }
        }
    }


    // 跨服匹配
    private void bridgeMatch(){

        matcher.matchGuildIdList.clear();
        Map<Integer, Map<Integer, List<Long>>> groupGradeGuildIdListMap = register.getGroupGradeGuildIdListMap();
        if(groupGradeGuildIdListMap == null){
            return;
        }
        Log.temp.info("===match, groupGradeGuildIdListMap={}", groupGradeGuildIdListMap);
        for(Map.Entry<Integer, Map<Integer, List<Long>>> entryMap : groupGradeGuildIdListMap.entrySet()) {
            try {
                int group = entryMap.getKey();
                GuildLeagueSeason season = guildSeasonMap.get(group);
                if(season == null){
                    Log.temp.error("===season=null , group={}", group);
                    season = GuildManager.inst().createGuildLeagueSeason(group);
                    guildSeasonMap.put(group, season);
                    if(season == null){
                        continue;
                    }
                }
                checkSeason(season);
                Map<Integer, List<Long>> gradeIdListMap = entryMap.getValue();

                for (int grade = GuildParamKey.gradeMax; grade > 0; grade--) {
                    if (grade == 0) {
                        continue;
                    }
                    try {
                        matcher.matchBridgeGuild(group, season.getSeason(), season.getWeek(), grade, guildSoreList(gradeIdListMap.getOrDefault(grade, new ArrayList<>())));
                    } catch (Exception e) {
                        Log.temp.error("===bridgeMatch error, group={}, serverIdGradeGuildIdListMap={}", group, gradeIdListMap, e);
                    }
                }

            } catch (Exception e) {
                Log.temp.error("===bridgeMatch error, group={}, serverIdGradeGuildIdListMap={}", entryMap.getKey(), entryMap.getValue(), e);
            }
        }
        matcher.matchGuildIdList.clear();
        Log.temp.info("===匹配结束,num={}", matcher.pairAllList.size());
    }

    //积分排序
    private List<Long> guildSoreList(List<Long> guildIdList) {
        try{
            List<Long> removeIdList = new ArrayList<>();
            if(guildIdList != null && guildIdList.size() > 1){
                guildIdList.sort((guildId1, guildId2) -> {
                    int ret = 0;// 0默认相等
                    if(guildRecordMap.get(guildId2) == null){
                        removeIdList.add(guildId2);
                        return -1;
                    }
                    if(guildRecordMap.get(guildId1) == null){
                        removeIdList.add(guildId1);
                        return -1;
                    }
                    if (guildRecordMap.get(guildId2) != null && guildRecordMap.get(guildId1) != null) {
                        if (guildRecordMap.get(guildId2).getScore() < guildRecordMap.get(guildId1).getScore()) {
                            ret = -1;
                        } else if (guildRecordMap.get(guildId2).getScore() > guildRecordMap.get(guildId1).getScore()) {
                            ret = 1;
                        }
                    }
                    return ret;
                });
            }
            if(!removeIdList.isEmpty()){
                Log.temp.info("===移除不存在的工会{}", removeIdList);
                guildIdList.removeAll(removeIdList);
            }
        } catch (Exception e){
            Log.temp.error("排序出问题了，", e);
        }
        return guildIdList;
    }

    @DistrMethod
    public void addGuildScore(Map<Long, Integer> guildIdAddScoreMap){
        long timeWeekOne = Utils.getTimeOfWeek(Port.getTime(), 1, 0);
        String date = Utils.formatTime(timeWeekOne, "yyyy-MM-dd");

        for(Map.Entry<Long, Integer> entry : guildIdAddScoreMap.entrySet()){
            GuildLeagueRecord guildData = guildRecordMap.get(entry.getKey());
            int score = guildData.getScore() + entry.getValue();
            guildData.setScore(score);

//            // 更新本周数据
//            updateDatePvpGroupGuildRank(guildData.getGroupPvp(), date, score, guildData.getGuildId(), guildData.getGrade(), matcher.getMacthId(guildData.getGuildId()));
//            // 总榜加入排行
//            updatePvpGroupGuildRank(guildData.getGroupPvp(), score, guildData.getGuildId());
        }
    }



    private void updatePvpGroupGuildRank(int group, int score, long guildId){
        String key = RedisKeys.bridge_pvp_group_guild_rank + group;
        List<String> keyList = new ArrayList<>();
        keyList.add(key);
        keyList.add(String.valueOf(score));
        keyList.add(String.valueOf(guildId));
        EntityManager.redisClient.zadd(keyList, r->{
            if(!r.succeeded()){
                Log.temp.error("===保存失败， key={}, score={}, guildId={}", key, score, guildId);
            }
        });
    }

    /**
     * 报名
     * <AUTHOR>
     * @Date 2024/7/11
     * @Param
     */
    @DistrMethod
    public void guildBattleRegister(List<LeagueMatchGroup> dataList) {
        for (LeagueMatchGroup group : dataList) {
            checkLeagueMatchGroup(group);
            register.register(group);
            Log.temp.error("报名数据，{},{}， groupPvp={}, group={}",group.guildId, group.name, group.groupPvp, group.grade);
        }
        Log.temp.error("--------------报名完成-------------");
    }

    @DistrMethod
    public void removeGuild(long guildId){
        boolean b = guildRecordMap.containsKey(guildId);
        if (!b){
            return ;
        }

        //移除报名信息
        GuildLeagueRecord info = guildRecordMap.get(guildId);
        int grade = info.getGrade();
        int groupPvp = info.getGroupPvp();
        register.removeGuild(info.getServerId(), guildId, grade);
        register.removeRegister(info.getSeason(), guildId);
        register.removeGroupGradeGuild(info.getGroupPvp(), guildId, grade);
        matcher.removeMatchGuild(guildId);
        info.remove();
        guildRecordMap.remove(guildId);

        long matchId = matcher.getMacthId(guildId);
        if(matchId > 0){
            CrossRedis.removeFromRank(RedisKeys.bridge_date_group_grade_guild_rank + matchId, guildId);
        }
        CrossRedis.removeFromRank(RedisKeys.bridge_pvp_group_guild_rank + groupPvp, guildId);

        Log.temp.error("===删除公会记录， guildId={}, season={}, serverId={}, grade={}", guildId, info.getSeason(), info.getServerId(), grade);
    }

    private void checkLeagueMatchGroup(LeagueMatchGroup group){
        try{
            int groupIndex = GlobalConfVal.getCrossServerIdGroup(CrossType.cross_chaos_battle.getType(),group.serverId);
            Log.temp.error("--------------{}-------{}----{}-", groupIndex, group.serverId, group.guildId);
            GuildLeagueSeason season = guildSeasonMap.get(groupIndex);
            if(season != null){
                group.season = season.getSeason();
            }
            if(group.groupPvp != groupIndex){
                group.groupPvp = groupIndex;
            }
            GuildLeagueRecord guildData = guildRecordMap.get(group.guildId);
            if(guildData != null){
                if(guildData.getGroupPvp() != group.groupPvp){
                    guildData.setGroupPvp(group.groupPvp);
                }
                if(group.grade != guildData.getGrade()){
                    group.grade = guildData.getGrade();
                    Log.temp.info("===guildid={}, name={},, grade={}", group.guildId, group.name, group.grade);
                }
                if(group.leaderId > 0 && group.leaderId != guildData.getLeaderId()){
                    guildData.setLeaderId(group.leaderId);
                }
                if(!group.leaderName.isEmpty() && !group.leaderName.equals(guildData.getLeaderName())){
                    guildData.setLeaderName(Utils.removeEmojis(group.leaderName));
                }
                if(!group.name.isEmpty() && !group.name.equals(guildData.getGuildName())){
                    guildData.setGuildName(Utils.removeEmojis(group.name));
                }
            }
            if(!guildRecordMap.containsKey(group.guildId) || guildData == null){
                createGuildLeagueRecord(group);
            }
        } catch (Exception e){
            Log.temp.info("===checkLeagueMatchGroup error, group={}", group, e);
        }

    }

    private void createGuildLeagueRecord(LeagueMatchGroup group){
        GuildLeagueRecord guildData = guildRecordMap.get(group.guildId);
        if(guildData != null){
            return;
        }
        guildData = new GuildLeagueRecord();
        guildData.setId(group.guildId);
        guildData.setGuildId(group.guildId);
        guildData.setGroupPvp(group.groupPvp);
        guildData.setSeason(group.season);
        guildData.setServerId(group.serverId);
        guildData.setGrade(group.grade);
        guildData.persist();
        guildRecordMap.put(group.guildId, guildData);
        guildData.setGuildName(Utils.removeEmojis(group.name));
        if(group.leaderId > 0){
            guildData.setLeaderId(group.leaderId);
            guildData.setLeaderName(Utils.removeEmojis(group.leaderName));
        }

        guildData.setFlagJSON(group.flagJSON);
    }

    @DistrMethod
    public void udpateLeagueGroup(int serverId, int groupIndex){
        for(GuildLeagueRecord guildData : guildRecordMap.values()){
            if(serverId == guildData.getServerId()){
                guildData.setGroupPvp(groupIndex);
            }
        }
    }

    @DistrMethod
    public void gvgSelectRoad(long guildId, long humanId, int road){
        if(step != ParamKey.guild_gvg_step_3 && step != ParamKey.guild_gvg_step_4 && step != ParamKey.guild_gvg_step_8 && step != ParamKey.guild_gvg_step_12){
            port.returns("result", false);
            return;
        }
        List<Define.p_gvg_road> roadInfoList = new ArrayList<>();
        List<Long> humanIdList= matcher.gvgSelectRoad(guildId, humanId, road, roadInfoList);

        long pid = port.createReturnAsync();
        CrossHumanLoader.getList(humanIdList, HumanBriefLoadType.SHOW_INFO, res -> {
            if(res.failed()){
				port.returnsAsync(pid, "roadInfoList", roadInfoList);
                return;
            }
            List<HumanBrief> humanBriefList = res.result();
            Map<Long, HumanBrief> briefMap = new HashMap<>();
            for(HumanBrief humanBrief : humanBriefList){
                if(humanBrief == null){
                    continue;
                }
                briefMap.put(humanBrief.getId(), humanBrief);
            }
            MsgGvg.gvg_select_road_s2c.Builder msg = MsgGvg.gvg_select_road_s2c.newBuilder();
            int index = 1;
            msg.setRoad(road);
            for(long id : humanIdList){
                Define.p_gvg_player info = GuildManager.inst().to_p_gvg_player(briefMap.get(id), index);
                if(info != null){
                    msg.addPlayerList(info);
                }
                index++;
            }
            port.returnsAsync(pid, "msg", msg.build(), "roadInfoList", roadInfoList);
        });

    }



    @DistrMethod
    public void gvgRoad(long guildId, int type, int road){
        List<Long> humanIdList= matcher.gvgRoadHumanIdList(guildId, type, road);
        List<Define.p_gvg_road> roadInfoList = matcher.get_p_gvg_road(guildId, 1, 2, 3);

        long pid = port.createReturnAsync();

        MsgGvg.gvg_road_info_s2c.Builder msg = MsgGvg.gvg_road_info_s2c.newBuilder();
        msg.setType(type);
        msg.setRoad(road);
        CrossHumanLoader.getList(humanIdList, HumanBriefLoadType.SHOW_INFO, res -> {
            if(res.failed()){
                port.returnsAsync(pid, "roadInfoList", roadInfoList);
                return;
            }
            List<HumanBrief> humanBriefList = res.result();
            Map<Long, HumanBrief> briefMap = new HashMap<>();
            int serverId = 0;
            for(HumanBrief humanBrief : humanBriefList){
                if(humanBrief == null){
                    continue;
                }
                briefMap.put(humanBrief.getId(), humanBrief);
                if(serverId == 0){
                    serverId = humanBrief.getServerId();
                }
            }
            int index = 1;
            Set<Long> set = new HashSet<>();
            List<Long> syncIdList = new ArrayList<>();
            for(long id : humanIdList){
                if(!set.add(id)){
                    Log.temp.error("===重复的id={}, set={}, humanIdList={}", id, set, humanIdList);
                    continue;
                }
                Define.p_gvg_player info = GuildManager.inst().to_p_gvg_player(briefMap.get(id), index);
                if(info != null){
                    msg.addPlayerList(info);
                } else {
                    syncIdList.add(id);
                }
                index++;
            }
            if(!syncIdList.isEmpty()){
                GuildManager.inst().syncToGameServer(syncIdList, serverId);
            }
            port.returnsAsync(pid, "msg", msg.build(), "roadInfoList", roadInfoList);
        });
    }

    @DistrMethod
    public void gvgRoadChangeAll(long guildId, int road, int road1, long humanId){
        Map<Integer, List<Long>> roadMap = matcher.gvgRoadChangeAll(guildId, road, road1);
        List<Define.p_gvg_road> roadInfoList = matcher.get_p_gvg_road(guildId, 1, 2, 3);

        MsgGvg.gvg_road_change_all_s2c.Builder msg = MsgGvg.gvg_road_change_all_s2c.newBuilder();

        List<Long> humanIdList1 = roadMap.getOrDefault(GuildParamKey.road1, new ArrayList<>());
        List<Long> humanIdList2 = roadMap.getOrDefault(GuildParamKey.road2, new ArrayList<>());
        List<Long> humanIdList3 = roadMap.getOrDefault(GuildParamKey.road3, new ArrayList<>());
        List<Long> humanIdAllList =  new ArrayList<>();
        humanIdAllList.addAll(humanIdList1);
        humanIdAllList.addAll(humanIdList2);
        humanIdAllList.addAll(humanIdList3);
        long pid = port.createReturnAsync();
        if(!humanIdAllList.isEmpty()){
            CrossHumanLoader.getList(humanIdAllList, HumanBriefLoadType.SHOW_INFO, ret->{
                if(ret.failed()){
					port.returns("roadInfoList", roadInfoList);
                    return;
                }
                List<HumanBrief> briefList = ret.result();
                Map<Long, HumanBrief> briefMap = new HashMap<>();
                for(HumanBrief brief : briefList){
                    if(brief == null){
                        continue;
                    }
                    briefMap.put(brief.getId(), brief);
                }
                MsgGvg.gvg_select_road_s2c.Builder selectMsg = MsgGvg.gvg_select_road_s2c.newBuilder();
                for(Map.Entry<Integer, List<Long>> entry : roadMap.entrySet()){
                    Define.p_gvg_road_info.Builder roadInfo = Define.p_gvg_road_info.newBuilder();
                    roadInfo.setRoad(entry.getKey());
                    List<Long> humanIdList = entry.getValue();
                    int index = 0;
                    boolean isMy = humanIdList.contains(humanId);
                    if(isMy){
                        selectMsg.setRoad(entry.getKey());
                    }
                    for(long id : humanIdList){
                        index ++;
                        Define.p_gvg_player info = GuildManager.inst().to_p_gvg_player(briefMap.get(id), index);
                        if(info != null){
                            roadInfo.addPlayerList(info);
                        }
                        if(isMy) {
                            selectMsg.addPlayerList(info);
                        }
                    }
                    roadInfo.setNum(humanIdList.size());
                    msg.addRoadInfoList(roadInfo);
                }
                port.returnsAsync(pid, "msg", msg.build(), "selectMsg", selectMsg.build(), "roadInfoList", roadInfoList);
            });
            return;
        }
        port.returns("roadInfoList", roadInfoList);
    }

    @DistrMethod
    public void gvgRoadChange(long guildId, int road, List<Define.p_key_value> roadInfoList){
        List<Long> humanIdList=  matcher.gvgRoadChange(guildId, road, roadInfoList);
        Define.p_gvg_road roadInfo = matcher.get_p_gvg_road(guildId, road);

        long pid = port.createReturnAsync();
        MsgGvg.gvg_road_change_s2c.Builder msg = MsgGvg.gvg_road_change_s2c.newBuilder();
        msg.setRoad(road);
        CrossHumanLoader.getList(humanIdList, HumanBriefLoadType.SHOW_INFO, res -> {
            if(res.failed()){
                port.returnsAsync(pid, "roadInfo", roadInfo);
                return;
            }
            List<HumanBrief> humanBriefList = res.result();
            Map<Long, HumanBrief> briefMap = new HashMap<>();
            List<Long> loadIdList = new ArrayList<>();
            int indexPos = -1;
            for(HumanBrief humanBrief : humanBriefList){
                indexPos++;
                if(humanBrief == null){
                    loadIdList.add(humanIdList.get(indexPos));
                    continue;
                }
                briefMap.put(humanBrief.getId(), humanBrief);
            }
            int index = 1;
            for(long id : humanIdList){
                Define.p_gvg_player info = GuildManager.inst().to_p_gvg_player(briefMap.get(id), index);
                if(info != null){
                    msg.addPlayerList(info);
                }
                index++;
            }
            port.returnsAsync(pid, "msg", msg.build(), "roadInfo", roadInfo);

            if(!loadIdList.isEmpty()){
                EntityManager.batchGetEntity(HumanBrief.class, loadIdList, res3 -> {});
            }
        });


    }

    // 战斗回放
    @DistrMethod
    public void gvgPlayVideo(long guildId, long vid, int source){
        matcher.gvgPlayVideo(guildId, vid, source);
    }

    // 进入分路战斗
    @DistrMethod
    public void gvgFightInfo(long guildId, int road){
        matcher.gvgFightInfo(guildId, road);
        List<Long> humanIdMyList= matcher.gvgRoadHumanIdList(guildId, GuildParamKey.guild_gvg_type_1, road);
        List<Long> humanIdEnemyList= matcher.gvgRoadHumanIdList(guildId, GuildParamKey.guild_gvg_type_2, road);
        port.returns("humanIdMyList", humanIdMyList, "humanIdEnemyList", humanIdEnemyList);
    }

    @DistrMethod
    public void gvgFightResult(long guildId, int road){
        long id = 0;
        long idEnemy = 0;
        int index=0;
        int indexEnemy=0;

        if(!ttSecBattle.isStarted()){
            port.returns("msg", null);
            return;
        }

        int indexPos = matcher.guildIdIndexMap.getOrDefault(guildId, -1);
        List<GuildGvgBattleCross> battleList = matcher.getBattleAllList();
        if(indexPos >= 0 && battleList.size() > indexPos){
            GuildGvgBattleCross battle = battleList.get(indexPos);
            if(battle.guildIdA == guildId){
                if(id == 0 || idEnemy == 0){
                    Queue<Long> idListA = battle.roadHumanListBattleA.computeIfAbsent(road, k->new ArrayDeque<>());
                    Queue<Long> idListB = battle.roadHumanListBattleB.computeIfAbsent(road, k->new ArrayDeque<>());
                    if(!idListA.isEmpty()){
                        id = idListA.peek();
                    }
                    if(!idListB.isEmpty()){
                        idEnemy = idListB.peek();
                    }
                }
            } else if(guildId == battle.guildIdB){
                if(id == 0 || idEnemy == 0){
                    Queue<Long> idListA = battle.roadHumanListBattleA.computeIfAbsent(road, k->new ArrayDeque<>());
                    Queue<Long> idListB = battle.roadHumanListBattleB.computeIfAbsent(road, k->new ArrayDeque<>());
                    if(!idListA.isEmpty()){
                        idEnemy = idListA.peek();
                    }
                    if(!idListB.isEmpty()){
                        id = idListB.peek();
                    }
                }
            }
        }
        if(id == 0 || idEnemy == 0){
            port.returns("msg", null);
            return;
        }

        long pid = port.createReturnAsync();
        MsgGvg.gvg_fight_info_s2c.Builder msg = MsgGvg.gvg_fight_info_s2c.newBuilder();
        int endTime = (int)(Utils.getDayTime(Port.getTime(),  ParamKey.guildLeagueOpen20, battleIdex + 1, 0) / Time.SEC);
        msg.setEndTime(endTime);
        List<Long> humanIdList = new ArrayList<>();
        humanIdList.add(id);
        humanIdList.add(idEnemy);
        final long humanIdMy = id;
        final long humanIdEnemy = idEnemy;
        CrossHumanLoader.getList(humanIdList, HumanBriefLoadType.SHOW_INFO, ret->{
            if(ret.failed()){
                return;
            }
            List<HumanBrief> briefList = ret.result();
            for(HumanBrief brief : briefList){
                if(brief == null){
                    continue;
                }
                if(brief.getId() == humanIdMy){
                    Define.p_gvg_player info = GuildManager.inst().to_p_gvg_player(brief, index);
                    if(info != null){
                        msg.setPlayer(info);
                    }
                } else if(brief.getId() == humanIdEnemy){
                    Define.p_gvg_player infoEnemy =  GuildManager.inst().to_p_gvg_player(brief, indexEnemy);
                    if(infoEnemy != null){
                        msg.setEnemyPlayer(infoEnemy);
                    }
                }
            }
            port.returnsAsync(pid, "msg", msg.build());
        });

    }

    private long checkRivalId(long rivalId, long guildId, GuildLeagueMatch match, int round){
        if(match == null){
            Log.temp.error("===match is null, rivalId={}, guildId={}", rivalId, guildId);
            return rivalId;
        }
        GuildLeagueRecord rivalData = guildRecordMap.get(rivalId);
        if(rivalData == null){
            Log.temp.error("===rivalData is null, rivalId={}, guildId={}, matchId={}", rivalId, guildId, match.getId());
            matcher.addMatch(match);
            long oldRivalId = rivalId;
            rivalId = matcher.getRivalId(round, guildId);
            if(oldRivalId != rivalId){
                Log.temp.error("===rivalId change, oldRivalId={}, rivalId={}", oldRivalId, rivalId);
            } else {
                Map<Integer, Map<Long, Long>> battleNumGuildIdMap = Utils.jsonToIntMapLongLong(match.getBattleNumGuildIdMap());
                Map<Long, Long> guildGuildIdMap = battleNumGuildIdMap.getOrDefault(round, new HashMap<>());
                if(guildGuildIdMap.containsKey(guildId)){
                    return guildGuildIdMap.get(guildId);
                } else {
                    for(Map.Entry<Long,Long> entry : guildGuildIdMap.entrySet()){
                        if(entry.getValue() == guildId){
                            return entry.getKey();
                        }
                    }
                }
            }
        }
        return rivalId;
    }

    @DistrMethod
    public void gvgInfo(long guildId, long humanId, int serverId){
        Log.temp.debug("===step={}", step);
        MsgGvg.gvg_info_s2c.Builder dInfo = MsgGvg.gvg_info_s2c.newBuilder();
        dInfo.addAllTimeList(GuildLeagueUtils.getGVGTime());

        int groupIndex = GlobalConfVal.getCrossServerIdGroup(CrossType.cross_chaos_battle.getType(), serverId);
        GuildLeagueSeason season = guildSeasonMap.get(groupIndex);
        if(season == null){
            null_gvg_info_s2c(step, 1);
            return;
        }
        if(season.getStep() == ParamKey.guild_gvg_step_0 || step == ParamKey.guild_gvg_step_0){
            checkStep();
        }

        GuildLeagueRecord guildData = guildRecordMap.get(guildId);
        if(guildData == null){
            null_gvg_info_s2c(step, season.getSeason());
            return;
        }

        int round = GuildManager.inst().guildLeagueRound();
        long rivalId = matcher.getRivalId(round, guildId);

        Map<Integer, List<Long>> mapRivalId = matcher.getRoadHumanIdListMap(rivalId);
        Map<Integer, List<Long>> map = matcher.getRoadHumanIdListMap(guildId);
        Define.p_gvg_map.Builder mapInfo =  Define.p_gvg_map.newBuilder();
        int roadMy = 0;

        long matchId = matcher.getMacthId(guildId);
        GuildLeagueMatch match = matcher.matchMap.get(matchId);
        if(rivalId > 0 ){
            rivalId = checkRivalId(rivalId, guildId, match, round);
        }

        boolean isEnd = false;
        int roundBattle = GuildManager.inst().guildLeagueRound();
        if(step == ParamKey.guild_gvg_step_5 || step == ParamKey.guild_gvg_step_10 || step == ParamKey.guild_gvg_step_14){
            mapInfo.setStatus(GuildParamKey.roadTwo);
            int index = matcher.guildIdIndexMap.getOrDefault(guildId, -1);
            List<GuildGvgBattleCross> battleList = matcher.getBattleAllList();
            if(index >= 0 && battleList.size() > index){
                for(int road = 1; road <= 3; road++){
                    Define.p_gvg_road.Builder roadInfo = Define.p_gvg_road.newBuilder();
                    roadInfo.setStatus(GuildParamKey.roadTwo);// 默认
                    GuildGvgBattleCross battle = battleList.get(index);
                    int num = 0;
                    int numEnemy = 0;
                    if(battle.guildIdA == guildId){
                        num = battle.roadHumanListBattleA.computeIfAbsent(road, k->new ArrayDeque<>()).size();
                        numEnemy = battle.roadHumanListBattleB.computeIfAbsent(road, k->new ArrayDeque<>()).size();
                        if(battle.roadHumanListMapA.getOrDefault(road, new ArrayList<>()).contains(humanId)){
                            roadMy = road;
                        }
                    } else if(battle.guildIdB == guildId){
                        numEnemy = battle.roadHumanListBattleA.computeIfAbsent(road, k->new ArrayDeque<>()).size();
                        num = battle.roadHumanListBattleB.computeIfAbsent(road, k->new ArrayDeque<>()).size();
                        if(battle.roadHumanListMapB.getOrDefault(road, new ArrayList<>()).contains(humanId)){
                            roadMy = road;
                        }
                    }
                    roadInfo.setRoad(road);
                    // 战斗期间如果到0一个默认1人（由于战斗拉人会先移除胜利后加回去）
                    roadInfo.setNum(num <= 0 ? 1 : num);
                    roadInfo.setEnemyNum(numEnemy <= 0 ? 1 : numEnemy);

                    if(battle.roadWinGuildIdMap.containsKey(road)){
                        long winId = battle.roadWinGuildIdMap.get(road);
                        if(winId == guildId || winId == rivalId){
                            roadInfo.setStatus(winId == guildId ? GuildParamKey.roadWin : GuildParamKey.roadLose);// 有结果
                        }
                    }

                    Map<Long, Map<Integer, Integer>> idMap = matcher.roundGuildRoadStatusMap.getOrDefault(roundBattle, new HashMap<>());
                    Map<Integer, Integer> roadStatusMap = idMap.getOrDefault(guildId, new HashMap<>());
                    if(roadStatusMap.containsKey(road)){
                        roadInfo.setStatus(Utils.intValue(roadStatusMap.get(road)));
                    }
                    mapInfo.addRoadList(roadInfo);
                    isEnd = battle.winGuildId > 0;
                }
            }
        } else if(step == ParamKey.guild_gvg_step_6 || step == ParamKey.guild_gvg_step_11 || step == ParamKey.guild_gvg_step_15){
            if(match != null){
                Map<Integer,Map<Long, Map<Integer, Integer>>> roundGuildIdRoadMap = Utils.jsonToIntLongMapIntInt(match.getBattleNumGuildIdRoadMap());
                Map<Long, Map<Integer, Integer>> idMap = roundGuildIdRoadMap.getOrDefault(roundBattle, new HashMap<>());
                Map<Integer, Integer> roadStatusMap = idMap.getOrDefault(guildId, new HashMap<>());
                int winNum = 0;
                int loseNum = 0;
                for(Map.Entry<Integer, Integer> entry : roadStatusMap.entrySet()){
                    int roadIndex = entry.getKey();
                    Define.p_gvg_road.Builder roadInfo = Define.p_gvg_road.newBuilder();
                    roadInfo.setRoad(roadIndex);
                    List<Long> humanIdList = map.get(roadIndex);
                    roadInfo.setNum(humanIdList.size());
                    roadInfo.setEnemyNum(0);
                    roadInfo.setStatus(entry.getValue());
                    mapInfo.addRoadList(roadInfo);
                    if(entry.getValue() == GuildParamKey.roadWin){
                        winNum ++;
                    } else {
                        loseNum++;
                    }
                }
                mapInfo.setStatus(winNum >= loseNum ? GuildParamKey.roadWin : GuildParamKey.roadLose);
            }
        } else {
            if(map != null){
                for(Map.Entry<Integer, List<Long>> entry : map.entrySet()){
                    int roadIndex = entry.getKey();
                    List<Long> humanIdList = entry.getValue();
                    if(humanIdList.contains(humanId)){
                        roadMy = roadIndex;
                    }
                    int enemyNum = mapRivalId.getOrDefault(roadIndex, Collections.emptyList()).size();

                    Define.p_gvg_road.Builder roadInfo = Define.p_gvg_road.newBuilder();
                    roadInfo.setRoad(roadIndex);
                    roadInfo.setNum(humanIdList.size());
                    roadInfo.setEnemyNum(enemyNum);
                    roadInfo.setStatus(matcher.getRoadStatus(round, guildId, roadIndex));
                    mapInfo.addRoadList(roadInfo);
                }
            }
        }
        if(match != null){
            Map<Integer, List<Long>> dayWinIdListMap = Utils.jsonToMapIntListLong(match.getBattleNumWinGuildIdMap());
            List<Long> winIdList = dayWinIdListMap.get(roundBattle);
            if(winIdList != null && winIdList.contains(guildId) || step == ParamKey.guild_gvg_step_6 || step == ParamKey.guild_gvg_step_11 || step == ParamKey.guild_gvg_step_15){
                mapInfo.setStatus(winIdList != null && winIdList.contains(guildId) ? GuildParamKey.roadWin : GuildParamKey.roadLose);
            }
        } else {
            mapInfo.setStatus(GuildParamKey.roadLose);
        }

        mapInfo.setGuildId(rivalId);
        GuildLeagueRecord rivalData = guildRecordMap.get(rivalId);
        if(rivalData != null){
            mapInfo.setServId(Utils.getServerIdTo(rivalData.getServerId()));
            mapInfo.setName(rivalData.getGuildName());
            dInfo.setIfJoin(1);// 0 无法参与本期家族乱斗，请等待下期 1  2 本期已轮空，请等待下期活动开启 3 家族未达到报名条件，本期无法参与
        } else {
            if(rivalId > 0){
                Log.temp.info("===guildId={}, rivalId={}, matchId={}", guildId, rivalId, matchId);
            }
            mapInfo.setServId(0);
            mapInfo.setName("");
            dInfo.setIfJoin(2);
        }



        dInfo.setStep(step);
        dInfo.setMapInfo(mapInfo);
        dInfo.setMyRoad(roadMy);
        dInfo.setIfCross(ParamKey.sourceKey_1);
        dInfo.setSeason(season.getSeason());
        dInfo.setRound(season.getWeek());
        if(step == ParamKey.guild_gvg_step_4 || step == ParamKey.guild_gvg_step_8 || step == ParamKey.guild_gvg_step_12){
            dInfo.setEndTime((int)(GuildLeagueUtils.getStepEndTime(step) / Time.SEC));
        } else {
            dInfo.setEndTime(0);
        }
        if(match != null) {
            dInfo.setRanking(match.getGradeType());
        } else {
            LeagueMatchGroup group =register.matchGroups.get(guildId);
            if(group != null){
                dInfo.setRanking(group.grade);
            } else {
                dInfo.setRanking(0);//青铜局
            }
        }
        if(Config.DATA_DEBUG){
//            Log.temp.info("===dInfo={}", dInfo.build());
        }

        GuildGvgBattleCross battle = matcher.getGuildBattle(guildId);
        if (isEnd && battle != null && battle.round == round) {
            List<Long> humanIdList = new ArrayList<>();
            Map<Long, Integer> idScoreMap = new HashMap<>();
            MsgGvg.gvg_fight_settlement_s2c msgTemp = matcher.getMsg_gvg_fight_settlement_s2c(guildId, humanId, humanIdList, idScoreMap);
            if(msgTemp== null){
                port.returns("dInfo", dInfo.build(), "isEnd", true, "settlementMsg", msgTemp);
                return;
            }
            long pid = port.createReturnAsync();
            MsgGvg.gvg_fight_settlement_s2c.Builder msg = msgTemp.toBuilder();
            if(!humanIdList.isEmpty() && msg != null){
                CrossHumanLoader.getList(humanIdList, HumanBriefLoadType.SHOW_INFO, ret2 -> {
                    if (ret2.failed()) {
                        Log.temp.error("===ret2.cause={}", ret2.cause());
                        port.returnsAsync(pid, "dInfo", dInfo.build(), "isEnd", true );
                        return;
                    }
                    List<HumanBrief> humanBriefList = ret2.result();
                    Map<Long, HumanBrief> humanBriefMap = new HashMap<>();
                    for (HumanBrief brief : humanBriefList) {
                        if (brief == null) {
                            continue;
                        }
                        humanBriefMap.put(brief.getId(), brief);
                    }
                    int rank = 1;
                    for(long id : humanIdList){
                        Define.p_gvg_player dplayer = GuildManager.inst().to_p_gvg_player(humanBriefMap.get(id), rank, idScoreMap.get(id));
                        if(dplayer != null){
                            msg.addTopThree(dplayer);
                            rank++;
                        }
                    }
                    port.returnsAsync(pid, "dInfo", dInfo.build(), "isEnd", true, "settlementMsg", msg.build());
                });
                return;
            }
            port.returns("dInfo", dInfo.build(), "isEnd", isEnd, "settlementMsg", msg != null ? msg.build() : null);
        } else{
            port.returns("dInfo", dInfo.build(), "isEnd", isEnd);
        }
    }


    private void null_gvg_info_s2c(int step, int season){
        MsgGvg.gvg_info_s2c.Builder dInfo = MsgGvg.gvg_info_s2c.newBuilder();
        dInfo.setSeason(season);
        dInfo.setStep(step);
        dInfo.setIfJoin(0);
        dInfo.setMyRoad(0);
        dInfo.setIfCross(ParamKey.sourceKey_1);
        dInfo.addAllTimeList(GuildLeagueUtils.getGVGTime());
        port.returns("dInfo", dInfo.build());
    }


    @DistrMethod
    public void seasonInfo(long guildId, long humanId, int serverId){
        int groupIndex = GlobalConfVal.getCrossServerIdGroup(CrossType.cross_chaos_battle.getType(), serverId);
        GuildLeagueSeason season = guildSeasonMap.get(groupIndex);
        if(season == null){
            port.returns("msg", null);
            return;
        }
        MsgGvg.gvg_season_info_s2c.Builder  msg = MsgGvg.gvg_season_info_s2c.newBuilder();
        msg.setSeason(season.getSeason());
        msg.setRound(season.getWeek());
        msg.setEndTime((int)(season.getEndTime() / Time.SEC));
        GuildLeagueRecord guildData = guildRecordMap.get(guildId);
        if(guildData == null){
            msg.setRanking(1);
            msg.setScore(0);
            msg.setRank(0);
            msg.setBattleTimes(0);
            msg.setWinTimes(0);
            port.returns("msg", msg.build());
            return;
        }

        int rank = Utils.getRedisRank(RedisKeys.bridge_pvp_group_guild_rank + guildData.getGroupPvp(), String.valueOf(guildId));
        int score = Utils.intValue(Utils.getRedisScore(RedisKeys.bridge_pvp_group_guild_rank + guildData.getGroupPvp(), String.valueOf(guildId)));
        msg.setRanking(guildData.getGrade());
        msg.setScore(guildData.getScore());
        msg.setRank(rank);
        msg.setBattleTimes(guildData.getEvents());
        msg.setWinTimes(guildData.getWinNum());

        port.returns("msg", msg.build());
    }


    @DistrMethod
    public void gm(int type, String json){
        Log.temp.info("gm type={}, json={}", type, json);
        checkStep();
        if(type == 1){
            isMatch = false;
            matcher = new LeagueMatcher();
            match();
        } else if(type == 2){
            Set<Integer> groupSet = new HashSet<>();

            String crossNodeId = D.NODE_CROSS_PREFIX+"0_"+(Config.SERVER_ID);

            String groupKey = CrossType.cross_chaos_battle.getType() + "_0";
            for(CrossGroupInfo info : GlobalConfVal.getCrossGroupSnInfoMap().values()) {
                if (!info.key.equals(groupKey)) {
                    continue;
                }
                for(Map.Entry<Integer, String> entry : info.groupAtCrossNodes.entrySet()){
                    if(crossNodeId.equals(entry.getValue())){
                        groupSet.add(entry.getKey());
                    }
                }
            }
            Log.temp.info("===加载乱斗组groupSet={}", groupSet);
            for(int group : groupSet){
                redisLoadData(group);
            }
        } else if(type == 3){
            battle = false;
            matcher.getBattleAllList().clear();
            matcher.addScoreIdList.clear();
            ttBattleReset();
            isOpenGvg = true;

            matcher.guildBattleGetHumanData(guildRecordMap);
            // 开始战斗
            guildBattle();
			ttSecBattle.start(Time.MIN);
        } else if(type == 4){
            gmLeagueAllClear();
        } else if(type == 5){
            initRedisInfo();
            initTtMatch();
            checkStep();
            checkGrade();
        } else if(type == 6){
            isSettleDay = false;
            matcher.settleGuildIdList.clear();
            int i = 0;
            while (!isSettleDay){
                boolean isEnd = matcher.pulseBattle(i);
                if(isEnd){
                    settleDay();
                }
                i++;
                if(i >= 1000){
                    break;
                }
            }
        } else if(type == 8){
            Log.temp.error("===测试tcp链接， league = {}", S.isCross);
            port.returns("ok");
            return;
        } else if(type == 9){
            isSeasonSend = false;
            isWeekSend = false;
            // 本周结算
            weekSettle();
            // 赛季结算
            seasonSettle();
        } else if(type == 10){
            if(ttBattleGetData == null){
                matcher.guildBattleGetHumanData(guildRecordMap);
                this.ttBattleGetData = new TickTimer(1 * Time.SEC);
            }
            this.ttBattleGetData = new TickTimer(1 * Time.SEC);
        } else if(type == 11){
            battle = false;
            isOpenGvg = true;
            matcher.guildBattleGetHumanData(guildRecordMap);
            // 开始战斗
            guildBattle();
            ttSecBattle.start(Time.MIN);
        } else if(type == 12){
            battle = false;
            isOpenGvg = true;
            // 开始战斗
            guildBattle();
            ttSecBattle.start(5 * Time.SEC);
        } else if(type == 13){
            matcher.guildBattleGetHumanData(guildRecordMap);
        } else if(type == 14){
            GuildServiceProxy proxy = GuildServiceProxy.newInstance(DistrKit.getWorldNodeID(30072));
            proxy.sendMsg_guild_schedule_s2c(new ArrayList(1), 1);
        } else if(type == 15){
            update2(new Param());
        } else if(type == 16){
            int num = 0;
            for(GuildLeagueRecord record : guildRecordMap.values()){
                record.setGrade(3);
                ++num;
                if(num >= 3){
                    break;
                }
            }
        } else if(type == 17){
            register = new GuildLeagueRegister();
        } else if(type == 18){
            matcher.settleGuildIdList.clear();
        } else if(type == 19){
            _CRON_WEEK_1_0();
        } else if(type == 20){
            for(GuildLeagueRecord record : guildRecordMap.values()){
                record.setScore( record.getScore() +1000);
                getMsg_gvg_settlement_week_s2c(record.getGuildId(), 1);
            }
        } else if(type == 21){
            JSONObject jo = Utils.toJSONObject(json);
            long guildId = jo.getLongValue("guildId");
            int score = jo.getIntValue("score");
            int season = jo.getIntValue("season");
            GuildLeagueRecord guildData = guildRecordMap.get(guildId);
            if(guildData == null){
                return;
            }
            guildData.setScore(score);
            guildData.setSeason(season);
        } else if(type == 22){
            isSettleDay = false;
            settleDay();
        } else if(type == 23){
            long humanIdB = 600220000005900716L;
            CrossHumanLoader.getHumanBrief(humanIdB, HumanBriefLoadType.BATTLE_INFO, ret2 -> {
                if (ret2.failed()) {
                    Log.game.error("加载玩家简要数据HumanBrief出错,humanId={}", humanIdB, ret2.cause());
                } else {
                    HumanBrief humanBriefData = ret2.result();
                    try {
                        if (humanBriefData != null && humanBriefData.getBattleRole() != null) {
                            Log.temp.info("===玩家简要数据成功，humanId={}", humanIdB);
                        } else {
                            Log.game.info("===玩家简要数据失败，humanBriefData=null，尝试回本服获取。id={}，PBattleRole={} PRoleFigure={}", humanIdB, humanBriefData.getBattleRole(), humanBriefData.getRoleFigure());
                            HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance(DistrKit.getWorldNodeID(Utils.getServerIdByHumanId(humanIdB)));
                            proxy.getHumanBrief(humanIdB);
                            proxy.listenResult(this::_get_human_brief_result);
                        }
                    } catch (Exception e) {
                        Log.temp.error("===加载玩家简要数据出错，humanId={}", humanIdB, e.getStackTrace());
                        HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance(DistrKit.getWorldNodeID(Utils.getServerIdByHumanId(humanIdB)));
                        proxy.getHumanBrief(humanIdB);
                        proxy.listenResult(this::_get_human_brief_result);
                    }
                }
            });
        }
    }

    @DistrMethod
    public void gm2(String json){
        JSONObject jo = Utils.toJSONObject(json);
        int type = jo.getIntValue("type");
        switch (type){
            case 1:
                long guildId = jo.getLongValue("guildId");
                GuildLeagueRecord guildData = guildRecordMap.get(guildId);
                if(guildData == null){
                    return;
                }
                int score = jo.getIntValue("score");
                guildData.setScore(score);
                getMsg_gvg_settlement_week_s2c(guildId, 1);
                break;
            case 2:// 本周4工会玩家积分排序
                guildId = jo.getLongValue("guildId");
                guildData = guildRecordMap.get(guildId);
                if(guildData == null){
                    return;
                }
                score = jo.getIntValue("score");
                long humanId = jo.getLongValue("humanId");
                String redisKey = RedisKeys.bridge_date_group_grade_human_rank + matcher.getMacthId(guildId);
                // 加入乱斗组本周（4个公会的玩家排行）
                CrossRedis.addScore(redisKey, humanId, score);
               break;
           case 3:// bridge_pvp_group_human_rank + group, score(玩家积分)，玩家id （赛季）
                guildId = jo.getLongValue("guildId");
                guildData = guildRecordMap.get(guildId);
                if(guildData == null){
                    return;
                }
                score = jo.getIntValue("score");
                humanId = jo.getLongValue("humanId");
                // 加入乱斗组个人排行榜
                CrossRedis.addScore(RedisKeys.bridge_pvp_group_human_rank + matcher.matchMap.get(matcher.getMacthId(guildId)).getGroupPvp(), humanId, score);

                break;
            case 4://乱斗组本周（4个公会排行）
                guildId = jo.getLongValue("guildId");
                guildData = guildRecordMap.get(guildId);
                if(guildData == null){
                    return;
                }
                score = jo.getIntValue("score");

                String redisKeyGuild = RedisKeys.bridge_date_group_grade_guild_rank + matcher.getMacthId(guildId);
                // 乱斗组本周（4个公会排行）
                CrossRedis.addScore(redisKeyGuild, guildId, score);
                break;
            case 5:// 同一个乱斗组排行（跨服1/本服所有公会）, score(工会积分)， 工会id
                guildId = jo.getLongValue("guildId");
                guildData = guildRecordMap.get(guildId);
                if(guildData == null){
                    return;
                }
                score = jo.getIntValue("score");
                // 同一个乱斗组排行（跨服1/本服所有公会）, score(工会积分)， 工会id
                CrossRedis.addScore(RedisKeys.bridge_pvp_group_guild_rank + matcher.matchMap.get(matcher.getMacthId(guildId)).getGroupPvp(), guildId, score);
                break;
        }
    }


    private void gmLeagueAllClear(){
        Log.temp.error("===乱斗删除所有数据");
        for(GuildLeagueSeason season : guildSeasonMap.values()){
            season.remove();
        }

        for(GuildLeagueRecord record : guildRecordMap.values()){
            record.remove();
        }
        register.removeAll();
        for(GuildLeagueHistory history : matcher.historyMap.values()){
            history.remove();
        }
        for(GuildLeagueMatch match : matcher.matchMap.values()){
            match.remove();
        }
        guildSeasonMap.clear();
        guildRecordMap.clear();
        register = new GuildLeagueRegister();
        matcher = new LeagueMatcher();

        initRedisInfo();
        initTtMatch();
        checkStep();
        checkGrade();
    }


    @DistrMethod
    public void getWeekRank(int type, int serverId, long guildId, long humanId){
        int groupIndex = GlobalConfVal.getCrossServerIdGroup(CrossType.cross_chaos_battle.getType(), serverId);
        GuildLeagueSeason season = guildSeasonMap.get(groupIndex);
        if(season == null){
            return;
        }
        long matchId = matcher.getMacthId(guildId);
        GuildLeagueMatch match = matcher.matchMap.get(matchId);
        if(match == null){
            return;
        }
        MsgGvg.gvg_week_rank_s2c.Builder msg = MsgGvg.gvg_week_rank_s2c.newBuilder();
        msg.setType(type);
        msg.setMyRankInfo(Define.p_rank_info.newBuilder());
        long pid = port.createReturnAsync();
        if(type == GuildParamKey.guild_rank_type_1){
            String redisKey = RedisKeys.bridge_date_group_grade_guild_rank + matchId;
            RedisTools.getRankListByIndex(EntityManager.redisClient, redisKey, 0, RankParamKey.pageNum - 1, true, f -> {
                if (f.failed()) {
                    port.returnsAsync(pid, "msg", msg.build());
                    return;
                }
                JsonArray json = f.result();
                int sumSize = json.size();
                int rank = 1;
                List<Define.p_rank_info> rankList = new ArrayList<>();
                Define.p_rank_info myRank = null;
                for (int i = 0; i < sumSize; i += 2) {
                    long guildIdTemp = Utils.longValue(json.getList().get(i));
                    int score = Utils.intValue(json.getList().get(i + 1));
                    GuildLeagueRecord guildData = guildRecordMap.get(guildIdTemp);
                    Define.p_rank_info rankInfo = GuildManager.inst().to_p_rank_info(guildId, guildData.getGuildName(), guildData.getServerId(), rank, score,
                            guildData.getLeaderId(), guildData.getLeaderName(), guildData.getFlagJSON());
                    rankList.add(rankInfo);
                    if (guildIdTemp == guildId) {
                        myRank = rankInfo;
                    }
                    ++rank;
                }
                if(myRank != null){
                    msg.addAllRankInfo(rankList);
                    msg.setMyRankInfo(myRank);
                    port.returnsAsync(pid, "msg", msg.build());
                    return;
                }

                RedisTools.getMyRank(EntityManager.redisClient, redisKey, String.valueOf(guildId), ret1 -> {
                    if(ret1.failed()){
                        Log.temp.error("获取排名失败={}", ret1.cause());
                        port.returnsAsync(pid, "msg", msg.build());
                        return;
                    }
                    int rankMy = Utils.intValue(ret1.result());
                    RedisTools.getMyScore(EntityManager.redisClient, redisKey, guildId, ret2 -> {
                        if(ret2.failed()){
                            Log.temp.error("获取排名积分={}", ret2.cause());
                            port.returnsAsync(pid, "msg", msg.build());
                            return;
                        }
                        int scoreMy = Utils.intValue(ret2.result());
                        GuildLeagueRecord guildData = guildRecordMap.get(guildId);
                        Define.p_rank_info rankInfo = GuildManager.inst().to_p_rank_info(guildId, guildData.getGuildName(), guildData.getServerId(), rankMy, scoreMy,
                                guildData.getLeaderId(), guildData.getLeaderName(), guildData.getFlagJSON());

                        msg.addAllRankInfo(rankList);
                        msg.setMyRankInfo(rankInfo);
                        port.returnsAsync(pid, "msg", msg.build());
                    });
                });
            });
        } else if(type == GuildParamKey.guild_rank_type_2){
            String redisKey =RedisKeys.bridge_date_group_grade_human_rank + match.getId();
            RedisTools.getRankListByIndex(EntityManager.redisClient, redisKey, 0, RankParamKey.pageNum - 1, true, f -> {
                if (f.failed()) {
                    port.returnsAsync(pid, "msg", msg.build());
                    return;
                }
                JsonArray json = f.result();
                int sumSize = json.size();
                List<Long> idList = new ArrayList<>();
                Map<Long, Integer> idScoreMap = new HashMap<>();
                Map<Long, Integer> idRankMap = new HashMap<>();
                int rank = 0;
                for(int i = 0; i < sumSize; i+=2) {
                    long humanIdTemp = Utils.longValue(json.getList().get(i));
                    int score = Utils.intValue(json.getList().get(i + 1));
                    idScoreMap.put(humanIdTemp, score);
                    idList.add(humanIdTemp);
                    rank++;
                    idRankMap.put(humanIdTemp, rank);
                }
                if(idList.isEmpty()){
                    port.returnsAsync(pid, "msg", msg.build());
                    return;
                }
                CrossHumanLoader.getList(idList, HumanBriefLoadType.BATTLE_INFO, ret2 -> {
                    if (ret2.failed()) {
                        Log.temp.error("===ret2.cause={}", ret2.cause());
                        port.returnsAsync(pid, "msg", msg.build());
                        return;
                    }
                    Define.p_rank_info myRank = null;
                    List<Define.p_rank_info> rankList = new ArrayList<>();
                    List<HumanBrief> humanBriefList = ret2.result();
                    for (HumanBrief brief : humanBriefList) {
                        if (brief == null) {
                            continue;
                        }
                        long id = brief.getId();
                        RankInfo rankInfo = new RankInfo();
                        Define.p_rank_info dInfo = rankInfo.toRankInfo(brief, idRankMap.getOrDefault(id, 0), idScoreMap.getOrDefault(id, 0));
                        rankList.add(dInfo);
                        if (id == humanId) {
                            myRank = dInfo;
                        }
                    }
                    msg.addAllRankInfo(rankList);
                    if (myRank != null) {
                        msg.setMyRankInfo(myRank);
                        port.returnsAsync(pid, "msg", msg.build());
                        return;
                    }
                    CrossHumanLoader.getHumanBrief(humanId, HumanBriefLoadType.BATTLE_INFO, ret3 -> {
                        if(ret3.failed()){
                            Log.temp.error("===获取玩家数据失败，humanId={}", humanId);
                            port.returnsAsync(pid, "msg", msg.build());
                            return;
                        }
                        HumanBrief brief = ret3.result();
                        if(brief == null){
                            Log.temp.error("===获取玩家数据失败，humanId={}", humanId);
                            port.returnsAsync(pid, "msg", msg.build());
                            return;
                        }
                        RedisTools.getMyRank(EntityManager.redisClient, redisKey, String.valueOf(humanId), ret4 -> {
                            if(ret4.failed()){
                                Log.temp.error("获取排名失败={}", ret4.cause());
                                port.returnsAsync(pid, "msg", msg.build());
                                return;
                            }
                            int rankMy = Utils.intValue(ret4.result());
                            RedisTools.getMyScore(EntityManager.redisClient, redisKey, humanId, ret5 -> {
                                if(ret5.failed()){
                                    Log.temp.error("获取排名积分={}", ret5.cause());
                                    port.returnsAsync(pid, "msg", msg.build());
                                    return;
                                }
                                int scoreMy = Utils.intValue(ret5.result());
                                if(brief != null){
                                    RankInfo rankInfo = new RankInfo();
                                    Define.p_rank_info dInfo = rankInfo.toRankInfo(brief, rankMy, scoreMy);
                                    msg.setMyRankInfo(dInfo);
                                }
                                port.returnsAsync(pid, "msg", msg.build());
                            });
                        });
                    });
                });
            });
        }
    }

    @DistrMethod
    public void getRankList(int type, int group, long tempId){
        List<Define.p_rank_info> rankList = new ArrayList<>();
        Define.p_rank_info myRank = null;
        if(type == GuildParamKey.guild_rank_type_1){
            List<String> strHumanList = Utils.getRedisZaddStrValueList(RedisKeys.bridge_pvp_group_human_rank + group, 0, -1, true);
            int rank = 0;
            for(int i = 0; i < strHumanList.size(); i+=2){
                ++rank;
                long humanIdTemp = Utils.longValue(strHumanList.get(i));
                int score = Utils.intValue(strHumanList.get(i+1));
                HumanData humanData = HumanData.getHumanData(humanIdTemp, Human.class, Human2.class);
                if(humanData == null){
                    continue;
                }
                RankInfo rankInfo = new RankInfo(humanData.human, humanData.human2, score);
                rankInfo.rank_info = rankInfo.rank_info.toBuilder().setRank(rank).setScore(score).build();
                rankList.add(rankInfo.rank_info);
                if(humanIdTemp == tempId){
                    myRank = rankInfo.rank_info;
                }
            }
        } else if(type == GuildParamKey.guild_rank_type_2){

            List<String> strGuildList = Utils.getRedisZaddStrValueList(RedisKeys.bridge_pvp_group_guild_rank + group, 0, -1, true);
            int rank = 1;
            for(int i = 0; i < strGuildList.size(); i+=2){
                long guildIdTemp = Utils.longValue(strGuildList.get(i));
                int score = Utils.intValue(strGuildList.get(i+1));
                GuildLeagueRecord guildData = guildRecordMap.get(guildIdTemp);
                rankList.add(GuildManager.inst().to_p_rank_info(guildData, rank, score));
                ++rank;
                if(guildIdTemp == tempId){
                    myRank = GuildManager.inst().to_p_rank_info(guildData, rank, score);
                }
            }
        }
        port.returns("rankList", rankList, "myRank", myRank);
    }

    @DistrMethod
    public void getGvgRankList(int grade, int groupIndex, long guildId){
        List<Define.p_gvg_guild> rankList = new ArrayList<>();
        if(guildId > 0){
            GuildLeagueRecord guildInfo = guildRecordMap.get(guildId);
            if(guildInfo != null){
                groupIndex = guildInfo.getGroupPvp();
            }
        }

        int sumNum = register.getGradeGuildNum(groupIndex, grade);
        long matchId = matcher.getMacthId(guildId);
        long pid = port.createReturnAsync();
        CrossRedis.getRankListByIndex(RedisKeys.bridge_date_group_grade_guild_rank + matchId, 0, -1, false, ret -> {
            if(ret.failed()){
                port.returnsAsync(pid, "rankList", rankList, "sumNum", sumNum);
                return;
            }
            JsonArray jsonArray = ret.result();
            int size = jsonArray.size();
            if (size == 0 || jsonArray.getList().isEmpty()) {
                port.returnsAsync(pid, "rankList", rankList, "sumNum", sumNum);
                return;
            }
            for(int i = 0; i < jsonArray.getList().size(); i++) {
                long guildIdTemp = Utils.longValue(jsonArray.getList().get(i));
                GuildLeagueRecord guildData = guildRecordMap.get(guildIdTemp);
                if (guildData == null) {
                    continue;
                }
                List<Define.p_key_value_string> keyList = new ArrayList<>();
                String flagJSON = guildData.getFlagJSON();
                if (!Utils.isEmptyJSONString(flagJSON)) {
                    keyList = GuildManager.inst().to_p_guild_flag(flagJSON);
                }
                rankList.add(GuildManager.inst().to_p_gvg_guild(guildData.getGuildId(), guildData.getGuildName(), 0, guildData.getServerId(), keyList));
            }
            port.returnsAsync(pid, "rankList", rankList, "sumNum", sumNum);
        });

    }

    @DistrMethod
    public void getGvgRankList(int groupIndex, long guildId){
        GuildLeagueRecord guildMy = guildRecordMap.get(guildId);
        int grade = 1;
        int myScore = 0;
        int serverId = 0;
        if(guildMy != null){
            grade = guildMy.getGrade();
            myScore = guildMy.getScore();
            serverId = guildMy.getServerId();
        }
        if(step <= ParamKey.guild_gvg_step_2){
            MsgGvg.gvg_rank_s2c.Builder msg = MsgGvg.gvg_rank_s2c.newBuilder();
            msg.setScore(myScore);
            msg.setRanking(grade);
            msg.addAllMatchList(new ArrayList<>());
            port.returns("msg", msg.build());
            return;
        }
        List<Define.p_gvg_guild> rankList = new ArrayList<>();

        long matchId = matcher.getMacthId(guildId);
//        List<String> strHumanList = Utils.getRedisZaddStrValueList(RedisKeys.bridge_date_group_grade_guild_rank + matchId, 0, -1, true);

        long pid = port.createReturnAsync();
        MsgGvg.gvg_rank_s2c.Builder msg = MsgGvg.gvg_rank_s2c.newBuilder();
        msg.setScore(myScore);
        msg.setRanking(grade);
        CrossRedis.getRankListByIndex(RedisKeys.bridge_date_group_grade_guild_rank + matchId, 0, -1, true, ret -> {
            if(ret.failed()){
                return;
            }
            JsonArray jsonArray = ret.result();
            int size = jsonArray.size();
            if (size == 0 || jsonArray.getList().isEmpty()) {
                return;
            }
            int rank = 0;
            int myScoreTemp = 0;
            for(int i = 0; i < jsonArray.getList().size(); i+=2) {
                ++rank;
                long guildIdTemp = Utils.longValue(jsonArray.getList().get(i));
                int score = Utils.intValue(jsonArray.getList().get(i + 1));
                if (guildIdTemp == guildId) {
                    myScoreTemp = score;
                }
                GuildLeagueRecord guildData = guildRecordMap.get(guildIdTemp);
                if (guildData == null) {
                    continue;
                }
                List<Define.p_key_value_string> keyList = new ArrayList<>();
                String flagJSON = guildData.getFlagJSON();
                if (!Utils.isEmptyJSONString(flagJSON)) {
                    keyList = GuildManager.inst().to_p_guild_flag(flagJSON);
                }
                rankList.add(GuildManager.inst().to_p_gvg_guild(guildData.getGuildId(), guildData.getGuildName(), score, guildData.getServerId(), keyList));
            }
            if(myScoreTemp > 0){
                msg.setScore(myScoreTemp);
            }
            msg.addAllMatchList(rankList);
            port.returnsAsync(pid, "msg", msg.build());
        });
    }

    @DistrMethod
    public void autoJoinHumanIdList(long guildId, List<Long> humanIdList){
        GuildLeagueRecord guildData = guildRecordMap.get(guildId);
        if(guildData == null){
            return;
        }
        long matchId = matcher.getMacthId(guildId);
        GuildLeagueMatch match = matcher.matchMap.get(matchId);
        if(match == null){
            return;
        }
        matcher.autoJoinHumanIdList(guildId, humanIdList);
        humanIdJoinSet.addAll(humanIdList);
        ttSecCheck.reStart();
    }
    @DistrMethod
    public void getMsg_gvg_fight_settlement_s2c(long guildId, long humanId){
        List<Long> humanIdList = new ArrayList<>();
        Map<Long, Integer> idScoreMap = new HashMap<>();
        long pid = port.createReturnAsync();
        MsgGvg.gvg_fight_settlement_s2c msgTemp = matcher.getMsg_gvg_fight_settlement_s2c(guildId, humanId, humanIdList, idScoreMap);
        if(msgTemp == null){
            port.returns("msg", msgTemp);
            return;
        }
        MsgGvg.gvg_fight_settlement_s2c.Builder msg = msgTemp.toBuilder();
        if(!humanIdList.isEmpty() && msg != null) {
            CrossHumanLoader.getList(humanIdList, HumanBriefLoadType.SHOW_INFO, ret2 -> {
                if (ret2.failed()) {
                    Log.temp.error("===ret2.cause={}", ret2.cause());
                    return;
                }
                List<HumanBrief> humanBriefList = ret2.result();
                Map<Long, HumanBrief> humanBriefMap = new HashMap<>();
                for (HumanBrief brief : humanBriefList) {
                    if (brief == null) {
                        continue;
                    }
                    humanBriefMap.put(brief.getId(), brief);
                }
                int rank = 1;
                for (long id : humanIdList) {
                    Define.p_gvg_player dplayer = GuildManager.inst().to_p_gvg_player(humanBriefMap.get(id), rank, idScoreMap.get(id));
                    if (dplayer != null) {
                        msg.addTopThree(dplayer);
                        rank++;
                    }
                }
                port.returnsAsync(pid, "msg", msg.build());
            });
            return;
        }
        port.returns("msg", msg.build());
    }

    @DistrMethod
    public void getMsg_gvg_settlement_week_s2c(long guildId, long humanId){
        GuildLeagueRecord guildData = guildRecordMap.get(guildId);
        if(guildData == null){
            port.returns("msgGuild", null);
            return;
        }

        int groupIndex = GlobalConfVal.getCrossServerIdGroup(CrossType.cross_chaos_battle.getType(), guildData.getServerId());
        GuildLeagueSeason season = guildSeasonMap.get(groupIndex);
        if(season == null){
            port.returns("msgGuild", null);
            return;
        }
        long matchId = matcher.getMacthId(guildId);
        GuildLeagueMatch match = matcher.matchMap.get(matchId);
        if(match == null){
            port.returns( "msgGuild", null);
            return;
        }

        int grade = match.getGradeType();

        ConfFamiliyBrawlRank confRank = ConfFamiliyBrawlRank.get(grade);
        if(confRank == null){
            Log.temp.error("ConfFamiliyBrawlRank配表错误， not find Sn={}", grade);
            port.returns( "msgGuild", null);
            return;
        }
        int newGrade = GuildManager.inst().gradeSn(GuildParamKey.scoreTypeGroup_0, guildData.getScore());

        MsgGvg.gvg_settlement_week_s2c.Builder msgGuild = MsgGvg.gvg_settlement_week_s2c.newBuilder();
        String keyGuild = RedisKeys.bridge_date_group_grade_guild_rank + matchId;
        int rankGuild = Utils.getRedisRank(keyGuild, String.valueOf(guildId));
        int scoreGuild = Utils.intValue(Utils.getRedisScore(keyGuild, String.valueOf(guildId)));
        int familiySnGuild = GlobalConfVal.getLeagueRankSn(confRank.family_rank_reward, rankGuild);// 工会段位排行榜
        ConfFamiliybrawlRankReward conf = ConfFamiliybrawlRankReward.get(familiySnGuild);
        if(conf != null){
            msgGuild.addAllRewardList(InstanceManager.inst().to_p_rewardList(conf.rank_reward));
        }
        msgGuild.setOldRanking(grade);
        msgGuild.setRanking(newGrade);
        msgGuild.setScore(scoreGuild);
        msgGuild.setRank(rankGuild);
        msgGuild.setSeason(season.getSeason());

        port.returns("msgGuild", msgGuild.build());
    }

    @DistrMethod
    public void getMsg_gvg_settlement_season_s2c(long guildId, long humanId){
        GuildLeagueRecord guildData = guildRecordMap.get(guildId);
        if(guildData == null){
            port.returns("msg", null);
            return;
        }

        int groupIndex = GlobalConfVal.getCrossServerIdGroup(CrossType.cross_chaos_battle.getType(), guildData.getServerId());
        GuildLeagueSeason season = guildSeasonMap.get(groupIndex);
        if(season == null){
            port.returns("msg", null);
            return;
        }
        if(season.getWeek() < 4){
            port.returns("msg", null);
            return;
        }
        int grade = GuildManager.inst().gradeSn(GuildParamKey.scoreTypeGroup_0, guildData.getScore());
        MsgGvg.gvg_settlement_season_s2c.Builder msg = MsgGvg.gvg_settlement_season_s2c.newBuilder();
        msg.setRanking(grade);
        msg.setScore(guildData.getScore());
        msg.setRank(season.getSeason());
        port.returns("msg", msg.build());
    }


    @DistrMethod
    public void updateHumanBrief(HumanBriefVO vo){
        EntityManager.getEntityAsync(HumanBrief.class, vo.humanId, f->{
            if(f.failed()){
                return;
            }
            HumanBrief brief = f.result();
            if(brief == null){
                brief = vo.buildHumanBrief();
                brief.persist();
                return;
            }
            vo.updateHumanBrief(brief);
        });
    }

    private void checkHumanExist(){
        humanIdJoinList.clear();
        humanIdJoinList.addAll(humanIdJoinSet);
        int end = Math.min(checkJoinIndex + 500, humanIdJoinList.size());
        Map<Integer, List<Long>> loadIdListMap = new HashMap<>();
        for(int i = checkJoinIndex; i < end; i++){
            long humanId = humanIdJoinList.get(i);
            if(!Utils.isExitRedisKey("entity.init.HumanBrief."+ humanId)){
                int serverId = Utils.getServerIdByHumanId(humanId);
                loadIdListMap.computeIfAbsent(serverId, k -> new ArrayList<>()).add(humanId);
                continue;
            }
        }
        checkJoinIndex = end;
        if(!loadIdListMap.isEmpty()){
            for(Map.Entry<Integer, List<Long>> entry : loadIdListMap.entrySet()){
                try{
                    HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance(DistrKit.getWorldNodeID(entry.getKey()));
                    proxy.getSyncHumanBrief(entry.getValue());
                } catch (Exception e){
                    Log.temp.error("===loadIdListMap ", e);
                }
            }
        }
        if(humanIdJoinList.size() == checkJoinIndex){
            ttSecCheck.stop();
        }
    }

    @DistrMethod
    public void updateHumanBriefList(List<HumanBrief> briefList){
        Map<Long, HumanBrief> map = new HashMap<>();
        List<Long> idList = new ArrayList<>();
        for(HumanBrief brief : briefList){
            try {
                map.put(brief.getId(), brief);
                if(!Utils.isExitRedisKey("entity.init.HumanBrief."+ brief.getId())){
                    HumanBrief newBrief = new HumanBriefVO(brief).buildHumanBrief();
                    newBrief.persist();
                } else {
                    idList.add(brief.getId());
                }
            } catch (Exception e){
                Log.temp.debug("====同步数据估计已经存在", e);
            }
        }
        if(idList.isEmpty()){
            map.clear();
            briefList.clear();
            return;
        }
        CrossHumanLoader.getList(idList, HumanBriefLoadType.BATTLE_INFO, ret2 -> {
            if (ret2.failed()) {
                Log.temp.error("===ret2.cause={}", ret2.cause());
                return;
            }
            List<HumanBrief> humanBriefList = ret2.result();
            for (HumanBrief brief : humanBriefList) {
                if (brief == null) {
                    continue;
                }
                HumanBriefVO vo = new HumanBriefVO(map.get(brief.getId()));
                vo.updateHumanBrief(brief);
            }
        });
    }

    @DistrMethod
    public void updateGuildInfo(long guildId, Param param){
        GuildLeagueRecord guildData = guildRecordMap.get(guildId);
        if(param.containsKey(GuildLeagueRecord.K.flagJSON)){
            guildData.setFlagJSON(param.getString(GuildLeagueRecord.K.flagJSON));
        }
        if(param.containsKey(GuildLeagueRecord.K.leaderId)){
            long leaderId = Utils.longValue(param.get(GuildLeagueRecord.K.leaderId));
            if(leaderId > 0){
                guildData.setLeaderId(leaderId);
            }
        }
        if(param.containsKey(GuildLeagueRecord.K.leaderName)){
            guildData.setLeaderName(Utils.removeEmojis(param.getString(GuildLeagueRecord.K.leaderName)));
        }
        if(param.containsKey(GuildLeagueRecord.K.guildName)){
            guildData.setGuildName(Utils.removeEmojis(param.getString(GuildLeagueRecord.K.guildName)));
        }
    }

    @DistrMethod
    public void gvg_hall_c2s(int serverId, long guildId){
        int groupIndex = GlobalConfVal.getCrossServerIdGroup(CrossType.cross_chaos_battle.getType(),serverId);

        long pid = port.createReturnAsync();
        CrossRedis.getRankListByIndex(RedisKeys.bridge_pvp_group_guild_rank + groupIndex, 0, 0, true, retGuild -> {
            long guildIdTop = 0;
            int scoreGuildTop = 0;
            if (retGuild.succeeded()) {
                JsonArray jsonGuild = retGuild.result();
                if(jsonGuild.getList() != null && !jsonGuild.getList().isEmpty()){
                    guildIdTop = Utils.longValue(jsonGuild.getList().get(0));
                    scoreGuildTop = Utils.intValue(jsonGuild.getList().get(1));
                }
            }
            long guildIdSeasonTop = guildIdTop;
            int scoreGuildSeasonTop = scoreGuildTop;
            RedisTools.getRankListByIndex(EntityManager.redisClient, RedisKeys.bridge_pvp_group_human_rank + groupIndex, 0, 0, true, retHuman ->{
                long humanIdTop = 0;
                int scoreTop = 0;
                if(retHuman.succeeded()){
                    JsonArray json = retHuman.result();
                    humanIdTop = Utils.longValue(json.getList().get(0));
                    scoreTop = Utils.intValue(json.getList().get(1));
                }
                long humanIdSeasonTop = humanIdTop;
                int scoreSeasonTop = scoreTop;
                long matchId = matcher.getMacthId(guildId);
                if(matchId <= 0){
                    result_gvg_hall_s2c(pid, guildIdSeasonTop, scoreGuildSeasonTop, humanIdSeasonTop, scoreSeasonTop, 0, 0, 0, 0);
                    return;
                }
                CrossRedis.getRankListByIndex(RedisKeys.bridge_date_group_grade_guild_rank + matchId, 0, 0, true, retGuildWeek -> {
                    long guildIdWeekTemp = 0;
                    int scoreGuildWeekTemp= 0;
                    if (retGuildWeek.succeeded()) {
                        JsonArray jsonGuild = retGuildWeek.result();
                        if (jsonGuild.getList() != null && !jsonGuild.getList().isEmpty()) {
                            guildIdWeekTemp = Utils.longValue(jsonGuild.getList().get(0));
                            scoreGuildWeekTemp = Utils.intValue(jsonGuild.getList().get(1));
                        }
                    }
                    long guildIdWeekTop = guildIdWeekTemp;
                    int scoreGuildWeekTop = scoreGuildWeekTemp;

                    CrossRedis.getRankListByIndex(RedisKeys.bridge_date_group_grade_human_rank + matchId, 0, 0, true, retHumanWeek -> {
                        long humanIdWeekTemp = 0;
                        int scoreWeekTop = 0;
                        if (retHumanWeek.succeeded()) {
                            JsonArray jsonGuild = retHumanWeek.result();
                            if (jsonGuild.getList() != null && !jsonGuild.getList().isEmpty()) {
                                humanIdWeekTemp = Utils.longValue(jsonGuild.getList().get(0));
                                scoreWeekTop = Utils.intValue(jsonGuild.getList().get(1));
                            }
                        }

                        result_gvg_hall_s2c(pid, guildIdSeasonTop, scoreGuildSeasonTop, humanIdSeasonTop, scoreSeasonTop,
                                guildIdWeekTop, scoreGuildWeekTop, humanIdWeekTemp, scoreWeekTop);

                    });
                });
            });
        });
    }

    private void result_gvg_hall_s2c(long pid, long guildIdSeasonTop, long scoreGuildSeasonTop, long humanIdSeasonTop, int scoreSeasonTop,
                                     long guildIdWeekTop, long scoreGuildWeekTop, long humanIdWeekTop, int scoreWeekTop){

        MsgGvg.gvg_hall_s2c.Builder msg = MsgGvg.gvg_hall_s2c.newBuilder();

        Set<Long> humanIdSet= new HashSet<>();
        // 赛季最强家族
        if(guildIdSeasonTop > 0){
            GuildLeagueRecord guildDataSeason = guildRecordMap.get(guildIdSeasonTop);
            if(guildDataSeason != null){
                humanIdSet.add(guildDataSeason.getLeaderId());
            }
        }
        // 赛季最强玩家
        if(humanIdSeasonTop > 0){
            humanIdSet.add(humanIdSeasonTop);
        }
        // 本周最强家族
        if(guildIdWeekTop > 0){
            GuildLeagueRecord guildDataWeek = guildRecordMap.get(guildIdSeasonTop);
            if(guildDataWeek != null){
                humanIdSet.add(guildDataWeek.getLeaderId());
            }
        }
        // 本周最强玩家
        if(humanIdWeekTop > 0){
            humanIdSet.add(humanIdWeekTop);
        }
        if(humanIdSet.isEmpty()){
            port.returnsAsync(pid, "msg", msg.build());
            return;
        }
        CrossHumanLoader.getList(new ArrayList<>(humanIdSet), HumanBriefLoadType.SHOW_INFO, ret ->{
            if(ret.failed()){
                port.returnsAsync(pid, "msg", msg.build());
                return;
            }
            List<HumanBrief> briefList = ret.result();
            Map<Long, HumanBrief> humanBriefMap = new HashMap<>();
            for(HumanBrief brief : briefList){
                humanBriefMap.put(brief.getId(), brief);
            }
            // 赛季最强家族
            if(guildIdSeasonTop > 0) {
                GuildLeagueRecord guildDataSeason = guildRecordMap.get(guildIdSeasonTop);
                if (guildDataSeason != null) {
                    Define.p_rank_info dInfo =GuildManager.inst().to_p_rank_info(guildDataSeason.getGuildName(), guildDataSeason.getLeaderId(),
                            guildDataSeason.getServerId(), guildDataSeason.getFlagJSON(),1, (int)scoreGuildSeasonTop, humanBriefMap.get(guildDataSeason.getLeaderId()));
                    if(dInfo != null){
                        msg.setSeasonTopGuild(dInfo);
                    }
                }
            }
            // 赛季最强玩家
            if(humanIdSeasonTop > 0){
                RankInfo rankInfo = new RankInfo();
                msg.setSeasonTopRole(rankInfo.toRankInfo(humanBriefMap.get(humanIdSeasonTop), 1, scoreSeasonTop));
            }
            // 本周最强家族
            if(guildIdWeekTop > 0){
                GuildLeagueRecord guildDataWeek = guildRecordMap.get(guildIdWeekTop);
                if(guildDataWeek != null){
                    List<Define.p_key_value_string> keyList = new ArrayList<>();
                    String flagJSON = guildDataWeek.getFlagJSON();
                    if (!Utils.isEmptyJSONString(flagJSON)) {
                        keyList = GuildManager.inst().to_p_guild_flag(flagJSON);
                    }
                    msg.setTopGuild(GuildManager.inst().to_p_gvg_guild(guildDataWeek.getGuildId(), guildDataWeek.getGuildName(), scoreGuildWeekTop, guildDataWeek.getServerId(), keyList));
                }
            }
            // 本周最强玩家
            if(humanIdWeekTop > 0){
                Define.p_gvg_player dInfo =GuildManager.inst().to_p_gvg_player(humanBriefMap.get(humanIdWeekTop), 1);
                if(dInfo != null){
                    msg.setTopRole(dInfo);
                }
            }
            port.returnsAsync(pid, "msg", msg.build());
        });
    }

    @DistrMethod
    public void gmUpdate(){
        ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.familybattle_initial_time.SN);
        long openTime = Utils.formatTimeToLong(confGlobal.strValue);
        int sumWeek = (int)Math.ceil((Utils.getDaysBetween(Port.getTime(), openTime)) *1.D / 7);
        int season = (int)Math.ceil(sumWeek / 4.D);
        int week = sumWeek % 4 == 0 ? 4 : sumWeek % 4;

        long timeOpen = Utils.getTimeOfWeek(Port.getTime(), 1, 0) - (week - 1) * Time.WEEK;// 本周二0点
        long timeEnd = Utils.getTimeOfWeek(Port.getTime(), 1, 0) + (4-week + 1) * Time.WEEK;// 第5周周一0点
        Log.temp.info("===执行gm开始，season={}, week={}, openTime={}, endTime={}， size={}， keys={}",
                season, week, timeOpen, timeEnd, guildSeasonMap.size(), guildSeasonMap.keySet());

        int seasonUpCount = 0;
        for(GuildLeagueSeason seasonUp : guildSeasonMap.values()){
            if(seasonUp.getSeason() != season){
                seasonUp.setSeason(season);
            }
            if(seasonUp.getWeek() != week){
                seasonUp.setWeek(week);
            }
            if(seasonUp.getOpenTime() != timeOpen || seasonUp.getEndTime() != timeEnd){
                seasonUp.setOpenTime(timeOpen);
                seasonUp.setEndTime(timeEnd);
            }
            seasonUpCount++;
            Log.temp.error("===更新赛季数据，season={}, week={}, openTime={}, endTime={}", season, week, timeOpen, timeEnd);
        }
        int guildCount = 0;
        for(GuildLeagueRecord guildData : guildRecordMap.values()){
            GuildLeagueSeason guildSeason =  guildSeasonMap.get(guildData.getGroupPvp());
            if(guildSeason == null) {
                continue;
            }
            if(season != guildData.getSeason()) {
                Log.temp.info("====gm设置工会赛季， guildId={}, season={}, newSeason={}", guildData.getGuildId(), guildData.getSeason(), season);
                guildData.setSeason(season);
                guildCount++;
            }
        }
        Log.temp.info("===执行gm结束，season={}, week={}, openTime={}, endTime={}， size={}， keys={}, guildCount={}, seasonUpCount={}",
                season, week, timeOpen, timeEnd, guildSeasonMap.size(), guildSeasonMap.keySet(), guildCount, seasonUpCount);
    }

    @DistrMethod
    public void sendBridgeGuildRank(int serverId, int type, int page, long guildId){
        int groupIndex = GlobalConfVal.getCrossServerIdGroup(CrossType.cross_chaos_battle.getType(), serverId);
        String redisKey = type == RankParamKey.rankTypeGuildBattleGuild_3016 ?
                Family32Utils.getGvgSeasonRankKey(Family32Utils.getCurrentSeason(), groupIndex) : RedisKeys.bridge_pvp_group_guild_rank + groupIndex;
        long pid = port.createReturnAsync();
        MsgRank.rank_data_list_s2c.Builder msg = MsgRank.rank_data_list_s2c.newBuilder();

        CrossRedis.getRankLen(redisKey, rest -> {
            if (rest.failed()) {
                Log.friend.error("获取失败，i={}", redisKey);
                port.returnsAsync(pid, "msg", msg.build());
                return;
            }
            long total = rest.result();
            if(total == 0){
                port.returnsAsync(pid, "msg", msg.build());
                return;
            }
            int maxPage = (int) Math.max(1, Math.ceil(total / RankParamKey.pageNum));

            int openIndex = (page - 1) * RankParamKey.pageNum;
            int closeIndex = page * RankParamKey.pageNum;
            openIndex = Math.max(openIndex, 0);
            closeIndex = Math.max(closeIndex, RankParamKey.pageNum);
            final int rankTemp = openIndex;

            Define.p_rank_data.Builder rankData = Define.p_rank_data.newBuilder();
            rankData.setType(type);
            rankData.setServer(Utils.getServerIdTo(serverId));
            rankData.setPage(page);
            rankData.setMaxPage(maxPage <= 0 ? 1 : maxPage);
            rankData.setTotalNum((int)total);
            ConfRanktype confRanktype = ConfRanktype.get(type);
            rankData.setNextFreshTime(confRanktype.refresh_time);

            CrossRedis.getRankListByIndex(redisKey, openIndex, closeIndex - 1, true, ret -> {
                if(ret.failed()){
                    Log.temp.error("获取失败，rankSn={}, redisKey={}", type, redisKey);
                    port.returnsAsync(pid, "msg", msg.build());
                    return;
                }
                JsonArray json = ret.result();
                if(json.getList() == null || json.getList().isEmpty()){
                    port.returnsAsync(pid, "msg", msg.build());
                    return;
                }
                int sumSize = json.size();
                if(rankTemp == 0){
                    List<Long> humanIdList = new ArrayList<>();
                    for (int i = 0; i < sumSize; i+=2) {
                        long id = Utils.longValue(json.getList().get(i));
                        GuildLeagueRecord guildData = guildRecordMap.get(id);
                        if(guildData == null){
                            Log.temp.error("未找到公会id={}", id);
                            continue;
                        }
                        humanIdList.add(guildData.getLeaderId());
                        if(humanIdList.size() >= 3){
                            break;
                        }
                    }
                    CrossHumanLoader.getList(humanIdList, HumanBriefLoadType.SHOW_INFO, ret2 -> {
                        if (ret2.failed()) {
                            Log.temp.error("===ret2.cause={}", ret2.cause());
                            port.returnsAsync(pid, "msg", msg.build());
                            return;
                        }
                        List<HumanBrief> humanBriefList = ret2.result();
                        Map<Long, HumanBrief> humanBriefMap = new HashMap<>();
                        for (HumanBrief brief : humanBriefList) {
                            if (brief == null) {
                                continue;
                            }
                            humanBriefMap.put(brief.getId(), brief);
                        }
                        int rank = rankTemp;
                        boolean isMy = false;
                        for (int i = 0; i < sumSize; i+=2) {
                            long id = Utils.longValue(json.getList().get(i));
                            int score = Utils.intValue(json.getList().get(i + 1));
                            GuildLeagueRecord guildData = guildRecordMap.get(id);
                            if(guildData == null){
                                Log.temp.error("未找到公会id={}", id);
                                continue;
                            }
                            rank++;
                            HumanBrief brief = humanBriefMap.get(guildData.getLeaderId());
                            if(brief == null){
                                getHumanBrief(guildData.getLeaderId());
                            }
                            Define.p_rank_info dInfo = RankManager.inst().to_p_rank_info(guildData, score, rank, serverId, brief);
                            rankData.addRankInfo(dInfo);
                            if (id == guildId) {
                                rankData.setMyRankInfo(dInfo);
                                isMy = true;
                            }
                        }
                        if(isMy) {
                            msg.addRankDataList(rankData);
                            port.returnsAsync(pid, "msg", msg.build());
                            return;
                        }
                        RedisTools.getMyRank(EntityManager.redisClient, redisKey, String.valueOf(guildId), ret1-> {
                            if(ret1.failed()){
                                msg.addRankDataList(rankData);
                                port.returnsAsync(pid, "msg", msg.build());
                                return;
                            }
                            int myRank = Utils.intValue(ret1.result());
                            RedisTools.getMyScore(EntityManager.redisClient, redisKey, guildId, ret3-> {
                                if (ret3.failed()) {
                                    return;
                                }
                                int myScore = Utils.intValue(ret3.result());
                                GuildLeagueRecord guildData = guildRecordMap.get(guildId);
                                if(guildData == null){
                                    Log.temp.error("未找到公会id={}", guildId);
                                    return;
                                }
                                HumanBrief brief = humanBriefMap.get(guildData.getLeaderId());
                                if(brief == null){
                                    getHumanBrief(guildData.getLeaderId());
                                }
                                Define.p_rank_info dInfo = RankManager.inst().to_p_rank_info(guildData, myScore, myRank, serverId, brief);
                                rankData.setMyRankInfo(dInfo);
                                msg.addRankDataList(rankData);
                                port.returnsAsync(pid, "msg", msg.build());
                            });
                        });
                    });
                    return;
                }
                int rank = rankTemp;
                boolean isMy = false;
                for (int i = 0; i < sumSize; i+=2) {
                    long id = Utils.longValue(json.getList().get(i));
                    int score = Utils.intValue(json.getList().get(i + 1));
                    rank++;
                    GuildLeagueRecord guildData = guildRecordMap.get(id);
                    if(guildData == null){
                        Log.temp.error("未找到公会id={}", guildId);
                        continue;
                    }
                    Define.p_rank_info dInfo = RankManager.inst().to_p_rank_info(guildData, score, rank, serverId, null);
                    rankData.addRankInfo(dInfo);
                    if (id == guildId) {
                        rankData.setMyRankInfo(dInfo);
                        isMy = true;
                    }
                }
                if(isMy) {
                    msg.addRankDataList(rankData);
                    port.returnsAsync(pid, "msg", msg.build());
                    return;
                }
                RedisTools.getMyRank(EntityManager.redisClient, redisKey, String.valueOf(guildId), ret1-> {
                    if(ret1.failed()){
                        msg.addRankDataList(rankData);
                        port.returnsAsync(pid, "msg", msg.build());
                        return;
                    }
                    int myRank = Utils.intValue(ret1.result());
                    RedisTools.getMyScore(EntityManager.redisClient, redisKey, guildId, ret2-> {
                        if (ret2.failed()) {
                            msg.addRankDataList(rankData);
                            port.returnsAsync(pid, "msg", msg.build());
                            return;
                        }
                        int myScore = Utils.intValue(ret2.result());
                        GuildLeagueRecord guildData = guildRecordMap.get(guildId);
                        if(guildData == null){
                            Log.temp.error("未找到公会id={}", guildId);
                            msg.addRankDataList(rankData);
                            port.returnsAsync(pid, "msg", msg.build());
                            return;
                        }
                        Define.p_rank_info dInfo = RankManager.inst().to_p_rank_info(guildData, myScore, myRank, serverId, null);
                        rankData.setMyRankInfo(dInfo);
                        msg.addRankDataList(rankData);
                        port.returnsAsync(pid, "msg", msg.build());
                    });
                });
            });
        });

    }


    @DistrMethod
    public void update(String json){

    }

    @DistrMethod
    public void update1(Object... objs){

    }


    @DistrMethod
    public void update2(Param param){
        ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.familybattle_initial_time.SN);
        long openTime = Utils.formatTimeToLong(confGlobal.strValue);
        int sumWeek = (int)Math.ceil((Utils.getDaysBetween(Port.getTime(), openTime)) *1.D / 7);
        int season = (int)Math.ceil(sumWeek / 4.D);
        int week = sumWeek % 4 == 0 ? 4 : sumWeek % 4;

        long timeOpen = Utils.getTimeOfWeek(Port.getTime(), 1, 0) - (week - 1) * Time.WEEK;// 本周二0点
        long timeEnd = Utils.getTimeOfWeek(Port.getTime(), 1, 0) + (4-week + 1) * Time.WEEK;// 第5周周一0点
        Log.temp.info("===执行gm开始，season={}, week={}, openTime={}, endTime={}， size={}， keys={}",
                season, week, timeOpen, timeEnd, guildSeasonMap.size(), guildSeasonMap.keySet());

        for(GuildLeagueSeason seasonWarmUp : guildSeasonMap.values()){
            if(seasonWarmUp.getSeason() != season){
                seasonWarmUp.setSeason(season);
            }
            if(seasonWarmUp.getWeek() != week){
                seasonWarmUp.setWeek(week);
            }
            if(seasonWarmUp.getOpenTime() != timeOpen || seasonWarmUp.getEndTime() != timeEnd){
                seasonWarmUp.setOpenTime(timeOpen);
                seasonWarmUp.setEndTime(timeEnd);
            }
            Log.temp.error("===更新赛季数据，season={}, week={}, openTime={}, endTime={}", season, week, timeOpen, timeEnd);
        }
        for(GuildLeagueRecord guildData : guildRecordMap.values()){
            GuildLeagueSeason guildSeason =  guildSeasonMap.get(guildData.getGroupPvp());
            if(guildSeason == null) {
                continue;
            }
            if(season != guildData.getSeason()) {
                Log.temp.info("====gm设置工会赛季， guildId={}, season={}, newSeason={}", guildData.getGuildId(), guildData.getSeason(), season);
                guildData.setSeason(season);
            }
        }

        Log.temp.error("===执行gm结束");
    }

    @DistrMethod
    public void update3(Param param){

    }

    @ScheduleMethod(DataResetService.CRON_WEEK_MON_FIVE_HOUR)
    public void CRON_WEEK_MON_FIVE_HOUR() {
        RedisTools.getFullSet(EntityManager.redisClient, RedisKeys.week_remove_log, ret->{
            if(ret.failed()){
                return;
            }
            JsonArray jsonArray = ret.result();
            List<String> removeKeyList = new ArrayList<>();
            for(int i = 0; i < jsonArray.getList().size(); i++){
                removeKeyList.add(String.valueOf(jsonArray.getList().get(i)));
            }
            RedisTools.del(removeKeyList);

        });
        RedisTools.getFullSet(EntityManager.redisClient, RedisKeys.guild_league_remove_list, ret->{
            if(ret.failed()){
                return;
            }
            JsonArray jsonArray = ret.result();
            List<String> removeKeyList = new ArrayList<>();
            for(int i = 0; i < jsonArray.getList().size(); i++){
                removeKeyList.add(String.valueOf(jsonArray.getList().get(i)));
            }
            RedisTools.del(removeKeyList);
        });
    }

    @DistrMethod
    public void getRoleOthersGuildInfo(long guildId, long humanId){
        GuildLeagueRecord guildData = guildRecordMap.get(guildId);
        if(guildData == null){
            port.returns("name","", "flagJSON", "");
            return;
        }
        port.returns("name", guildData.getGuildName(), "flagJSON", guildData.getFlagJSON());
    }

    @DistrMethod
    public void getHumanBrief(long humanId){
        int serverId = Utils.getServerIdByHumanId(humanId);
        List<Long> humanIdList = new ArrayList<>();
        humanIdList.add(humanId);
        HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance(serverId);
        proxy.getSyncHumanBrief(humanIdList);
    }


    @DistrMethod
    public void getCrossHumanInfo(long humanId, int type){
        long pid = port.createReturnAsync();
        GuildServiceProxy proxy = GuildServiceProxy.newInstance(DistrKit.getWorldNodeID(Utils.getServerIdByHumanId(humanId)));
        proxy.getCrossHumanInfo(humanId, type);
        proxy.listenResult(this::result_get_cross_human_info,"humanId",humanId, "type",type, "pid", pid);
    }

    private void result_get_cross_human_info(Param results, Param context){
        long pid = context.get("pid");
//        long humanId = context.get("humanId");
//        int type = context.get("type");

        HumanBrief humanBrief = results.get("brief");
        Equip equip = results.get("equip");
        if(humanBrief == null){
            port.returnsAsync(pid, "result", false, "brief", humanBrief, "equip", equip);
            return;
        }
        if(humanBrief.getGuildId() == 0){
            port.returnsAsync(pid, "brief", humanBrief, "equip", equip);
            return;
        }
        port.returnsAsync(pid, "brief", humanBrief, "guildName", results.get("guildName"), "guildLv", results.get("guildLv"),
                "guildHumanNum", results.get("guildHumanNum"), "guildFlagJSON", results.get("guildFlagJSON"), "equip", equip);
    }


    @DistrMethod
    public void removeGuildEnroll(long guildId){
        //移除报名信息
        GuildLeagueRecord info = guildRecordMap.get(guildId);
        if(info!= null){
            int grade = info.getGrade();
            register.removeGuild(info.getServerId(), guildId, grade);
            register.removeRegister(info.getSeason(), guildId);
            register.removeGroupGradeGuild(info.getGroupPvp(), guildId, grade);
        }
        matcher.removeMatchGuild(guildId);

    }

}
