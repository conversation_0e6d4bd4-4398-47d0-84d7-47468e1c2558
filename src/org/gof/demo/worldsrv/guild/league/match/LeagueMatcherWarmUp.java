package org.gof.demo.worldsrv.guild.league.match;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.gof.core.Port;
import org.gof.core.dbsrv.redis.EntityManager;
import org.gof.core.dbsrv.redis.RedisTools;
import org.gof.core.support.*;
import org.gof.demo.battlesrv.battle.BattleData;
import org.gof.demo.battlesrv.battle.BattleDataFill;
import org.gof.demo.battlesrv.battle.BattleMainServer;
import org.gof.demo.battlesrv.battle.PlayerData;
import org.gof.demo.battlesrv.battle.enumVo.RunState;
import org.gof.demo.distr.DistrKit;
import org.gof.demo.worldsrv.bridgeEntity.*;
import org.gof.demo.worldsrv.character.HumanObjectServiceProxy;
import org.gof.demo.worldsrv.config.ConfFamiliyBrawl;
import org.gof.demo.worldsrv.config.ConfGlobal;
import org.gof.demo.worldsrv.entity.Human;
import org.gof.demo.worldsrv.guild.GuildGvgBattle;
import org.gof.demo.worldsrv.guild.GuildManager;
import org.gof.demo.worldsrv.guild.GuildParamKey;
import org.gof.demo.worldsrv.guild.league.GuildLeagueUtils;
import org.gof.demo.worldsrv.guild.league.LeagueVO;
import org.gof.demo.worldsrv.human.HumanData;
import org.gof.demo.worldsrv.human.HumanGlobalInfo;
import org.gof.demo.worldsrv.human.HumanGlobalServiceProxy;
import org.gof.demo.worldsrv.human.HumanManager;
import org.gof.demo.worldsrv.instance.InstanceManager;
import org.gof.demo.worldsrv.mail.MailManager;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgGvg;
import org.gof.demo.worldsrv.pocketLine.Pocket;
import org.gof.demo.worldsrv.pocketLine.PocketLineEventSubKey;
import org.gof.demo.worldsrv.rank.RankInfo;
import org.gof.demo.worldsrv.redis.RedisKeys;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.enumKey.ConfGlobalKey;
import org.gof.demo.worldsrv.task.TaskConditionTypeKey;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

public class LeagueMatcherWarmUp {

    // id， 匹配信息
    public Map<Long, GuildLeagueMatchWarmUp> matchMap = new HashMap<>();

    // 周几（三四五）宗门id和对手宗门id
    private Map<Integer, Map<Long, Long>> dayGuildIdRivalIdMap = new HashMap<>();
    // 工会id, 上中下路，玩家id集合
    public Map<Long, Map<Integer, List<Long>>> guildIdPoadHumanIdMap = new HashMap<>();
    // 工会id, 匹配id
    public Map<Long, Long> guildIdMatchIdMap = new HashMap<>();

    public Map<Integer,Map<Long, Map<Integer, Integer>>> roundGuildRoadStatusMap  = new HashMap<>();

    // 工会id, 分路,  战斗次数, 战斗记录id
    public Map<Long, Map<Integer, List<Long>>> guildIdRoadBattleNumIdMap = new HashMap<>();

    private Map<Long, Integer> guildIdAddScoreAllMap = new HashMap<>();

    private List<GuildGvgBattle> battleAllList = new ArrayList<>();
    public int getIndex = 0;

    public Map<Long, Integer> guildIdIndexMap = new HashMap<>();

    private Map<Long, GuildGvgBattle> guildIdBattleMap = new HashMap<>();

    public List<Long> winGuildIdList = new ArrayList<>();

    public Set<Long> addScoreIdList = new HashSet<>();

    public Set<Long> matchGuildIdList = new HashSet<>();

    public LeagueMatcherWarmUp() {

    }

    public void addMatch(GuildLeagueMatchWarmUp match) {
        long timeNow = Port.getTime();
        if(!Utils.isSameWeek(timeNow, match.getOpenTime())){
            return;
        }
        long matchId = match.getId();
        matchMap.put(matchId, match);
        List<Long> guildIdList = Utils.strToLongList(match.getGuildIdList());
        for(long guildId : guildIdList){
            guildIdMatchIdMap.put(guildId, matchId);
        }
        addGuildIdPoadHumanIdMap(match);
        addPairListMap(match);

        Map<Integer,Map<Long, Map<Integer, Integer>>> map = Utils.jsonToIntLongMapIntInt(match.getBattleNumGuildIdRoadMap());
        for(Map.Entry<Integer, Map<Long, Map<Integer, Integer>>> entry : map.entrySet()){
            int round = entry.getKey();
            Map<Long, Map<Integer, Integer>> guildIdRoadMap = entry.getValue();
            Map<Long, Map<Integer, Integer>> map2 = roundGuildRoadStatusMap.getOrDefault(round, new HashMap<>());
            for(Map.Entry<Long, Map<Integer, Integer>> guildEntry :guildIdRoadMap.entrySet()){
                if(map2.containsKey(guildEntry.getKey())){
                    continue;
                }
                map2.put(guildEntry.getKey(), guildEntry.getValue());
            }
            roundGuildRoadStatusMap.put(round, map2);
        }
    }

    private void addPairListMap(GuildLeagueMatchWarmUp match){
        Map<Integer, Map<Long, Long>> battleNumGuildIdMap = Utils.jsonToIntMapLongLong(match.getBattleNumGuildIdMap());
        for (Map.Entry<Integer, Map<Long, Long>> ent : battleNumGuildIdMap.entrySet()) {
            Map<Long, Long> guildMap = dayGuildIdRivalIdMap.computeIfAbsent(ent.getKey(), k -> new HashMap<>());
            for(Map.Entry<Long, Long> entry : ent.getValue().entrySet()){
                guildMap.put(entry.getKey(), entry.getValue());
            }
            guildMap.putAll(ent.getValue());
        }
    }

    private void addGuildIdPoadHumanIdMap(GuildLeagueMatchWarmUp match){
        List<Long> guildIdList = Utils.strToLongList(match.getGuildIdList());
        for(int i = 0; i < guildIdList.size(); i++){
            long guildId = guildIdList.get(i);
            switch (i){
                case 0:
                    Map<Integer, List<Long>> map = Utils.jsonToMapIntListLong(match.getGuildId1Map());
                    GuildManager.inst().initRoad(map);
                    guildIdPoadHumanIdMap.put(guildId, map);
                    break;
                case 1:
                    Map<Integer, List<Long>> map2 = Utils.jsonToMapIntListLong(match.getGuildId2Map());
                    GuildManager.inst().initRoad(map2);
                    guildIdPoadHumanIdMap.put(guildId, map2);
                    break;
                case 2:
                    Map<Integer, List<Long>> map3 = Utils.jsonToMapIntListLong(match.getGuildId3Map());
                    GuildManager.inst().initRoad(map3);
                    guildIdPoadHumanIdMap.put(guildId, map3);
                    break;
                case 3:
                    Map<Integer, List<Long>> map4 = Utils.jsonToMapIntListLong(match.getGuildId4Map());
                    GuildManager.inst().initRoad(map4);
                    guildIdPoadHumanIdMap.put(guildId, map4);
                    break;
            }
        }
    }

    public long getMacthId(long guildId){
        return guildIdMatchIdMap.getOrDefault(guildId, 0l);
    }

    public List<Long> gvgSelectRoad(long guildId, long humanId, int road, List<Define.p_gvg_road> roadInfoList){
        long matchId = getMacthId(guildId);
        GuildLeagueMatchWarmUp match = matchMap.get(matchId);
        if(match == null){
            return new ArrayList<>();
        }
        List<Long> guildIdList = Utils.strToLongList(match.getGuildIdList());
        int index = guildIdList.indexOf(guildId);
        if(index == -1){
            return new ArrayList<>();
        }
        Map<Integer, List<Long>> map = getRoadHumanIdListMap(guildId);
        List<Long> humanIdList = map.get(road);
        for(Map.Entry<Integer, List<Long>> entry : map.entrySet()){
            List<Long> list = entry.getValue();
            if(list.contains(humanId)){
                list.remove(humanId);
                roadInfoList.add(get_p_gvg_road(guildId, entry.getKey()));
            }
        }
        if(!humanIdList.contains(humanId)){
            humanIdList.add(humanId);
        }

        switch (index){
            case 0:
                match.setGuildId1Map(Utils.mapIntListLongToJSON(map));
                break;
            case 1:
                match.setGuildId2Map(Utils.mapIntListLongToJSON(map));
                break;
            case 2:
                match.setGuildId3Map(Utils.mapIntListLongToJSON(map));
                break;
            case 3:
                match.setGuildId4Map(Utils.mapIntListLongToJSON(map));
                break;
            default:
                Log.temp.error("===index={}", index);
                break;
        }
        match.update();
        roadInfoList.add(get_p_gvg_road(guildId, road));
        return humanIdList;
    }

    public Define.p_gvg_road get_p_gvg_road(long guildId, int road){
        int round = GuildManager.inst().guildLeagueRound();
        long rivalId = getRivalId(round, guildId);

        Map<Integer, List<Long>> map = getRoadHumanIdListMap(guildId);
        List<Long> humanIdList = map.get(road);

        Map<Integer, List<Long>> mapRival = getRoadHumanIdListMap(rivalId);
        List<Long> humanIdListRival = mapRival.get(road);
        return GuildManager.inst().to_p_gvg_road(road, humanIdList.size(), humanIdListRival.size(), GuildParamKey.roadTwo);
    }

    public List<Define.p_gvg_road> get_p_gvg_road(long guildId, int... roadArr){
        int round = GuildManager.inst().guildLeagueRound();
        long rivalId = getRivalId(round, guildId);

        Map<Integer, List<Long>> map = getRoadHumanIdListMap(guildId);
        Map<Integer, List<Long>> mapRival = getRoadHumanIdListMap(rivalId);

        List<Define.p_gvg_road> dInfoList = new ArrayList<>();
        for(int road : roadArr){
            List<Long> humanIdList = map.getOrDefault(road, new ArrayList<>());
            List<Long> humanIdListRival = mapRival.getOrDefault(road, new ArrayList<>());
            dInfoList.add(GuildManager.inst().to_p_gvg_road(road, humanIdList.size(), humanIdListRival.size(), GuildParamKey.roadTwo));
        }
        return dInfoList;
    }

    public Map<Integer, List<Long>> getRoadHumanIdListMap(long guildId){
        Map<Integer, List<Long>> roadHumanIdListMap = guildIdPoadHumanIdMap.get(guildId);
        if(roadHumanIdListMap == null){
            roadHumanIdListMap = new HashMap<>();
            GuildManager.inst().initRoad(roadHumanIdListMap);
            guildIdPoadHumanIdMap.put(guildId, roadHumanIdListMap);
        }
        return roadHumanIdListMap;
    }

    public Map<Integer, List<Long>> gvgRoadChangeAll(long guildId, int road, int road1){
        long matchId = getMacthId(guildId);
        GuildLeagueMatchWarmUp match = matchMap.get(matchId);
        if(match == null){
            return new HashMap<>();
        }
        List<Long> guildIdList = Utils.strToLongList(match.getGuildIdList());
        int index = guildIdList.indexOf(guildId);
        Map<Integer, List<Long>> map = getRoadHumanIdListMap(guildId);
        List<Long> humanIdList = new ArrayList<>(map.getOrDefault(road, new ArrayList<>()));
        List<Long> humanIdList1 = new ArrayList<>(map.getOrDefault(road1, new ArrayList<>()));
        map.put(road1, humanIdList);
        map.put(road, humanIdList1);

        switch (index){
            case 0:
                match.setGuildId1Map(Utils.mapIntListLongToJSON(map));
                break;
            case 1:
                match.setGuildId2Map(Utils.mapIntListLongToJSON(map));
                break;
            case 2:
                match.setGuildId3Map(Utils.mapIntListLongToJSON(map));
                break;
            case 3:
                match.setGuildId4Map(Utils.mapIntListLongToJSON(map));
                break;
        }
        match.update();
        return map;
    }

    public List<Long> gvgRoadChange(long guildId, int road, List<Define.p_key_value> roadInfoList){
        long matchId = guildIdMatchIdMap.getOrDefault(guildId, 0L);
        GuildLeagueMatchWarmUp match = matchMap.get(matchId);
        if(match == null){
            return Collections.emptyList();
        }
        if (roadInfoList == null || roadInfoList.isEmpty()) {
            Log.temp.warn("Empty road info list for guild {}", guildId);
            return Collections.emptyList();
        }

        List<Long> guildIdList = Utils.strToLongList(match.getGuildIdList());
        int index = guildIdList.indexOf(guildId);
        if (index < 0 || index > 3) {
            Log.temp.error("Invalid guild index {} for {}", index, guildId);
            return Collections.emptyList();
        }
        Map<Integer, List<Long>> map = getRoadHumanIdListMap(guildId);
        if (!map.containsKey(road)) {
            Log.temp.error("Road {} not found for guild {}", road, guildId);
            return Collections.emptyList();
        }

        final List<Long> originalIds = new ArrayList<>(map.get(road));
        if (originalIds.isEmpty()) {
            return Collections.emptyList();
        }

        Set<Long> validHumanIds = new HashSet<>(originalIds);
        TreeMap<Integer, Long> positionMap = new TreeMap<>();

        // 初始化位置映射
        IntStream.rangeClosed(1, originalIds.size())
                .forEach(i -> positionMap.put(i, originalIds.get(i-1)));

        roadInfoList.forEach(entry -> {
            long humanId = entry.getK();
            int position = (int) entry.getV();

            if (!validHumanIds.contains(humanId)) {
                Log.temp.warn("Invalid human {} for guild {}", humanId, guildId);
                return;
            }

            if (position < 1 || position > originalIds.size()) {
                Log.temp.warn("Position {} out of range [1-{}]", position, originalIds.size());
                return;
            }

            // 清除原有位置
            positionMap.entrySet().removeIf(e -> e.getValue() == humanId);

            // 处理位置冲突
            final Long existing = positionMap.get(position);
            if (existing != null && existing != humanId) {
                Log.temp.info("Overriding position {}: {} -> {}", position, existing, humanId);
            }

            positionMap.put(position, humanId);
        });

        List<Long> newOrder = new ArrayList<>(positionMap.values());

        map.put(road, newOrder);

        switch (index){
            case 0:
                match.setGuildId1Map(Utils.mapIntListLongToJSON(map));
                break;
            case 1:
                match.setGuildId2Map(Utils.mapIntListLongToJSON(map));
                break;
            case 2:
                match.setGuildId3Map(Utils.mapIntListLongToJSON(map));
                break;
            case 3:
                match.setGuildId4Map(Utils.mapIntListLongToJSON(map));
                break;
        }
        return Collections.unmodifiableList(newOrder);
    }

    public void gvgPlayVideo(long guildId, long vid, int source){

    }

    public void gvgFightInfo(long guildId, int road){

    }

    public List<Long> gvgRoadHumanIdList(long guildId, int type, int road){
        long matchId = Utils.longValue(guildIdMatchIdMap.get(guildId));
        GuildLeagueMatchWarmUp match = matchMap.get(matchId);
        if(match == null){
            return new ArrayList<>();
        }
        long id = 0;
        if(type == GuildParamKey.guild_gvg_type_1){// 自己
            id = guildId;
        } else if(type == GuildParamKey.guild_gvg_type_2){// 对手
            int round = GuildManager.inst().guildLeagueRound();
            id = getRivalId(round, guildId);
        }
        if(id <= 0){
            return new ArrayList<>();
        }

        Map<Integer, List<Long>> map = getRoadHumanIdListMap(id);
        List<Long> humanIdList = map.get(road);
        if(humanIdList == null){
            humanIdList = new ArrayList<>();
            map.put(road, humanIdList);
        }
        return humanIdList;
    }


    private Map<Long, Long> getFactionIdRivalIdMap(int day){
        Map<Long, Long> factionIdRivalIdMap = dayGuildIdRivalIdMap.get(day);
        if(factionIdRivalIdMap == null){
            factionIdRivalIdMap = new HashMap<>();
            dayGuildIdRivalIdMap.put(day, factionIdRivalIdMap);
        }
        return factionIdRivalIdMap;
    }

    private void createMatch(LeagueMatchPair pair) {
        createMatch(pair, 0);
    }

    private void createMatch(LeagueMatchPair pair, int serverId) {
        GuildLeagueMatchWarmUp match = new GuildLeagueMatchWarmUp();
        match.setId(Port.applyId());
        match.setSeasonSn(pair.season);
        match.setWeek(pair.week);
        match.setGroupPvp(pair.group);
        match.setGradeType(pair.gradeType);
        match.setServerId(serverId);
        match.setOpenTime(Port.getTime());
        match.setGuildIdList(Utils.listToString(pair.guildIdList));
        Map<Integer, Map<Long, Long>> battleNumGuildIdMap = battleNumGuildIdMap(pair.guildIdList);
        match.setBattleNumGuildIdMap(Utils.mapIntMapLongLongToJSON(battleNumGuildIdMap));
        match.persist();
        pair.matchId = match.getId();
        addMatch(match);

        List<String> keyList = new ArrayList<>();
        keyList.add(RedisKeys.bridge_date_group_grade_guild_rank + match.getId());
        for(long guildId : pair.guildIdList){
            keyList.add("0");
            keyList.add(String.valueOf(guildId));
        }
        RedisTools.zadd(keyList);
        RedisTools.addToSet(EntityManager.redisClient, RedisKeys.guild_league_remove_list, RedisKeys.bridge_date_group_grade_guild_rank + match.getId());
        RedisTools.addToSet(EntityManager.redisClient, RedisKeys.guild_league_remove_list, RedisKeys.bridge_date_group_grade_human_rank + match.getId());
    }

    public static Map<Integer, Map<Long, Long>> battleNumGuildIdMap(List<Long> pairGuildIdList){
        Map<Integer, Map<Long, Long>> battleNumGuildIdMap = new HashMap<>();
        int size = pairGuildIdList.size();
        if(size == 2){ // 双宗门赛，特殊处理，打三天
            long guildId1 = pairGuildIdList.get(0);
            long guildId2 = pairGuildIdList.get(1);
            Map<Long, Long> mapNew = new HashMap<>();
            mapNew.put(guildId1, guildId2);
            for(int i = GuildParamKey.battleNum_1; i <= GuildParamKey.sumBattleNum; i++){
                battleNumGuildIdMap.put(i, mapNew);
            }
            return battleNumGuildIdMap;
        }

        Map<Long, List<Long>> idIdListMap = new HashMap<>();
        for(int m = 0; m < size; m+=2){
            long guildId1 = pairGuildIdList.get(m);
            long guildId2 = 0;
            if(m + 1 < size){
                guildId2 = pairGuildIdList.get(m + 1);
            }

            List<Long> idListNew = new ArrayList<>();
            idListNew.add(guildId2);
            idIdListMap.put(guildId1, idListNew);

            if(guildId2 > 0){
                List<Long> idListNew2 = new ArrayList<>();
                idListNew2.add(guildId1);
                idIdListMap.put(guildId2, idListNew2);
            }
            Map<Long, Long> mapNew = battleNumGuildIdMap.get(GuildParamKey.battleNum_1);
            if(mapNew == null){
                mapNew = new HashMap<>();
            }
            mapNew.put(guildId1, guildId2);
            battleNumGuildIdMap.put(GuildParamKey.battleNum_1, mapNew);
        }


        for(int i = GuildParamKey.battleNum_2; i <= GuildParamKey.sumBattleNum; i++){
            Map<Long, Long> mapNew = new HashMap<>();
            battleNumGuildIdMap.put(i, mapNew);
            for(int m = 0; m < size; m++){
                long guildId = pairGuildIdList.get(m);
                if(mapNew.containsKey(guildId) || mapNew.containsValue(guildId)){
                    continue;
                }
                List<Long> idListNew = new ArrayList<>(pairGuildIdList);
                List<Long> useIdList = idIdListMap.get(guildId);
                idListNew.removeAll(useIdList);
                idListNew.remove(guildId);
                idListNew.removeAll(mapNew.keySet());
                idListNew.removeAll(mapNew.values());

                if(idListNew.isEmpty()){
                    mapNew.put(guildId, 0L);
                    continue;
                }
                long enemyId = idListNew.get(0);
                mapNew.put(guildId, enemyId);
                useIdList.add(enemyId);
                List<Long> useIdList2 = idIdListMap.get(enemyId);
                useIdList2.add(guildId);
            }
        }
        return battleNumGuildIdMap;
    }

    public long getRivalId(int day, long factionId){
        Map<Long, Long> factionIdRivalIdMap = getFactionIdRivalIdMap(day);
        if(factionIdRivalIdMap.containsKey(factionId)){
            return factionIdRivalIdMap.get(factionId);
        } else if(factionIdRivalIdMap.containsValue(factionId)){
            for(Map.Entry<Long,Long> entry : factionIdRivalIdMap.entrySet()){
                if(entry.getValue() == factionId){
                    return entry.getKey();
                }
            }
        }
        return -1;
    }

    public void matchWarmUp(int season, int week, int groupType, List<Long> guildIdList, int serverId, Map<Long, GuildLeagueRecordWarmUp> guildMap){
        try {
            if(guildIdList == null || guildIdList.isEmpty()){
                return;
            }
            Set<Long> guildIdSet = new HashSet<>();
            Collections.shuffle(guildIdList);
            int num = 4;
            int loopNum = 100000000;
            while (!guildIdList.isEmpty()) {
                List<Long> idListTemp = new ArrayList<>();
                for (Long guildId : guildIdList) {
                    if (idListTemp.size() >= num) {
                        break;
                    }
                    if (guildIdSet.add(guildId)) {
                        if(matchGuildIdList.add(guildId)){
                            idListTemp.add(guildId);
                        }
                    }
                }
                if (!idListTemp.isEmpty()) {
                    LeagueMatchPair pair = new LeagueMatchPair(season, week, idListTemp, groupType, serverId);
                    createMatch(pair, serverId);
                    guildIdList.removeAll(idListTemp);
                } else {
                    matchGuildIdList.containsAll(guildIdList);
                    break;
                }
                loopNum--;// 防止死循环
                if(loopNum <= 0){
                    break;
                }
            }
        } catch (Exception e) {
            Log.temp.error("===匹配过程中发生异常: {}", e.getMessage());
        }

    }


    public long getThreeBattleTime(){
        long timeNow = Port.getTime();
        ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.familybattle_detail_time.SN);
        int[][] intArr = Utils.parseIntArray2(confGlobal.strValue);
        long weekTwoTime = Utils.getTimeOfWeek(timeNow, 2, 0);
        int sec = 0;
        for(int i = 0; i < intArr.length; i++){
            sec += intArr[i][1];
            if(i >= 3){
                break;
            }
        }
        return weekTwoTime + (sec + intArr[5][1]) * Time.SEC;
    }

    public long getFourBattleTime(){
        long timeNow = Port.getTime();
        ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.familybattle_detail_time.SN);
        int[][] intArr = Utils.parseIntArray2(confGlobal.strValue);
        long weekTwoTime = Utils.getTimeOfWeek(timeNow, 2, 0);
        int sec = 0;
        for(int i = 0; i < intArr.length; i++){
            sec += intArr[i][1];
            if(i >= 7){
                break;
            }
        }
        return weekTwoTime + sec * Time.SEC;
    }


    public long getFiveBattleTime(){
        long timeNow = Port.getTime();
        ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.familybattle_detail_time.SN);
        int[][] intArr = Utils.parseIntArray2(confGlobal.strValue);
        long weekTwoTime = Utils.getTimeOfWeek(timeNow, 2, 0);
        int sec = 0;
        for(int i = 0; i < intArr.length; i++){
            sec += intArr[i][1];
            if(i >= 11){
                break;
            }
        }
        return weekTwoTime + sec * Time.SEC;
    }


    private void updateRoadWinStatus(long winGuildId, long loseGuildId, int road){
        int round = GuildManager.inst().guildLeagueRound();
        Map<Long, Map<Integer, Integer>> guildRoadStatusMap = roundGuildRoadStatusMap.getOrDefault(round, new HashMap<>());
        roundGuildRoadStatusMap.put(round, guildRoadStatusMap);

        Map<Integer, Integer> roadStatusMap = guildRoadStatusMap.getOrDefault(winGuildId, new HashMap<>());
        guildRoadStatusMap.put(winGuildId, roadStatusMap);
        roadStatusMap.put(road, GuildParamKey.roadWin);

        Map<Integer, Integer> roadStatusMapLose = guildRoadStatusMap.getOrDefault(loseGuildId, new HashMap<>());
        guildRoadStatusMap.put(loseGuildId, roadStatusMapLose);
        roadStatusMapLose.put(road, GuildParamKey.roadLose);
    }

    public int getRoadStatus(int round, long guildId, int road){
        return roundGuildRoadStatusMap.getOrDefault(round, new HashMap<>()).getOrDefault(guildId, new HashMap<>()).getOrDefault(road, GuildParamKey.roadTwo);
    }

    private void addTopGuildLog(int season, long guildId, long humanId, int score, long combat){
        if(humanId <= 0){
            return;
        }
        JSONObject jo = new JSONObject();
        jo.put("season", season);
        jo.put("humanId", humanId);
        jo.put("power", combat);
        jo.put("score", score);
        jo.put("round", GuildManager.inst().guildLeagueRound());

        JSONArray ja = Utils.toJSONArray(Utils.getRedisStrValue(RedisKeys.bridge_pvp_guild_top_log + guildId));
        ja.add(jo);
        if(ja.size() > ParamKey.guildTopLogNum){
            int num = ja.size() - ParamKey.guildTopLogNum;
            for(int i = 0; i < num; i++){
                ja.remove(0);
            }
        }
        RedisTools.set(EntityManager.redisClient, RedisKeys.bridge_pvp_guild_top_log + guildId, ja.toJSONString(), r->{
            if(!r.succeeded()){
                Log.temp.error("===保存失败，key={}， json={}", RedisKeys.bridge_pvp_guild_top_log + guildId, ja.toJSONString());
            }
        });

    }

    private List<Long> getGuildHumanIdAllList(long guildId){
        Map<Integer, List<Long>> map = getRoadHumanIdListMap(guildId);
        List<Long> humanIdList = new ArrayList<>();
        if(map == null){
            return humanIdList;
        }
        for(List<Long> idList : map.values()){
            humanIdList.addAll(idList);
        }
        return humanIdList;
    }

    public void autoJoinHumanIdList(long guildId, List<Long> humanIdList){
        long matchId = getMacthId(guildId);
        GuildLeagueMatchWarmUp match = matchMap.get(matchId);
        if(match == null){
            return;
        }
        List<Long> guildIdList = Utils.strToLongList(match.getGuildIdList());
        int index = guildIdList.indexOf(guildId);
        if(index == -1){
            return;
        }
        Map<Integer, List<Long>> map = getRoadHumanIdListMap(guildId);
        List<Long> joinList = new ArrayList<>();
        int road1 = 0;
        int road2 = 0;
        int road3 = 0;
        for(int road = 1; road <= 3; road++){
            List<Long> tempIdList =  map.getOrDefault(road, new ArrayList<>());
            if(road == 1){
                road1 = tempIdList.size();
            } else if(road == 2){
                road2 = tempIdList.size();
            } else if(road == 3){
                road3 = tempIdList.size();
            }
            joinList.addAll(tempIdList);
        }
        humanIdList.removeAll(joinList);

        for(long humanId : humanIdList){
            int minRoad = road1 < road2 ? (road1 < road3 ? 1 : 3) : (road2 < road3 ? 2 : 3);
            map.get(minRoad).add(humanId);
            if(minRoad == 1){
                ++road1;
            } else if(minRoad == 2){
                ++road2;
            } else if(minRoad == 3){
                ++road3;
            }
        }

        switch (index){
            case 0:
                match.setGuildId1Map(Utils.mapIntListLongToJSON(map));
                break;
            case 1:
                match.setGuildId2Map(Utils.mapIntListLongToJSON(map));
                break;
            case 2:
                match.setGuildId3Map(Utils.mapIntListLongToJSON(map));
                break;
            case 3:
                match.setGuildId4Map(Utils.mapIntListLongToJSON(map));
                break;
            default:
                Log.temp.error("===index={}", index);
                break;
        }
        match.update();
    }

    public void clearRoad(){

    }

    public void log(){

    }



    private long byeBattleHumanAll(long guildId, int group, int grade, long matchId){
        List<Long> humanIdList = getGuildHumanIdAllList(guildId);
        ConfGlobal confByeReward = ConfGlobal.get(ConfGlobalKey.familybattle_nullenemy_reward.SN);
        // 个人积分
        ConfGlobal confHumanScore = ConfGlobal.get(ConfGlobalKey.familybattle_nullenemy_selfpoint_reward.SN);
        // 家族积分
        ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.familybattle_nullenemy_familypoint_reward.SN);

        String redisKeyHuman = RedisKeys.bridge_date_group_grade_human_rank + matchId;

        int mailSn = 10130;//家族乱斗轮空奖励
        // 奖励
        String byeItemJSON = Utils.mapIntIntToJSON(Utils.intArrToIntMap(new HashMap<>(), Utils.parseIntArray2(confByeReward.strValue)));

        for(long humanId : humanIdList){
            String idStr = String.valueOf(humanId);

            // 加入乱斗组本周（4个公会的玩家排行）
            RedisTools.addScore(redisKeyHuman, humanId, confHumanScore.value);
            // 加入乱斗组个人排行榜
            RedisTools.addScore(RedisKeys.bridge_pvp_group_human_rank + Config.SERVER_ID, humanId, confHumanScore.value);

            JSONObject jo = new JSONObject();
            JSONObject joTemp = new JSONObject();
            joTemp.put(MailManager.MAIL_K_4, confGlobal.value);
            jo.put(MailManager.MAIL_PARAM_1, joTemp);
            MailManager.inst().sendMail(humanId, MailManager.SYS_SENDER, mailSn, "", jo.toJSONString(), byeItemJSON, null);

            // 排行榜加积分，对应的物品要加
            Param paramLine = new Param();
            paramLine.put("addScore", confHumanScore.value + confGlobal.value);
            HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance();
            proxy.pockLineAdd(Utils.longValue(humanId), PocketLineEventSubKey.GUILD_LEAGUE_ADD_SCORE, paramLine.toJsonString());
        }
        winGuildIdList.add(guildId);
        guildIdAddScoreAllMap.put(guildId, confGlobal.value);

        String redisKeyGuild = RedisKeys.bridge_date_group_grade_guild_rank + matchId;
        // 乱斗组本周（4个公会排行）
        RedisTools.addScore(redisKeyGuild, guildId, confGlobal.value);
        // 同一个乱斗组排行（跨服1/本服所有公会）, score(工会积分)， 工会id
        RedisTools.addScore(RedisKeys.bridge_pvp_group_guild_rank + Config.SERVER_ID, guildId, confGlobal.value);
        Log.temp.info("===乱斗结算公会，guildId={}, addScore={}", guildId, confGlobal.value);

        return guildId;
    }

    public boolean pulseBattle(int indexPos){
        try {
            boolean isEndAll = true;
            for (GuildGvgBattle battle : battleAllList) {
                try{
                    battle.pulseBattleWarmUp(indexPos);
                    if (battle.winGuildId <= 0) {
                        int i = GuildLeagueUtils.nowStep();
                        if (i == 6 || i == 11 || i == 15){
                            for(int road = 1; road <= GuildParamKey.pathMax; road++){
                                if(!battle.roadWinGuildIdMap.containsKey(road)){
                                    battle.roadWinGuildIdMap.put(road, battle.guildIdA);
                                    battle.settleRoadWinError(road, battle.guildIdA, battle.guildIdB);
                                }
                            }
                            battle.winGuildId = battle.guildIdA;
                            if (!battle.isSettle) {
                                battle.isSettle = true;
                                updateGuildScoreSettle(battle);
                            }
                        }else{
                            isEndAll = false;
                        }
                    } else{
                        if (!battle.isSettle) {
                            battle.isSettle = true;
                            updateGuildScoreSettle(battle);
                        }
                    }
                }catch (Exception e){
                    Log.temp.error(" pulseBattle error ", e);
                }
            }
            return isEndAll;
        } catch (Exception e) {
            Log.temp.error(" pulseBattle error ", e);
            return true;
        }
    }

    private void updateGuildScoreSettle(GuildGvgBattle battle){

        if(!winGuildIdList.contains(battle.winGuildId)){
            winGuildIdList.add(battle.winGuildId);
        }
        String redisKeyGuild = RedisKeys.bridge_date_group_grade_guild_rank + battle.matchId;
        String redisKeyHuman =RedisKeys.bridge_date_group_grade_human_rank + battle.matchId;

        Log.temp.info("===乱斗战斗结算奖励，redisKeyGuild={}, redisKeyHuman={}, winGuildId={}", redisKeyGuild, redisKeyHuman, battle.winGuildId);

        List<Long> humanIdListA = battle.roadHumanListMapA.values().stream().flatMap(List::stream).collect(Collectors.toList());
        List<Long> humanIdListB = battle.roadHumanListMapB.values().stream().flatMap(List::stream).collect(Collectors.toList());

        ConfFamiliyBrawl conf = ConfFamiliyBrawl.get(ParamKey.familiyBrawlSn);
        // 奖励
        Map<Integer, Integer> winItemMap = Utils.intArrToIntMap(new HashMap<>(), conf.win_reward);
        Map<Integer, Integer> loseItemMap = Utils.intArrToIntMap(new HashMap<>(), conf.lose_reward);

        settleScore(battle, battle.guildIdA, battle.guildIdA == battle.winGuildId, redisKeyGuild, redisKeyHuman, humanIdListA, battle.guildIdA == battle.winGuildId ? winItemMap : loseItemMap);
        settleScore(battle, battle.guildIdB, battle.guildIdB == battle.winGuildId, redisKeyGuild, redisKeyHuman, humanIdListB, battle.guildIdB == battle.winGuildId ? winItemMap : loseItemMap);

        guildIdBattleMap.put(battle.guildIdA, battle);
        guildIdBattleMap.put(battle.guildIdB, battle);


        if(battle.roadWinGuildIdMap.isEmpty()){
            updateRoadWinStatus(battle.winGuildId, battle.guildIdA == battle.winGuildId ? battle.guildIdB : battle.guildIdA, battle.round);
        }
        for(Map.Entry<Integer, Long> entry : battle.roadWinGuildIdMap.entrySet()){
            updateRoadWinStatus(entry.getValue(), battle.guildIdA == entry.getValue() ? battle.guildIdB : battle.guildIdA, entry.getKey());
        }
        GuildLeagueMatchWarmUp match = matchMap.get(battle.matchId);
        if(match != null){
            Map<Integer, List<Long>> dayWinIdListMap = Utils.jsonToMapIntListLong(match.getBattleNumWinGuildIdMap());
            if (dayWinIdListMap == null) {
                dayWinIdListMap = new HashMap<>();
            }
            List<Long> winIDList = dayWinIdListMap.get(battle.round);
            if (winIDList == null) {
                winIDList = new ArrayList<>();
                dayWinIdListMap.put(battle.round, winIDList);
            }
            if(!winIDList.contains(battle.winGuildId)){
                winIDList.add(battle.winGuildId);
            }
            match.setBattleNumWinGuildIdMap(Utils.mapIntListLongToJSON(dayWinIdListMap));
            match.setBattleNumGuildIdRoadMap(Utils.mapIntLongMapIntIntToJSON(roundGuildRoadStatusMap));
            match.update();
            Log.temp.info("===战斗结算，matchId={}, BattleNumGuildIdRoadMap={}, roadWinGuildIdMap={}",
                    match.getId(),  match.getBattleNumGuildIdRoadMap(), battle.roadWinGuildIdMap);
        }
    }

    private void settleScore(GuildGvgBattle battle, long guildId, boolean isWin, String redisKeyGuild,String redisKeyHuman, List<Long> humanIdList, Map<Integer,Integer> itemMap ){
        if(guildId <= 0 || humanIdList.isEmpty()){
            Log.temp.error("===工会结算，没有人，guildId={}，humanIdList={}", guildId, humanIdList);
            return;
        }
        ConfFamiliyBrawl conf = ConfFamiliyBrawl.get(ParamKey.familiyBrawlSn);

        int sumKillNum = 0;
        int topKillNum = 0;
        long topHumanId = 0;
        int topScore = 0;

        int serNo = Utils.getServerIdByHumanId(humanIdList.get(0));
        List<RankInfo> rankInfoList = new ArrayList<>();
        //家族乱斗奖励
        int mailSn = ParamKey.guildLeagueWinMailSn;
        if(!isWin){
            mailSn = ParamKey.guildLeagueLoseMailSn;
        }
        Map<Long, LeagueVO> leagueVOMap = new HashMap<>();
        for(long humanId : humanIdList){
            try {
                int killNum = battle.humanIdKillNumMap.getOrDefault(humanId, 0);
                int addScore = 0;
                if (isWin) {
                    addScore = conf.win_score + killNum * conf.kill_score;
                } else {
                    addScore = conf.lose_score + killNum * conf.kill_score;
                }
                RankInfo rankInfo = new RankInfo();
                rankInfo.humanId = humanId;
                rankInfo.param = killNum;
                rankInfoList.add(rankInfo);

                sumKillNum += killNum;

                // 加入乱斗组本周（4个公会的玩家排行）
                RedisTools.addScore(redisKeyHuman, humanId, addScore);
                // 加入乱斗组个人排行榜
                RedisTools.addScore(RedisKeys.bridge_pvp_group_human_rank + serNo, humanId, addScore);
                Log.temp.info("===乱斗结算个人，guildId={},humanId={}, addScore={}, killNum={}", guildId, humanId, addScore, killNum);

                LeagueVO info = new LeagueVO();
                info.voId = humanId;
                info.intValue = mailSn;
                info.param.put("itemJSON", Utils.mapIntIntToJSON(itemMap));
                info.param.put("killNum", killNum);
                leagueVOMap.put(humanId, info);

//                MailManager.inst().sendMail(humanId, MailManager.SYS_SENDER, mailSn, "", "", Utils.mapIntIntToJSON(itemMap), param);

                if (topKillNum < killNum) {
                    topKillNum = killNum;
                    topHumanId = humanId;
                    topScore = addScore;
                }
            }catch (Exception e){
                Log.temp.error(" settleScore 发奖励失败， humanId={}, error ", humanId, e);
            }
        }

        ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.familybattle_point.SN);
        int winNumAddScore = confGlobal.intArray[0];
        int joinAddScore = confGlobal.intArray[1];
        // 个人战斗分=我的胜场数*50
        // 家族战斗积分=家族胜败基础分+胜场*20+参与人数*5
        // 胜败基础分读取FamiliyBrawl_家族乱斗表family_basic_score（失败）/family_break_score（胜利）
        int addScore = sumKillNum * winNumAddScore + joinAddScore * humanIdList.size();
        if(isWin){
            addScore += conf.family_break_score;
        } else {
            addScore += conf.family_basic_score;
        }
        guildIdAddScoreAllMap.put(guildId, addScore);

        // 乱斗组本周（4个公会排行）
        RedisTools.addScore(redisKeyGuild, guildId, addScore);
        // 同一个乱斗组排行（跨服1/本服所有公会）, score(工会积分)， 工会id
        RedisTools.addScore(RedisKeys.bridge_pvp_group_guild_rank + serNo, guildId, addScore);
        Log.temp.info("===乱斗结算公会，guildId={}, addScore={}", guildId, addScore);

        Collections.sort(rankInfoList, (a, b) -> {
            int ret = 0;// 0默认相等
            if (a != null && b != null) {
                if (a.param < b.param) {
                    ret = 1;
                } else if (a.param > b.param) {
                    ret = -1;
                }
            }
            return ret;
        });

        int week = Utils.getDayOfWeek() - 1;

        JSONArray ja = new JSONArray();
        for(RankInfo rankInfo : rankInfoList){
            JSONObject jo = new JSONObject();
            jo.put("id", rankInfo.humanId );
            jo.put("num", Utils.intValue(rankInfo.param));
            ja.add(jo);
            if(ja.size() >= 3){
                break;
            }
        }
        for(long humanId : humanIdList) {
            try {
                JSONObject settleJo = new JSONObject();
                int killNum = battle.humanIdKillNumMap.getOrDefault(humanId, 0);
                settleJo.put("killNum", killNum);
                int sumScore = 0;
                if(guildId == battle.winGuildId){
                    settleJo.put("status", 1);//0 失败 1 胜利
                    settleJo.put("statusScore",conf.win_score);
                    settleJo.put("selfScore",  killNum * conf.kill_score);
                    sumScore += conf.win_score + killNum * conf.kill_score;
                } else {
                    settleJo.put("status",  0);//0 失败 1 胜利
                    settleJo.put("statusScore", conf.lose_score);
                    settleJo.put("selfScore", killNum * conf.kill_score);
                    sumScore += conf.lose_score + killNum * conf.kill_score;
                }
                settleJo.put("guildScore", addScore);
                settleJo.put("totalScore", addScore + sumScore);
                settleJo.put("rankInfo", ja);
                RedisTools.setAndExpire(EntityManager.redisClient, RedisKeys.guild_human_week_settle + week + humanId, settleJo.toJSONString(), (int)(Time.DAY / Time.SEC));

                if(addScoreIdList.add(humanId)){
                    LeagueVO info = leagueVOMap.get(humanId);
                    if(info == null){
                        info = new LeagueVO();
                        info.voId = humanId;
                    }
                    info.param.put("addScore", addScore + sumScore);
                }
            }catch (Exception e){
                Log.temp.error(" settleScore 个人排行榜失败， humanId={}, error ", humanId, e);
            }
        }

        try {
            HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance();
            proxy.sendLeagueSettleScore(leagueVOMap);
        }catch (Exception e){
            Log.temp.error("===公会结算, guildId={}", guildId, e);
        }

        if(battle.guildIdA == guildId){
            battle.rankInfoListA = rankInfoList.subList(0, Math.min(rankInfoList.size(), 3));
        } else if(battle.guildIdB == guildId){
            battle.rankInfoListB = rankInfoList.subList(0, Math.min(rankInfoList.size(), 3));
        }

        HumanData humanData = battle.humanIdDataMap.get(topHumanId);
        long combat = humanData == null ? 0 : Utils.longValue(humanData.human.getCombat());
        addTopGuildLog(battle.season, guildId, topHumanId, topScore, combat);
    }


    public Map<Long, Integer> getGuildIdAddScoreAllMap(){
        return guildIdAddScoreAllMap;
    }

    public List<GuildGvgBattle> getBattleAllList(){
        return battleAllList;
    }


    public MsgGvg.gvg_fight_settlement_s2c getMsg_gvg_fight_settlement_s2c(long guildId, long humanId) {
        MsgGvg.gvg_fight_settlement_s2c.Builder msg = MsgGvg.gvg_fight_settlement_s2c.newBuilder();
        GuildGvgBattle battle = guildIdBattleMap.get(guildId);
        if(battle == null){
//            Log.temp.info("===没有战斗记录，应该轮空了，guildId={}", guildId);
            return null;
        }

        int killNum = battle.humanIdKillNumMap.getOrDefault(humanId, 0);
        msg.setStatus(guildId == battle.winGuildId ? 1 : 0);//0 失败 1 胜利
        msg.setKillNum(killNum);
        ConfFamiliyBrawl conf = ConfFamiliyBrawl.get(ParamKey.familiyBrawlSn);
        msg.setStatusScore(guildId == battle.winGuildId ? conf.win_score : conf.lose_score);
        msg.setSelfScore(killNum * conf.kill_score);
        msg.setGuildScore(guildIdAddScoreAllMap.getOrDefault(guildId, 0));
        msg.setTotalScore(msg.getStatusScore() + msg.getSelfScore() + msg.getGuildScore());

        if(guildId == battle.winGuildId){
            msg.addAllRewardList(InstanceManager.inst().to_p_rewardList(conf.win_reward));
        } else {
            msg.addAllRewardList(InstanceManager.inst().to_p_rewardList(conf.lose_reward));
        }
        List<RankInfo> rankInfoList = new ArrayList<>();
        if(battle.guildIdA == guildId){
            rankInfoList =battle.rankInfoListA;
        } else if(battle.guildIdB == guildId){
            rankInfoList =battle.rankInfoListB;
        }
        int rank = 1;
        for(RankInfo rankInfo : rankInfoList){
            Define.p_gvg_player dInfo = GuildManager.inst().to_p_gvg_player(rankInfo.humanId, rank, Utils.intValue(rankInfo.param));
            if(dInfo != null){
                msg.addTopThree(dInfo);
                rank++;
            }
            if(rank > 3){
                break;
            }
        }

        return msg.build();
    }

    public void removeMatchGuild(long guildId){
        boolean match = guildIdMatchIdMap.containsKey(guildId);
        if (!match){
            return;
        }
        Long matchId = guildIdMatchIdMap.get(guildId);

        guildIdPoadHumanIdMap.remove(guildId);
        //去除持久化
        GuildLeagueMatchWarmUp warmUp = matchMap.get(matchId);
        String _guildIdList = warmUp.getGuildIdList();
        List<Long> guildIdList = Utils.strToLongList(_guildIdList);
        guildIdList.remove(guildId);
        warmUp.setGuildIdList(Utils.listToString(guildIdList));

        String _battleNumGuildIdMap = warmUp.getBattleNumGuildIdMap();
        Map<Integer, Map<Long, Long>> battleNumGuildIdMap = Utils.jsonToIntMapLongLong(_battleNumGuildIdMap);
        Map<Integer, Map<Long, Long>> battleNumGuildIdMap2 = new HashMap<>();
        for (Map.Entry<Integer, Map<Long, Long>> entry : battleNumGuildIdMap.entrySet()) {
            Integer day = entry.getKey();
            Map<Long, Long> valueMap = entry.getValue();
            Map<Long, Long> valueMap2 = new HashMap<>();
            if (valueMap.containsKey(guildId)){
                Long aLong = valueMap.get(guildId);
                valueMap2.put(aLong, 0L);
            }
            if (valueMap.containsValue(guildId)){
                for (Map.Entry<Long, Long> entry2 : valueMap.entrySet()) {
                    if (entry2.getValue() == guildId){
                        valueMap2.put(entry2.getKey(), 0L);
                        break;
                    }
                }
            }
            battleNumGuildIdMap2.put(day, valueMap2);
        }
        warmUp.setBattleNumGuildIdMap(Utils.mapIntMapLongLongToJSON(battleNumGuildIdMap2));
        warmUp.update();


        //移除内存中的
        for (Integer day : dayGuildIdRivalIdMap.keySet()) {
            Map<Long, Long> guildIdRivalIdMap = dayGuildIdRivalIdMap.get(day);
            Map<Long, Long> guildIdRivalIdMap2 = new HashMap<>();
            if (guildIdRivalIdMap.containsKey(guildId)){
                Long aLong = guildIdRivalIdMap.get(guildId);
                guildIdRivalIdMap2.put(aLong, 0L);
            }
            if (guildIdRivalIdMap.containsValue(guildId)){
                for (Map.Entry<Long, Long> entry2 : guildIdRivalIdMap.entrySet()) {
                    if (entry2.getValue() == guildId){
                        guildIdRivalIdMap2.put(entry2.getKey(), 0L);
                        break;
                    }
                }
            }
            dayGuildIdRivalIdMap.put(day, guildIdRivalIdMap2);
        }

        guildIdMatchIdMap.remove(guildId);
        guildIdRoadBattleNumIdMap.remove(guildId);
        guildIdAddScoreAllMap.remove(guildId);
        guildIdBattleMap.remove(guildId);
        guildIdIndexMap.remove(guildId);
        winGuildIdList.remove(guildId);
    }

    public void removeServerId(int serverId){
        List<Long> removeList = new ArrayList<>();
        for(GuildLeagueMatchWarmUp match : matchMap.values()){
            if(match.getServerId() == serverId){
                removeList.add(match.getId());
                List<Long> guildIdList = Utils.strToLongList(match.getGuildIdList());
                for(long guildId : guildIdList){
                    guildIdMatchIdMap.remove(guildId);
                }
            }
        }
        for(long id : removeList){
            matchMap.remove(id);
        }
//        seasonWeekMatchIdMap.remove(serverId);

        guildIdPoadHumanIdMap.clear();
        dayGuildIdRivalIdMap.clear();
        roundGuildRoadStatusMap.clear();
        for(GuildLeagueMatchWarmUp match : matchMap.values()){
            addGuildIdPoadHumanIdMap(match);
            addPairListMap(match);
            Map<Integer,Map<Long, Map<Integer, Integer>>> map = Utils.jsonToIntLongMapIntInt(match.getBattleNumGuildIdRoadMap());
            for(Map.Entry<Integer, Map<Long, Map<Integer, Integer>>> entry : map.entrySet()){
                int round = entry.getKey();
                Map<Long, Map<Integer, Integer>> guildIdRoadMap = entry.getValue();
                Map<Long, Map<Integer, Integer>> map2 = roundGuildRoadStatusMap.getOrDefault(round, new HashMap<>());
                for(Map.Entry<Long, Map<Integer, Integer>> guildEntry :guildIdRoadMap.entrySet()){
                    if(map2.containsKey(guildEntry.getKey())){
                        continue;
                    }
                    map2.put(guildEntry.getKey(), guildEntry.getValue());
                }
                roundGuildRoadStatusMap.put(round, map2);
            }
        }
    }

    public GuildGvgBattle getGuildBattle(long guildId){
        return guildIdBattleMap.get(guildId);
    }


    public void gm(Param param){

    }

    public void gm2(Object... args){

    }

    public void gm3(Object... args){
        for (GuildGvgBattle battle : battleAllList) {
            try{
                if (!battle.isSettle) {
                    updateGuildScoreSettle(battle);
                    battle.isSettle = true;
                }
            }catch (Exception e){
                Log.temp.error(" pulseBattle error ", e);
            }
        }
    }

    // =========================== TODO 改成19点开始先拉人===============================
    public void guildBattleGetHumanData(Map<Long, GuildLeagueRecordWarmUp> guildRecordMap){
        battleAllList.clear();
        getIndex = 0;
        int round = GuildManager.inst().guildLeagueRound();

        Log.temp.info("===乱斗开始拉人，round={}", round);

        long timeNow = Port.getTime();
        long timeWeekOne = Utils.getTimeOfWeek(timeNow, 1, 0);
        String dateStr = Utils.formatTime(timeWeekOne, "yyyy-MM-dd");
        for(GuildLeagueMatchWarmUp match : matchMap.values()){
            if(match == null){
                continue;
            }
            if(!Utils.isSameWeek(match.getOpenTime(), timeNow)){
                continue;
            }
            Map<Integer, Map<Long, Long>> battleNumGuildIdMap = Utils.jsonToIntMapLongLong(match.getBattleNumGuildIdMap());
            if(battleNumGuildIdMap == null || battleNumGuildIdMap.isEmpty()){
                continue;
            }
            Map<Long, Long> guildIdMap = battleNumGuildIdMap.get(round);
            if(guildIdMap == null){
                continue;
            }
            for(Map.Entry<Long, Long> entry : guildIdMap.entrySet()){
                long guildId = entry.getKey();
                long guildIdRival = entry.getValue();

                if(guildIdRival <= 0){
                    // 轮空战斗先不处理
                    continue;
                }
                GuildGvgBattle gvgBattle = new GuildGvgBattle(match, round, guildId, guildIdRival, dateStr,
                        getRoadHumanIdListMap(guildId), getRoadHumanIdListMap(guildIdRival));
                battleAllList.add(gvgBattle);
                int index = battleAllList.size() - 1;
                guildIdIndexMap.put(guildId, index);
                guildIdIndexMap.put(guildIdRival, index);
            }
        }
    }

    public void guildBattleStartBye(Map<Long, GuildLeagueRecordWarmUp> guildRecordMap){
        int round = GuildManager.inst().guildLeagueRound();

        long timeNow = Port.getTime();
        long timeWeekOne = Utils.getTimeOfWeek(timeNow, 1, 0);
        String dateStr = Utils.formatTime(timeWeekOne, "yyyy-MM-dd");
        for(GuildLeagueMatchWarmUp match : matchMap.values()){
            if(match == null){
                continue;
            }
            if(!Utils.isSameWeek(match.getOpenTime(), timeNow)){
                continue;
            }
            Map<Integer, Map<Long, Long>> battleNumGuildIdMap = Utils.jsonToIntMapLongLong(match.getBattleNumGuildIdMap());
            if(battleNumGuildIdMap == null || battleNumGuildIdMap.isEmpty()){
                continue;
            }
            Map<Long, Long> guildIdMap = battleNumGuildIdMap.get(round);
            if(guildIdMap == null){
                continue;
            }
            int group = match.getGroupPvp();
            int grade = match.getGradeType();
            for(Map.Entry<Long, Long> entry : guildIdMap.entrySet()){
                long guildId = entry.getKey();
                long guildIdRival = entry.getValue();

                if(guildIdRival <= 0){
                    // 轮空战斗，一定是guildIdRival=0
                    byeBattleHumanAll(guildId, group, grade, match.getId());
                }
            }
        }
    }

    public void getDataBattleAllList(){
        if(battleAllList.isEmpty() || battleAllList.size() <= getIndex){
            return;
        }
        GuildGvgBattle gvgBattle = battleAllList.get(getIndex);
        int index = gvgBattle.loadIndex;
        List<Long> humanIdList = new ArrayList<>();
        boolean isAdd = isAddIndex( true, index, gvgBattle.roadHumanListMapA, humanIdList);
        isAdd = isAddIndex(isAdd, index, gvgBattle.roadHumanListMapB, humanIdList);
        List<Long> removeList = new ArrayList<>();
        for(long humanId : humanIdList){
            if(gvgBattle.humanIdDataMap.containsKey(humanId)){
                removeList.add(humanId);
            }
        }
        humanIdList.removeAll(removeList);
        if(!humanIdList.isEmpty()){
            HumanData.getList(humanIdList, HumanManager.inst().humanClasses, res -> {
                if (!res.succeeded()) {
                    Log.human.error("MemberCallback getMemberAsync error", res.cause());
                    return;
                }
                List<HumanData> humanDataList = res.result();
                for(HumanData humanData : humanDataList){
                    if(humanData == null || humanData.human == null){
                        continue;
                    }
                    gvgBattle.humanIdDataMap.put(humanData.human.getId(), humanData);
                }
            });
        }
        gvgBattle.loadIndex++;
        if(isAdd){
            ++getIndex;
        }
    }

    private boolean isAddIndex(boolean isAdd, int index, Map<Integer, List<Long>> roadHumanListMap, List<Long> humanIdList){
        for(List<Long> roadHumanIdList : roadHumanListMap.values()){
            if(roadHumanIdList == null || roadHumanIdList.isEmpty()){
                continue;
            }
            int roadSize = roadHumanIdList.size();
            int openIndex = index * ParamKey.guildLoadNum;
            if(openIndex >= roadSize){
                continue;
            }
            int endIndex = (index + 1) *  ParamKey.guildLoadNum;
            if(endIndex > roadSize){
                endIndex = roadSize;
            }
            List<Long> tempIdList = roadHumanIdList.subList(openIndex, endIndex);
            if(tempIdList == null || tempIdList.isEmpty()){
                continue;
            }
            humanIdList.addAll(tempIdList);
            isAdd = false;
        }
        return isAdd;
    }
}
