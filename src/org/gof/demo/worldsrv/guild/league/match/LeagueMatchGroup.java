package org.gof.demo.worldsrv.guild.league.match;

import com.alibaba.fastjson.JSONObject;
import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.interfaces.ISerilizable;
import org.gof.demo.worldsrv.bridgeEntity.GuildLeagueEnroll;
import org.gof.demo.worldsrv.bridgeEntity.GuildLeagueEnrollWarmUp;

import java.io.IOException;

public class LeagueMatchGroup implements ISerilizable {
    public long guildId = 0;
    public String name = "";
    public float score = 0;
    public long combat = 0;
    public int serverId = 0;
    public int season = 1;
    public int factionScore = 0;
    public String leaderName ="";
    public String leaderModelSn ="";
    public int groupPvp = 0;
    public int grade = 0;
    public long leaderId = 0;
    public String flagJSON = "";


    public LeagueMatchGroup() {

    }


    public LeagueMatchGroup(GuildLeagueEnroll enroll) {
        guildId = enroll.getGuildId();
        serverId = enroll.getServerId();
        season = enroll.getSeason();
        groupPvp=enroll.getGroupPvp();
        grade = 1;
    }

    public LeagueMatchGroup(GuildLeagueEnrollWarmUp enroll) {
        guildId = enroll.getGuildId();
        serverId = enroll.getServerId();
        season = enroll.getSeason();
        groupPvp= enroll.getGroupPvp();
    }


    @Override
    public void writeTo(OutputStream out) throws IOException {
        out.write(guildId);
        out.write(name);
        out.write(score);
        out.write(combat);
        out.write(serverId);
        out.write(season);
        out.write(factionScore);
        out.write(leaderName);
        out.write(leaderModelSn);
        out.write(groupPvp);
        out.write(grade);
        out.write(leaderId);
        out.write(flagJSON);
    }

    @Override
    public void readFrom(InputStream in) throws IOException {
        guildId = in.read();
        name = in.read();
        score = in.read();
        combat = in.read();
        serverId = in.read();
        season = in.read();
        factionScore = in.read();
        leaderName = in.read();
        leaderModelSn = in.read();
        groupPvp = in.read();
        grade = in.read();
        leaderId = in.read();
        flagJSON = in.read();
    }

    public String toJSON(){
        JSONObject jo = new JSONObject();
        jo.put("guildId", guildId);
        jo.put("name", name);
        jo.put("score", score);
        jo.put("combat", combat);
        jo.put("serverId", serverId);
        jo.put("season", season);
        jo.put("factionScore", factionScore);
        jo.put("leaderName", leaderName);
        jo.put("leaderModelSn", leaderModelSn);
        jo.put("groupPvp", groupPvp);
        jo.put("grade", grade);
        jo.put("leaderId", leaderId);
        jo.put("flagJSON", flagJSON);
        return jo.toJSONString();
    }

}
