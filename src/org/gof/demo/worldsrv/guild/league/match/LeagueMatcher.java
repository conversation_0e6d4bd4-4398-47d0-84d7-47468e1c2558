package org.gof.demo.worldsrv.guild.league.match;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.gof.core.Port;
import org.gof.core.dbsrv.redis.CrossRedis;
import org.gof.core.support.*;
import org.gof.demo.distr.DistrKit;
import org.gof.demo.distr.cross.CrossHumanLoader;
import org.gof.demo.worldsrv.bridgeEntity.GuildLeagueHistory;
import org.gof.demo.worldsrv.bridgeEntity.GuildLeagueMatch;
import org.gof.demo.worldsrv.bridgeEntity.GuildLeagueRecord;
import org.gof.demo.worldsrv.character.HumanObjectServiceProxy;
import org.gof.demo.worldsrv.config.ConfFamiliyBrawl;
import org.gof.demo.worldsrv.config.ConfGlobal;
import org.gof.demo.worldsrv.entity.HumanBrief;
import org.gof.demo.worldsrv.guild.GuildGvgBattleCross;
import org.gof.demo.worldsrv.guild.GuildManager;
import org.gof.demo.worldsrv.guild.GuildParamKey;
import org.gof.demo.worldsrv.guild.league.GuildLeagueUtils;
import org.gof.demo.worldsrv.guild.league.LeagueVO;
import org.gof.demo.worldsrv.human.HumanBriefLoadType;
import org.gof.demo.worldsrv.human.HumanGlobalInfo;
import org.gof.demo.worldsrv.human.HumanGlobalServiceProxy;
import org.gof.demo.worldsrv.instance.InstanceManager;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgGvg;
import org.gof.demo.worldsrv.pocketLine.Pocket;
import org.gof.demo.worldsrv.pocketLine.PocketLineEventSubKey;
import org.gof.demo.worldsrv.rank.RankInfo;
import org.gof.demo.worldsrv.redis.RedisKeys;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.enumKey.ConfGlobalKey;
import org.gof.demo.worldsrv.task.TaskConditionTypeKey;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

public class LeagueMatcher {

    // 赛季，第几周，历史记录
    public Map<Integer, Map<Integer, List<Long>>> seasonWeekHistoryIdMap = new HashMap<>();
    // id，历史信息
    public Map<Long, GuildLeagueHistory> historyMap = new HashMap<>();
    // id， 匹配信息
    public Map<Long, GuildLeagueMatch> matchMap = new HashMap<>();
    // 周几（三四五）宗门id和对手宗门id
    public Map<Integer, Map<Long, Long>> dayGuildIdRivalIdMap = new HashMap<>();
    // 工会id, 上中下路，玩家id集合
    private Map<Long, Map<Integer, List<Long>>> guildIdPoadHumanIdMap = new HashMap<>();

    // 轮次， 工会id, 上中下路，胜利失败状态
    public Map<Integer,Map<Long, Map<Integer, Integer>>> roundGuildRoadStatusMap  = new HashMap<>();
    // 工会id, 匹配id
    public Map<Long, Long> guildIdMatchIdMap = new HashMap<>();

    // 工会id, 分路,  战斗次数, 战斗记录id
//    public Map<Long, Map< Integer, List<Long>>> guildIdRoadBattleNumIdMap = new HashMap<>();


    private Map<Long, Integer> guildIdAddScoreAllMap = new HashMap<>();

    private List<GuildGvgBattleCross> battleAllList = new ArrayList<>();
    public int getIndex = 0;

    public Map<Long, Integer> guildIdIndexMap = new HashMap<>();
    private Map<Long, GuildGvgBattleCross> guildIdBattleMap = new HashMap<>();

    public List<Long> winGuildIdList = new ArrayList<>();
    public Set<Long> addScoreIdList = new HashSet<>();

    public Set<Long> matchGuildIdList = new HashSet<>();
    public Set<Long> settleGuildIdList = new HashSet<>();

    // 已经分配公会对手数据，分批次去创建数据
    public LinkedList<LeagueMatchPair> pairAllList = new LinkedList<>();
    private TickTimer ttPairCreate = new TickTimer(Time.SEC);


    public LeagueMatcher() {

    }

    public void pulse(){
        if(ttPairCreate.isPeriod(Port.getTime())){
            batchesCreateMatch();
        }
    }

    public void addMatch(GuildLeagueMatch match) {
        long matchId = match.getId();
        matchMap.put(matchId, match);

        List<Long> guildIdList = Utils.strToLongList(match.getGuildIdList());
        for(long guildId : guildIdList){
            guildIdMatchIdMap.put(guildId, matchId);
        }
        addGuildIdPoadHumanIdMap(match);
        addPairListMap(match);

        Map<Integer,Map<Long, Map<Integer, Integer>>> map = Utils.jsonToIntLongMapIntInt(match.getBattleNumGuildIdRoadMap());
        for(Map.Entry<Integer, Map<Long, Map<Integer, Integer>>> entry : map.entrySet()){
            int round = entry.getKey();
            Map<Long, Map<Integer, Integer>> guildIdRoadMap = entry.getValue();
            Map<Long, Map<Integer, Integer>> map2 = roundGuildRoadStatusMap.getOrDefault(round, new HashMap<>());
            for(Map.Entry<Long, Map<Integer, Integer>> guildEntry :guildIdRoadMap.entrySet()){
                if(map2.containsKey(guildEntry.getKey())){
                    continue;
                }
                map2.put(guildEntry.getKey(), guildEntry.getValue());
            }
            roundGuildRoadStatusMap.put(round, map2);
        }

    }



    private void addGuildIdPoadHumanIdMap(GuildLeagueMatch match){
        List<Long> guildIdList = Utils.strToLongList(match.getGuildIdList());
        for(int i = 0; i < guildIdList.size(); i++){
            long guildId = guildIdList.get(i);
            switch (i){
                case 0:
                    Map<Integer, List<Long>> map = Utils.jsonToMapIntListLong(match.getGuildId1Map());
                    GuildManager.inst().initRoad(map);
                    guildIdPoadHumanIdMap.put(guildId, map);
                    break;
                case 1:
                    Map<Integer, List<Long>> map2 = Utils.jsonToMapIntListLong(match.getGuildId2Map());
                    GuildManager.inst().initRoad(map2);
                    guildIdPoadHumanIdMap.put(guildId, map2);
                    break;
                case 2:
                    Map<Integer, List<Long>> map3 = Utils.jsonToMapIntListLong(match.getGuildId3Map());
                    GuildManager.inst().initRoad(map3);
                    guildIdPoadHumanIdMap.put(guildId, map3);
                    break;
                case 3:
                    Map<Integer, List<Long>> map4 = Utils.jsonToMapIntListLong(match.getGuildId4Map());
                    GuildManager.inst().initRoad(map4);
                    guildIdPoadHumanIdMap.put(guildId, map4);
                    break;
            }
        }
    }

    public long getMacthId(long guildId){
        return guildIdMatchIdMap.getOrDefault(guildId, 0L);
    }

    private void addPairListMap(GuildLeagueMatch match){
        Map<Integer, Map<Long, Long>> battleNumGuildIdMap = Utils.jsonToIntMapLongLong(match.getBattleNumGuildIdMap());
        if (battleNumGuildIdMap == null || battleNumGuildIdMap.isEmpty()) {
            Log.temp.info("battleNumGuildIdMap is null or empty for matchId={}", match.getId());
            return;
        }
        for (Map.Entry<Integer, Map<Long, Long>> ent : battleNumGuildIdMap.entrySet()) {
            int day = ent.getKey();
            Map<Long, Long> guildPairMap = ent.getValue();
            if (guildPairMap == null || guildPairMap.isEmpty()) {
                Log.temp.warn("guildPairMap is null or empty for day={} in matchId={}", day, match.getId());
                continue;
            }
            Map<Long, Long> guildMap = dayGuildIdRivalIdMap.computeIfAbsent(day, k -> new HashMap<>());
            guildMap.putAll(guildPairMap);
        }
    }

    public Map<Integer, List<Long>> getRoadHumanIdListMap(long guildId){
        Map<Integer, List<Long>> roadHumanIdListMap = guildIdPoadHumanIdMap.get(guildId);
        if(roadHumanIdListMap == null){
            roadHumanIdListMap = new HashMap<>();
            GuildManager.inst().initRoad(roadHumanIdListMap);
            guildIdPoadHumanIdMap.put(guildId, roadHumanIdListMap);
        }
        return roadHumanIdListMap;
    }

    public List<Long> gvgSelectRoad(long guildId, long humanId, int road, List<Define.p_gvg_road> roadInfoList){
        long matchId = getMacthId(guildId);
        GuildLeagueMatch match = matchMap.get(matchId);
        if(match == null){
            return new ArrayList<>();
        }
        List<Long> guildIdList = Utils.strToLongList(match.getGuildIdList());
        int index = guildIdList.indexOf(guildId);
        if(index == -1){
            return new ArrayList<>();
        }
        Map<Integer, List<Long>> map = getRoadHumanIdListMap(guildId);
        List<Long> humanIdList = map.get(road);
        for(Map.Entry<Integer, List<Long>> entry : map.entrySet()){
            List<Long> list = entry.getValue();
            if(list.contains(humanId)){
                list.remove(humanId);
                roadInfoList.add(get_p_gvg_road(guildId, entry.getKey()));
            }
        }
        if(!humanIdList.contains(humanId)){
            humanIdList.add(humanId);
        }
        int size = humanIdList.size();
        LinkedHashSet set = new LinkedHashSet<>(humanIdList);
        humanIdList.clear();
        humanIdList.addAll(set);

        if(humanIdList.size() != size){
            Log.temp.error("===定位问题在哪重复,roadInfoList = {}", roadInfoList);
        }

        switch (index){
            case 0:
                match.setGuildId1Map(Utils.mapIntListLongToJSON(map));
                break;
            case 1:
                match.setGuildId2Map(Utils.mapIntListLongToJSON(map));
                break;
            case 2:
                match.setGuildId3Map(Utils.mapIntListLongToJSON(map));
                break;
            case 3:
                match.setGuildId4Map(Utils.mapIntListLongToJSON(map));
                break;
            default:
                Log.temp.error("===index={}", index);
                break;
        }
        roadInfoList.add(get_p_gvg_road(guildId, road));
        return humanIdList;
    }

    public List<Long> gvgRoadHumanIdList(long guildId, int type, int road){
        long matchId = getMacthId(guildId);
        GuildLeagueMatch match = matchMap.get(matchId);
        if(match == null){
            return new ArrayList<>();
        }
        long id = 0;
        if(type == GuildParamKey.guild_gvg_type_1){// 自己
            id = guildId;
        } else if(type == GuildParamKey.guild_gvg_type_2){// 对手
            int round = GuildManager.inst().guildLeagueRound();
            id = getRivalId(round, guildId);
        }
        if(id <= 0){
            return new ArrayList<>();
        }

        Map<Integer, List<Long>> map = getRoadHumanIdListMap(id);
        List<Long> humanIdList = map.get(road);
        if(humanIdList == null){
            humanIdList = new ArrayList<>();
            map.put(road, humanIdList);
        }
        if(!humanIdList.isEmpty()){
            // 去重且有序
            List<Long> tempList = new ArrayList<>();
            Set<Long> set = new HashSet<>();
            for(long tempId : humanIdList){
                if(set.add(tempId)){
                    tempList.add(tempId);
                }
            }
            humanIdList = tempList;
        }
        return humanIdList;
    }

    public Define.p_gvg_road get_p_gvg_road(long guildId, int road){
        int round = GuildManager.inst().guildLeagueRound();
        long rivalId = getRivalId(round, guildId);

        Map<Integer, List<Long>> map = getRoadHumanIdListMap(guildId);
        List<Long> humanIdList = map.get(road);

        Map<Integer, List<Long>> mapRival = getRoadHumanIdListMap(rivalId);
        List<Long> humanIdListRival = mapRival.get(road);
        return GuildManager.inst().to_p_gvg_road(road, humanIdList.size(), humanIdListRival.size(), GuildParamKey.roadTwo);
    }

    public List<Define.p_gvg_road> get_p_gvg_road(long guildId, int... roadArr){
        int round = GuildManager.inst().guildLeagueRound();
        long rivalId = getRivalId(round, guildId);

        Map<Integer, List<Long>> map = getRoadHumanIdListMap(guildId);
        Map<Integer, List<Long>> mapRival = getRoadHumanIdListMap(rivalId);

        List<Define.p_gvg_road> dInfoList = new ArrayList<>();
        for(int road : roadArr){
            List<Long> humanIdList = map.getOrDefault(road, new ArrayList<>());
            List<Long> humanIdListRival = mapRival.getOrDefault(road, new ArrayList<>());
            dInfoList.add(GuildManager.inst().to_p_gvg_road(road, humanIdList.size(), humanIdListRival.size(), GuildParamKey.roadTwo));
        }
        return dInfoList;
    }


    public Map<Integer, List<Long>> gvgRoadChangeAll(long guildId, int road, int road1){
        long matchId = getMacthId(guildId);
        GuildLeagueMatch match = matchMap.get(matchId);
        if(match == null){
            return new HashMap<>();
        }
        List<Long> guildIdList = Utils.strToLongList(match.getGuildIdList());
        int index = guildIdList.indexOf(guildId);
        Map<Integer, List<Long>> map = getRoadHumanIdListMap(guildId);
        List<Long> humanIdList = new ArrayList<>(map.getOrDefault(road, new ArrayList<>()));
        List<Long> humanIdList1 = new ArrayList<>(map.getOrDefault(road1, new ArrayList<>()));

        int size = humanIdList.size();
        LinkedHashSet<Long> set = new LinkedHashSet<>(humanIdList);
        humanIdList.clear();
        humanIdList.addAll(set);
        map.put(road1, humanIdList);


        if(humanIdList.size() != size){
            Log.temp.error("===定位问题在哪重复");
        }

        int size1 = humanIdList1.size();
        LinkedHashSet<Long> set1 = new LinkedHashSet<>(humanIdList1);
        humanIdList1.clear();
        humanIdList1.addAll(set1);
        map.put(road, humanIdList1);

        if(humanIdList1.size() != size1){
            Log.temp.error("===定位问题在哪重复");
        }

        switch (index){
            case 0:
                match.setGuildId1Map(Utils.mapIntListLongToJSON(map));
                break;
            case 1:
                match.setGuildId2Map(Utils.mapIntListLongToJSON(map));
                break;
            case 2:
                match.setGuildId3Map(Utils.mapIntListLongToJSON(map));
                break;
            case 3:
                match.setGuildId4Map(Utils.mapIntListLongToJSON(map));
                break;
        }
        return map;
    }

    public List<Long> gvgRoadChange(long guildId, int road, List<Define.p_key_value> roadInfoList){
        long matchId = getMacthId(guildId);
        GuildLeagueMatch match = matchMap.get(matchId);
        if(match == null){
            return Collections.emptyList();
        }
        List<Long> guildIdList = Utils.strToLongList(match.getGuildIdList());
        int index = guildIdList.indexOf(guildId);
        Map<Integer, List<Long>> roadMap = getRoadHumanIdListMap(guildId);

        final List<Long> originalList = new ArrayList<>(roadMap.get(road));
        if (originalList.isEmpty()) {
            return Collections.emptyList();
        }

        int oldSize = originalList.size();

        final TreeMap<Integer, Long> positionMap = new TreeMap<>();
        final Set<Long> processedIds = new HashSet<>();

        IntStream.range(0, originalList.size()).forEach(i -> positionMap.put(i + 1, originalList.get(i)));

        for (Define.p_key_value entry : roadInfoList) {
            long humanId = entry.getK();
            int position = (int) entry.getV();

            // 有效性校验
            if (!originalList.contains(humanId)) {
                Log.temp.warn("定位问题Invalid human {} in road {}", humanId, road);
                continue;
            }

            if (position < 1 || position > originalList.size()) {
                Log.temp.warn("定位问题Position {} out of range [1-{}]", position, originalList.size());
                continue;
            }

            // 冲突处理
            if (processedIds.contains(humanId)) {
                Log.temp.warn("定位问题Duplicate config for humanId {}, pos={}", humanId, position);
                continue;
            }
            // 移除旧位置
            positionMap.entrySet().removeIf(e -> e.getValue() == humanId);

            // 处理位置覆盖
            long existing = positionMap.getOrDefault(position, 0L);
            if (existing > 0) {
                Log.temp.info("定位问题Position {} changed: {} -> {}", position, existing, humanId);
            }
            positionMap.put(position, humanId);
            processedIds.add(humanId);
        }

        Set<Long> remaining = new HashSet<>(originalList);
        remaining.removeAll(processedIds);

        positionMap.forEach((pos, id) -> remaining.remove(id));

        Iterator<Long> remainingIt = remaining.iterator();
        IntStream.rangeClosed(1, originalList.size())
                .filter(pos -> !positionMap.containsKey(pos))
                .forEach(pos -> {
                    if (remainingIt.hasNext()) {
                        positionMap.put(pos, remainingIt.next());
                    }
                });

        List<Long> newOrder = new ArrayList<>(positionMap.values());

        // 完整性校验
        if (newOrder.size() != originalList.size()) {
            Log.temp.error("Data loss detected! Original size {} vs new size {}",
                    originalList.size(), newOrder.size());
            return Collections.unmodifiableList(originalList); // 返回原始数据保证安全
        }

        // 8. 更新持久化数据
        roadMap.put(road, newOrder);

        if(newOrder.size() != oldSize){
            Log.temp.error("===定位问题在哪重复, size={}, humanIdListSize={}, roadInfoList={}, newmap={}, map={}", oldSize, newOrder.size(), roadInfoList, positionMap);
        }
        switch (index){
            case 0:
                match.setGuildId1Map(Utils.mapIntListLongToJSON(roadMap));
                break;
            case 1:
                match.setGuildId2Map(Utils.mapIntListLongToJSON(roadMap));
                break;
            case 2:
                match.setGuildId3Map(Utils.mapIntListLongToJSON(roadMap));
                break;
            case 3:
                match.setGuildId4Map(Utils.mapIntListLongToJSON(roadMap));
                break;
        }
        return Collections.unmodifiableList(newOrder);
    }

    public void gvgPlayVideo(long guildId, long vid, int source){

    }

    public void gvgFightInfo(long guildId, int road){

    }

    public void gvgInfo(long guildId, long humanId, MsgGvg.gvg_info_s2c.Builder dInfo){
//        long matchId = guildIdMatchIdMap.get(guildId);
//        GuildLeagueMatch match = matchMap.get(matchId);
//        if(match == null){
//            return;
//        }
//        List<Long> guildIdList = Utils.strToLongList(match.getGuildIdList());
//        int index = guildIdList.indexOf(guildId);
//        Map<Integer, List<Long>> map = guildIdPoadHumanIdMap.get(guildId);
//        Define.p_gvg_map.Builder mapInfo =  Define.p_gvg_map.newBuilder();
//
//        int week = Utils.getDayOfWeek();
//        if(){
//
//        }
//        long rivalId = getRivalId(3, guildId);
//        Map<Integer, List<Long>> mapRivalId = guildIdPoadHumanIdMap.get(rivalId);
//
//        for(Map.Entry<Integer, List<Long>> entry : map.entrySet()){
//            Define.p_gvg_road.Builder roadInfo = Define.p_gvg_road.newBuilder();
//            roadInfo.setRoad(entry.getKey());
//            roadInfo.setNum(entry.getValue().size());
//            roadInfo.setEnemyNum(mapRivalId.get(entry.getKey()).size());
//            roadInfo.setStatus(0);// 0 我方胜利 1 敌方胜利  2 战斗中
//            mapInfo.addRoadList(roadInfo);
//        }
//        mapInfo.setStatus(0);// 最终 0 我方胜利 1 敌方胜利  2 战斗中
//        mapInfo.setGuildId(rivalId);
//        mapInfo.setServId(rivalSerId);
//        mapInfo.setName(rivalName);
//        dInfo.setMapInfo(mapInfo);
    }

    private Map<Long, Long> getFactionIdRivalIdMap(int day){
        Map<Long, Long> factionIdRivalIdMap = dayGuildIdRivalIdMap.get(day);
        if(factionIdRivalIdMap == null){
            factionIdRivalIdMap = new HashMap<>();
            dayGuildIdRivalIdMap.put(day, factionIdRivalIdMap);
        }
        return factionIdRivalIdMap;
    }

    private void createMatch(LeagueMatchPair pair) {
        createMatch(pair, 0);
    }

    private void createMatch(LeagueMatchPair pair, int serverId) {
        GuildLeagueMatch match = new GuildLeagueMatch();
        match.setId(Port.applyId());
        match.setSeasonSn(pair.season);
        match.setWeek(pair.week);
        match.setGroupPvp(pair.group);
        match.setGradeType(pair.gradeType);
        match.setServerId(serverId);
        match.setOpenTime(Port.getTime());
        match.setGuildIdList(Utils.listToString(pair.guildIdList));
        Map<Integer, Map<Long, Long>> battleNumGuildIdMap = battleNumGuildIdMap(pair.guildIdList);
        match.setBattleNumGuildIdMap(Utils.mapIntMapLongLongToJSON(battleNumGuildIdMap));
        match.persist();
        pair.matchId = match.getId();
        addMatch(match);

        List<String> keyList = new ArrayList<>();
        keyList.add(RedisKeys.bridge_date_group_grade_guild_rank + match.getId());
        for(long guildId : pair.guildIdList){
            keyList.add("0");
            keyList.add(String.valueOf(guildId));
        }
        CrossRedis.zadd(keyList);
        CrossRedis.addToSet(RedisKeys.guild_league_remove_list, RedisKeys.bridge_date_group_grade_guild_rank + match.getId());
        CrossRedis.addToSet(RedisKeys.guild_league_remove_list, RedisKeys.bridge_date_group_grade_human_rank + match.getId());

        Log.temp.info("===createMatch, matchId={}, guildIdList={}", match.getId(), pair.guildIdList);
    }

    public static Map<Integer, Map<Long, Long>> battleNumGuildIdMap(List<Long> pairGuildIdList){
        Map<Integer, Map<Long, Long>> battleNumGuildIdMap = new HashMap<>();
        int size = pairGuildIdList.size();
        if(size == 2){ // 双宗门赛，特殊处理，打三天
            long guildId1 = pairGuildIdList.get(0);
            long guildId2 = pairGuildIdList.get(1);
            Map<Long, Long> mapNew = new HashMap<>();
            mapNew.put(guildId1, guildId2);
            for(int i = GuildParamKey.battleNum_1; i <= GuildParamKey.sumBattleNum; i++){
                battleNumGuildIdMap.put(i, mapNew);
            }
            return battleNumGuildIdMap;
        }

        Map<Long, List<Long>> idIdListMap = new HashMap<>();
        for(int m = 0; m < size; m+=2){
            long guildId1 = pairGuildIdList.get(m);
            long guildId2 = 0;
            if(m + 1 < size){
                guildId2 = pairGuildIdList.get(m + 1);
            }

            List<Long> idListNew = new ArrayList<>();
            idListNew.add(guildId2);
            idIdListMap.put(guildId1, idListNew);

            if(guildId2 > 0){
                List<Long> idListNew2 = new ArrayList<>();
                idListNew2.add(guildId1);
                idIdListMap.put(guildId2, idListNew2);
            }
            Map<Long, Long> mapNew = battleNumGuildIdMap.get(GuildParamKey.battleNum_1);
            if(mapNew == null){
                mapNew = new HashMap<>();
            }
            mapNew.put(guildId1, guildId2);
            battleNumGuildIdMap.put(GuildParamKey.battleNum_1, mapNew);
        }


        for(int i = GuildParamKey.battleNum_2; i <= GuildParamKey.sumBattleNum; i++){
            Map<Long, Long> mapNew = new HashMap<>();
            battleNumGuildIdMap.put(i, mapNew);
            for(int m = 0; m < size; m++){
                long guildId = pairGuildIdList.get(m);
                if(mapNew.containsKey(guildId) || mapNew.containsValue(guildId)){
                    continue;
                }
                List<Long> idListNew = new ArrayList<>(pairGuildIdList);
                List<Long> useIdList = idIdListMap.get(guildId);
                idListNew.removeAll(useIdList);
                idListNew.remove(guildId);
                idListNew.removeAll(mapNew.keySet());
                idListNew.removeAll(mapNew.values());

                if(idListNew.isEmpty()){
                    mapNew.put(guildId, 0L);
                    continue;
                }
                long enemyId = idListNew.get(0);
                mapNew.put(guildId, enemyId);
                useIdList.add(enemyId);
                List<Long> useIdList2 = idIdListMap.get(enemyId);
                useIdList2.add(guildId);
            }
        }
        return battleNumGuildIdMap;
    }

    public long getRivalId(int day, long factionId){
        Map<Long, Long> factionIdRivalIdMap = getFactionIdRivalIdMap(day);
        if(factionIdRivalIdMap.containsKey(factionId)){
            return factionIdRivalIdMap.get(factionId);
        } else {
            for(Map.Entry<Long,Long> entry : factionIdRivalIdMap.entrySet()){
                if(entry.getValue() == factionId){
                    return entry.getKey();
                }
            }
        }
        return -1;
    }

    public void matchWarmUp(int season, int week, int groupType, List<Long> guildIdList, int serverId){
//        Collections.shuffle(guildIdList);
        try {
            if(guildIdList == null || guildIdList.isEmpty()){
                return;
            }
            Set<Long> guildIdSet = new HashSet<>();
            Collections.shuffle(guildIdList);
            int num = 4;
            int loopNum = 100000000;
            while (!guildIdList.isEmpty()) {
                List<Long> idListTemp = new ArrayList<>();
                for (Long guildId : guildIdList) {
                    if (idListTemp.size() >= num) {
                        break;
                    }
                    if (guildIdSet.add(guildId)) {
                        if(matchGuildIdList.add(guildId)){
                            idListTemp.add(guildId);
                        }
                    }
                }
                if (!idListTemp.isEmpty()) {
                    LeagueMatchPair pair = new LeagueMatchPair(season, week, idListTemp, groupType, serverId);
                    createMatch(pair, serverId);
                    guildIdList.removeAll(idListTemp);
                } else {
                    matchGuildIdList.containsAll(guildIdList);
                    break;
                }
                loopNum--;// 防止死循环
                if(loopNum <= 0){
                    break;
                }
            }
        } catch (Exception e) {
            Log.temp.error("===匹配过程中发生异常: {}", e.getMessage());
        }
    }


    public void match(int season, int week, int groupType, List<Long> guildIdList){
        Collections.shuffle(guildIdList);
        int size = guildIdList.size();
        for(int i = 0; i < size; i += 4){
            int length = i + 4;
            if(length > size){
                length = size;
            }
            List<Long> idList = guildIdList.subList(i, length);
            LeagueMatchPair pair = new LeagueMatchPair(season, week, idList, groupType, 0);
            createMatch(pair);
        }
    }

    public void matchBridgeGuild(int group, int season, int week, int grade, List<Long> guildIdList){
        Set<Long> rankGuildIdSet = new LinkedHashSet<>(guildIdList);
        LinkedList<Long> rankGuildIdList = new LinkedList<>(rankGuildIdSet);
        int random = ConfGlobal.get(ConfGlobalKey.乱斗公会排行范围.SN).value;
        int loopNum = rankGuildIdList.size() + 100;
        while(!rankGuildIdList.isEmpty() && loopNum > 0){
            loopNum--;
            int size = rankGuildIdList.size();
            if(size < random){
                random = size;
            }
            if(size <= ParamKey.guildLeagueParamNum){
                List<Long> idListTemp = new ArrayList<>();
                for(long guildId : rankGuildIdList){
                    if(matchGuildIdList.add(guildId)){
                        idListTemp.add(guildId);
                    }
                }
                if(!idListTemp.isEmpty()){
                    LeagueMatchPair pair = new LeagueMatchPair(season, week, idListTemp, grade, group);
                    pairAllList.add(pair);
                }
                matchGuildIdList.addAll(idListTemp);
                break;
            }
            List<Long> idListTemp = new ArrayList<>();
            int sizeTemp = rankGuildIdList.size();
            // 保证第一个公会有
            for(int i = 0; i < sizeTemp; i++){
                long id = rankGuildIdList.poll();
                if(matchGuildIdList.add(id)) {
                    idListTemp.add(id);
                }
                if(idListTemp.size() >= 1){
                    break;
                }
            }
            // 随机范围内的公会
            for(int i = 1; i < ParamKey.guildLeagueParamNum; i++){
                size = rankGuildIdList.size();
                if(size < random){
                    random = size;
                }
                if (random <= 0) {
                    break;
                }
                int index = Utils.random(random);
                long tempId = rankGuildIdList.remove(index);
                if(matchGuildIdList.add(tempId)){
                    idListTemp.add(tempId);
                    if(idListTemp.size() >= ParamKey.guildLeagueParamNum){
                        break;
                    }
                }
            }
            if(!idListTemp.isEmpty()){
                LeagueMatchPair pair = new LeagueMatchPair(season, week, idListTemp, grade, group);
                matchGuildIdList.addAll(idListTemp);
                pairAllList.add(pair);
            }
        }
        ttPairCreate.reStart();
        Log.temp.error("===time={}, group={}, grade={}, guildIdList={}", Utils.formatTime(Port.getTime(),"yyyy-MM-dd HH:mm:ss"),
                group, grade, rankGuildIdSet);
    }

    private void batchesCreateMatch(){
        if (pairAllList == null || pairAllList.isEmpty()) {
            return;
        }
        try{
            // TODO 每次200个
            int num = Math.min(200, pairAllList.size());
            if(Utils.isDebugMode()){
                num = 10000;// 本地直接全部
            }
            for(int i = 0; i < num; i++){
                if(pairAllList == null || pairAllList.isEmpty()){
                    break;
                }
                LeagueMatchPair pair = pairAllList.poll();
                createMatch(pair);
            }
            if(pairAllList.isEmpty()){
                ttPairCreate.stop();
                Log.temp.info("===pairAllList 匹配结束");
            }
        }catch (Exception e){
            Log.temp.error("===batchesCreateMatch ", e);
        }

    }


    private void updateRoadWinStatus(long winGuildId, long loseGuildId, int road){
        int round = GuildManager.inst().guildLeagueRound();
        Map<Long, Map<Integer, Integer>> guildRoadStatusMap = roundGuildRoadStatusMap.getOrDefault(round, new HashMap<>());
        roundGuildRoadStatusMap.put(round, guildRoadStatusMap);

        Map<Integer, Integer> roadStatusMap = guildRoadStatusMap.getOrDefault(winGuildId, new HashMap<>());
        guildRoadStatusMap.put(winGuildId, roadStatusMap);
        roadStatusMap.put(road, GuildParamKey.roadWin);

        Map<Integer, Integer> roadStatusMapLose = guildRoadStatusMap.getOrDefault(loseGuildId, new HashMap<>());
        guildRoadStatusMap.put(loseGuildId, roadStatusMapLose);
        roadStatusMapLose.put(road, GuildParamKey.roadLose);
    }

    public int getRoadStatus(int round, long guildId, int road){
        return roundGuildRoadStatusMap.getOrDefault(round, new HashMap<>()).getOrDefault(guildId, new HashMap<>()).getOrDefault(road, GuildParamKey.roadTwo);
    }

    private List<Long> getGuildHumanIdAllList(long guildId){
        Map<Integer, List<Long>> map = getRoadHumanIdListMap(guildId);
        List<Long> humanIdList = new ArrayList<>();
        if(map == null){
            return humanIdList;
        }
        for(List<Long> idList : map.values()){
            humanIdList.addAll(idList);
        }
        return humanIdList;
    }



    public void autoJoinHumanIdList(long guildId, List<Long> humanIdList){
        if (humanIdList == null || humanIdList.isEmpty()) {
            Log.temp.error("===humanIdList is null or empty");
            return;
        }
        long matchId = getMacthId(guildId);
        GuildLeagueMatch match = matchMap.get(matchId);
        if(match == null){
            Log.temp.error("===match is null for guildId={}", guildId);
            return;
        }
        List<Long> guildIdList = Utils.strToLongList(match.getGuildIdList());
        int index = guildIdList.indexOf(guildId);
        if(index == -1){
            Log.temp.error("===guildId not found in match, guildId={}", guildId);
            return;
        }
        Map<Integer, List<Long>> map = getRoadHumanIdListMap(guildId);
        List<Long> joinList = new ArrayList<>();
        int road1 = 0;
        int road2 = 0;
        int road3 = 0;
        for(int road = 1; road <= 3; road++){
            List<Long> tempIdList =  map.getOrDefault(road, new ArrayList<>());
            if(road == 1){
                road1 = tempIdList.size();
            } else if(road == 2){
                road2 = tempIdList.size();
            } else if(road == 3){
                road3 = tempIdList.size();
            }
            joinList.addAll(tempIdList);
        }
        humanIdList.removeAll(joinList);

        for(long humanId : humanIdList){
            int minRoad = road1 < road2 ? (road1 < road3 ? 1 : 3) : (road2 < road3 ? 2 : 3);
            List<Long> tempIdList =  map.getOrDefault(minRoad, new ArrayList<>());
            if(!tempIdList.contains(humanId)){
                tempIdList.add(humanId);
                map.put(minRoad, tempIdList);
            }
            if(minRoad == 1){
                ++road1;
            } else if(minRoad == 2){
                ++road2;
            } else if(minRoad == 3){
                ++road3;
            }
		}
        // 去重
        for (List<Long> list : map.values()) {
            int size = list.size();
            List<Long> uniqueList = new ArrayList<>(new LinkedHashSet<>(list));
            list.clear();
            list.addAll(uniqueList);
            if (list.size() != size) {
                Log.temp.error("===定位问题在哪重复");
            }
        }

        switch (index){
            case 0:
                match.setGuildId1Map(Utils.mapIntListLongToJSON(map));
                break;
            case 1:
                match.setGuildId2Map(Utils.mapIntListLongToJSON(map));
                break;
            case 2:
                match.setGuildId3Map(Utils.mapIntListLongToJSON(map));
                break;
            case 3:
                match.setGuildId4Map(Utils.mapIntListLongToJSON(map));
                break;
            default:
                Log.temp.error("===index={}", index);
                break;
        }
    }

    public void clearRoad(){
//        guildIdPoadHumanIdMap.clear();
//        for(GuildLeagueMatch match : matchMap.values()){
//            match.setGuildId1Map("");
//            match.setGuildId2Map("");
//            match.setGuildId3Map("");
//            match.setGuildId4Map("");
//            match.update();
//        }
    }

    public void log(){

    }

    //===========================================改成慢慢战斗===============================
    public Map<Long, Integer> guildBattleStart(Map<Long, GuildLeagueRecord> guildRecordMap){
        int round = GuildManager.inst().guildLeagueRound();

        Map<Long, Integer> guildIdAddScoreMap = new HashMap<>();

        long timeNow = Port.getTime();
        for(GuildLeagueMatch match : matchMap.values()){
            if(match == null){
                continue;
            }
            if(!Utils.isSameWeek(match.getOpenTime(), timeNow)){
                continue;
            }
            Map<Integer, Map<Long, Long>> battleNumGuildIdMap = Utils.jsonToIntMapLongLong(match.getBattleNumGuildIdMap());
            if(battleNumGuildIdMap == null || battleNumGuildIdMap.isEmpty()){
                continue;
            }
            Map<Long, Long> guildIdMap = battleNumGuildIdMap.get(round);
            if(guildIdMap == null){
                continue;
            }
            int group = match.getGroupPvp();
            int grade = match.getGradeType();
            for(Map.Entry<Long, Long> entry : guildIdMap.entrySet()){
                long guildId = entry.getKey();
                long guildIdRival = entry.getValue();

                if(guildIdRival <= 0){
                    // 轮空战斗，一定是guildIdRival=0
                    byeBattleHumanAll(guildId, group, grade, match.getId());
                    continue;
                }

                GuildGvgBattleCross gvgBattle = new GuildGvgBattleCross(match, round, guildId, guildIdRival,
                        getRoadHumanIdListMap(guildId), getRoadHumanIdListMap(guildIdRival));
//                List<GuildLeagueHistory> historyList = gvgBattle.pulseBattle(0);
                battleAllList.add(gvgBattle);
                int index = battleAllList.size() - 1;
                guildIdIndexMap.put(guildId, index);
                guildIdIndexMap.put(guildIdRival, index);
            }
        }
        return guildIdAddScoreMap;
    }

    public boolean pulseBattle(int indexPos){
        try {
            boolean isEndAll = true;
            for (GuildGvgBattleCross battle : battleAllList) {
                try {
                    battle.pulseBattle(indexPos);
                    if (battle.winGuildId <= 0) {
                        int i = GuildLeagueUtils.nowStep();
                        if (i == 6 || i == 11 || i == 15){
                            for(int road = 1; road <= GuildParamKey.pathMax; road++){
                                if(!battle.roadWinGuildIdMap.containsKey(road)){
                                    battle.roadWinGuildIdMap.put(road, battle.guildIdA);
                                    battle.settleRoadWinError(road, battle.guildIdA, battle.guildIdB);
                                }
                            }
                            battle.winGuildId = battle.guildIdA;
                            if (!battle.isSettle) {
                                battle.isSettle = true;
                                updateGuildScoreSettle(battle);
                            }
                        }else{
                            isEndAll = false;
                        }
                    }else{
                        if (!battle.isSettle) {
                            battle.isSettle = true;
                            updateGuildScoreSettle(battle);
                        }
                    }
                } catch (Exception e) {
                    Log.temp.error("pulseBattle error 战斗异常", e);
                }
            }
            return isEndAll;
        } catch (Exception e) {
            Log.game.error("pulseBattle error 战斗异常", e);
            return true;
        }
    }

    private long byeBattleHumanAll(long guildId, int group, int grade, long matchId){
        try {
            Map<Long, LeagueVO> leagueVOMap = new HashMap<>();
            List<Long> humanIdList = getGuildHumanIdAllList(guildId);
            ConfGlobal confByeReward = ConfGlobal.get(ConfGlobalKey.familybattle_nullenemy_reward.SN);
            // 个人积分
            ConfGlobal confHumanScore = ConfGlobal.get(ConfGlobalKey.familybattle_nullenemy_selfpoint_reward.SN);
            // 家族积分
            ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.familybattle_nullenemy_familypoint_reward.SN);

            String redisKeyHuman = RedisKeys.bridge_date_group_grade_human_rank + getMacthId(guildId);


            int mailSn = 10130;//家族乱斗轮空奖励
            // 奖励
            String byeItemJSON = Utils.mapIntIntToJSON(Utils.intArrToIntMap(new HashMap<>(), Utils.parseIntArray2(confByeReward.strValue)));

            int serverId = 0;
            for (long humanId : humanIdList) {
                if(serverId == 0){
                    serverId = Utils.getServerIdByHumanId(humanId);
                }
                // 加入乱斗组本周（4个公会的玩家排行）
                CrossRedis.addScore(redisKeyHuman, humanId, confHumanScore.value);
                // 加入乱斗组个人排行榜
                CrossRedis.addScore(RedisKeys.bridge_pvp_group_human_rank + group, humanId, confHumanScore.value);
                Log.temp.info("===乱斗轮空结算个人，guildId={},humanId={}, addScore={}", guildId, humanId, confHumanScore.value );

//                JSONObject jo = new JSONObject();
//                JSONObject joTemp = new JSONObject();
//                joTemp.put(MailManager.MAIL_K_4, confGlobal.value);
//                jo.put(MailManager.MAIL_PARAM_1, joTemp);
//                MailManager.inst().sendMail(humanId, MailManager.SYS_SENDER, mailSn, "", jo.toJSONString(), byeItemJSON, param);

                LeagueVO info = new LeagueVO();
                info.voId = humanId;
                info.intValue = mailSn;
                info.param.put("itemJSON", byeItemJSON);
                info.param.put("addScore", confHumanScore.value + confGlobal.value);
                leagueVOMap.put(humanId, info);

            }
            winGuildIdList.add(guildId);
            guildIdAddScoreAllMap.put(guildId, confGlobal.value);

            String redisKeyGuild = RedisKeys.bridge_date_group_grade_guild_rank + matchId;
            // 乱斗组本周（4个公会排行）
            CrossRedis.addScore(redisKeyGuild, guildId, confGlobal.value);
            // 同一个乱斗组排行（跨服1/本服所有公会）, score(工会积分)， 工会id
            CrossRedis.addScore(RedisKeys.bridge_pvp_group_guild_rank + group, guildId, confGlobal.value);
            Log.temp.info("===乱斗结算公会，serverId={}, guildId={}, addScore={}", serverId, guildId, confGlobal.value);

            HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance(DistrKit.getWorldNodeID(serverId));
            proxy.sendLeagueSettleScore(leagueVOMap);
            proxy.listenResult(this::_result_sendLeagueSettleScore, "serverId", serverId, "leagueVOMap", leagueVOMap);
        } catch (Exception e) {
            Log.temp.error("===byeBattleHumanAll error", e);
        }
        return guildId;
    }

    //这是给玩家加分的
    private void updateGuildScoreSettle(GuildGvgBattleCross battle){
        if(!winGuildIdList.contains(battle.winGuildId)){
            winGuildIdList.add(battle.winGuildId);
        }
        String redisKeyGuild = RedisKeys.bridge_date_group_grade_guild_rank + battle.matchId;
        String redisKeyHuman =RedisKeys.bridge_date_group_grade_human_rank + battle.matchId;

        Log.temp.info("===乱斗战斗结算奖励，redisKeyGuild={}, redisKeyHuman={}, winGuildId={}", redisKeyGuild, redisKeyHuman, battle.winGuildId);

        List<Long> humanIdListA = battle.roadHumanListMapA.values().stream().flatMap(List::stream).collect(Collectors.toList());
        List<Long> humanIdListB = battle.roadHumanListMapB.values().stream().flatMap(List::stream).collect(Collectors.toList());

        ConfFamiliyBrawl conf = ConfFamiliyBrawl.get(ParamKey.familiyBrawlSn);
        // 奖励
        Map<Integer, Integer> winItemMap = Utils.intArrToIntMap(new HashMap<>(), conf.win_reward);
        Map<Integer, Integer> loseItemMap = Utils.intArrToIntMap(new HashMap<>(), conf.lose_reward);

        if(settleGuildIdList.add(battle.guildIdA)){
            settleScore(battle, battle.guildIdA, battle.guildIdA == battle.winGuildId, redisKeyGuild, redisKeyHuman, humanIdListA, battle.guildIdA == battle.winGuildId ? winItemMap : loseItemMap);
        }
        if(settleGuildIdList.add(battle.guildIdB)) {
            settleScore(battle, battle.guildIdB, battle.guildIdB == battle.winGuildId, redisKeyGuild, redisKeyHuman, humanIdListB, battle.guildIdB == battle.winGuildId ? winItemMap : loseItemMap);
        }
        guildIdBattleMap.put(battle.guildIdA, battle);
        guildIdBattleMap.put(battle.guildIdB, battle);


        if(battle.roadWinGuildIdMap.isEmpty()){
            updateRoadWinStatus(battle.winGuildId, battle.guildIdA == battle.winGuildId ? battle.guildIdB : battle.guildIdA, battle.round);
        }
        for(Map.Entry<Integer, Long> entry : battle.roadWinGuildIdMap.entrySet()){
            updateRoadWinStatus(entry.getValue(), battle.guildIdA == entry.getValue() ? battle.guildIdB : battle.guildIdA, entry.getKey());
        }
        GuildLeagueMatch match = matchMap.get(battle.matchId);
        if(match != null){
            Map<Integer, List<Long>> dayWinIdListMap = Utils.jsonToMapIntListLong(match.getBattleNumWinGuildIdMap());
            if (dayWinIdListMap == null) {
                dayWinIdListMap = new HashMap<>();
            }
            List<Long> winIDList = dayWinIdListMap.get(battle.round);
            if (winIDList == null) {
                winIDList = new ArrayList<>();
                dayWinIdListMap.put(battle.round, winIDList);
            }
            if(!winIDList.contains(battle.winGuildId)){
                winIDList.add(battle.winGuildId);
            }
            match.setBattleNumWinGuildIdMap(Utils.mapIntListLongToJSON(dayWinIdListMap));

            // 轮次， 工会id, 上中下路，胜利失败状态
            Map<Long, Map<Integer, Integer>> roundGuildIdRoadStatusMap = roundGuildRoadStatusMap.getOrDefault(battle.round, new HashMap<>());

            Map<Integer,Map<Long, Map<Integer, Integer>>> newMap = Utils.jsonToIntLongMapIntInt(match.getBattleNumGuildIdRoadMap());
            if(match.getBattleNumGuildIdRoadMap().length() > 2000){
                newMap = new HashMap<>();
            }
            List<Long> guildIdList = Utils.strToLongList(match.getGuildIdList());
            Map<Long, Map<Integer, Integer>> guildIdRoadStatusMap = new HashMap<>();
            for(long guildId : guildIdList) {
                Map<Integer, Integer> roadStatusMap =roundGuildIdRoadStatusMap.getOrDefault(guildId, new HashMap<>());
                guildIdRoadStatusMap.put(guildId, roadStatusMap);
            }
            newMap.put(battle.round, guildIdRoadStatusMap);

            match.setBattleNumGuildIdRoadMap(Utils.mapIntLongMapIntIntToJSON(newMap));
            Log.temp.info("===战斗结算，matchId={}, BattleNumGuildIdRoadMap={}, roadWinGuildIdMap={}",
                    match.getId(),  match.getBattleNumGuildIdRoadMap(), battle.roadWinGuildIdMap);
        }
    }

    private void settleScore(GuildGvgBattleCross battle, long guildId, boolean isWin, String redisKeyGuild,
                             String redisKeyHuman, List<Long> humanIdList, Map<Integer,Integer> itemMap){
        if(guildId <= 0 || humanIdList.isEmpty()){
            Log.temp.error("===工会结算，没有人，guildId={}，humanIdList={}", guildId, humanIdList);
            return;
        }
        ConfFamiliyBrawl conf = ConfFamiliyBrawl.get(ParamKey.familiyBrawlSn);

        int sumKillNum = 0;
        int topKillNum = 0;

        int serNo = battle.group;
        List<RankInfo> rankInfoList = new ArrayList<>();
        //家族乱斗奖励
        int mailSn = ParamKey.guildLeagueWinMailSn;
        if(!isWin){
            mailSn = ParamKey.guildLeagueLoseMailSn;
        }

        Map<Long, LeagueVO> leagueVOMap = new HashMap<>();
        int serverId = 0;

        for(long humanId : humanIdList){
            try {
                if(serverId == 0) {
                    serverId = Utils.getServerIdByHumanId(humanId);
                }
                int killNum = battle.humanIdKillNumMap.getOrDefault(humanId, 0);
                int addScore = 0;
                if (isWin) {
                    addScore = conf.win_score + killNum * conf.kill_score;
                } else {
                    addScore = conf.lose_score + killNum * conf.kill_score;
                }
                RankInfo rankInfo = new RankInfo();
                rankInfo.humanId = humanId;
                rankInfo.param = killNum;
                rankInfoList.add(rankInfo);

                // 加入个人组个人排行榜
//                itemMap.put(ParamKey.guildScoreSn, addScore);

                sumKillNum += killNum;

                // 加入乱斗组本周（4个公会的玩家排行）
                CrossRedis.addScore(redisKeyHuman, humanId, addScore);
                // 加入乱斗组个人排行榜
                CrossRedis.addScore(RedisKeys.bridge_pvp_group_human_rank + serNo, humanId, addScore);
                Log.temp.info("===乱斗结算个人，guildId={},humanId={}, addScore={}, killNum={}", guildId, humanId, addScore, killNum);


                LeagueVO info = new LeagueVO();
                info.voId = humanId;
                info.intValue = mailSn;
                info.param.put("itemJSON", Utils.mapIntIntToJSON(itemMap));
                info.param.put("killNum", killNum);
                leagueVOMap.put(humanId, info);

//                MailManager.inst().sendMail(humanId, MailManager.SYS_SENDER, mailSn, "", "", Utils.mapIntIntToJSON(itemMap), param);

                if (topKillNum < killNum) {
                    topKillNum = killNum;
                }
            }catch (Exception e){
                Log.temp.error(" settleScore 发奖励失败， humanId={}, error ", humanId, e);
            }
        }

        ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.familybattle_point.SN);
        int winNumAddScore = confGlobal.intArray[0];
        int joinAddScore = confGlobal.intArray[1];
        // 个人战斗分=我的胜场数*50
        // 家族战斗积分=家族胜败基础分+胜场*20+参与人数*5
        // 胜败基础分读取FamiliyBrawl_家族乱斗表family_basic_score（失败）/family_break_score（胜利）
        int addScore = sumKillNum * winNumAddScore + joinAddScore * humanIdList.size();
        if(isWin){
            addScore += conf.family_break_score;
        } else {
            addScore += conf.family_basic_score;
        }
        guildIdAddScoreAllMap.put(guildId, addScore);

        // 乱斗组本周（4个公会排行）
        CrossRedis.addScore(redisKeyGuild, guildId, addScore);
        // 同一个乱斗组排行（跨服1/本服所有公会）, score(工会积分)， 工会id
        CrossRedis.addScore(RedisKeys.bridge_pvp_group_guild_rank + serNo, guildId, addScore);
        Log.temp.info("===乱斗结算公会，guildId={}, addScore={}", guildId, addScore);

        Collections.sort(rankInfoList, (a, b) -> {
            int ret = 0;// 0默认相等
            if (a != null && b != null) {
                if (a.param < b.param) {
                    ret = 1;
                } else if (a.param > b.param) {
                    ret = -1;
                }
            }
            return ret;
        });

        int week = Utils.getDayOfWeek() - 1;

        JSONArray ja = new JSONArray();
        for(RankInfo rankInfo : rankInfoList){
            JSONObject jo = new JSONObject();
            jo.put("id", rankInfo.humanId );
            jo.put("num", Utils.intValue(rankInfo.param));
            ja.add(jo);
            if(ja.size() >= 3){
                break;
            }
        }
        for(long humanId : humanIdList) {
            try {
                JSONObject settleJo = new JSONObject();
                int killNum = battle.humanIdKillNumMap.getOrDefault(humanId, 0);
                settleJo.put("killNum", killNum);
                int sumScore = 0;
                if(guildId == battle.winGuildId){
                    settleJo.put("status", 1);//0 失败 1 胜利
                    settleJo.put("statusScore",conf.win_score);
                    settleJo.put("selfScore",  killNum * conf.kill_score);
                    sumScore += conf.win_score + killNum * conf.kill_score;
                } else {
                    settleJo.put("status",  0);//0 失败 1 胜利
                    settleJo.put("statusScore", conf.lose_score);
                    settleJo.put("selfScore", killNum * conf.kill_score);
                    sumScore += conf.lose_score + killNum * conf.kill_score;
                }
                settleJo.put("guildScore", addScore);
                settleJo.put("totalScore", addScore + sumScore);
                settleJo.put("rankInfo", ja);
                CrossRedis.setAndExpire(RedisKeys.guild_human_week_settle + week + humanId, settleJo.toJSONString(), (int)(Time.DAY / Time.SEC));

                if(addScoreIdList.add(humanId)){
                    LeagueVO info = leagueVOMap.get(humanId);
                    if(info == null){
                        info = new LeagueVO();
                        info.voId = humanId;
                        leagueVOMap.put(humanId, info);
                    }
                    info.param.put("addScore", addScore + sumScore);
                    Log.temp.info("===发过积分奖励，humanId={}, voJson={}", humanId, info.toJSON());
                } else {
                    Log.temp.info("===已经发过奖励，humanId={}, addScore={}", humanId, addScore+sumScore);
                }
            }catch (Exception e){
                Log.temp.error(" settleScore 个人排行榜失败， humanId={}, error ", humanId, e);
            }
        }

        try {
            HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance(DistrKit.getWorldNodeID(serverId));
            proxy.sendLeagueSettleScore(leagueVOMap);
            proxy.listenResult(this::_result_sendLeagueSettleScore, "serverId", serverId, "leagueVOMap", leagueVOMap);
        }catch (Exception e){
            Log.temp.error("===公会结算，通知游戏服, guildId={}, serverId={}", guildId, serverId, e);
        }

        if(battle.guildIdA == guildId){
            battle.rankInfoListA = rankInfoList.subList(0, Math.min(rankInfoList.size(), 3));
        } else if(battle.guildIdB == guildId){
            battle.rankInfoListB = rankInfoList.subList(0, Math.min(rankInfoList.size(), 3));
        }
    }

    private void _result_sendLeagueSettleScore(boolean timeout, Param results, Param context){
        if(timeout){
            Log.temp.error("===sendLeagueSettleScore timeout, context={}", context);
            return;
        }
        if(results.containsKey("result") && results.getBoolean("result")){
            return;
        }
        Map<Long, LeagueVO> leagueVOMap = context.get("leagueVOMap");

    }


    public Map<Long, Integer> getGuildIdAddScoreAllMap(){
        return guildIdAddScoreAllMap;
    }

    public List<GuildGvgBattleCross> getBattleAllList(){
        return battleAllList;
    }


    public MsgGvg.gvg_fight_settlement_s2c getMsg_gvg_fight_settlement_s2c(long guildId, long humanId, List<Long> humanIdList, Map<Long, Integer> idScoreMap) {
        MsgGvg.gvg_fight_settlement_s2c.Builder msg = MsgGvg.gvg_fight_settlement_s2c.newBuilder();
        GuildGvgBattleCross battle = guildIdBattleMap.get(guildId);
        if(battle == null){
//            Log.temp.info("===没有战斗记录，应该轮空了，guildId={}", guildId);
            return null;
        }

        int killNum = battle.humanIdKillNumMap.getOrDefault(humanId, 0);
        msg.setStatus(guildId == battle.winGuildId ? 1 : 0);//0 失败 1 胜利
        msg.setKillNum(killNum);
        ConfFamiliyBrawl conf = ConfFamiliyBrawl.get(ParamKey.familiyBrawlSn);
        msg.setStatusScore(guildId == battle.winGuildId ? conf.win_score : conf.lose_score);
        msg.setSelfScore(killNum * conf.kill_score);
        msg.setGuildScore(guildIdAddScoreAllMap.getOrDefault(guildId, 0));
        msg.setTotalScore(msg.getStatusScore() + msg.getSelfScore() + msg.getGuildScore());

        if(guildId == battle.winGuildId){
            msg.addAllRewardList(InstanceManager.inst().to_p_rewardList(conf.win_reward));
        } else {
            msg.addAllRewardList(InstanceManager.inst().to_p_rewardList(conf.lose_reward));
        }
        List<RankInfo> rankInfoList = new ArrayList<>();
        if(battle.guildIdA == guildId){
            rankInfoList =battle.rankInfoListA;
        } else if(battle.guildIdB == guildId){
            rankInfoList =battle.rankInfoListB;
        }

        for(RankInfo rankInfo : rankInfoList){
            humanIdList.add(rankInfo.humanId);
            idScoreMap.put(rankInfo.humanId, Utils.intValue(rankInfo.param));
            if(humanIdList.size() >= 3){
                break;
            }
        }
        return msg.build();
    }

    public GuildGvgBattleCross getGuildBattle(long guildId){
        return guildIdBattleMap.get(guildId);
    }


    // =========================== TODO 改成19点开始先拉人===============================
    public void guildBattleGetHumanData(Map<Long, GuildLeagueRecord> guildRecordMap){
        battleAllList.clear();
        getIndex = 0;

        int round = GuildManager.inst().guildLeagueRound();
        Log.temp.info("===乱斗开始拉人，round={}", round);


        long timeNow = Port.getTime();
        for(GuildLeagueMatch match : matchMap.values()){
            if(match == null){
                continue;
            }
            if(!Utils.isSameWeek(match.getOpenTime(), timeNow)){
                continue;
            }
            Map<Integer, Map<Long, Long>> battleNumGuildIdMap = Utils.jsonToIntMapLongLong(match.getBattleNumGuildIdMap());
            if(battleNumGuildIdMap == null || battleNumGuildIdMap.isEmpty()){
                continue;
            }
            Map<Long, Long> guildIdMap = battleNumGuildIdMap.get(round);
            if(guildIdMap == null){
                continue;
            }
            for(Map.Entry<Long, Long> entry : guildIdMap.entrySet()){
                long guildId = entry.getKey();
                long guildIdRival = entry.getValue();

                if(guildIdRival <= 0){
                    // 轮空战斗先不处理
                    continue;
                }
                GuildGvgBattleCross gvgBattle = new GuildGvgBattleCross(match, round, guildId, guildIdRival,
                        getRoadHumanIdListMap(guildId), getRoadHumanIdListMap(guildIdRival));
                battleAllList.add(gvgBattle);
                int index = battleAllList.size() - 1;
                guildIdIndexMap.put(guildId, index);
                guildIdIndexMap.put(guildIdRival, index);
            }
        }
    }

    public void guildBattleStartBye(Map<Long, GuildLeagueRecord> guildRecordMap){
        int round = GuildManager.inst().guildLeagueRound();

        long timeNow = Port.getTime();
        for(GuildLeagueMatch match : matchMap.values()){
            if(match == null){
                continue;
            }
            if(!Utils.isSameWeek(match.getOpenTime(), timeNow)){
                continue;
            }
            Map<Integer, Map<Long, Long>> battleNumGuildIdMap = Utils.jsonToIntMapLongLong(match.getBattleNumGuildIdMap());
            if(battleNumGuildIdMap == null || battleNumGuildIdMap.isEmpty()){
                continue;
            }
            Map<Long, Long> guildIdMap = battleNumGuildIdMap.get(round);
            if(guildIdMap == null){
                continue;
            }
            int group = match.getGroupPvp();
            int grade = match.getGradeType();
            for(Map.Entry<Long, Long> entry : guildIdMap.entrySet()){
                long guildId = entry.getKey();
                long guildIdRival = entry.getValue();

                if(guildIdRival <= 0){
                    // 轮空战斗，一定是guildIdRival=0
                    byeBattleHumanAll(guildId, group, grade, match.getId());
                }
            }
        }
    }

    public void clear(){
        battleAllList.clear();
        guildIdIndexMap.clear();
        guildIdBattleMap.clear();
        guildIdAddScoreAllMap.clear();
        addScoreIdList.clear();
        roundGuildRoadStatusMap.clear();
        historyMap.clear();
    }

    public void getDataBattleAllList(){
        if(battleAllList.isEmpty() || battleAllList.size() <= getIndex){
            Log.temp.info("拉人结束， battleAllList={}", battleAllList.size());
            return;
        }
        try{
            GuildGvgBattleCross gvgBattle = battleAllList.get(getIndex);
            int index = gvgBattle.loadIndex;
            List<Long> humanIdList = new ArrayList<>();
            boolean isAdd = isAddIndex( true, index, gvgBattle.roadHumanListMapA, humanIdList);
            isAdd = isAddIndex(isAdd, index, gvgBattle.roadHumanListMapB, humanIdList);
            List<Long> removeList = new ArrayList<>();
            for(long humanId : humanIdList){
                if(gvgBattle.humanIdBriefMap.containsKey(humanId)){
                    removeList.add(humanId);
                }
            }
            humanIdList.removeAll(removeList);
            if(!humanIdList.isEmpty()){
                CrossHumanLoader.getList(humanIdList, HumanBriefLoadType.BATTLE_INFO, ret2 -> {
                    if (ret2.failed()) {
                        Log.temp.error("===ret2.cause={}", ret2.cause());
                        return;
                    }
                    List<HumanBrief> humanBriefList = ret2.result();
                    List<Long> addIdList = new ArrayList<>();
                    for(HumanBrief brief : humanBriefList){
                        if(brief == null || brief.getBattleRole() == null || brief.getBattleRole().length == 0){
                            continue;
                        }
                        addIdList.add(brief.getId());
                        gvgBattle.humanIdBriefMap.put(brief.getId(), brief);
                    }
                    Log.temp.info("===拉人，guildIdA={}, guildIdB={}, {} humanIdListNum={}", gvgBattle.guildIdA, gvgBattle.guildIdB, humanBriefList.size(),humanIdList.size());

                    List<Long> selectIdList = new ArrayList<>();
                    selectIdList.addAll(humanIdList);
                    selectIdList.removeAll(addIdList);
                    if(!selectIdList.isEmpty()){
                        Map<Integer, List<Long>> serverIdLoadIdListMap = new HashMap<>();
                        for(long humanId : selectIdList){
                            int serverId = Utils.getServerIdByHumanId(humanId);
                            serverIdLoadIdListMap.computeIfAbsent(serverId, k -> new ArrayList<>()).add(humanId);
                        }
                        for(Map.Entry<Integer, List<Long>> entry : serverIdLoadIdListMap.entrySet()){
                            try {
                                HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance(DistrKit.getWorldNodeID(entry.getKey()));
                                proxy.getSyncHumanBrief(entry.getValue());
                            } catch (Exception e){
                                Log.temp.error("===getSyncHumanBrief error, serverId={}", entry.getKey(), e);
                            }
                        }
                    }
                });
            }
            gvgBattle.loadIndex++;
            if(isAdd){
                ++getIndex;
            }
        }catch (Exception e){
            Log.temp.error("getDataBattleAllList error ", e);
        }
    }

    private boolean isAddIndex(boolean isAdd, int index, Map<Integer, List<Long>> roadHumanListMap, List<Long> humanIdList){
        for(List<Long> roadHumanIdList : roadHumanListMap.values()){
            if(roadHumanIdList == null || roadHumanIdList.isEmpty()){
                continue;
            }
            int roadSize = roadHumanIdList.size();
            int openIndex = index * ParamKey.guildLoadNum;
            if(openIndex >= roadSize){
                continue;
            }
            int endIndex = (index + 1) *  ParamKey.guildLoadNum;
            if(endIndex > roadSize){
                endIndex = roadSize;
            }
            List<Long> tempIdList = roadHumanIdList.subList(openIndex, endIndex);
            if(tempIdList == null || tempIdList.isEmpty()){
                continue;
            }
            humanIdList.addAll(tempIdList);
            isAdd = false;
        }
        return isAdd;
    }

    public void removeMatchGuild(long guildId){
        boolean match = guildIdMatchIdMap.containsKey(guildId);
        if (!match){
            return;
        }
        Long matchId = guildIdMatchIdMap.get(guildId);

        guildIdPoadHumanIdMap.remove(guildId);
        //去除持久化
        GuildLeagueMatch crossMatch = matchMap.get(matchId);
        String _guildIdList = crossMatch.getGuildIdList();
        List<Long> guildIdList = Utils.strToLongList(_guildIdList);
        guildIdList.remove(guildId);
        crossMatch.setGuildIdList(Utils.listToString(guildIdList));

        String _battleNumGuildIdMap = crossMatch.getBattleNumGuildIdMap();
        Map<Integer, Map<Long, Long>> battleNumGuildIdMap = Utils.jsonToIntMapLongLong(_battleNumGuildIdMap);
        Map<Integer, Map<Long, Long>> battleNumGuildIdMap2 = new HashMap<>();
        for (Map.Entry<Integer, Map<Long, Long>> entry : battleNumGuildIdMap.entrySet()) {
            Integer day = entry.getKey();
            Map<Long, Long> valueMap = entry.getValue();
            Map<Long, Long> valueMap2 = new HashMap<>();
            if (valueMap.containsKey(guildId)){
                Long aLong = valueMap.get(guildId);
                valueMap2.put(aLong, 0L);
            }
            if (valueMap.containsValue(guildId)){
                for (Map.Entry<Long, Long> entry2 : valueMap.entrySet()) {
                    if (entry2.getValue() == guildId){
                        valueMap2.put(entry2.getKey(), 0L);
                        break;
                    }
                }
            }
            battleNumGuildIdMap2.put(day, valueMap2);
        }
        crossMatch.setBattleNumGuildIdMap(Utils.mapIntMapLongLongToJSON(battleNumGuildIdMap2));


        //移除内存中的
        for (Integer day : dayGuildIdRivalIdMap.keySet()) {
            Map<Long, Long> guildIdRivalIdMap = dayGuildIdRivalIdMap.get(day);
            Map<Long, Long> guildIdRivalIdMap2 = new HashMap<>();
            if (guildIdRivalIdMap.containsKey(guildId)){
                Long aLong = guildIdRivalIdMap.get(guildId);
                guildIdRivalIdMap2.put(aLong, 0L);
            }
            if (guildIdRivalIdMap.containsValue(guildId)){
                for (Map.Entry<Long, Long> entry2 : guildIdRivalIdMap.entrySet()) {
                    if (entry2.getValue() == guildId){
                        guildIdRivalIdMap2.put(entry2.getKey(), 0L);
                        break;
                    }
                }
            }
            dayGuildIdRivalIdMap.put(day, guildIdRivalIdMap2);
        }

        guildIdMatchIdMap.remove(guildId);
        guildIdAddScoreAllMap.remove(guildId);
        guildIdBattleMap.remove(guildId);
        guildIdIndexMap.remove(guildId);
        winGuildIdList.remove(guildId);
    }
}
