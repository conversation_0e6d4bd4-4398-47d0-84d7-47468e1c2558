package org.gof.demo.worldsrv.guild.league.match;

import com.alibaba.fastjson.JSONObject;
import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.interfaces.ISerilizable;
import org.gof.demo.worldsrv.bridgeEntity.GuildLeagueEnroll;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class LeagueBattleGroup implements ISerilizable {
    // 工会id, 上中下路，玩家id集合
    private Map<Long, Map<Integer, List<Long>>> guildIdPoadHumanIdMap = new HashMap<>();

    public LeagueBattleGroup() {

    }


    @Override
    public void writeTo(OutputStream out) throws IOException {
        out.write(guildIdPoadHumanIdMap);
    }

    @Override
    public void readFrom(InputStream in) throws IOException {
        guildIdPoadHumanIdMap = in.read();
    }

    public String toJSON(){
        JSONObject jo = new JSONObject();
        jo.put("guildIdPoadHumanIdMap", guildIdPoadHumanIdMap);
        return jo.toJSONString();
    }

}
