package org.gof.demo.worldsrv.guild;

import com.alibaba.fastjson.JSONObject;
import org.gof.core.*;
import org.gof.core.dbsrv.redis.EntityManager;
import org.gof.core.dbsrv.redis.RedisTools;
import org.gof.core.interfaces.ISerilizable;
import org.gof.core.support.Param;
import org.gof.core.support.ParamKey;
import org.gof.core.support.Time;
import org.gof.core.support.Utils;
import org.gof.demo.battlesrv.battle.*;
import org.gof.demo.battlesrv.battle.enumVo.AttribDefine;
import org.gof.demo.worldsrv.bridgeEntity.GuildLeagueHistory;
import org.gof.demo.worldsrv.bridgeEntity.GuildLeagueHistoryWarmUp;
import org.gof.demo.worldsrv.bridgeEntity.GuildLeagueMatch;
import org.gof.demo.worldsrv.bridgeEntity.GuildLeagueMatchWarmUp;
import org.gof.demo.worldsrv.config.ConfGlobal;
import org.gof.demo.worldsrv.entity.HumanBrief;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.guild.league.GuildLeagueUtils;
import org.gof.demo.worldsrv.human.HumanData;
import org.gof.demo.worldsrv.human.HumanManager;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgBattle;
import org.gof.demo.worldsrv.msg.MsgGvg;
import org.gof.demo.worldsrv.rank.RankInfo;
import org.gof.demo.worldsrv.redis.RedisKeys;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.StringZipUtils;
import org.gof.demo.worldsrv.support.enumKey.ConfGlobalKey;
import org.gof.demo.worldsrv.team.TeamMember;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

public class GuildGvgBattle implements ISerilizable {
    // 工会id
    public long guildIdA = 0;

    public long guildIdB = 0;

    public String dateStr = "";
    public int group = 0;
    public int grade = 0;
    public int round = 0;

    public long winGuildId = 0;

    public int loadIndex = 0;

    public List<RankInfo> rankInfoListA = new ArrayList<>();
    public List<RankInfo> rankInfoListB = new ArrayList<>();

    public Map<Integer, List<Long>> roadHumanListMapA = new ConcurrentHashMap<>();
    public Map<Integer, List<Long>> roadHumanListMapB = new ConcurrentHashMap<>();

    public Map<Integer, Queue<Long>> roadHumanListBattleA = new ConcurrentHashMap<>();
    public Map<Integer, Queue<Long>> roadHumanListBattleB = new ConcurrentHashMap<>();

    public Map<Long, Integer> humanIdKillNumMap = new ConcurrentHashMap<>();
    public Map<Integer, Long> roadWinHumanIdMap = new HashMap<>();

    public Map<Long, HumanBrief> humanIdBriefMap = new HashMap<>();
    public Map<Long, HumanData> humanIdDataMap = new HashMap<>();
    public Map<Long,TeamMember> humanIdTeamMemberMap = new HashMap<>();

    // road 1 2 3, 胜利的公会id
    public Map<Integer, Long> roadWinGuildIdMap = new ConcurrentHashMap<>();
    // 三路战斗计数
    public Map<Integer, AtomicInteger> roadAtomicInteger = new ConcurrentHashMap<>();

    public long matchId = 0;

    public int season = 0;

    public boolean isSettle = false;

    public List<GuildLeagueHistory> historyList = new ArrayList<>();
    public List<GuildLeagueHistoryWarmUp> historyWarmUpList = new ArrayList<>();


    public GuildGvgBattle() {

    }

    public GuildGvgBattle(GuildLeagueMatch match, int round, long guildId, long guildIdRival, String dateStr,
                          Map<Integer, List<Long>> mapA, Map<Integer, List<Long>> mapB) {
        group = match.getGroupPvp();
        grade = match.getGradeType();
        season = match.getSeasonSn();
        guildIdA = guildId;
        guildIdB = guildIdRival;
        roadHumanListMapA = new HashMap<>(mapA);
        roadHumanListMapB = new HashMap<>(mapB);
        matchId = match.getId();
        this.dateStr = dateStr;
        this.round = round;

        for(Map.Entry<Integer, List<Long>> entry : roadHumanListMapA.entrySet()){
            int road = entry.getKey();
            Queue<Long> humanIdListBattle = new ArrayDeque<>(entry.getValue());
            Log.temp.info("===guildIdA={} road={} humanIdList={}", guildIdA, road, humanIdListBattle);
            roadHumanListBattleA.put(road, humanIdListBattle);
        }
        for(Map.Entry<Integer, List<Long>> entry : roadHumanListMapB.entrySet()){
            int road = entry.getKey();
            Queue<Long> humanIdListBattle = new ArrayDeque<>(entry.getValue());
            Log.temp.info("===guildIdB={} road={} humanIdList={}", guildIdB, road, humanIdListBattle);
            roadHumanListBattleB.put(road, humanIdListBattle);
        }
    }

    public GuildGvgBattle(GuildLeagueMatchWarmUp match, int round, long guildId, long guildIdRival, String dateStr,
                          Map<Integer, List<Long>> mapA, Map<Integer, List<Long>> mapB) {
        group = match.getGroupPvp();
        grade = match.getGradeType();
        season = match.getSeasonSn();
        guildIdA = guildId;
        guildIdB = guildIdRival;
        roadHumanListMapA = new HashMap<>(mapA);
        roadHumanListMapB = new HashMap<>(mapB);
        matchId = match.getId();
        this.dateStr = dateStr;
        this.round = round;

        for(Map.Entry<Integer, List<Long>> entry : roadHumanListMapA.entrySet()){
            int road = entry.getKey();
            Queue<Long> humanIdListBattle = new ArrayDeque<>(entry.getValue());
            roadHumanListBattleA.put(road, humanIdListBattle);
        }
        for(Map.Entry<Integer, List<Long>> entry : roadHumanListMapB.entrySet()){
            int road = entry.getKey();
            Queue<Long> humanIdListBattle = new ArrayDeque<>(entry.getValue());
            roadHumanListBattleB.put(road, humanIdListBattle);
        }
    }

    public List<GuildLeagueHistory> pulseBattle(int indexPos){
        pulseBattle(indexPos, false);
        return historyList;
    }

    public List<GuildLeagueHistoryWarmUp> pulseBattleWarmUp(int indexPos){
        pulseBattle(indexPos, true);
        return historyWarmUpList;
    }

    public void pulseBattle(int indexPos, boolean isWarmUp){
        boolean isEnd = true;
        // 三路
        for(int road = 1; road <= GuildParamKey.pathMax; road++){
            startBattle(road, indexPos, isWarmUp);
            if(!roadWinGuildIdMap.containsKey(road)){
                isEnd = false;
            }
        }
        if(!isEnd && indexPos >= 100){
            Log.temp.error("===代码逻辑出错，indexPos={}，超过120，请检查", indexPos);
            for(int road = 1; road <= GuildParamKey.pathMax; road++){
                startBattle(road, indexPos, isWarmUp);
                if(!roadWinGuildIdMap.containsKey(road)){
                    Queue<Long> humanIdListA = roadHumanListBattleA.computeIfAbsent(road, k->new ArrayDeque<>());
                    Queue<Long> humanIdListB = roadHumanListBattleB.computeIfAbsent(road, k->new ArrayDeque<>());
                    if(humanIdListA.size() > humanIdListB.size()){
                        roadWinGuildIdMap.put(road, guildIdA);
                    } else {
                        roadWinGuildIdMap.put(road, guildIdB);
                    }
                }
            }
            isEnd = true;
        }

        if(isEnd){
            int numA = 0;
            int numB = 0;
            for(long guildId : roadWinGuildIdMap.values()){
                if(guildId == guildIdA){
                    numA ++;
                } else if(guildId == guildIdB){
                    numB ++;
                }
            }
            if(numA > numB){
                winGuildId = guildIdA;
            } else if(numB > numA){
                winGuildId = guildIdB;
            } else {
                if(Utils.random(2) == 1){
                    winGuildId = guildIdA;
                } else {
                    winGuildId = guildIdB;
                }
            }
        }
    }

    public void startBattle(int road, int indexPos, boolean isWarmUp) {
        try {
            if (roadWinGuildIdMap.containsKey(road)) {
                return;
            }
            Queue<Long> humanIdListA = roadHumanListBattleA.computeIfAbsent(road, k->new ArrayDeque<>());
            Queue<Long> humanIdListB = roadHumanListBattleB.computeIfAbsent(road, k->new ArrayDeque<>());

            if (humanIdListA.isEmpty()) {
                settleRoadWin(road, guildIdA, guildIdB,humanIdListA, humanIdListB, false);
                return;
            }
            if (humanIdListB.isEmpty()) {
                settleRoadWin(road, guildIdA, guildIdB,humanIdListA, humanIdListB, true);
                return;
            }

            AtomicInteger battleRoad = roadAtomicInteger.computeIfAbsent(road,k->new AtomicInteger(0));
            battleRoad.incrementAndGet();

            long humanIdA = Utils.longValue(humanIdListA.poll());
            long humanIdB = Utils.longValue(humanIdListB.poll());

            AtomicBoolean getDataA = new AtomicBoolean(humanIdDataMap.containsKey(humanIdA));
            AtomicBoolean getDataB = new AtomicBoolean(humanIdDataMap.containsKey(humanIdB));

            Runnable runnable = () -> {
                if(getDataA.get() && getDataB.get()){
                    settleBattleAB(humanIdA, humanIdB, road, indexPos, isWarmUp);
                }
            };

            if(!getDataA.get()){
                HumanData.getHumanDataAsync(humanIdA, ret -> {
                    getDataA.set(true);
                    if (ret.failed()) {
                        Log.temp.info("获取玩家战斗属性失败，humanId={}, {}", humanIdA, ret.cause());
                    }else {
                        HumanData humanData = ret.result();
                        long humanId =humanData.human.getId();
                        humanIdDataMap.put(humanId, humanData);
                    }
                    runnable.run();
                });
            }
            if(!getDataB.get()){
                HumanData.getHumanDataAsync(humanIdB, ret -> {
                    getDataB.set(true);
                    if (ret.failed()) {
                        Log.temp.info("获取玩家战斗属性失败，humanId={}, {}", humanIdB, ret.cause());
                    }else {
                        HumanData humanData = ret.result();
                        long humanId =humanData.human.getId();
                        humanIdDataMap.put(humanId, humanData);
                    }
                    runnable.run();
                });
            }
            runnable.run();

        } catch (Exception e) {
            Log.temp.error("战斗失败，road={}, {}", road, e.getStackTrace());
        }
    }

    public void settleBattleAB(long humanIdA, long humanIdB, int road, int indexPos, boolean isWarmUp){
        Queue<Long> humanIdListA = roadHumanListBattleA.computeIfAbsent(road, k->new ArrayDeque<>());
        Queue<Long> humanIdListB = roadHumanListBattleB.computeIfAbsent(road, k->new ArrayDeque<>());
        long winHumanId = 0;
        try{
            HumanData humanData = humanIdDataMap.getOrDefault(humanIdA,null);
            HumanData humanDataRival =  humanIdDataMap.getOrDefault(humanIdB,null);

            if(humanData == null || humanDataRival == null){
                if(humanData != null){
                    humanIdListA.add(humanIdA);
                }
                if(humanDataRival != null){
                    humanIdListB.add(humanIdB);
                }
                return;
            }

            TeamMember member = humanIdTeamMemberMap.computeIfAbsent(humanIdA ,k -> new TeamMember(humanIdDataMap.get(k)));
            TeamMember memberRival = humanIdTeamMemberMap.computeIfAbsent(humanIdB ,k -> new TeamMember(humanIdDataMap.get(k)));

            Define.p_battle_role.Builder atkBattle = member.p_battle.toBuilder();
            Define.p_battle_role.Builder defBattle = memberRival.p_battle.toBuilder();

            BigDecimal combatA = new BigDecimal(humanData.human.getCombat());
            BigDecimal combatB = new BigDecimal(humanDataRival.human.getCombat());
            int value = combatA.compareTo(combatB);
            // -1 combat 小于 combatNew ， 0 相等， 1 combat 大于 combatNew
            winHumanId = value >= 0 ? humanIdA : humanIdB;
            long seed = Port.getTime();
            long hp1=0,hp2=0;
            try {
                Define.p_battle_role atkData =atkBattle.build();
                Define.p_battle_role defData =defBattle.build();
                hp1 = BattleDataFill.getAttrib(AttribDefine.hp.getValue(), atkData.getAttrListList());
                hp2 = BattleDataFill.getAttrib(AttribDefine.hp.getValue(), defData.getAttrListList());
                Log.temp.info("====战斗前校验血量，humanIdA={} ：{}，humanIdB={}：{}", humanIdA,hp1, humanIdB,hp2);

                MsgBattle.battle_result_c2s battleResult = BattleLib.doGvgBattle(seed / Time.SEC, atkData, defData);
                hp1 = battleResult.getExt(0).getV();
                hp2 = battleResult.getExt(1).getV();
                winHumanId = hp1>=hp2?humanIdA:humanIdB;
                Log.temp.info("====战斗结果，humanIdA={}，humanIdB={}, winner is {}", humanIdA, humanIdB, winHumanId);
            } catch (Exception e) {
                Log.temp.error("====战斗出错，humanIdA={}，humanIdB={},{}", humanIdA, humanIdB, e.getStackTrace());
            }

            roadWinHumanIdMap.put(road, winHumanId);

            /// 保存战报
            Param paramA = new Param();
            paramA.put("hp", hp1);
            int killNum1 = humanIdKillNumMap.getOrDefault(humanIdA, 0);
            int buffSn1 = GlobalConfVal.getGuildKillNumBuffSn(killNum1);
            if(buffSn1 > 0) {
                paramA.put("buffSn", buffSn1);
            }

            Param paramB = new Param();
            paramB.put("hp", hp2);
            int killNum2 = humanIdKillNumMap.getOrDefault(humanIdB, 0);
            int buffSn2 = GlobalConfVal.getGuildKillNumBuffSn(killNum2);
            if(buffSn2 > 0) {
                paramB.put("buffSn", buffSn2);
            }

            JSONObject joAtk = new JSONObject();
            joAtk.put("json", member.p_battle.toByteArray());
            joAtk.put("param", paramA.toJsonString());
            JSONObject joDef = new JSONObject();
            joDef.put("json", memberRival.p_battle.toByteArray());
            joDef.put("param", paramB.toJsonString());

            if(winHumanId==humanIdA){
                killNum1 += 1;
                humanIdKillNumMap.put(humanIdA, killNum1);
            }else{
                killNum2 += 1;
                humanIdKillNumMap.put(humanIdB, killNum2);
            }
            joAtk.put("kill", killNum1);
            joDef.put("kill", killNum2);

            long vid=Port.applyId();
            JSONObject jo = new JSONObject();
            jo.put("vid", vid);
            jo.put("time", seed / Time.SEC);
            jo.put("guildA", guildIdA);
            jo.put("guildB", guildIdB);
            jo.put("atkId", humanIdA);
            jo.put("atkName", member.human.getName());
            jo.put("num", 0);
            jo.put("defId", humanIdB);
            jo.put("defName", memberRival.human.getName());
            jo.put("atk_role", joAtk);
            jo.put("def_role", joDef);
            jo.put("expired", 0);
            jo.put("winId", winHumanId);

            int killNum = humanIdKillNumMap.getOrDefault(winHumanId, 0);
            if(killNum >= ConfGlobal.get(ConfGlobalKey.familybattle_defeat_Number_news.SN).value){
                jo.put("type", GuildParamKey.battleType_1);
            }
            if(winHumanId == humanIdA){
                humanIdListA.add(humanIdA);
                if(humanIdListB.isEmpty()){
                    jo.put("typeResult", guildIdA);
                }
            }else{
                humanIdListB.add(humanIdB);
                if(humanIdListA.isEmpty()){
                    jo.put("typeResult", guildIdB);
                }
            }

            String keyA = RedisKeys.guild_league_week_road_log + guildIdA + Utils.formatTimeToInt(Port.getTime()) + road;
            RedisTools.addToSet(EntityManager.redisClient, RedisKeys.week_remove_log, keyA);

            String json = StringZipUtils.zip(jo.toJSONString());

            List<String> keyList = new ArrayList<>();
            keyList.add(keyA);
            keyList.add(String.valueOf(seed));
            keyList.add(json);
            EntityManager.redisClient.zadd(keyList, r->{
                if(!r.succeeded()){
                    Log.temp.error("====保存失败，keyList={}", keyList);
                }
            });

            Log.temp.info("====保存战报 {} guildIdA={}, guildIdB={}, road={}, winId={}, humanIdA={}, humanIdB={}, winKillNum={}",
                    keyA+" "+vid,guildIdA, guildIdB, road, winHumanId, humanIdA, humanIdB, humanIdKillNumMap.get(winHumanId));

            String keyB = RedisKeys.guild_league_week_road_log + guildIdB + Utils.formatTimeToInt(Port.getTime()) + road;
            RedisTools.addToSet(EntityManager.redisClient, RedisKeys.week_remove_log, keyB);

            List<String> keyListB = new ArrayList<>();
            keyListB.add(keyB);
            keyListB.add(String.valueOf(seed));
            keyListB.add(json);
            EntityManager.redisClient.zadd(keyListB, r->{
                if(!r.succeeded()){
                    Log.temp.error("====保存失败，keyList={}", keyListB);
                }
            });

            // 更新乱斗玩家的剩余血量和连胜buff
            GuildLeagueUtils.updateGvgPlayerLeftHpAndBuff(atkBattle, hp1, buffSn1);
            member.p_battle = atkBattle.build();
            humanIdTeamMemberMap.put(humanIdA, member);

            GuildLeagueUtils.updateGvgPlayerLeftHpAndBuff(defBattle, hp2, buffSn2);
            memberRival.p_battle = defBattle.build();
            humanIdTeamMemberMap.put(humanIdB, memberRival);
        } catch (Exception e){
            if (!humanIdListA.contains(humanIdA)) {
                humanIdListA.add(humanIdA);
            }
            if (!humanIdListB.contains(humanIdB)) {
                humanIdListB.add(humanIdB);
            }
            Log.temp.error("====战斗出错，humanIdA={}，humanIdB={}，{}", humanIdA, humanIdB, e.getStackTrace());
        } finally {
            AtomicInteger battleRoad = roadAtomicInteger.computeIfAbsent(road,k->new AtomicInteger(0));
            battleRoad.decrementAndGet();
            if(winHumanId > 0 && (humanIdListA.isEmpty() || humanIdListB.isEmpty())){
                settleRoadWin(road, guildIdA, guildIdB, humanIdListA, humanIdListB, winHumanId == humanIdA);
            }
        }
    }

    private void settleRoadWin(int road, long guildIdA, long guildIdB, Queue<Long> humanIdListA, Queue<Long> humanIdListB, boolean isWinA){
        AtomicInteger battleRoad = roadAtomicInteger.computeIfAbsent(road,k->new AtomicInteger(0));
        if(battleRoad.get() > 0) {
            return;
        }
        if(roadWinGuildIdMap.containsKey(road)){
            return;
        }
        long guildWinner= isWinA?guildIdA:guildIdB;
        roadWinGuildIdMap.put(road, guildWinner);
        Log.temp.info("===战斗分线结果 road={}，guildA={}, guildB={}, aNum={},bNum={}, isWinAHuman={}",
                road, guildIdA, guildIdB, humanIdListA.size(), humanIdListB.size(), isWinA);
    }

    /**
     * 异常结算
     * @param road
     * @param guildIdA
     * @param guildIdB
     */
    public void settleRoadWinError(int road, long guildIdA, long guildIdB){
        int num = roadAtomicInteger.computeIfAbsent(road,k->new AtomicInteger(0)).get();
        Queue<Long> humanIdListA = roadHumanListBattleA.computeIfAbsent(road, k->new ArrayDeque<>());
        Queue<Long> humanIdListB = roadHumanListBattleB.computeIfAbsent(road, k->new ArrayDeque<>());
        if(humanIdListA.size() > humanIdListB.size()){
            roadWinGuildIdMap.put(road, guildIdA);
        } else if(humanIdListB.size() > humanIdListA.size()){
            roadWinGuildIdMap.put(road, guildIdB);
        } else {
            if(Utils.random(2) == 1){
                roadWinGuildIdMap.put(road, guildIdA);
            } else {
                roadWinGuildIdMap.put(road, guildIdB);
            }
        }
        Log.temp.error("====异常结算：guildIdA={}，guildIdB={}，road={}，winGuildId={}， humanIdListA={}，humanIdListB={}，winHumanId={}, loseHumanIdList={}, num={}",
                guildIdA, guildIdB, road, roadWinGuildIdMap.get(road), humanIdListA, humanIdListB, roadWinHumanIdMap.get(road), null, num);
    }

    private void createGuildLeagueHistoryWarmUp(int group, int season, int week, long guildIdA, long humanIdA,
                                                long guildIdB, long humanIdB, long winGuildId, int battleNum, int road) {
        GuildLeagueHistoryWarmUp history = new GuildLeagueHistoryWarmUp();
        history.setSeasonSn(season);
        history.setWeek(week);
        history.setGroupPvp(group);
        history.setGuildA(guildIdA);
        history.setGuildB(guildIdB);
        history.setWinGuildId(winGuildId);
        history.setHumanIdA(humanIdA);
        history.setHumanIdB(humanIdB);
        history.setBattleNum(battleNum);
        history.setRoad(road);
        history.setTime(Utils.getTimeSec());
        history.persist();
        historyWarmUpList.add(0,history);
    }

    private void createGuildLeagueHistory(int group, int season, int week, long guildIdA, long humanIdA,
                                          long guildIdB, long humanIdB, long winGuildId, int battleNum, int road) {
        GuildLeagueHistory history = new GuildLeagueHistory();
        history.setSeasonSn(season);
        history.setWeek(week);
        history.setGroupPvp(group);
        history.setGuildA(guildIdA);
        history.setGuildB(guildIdB);
        history.setWinGuildId(winGuildId);
        history.setHumanIdA(humanIdA);
        history.setHumanIdB(humanIdB);
        history.setBattleNum(battleNum);
        history.setRoad(road);
        history.setTime(Utils.getTimeSec());
        history.persist();
        historyList.add(0, history);
    }

    @Override
    public void writeTo(OutputStream out) throws IOException {
    //        out.write();
    }

    @Override
    public void readFrom(InputStream in) throws IOException {
    //         = in.read();
    }

    public String toJSON(){
        JSONObject jo = new JSONObject();
        return jo.toJSONString();
    }
}

