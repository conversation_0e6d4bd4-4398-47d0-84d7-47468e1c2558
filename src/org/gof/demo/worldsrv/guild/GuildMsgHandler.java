package org.gof.demo.worldsrv.guild;

import org.gof.core.support.observer.MsgReceiver;
import org.gof.demo.seam.msg.MsgParam;
import org.gof.demo.worldsrv.config.ConfQuiz;
import org.gof.demo.worldsrv.msg.MsgDungeon;
import org.gof.demo.worldsrv.msg.MsgGuild;
import org.gof.demo.worldsrv.msg.MsgGuild.*;
import org.gof.demo.worldsrv.msg.MsgGuildBoss;
import org.gof.demo.worldsrv.msg.MsgGvg;

public class GuildMsgHandler {

    /**
     * 客户端请求公会信息
     *
     * <AUTHOR>
     * @Date 2024/4/2
     * @Param
     */
    @MsgReceiver(guild_info_c2s.class)
    public void _msg_guild_info_c2s(MsgParam param) {
        guild_info_c2s msg = param.getMsg();
        GuildManager.inst()._msg_guild_info_c2s(param.getHumanObject(), msg.getGuildId());
    }

    /**
     * 客户端请求创建公会
     *
     * <AUTHOR>
     * @Date 2024/4/2
     * @Param
     */
    @MsgReceiver(guild_create_c2s.class)
    public void _msg_guild_create_c2s(MsgParam param) {
        guild_create_c2s msg = param.getMsg();
        GuildManager.inst()._msg_guild_create_c2s(param.getHumanObject(), msg.getName());
    }

    /**
     * 客户端请求搜索公会
     * TODO 查询规则，返回
     * <AUTHOR>
     * @Date 2024/4/2
     * @Param
     */
    @MsgReceiver(guild_search_c2s.class)
    public void _msg_guild_search_c2s(MsgParam param) {
        guild_search_c2s msg = param.getMsg();
        GuildManager.inst()._msg_guild_search_c2s(param.getHumanObject(), msg.getType(), msg.getSearchKey(), msg.getPage());
    }

    /**
     * 客户端请求加入公会
     *
     * <AUTHOR>
     * @Date 2024/4/2
     * @Param
     */
    @MsgReceiver(guild_join_c2s.class)
    public void _msg_guild_join_c2s(MsgParam param) {
        guild_join_c2s msg = param.getMsg();
        GuildManager.inst()._msg_guild_join_c2s(param.getHumanObject(), msg.getGuildId());
    }

    /**
     * 客户端请求快速加入公会
     *
     * <AUTHOR>
     * @Date 2024/4/2
     * @Param
     */
    @MsgReceiver(guild_quick_join_c2s.class)
    public void _msg_guild_quick_join_c2s(MsgParam param) {
        GuildManager.inst()._msg_guild_quick_join_c2s(param.getHumanObject());
    }


    /**
     * 客户端请求设置公会参数
     *
     * <AUTHOR>
     * @Date 2024/4/2
     * @Param
     */
    @MsgReceiver(guild_setting_c2s.class)
    public void _msg_guild_setting_c2s(MsgParam param) {
        guild_setting_c2s msg = param.getMsg();
        GuildManager.inst()._msg_guild_setting_c2s(param.getHumanObject(), msg.getK(), msg.getV(), msg.getS(), msg.getLList());
    }

    /**
     * 客户端请求申请加入公会
     * <AUTHOR>
     * @Date 2024/4/2
     * @Param // TODO 是申请 还是 请求申请列表
     */
    @MsgReceiver(guild_apply_c2s.class)
    public void _msg_guild_apply_c2s(MsgParam param) {
        guild_apply_c2s msg = param.getMsg();
        GuildManager.inst()._msg_guild_apply_c2s(param.getHumanObject(), msg.getGuildId());
    }

    /**
     * 客户端请求审批公会申请
     * <AUTHOR>
     * @Date 2024/4/2
     * @Param
     */
    @MsgReceiver(guild_approve_c2s.class)
    public void _msg_guild_approve_c2s(MsgParam param) {
        guild_approve_c2s msg = param.getMsg();
        GuildManager.inst()._msg_guild_approve_c2s(param.getHumanObject(), msg.getApplyUid(), msg.getType());
    }

    /**
     * 客户端请求踢出公会成员
     *
     * <AUTHOR>
     * @Date 2024/4/2
     * @Param
     */
    @MsgReceiver(guild_kick_out_c2s.class)
    public void _msg_guild_kick_out_c2s(MsgParam param) {
        guild_kick_out_c2s msg = param.getMsg();
        GuildManager.inst()._msg_guild_kick_out_c2s(param.getHumanObject(), msg.getRoleId());
    }

    /**
     * 客户端请求退出公会
     *
     * <AUTHOR>
     * @Date 2024/4/2
     * @Param
     */
    @MsgReceiver(guild_quit_c2s.class)
    public void _msg_guild_quit_c2s(MsgParam param) {
        GuildManager.inst()._msg_guild_quit_c2s(param.getHumanObject());
    }

    /**
     * 客户端请求解散公会
     *
     * <AUTHOR>
     * @Date 2024/4/2
     * @Param
     */
    @MsgReceiver(guild_dissolve_c2s.class)
    public void _msg_guild_dissolve_c2s(MsgParam param) {
        GuildManager.inst()._msg_guild_dissolve_c2s(param.getHumanObject());
    }

    /**
     * 客户端请求更改公会成员职业
     *
     * <AUTHOR>
     * @Date 2024/4/2
     * @Param
     */
    @MsgReceiver(guild_change_career_c2s.class)
    public void _msg_guild_change_career_c2s(MsgParam param) {
        guild_change_career_c2s msg = param.getMsg();
        GuildManager.inst()._msg_guild_change_career_c2s(param.getHumanObject(), msg.getRoleId(), msg.getCareer());
    }

    // TODO
    @MsgReceiver(guild_info_update_s2c.class)
    public void _msg_guild_info_update_s2c(MsgParam param) {
        guild_info_update_s2c msg = param.getMsg();
        GuildManager.inst()._msg_guild_info_update_s2c(param.getHumanObject(), msg.getUpdateList());
    }
    /**
     * 客户端请求公会成员信息
     *
     * <AUTHOR>
     * @Date 2024/4/2
     * @Param
     */
    @MsgReceiver(guild_members_info_c2s.class)
    public void _msg_guild_members_info_c2s(MsgParam param) {
        guild_members_info_c2s msg = param.getMsg();
        GuildManager.inst()._msg_guild_members_info_c2s(param.getHumanObject(), msg.getGuildId());
    }

    /**
     * 客户端请求公会捐献
     *
     * <AUTHOR>
     * @Date 2024/4/2
     * @Param
     */
    @MsgReceiver(guild_donate_c2s.class)
    public void _msg_guild_donate_c2s(MsgParam param) {
        guild_donate_c2s msg = param.getMsg();
        GuildManager.inst()._msg_guild_donate_c2s(param.getHumanObject());
    }

    /**
     * 客户端请求公会申请列表
     *
     * <AUTHOR>
     * @Date 2024/4/2
     * @Param
     */
    @MsgReceiver(guild_apply_list_c2s.class)
    public void _msg_guild_apply_list_c2s(MsgParam param) {
        GuildManager.inst()._msg_guild_apply_list_c2s(param.getHumanObject());
    }

    /**
     * 客户端请求公会排名信息
     * TODO
     * <AUTHOR>
     * @Date 2024/4/2
     * @Param
     */
    @MsgReceiver(guild_rank_info_c2s.class)
    public void _msg_guild_rank_info_c2s(MsgParam param) {
        guild_rank_info_c2s msg = param.getMsg();
        GuildManager.inst()._msg_guild_rank_info_c2s(param.getHumanObject(), msg.getRankType(), msg.getPage(), msg.getGuildId());
    }

    /**
     * 客户端请求我的公会排名信息
     *
     * <AUTHOR>
     * @Date 2024/4/2
     * @Param
     */
    @MsgReceiver(guild_rank_my_info_c2s.class)
    public void _msg_guild_rank_my_info_c2s(MsgParam param) {
        guild_rank_my_info_c2s msg = param.getMsg();
        GuildManager.inst()._msg_guild_rank_my_info_c2s(param.getHumanObject(), msg.getRankType());
    }

    /**
     * 趣味竞答请求当前题目
     * @param param
     */
    @MsgReceiver(guild_question_c2s.class)
    public void _msg_guild_question_c2s(MsgParam param) {
        GuildManager.inst()._msg_guild_question_c2s(param.getHumanObject());
    }

    /**
     * 趣味竞答请求骰子信息
     * @param param
     */
    @MsgReceiver(guild_dice_start_c2s.class)
    public void _msg_guild_dice_start_c2s(MsgParam param) {
        GuildManager.inst()._msg_guild_dice_start_c2s(param.getHumanObject());
    }

    /**
     * 趣味竞答请求骰子点数
     * @param param
     */
    @MsgReceiver(guild_dice_point_c2s.class)
    public void _msg_guild_dice_point_c2s(MsgParam param) {
        GuildManager.inst()._msg_guild_dice_point_c2s(param.getHumanObject());
    }


    /**
     * 客户端请求进入公会区域
     *
     * <AUTHOR>
     * @Date 2024/4/2
     * @Param
     */
    @MsgReceiver(guild_area_enter_c2s.class)
    public void _msg_guild_area_enter_c2s(MsgParam param) {
        guild_area_enter_c2s msg = param.getMsg();
        GuildManager.inst()._msg_guild_area_enter_c2s(param.getHumanObject(), msg.getPos(), msg.getBreak());
    }

    @MsgReceiver(guild_area_exit_c2s.class)
    public void _msg_guild_area_exit_c2s(MsgParam param) {
        guild_area_exit_c2s msg = param.getMsg();
        GuildManager.inst()._msg_guild_area_exit_c2s(param.getHumanObject());
    }

    @MsgReceiver(guild_area_move_c2s.class)
    public void _msg_guild_area_move_c2s(MsgParam param) {
        guild_area_move_c2s msg = param.getMsg();
        GuildManager.inst()._msg_guild_area_move_c2s(param.getHumanObject(), msg.getPosListList());
    }

    @MsgReceiver(guild_help_info_c2s.class)
    public void _msg_guild_help_info_c2s(MsgParam param) {
        guild_help_info_c2s msg = param.getMsg();
        GuildManager.inst()._msg_guild_help_info_c2s(param.getHumanObject(), msg.getType());
    }

    /**
     * 客户端请求公会求助
     *
     * <AUTHOR>
     * @Date 2024/4/2
     * @Param
     */
    @MsgReceiver(guild_help_ask_c2s.class)
    public void _msg_guild_help_ask_c2s(MsgParam param) {
        guild_help_ask_c2s msg = param.getMsg();
        GuildManager.inst()._msg_guild_help_ask_c2s(param.getHumanObject(), msg.getType(), msg.getSubType());
    }

    /**
     * 客户端请求提供公会帮助
     *
     * <AUTHOR>
     * @Date 2024/4/2
     * @Param
     */
    @MsgReceiver(guild_help_c2s.class)
    public void _msg_guild_help_c2s(MsgParam param) {
        guild_help_c2s msg = param.getMsg();
        GuildManager.inst()._msg_guild_help_c2s(param.getHumanObject(), msg.getHelpId());
    }


    /**
     * 客户端请求公会帮助状态
     *
     * <AUTHOR>
     * @Date 2024/4/2
     * @Param
     */
    @MsgReceiver(guild_help_status_c2s.class)
    public void _msg_guild_help_status_c2s(MsgParam param) {
        GuildManager.inst()._msg_guild_help_status_c2s(param.getHumanObject());
    }

    /**
     * 客户端请求公会宝箱信息
     *
     * <AUTHOR>
     * @Date 2024/4/2
     * @Param
     */
    @MsgReceiver(guild_treasure_info_c2s.class)
    public void _msg_guild_treasure_info_c2s(MsgParam param) {
        GuildManager.inst()._msg_guild_treasure_info_c2s(param.getHumanObject());
    }

    /**
     * 客户端请求开启公会宝箱
     *
     * <AUTHOR>
     * @Date 2024/4/2
     * @Param
     */
    @MsgReceiver(guild_treasure_open_c2s.class)
    public void _msg_guild_treasure_open_c2s(MsgParam param) {
        guild_treasure_open_c2s msg = param.getMsg();
        GuildManager.inst()._msg_guild_treasure_open_c2s(param.getHumanObject(), msg.getRound(), msg.getN());
    }

    /**
     * 客户端请求公会日程信息
     *
     * <AUTHOR>
     * @Date 2024/4/2
     * @Param
     */
    @MsgReceiver(guild_schedule_c2s.class)
    public void _msg_guild_schedule_c2s(MsgParam param) {
        guild_schedule_c2s msg = param.getMsg();
        GuildManager.inst()._msg_guild_schedule_c2s(param.getHumanObject());
    }

    /**
     * 客户端请求公会日志信息
     *
     * <AUTHOR>
     * @Date 2024/4/2
     * @Param
     */
    @MsgReceiver(guild_log_c2s.class)
    public void _msg_guild_log_c2s(MsgParam param) {
        guild_log_c2s msg = param.getMsg();
        GuildManager.inst()._msg_guild_log_c2s(param.getHumanObject(), msg.getGuildLogId());
    }


    /** 
     * 请求boss数据讨伐郁郁胖头鱼
     * <AUTHOR>
     * @Date 2024/5/9
     * @Param 
     */
    @MsgReceiver(MsgGuildBoss.guild_boss_info_c2s.class)
    public void _msg_guild_boss_info_c2s(MsgParam param) {
        GuildManager.inst()._msg_guild_boss_info_c2s(param.getHumanObject());
    }



    /** 
     * 报名讨伐 讨伐郁郁胖头鱼 
     * <AUTHOR>
     * @Date 2024/5/9
     * @Param 
     */
    @MsgReceiver(MsgGuildBoss.guild_boss_sign_up_c2s.class)
    public void _msg_guild_boss_sign_up_c2s(MsgParam param) {
        GuildManager.inst()._msg_guild_boss_sign_up_c2s(param.getHumanObject());
    }


    /** 
     * 讨伐郁郁胖头鱼  进入boss战斗
     * <AUTHOR>
     * @Date 2024/5/9
     * @Param 
     */
    @MsgReceiver(MsgGuildBoss.guild_boss_enter_c2s.class)
    public void _msg_guild_boss_enter_c2s(MsgParam param) {
        GuildManager.inst()._msg_guild_boss_enter_c2s(param.getHumanObject());
    }

    @MsgReceiver(MsgGuildBoss.guild_boss_exit_c2s.class)
    public void _msg_guild_boss_exit_c2s(MsgParam param) {
        GuildManager.inst()._msg_guild_boss_exit_c2s(param.getHumanObject());
    }

    /**
     * 家族单人副本（征战熔岩巨兽）信息
     */
    @MsgReceiver(MsgDungeon.dungeon_league_solo_info_c2s.class)
    public void _msg_dungeon_league_solo_info_c2s(MsgParam param) {
        MsgDungeon.dungeon_league_solo_info_c2s msg =  param.getMsg();
        GuildManager.inst()._msg_dungeon_league_solo_info_c2s(param.getHumanObject());
    }

    /**
     * 领取宝箱奖励  征战熔岩巨兽
     * <AUTHOR>
     * @Date 2024/5/9
     * @Param
     */
    @MsgReceiver(MsgDungeon.dungeon_league_solo_get_reward_c2s.class)
    public void _msg_dungeon_league_solo_get_reward_c2s(MsgParam param) {
        MsgDungeon.dungeon_league_solo_get_reward_c2s msg = param.getMsg();
        GuildManager.inst()._msg_dungeon_league_solo_get_reward_c2s(param.getHumanObject(), msg.getType());
    }

    /** 
     * 家族乱斗 加入1/2/3路 
     * <AUTHOR>
     * @Date 2024/5/9
     * @Param 
     */
    @MsgReceiver(MsgGvg.gvg_select_road_c2s.class)
    public void _msg_gvg_select_road_c2s(MsgParam param) {
        MsgGvg.gvg_select_road_c2s msg =  param.getMsg();
        GuildManager.inst()._msg_gvg_select_road_c2s(param.getHumanObject(), msg.getRoad());
    }
    /**
     * 家族乱斗 切换分路
     * <AUTHOR>
     * @Date 2024/5/9
     * @Param
     */
    @MsgReceiver(MsgGvg.gvg_road_change_all_c2s.class)
    public void _msg_gvg_road_change_all_c2s(MsgParam param) {
        MsgGvg.gvg_road_change_all_c2s msg =  param.getMsg();
        GuildManager.inst()._msg_gvg_road_change_all_c2s(param.getHumanObject(), msg.getRoad(), msg.getRoad1());
    }
    /**
     * 家族乱斗  保存分路
     * <AUTHOR>
     * @Date 2024/5/9
     * @Param
     */
    @MsgReceiver(MsgGvg.gvg_road_change_c2s .class)
    public void _msg_gvg_road_change_c2s(MsgParam param) {
        MsgGvg.gvg_road_change_c2s msg =  param.getMsg();
        GuildManager.inst()._msg_gvg_road_change_c2s(param.getHumanObject(), msg.getRoad(), msg.getRoadInfoList());
    }

    /**
     * 家族乱斗  战斗回放
     * <AUTHOR>
     * @Date 2024/5/9
     * @Param
     */
    @MsgReceiver(MsgGvg.gvg_play_video_c2s.class)
    public void _msg_gvg_play_video_c2s(MsgParam param) {
        MsgGvg.gvg_play_video_c2s msg =  param.getMsg();
        GuildManager.inst()._msg_gvg_play_video_c2s(param.getHumanObject(), msg.getVid(), msg.getSource());
    }

    /** 
     * 家族乱斗  进入分路战斗
     * <AUTHOR>
     * @Date 2024/5/9
     * @Param 
     */
    @MsgReceiver(MsgGvg.gvg_fight_info_c2s.class)
    public void _msg_gvg_fight_info_c2s(MsgParam param) {
        MsgGvg.gvg_fight_info_c2s msg =  param.getMsg();
        GuildManager.inst()._msg_gvg_fight_info_c2s(param.getHumanObject(), msg.getRoad());
    }

    @MsgReceiver(MsgGvg.gvg_road_info_c2s.class)
    public void _msg_gvg_road_info_c2s(MsgParam param) {
        MsgGvg.gvg_road_info_c2s msg =  param.getMsg();
        GuildManager.inst()._msg_gvg_road_info_c2s(param.getHumanObject(), msg.getType(), msg.getRoad());
    }

    @MsgReceiver(MsgGvg.gvg_fight_report_c2s.class)
    public void _msg_gvg_fight_report_c2s(MsgParam param) {
        MsgGvg.gvg_fight_report_c2s msg =  param.getMsg();
        GuildManager.inst()._msg_gvg_fight_report_c2s(param.getHumanObject(), msg.getRoad());
    }



    @MsgReceiver(MsgGvg.gvg_cancel_red_point_c2s.class)
    public void _msg_gvg_cancel_red_point_c2s(MsgParam param) {
        MsgGvg.gvg_cancel_red_point_c2s msg =  param.getMsg();
        GuildManager.inst()._msg_gvg_cancel_red_point_c2s(param.getHumanObject(), msg.getType());
    }


    @MsgReceiver(MsgGvg.gvg_serv_list_c2s.class)
    public void _msg_gvg_serv_list_c2s(MsgParam param) {
        MsgGvg.gvg_serv_list_c2s msg =  param.getMsg();
        GuildManager.inst()._msg_gvg_serv_list_c2s(param.getHumanObject());
    }



    @MsgReceiver(MsgGvg.gvg_season_info_c2s.class)
    public void _msg_gvg_season_info_c2s(MsgParam param) {
        MsgGvg.gvg_season_info_c2s msg =  param.getMsg();
        GuildManager.inst()._msg_gvg_season_info_c2s(param.getHumanObject());
    }



    @MsgReceiver(MsgGvg.gvg_hall_c2s.class)
    public void _msg_gvg_hall_c2s(MsgParam param) {
        MsgGvg.gvg_hall_c2s msg =  param.getMsg();
        GuildManager.inst()._msg_gvg_hall_c2s(param.getHumanObject());
    }

    @MsgReceiver(MsgGvg.gvg_hall_of_fame_c2s.class)
    public void _msg_gvg_hall_of_fame_c2s(MsgParam param) {
        MsgGvg.gvg_hall_of_fame_c2s msg =  param.getMsg();
        GuildManager.inst()._msg_gvg_hall_of_fame_c2s(param.getHumanObject());
    }
    @MsgReceiver(MsgGvg.gvg_week_rank_c2s.class)
    public void _msg_gvg_week_rank_c2s(MsgParam param) {
        MsgGvg.gvg_week_rank_c2s msg =  param.getMsg();
        GuildManager.inst()._msg_gvg_week_rank_c2s(param.getHumanObject(), msg.getType());
    }
    @MsgReceiver(MsgGvg.gvg_rank_c2s.class)
    public void _msg_gvg_rank_c2s(MsgParam param) {
        GuildManager.inst()._msg_gvg_rank_c2s(param.getHumanObject());
    }



    @MsgReceiver(MsgGvg.gvg_rank_list_c2s.class)
    public void _msg_gvg_rank_list_c2s(MsgParam param) {
        MsgGvg.gvg_rank_list_c2s msg =  param.getMsg();
        GuildManager.inst()._msg_gvg_rank_list_c2s(param.getHumanObject(), msg.getRanking());
    }


    @MsgReceiver(MsgGvg.gvg_info_c2s.class)
    public void _msg_gvg_info_c2s(MsgParam param) {
        MsgGvg.gvg_info_c2s msg =  param.getMsg();
        GuildManager.inst().sendMsg_gvg_info_s2c(param.getHumanObject());
    }

}
