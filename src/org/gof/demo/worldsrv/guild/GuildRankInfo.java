package org.gof.demo.worldsrv.guild;

import com.alibaba.fastjson.JSONObject;
import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.interfaces.ISerilizable;
import java.io.IOException;

public class GuildRankInfo implements ISerilizable {
    public long guildId = 0;
    public int level = 0;
    public long combat = 0;
    public String param ="";

    public GuildRankInfo(){

    }
    public GuildRankInfo(long guildId, int level, long combat, String param){
        this.guildId = guildId;
        this.level = level;
        this.combat = combat;
        this.param = param;
    }

    @Override
    public void writeTo(OutputStream out) throws IOException {
        out.write(guildId);
        out.write(level);
        out.write(combat);
        out.write(param);
    }

    @Override
    public void readFrom(InputStream in) throws IOException {
        guildId = in.read();
        level = in.read();
        combat = in.read();
        param = in.read();
    }


    public String toJSON(){
        JSONObject jo = new JSONObject();
        jo.put("guildId", guildId);
        jo.put("level", level);
        jo.put("combat", combat);
        jo.put("param", param);
        return jo.toJSONString();
    }

}
