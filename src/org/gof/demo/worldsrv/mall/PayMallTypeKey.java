package org.gof.demo.worldsrv.mall;

/**
 * 充值表类型
 * <AUTHOR>
 * @Date 2024/4/22
 * @Param 
 */
public class PayMallTypeKey {
    public static final int PAY_Type_1 = 1;
    public static final int PAY_Type_2 = 2;
    public static final int PAY_Type_3 = 3;     //钻石商店
    public static final int PAY_Type_4 = 4;     //限时弹窗/礼包商店
    public static final int PAY_Type_5 = 5;     //推送礼包
    public static final int PAY_Type_6 = 6;
    public static final int PAY_Type_7 = 7;     //活动礼包
    public static final int PAY_Type_8 = 8;     //星钻商店
    public static final int PAY_Type_9 = 9;
    public static final int PAY_Type_10 = 10;
    public static final int PAY_Type_11 = 11;
    public static final int PAY_Type_12 = 12;   //自选礼包
    public static final int PAY_Type_13 = 13;
    public static final int PAY_Type_20 = 20;
    public static final int PAY_Type_21 = 21;
    public static final int PAY_Type_23 = 23;
    public static final int PAY_Type_25 = 25;
    public static final int PAY_Type_26 = 26;
    public static final int PAY_Type_27 = 27;
    public static final int PAY_Type_28 = 28;   // 应援礼包
    public static final int PAY_Type_29 = 29;   //连冲礼包
    public static final int PAY_Type_30 = 30;   // 多档刷新礼包
    public static final int PAY_Type_31 = 31;   // 皮肤试用礼包
    public static final int PAY_Type_32 = 32;   // 团购礼包

    public static final int resetType_0 = 0;    // 不重置
    public static final int resetType_1 = 1;    // 每日重置
    public static final int resetType_2 = 2;    // 每周重置

    public static final int BUY_CONDITION_TYPE_1 = 1;// 购买条件1类型，功能解锁表

    public static final String pay_condition_open_day = "open_day";
    public static final String pay_condition_act_type = "act_type";
    public static final String pay_condition_dungeon = "dungeon";
    public static final String pay_condition_battlepass = "battlepass";
    public static final String pay_condition_fairybattlepass = "fairybattlepass";
    public static final String pay_condition_wartoken = "wartoken";
    public static final String pay_condition_guard = "guard";
    public static final String pay_condition_pay_reward = "pay_reward";
    public static final String pay_condition_pay_exclude = "pay_exclude";
    public static final String pay_condition_get_reward = "get_reward";
    public static final String pay_condition_shortage = "shortage"; // 道具不足时弹窗
    public static final String pay_condition_draw = "draw";// 抽卡到一定次数
    public static final String pay_condition_flyEgg = "flyegg";// 飞宠蛋为空的时候
    public static final String pay_condition_return = "return";
    public static final String pay_condition_fashionSkin = "fashionSkin_level";
    public static final String pay_condition_any_shortage = "any_shortage";
    public static final String pay_condition_common = "common";

    public static final int wartoken_type_1 = 1; //翻牌活动特权1
    public static final int wartoken_type_2 = 2; //翻牌活动特权2

    //通用礼包条件类型
    public static final String slime_level = "slime_level";
    public static final String dungeon_level = "dungeon_level";
}
