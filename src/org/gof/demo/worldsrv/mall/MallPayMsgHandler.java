package org.gof.demo.worldsrv.mall;

import org.gof.core.support.observer.MsgReceiver;
import org.gof.demo.seam.msg.MsgParam;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgPayMall;

import java.util.List;

public class MallPayMsgHandler {


    /**
     * 请求商店信息的消息
     * <AUTHOR>
     * @Date 2024/3/12
     * @Param 
     */
	@MsgReceiver(MsgPayMall.pay_mall_info_c2s.class)
	public void _msg_pay_mall_info_c2s(MsgParam param) {
		HumanObject humanObj = param.getHumanObject();
        MsgPayMall.pay_mall_info_c2s msg = param.getMsg();
        MallManager.inst()._msg_pay_mall_info_c2s(humanObj, msg.getType());
	}

	/** 
	 * 请求奖励包
	 * <AUTHOR>
	 * @Date 2024/3/12
	 * @Param 
	 */
    @MsgReceiver(MsgPayMall.pay_mall_reward_c2s.class)
    public void _msg_pay_mall_reward_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgPayMall.pay_mall_reward_c2s msg = param.getMsg();
        MallManager.inst()._msg_pay_mall_reward_c2s(humanObj, msg.getBundleId());
    }

    /**
     * 请求奖励包
     * <AUTHOR>
     * @Date 2024/3/12
     * @Param
     */
    @MsgReceiver(MsgPayMall.pay_mall_rewards_c2s.class)
    public void _msg_pay_mall_rewards_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgPayMall.pay_mall_rewards_c2s msg = param.getMsg();
        MallManager.inst()._msg_pay_mall_rewards_c2s(humanObj, msg.getBundleIdList());
    }

    @MsgReceiver(MsgPayMall.fake_recharge_c2s.class)
    public void _msg_fake_recharge_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgPayMall.fake_recharge_c2s msg = param.getMsg();
        MallManager.inst()._msg_fake_recharge_c2s(humanObj, msg.getBundleId(), msg.getPayId());
    }

    @MsgReceiver(MsgPayMall.role_pay_check_c2s.class)
    public void _msg_role_pay_check_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgPayMall.role_pay_check_c2s msg = param.getMsg();
        MallManager.inst()._msg_role_pay_check_c2s(humanObj, msg.getBundleId(), msg.getPrice(), msg.getTime(), msg.getIsMoney999());
    }


//    @MsgReceiver(MsgPayMall.pay_mall_custom_mall_info_c2s.class)
//    public void _msg_pay_mall_custom_mall_info_c2s(MsgParam param) {
//        HumanObject humanObj = param.getHumanObject();
//    }


//    @MsgReceiver(MsgPayMall.pay_mall_custom_mall_set_c2s.class)
//    public void _msg_pay_mall_custom_mall_set_c2s(MsgParam param) {
//        HumanObject humanObj = param.getHumanObject();
//    }


    @MsgReceiver(MsgPayMall.pay_gift_draw_c2s.class)
    public void _msg_pay_gift_draw_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgPayMall.pay_gift_draw_c2s msg = param.getMsg();
        MallManager.inst()._msg_pay_gift_draw_c2s(humanObj, msg.getPayId());
    }


    @MsgReceiver(MsgPayMall.pay_mall_check_push_c2s.class)
    public void _msg_pay_mall_check_push_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgPayMall.pay_mall_check_push_c2s msg = param.getMsg();
        MallManager.inst()._msg_pay_mall_check_push_c2s(humanObj, msg.getPayMallType(), msg.getConditionType(), msg.getArgs1(), msg.getArgs2());
    }

    @MsgReceiver(MsgPayMall.pay_mall_custom_mall_info_c2s.class)
    public void _msg_pay_mall_custom_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgPayMall.pay_mall_custom_mall_info_c2s msg = param.getMsg();
        MallManager.inst()._msg_pay_mall_custom_info_c2s(humanObj);
    }

    @MsgReceiver(MsgPayMall.pay_mall_custom_mall_set_c2s.class)
    public void _msg_pay_mall_custom_set_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgPayMall.pay_mall_custom_mall_set_c2s msg = param.getMsg();
        Define.p_custom_mall_info setInfo = msg.getSetInfo();
        MallManager.inst()._msg_pay_mall_custom_set_c2s(humanObj, setInfo);
    }
}
