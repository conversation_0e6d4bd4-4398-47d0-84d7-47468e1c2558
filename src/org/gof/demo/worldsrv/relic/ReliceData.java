package org.gof.demo.worldsrv.relic;

import com.alibaba.fastjson.JSONObject;
import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.interfaces.ISerilizable;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.config.ConfRelic_0;
import org.gof.demo.worldsrv.entity.Relic;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class ReliceData implements ISerilizable {
    //原始数据
    public Relic relic;
    //遗物<sn，等级>map
    public Map<Integer,Integer> relicMap = new HashMap<>();
    //遗迹方案<方案id，方案信息>map
    public Map<Integer, RelicTabVo> tabMap = new HashMap<>();

    public void initOffline(Relic relic){
        this.relic = relic;
        relicMap = Utils.jsonToMapIntInt(relic.getRelicMap());
        planMapFromJsonString(relic.getTabMap());
    }
    public String tabMapToJsonString() {
        JSONObject json = new JSONObject();
        for (Map.Entry<Integer, RelicTabVo> entry : tabMap.entrySet()) {
            json.put(entry.getKey().toString(), entry.getValue().toJsonString());
        }
        return json.toJSONString();
    }

    public void planMapFromJsonString(String jsonString) {
        JSONObject json = Utils.toJSONObject(jsonString);
        for (Map.Entry<String, Object> entry : json.entrySet()) {
            RelicTabVo vo = new RelicTabVo();
            vo.fromJsonString(entry.getValue().toString());
            tabMap.put(Integer.parseInt(entry.getKey()), vo);
        }
    }

    public int getMaxLocation(int tab){
        RelicTabVo tabVo = tabMap.get(tab);
        if (tabVo == null) {
            return 0;
        }
        int type = 0;
        int sn = 0;
        for (Integer relicId : tabVo.relics) {
            ConfRelic_0 confRelic_0 = ConfRelic_0.get(relicId,1);
            if(confRelic_0.type > type) {
                type = confRelic_0.type;
                sn = relicId;
            }
        }
        return sn;
    }

    @Override
    public void writeTo(OutputStream out) throws IOException {
        out.write(relicMap);
        out.write(tabMap);
    }

    @Override
    public void readFrom(InputStream in) throws IOException {
        relicMap = in.read();
        tabMap = in.read();
    }
}
