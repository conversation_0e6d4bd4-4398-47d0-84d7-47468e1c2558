package org.gof.demo.worldsrv.mail;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.gof.core.Port;
import org.gof.core.Record;
import org.gof.core.dbsrv.DB;
import org.gof.core.dbsrv.redis.EntityManager;
import org.gof.core.dbsrv.redis.Model;
import org.gof.core.dbsrv.redis.RedisTools;
import org.gof.core.gen.proxy.DistrClass;
import org.gof.core.gen.proxy.DistrMethod;
import org.gof.core.scheduler.ScheduleMethod;
import org.gof.core.support.Config;
import org.gof.core.support.Param;
import org.gof.core.support.S;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.common.GamePort;
import org.gof.demo.worldsrv.common.GameServiceBase;
import org.gof.demo.worldsrv.config.ConfGlobal;
import org.gof.demo.worldsrv.entity.FillMail;
import org.gof.demo.worldsrv.entity.Human;
import org.gof.demo.worldsrv.entity.ServerGlobal;
import org.gof.demo.worldsrv.human.HumanGlobalServiceProxy;
import org.gof.demo.worldsrv.redis.RedisKeys;
import org.gof.demo.worldsrv.support.C;
import org.gof.demo.worldsrv.support.D;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.Util;
import org.gof.demo.worldsrv.support.enumKey.ConfGlobalKey;
import org.gof.demo.worldsrv.support.enumKey.LanguageTypeKey;

import java.util.*;

/**
 * 全服补偿邮件服务
 * <AUTHOR>
 *
 */
@DistrClass(
	servId = D.SERV_FILL_MAIL,
	importClass = {List.class, FillMail.class}
)
public class FillMailService extends GameServiceBase {
	
	/**
	 * 当前所有的补偿邮件
	 */
	Map<Long, FillMail> mails = new HashMap<>();

	// 本服下的所有游戏服serverid
	public static List<Integer> serverIdListNow = new ArrayList<>();

	/** 单次查询的数据条目 **/
	public static final int pageNum = 1000;
	
	public FillMailService(GamePort port) {
		super(port);
	}
	
	@Override
	protected void init() {
		if(S.isRedis){
			initRedisLoadFillMail(C.GAME_SERVER_ID);
			return;
		}
		//初始化补偿邮件
		DB db = DB.newInstance(FillMail.tableName);
		db.countAll(false);
		Param paramCount = db.waitForResult();
		int count = paramCount.get();
		if (count > 0) {
			
			int page = count / pageNum;
			for (int i = 0; i <= page; i++) {

				db.findBy(false, i * pageNum, pageNum);
				Param params = db.waitForResult();
				List<Record> records = params.get();
				for (Record record : records) {
					FillMail fillMail = new FillMail(record);
					mails.put(fillMail.getId(), fillMail);
				}
			}
		}
	}


	private void checkServerId(){
		List<Integer> serverList = Util.getServerTagList(C.GAME_SERVER_ID);
		if(serverList.isEmpty()){
			Log.temp.error("===没有获取到serverId列表，请检查配置,{}", C.GAME_SERVER_ID);
			return;
		}
		for(Integer serverId : serverList){
			if(!serverIdListNow.contains(serverId)){
				serverIdListNow.add(serverId);
				initRedisLoadFillMail(serverId);
			}
		}

		Collection<Integer> diffAdd = (Collection<Integer>) CollectionUtils.subtract(serverIdListNow, serverList);
		if (diffAdd.isEmpty()) {
			return;
		}
		Log.temp.error("===本服id={},  已经下线serverIds={}", C.GAME_SERVER_ID, diffAdd);
	}

	private void initRedisLoadFillMail(int serverId) {
		List<FillMail> removeIdList = new ArrayList<>();
		EntityManager.getEntityListAsync(FillMail.class, serverId, (res)-> {
			List<FillMail> modelList = res.result();
			for (FillMail fillMail : modelList) {
				if (fillMail.getEndTime() <= Port.getTime()) {
					//跳过过期的邮件，且维护玩家的领取记录
					removeIdList.add(fillMail);
					continue;
				}
				mails.put(fillMail.getId(), fillMail);
			}
			for (FillMail fillMail : removeIdList) {
				fillMail.remove();
			}
		});
	}

	/**
	 * 全服发送补偿邮件
	 */
	@DistrMethod
	public void sendMail(String msg) {
		JSONObject jo = Utils.toJSONObject(msg);
		JSONObject joTitle = new JSONObject();
		joTitle.put(LanguageTypeKey.ja.getName(), jo.getString("title"));
		joTitle.put(LanguageTypeKey.en.getName(), jo.getString("title-en"));
		joTitle.put(LanguageTypeKey.zhTW.getName(), jo.getString("title-tw"));
		joTitle.put(LanguageTypeKey.ko.getName(), jo.getString("title-ko"));

		JSONObject joContent = new JSONObject();
		joContent.put(LanguageTypeKey.ja.getName(), jo.getString("detail"));
		joContent.put(LanguageTypeKey.en.getName(), jo.getString("detail-en"));
		joContent.put(LanguageTypeKey.zhTW.getName(), jo.getString("detail-tw"));
		joContent.put(LanguageTypeKey.ko.getName(), jo.getString("detail-ko"));

		String itemIdStr = jo.getString("sn");
		String numStr = jo.getString("num");
		long startTime = jo.getLongValue("startTime");
		long endTime = jo.getLongValue("endTime");
		String eventKey = jo.getString("eventKey");
		//持久化邮件
		FillMail mail = new FillMail();
		mail.setId(Port.applyId());
		mail.setTitle(joTitle.toJSONString());
		mail.setContent(joContent.toJSONString());
		mail.setSendTime(Port.getTime());
		mail.setStartTime(startTime);
		mail.setEventKey(eventKey);
		
		if(itemIdStr != null && !"".equals(itemIdStr)){			
		    mail.setEndTime(endTime);
		    
		    mail.setItemSn(itemIdStr);
		    mail.setItemNum(numStr);
		}else{
			mail.setEndTime(endTime);
		}
		mail.setServerId(C.GAME_SERVER_ID);
		mail.persist();
		
		mails.put(mail.getId(), mail);
		
		if(mail.getStartTime() > Port.getTime()){
			//未到邮件发送时间,返回
			return;
		}
		mail.setSysSendTime(Port.getTime());
		mail.update();
		
		HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance();
		prx.sendFillMail(mail, true);

	}
	
	/**
	 * 删除邮件
	 * @param eventKey
	 */
	@DistrMethod
	public void deleteMail(String eventKey){
		boolean success = false;
		String reason = "邮件不存在或者已过期";
		Iterator<FillMail> itrs = mails.values().iterator();
		while(itrs.hasNext()){
			FillMail mail = itrs.next();
			if(mail.getEventKey().equals(eventKey)){
				mail.remove();
				itrs.remove();
				success = true;
				reason = "";
			}
		}
		port.returns("success", success, "reason", reason);
	}
	
	/**
	 * 登录检查补偿邮件
	 * @param humanId
	 * @param
	 */
	@DistrMethod
	public void loginCheck(long humanId, int serverId, List<Long> fillMailList){
		List<FillMail> results = new ArrayList<>();
		List<Long> removeIdList = new ArrayList<>();
		removeIdList.addAll(fillMailList);
		removeIdList.removeAll(mails.keySet());

		List<Long> addIdList = new ArrayList<>();
		for (FillMail fillMail : mails.values()) {
			if (fillMail.getStartTime() > Port.getTime()) {
				//未到发送时间,不发送
				continue;
			}
			if (fillMail.getEndTime() <= Port.getTime()) {
				//跳过过期的邮件，且维护玩家的领取记录
				removeIdList.add(fillMail.getId());
				continue;
			}
			if (fillMailList.contains(fillMail.getId())) {
				//跳过已经领取的
				continue;
			}
			//领取记录
			results.add(fillMail);
			addIdList.add(fillMail.getId());
		}

		port.returns("mailList", results, "removeIdList", removeIdList, "addIdList", addIdList);
	}
	
	/**
	 * 定时删除过期的补偿邮件 每2分钟一次
	 */
	@ScheduleMethod("0 0/2 * * * ?")
	public void deleteTimeoutMail() {	
		Iterator<FillMail> itrs = mails.values().iterator();
		while(itrs.hasNext()){
			FillMail mail = itrs.next();
			if(mail.getEndTime() <= Port.getTime()){
				mail.remove();
				itrs.remove();
			}
		}
	}


	@ScheduleMethod("0 0/10 * * * ?")
	public void getFillMail() {
//		DB db = DB.newInstance(FillMail.tableName);
//		db.countAll(false);
//		Param paramCount = db.waitForResult();
//		int count = paramCount.get();
//		if (count > 0) {
//			int page = count / pageNum;
//			for (int i = 0; i <= page; i++) {
//				db.findBy(false, i * pageNum, pageNum);
//				Param params = db.waitForResult();
//				List<Record> records = params.get();
//				for (Record record : records) {
//					FillMail fillMail = new FillMail(record);
//					if(mails.containsKey(fillMail.getId())){
//						continue;
//					}
//					mails.put(fillMail.getId(), fillMail);
//				}
//			}
//		}
	}

	/**
	 * 每1分钟检查一次可发送的邮件(每发送100个人，等待1s)
	 */
	@ScheduleMethod("0 0/1 * * * ?")
	public void checkAndSend() {
		Iterator<FillMail> itrs = mails.values().iterator();
		while(itrs.hasNext()){
			FillMail fillMail = itrs.next();
			if(Port.getTime() > fillMail.getStartTime() && Port.getTime() < fillMail.getEndTime()){
				if(fillMail.getSysSendTime() == 0){
					fillMail.setSysSendTime(Port.getTime());
					fillMail.update();
					HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance();
					prx.sendFillMail(fillMail, true);
				}
			}
		}
	}

	@DistrMethod
	public void sendFillMail(String json){
		JSONObject jo = Utils.toJSONObject(json);
		int id = jo.getIntValue("id");
		FillMail fillMail = null;
		boolean isCreate = false;
		long timeNow = Port.getTime();
		if(mails.containsKey(id)){
			fillMail = mails.get(id);
		} else {
			fillMail = new FillMail();
			fillMail.setId(id);
			isCreate = true;
		}
		fillMail.setStartTime(jo.getLongValue("startTime"));
		fillMail.setEndTime(jo.getLongValue("endTime"));
		// 时间过期，可能发送数据出错或需要删除
		if(fillMail.getEndTime() <= timeNow){
			if(!isCreate){
				fillMail.remove();
				mails.remove(id);
			}
			return;
		}

		fillMail.setLevelMin(jo.getIntValue("levelMin"));
		fillMail.setLevelMax(jo.getIntValue("levelMax"));
		fillMail.setRegisteredBefore(jo.getLongValue("registeredBefore"));
		fillMail.setRegisteredAfter(jo.getLongValue("registeredAfter"));
		fillMail.setParam(jo.getString("extends"));
		fillMail.setSendTime(timeNow);

		JSONArray joMail = Utils.toJSONArray(jo.getString("mail"));
		JSONObject joTitle = new JSONObject();
		JSONObject joContent = new JSONObject();
		JSONObject joBannerLink = new JSONObject();
		for(int i = 0; i < joMail.size(); i++){
			JSONObject obj = joMail.getJSONObject(i);
			String language = obj.getString("language");
			String name = language;
			LanguageTypeKey key = LanguageTypeKey.getEnumByType(language);
			if(key == null){
				Log.temp.error("===后台邮件多语言出错language={},key={},json={}", language, key, json);
			} else {
				name = key.getName();
			}
			String header = obj.getString("header");
			String content = obj.getString("content");
			String bannerLink = obj.getString("bannerLink");
			joTitle.put(name, header);
			joContent.put(name, content);
			joBannerLink.put(name, bannerLink);
		}

		fillMail.setTitle(joTitle.toJSONString());
		fillMail.setContent(joContent.toJSONString());
		fillMail.setBannerLink(joBannerLink.toJSONString());

		JSONArray joAssets = Utils.toJSONArray(jo.getString("assets"));
		int size = joAssets.size();
		int[] itemSnArr = new int[size];
		int[] itemNumArr = new int[size];
		for(int i = 0; i < size; i++){
			JSONObject obj = joAssets.getJSONObject(i);
			itemSnArr[i] = obj.getIntValue("itemId");
			itemNumArr[i] = obj.getIntValue("num");
		}
		fillMail.setItemSn(Utils.arrayIntToStr(itemSnArr));
		fillMail.setItemNum(Utils.arrayIntToStr(itemNumArr));

		if(isCreate){
			fillMail.persist();
		}
		mails.put(fillMail.getId(), fillMail);

		if(fillMail.getStartTime() > timeNow){
			//未到邮件发送时间,返回
			return;
		}
		fillMail.setSysSendTime(Port.getTime());
		fillMail.update();

		HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance();
		prx.sendFillMail(fillMail, false);
	}

	@DistrMethod
	public void gm_sendFillMail(FillMail fillMail){
		mails.put(fillMail.getId(), fillMail);
		if(fillMail.getStartTime() > Port.getTime()){
			//未到邮件发送时间,返回
			return;
		}
		fillMail.setSysSendTime(Port.getTime());
		fillMail.update();
		HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance();
		prx.sendFillMail(fillMail, false);
	}

	@DistrMethod
	public void addFillMail(FillMail fillMail){
		mails.put(fillMail.getId(), fillMail);
		if(fillMail.getStartTime() > Port.getTime()){
			//未到邮件发送时间,返回
			return;
		}
		fillMail.setSysSendTime(Port.getTime());
		fillMail.update();
		HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance();
		prx.sendFillMail(fillMail, false);
	}

}