package org.gof.demo.worldsrv.human;

import com.alibaba.fastjson.JSONArray;
import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.interfaces.ISerilizable;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.msg.Define;

import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

public class PlanVo implements ISerilizable {
    int planId = 0;
    String name = "";
    long[] tab = new long[PLAN_NUM];

    public static final int TAB_EQUIP = 1;
    public static final int TAB_SKILL = 2;
    public static final int TAB_PET = 3;
    public static final int TAB_STATUE = 4;
    public static final int TAB_RELIC = 5;
    public static final int TAB_WING = 6;
    public static final int TAB_MOUNT = 7;
    public static final int TAB_ARTF = 8;
    public static final int TAB_WING_TALENT = 9;
    public static final int TAB_FLYPET = 10;
    public static final int TAB_ANGEL = 13;
    public static final int TAB_NUM = 13;
    public static final int PLAN_NUM = 13;


    public static final int MOUNT_INDEX = 1;
    public static final int ARTIFACT_INDEX = 2;
    public static final int WING_INDEX = 3;

    public PlanVo() {
    }

    public PlanVo(int planId) {
        Arrays.fill(tab, 0, TAB_RELIC, 1);
        Arrays.fill(tab, TAB_MOUNT-1, TAB_NUM, 0);
        tab[TAB_ANGEL-1] = 1;
        this.planId = planId;
    }

    public PlanVo(String jsonArrStr){
        Arrays.fill(tab, 1);
        JSONArray jsonArray = Utils.toJSONArray(jsonArrStr);
        planId = jsonArray.getInteger(0);
        name = jsonArray.getString(1);
        int i = 2;
        while (i<jsonArray.size()){
            tab[i-2] = jsonArray.getLongValue(i);
            ++i;
        }
    }

    public PlanVo(int planId, PlanVo planVo){
        this.planId = planId;
        System.arraycopy(planVo.tab, 0, tab, 0, planVo.tab.length);
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setTab(int key, long value){
        tab[key-1] = value;
    }
    public int getTab(int key) {
        return (int) tab[key-1];
    }

    /**
     * 为了少改动getTab的调用出，新增此方法
     */
    public long getTabValue(int key) {
        return tab[key - 1];
    }

    public String toJsonStr(){
        JSONArray jsonArray = new JSONArray();
        jsonArray.add(planId);
        jsonArray.add(name);
        for (int i = 0; i < tab.length; i++) {
            jsonArray.add(tab[i]);
        }
        return jsonArray.toJSONString();
    }

    public Define.p_plan_detail.Builder build(){
        Define.p_plan_detail.Builder builder = Define.p_plan_detail.newBuilder();
        builder.setPlanId(planId);
        builder.setName(name);
        for (int i = 0; i < tab.length; i++) {
            Define.p_key_value.Builder kv = Define.p_key_value.newBuilder();
            kv.setK(i+1);
            kv.setV(tab[i]);
            builder.addDetail(kv);
        }
        return builder;
    }

    public static String mapToString(Map<Integer,PlanVo> map){
        JSONArray jsonArray = new JSONArray();
        for (Map.Entry<Integer, PlanVo> entry : map.entrySet()) {
            jsonArray.add(entry.getValue().toJsonStr());
        }
        return jsonArray.toJSONString();
    }

    public static Map<Integer,PlanVo> stringToMap(String jsonArrStr){
        Map<Integer,PlanVo> map = new HashMap<>();
        JSONArray jsonArray = Utils.toJSONArray(jsonArrStr);
        for (int i = 0; i < jsonArray.size(); i++) {
            PlanVo planVo = new PlanVo(jsonArray.getString(i));
            map.put(planVo.planId,planVo);
        }
        return map;
    }


    @Override
    public void writeTo(OutputStream out) throws IOException {
        out.write(planId);
        out.write(name);
        out.write(tab);
    }

    @Override
    public void readFrom(InputStream in) throws IOException {
        planId = in.read();
        name = in.read();
        tab = in.read();
    }
}
