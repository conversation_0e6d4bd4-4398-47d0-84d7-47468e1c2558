package org.gof.demo.worldsrv.human;

import org.gof.core.CallPoint;
import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.interfaces.ISerilizable;

import java.io.IOException;

public class HumanGlobalInfo implements ISerilizable {
	public long id;						//ID
	public String modelSn;				//modelSn:1（human对象sn）
	public String humanModelSn;			//modelSn:role_04（human对象modelSn）
	public int fashionSn;            	//时装Sn
	public int borderSn;            	//边框Sn
	public int bubble;					//气泡
	public int titleSn;					//称号Sn
	public String url = "";				//地址
	public String account;				//登录账号
	public String channel;				//渠道
	public String name;					//昵称
	public String nodeId;				//Node名称
	public String portId;				//Port名称
	public long headSn;					//头像ID
	public int level;					//等级
	public String combat;               //战斗力
	public int sex;						//性别
	public int profession;				//职业
	public long timeLogin;				//玩家登陆时间
	public long campType;				//阵营类型
	public long guildId;				//公会ID
	public String guildName;			//公会名称
	public int guildPosition;			//公会职位

	public long teamId;					//队伍ID
	public String sign;					//签名
	public int headFrame;
	public CallPoint connPoint = new CallPoint();		//玩家连接ID
	public long syncTime;
	public int lineNum;

	public long battleTeamId;			//战场队伍id
	public int serverId;                //serverId
	public boolean isCheckRobot;

	@Override
	public void writeTo(OutputStream out) throws IOException {
		out.write(id);
		out.write(modelSn);
		out.write(humanModelSn);
		out.write(fashionSn);
		out.write(borderSn);
		out.write(url);
		out.write(account);
		out.write(channel);
		out.write(name);
		out.write(nodeId);
		out.write(portId);
		out.write(headSn);
		out.write(level);
		out.write(combat);
		out.write(sex);
		out.write(profession);
		out.write(timeLogin);
		out.write(connPoint);
		out.write(campType);
		out.write(guildId);
		out.write(guildPosition);
		out.write(guildName);
		out.write(teamId);
		out.write(sign);
		out.write(syncTime);
		out.write(lineNum);
		out.write(headFrame);
		out.write(battleTeamId);
		out.write(serverId);
		out.write(isCheckRobot);
		out.write(titleSn);
		out.write(bubble);
	}

	@Override
	public void readFrom(InputStream in) throws IOException {
		id = in.read();
		modelSn = in.read();
		humanModelSn = in.read();
		fashionSn = in.read();
		borderSn = in.read();
		url = in.read();
		account = in.read();
		channel = in.read();
		name = in.read();
		nodeId = in.read();
		portId = in.read();
		headSn = in.read();
		level = in.read();
		combat = in.read();
		sex = in.read();
		profession = in.read();
		timeLogin = in.read();
		connPoint = in.read();
		campType = in.read();
		guildId = in.read();
		guildPosition = in.read();
		guildName = in.read();
		teamId = in.read();
		sign = in.read();
		syncTime = in.read();
		lineNum = in.read();
		headFrame = in.read();
		battleTeamId = in.read();
		serverId = in.read();
		isCheckRobot=in.read();
		titleSn = in.read();
		bubble = in.read();
	}
}
