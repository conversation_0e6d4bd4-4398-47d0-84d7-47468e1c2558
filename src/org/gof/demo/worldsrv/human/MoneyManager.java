package org.gof.demo.worldsrv.human;

import com.alibaba.fastjson.JSONObject;
import com.pwrd.op.LogOpChannel;
import org.gof.core.Port;
import org.gof.core.support.*;
import com.pwrd.op.LogOp;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.ConfArcheryChapter;
import org.gof.demo.worldsrv.config.ConfExchangeRate;
import org.gof.demo.worldsrv.config.ConfGoods;
import org.gof.demo.worldsrv.entity.Currency;
import org.gof.demo.worldsrv.entity.Human;
import org.gof.demo.worldsrv.entity.ItemLog;
import org.gof.demo.worldsrv.guild.GuildServiceProxy;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.ReasonResult;
import org.gof.demo.worldsrv.support.TokenItemType;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;
import org.gof.demo.worldsrv.support.observer.Event;
import org.gof.demo.worldsrv.support.observer.EventKey;

import java.util.Map;

public class MoneyManager extends ManagerBase {

	/**
	 * 获取实例
	 * @return
	 */
	public static MoneyManager inst() {
		return inst(MoneyManager.class);
	}



	/**
	 * 所有货币添加统一接口 ProduceMoneyKey中所有类型
	 * @param humanObj
	 * @param type
	 * @param num
	 * @param log
	 */
	public void produceMoneyAdd(HumanObject humanObj, int type, long num , MoneyItemLogKey log, Object... obj) {

		//判断数量
		if(num <= 0) {
//			throw new SysException("添加数量不能小于0");
			Log.temp.error("添加数量不能小于0, humanId={}, key={}, num={}, log={}", humanObj.id, type, num, log);
			return;
		}

//		if(num > ConfGlobalUtils.getIntArray(ConfGlobalKey.资源数值安全检测值)[0]){
//			Log.temp.error("添加资源时数值过大 humanId = {}, type = {}, num = {}", humanObj.id, type.getType(), num);
////			throw new SysException("添加资源时数值过大");
//			return;
//		}


		//默认日志
		if(log == null) log = MoneyItemLogKey.未设置;

		if(Utils.isDebugMode()){
//			Log.temp.info("====获得货币， sn={}，num={}, log={}", type, num, log);
		}


		Human human = humanObj.getHuman();
		Currency currency = humanObj.currency;

		//监听属性变化
//		HumanInfoChange.listen(humanObj);
		long oldNum = 0;
		long newNum = 0;

		switch (type) {
			case TokenItemType.COIN: {//1
				//金币
				oldNum = currency.getCoin();
				newNum = oldNum + num;
				currency.setCoin(newNum);
//				currency.update(true);
			}
			break;
			case TokenItemType.GOLD: { //2
				//钻石
				oldNum = currency.getGold();
				newNum = oldNum + num;
				currency.setGold(newNum);
				//持久化
//				currency.update(true);
				//测试充值任务，暂时放在这
				//Event.fire(EventKey.PAY, "humanObj", humanObj, "gold", num);
			}
			break;

			case TokenItemType.EXP: {//3
//				Log.game.info("加钱接口 time={}",Port.getTime());
				oldNum = human.getExpCur();
				//经验
				HumanManager.inst().expAdd(humanObj, num, log);
				newNum = human.getExpCur();
			}
			break;
			case TokenItemType.GuildExp: {//8
				if(humanObj.getHuman2().getGuildId() > 0){
					GuildServiceProxy proxy = GuildServiceProxy.newInstance();
					proxy.guildDonate(humanObj.getHuman2().getGuildId(), humanObj.id, (int)num);
				}
			}
			break;
			case TokenItemType.Money999: {//999
				//星钻
				JSONObject moneyJson = Utils.toJSONObject(currency.getJson_999());
				String regional = humanObj.getRegional();
				oldNum = moneyJson.getLongValue(regional);
				long regionalNum = getRegionalMoney999(regional,num);
				if(regionalNum == 0){
					Log.game.error("加钱接口:regionalNum==0,humanId:{},regional:{},name:{}",humanObj.id,regional,humanObj.name);
					return;
				}
				newNum = oldNum + regionalNum;
				moneyJson.put(regional, newNum);
				currency.setJson_999(moneyJson.toJSONString());
				//持久化
				currency.update(true);
			}
			break;

			case TokenItemType.Money1001: {//1001
				//神灯
				oldNum = currency.getMoney_1001();
				newNum = oldNum + num;
				currency.setMoney_1001(newNum);
//				//持久化
//				currency.update(true);
			}
			break;
			default:
				throw new SysException("消耗玩家货币时发现无法解析的类型：{}", type);
		}
		if (type == TokenItemType.COIN
				|| type == TokenItemType.GOLD || type == TokenItemType.EXP
				|| type == TokenItemType.Money999 || type == TokenItemType.Money1001)
			costPersist(humanObj, type, oldNum, newNum, num, log);

		this.logCost(num, newNum, type,log.name(), humanObj, obj);

		//货币改变事件
		Event.fire(EventKey.PRODUCE_MONEY_CHANGE, "humanObj", humanObj, "useType",  ParamKey.useType_1, "type", type, "num", num);
	}

	public long getRegionalMoney999(String regional, long gameMoney){
		ConfExchangeRate conf = ConfExchangeRate.get((int)gameMoney);
		if(conf != null){
			Object value = conf.getFieldValue(regional);
			if(value == null){
				return 0;
			}else {
				return (long)(Utils.floatValue(value) * 100);
			}
		}else {
			ConfExchangeRate confUnit = ConfExchangeRate.get(20);
			Object value = confUnit.getFieldValue(regional);
			if(value == null){
				return 0;
			}else {
				return (int) Math.ceil(gameMoney/20.0 * Utils.floatValue(value) * 100);
			}
		}
	}

	private long getGameMoney999(String regional, long regionalMoney) {
		ConfExchangeRate confUnit = ConfExchangeRate.get(20);
		Object value = confUnit.getFieldValue(regional);
		if (value == null) {
			return 0;
		}

		long exchangeRate = (long)(Utils.floatValue(value) * 100);
		if (exchangeRate == 0) {
			return 0;
		}

		for (long gameMoney = 20; ; gameMoney += 20) {
			ConfExchangeRate conf = ConfExchangeRate.get((int)gameMoney);
			if (conf == null) {
				// 如果没有找到精确匹配，使用单位汇率进行计算
				return (long) Math.floor(regionalMoney * 20.0 / exchangeRate);
			}

			Object exactValue = conf.getFieldValue(regional);
			if (exactValue != null) {
				long exactRate = Utils.longValue(exactValue) * 100;
				if (exactRate > 0 && regionalMoney == exactRate) {
					return gameMoney;
				}
			}
		}
	}


	/**
	 * 所有货币统一删除接口 ProduceMoneyKey中所有类型 不包括exp exp现在不能减少
	 * @param humanObj
	 * @param type ProduceMoneyKey 中的type
	 * @param num
	 * @param log
	 */

	public void produceMoneyReduce(HumanObject humanObj, int type, long num , MoneyItemLogKey log) {
        if(log != MoneyItemLogKey.GM后台操作){
            ReasonResult rr = canProduceReduce(humanObj, type, num);
            if(!rr.success)
                throw new SysException(rr.reason);
        }

		//判断数量
		if(num < 0)
			throw new SysException("{}扣除的数量不能小于0",type);


		//默认日志
		if(log == null)
			log = MoneyItemLogKey.未设置;

		//玩家信息
		Currency currency = humanObj.currency;

		//监听玩家自身属性变化
//		HumanInfoChange.listen(humanObj);
		long oldNum = 0;
		long newNum = 0;

		switch (type) {
			case TokenItemType.COIN: {
				//金币
				oldNum = currency.getCoin();
				newNum = oldNum - num;
				currency.setCoin(newNum);
			}
			break;
			case TokenItemType.GOLD: {
				//非绑定钻石
				oldNum = currency.getGold();
				newNum = oldNum - num;
				currency.setGold(newNum);
				//持久化
//				currency.update(true);

				//添加消费日志
				if (!S.isBridge) {
//					ChargeManager.inst().consumerGold(humanObj,(int) num);
				}
			}
			break;
			case TokenItemType.EXP: {
				//经验
			}
			break;
			case TokenItemType.Money999: {
				//非绑定钻石
				String regional = humanObj.getRegional();
				num = getRegionalMoney999(regional, num);
				if(num == 0){
					Log.game.error("扣除闪钻失败:regionalNum==0,humanId:{},regional:{},name:{}",Port.getTime(),humanObj.id,regional,humanObj.name);
					return;
				}
				JSONObject jsonMoney = Utils.toJSONObject(currency.getJson_999());
				oldNum = jsonMoney.getLongValue(regional);
				newNum = oldNum - num;
				jsonMoney.put(regional, newNum);
				currency.setJson_999(jsonMoney.toJSONString());
				//持久化
				currency.update(true);
				//添加消费日志
			}
			break;
			case TokenItemType.Money1001: {
				//非绑定钻石
				oldNum = currency.getMoney_1001();
				newNum = oldNum - num;
				currency.setMoney_1001(newNum);
				//持久化
//				currency.update(true);
				//添加消费日志
			}
			break;

			default:
				throw new SysException("消耗玩家货币时发现无法解析的类型：{}", type);
		}
		if (type == TokenItemType.COIN
				|| type == TokenItemType.GOLD
				|| type == TokenItemType.Money999
				|| type == TokenItemType.Money1001
				|| type == 1235
				|| type == 1233){
			costPersist(humanObj, type, oldNum, newNum, -num, log);
		}

		if (!S.isBridge) {
			logCost(-num,newNum, type,log.name(),humanObj);
		}

		Event.fire(EventKey.PRODUCE_MONEY_CHANGE, "humanObj", humanObj, "useType", ParamKey.useType_2, "type", type, "num", num);
	}

	/**
	 * 是否可以消费
	 * @param humanObj
	 * @param type
	 * @param num
	 * @return
	 */
	public ReasonResult canProduceReduce(HumanObject humanObj, int type, long num) {
		//判断数量
		if(num < 0) {
			throw new SysException("添加数量不能小于0");
		}

//		if(num >= ConfGlobalUtils.getIntArray(ConfGlobalKey.资源数值安全检测值)[1]){
//			Log.temp.error("扣除资源时数值过大 humanId = {}, type = {}, num = {}", humanObj.id, type, num);
//			throw new SysException("扣除资源时数值过大");
//		}
		long nowNum = 0;
		if(type == TokenItemType.Money999){
			nowNum = getMoney999(humanObj);
			num = getRegionalMoney999(humanObj.getRegional(),num);
		}else {
			nowNum = this.getProduceReduce(humanObj, type);
		}
		if(nowNum < num) {
			return new ReasonResult(false);
		}

		return new ReasonResult(true);
	}

	/**
	 * 获取某种类型货币的数量
	 * @param humanObj
	 * @param type
	 * @return
	 */
	public long getProduceReduce(HumanObject humanObj, int type) {
		Human human = humanObj.getHuman();
		Currency currency = humanObj.currency;

		switch (type) {
			case TokenItemType.COIN:  return currency.getCoin();				//1金币
			case TokenItemType.GOLD:  return currency.getGold(); 				//2钻石
			case TokenItemType.EXP:  return human.getExpCur(); 					//3经验
			case TokenItemType.Money999:  return getMoney999(humanObj);			//4绑定魂钻
			case TokenItemType.Money1001: return currency.getMoney_1001();			//5金魂币
			default:
				new SysException("检查是否能消耗玩家货币时发现无法解析的类型：type={}", type);
				return -1;
		}
	}

	private long getMoney999(HumanObject humanObj){
		JSONObject money999 = Utils.toJSONObject(humanObj.currency.getJson_999());
		return money999.getLongValue(humanObj.getRegional());
	}


	private void logCost(long cost, long nowNum, int sn, String reason, HumanObject humanObj, Object... obj) {
		Human human = humanObj.getHuman();
		long nowTimestamp = Port.getTime();
//		添加消费日志
		ConfGoods itemData = ConfGoods.get(sn);
		String name = itemData==null?"":itemData.locname;
		// 后台要求默认值为null,目前就礼包有用，当为礼包时obj1为礼包sn(物品表sn)
		int obj1 = 0;
		if(obj != null && obj.length >= 1){
			obj1 =(int)obj[0];
		}

		if(cost<0){
			LogOp.log(LogOpChannel.COST,
					human.getId(),//0
					nowTimestamp,//1
					Utils.formatTime(nowTimestamp, "yyyy-MM-dd"),//2
					sn,//3
					name,//4
					-cost,//5
					reason,//6
					human.getAccount(),//7
					human.getName(),//8
					human.getLevel(),//9
					human.getServerId(),//10
					nowNum,//11
					obj1
			);
		}
		else{
			if(sn == TokenItemType.COIN && Math.abs(cost) < 50000){
				return;
			}
			LogOp.log(LogOpChannel.GAIN,
					human.getId(),
					nowTimestamp,
					Utils.formatTime(nowTimestamp, "yyyy-MM-dd"),
					sn,
					name,
					cost,
					reason,
					human.getAccount(),
					human.getName(),
					human.getLevel(),
					human.getServerId(),
					nowNum,
					obj1, 0, 0);
		}



	}



	/**
	 * 消费日志入库
	 * @param humanObj
	 * @param type		    消费类型
	 * @param oldMoney	    消费前
	 * @param newMoney    消费后
	 * @param count       消费数量
	 * @param key1         消费增加点
	 */
	private void costPersist(HumanObject humanObj, int type, long oldMoney, long newMoney, long count, MoneyItemLogKey key1) {
		// 银币小于8万不记录
		if (type == TokenItemType.COIN && Math.abs(count) < 50000)
			return;
		if(key1 == MoneyItemLogKey.打开神灯消耗||key1 == MoneyItemLogKey.通关副本类型1){
			return;
		}

//		ItemLog log = new ItemLog();
//		log.setHumanId(humanObj.id);
//		log.setSn(type);
//		log.setOldNum(oldMoney);
//		log.setNum(count);
//		log.setNewNum(newMoney);
//		if (key1 != null) {
//			log.setType(key1.getType());
//			log.setOperate(key1.name());
//		}
//		long nowTime = Port.getTime();
//		log.setTime(nowTime);
//		log.setDataStr(Utils.formatTime(nowTime, "yyyy-MM-dd HH:mm:ss"));
//		log.persist();
	}

	/**
	 * 是否能消耗货币或者消耗绑钻不足则消耗魂钻
	 * @param humanObject
	 * @param buyType
	 * @param costPrice
	 * @return
	 */
	public ReasonResult canConsumeMoneyOrCatGold(HumanObject humanObject,int buyType,int costPrice){
		ReasonResult rs = MoneyManager.inst().canProduceReduce(humanObject, buyType, costPrice);
		if(!rs.success && buyType == TokenItemType.Money999){
			long lackGold = costPrice - humanObject.currency.getMoney_999();
			rs= MoneyManager.inst().canProduceReduce(humanObject, TokenItemType.GOLD, lackGold);
			return rs;
		}
		return rs;
	}

	/**
	 * 消耗货币或者消耗绑钻不足则消耗魂钻
	 * @param humanObject
	 * @param buyType
	 * @param costPrice
	 * @param key
	 * @return
	 */
	public boolean consumeMoneyOrCatGold(HumanObject humanObject,int buyType,int costPrice,MoneyItemLogKey key){
		boolean isConsumeOk = false;
//		isConsumeOk = RewardHelper.checkAndConsume(humanObject, buyType, costPrice, key);
//		//如果绑定魂钻不足，则计算出剩余数，使用非绑定魂钻扣除
//		if(!isConsumeOk && buyType == TokenItemType.BIND_GOLD){
//			long lackGold = costPrice - humanObject.currency.getMoney_999();
//			ReasonResult rr = MoneyManager.inst().canProduceReduce(humanObject, TokenItemType.GOLD, lackGold);
//			//绑定魂钻与充值魂钻不足
//			if(!rr.success){
//				return isConsumeOk;
//			}
//			MoneyManager.inst().produceMoneyReduce(humanObject, TokenItemType.BIND_GOLD,humanObject.currency.getMoney_999() , key);
//			MoneyManager.inst().produceMoneyReduce(humanObject, TokenItemType.GOLD,lackGold , key);
//			isConsumeOk = true;
//			return isConsumeOk;
//		}
		return isConsumeOk;
	}

	public void produceMoneyReduce(HumanObject humanObj, int type, long num ,MoneyItemLogKey log,String itemName) {
		ReasonResult rr = canProduceReduce(humanObj, type, num);
		if(!rr.success)
			throw new SysException(rr.reason);

		//判断数量
		if(num < 0)
			throw new SysException("{}扣除的数量不能小于0",type);


		//默认日志
		if(log == null)
			log = MoneyItemLogKey.未设置;

		//玩家信息
		Human human = humanObj.getHuman();
		Currency currency = humanObj.currency;

		//监听玩家自身属性变化
//		HumanInfoChange.listen(humanObj);
		long oldNum = 0;
		long newNum = 0;

		switch (type) {
			case TokenItemType.COIN: {
				//金币
				oldNum = currency.getCoin();
				newNum = oldNum - num;
				currency.setCoin(newNum);
			}
			break;
			case TokenItemType.GOLD: {
				//非绑定钻石
				oldNum = currency.getGold();
				newNum = oldNum - num;
				currency.setGold(newNum);
				//持久化
//				currency.update(true);
				//添加消费日志
			}
			break;
			case TokenItemType.Money1001: {
				//非绑定钻石
				oldNum = currency.getMoney_1001();
				newNum = oldNum - num;
				currency.setMoney_1001(newNum);
				//持久化
//				currency.update(true);
				//添加消费日志
			}
			break;
			case TokenItemType.Money999: {

				//非绑定钻石
				oldNum = currency.getMoney_999();
				newNum = oldNum - num;
				currency.setMoney_999(newNum);
				//持久化
				currency.update(true);
				//添加消费日志
			}
			break;
			case TokenItemType.EXP: {
				//经验
			}
			break;
			default:
				throw new SysException("消耗玩家货币时发现无法解析的类型：{}", type);
		}
		if (type == TokenItemType.COIN
				|| type == TokenItemType.GOLD
				|| type == TokenItemType.Money999
				|| type == TokenItemType.Money1001)
			costPersist(humanObj, type, oldNum, newNum, -num, log);


		//货币改变事件
		Event.fire(EventKey.PRODUCE_MONEY_CHANGE, "humanObj", humanObj, "useType", ParamKey.useType_2, "type", type, "num", num);
	}



}
