package org.gof.demo.worldsrv.msg;

import org.gof.core.Port;
import org.gof.core.support.ManagerBase;
import org.gof.demo.worldsrv.activity.ActivityManager;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.support.Log;

/**
 * 无职手办活动消息处理器
 */
public class MsgHandlerAct2 extends ManagerBase {
    
//    /**
//     * 处理手办活动信息请求
//     */
//    public void act_wuzhi_figure_info_c2s(Port port, MsgAct2.act_wuzhi_figure_info_c2s msg) {
//        HumanObject humanObj = port.getHuman();
//        if (humanObj == null) {
//            Log.game.error("玩家对象为空");
//            return;
//        }
//
//        try {
//            ActivityManager.inst().on_act_wuzhi_figure_info_c2s(humanObj, msg.getActType());
//        } catch (Exception e) {
//            Log.activity.error("处理手办活动信息请求异常", e);
//        }
//    }
//
//    /**
//     * 处理手办制作请求
//     */
//    public void act_wuzhi_figure_make_c2s(Port port, MsgAct2.act_wuzhi_figure_make_c2s msg) {
//        HumanObject humanObj = port.getHuman();
//        if (humanObj == null) {
//            Log.game.error("玩家对象为空");
//            return;
//        }
//
//        try {
//            ActivityManager.inst().on_act_wuzhi_figure_make_c2s(humanObj, msg.getActType(), msg.getMakeType(), msg.getCount());
//        } catch (Exception e) {
//            Log.activity.error("处理手办制作请求异常", e);
//        }
//    }
}
