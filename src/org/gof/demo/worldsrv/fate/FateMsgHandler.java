package org.gof.demo.worldsrv.fate;

import org.gof.core.support.observer.MsgReceiver;
import org.gof.demo.seam.msg.MsgParam;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.msg.MsgFate;

public class FateMsgHandler {
    /**
     * 武魂信息
     */
    @MsgReceiver(MsgFate.fate_info_c2s.class)
    public void fate_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        FateManager.inst().handleFateInfoC2S(humanObj);
    }

    /**
     * 武魂镶嵌
     * @param type 类型
     * @param pos_id 位置ID
     * @param id ID
     */
    @MsgReceiver(MsgFate.fate_inlay_c2s.class)
    public void fate_inlay_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgFate.fate_inlay_c2s msg = param.getMsg();
        FateManager.inst().handleFateInlayC2S(humanObj, msg.getType(), msg.getPosId(), msg.getId());
    }

    /**
     * 武魂升级
     * @param id ID
     */
    @MsgReceiver(MsgFate.fate_level_up_c2s.class)
    public void fate_level_up_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgFate.fate_level_up_c2s msg = param.getMsg();
        FateManager.inst().handleFateLevelUpC2S(humanObj, msg.getId());
    }

    /**
     * 武魂祈福信息
     */
    @MsgReceiver(MsgFate.fate_pray_info_c2s.class)
    public void fate_pray_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        FateManager.inst().handleFatePrayInfoC2S(humanObj);
    }

    /**
     * 武魂祈福
     * @param type 类型
     */
    @MsgReceiver(MsgFate.fate_pray_c2s.class)
    public void fate_pray_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgFate.fate_pray_c2s msg = param.getMsg();
        FateManager.inst().handleFatePrayC2S(humanObj, msg.getType());
    }

    /**
     * 武魂分解
     * @param id_list ID列表
     */
    @MsgReceiver(MsgFate.fate_dismantle_c2s.class)
    public void fate_dismantle_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgFate.fate_dismantle_c2s msg = param.getMsg();
        FateManager.inst().handleFateDismantleC2S(humanObj, msg.getIdListList());
    }

    /**
     * 武魂融合
     * @param target_id 目标ID
     * @param id_list ID列表
     */
    @MsgReceiver(MsgFate.fate_fusion_c2s.class)
    public void fate_fusion_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgFate.fate_fusion_c2s msg = param.getMsg();
        FateManager.inst().handleFateFusionC2S(humanObj, msg.getTargetId(), msg.getIdListList());
    }

    /**
     * 武魂融合信息
     */
    @MsgReceiver(MsgFate.fate_fusion_info_c2s.class)
    public void fate_fusion_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        FateManager.inst().handleFateFusionInfoC2S(humanObj);
    }


    /**
     * 武魂融合选择
     * @param choose 选择
     */
    @MsgReceiver(MsgFate.fate_fusion_choose_c2s.class)
    public void fate_fusion_choose_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgFate.fate_fusion_choose_c2s msg = param.getMsg();
        FateManager.inst().handleFateFusionChooseC2S(humanObj, msg.getChoose());
    }

    /**
     * 武魂显示选择
     * @param choose 选择
     * @param show_pos_id 显示位置ID
     */
    @MsgReceiver(MsgFate.fate_show_choose_c2s.class)
    public void fate_show_choose_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgFate.fate_show_choose_c2s msg = param.getMsg();
        FateManager.inst().handleFateShowChooseC2S(humanObj, msg.getChoose(), msg.getShowPosId());
    }

    /**
     * 武魂红点已读
     * @param fate_id 武魂ID
     */
    @MsgReceiver(MsgFate.fate_red_read_c2s.class)
    public void fate_red_read_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgFate.fate_red_read_c2s msg = param.getMsg();
        FateManager.inst().handleFateRedReadC2S(humanObj, msg.getFateId());
    }

    /**
     * 武魂重置
     * @param fate_id 武魂ID
     */
    @MsgReceiver(MsgFate.fate_reset_c2s.class)
    public void fate_reset_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgFate.fate_reset_c2s msg = param.getMsg();
        FateManager.inst().handleFateResetC2S(humanObj, msg.getFateId());
    }

    @MsgReceiver(MsgFate.fate_pray_set_auto_c2s.class)
    public void fate_pray_set_auto_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgFate.fate_pray_set_auto_c2s msg = param.getMsg();
        FateManager.inst().handleFatePraySetAutoC2S(humanObj, msg.getIsAutoDismantle(), msg.getDismantleType());
    }


}
