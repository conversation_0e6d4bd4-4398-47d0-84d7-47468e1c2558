package org.gof.demo.worldsrv.monster;

import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.interfaces.ISerilizable;
import org.gof.demo.battlesrv.support.Vector2D;

import java.io.IOException;

public class DeadMonsterInfo implements ISerilizable {
    public long id;
    public String name;
    public String sn;
    public Vector2D posNow;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public Vector2D getPosNow() {
        return posNow;
    }

    public void setPosNow(Vector2D posNow) {
        this.posNow = posNow;
    }

    @Override
    public void writeTo(OutputStream out) throws IOException {
        out.write(id);
        out.write(name);
        out.write(sn);
        out.write(posNow);
    }

    @Override
    public void readFrom(InputStream in) throws IOException {
        id = in.read();
        name = in.read();
        sn = in.read();
        posNow = in.read();
    }
}
