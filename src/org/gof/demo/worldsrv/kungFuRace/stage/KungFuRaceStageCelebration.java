package org.gof.demo.worldsrv.kungFuRace.stage;

import io.vertx.core.AsyncResult;
import io.vertx.core.Handler;
import org.gof.core.Port;
import org.gof.core.RemoteNode;
import org.gof.core.dbsrv.redis.AsyncActionResult;
import org.gof.core.dbsrv.redis.CrossRedis;
import org.gof.core.dbsrv.redis.EntityManager;
import org.gof.core.dbsrv.redis.RedisTools;
import org.gof.core.support.Utils;
import org.gof.demo.distr.DistrKit;
import org.gof.demo.worldsrv.bridgeEntity.KungFuRaceChampionInfo;
import org.gof.demo.worldsrv.bridgeEntity.KungFuRaceTeam;
import org.gof.demo.worldsrv.config.ConfBattleCompetitionReward;
import org.gof.demo.worldsrv.config.ConfBattleCompetitionStage;
import org.gof.demo.worldsrv.human.HumanGlobalServiceProxy;
import org.gof.demo.worldsrv.kungFuRace.*;
import org.gof.demo.worldsrv.kungFuRace.data.KungFuRaceCompetitionData;
import org.gof.demo.worldsrv.kungFuRace.data.KungFuRaceTeamData;
import org.gof.demo.worldsrv.redis.RedisKeys;
import org.gof.demo.worldsrv.support.Log;

import java.util.*;

public class KungFuRaceStageCelebration extends KungFuRaceStage {
	// 结算奖励VOMap
	private final Map<Integer, Map<Long, KungFuRaceRewardVO>> m_serverRewardVOMap = new HashMap<>();

	public KungFuRaceStageCelebration(KungFuRaceZone zone, ConfBattleCompetitionStage conf) {
		super(zone, conf);
	}

	@Override
	public void onStart(Handler<AsyncResult<Void>> onComplete) {
		// 计算排名信息
		List<KungFuRaceCompetitionData> compList = m_zone.getAllCompetition();
		for(KungFuRaceCompetitionData compData : compList){
			m_zone.addFinalRank(compData);
		}
		Map<Integer, Set<Long>> finalRankSetMap = m_zone.getFinalRankSetMap();
		for(Map.Entry<Integer, Set<Long>> entry : finalRankSetMap.entrySet()){
			int battleFlag = entry.getKey();
			Set<Long> rankSet = entry.getValue();
			if(rankSet.size() != battleFlag){
				Log.kungFuRace.warn("最终排名信息数量错误 rankSet.size()={} != battleFlag={}", rankSet.size(), battleFlag);
				AsyncActionResult.fail(port, onComplete, new Exception("最终排名信息数量错误"));
				return;
			}
		}
		Set<Long> championSet = finalRankSetMap.getOrDefault(1, new HashSet<>());
		long championTeamId = championSet.isEmpty() ? 0 : championSet.iterator().next();
		if(championTeamId == 0){
			Log.kungFuRace.warn("冠军队伍id不存在");
			AsyncActionResult.fail(port, onComplete, new Exception("冠军队伍id不存在"));
			return;
		}
		KungFuRaceTeamData championTeamData = m_zone.getTeam(championTeamId);
		if(championTeamData == null){
			Log.kungFuRace.warn("冠军队伍信息不存在");
			AsyncActionResult.fail(port, onComplete, new Exception("冠军队伍信息不存在"));
			return;
		}
		int season = KungFuRaceUtils.getCurrentSeason();
		KungFuRaceChampionInfo championInfo = m_zone.getChampion(season);
		if(championInfo != null){
			if(!championInfo.getTeamName().equals(championTeamData.getTeamName())){
				Log.kungFuRace.warn("冠军队伍名字检查失败");
				AsyncActionResult.fail(port, onComplete, new Exception("冠军队伍名字检查失败"));
				return;
			}
		}
		else{
			// 新一届冠军，加入名人堂
			KungFuRaceTeam team = championTeamData.getTeam();
			championInfo = new KungFuRaceChampionInfo();
			championInfo.setId(Port.applyId());
			championInfo.setZoneId(getZoneId());
			championInfo.setSeason(season);
			championInfo.setTeamName(team.getTeamName());
			championInfo.setHumanId1(team.getHumanId1());
			championInfo.setHumanId2(team.getHumanId2());
			championInfo.setRoleFigure1(team.getRoleFigure1());
			championInfo.setRoleFigure2(team.getRoleFigure2());
			championInfo.persist();
			m_zone.addChampionInfo(championInfo);
		}
		Log.kungFuRace.info("{} season={} 冠军队伍为 {} 信仰值={}", getZoneName(), season, championTeamId, championInfo.getWorship());
		// 回收竞猜币
		recycleBetCoin();
		AsyncActionResult.success(port, onComplete, null);
	}

	/**
	 * 回收竞猜币
	 */
	private void recycleBetCoin(){
		Set<String> nodes = m_zone.getBroadcastNodes();
		for(String nodeId : nodes){
			HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance(nodeId);
			proxy.kungFuRaceRecycleBetCoin();
		}
		Log.kungFuRace.info("==={} 回收竞猜币 广播消息给{}个游戏服", getZoneName(), nodes.size());
	}

	/**
	 * 阶段结束
	 */
	@Override
	public void onEnd(Handler<AsyncResult<Void>> onComplete) {
        String settledRewardKey = KungFuRaceUtils.getSettledRewardKey(getZoneId());
        RedisTools.get(EntityManager.redisClient, settledRewardKey, res->{
            if (res.failed()) {
                Log.kungFuRace.warn("==={} 结算赛季奖励失败 settledRewardKey不存在", getZoneName());
                AsyncActionResult.fail(port, onComplete, new Exception("settledRewardKey不存在"));
                return;
            }
            boolean isSettled = Objects.equals(res.result(), "1");
            if(isSettled){
                Log.kungFuRace.info("{} 赛季奖励已结算", getZoneName());
            }
            else{
                Log.kungFuRace.info("==={} 结算赛季奖励", getZoneName());
                settleSeasonReward();
            }
            AsyncActionResult.success(port, onComplete, null);
        });
	}

	/**
	 * 结算赛季奖励
	 */
	public void settleSeasonReward(){
		Set<Long> alreadyCreateRewardSet = new HashSet<>();
		// 创建128强以上的奖励，奖励从高往低发，发过名次高的不再发名次低的
		Map<Integer, Set<Long>> finalRankSetMap = m_zone.getFinalRankSetMap();
		for(Map.Entry<Integer, Set<Long>> entry : finalRankSetMap.entrySet()){
			int battleFlag = entry.getKey();
			Set<Long> rankSet = entry.getValue();
			int confSn = KungFuRaceUtils.getCompetitionRank(battleFlag);
			ConfBattleCompetitionReward conf = ConfBattleCompetitionReward.get(confSn);
			if(conf == null){
				Log.kungFuRace.error("===ConfBattleCompetitionReward 配置不存在，confSn={}", confSn);
				continue;
			}
			for(long teamId : rankSet){
				KungFuRaceTeamData teamData = m_zone.getTeam(teamId);
				if(teamData == null){
					Log.kungFuRace.error("===settleSeasonReward team={}不存在", teamId);
					continue;
				}
				if(alreadyCreateRewardSet.contains(teamId)){
					continue;
				}
				alreadyCreateRewardSet.add(teamId);
				createRewardVO(teamData.getTeam(), confSn, battleFlag);
			}
		}
		// 创建保底奖励
		int confSn = KungFuRaceUtils.getCompetitionRank(0);
		ConfBattleCompetitionReward conf = ConfBattleCompetitionReward.get(confSn);
		if(conf == null){
			Log.kungFuRace.error("===ConfBattleCompetitionReward 配置不存在，confSn={}", confSn);
			return;
		}
		List<KungFuRaceTeamData> allTeamList = m_zone.getAllTeam();
		for(KungFuRaceTeamData teamData : allTeamList){
			long teamId = teamData.getTeamId();
			if(alreadyCreateRewardSet.contains(teamId)){
				continue;
			}
			alreadyCreateRewardSet.add(teamId);
			// 999表示未上榜
			createRewardVO(teamData.getTeam(), confSn, 999);
		}
		if(alreadyCreateRewardSet.size() != allTeamList.size()){
			Log.kungFuRace.warn("{} 创建结算奖励的队伍数量错误 alreadyCreateRewardSet.size()={} != allTeamList.size()={}",
					getZoneName(), alreadyCreateRewardSet.size(), allTeamList.size());
			return;
		}
		Log.kungFuRace.info("{} 创建结算奖励的队伍数量={}", getZoneName(), alreadyCreateRewardSet.size());
		// 发送奖励到游戏服
		sendRewardToGameServer();
	}

	/**
	 * 创建奖励VO
	 * @param team
	 * @param confSn
	 * @param rank
	 */
	private void createRewardVO(KungFuRaceTeam team, int confSn, int rank){
		String redisKey = KungFuRaceUtils.getFinalRankKey(getZoneId());
		long humanId1 = team.getHumanId1();
		int serverId1 = Utils.getServerIdByHumanId(humanId1);
		boolean isAbsent1 = m_serverRewardVOMap.computeIfAbsent(serverId1, k -> new HashMap<>())
				.putIfAbsent(humanId1, new KungFuRaceRewardVO(humanId1, confSn)) == null;
		if(isAbsent1){
			// 在redis记录最终名次，不使用，方便测试查看
			RedisTools.addRank(EntityManager.redisClient, redisKey, humanId1, rank);
			RedisTools.expire(EntityManager.redisClient, redisKey, KungFuRaceConst.KUNG_FU_RACE_REDIS_KEY_EXPIRE_TIME);
			Log.kungFuRace.info("createRewardVO humanId1={}, confSn={}", humanId1, confSn);
		}
		long humanId2 = team.getHumanId2();
		if(humanId2 > 0){
			int serverId2 = Utils.getServerIdByHumanId(humanId2);
			boolean isAbsent2 = m_serverRewardVOMap.computeIfAbsent(serverId2, k -> new HashMap<>())
					.putIfAbsent(humanId2, new KungFuRaceRewardVO(humanId2, confSn)) == null;
			if(isAbsent2){
				// 在redis记录最终名次，不使用，方便测试查看
				RedisTools.addRank(EntityManager.redisClient, redisKey, humanId2, rank);
				RedisTools.expire(EntityManager.redisClient, redisKey, KungFuRaceConst.KUNG_FU_RACE_REDIS_KEY_EXPIRE_TIME);
				Log.kungFuRace.info("createRewardVO humanId2={}, confSn={}", humanId2, confSn);
			}
		}
	}

	/**
	 * 发送奖励到游戏服
	 */
	private void sendRewardToGameServer(){
		int totalRewardNum = 0;
		for(Map.Entry<Integer, Map<Long, KungFuRaceRewardVO>> entry : m_serverRewardVOMap.entrySet()){
			int serverId = entry.getKey();
			Map<Long, KungFuRaceRewardVO> rewardVOMap = entry.getValue();
			String worldNodeId = DistrKit.getWorldNodeID(serverId);
			RemoteNode rn = Port.getCurrent().getNode().getRemoteNode(worldNodeId);
			if(rn == null) {
				Log.kungFuRace.warn("===sendRewardToGameServer fail. worldNodeId={}未连接", worldNodeId);
				// 武道会结算发送失败备份
				saveErrorBackup(false, serverId, rewardVOMap);
				continue;
			}
			try{
				HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance(worldNodeId);
				Log.kungFuRace.info("武道会发送奖励到游戏服={} 人数={}", serverId, rewardVOMap.size());
				totalRewardNum += rewardVOMap.size();
				proxy.sendKungFuRaceReward(rewardVOMap);
				proxy.listenResult((timeout, results, context) -> {
					boolean bResult = Utils.getParamValue(results, "result", false);
					if (bResult) {
						// 返回正常
						return;
					}
					// 武道会结算发送失败备份
					saveErrorBackup(timeout, serverId, rewardVOMap);
				});
			}catch (Exception e){
				Log.kungFuRace.error("===sendRewardToGameServer 失败，serverId={}", serverId, e);
				// 武道会结算发送失败备份
				saveErrorBackup(false, serverId, rewardVOMap);
			}
		}
        // 设置已发结算奖励
        String settledRewardKey = KungFuRaceUtils.getSettledRewardKey(getZoneId());
        RedisTools.setAndExpire(EntityManager.redisClient, settledRewardKey, "1", KungFuRaceConst.KUNG_FU_RACE_REDIS_KEY_EXPIRE_TIME);
		Log.kungFuRace.info("武道会发送奖励到所有游戏服的总人数={}", totalRewardNum);
	}

	/**
	 * 武道会结算发送失败备份
	 * @param timeout
	 * @param serverId
	 * @param rewardVOMap
	 */
	private void saveErrorBackup(boolean timeout, int serverId, Map<Long, KungFuRaceRewardVO> rewardVOMap){
		Log.kungFuRace.warn("===timeout ={}, serverId={}, 武道会结算发送失败备份={}", timeout, serverId, rewardVOMap.size());
		List<String> keyList = new ArrayList<>();
		keyList.add(RedisKeys.kung_fu_race_error + serverId);
		for(KungFuRaceRewardVO vo : rewardVOMap.values()){
			keyList.add(vo.toString());
		}
		CrossRedis.sadd(keyList);
	}

}

