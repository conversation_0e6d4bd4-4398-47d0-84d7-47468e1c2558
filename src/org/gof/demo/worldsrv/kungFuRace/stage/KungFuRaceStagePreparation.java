package org.gof.demo.worldsrv.kungFuRace.stage;

import io.vertx.core.AsyncResult;
import io.vertx.core.Handler;
import org.gof.core.dbsrv.redis.AsyncActionResult;
import org.gof.demo.worldsrv.config.ConfBattleCompetitionStage;
import org.gof.demo.worldsrv.kungFuRace.KungFuRaceConst;
import org.gof.demo.worldsrv.kungFuRace.KungFuRaceUtils;
import org.gof.demo.worldsrv.kungFuRace.KungFuRaceZone;
import org.gof.demo.worldsrv.support.Log;

import java.util.*;

public class KungFuRaceStagePreparation extends KungFuRaceStage {
	public KungFuRaceStagePreparation(KungFuRaceZone zone, ConfBattleCompetitionStage conf) {
		super(zone, conf);
	}

	@Override
	public void onStart(Handler<AsyncResult<Void>> onComplete) {
		// 获取128强的信息
		KungFuRaceUtils.getTop128Info(getZoneId(), res->{
			if (res.failed()) {
				AsyncActionResult.fail(port, onComplete, res.cause());
				return;
			}
            LinkedHashSet<Long> top128TeamSet = res.result();
			if(top128TeamSet.size() != KungFuRaceConst.KUNG_FU_RACE_BATTLE_FLAG_128){
				Log.kungFuRace.error("top128 team set has incorrect size = {}", top128TeamSet.size());
				AsyncActionResult.fail(port, onComplete, new Exception("128强队伍不足"));
				return;
			}
			AsyncActionResult.success(port, onComplete, null);
		});
	}

}

