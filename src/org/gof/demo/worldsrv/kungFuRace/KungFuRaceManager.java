package org.gof.demo.worldsrv.kungFuRace;

import com.alibaba.fastjson.JSONObject;
import org.gof.core.support.*;
import org.gof.core.support.observer.Listener;
import org.gof.demo.worldsrv.character.HumanDailyResetInfo;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.ConfGlobal;
import org.gof.demo.worldsrv.entity.PocketLine;
import org.gof.demo.worldsrv.human.FuncOpenType;
import org.gof.demo.worldsrv.human.HumanManager;
import org.gof.demo.worldsrv.inform.ErrorTip;
import org.gof.demo.worldsrv.inform.Inform;
import org.gof.demo.worldsrv.instance.InstanceConstants;
import org.gof.demo.worldsrv.instance.InstanceManager;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.pocketLine.PocketLineEventSubKey;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.ReasonResult;
import org.gof.demo.worldsrv.support.enumKey.ConfGlobalKey;
import org.gof.demo.worldsrv.support.enumKey.DailyResetTypeKey;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;
import org.gof.demo.worldsrv.support.observer.EventKey;
import org.gof.demo.worldsrv.task.TaskConditionTypeKey;

import java.util.ArrayList;
import java.util.List;


public class KungFuRaceManager extends ManagerBase {

	private boolean isKungFuRaceNotOpen(HumanObject humanObj){
        return !humanObj.isModUnlock(FuncOpenType.FUNC_KUNG_FU_RACE);
    }

	/**
	 * 获取实例
	 * @return
	 */
	public static KungFuRaceManager inst() {
		return inst(KungFuRaceManager.class);
	}

	/**
	 * 信息总览
	 * @param humanObj
	 * @param isLogin
	 */
	public void _msg_kungfu_race_info_c2s(HumanObject humanObj, boolean isLogin) {
		if(isKungFuRaceNotOpen(humanObj)){
			return;
		}
		KungFuRaceUtils.getCrossKungFuRaceServiceProxy(humanObj, res -> {
			if(res.failed()){
				return;
			}
			HumanDailyResetInfo info = humanObj.getDailyResetInfo(DailyResetTypeKey.dailyKungFuRaceQualifyingChallengeNum.getType());
			KungFuRaceServiceProxy proxy = res.result();
			proxy.getOverView(humanObj.id, info.getValue());
			proxy.listenResult(this::_result_getOverView, "humanObj", humanObj, "isLogin", isLogin);
		});
	}

	private void _result_getOverView(Param results, Param context) {
		HumanObject humanObj = Utils.getParamValue(context, "humanObj", null);
		boolean isLogin = Utils.getParamValue(context, "isLogin", false);
		boolean bResult = Utils.getParamValue(results, "result", false);
		if (!bResult) {
			Log.kungFuRace.warn("getOverView fail humanId={}", humanObj.id);
			return;
		}
		humanObj.currentKungFuRaceStageGroup = Utils.getParamValue(results, "curStageGroup", 0);
//		Log.kungFuRace.info("获取武道会总览信息 humanId={} curStageGroup={}", humanObj.id, humanObj.currentKungFuRaceStageGroup);
		if(isLogin){
			// 登录时，回收竞猜币
			recycleBetCoin(humanObj, false);
		}
	}

	/**
	 * 获取可加入队伍列表
	 * @param humanObj
	 */
	public void _msg_kungfu_race_null_list_c2s(HumanObject humanObj) {
		if(isKungFuRaceNotOpen(humanObj)){
			return;
		}
		KungFuRaceUtils.getCrossKungFuRaceServiceProxy(humanObj, res -> {
			if(res.failed()){
				return;
			}
			KungFuRaceServiceProxy proxy = res.result();
			proxy.getMainTeamList(humanObj.id);
		});
	}

	/**
	 * 搜索队伍
	 * @param humanObj
	 * @param teamId
	 */
	public void _msg_kungfu_race_lookup_c2s(HumanObject humanObj, long teamId) {
		if(isKungFuRaceNotOpen(humanObj)){
			return;
		}
		KungFuRaceUtils.getCrossKungFuRaceServiceProxy(humanObj, res -> {
			if(res.failed()){
				return;
			}
			KungFuRaceServiceProxy proxy = res.result();
			proxy.lookupTeam(humanObj.id, teamId);
		});
	}


	/**
	 * 创建队伍
	 * @param humanObj
	 * @param teamName
	 */
	public void _msg_kungfu_race_team_create_c2s(HumanObject humanObj, String teamName) {
		if(isKungFuRaceNotOpen(humanObj)){
			return;
		}
		// 每次创建队伍时，同步最新humanBrief到跨服，保证入队时能加载到玩家的humanBrief
		HumanManager.inst().updateHumanBrief(humanObj, true, ret->{
			KungFuRaceUtils.getCrossKungFuRaceServiceProxy(humanObj, res -> {
				if(res.failed()){
					return;
				}
				KungFuRaceServiceProxy proxy = res.result();
				proxy.createTeam(humanObj.id, teamName);
			});
		});
	}

	/**
	 * 申请加入队伍
	 * @param humanObj
	 * @param teamId
	 */
	public void _msg_kungfu_race_req_team_c2s(HumanObject humanObj, long teamId) {
		if(isKungFuRaceNotOpen(humanObj)){
			return;
		}
		// 每次申请加入队伍时，同步最新humanBrief到跨服，保证入队时能加载到玩家的humanBrief
		HumanManager.inst().updateHumanBrief(humanObj, true, ret->{
			KungFuRaceUtils.getCrossKungFuRaceServiceProxy(humanObj, res -> {
				if(res.failed()){
					return;
				}
				KungFuRaceServiceProxy proxy = res.result();
				proxy.applyJoinTeam(humanObj.id, humanObj.name, teamId);
			});
		});
	}

	/**
	 * 获取申请入队列表
	 * @param humanObj
	 */
	public void _msg_kungfu_race_req_list_c2s(HumanObject humanObj) {
		if(isKungFuRaceNotOpen(humanObj)){
			return;
		}
		KungFuRaceUtils.getCrossKungFuRaceServiceProxy(humanObj, res -> {
			if(res.failed()){
				return;
			}
			KungFuRaceServiceProxy proxy = res.result();
			proxy.getTeamApplyList(humanObj.id);
		});
	}

	/**
	 * 查询申请队伍角色信息
	 * @param humanObj
	 * @param playerName
	 */
	public void _msg_kungfu_race_lookup_role_c2s(HumanObject humanObj, String playerName) {
		if(isKungFuRaceNotOpen(humanObj)){
			return;
		}
		KungFuRaceUtils.getCrossKungFuRaceServiceProxy(humanObj, res -> {
			if(res.failed()){
				return;
			}
			KungFuRaceServiceProxy proxy = res.result();
			proxy.lookupRole(humanObj.id, playerName);
		});
	}

	/**
	 * 处理入队申请
	 * @param humanObj
	 * @param applyHumanId
	 * @param isAgree
	 */
	public void _msg_kungfu_race_resp_role_c2s(HumanObject humanObj, long applyHumanId, int isAgree) {
		if(isKungFuRaceNotOpen(humanObj)){
			return;
		}
		KungFuRaceUtils.getCrossKungFuRaceServiceProxy(humanObj, res -> {
			if(res.failed()){
				return;
			}
			KungFuRaceServiceProxy proxy = res.result();
			proxy.respJoinTeamApply(humanObj.id, applyHumanId, isAgree);
		});
	}

	/**
	 * 退出队伍
	 * @param humanObj
	 */
	public void _msg_kungfu_race_team_exit_c2s(HumanObject humanObj) {
		if(isKungFuRaceNotOpen(humanObj)){
			return;
		}
		KungFuRaceUtils.getCrossKungFuRaceServiceProxy(humanObj, res -> {
			if(res.failed()){
				return;
			}
			KungFuRaceServiceProxy proxy = res.result();
			proxy.exitTeam(humanObj.id);
		});
	}

	/**
	 * 获取排位赛挑战列表
	 * @param humanObj
	 * @param type
	 */
	public void _msg_kungfu_race_enemys_c2s(HumanObject humanObj, int type) {
		if(isKungFuRaceNotOpen(humanObj)){
			return;
		}
		KungFuRaceUtils.getCrossKungFuRaceServiceProxy(humanObj, res -> {
			if(res.failed()){
				return;
			}
			HumanDailyResetInfo info = humanObj.getDailyResetInfo(DailyResetTypeKey.dailyKungFuRaceQualifyingChallengeNum.getType());
			KungFuRaceServiceProxy proxy = res.result();
			proxy.getQualifyingEnemyList(humanObj.id, type, info.getValue());
		});
	}

	/**
	 * 获取队伍详情
	 * @param humanObj
	 * @param teamId
	 */
	public void _msg_kungfu_race_team_info_c2s(HumanObject humanObj, long teamId) {
		if(isKungFuRaceNotOpen(humanObj)){
			return;
		}
		KungFuRaceUtils.getCrossKungFuRaceServiceProxy(humanObj, res -> {
			if(res.failed()){
				return;
			}
			KungFuRaceServiceProxy proxy = res.result();
			proxy.getTeamInfo(humanObj.id, teamId);
		});
	}

	/**
	 * 变更战斗站位
	 * @param humanObj
	 * @param posList
	 */
	public void _msg_kungfu_race_pos_c2s(HumanObject humanObj, List<Define.p_key_value> posList) {
		if(isKungFuRaceNotOpen(humanObj)){
			return;
		}
		KungFuRaceUtils.getCrossKungFuRaceServiceProxy(humanObj, res -> {
			if(res.failed()){
				return;
			}
			KungFuRaceServiceProxy proxy = res.result();
			proxy.adjustBattlePos(humanObj.id, posList);
		});
	}

	/**
	 * 同步方案
	 * @param humanObj
	 */
	public void _msg_kungfu_race_pet_plan_c2s(HumanObject humanObj) {
		if(isKungFuRaceNotOpen(humanObj)){
			return;
		}
		// 同步最新humanBrief到跨服，保证跨服能加载到玩家最新的humanBrief
		HumanManager.inst().updateHumanBrief(humanObj, true, ret->{
			KungFuRaceUtils.getCrossKungFuRaceServiceProxy(humanObj, res -> {
				if(res.failed()){
					return;
				}
				KungFuRaceServiceProxy proxy = res.result();
				proxy.updateHumanBrief(humanObj.id);
			});
		});
	}

	/**
	 * 获取排位赛排行榜
	 * @param humanObj
	 * @param page
	 */
	public void _msg_kungfu_race_rank_c2s(HumanObject humanObj, int page) {
		if(isKungFuRaceNotOpen(humanObj)){
			return;
		}
		if (page <= 0 || page > 100000) {
			// 数据异常，不处理
			Log.kungFuRace.warn("===请求的页码异常，page={}, humanId={}", page, humanObj.id);
			return;
		}
		KungFuRaceUtils.getCrossKungFuRaceServiceProxy(humanObj, res -> {
			if(res.failed()){
				return;
			}
			KungFuRaceServiceProxy proxy = res.result();
			proxy.getQualifyingRankInfo(humanObj.id, page);
		});
	}

	/**
	 * 排位赛进入战斗
	 * @param humanObj
	 * @param teamId
	 */
	public void _msg_kungfu_race_team_combat_c2s(HumanObject humanObj, long teamId) {
		if(isKungFuRaceNotOpen(humanObj)){
			return;
		}
		KungFuRaceUtils.getCrossKungFuRaceServiceProxy(humanObj, res -> {
			if(res.failed()){
				return;
			}
			HumanDailyResetInfo info = humanObj.getDailyResetInfo(DailyResetTypeKey.dailyKungFuRaceQualifyingChallengeNum.getType());
			KungFuRaceServiceProxy proxy = res.result();
			proxy.enterCombat(humanObj.id, teamId, info.getValue());
		});
	}

	/**
	 * 排位赛发送战斗结果
	 * @param humanObj
	 * @param result
	 * @param extList
	 */
	public void _msg_kungfu_race_team_result_c2s(HumanObject humanObj, int result, List<Define.p_key_value> extList) {
		if(isKungFuRaceNotOpen(humanObj)){
			return;
		}
		KungFuRaceUtils.getCrossKungFuRaceServiceProxy(humanObj, res -> {
			if(res.failed()){
				return;
			}
			HumanDailyResetInfo info = humanObj.getDailyResetInfo(DailyResetTypeKey.dailyKungFuRaceQualifyingChallengeNum.getType());
			KungFuRaceServiceProxy proxy = res.result();
			proxy.combatResult(humanObj.id, result, extList, info.getValue());
			proxy.listenResult((results, context) -> {
				boolean bResult = Utils.getParamValue(results, "result", false);
				if (!bResult) {
					Log.kungFuRace.warn("combatResult fail humanId={}", humanObj.id);
					return;
				}
				int myNewScore = Utils.getParamValue(results, "myNewScore", 0);
				// 增加每日战斗次数
				info.setValue(info.getValue() + 1);
				humanObj.saveDailyResetRecord();
				humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_武道会任务, TaskConditionTypeKey.TASK_TYPE_98, 1);
				humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_武道会任务, TaskConditionTypeKey.TASK_TYPE_99, myNewScore);
			});
		});
	}

	/**
	 * 获取排位赛战报
	 * @param humanObj
	 */
	public void _msg_kungfu_race_report_c2s(HumanObject humanObj) {
		if(isKungFuRaceNotOpen(humanObj)){
			return;
		}
		KungFuRaceUtils.getCrossKungFuRaceServiceProxy(humanObj, res -> {
			if(res.failed()){
				return;
			}
			KungFuRaceServiceProxy proxy = res.result();
			proxy.getQualifyingBattleReport(humanObj.id);
		});
	}

	/**
	 * 获取循环赛信息
	 * @param humanObj
	 * @param group
	 * @param round
	 * @param stage
	 */
	public void _msg_kungfu_race_loop_c2s(HumanObject humanObj, int group, int round, int stage) {
		if(isKungFuRaceNotOpen(humanObj)){
			return;
		}
		KungFuRaceUtils.getCrossKungFuRaceServiceProxy(humanObj, res -> {
			if(res.failed()){
				return;
			}
			KungFuRaceServiceProxy proxy = res.result();
			proxy.getLoopInfo(humanObj.id, group, round, stage);
		});
	}

	/**
	 * 获取循环赛积分榜
	 * @param humanObj
	 * @param group
	 * @param stage
	 */
	public void _msg_kungfu_race_loop_rank_c2s(HumanObject humanObj, int group, int stage) {
		if(isKungFuRaceNotOpen(humanObj)){
			return;
		}
		KungFuRaceUtils.getCrossKungFuRaceServiceProxy(humanObj, res -> {
			if(res.failed()){
				return;
			}
			KungFuRaceServiceProxy proxy = res.result();
			proxy.getLoopRankInfo(humanObj.id, group, stage);
		});
	}

	/**
	 * 获取循环赛竞猜信息
	 * @param humanObj
	 * @param group
	 * @param action
	 * @param stage
	 */
	public void _msg_kungfu_race_loop_bet_info_c2s(HumanObject humanObj, int group, int action, int stage) {
		if(isKungFuRaceNotOpen(humanObj)){
			return;
		}
		KungFuRaceUtils.getCrossKungFuRaceServiceProxy(humanObj, res -> {
			if(res.failed()){
				return;
			}
			KungFuRaceServiceProxy proxy = res.result();
			proxy.getLoopBetInfo(humanObj.id, group, action, stage);
		});
	}

	/**
	 * 获取淘汰赛信息
	 * @param humanObj
	 */
	public void _msg_kungfu_race_knockout_c2s(HumanObject humanObj) {
		if(isKungFuRaceNotOpen(humanObj)){
			return;
		}
		KungFuRaceUtils.getCrossKungFuRaceServiceProxy(humanObj, res -> {
			if(res.failed()){
				return;
			}
			KungFuRaceServiceProxy proxy = res.result();
			proxy.getKnockoutInfo(humanObj.id);
		});
	}

	/**
	 * 获取名人堂信息
	 * @param humanObj
	 * @param season
	 */
	public void _msg_kungfu_race_champion_history_c2s(HumanObject humanObj, int season) {
		if(isKungFuRaceNotOpen(humanObj)){
			return;
		}
		KungFuRaceUtils.getCrossKungFuRaceServiceProxy(humanObj, res -> {
			if(res.failed()){
				return;
			}
			KungFuRaceServiceProxy proxy = res.result();
			proxy.getChampionHistory(humanObj.id, season);
		});
	}

	/**
	 * 获取庆祝期信息
	 * @param humanObj
	 */
	public void _msg_kungfu_race_champion_info_c2s(HumanObject humanObj) {
		if(isKungFuRaceNotOpen(humanObj)){
			return;
		}
		KungFuRaceUtils.getCrossKungFuRaceServiceProxy(humanObj, res -> {
			if(res.failed()){
				return;
			}
			HumanDailyResetInfo info = humanObj.getDailyResetInfo(DailyResetTypeKey.dailyKungFuRaceWorshipNum.getType());
			KungFuRaceServiceProxy proxy = res.result();
			proxy.getChampionInfo(humanObj.id, info.getValue());
		});
	}

	/**
	 * 膜拜冠军
	 * @param humanObj
	 */
	public void _msg_kungfu_race_worship_c2s(HumanObject humanObj) {
		if(isKungFuRaceNotOpen(humanObj)){
			return;
		}
		HumanDailyResetInfo info = humanObj.getDailyResetInfo(DailyResetTypeKey.dailyKungFuRaceWorshipNum.getType());
		if(info.getValue() > 0){
			Log.kungFuRace.warn("今日已膜拜冠军 humanId={}", humanObj.id);
			return;
		}
		KungFuRaceUtils.getCrossKungFuRaceServiceProxy(humanObj, res -> {
			if(res.failed()){
				return;
			}
			KungFuRaceServiceProxy proxy = res.result();
			proxy.worshipChampion(humanObj.id);
			proxy.listenResult((results, context) -> {
				boolean bResult = Utils.getParamValue(results, "result", false);
				if (!bResult) {
					Log.kungFuRace.warn("worshipChampion fail humanId={}", humanObj.id);
					return;
				}
				// 增加每日膜拜次数
				info.setValue(info.getValue() + 1);
				humanObj.saveDailyResetRecord();
				ConfGlobal conf = ConfGlobal.get(ConfGlobalKey.display_worship_reward);
				// 膜拜奖励
				int[] rewards = conf != null ? conf.intArray : new int[]{2, 200};
				ProduceManager.inst().produceAdd(humanObj, rewards, MoneyItemLogKey.武道会膜拜获得);
				InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, InstanceConstants.showType_0, rewards);
			});
		});
	}

	/**
	 * 播放战斗录像
	 * @param humanObj
	 * @param vid
	 */
	public void _msg_kungfu_race_play_video_c2s(HumanObject humanObj, long vid) {
		if(isKungFuRaceNotOpen(humanObj)){
			return;
		}
		KungFuRaceUtils.getCrossKungFuRaceServiceProxy(humanObj, res -> {
			if(res.failed()){
				return;
			}
			KungFuRaceServiceProxy proxy = res.result();
			proxy.playBattleVideo(humanObj.id, vid);
		});
	}

	/**
	 * 发送战斗录像结果
	 * @param humanObj
	 * @param vid
	 * @param result
	 */
	public void _msg_kungfu_race_play_video_result_c2s(HumanObject humanObj, long vid, int result) {
		if(isKungFuRaceNotOpen(humanObj)){
			return;
		}
		KungFuRaceUtils.getCrossKungFuRaceServiceProxy(humanObj, res -> {
			if(res.failed()){
				return;
			}
			KungFuRaceServiceProxy proxy = res.result();
			proxy.playBattleVideoResult(humanObj.id, vid, result);
		});
	}

	/**
	 * 竞猜比赛
	 * @param humanObj
	 * @param stage
	 * @param teamId
	 * @param betNum
	 */
	public void _msg_kungfu_race_bet_c2s(HumanObject humanObj, int stage, long teamId, int betNum) {
		if(isKungFuRaceNotOpen(humanObj)){
			return;
		}
		if(betNum == 0){
			Log.kungFuRace.warn("betNum == 0 humanId={}", humanObj.id);
			return;
		}
		int[] costArr = new int[]{KungFuRaceConst.KUNG_FU_RACE_BET_COIN_ID, betNum};
		ReasonResult result = ProduceManager.inst().canCostIntArr(humanObj, costArr);
		if(!result.success){
			Inform.sendMsg_error(humanObj, ErrorTip.ItemNotEnough);
			return;
		}
		KungFuRaceUtils.getCrossKungFuRaceServiceProxy(humanObj, res -> {
			if(res.failed()){
				return;
			}
			KungFuRaceServiceProxy proxy = res.result();
			proxy.betCompetition(humanObj.id, stage, teamId, betNum);
			proxy.listenResult((results, context) -> {
				boolean bResult = Utils.getParamValue(results, "result", false);
				if (!bResult) {
					Log.kungFuRace.warn("betCompetition fail humanId={}", humanObj.id);
					return;
				}
				ProduceManager.inst().costIntArr(humanObj, costArr, MoneyItemLogKey.武道会竞猜消耗);
			});
		});
	}

	/**
	 * 获取竞猜历史
	 * @param humanObj
	 */
	public void _msg_kungfu_bet_history_c2s(HumanObject humanObj) {
		if(isKungFuRaceNotOpen(humanObj)){
			return;
		}
		KungFuRaceUtils.getCrossKungFuRaceServiceProxy(humanObj, res -> {
			if(res.failed()){
				return;
			}
			KungFuRaceServiceProxy proxy = res.result();
			proxy.getBetHistory(humanObj.id);
		});
	}

	@Listener(EventKey.FUNCTION_OPEN)
	public void listener_FUNCTION_OPEN(Param param) {
		HumanObject humanObj = param.get("humanObj");
		List<Integer> snList = Utils.getParamValue(param, "openList", new ArrayList<>());
		if (!snList.contains(FuncOpenType.FUNC_KUNG_FU_RACE)) {
			return;
		}
		// 功能开放时，请求一下总览信息
		_msg_kungfu_race_info_c2s(humanObj, false);
	}

	@Listener(EventKey.HUMAN_LOGIN_FINISH)
	public void _on_HUMAN_LOGIN_FINISH(Param param) {
		HumanObject humanObj = param.get("humanObj");
		_msg_kungfu_race_info_c2s(humanObj, true);
	}

	/**
	 * 武道会发竞猜币代办
	 * @param param
	 */
	@Listener(value = EventKey.POCKET_LINE_HANDLE_ONE, subStr = PocketLineEventSubKey.KUNG_FU_RACE_ADD_BET_COIN)
	public void pocketLine_KUNG_FU_RACE_ADD_BET_COIN(Param param) {
		HumanObject humanObj = param.get("humanObj");
		PocketLine p = param.get("pocketLine");
		JSONObject jo = Utils.toJSONObject(p.getParam());
		int betNum = jo.getIntValue("betNum");
		int[] rewards = new int[]{KungFuRaceConst.KUNG_FU_RACE_BET_COIN_ID, betNum};
		ProduceManager.inst().produceAdd(humanObj, rewards, MoneyItemLogKey.武道会竞猜获得);
		Log.task.info("武道会发竞猜币代办 humanId:{} betNum={}", humanObj.id, betNum);
	}

    /**
     * 武道会回收竞猜币
     * @param humanObj
     * @param isForce
     */
	public void recycleBetCoin(HumanObject humanObj, boolean isForce) {
		int loginSeason = humanObj.kungFuRaceLoginSeason;
		int season = KungFuRaceUtils.getCurrentSeason();
		if(humanObj.currentKungFuRaceStageGroup == KungFuRaceConst.KUNG_FU_RACE_STEP_LOOP || humanObj.currentKungFuRaceStageGroup == KungFuRaceConst.KUNG_FU_RACE_STEP_KNOCKOUT){
			if(!isForce && loginSeason == season){
				// 在同一个赛季的循环赛和淘汰赛，不回收
				return;
			}
		}
		int betCoinNum = humanObj.operation.itemData.getItemNum(KungFuRaceConst.KUNG_FU_RACE_BET_COIN_ID);
		if(betCoinNum == 0) {
			return;
		}
		int[] costs = new int[]{KungFuRaceConst.KUNG_FU_RACE_BET_COIN_ID, betCoinNum};
		ProduceManager.inst().costIntArr(humanObj, costs, MoneyItemLogKey.武道会回收竞猜币);
		ConfGlobal conf = ConfGlobal.get(ConfGlobalKey.guess_points_rate);
		int rate = conf != null ? conf.value : 5;
		int[] adds = new int[]{KungFuRaceConst.KUNG_FU_RACE_BET_SCORE_ID, betCoinNum * rate};
		ProduceManager.inst().produceAdd(humanObj, adds, MoneyItemLogKey.武道会发放竞猜积分);
		InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, InstanceConstants.showType_0, adds);
		Log.kungFuRace.info("===武道会回收竞猜币 humanId={} mySeason={} season={} costs={} adds={}", humanObj.id, loginSeason, season, costs, adds);
	}

	/**
	 * 武道会更新任务代办
	 * @param param
	 */
	@Listener(value = EventKey.POCKET_LINE_HANDLE_ONE, subStr = PocketLineEventSubKey.KUNG_FU_RACE_UPDATE_TASK)
	public void pocketLine_KUNG_FU_RACE_UPDATE_TASK(Param param) {
		HumanObject humanObj = param.get("humanObj");
		PocketLine p = param.get("pocketLine");
		JSONObject jo = Utils.toJSONObject(p.getParam());
		int season = jo.getIntValue("season");
		int taskType = jo.getIntValue("taskType");
		Object[] objs = jo.getObject("objs", Object[].class);
		int loginSeason = humanObj.kungFuRaceLoginSeason;
		if(loginSeason != season){
			Log.task.info("武道会更新任务代办忽略 humanId:{} taskType={} objs={} loginSeason={} season={}", humanObj.id, taskType, objs, loginSeason, season);
			return;
		}
		humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_武道会任务, taskType, objs);
		Log.task.info("武道会更新任务代办成功 humanId:{} taskType={} objs={}", humanObj.id, taskType, objs);
	}
}
