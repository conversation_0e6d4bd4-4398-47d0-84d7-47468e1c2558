package org.gof.demo.worldsrv.kungFuRace;

import com.alibaba.fastjson.JSONObject;
import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.interfaces.ISerilizable;

import java.io.IOException;

public class KungFuRaceRewardVO implements ISerilizable {
    public long humanId;
    public int confSn;

    public KungFuRaceRewardVO() {}
    public KungFuRaceRewardVO(long humanId, int confSn) {
        this.humanId = humanId;
        this.confSn = confSn;
    }

    @Override
    public void writeTo(OutputStream out) throws IOException {
        out.write(humanId);
        out.write(confSn);
    }

    @Override
    public void readFrom(InputStream in) throws IOException {
        humanId = in.read();
        confSn = in.read();
    }

    @Override
    public String toString(){
        JSONObject jo = new JSONObject();
        jo.put("humanId", humanId);
        jo.put("confSn", confSn);
        return jo.toJSONString();
    }

}