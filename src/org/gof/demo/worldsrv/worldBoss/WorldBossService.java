package org.gof.demo.worldsrv.worldBoss;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import io.vertx.core.Future;
import io.vertx.core.json.JsonArray;
import io.vertx.redis.client.Response;
import org.apache.commons.collections.CollectionUtils;
import org.gof.core.Port;
import org.gof.core.dbsrv.redis.AwaitUtil;
import org.gof.core.dbsrv.redis.EntityManager;
import org.gof.core.dbsrv.redis.RedisTools;
import org.gof.core.gen.proxy.DistrClass;
import org.gof.core.gen.proxy.DistrMethod;
import org.gof.core.scheduler.ScheduleMethod;
import org.gof.core.scheduler.ScheduleTask;
import org.gof.core.support.*;
import org.gof.core.support.S;
import org.gof.demo.worldsrv.common.DataResetService;
import org.gof.demo.worldsrv.common.GamePort;
import org.gof.demo.worldsrv.common.GameServiceBase;
import org.gof.demo.worldsrv.config.*;
import org.gof.demo.worldsrv.entity.Human;
import org.gof.demo.worldsrv.entity.Human2;
import org.gof.demo.worldsrv.entity.WorldBossInfo;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.human.HumanData;
import org.gof.demo.worldsrv.human.HumanGlobalServiceProxy;
import org.gof.demo.worldsrv.human.HumanManager;
import org.gof.demo.worldsrv.mail.MailManager;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgDungeon;
import org.gof.demo.worldsrv.rank.RankInfo;
import org.gof.demo.worldsrv.rank.RankParamKey;
import org.gof.demo.worldsrv.redis.RedisKeys;
import org.gof.demo.worldsrv.support.*;
import org.gof.demo.worldsrv.support.enumKey.ConfGlobalKey;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

@DistrClass(
	servId = D.SERV_WORLD_BOSS,
	importClass = {RankInfo.class, List.class}
)
public class WorldBossService extends GameServiceBase {

	private Map<Integer, WorldBossInfo> worldBossMap = new HashMap<>();
	Map<Integer, List<RankInfo>> serverBossHurtRankMap = new ConcurrentHashMap<>();

	private static boolean isSendRank = false;
	private TickTimer ttOpen = new TickTimer();
	private TickTimer ttEnd = new TickTimer();
	// 昨天
	Map<Integer, List<RankInfo>> zoneBossHurtRankOldMap = new HashMap<>();

	private List<Integer> serverIdListNow = new ArrayList<>();

	private TickTimer checkServerIdTT = new TickTimer(5 * Time.MIN);//调度的处理


	public volatile transient AtomicInteger aiInit = new AtomicInteger(0);

	public WorldBossService(GamePort port) {
		super(port);
	}

	@Override
	protected void init() {
		initTT();
		checkServerId();
		if(Config.DATA_DEBUG){
			loadServer(Arrays.asList(C.GAME_SERVER_ID));
		}
		if(S.isRedis){
			return;
		}
	}

	@DistrMethod
	public void loadServer(List<Integer> serverIdList){
		Collection<Integer> diffAdd = CollectionUtils.subtract(serverIdList, serverIdListNow);
		long timeNow = Port.getTime();
		for(int serverId : diffAdd){
			serverIdListNow.add(serverId);
			WorldBossInfo worldBoss = (WorldBossInfo) EntityManager.getEntity(WorldBossInfo.class, serverId);
			if(worldBoss == null){
				createWorldBossInfo(serverId);
			} else {
				if(!Utils.isSameDay(worldBoss.getUpdateTime(), timeNow)){
					worldBoss.setInitBuff(getBuffSn(0));
					worldBoss.update();
				}
				worldBossMap.put(serverId, worldBoss);
			}

			initLoadRedis(serverId);
		}
	}

	private WorldBossInfo createWorldBossInfo(int serverId){
		WorldBossInfo worldBoss = new WorldBossInfo();
		worldBoss.setId(serverId);
		worldBoss.setInitBuff(getBuffSn(0));
		worldBoss.persist();
		worldBossMap.put(serverId, worldBoss);
		return worldBoss;
	}

	private void initLoadRedis(int serverId){
		String dateStr = Utils.formatTime(Port.getTime(), "yyyy-MM-dd");
		String redisKey = RedisKeys.worldBoss + serverId + dateStr;

		RedisTools.getRankListByIndex(EntityManager.redisClient, redisKey, 0, -1, false, ret->{
			JsonArray json = ret.result();
			int size = json.getList().size();
			for(int i = 0; i < size; i++) {
				String str = String.valueOf(json.getList().get(i));
				JSONArray ja = Utils.toJSONArray(str);

				int sumSize = ja.size();
				Map<Long, Long> humanIdHurtMap = new HashMap<>();
				for (int m = 0; m < sumSize; m++) {
					JSONObject jo = Utils.toJSONObject(ja.getString(m));
					long humanId = jo.getLong("humanId");
					humanIdHurtMap.put(humanId, jo.getLong("param"));
				}
				List<Long> humanIdList = new ArrayList<>(humanIdHurtMap.keySet());
				if(humanIdList.isEmpty()){
					return;
				}
				int listSize = humanIdList.size();
				int sum = Math.max(1,(int)Math.ceil((double) humanIdList.size() / RankParamKey.pageNum));

				for(int m = 0; m < sum; m++){
					List<Long> pageIdList = humanIdList.subList(Math.min(m * 50,listSize), Math.min(listSize, (m + 1) * 50));
					if(pageIdList.isEmpty()){
						break;
					}
					aiInit.incrementAndGet();
					HumanData.getList(pageIdList, HumanManager.inst().humanClasses2, ret2 -> {
						if (ret2.failed()) {
							Log.game.error("无法获取数据，roleId={}", pageIdList);
							aiInit.decrementAndGet();
							if(aiInit.get() <= 0){
								checkRank();
							}
							return;
						}
						List<HumanData> humanDataList = ret2.result();
						if(humanDataList == null){
							Log.game.error("无法获数据，roleIds={}", pageIdList);
							aiInit.decrementAndGet();
							if(aiInit.get() <= 0){
								checkRank();
							}
							return;
						}
						for(HumanData humanData : humanDataList){
							RankInfo rankInfo = new RankInfo(humanData.human,humanData.human2, humanIdHurtMap.get(humanData.human.getId()));
							List<RankInfo> rankList = serverBossHurtRankMap.computeIfAbsent(rankInfo.serverId, k -> new ArrayList<>());
							rankList.add(rankInfo);
						}
						aiInit.decrementAndGet();
						if(aiInit.get() <= 0){
							checkRank();
						}
					});
				}
			}
		});
	}

	private void checkRank(){
		for(List<RankInfo> rankList : serverBossHurtRankMap.values()){
			// 倒序，伤害高的在前面
			Collections.sort(rankList, (a, b) -> {
				int ret = 0;// 0默认相等
				if (a != null && b != null) {
					if (a.param < b.param) {
						ret = 1;
					} else if (a.param > b.param) {
						ret = -1;
					}
				}
				return ret;
			});
			int rank = 1;
			for(RankInfo rankInfo : rankList){
				rankInfo.rank = rank;
				rankInfo.rank_info = rankInfo.rank_info.toBuilder().setRank(rank).build();
				++rank;
			}
		}
	}

	private void initTT(){
		ConfGlobal conf = ConfGlobal.get(ConfGlobalKey.world_boss_open_time.SN);
		long timeNow = Port.getTime();
		int hour = Utils.getHourOfDay(timeNow);
		long timeOpen = Utils.getDayTime(timeNow, hour, 0,0);
		long timeEnd = Utils.getDayTime(timeNow, hour, conf.value,0);
		if(timeNow > timeEnd){
			timeOpen = Utils.getDayTime(timeNow, hour + 1, 0,0);
			timeEnd = Utils.getDayTime(timeNow, hour + 1, conf.value,0);
		}

		if(timeNow < timeOpen){
			long interval = timeOpen - timeNow;
			ttOpen.start(interval);
		}

		if(timeNow < timeEnd){
			long interval = timeEnd - timeNow;
			ttEnd.start(interval);
		}
	}

	@Override
	public void pulseOverride() {
		long nowTime = Port.getTime();
        if (ttOpen.isPeriod(nowTime)) {
            ttOpen.stop();
			initTT();
			HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance();
			proxy.sendMsgToAll(null, sendMsg_dungeon_world_boss_change_s2c(true));
        }
		if (ttEnd.isPeriod(nowTime)) {
			ttEnd.stop();
			initTT();
			HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance();
			proxy.sendMsgToAll(null, sendMsg_dungeon_world_boss_change_s2c(false));
		}

		if(checkServerIdTT.isPeriod(nowTime)){
			checkServerId();
		}
	}

	private void checkServerId(){
		List<Integer> serverList = Util.getServerTagList(C.GAME_SERVER_ID);
		if(serverList.isEmpty()){
			Log.temp.error("===没有获取到serverId列表，请检查配置,{}", C.GAME_SERVER_ID);
			return;
		}
		long timeNow = Port.getTime();
		for(Integer serverId : serverList){
			if(!serverIdListNow.contains(serverId)){
				serverIdListNow.add(serverId);
				WorldBossInfo worldBoss = (WorldBossInfo) EntityManager.getEntity(WorldBossInfo.class, serverId);
				if(worldBoss == null){
					worldBoss = createWorldBossInfo(serverId);
				} else {
					if(!Utils.isSameDay(worldBoss.getUpdateTime(), timeNow)){
						worldBoss.setInitBuff(getBuffSn(0));
						worldBoss.update();
					}
				}
				worldBossMap.put(serverId, worldBoss);
				initLoadRedis(serverId);
			}
		}

	}


	public MsgDungeon.dungeon_world_boss_change_s2c sendMsg_dungeon_world_boss_change_s2c(boolean isOpen){
		MsgDungeon.dungeon_world_boss_change_s2c.Builder msg = MsgDungeon.dungeon_world_boss_change_s2c.newBuilder();
		msg.setIsOpen(isOpen ? 1 : 0);
		return msg.build();
	}

	private int getBuffSn(int buffSn){
		List<Integer> snList = new ArrayList<>();
		for(ConfWorldBossBufflist conf : ConfWorldBossBufflist.findAll()){
			if(buffSn== conf.sn){
				continue;
			}
			snList.add(conf.sn);
		}
		int index = Utils.random(snList.size());
		return snList.get(index);
	}

	@ScheduleMethod(DataResetService.CRON_DAY_ZERO)
	public void _CRON_DAY_ZERO() {
		isSendRank = false;
		long timeNow = Port.getTime();
		for(int serverId : serverIdListNow){
			WorldBossInfo worldBoss = (WorldBossInfo) EntityManager.getEntity(WorldBossInfo.class, serverId);
			if(worldBoss == null){
				worldBoss = createWorldBossInfo(serverId);
			}
			int day = Utils.getDaysBetween(worldBoss.getUpdateTime(), timeNow);
			ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.world_boss_buff_switch_day.SN);
			if(day > confGlobal.value){
				worldBoss.setUpdateTime(timeNow);
				worldBoss.setInitBuff(getBuffSn(worldBoss.getInitBuff()));
				worldBoss.update();
			}
		}
	}
	/**
	 * 定时每天23点55分
	 */
	@ScheduleMethod(DataResetService.CRON_DAY_23_55ST)
	public void _CRON_DAY_23_55ST() {
		sendRankReward();
	}


	@ScheduleMethod(DataResetService.CRON_DAY_HOUR_30ST)
	public void _CRON_DAY_HOUR_30ST() {
		saveRank();
	}

	private void saveRank(){
		if(serverBossHurtRankMap.isEmpty()){
			return;
		}
		String dateStr = Utils.formatTime(Port.getTime(), "yyyy-MM-dd");
		List<String> keyList = new ArrayList<>();
		for(int serverId : serverBossHurtRankMap.keySet()){
			keyList.add(Utils.createStr("{}{}{}", RedisKeys.worldBoss, serverId, dateStr));
		}

		AwaitUtil.awaitResult(handler -> {
			EntityManager.redisClient.del(keyList, delRes -> {
				if (delRes.succeeded()) {
					handler.handle(Future.succeededFuture(delRes.result()));
				} else {
					handler.handle(Future.failedFuture(delRes.cause()));
				}
				if (delRes.failed()) {
					Log.temp.error("删除失败 keys " + keyList + ": " + delRes.cause().getMessage());
				}
			});
		});

		for(Map.Entry<Integer, List<RankInfo>> entry : serverBossHurtRankMap.entrySet()){
			int serverId = entry.getKey();
			String key = RedisKeys.worldBoss + serverId + dateStr;
			List<String> keyListNew = new ArrayList<>();
			keyListNew.add(key);
			keyListNew.add(String.valueOf(serverId));
			keyListNew.add(toJSON(entry.getValue()));
			EntityManager.redisClient.zadd(keyListNew, zaddRes -> {
				if(!zaddRes.succeeded()){
					Log.temp.error("zadd失败 keys ={}, e={}", keyListNew, zaddRes.cause().getMessage());
				}
			});
		}
		for(Map.Entry<Integer, List<RankInfo>> entry : serverBossHurtRankMap.entrySet()) {
			String key = RedisKeys.worldBoss + entry.getKey() + dateStr;
			RedisTools.expire(EntityManager.getRedisClient(), key, (int)(4 * Time.DAY / Time.SEC));
		}
	}

	private String toJSON(List<RankInfo> rankList){
		JSONArray ja = new JSONArray();
		for(RankInfo rankInfo : rankList){
			ja.add(rankInfo.toJSON());
		}
		return ja.toJSONString();
	}

	private void sendRankReward() {
		if (isSendRank) {
			return;
		}
		isSendRank = true;
		long timeNow = Port.getTime();

		// 处理总伤害奖励
		for (Map.Entry<Integer, List<RankInfo>> entry : serverBossHurtRankMap.entrySet()) {
			int zone = entry.getKey();
			List<RankInfo> rankList = entry.getValue();

			long openTime = Util.getZoneOpenServerTime(zone);
			int day = Utils.getDaysBetween(openTime, timeNow) + 1;
			int sn = GlobalConfVal.getWorldBossTotalDmgSn(day);
			ConfWorldBossTotalDmg conf = ConfWorldBossTotalDmg.get(sn);
			if (conf == null || conf.total_dmg == null || conf.total_dmg.length <= 0) {
				Log.temp.error("===没有配置世界boss总伤害，ConfWorldBossTotalDmg={}, day={}", sn, day);
				continue;
			}

			long sumHurt = rankList.stream().mapToLong(r -> r.param).sum();
			long levelDmg = 0L;
			Map<Integer, Integer> rewardMap = new HashMap<>();
			for (int i = 0; i < conf.total_dmg.length; i++) {
				if (sumHurt >= conf.total_dmg[i]) {
					int[] rewards = conf.reward_participate[i];
					for (int j = 0; j < rewards.length - 1; j += 2) {
						int itemSn = rewards[j];
						int itemNum = rewards[j + 1];
						int num = rewardMap.getOrDefault(itemSn, 0);
						rewardMap.put(itemSn, num + itemNum);
					}
					levelDmg = Math.max(levelDmg, conf.total_dmg[i]);
				}
			}

			if (rewardMap.isEmpty()) {
				continue;
			}

			JSONObject jo = new JSONObject();
			JSONObject joTemp = new JSONObject();
			joTemp.put(MailManager.MAIL_K_4, levelDmg);
			jo.put(MailManager.MAIL_PARAM_1, joTemp);
			String contentJSON = jo.toJSONString();
			String itemJSON = Utils.mapIntIntToJSON(rewardMap);

			// 分批发送总伤害邮件
			final int batchSize = 200;
			for (int i = 0; i < rankList.size(); i += batchSize) {
				final int startIndex = i;
				scheduleOnce(new ScheduleTask() {
					@Override
					public void execute() {
						int endIndex = Math.min(startIndex + batchSize, rankList.size());
						for (int j = startIndex; j < endIndex; j++) {
							RankInfo rankInfo = rankList.get(j);
							MailManager.inst().sendMail(rankInfo.humanId, MailManager.SYS_SENDER,
									10129, "", contentJSON, itemJSON, null);
						}
					}
				}, (i / batchSize) * 2000L); // 每2秒发送一批
			}
		}

		zoneBossHurtRankOldMap = Collections.unmodifiableMap(serverBossHurtRankMap);

		// 处理排名奖励
		for (Map.Entry<Integer, List<RankInfo>> entry : serverBossHurtRankMap.entrySet()) {
			List<RankInfo> rankList = entry.getValue();
			// 分批发送排名奖励邮件
			final int batchSize = 200;
			for (int i = 0; i < rankList.size(); i += batchSize) {
				final int startIndex = i;
				scheduleOnce(new ScheduleTask() {
					@Override
					public void execute() {
						int endIndex = Math.min(startIndex + batchSize, rankList.size());
						for (int j = startIndex; j < endIndex; j++) {
							RankInfo rankInfo = rankList.get(j);
							int rank = j + 1;

							int[][] arr = null;
							for (ConfWorldBossRank conf : ConfWorldBossRank.findAll()) {
								if (conf != null && conf.rank[0] <= rank && rank <= conf.rank[1]) {
									arr = conf.reward_rank;
									break;
								}
							}

							if (arr == null) {
								continue;
							}

							String itemJSON = Utils.mapIntIntToJSON(Utils.intArrToIntMap(new HashMap<>(), arr));

							JSONObject jo = new JSONObject();
							JSONObject joTemp = new JSONObject();
							joTemp.put(MailManager.MAIL_K_4, rank);
							jo.put(MailManager.MAIL_PARAM_1, joTemp);
							MailManager.inst().sendMail(rankInfo.humanId, MailManager.SYS_SENDER,
									10128, "", jo.toJSONString(), itemJSON, new Param().put("rank", rank));
						}
					}
				}, (i / batchSize) * 2000L); // 每2秒发送一批
			}
		}

		serverBossHurtRankMap.clear();
	}


	@DistrMethod
	public void updateBossHurt(int serverId, RankInfo rankInfo){
		// 累计排行
		updateBossHurtRank(serverId, rankInfo);
	}

	/**
	 * 本日单次最高排行
	 * <AUTHOR>
	 * @Date 2024/5/11
	 * @Param 
	 */
	public void updateBossHurtRank(int serverId, RankInfo rankInfo){
		if(rankInfo.humanId == 0 || rankInfo.param == 0){
			return;
		}
		List<RankInfo> rankList = serverBossHurtRankMap.get(serverId);
		if(rankList == null){
			rankList = new ArrayList<>();
			serverBossHurtRankMap.put(serverId, rankList);
		}
		if(rankList.isEmpty()){
			rankInfo.rank = 1;
			rankList.add(rankInfo);
			return;
		}
		boolean isAdd = true;
		for(RankInfo info : rankList){
			if(info.humanId == rankInfo.humanId){
				isAdd = false;
				if(rankInfo.param <= info.param){
					return;// 无变化不处理
				}
				rankList.remove(info);
				rankList.add(rankInfo);
				break;
			}
		}
		if(isAdd){
			rankList.add(rankInfo);
		}
		// 倒序，伤害高的在前面
		Collections.sort(rankList, (a, b) -> {
			int ret = 0;// 0默认相等
			if (a != null && b != null) {
				if (a.param < b.param) {
					ret = 1;
				} else if (a.param > b.param) {
					ret = -1;
				}
			}
			return ret;
		});
		List<RankInfo> removeList = new ArrayList<>();
		for(RankInfo rankInfo1 : rankList){
			if(rankInfo1.humanId <=0 || rankInfo1.param <=0){
				removeList.add(rankInfo1);
			}
		}
		
		rankList.removeAll(removeList);
		int rank = 1;
		for(RankInfo rankInfo1 : rankList){
			rankInfo1.rank = rank;
			rankInfo1.rank_info = rankInfo1.rank_info.toBuilder().setRank(rank).build();
			++rank;
		}
		saveRank();
	}

	@DistrMethod
	public void getWorldInfo(long humanId, int serverId){
		List<RankInfo> rankList = serverBossHurtRankMap.get(serverId);
		int myRank = 0;
		long myHurt = 0;
		long totalHurt = 0;
		WorldBossInfo worldBoss = worldBossMap.get(serverId);
		if(worldBoss == null){
			worldBoss = createWorldBossInfo(serverId);
			worldBossMap.put(serverId, worldBoss);
		}
		int buffSn = worldBoss.getInitBuff();
		RankInfo topRankInfo = null;
		if(rankList != null){
			int rank = 1;
			for(RankInfo rankInfo : rankList){
				if(rank == 1){
					topRankInfo = rankInfo;
				}
				if(rankInfo.humanId == humanId){
					myRank = rank;
					myHurt = rankInfo.param;
				}
				totalHurt += rankInfo.param;
				++rank;
			}
		}
		port.returns("myRank", myRank, "myHurt", myHurt, "totalHurt", totalHurt, "buffSn", buffSn, "topRankInfo", topRankInfo);
	}

	@DistrMethod
	public void getWorldRank(long humanId, int server, int serverId, int page){
		List<RankInfo> rankList = serverBossHurtRankMap.get(serverId);
		long totalHurt = 0;
		int pageMax = 1;
		Define.p_rank_info myInfo = null;
		if(rankList != null){
			int rank = 1;
			for(RankInfo rankInfo : rankList){
				if(rankInfo.humanId == humanId){
					myInfo = rankInfo.rank_info.toBuilder().setRank(rank).build();
				}
				totalHurt += rankInfo.param;
				rankInfo.rank_info = rankInfo.rank_info.toBuilder().setRank(rank).build();
				++rank;
			}
		} else {
			port.returns("myInfo", null, "list", null, "totalHurt", (int)totalHurt, "pageMax", pageMax);
			return;
		}
		pageMax = (int)Math.ceil(rankList.size() * 1.0D / RankParamKey.pageNum);
		int pageOpen = (page - 1) * RankParamKey.pageNum;
		int pageEnd =  page * RankParamKey.pageNum;
		if(pageOpen > pageEnd){
			return;
		}
		if(pageEnd > rankList.size()){
			pageEnd = rankList.size();
		}
		List<RankInfo> rankListNew = rankList.subList(pageOpen, pageEnd);
		List<Define.p_rank_info> list = new ArrayList<>();
		for(RankInfo rankInfo : rankListNew){
			list.add(rankInfo.rank_info);
		}
		port.returns("myInfo", myInfo, "list", list, "totalHurt", (int)totalHurt, "pageMax", pageMax);
	}

	@DistrMethod
	public void getBattleRank(int zone, int serverId){
		if(Utils.isDebugMode()){
			zone =1;
		}
		List<Define.p_battle_rank> resultList = new ArrayList<>();
		List<RankInfo> rankList = serverBossHurtRankMap.get(serverId);
		if(rankList == null){
			rankList = new ArrayList<>();
		}
		ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.world_boss_player_num.SN);
		int num = confGlobal.value - 1;
		if(num > rankList.size()){
			num = rankList.size();
		}
		List<RankInfo> rankListNew = rankList.subList(0, num);
		for(RankInfo rankInfo : rankListNew){
			Define.p_battle_rank.Builder dInfo = Define.p_battle_rank.newBuilder();
			dInfo.setName(rankInfo.rank_info.getName());
			dInfo.setHurt(rankInfo.param);
			resultList.add(dInfo.build());
		}
		port.returns("resultList", resultList);
	}

	@DistrMethod
	public void update(String json){
		saveRank();
	}

	@DistrMethod
	public void update1(String... objs){
		isSendRank = false;
		sendRankReward();
	}


	@DistrMethod
	public void update2(Param param){

	}

	@DistrMethod
	public void update3(Object... objs){

	}

}