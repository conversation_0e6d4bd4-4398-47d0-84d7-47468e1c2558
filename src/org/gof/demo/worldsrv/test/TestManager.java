package org.gof.demo.worldsrv.test;
import java.net.URI;
import java.net.URISyntaxException;
import org.gof.core.support.ManagerBase;
import org.gof.core.support.Utils;
import redis.clients.jedis.Jedis;
import java.util.Base64;

public class TestManager extends ManagerBase {
    
    public static void main(String[] args) {
        String str = "600710000013800002";
        long num = Utils.longValue(str);
        System.out.println("成功将值推送到列表。新列表长度: " + num);

    }
    public void redisFun(){
        // 创建Jedis实例连接本地Redis
        Jedis jedis = new Jedis("localhost", 6379);

        try {
            // 定义要操作的列表名称
            String listKey = "PayGift.3105720000025900001";
            String listKeyInit = "entity.init.PayGift.3105720000025900001";

            // 要推送到列表的值
            String value = "{\"humanId\":3105720000025900001,\"payId\":\"5217a1aa-18da-48a3-a532-ee0eec8251c5\",\"giftCode\":\"1000009\",\"vipPoints\":100,\"amountToDiamond\":0,\"price\":0,\"priceValueRatio\":0,\"duration\":86400,\"purchaseLimitAmount\":1,\"buyNum\":0,\"currency\":\"KRW\",\"currencyPrice\":0.0,\"currencyTag\":\"0\",\"templateId\":\"3\",\"items\":\"{\\\"2\\\":1,\\\"205\\\":1,\\\"206\\\":1,\\\"207\\\":1,\\\"208\\\":1,\\\"209\\\":1,\\\"210\\\":1,\\\"7\\\":1,\\\"9\\\":1,\\\"999\\\":1}\",\"enabled\":false,\"background\":\"\",\"createTime\":1729590982309,\"endTime\":1729677382309,\"id\":3105720000025900001,\"updateDBTime\":1729590982,\"redisExpireTime\":1730195782}";

            // 使用LPUSH命令将值推送到列表的左侧（头部）
            Long result = jedis.lpush(listKey, value);

            System.out.println("成功将值推送到列表。新列表长度: " + result);

            // 为listKeyInit设置值为1
            String setResult = jedis.set(listKeyInit, "1");
            System.out.println("设置 " + listKeyInit + " 的值为1。结果: " + setResult);

            // 可选：获取并打印列表中的所有元素
            System.out.println("列表内容: " + jedis.lrange(listKey, 0, -1));

            // 获取并打印listKeyInit的值
            String initValue = jedis.get(listKeyInit);
            System.out.println(listKeyInit + " 的值: " + initValue);
        } finally {
            // 关闭Jedis连接
            jedis.close();
        }
    }

}
