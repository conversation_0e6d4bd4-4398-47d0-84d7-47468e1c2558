package org.gof.demo.worldsrv.test;

import org.gof.demo.support.TimeUtil;
import org.gof.demo.worldsrv.time.MyTime;
import org.gof.demo.worldsrv.time.TimeInfo;



public class MyTimeTest {

	
	public void testMytime6(){
		long nowTime = System.currentTimeMillis();
		MyTime durationTime = MyTime.newMyTime("6 * *", nowTime);
	}
	
	
	public void testMytime1(){
		String timeStr = "1 2020-4-6 00:00:00,2020-4-8 10:00:00";
		System.out.println("========testTime1-1========="+timeStr);
		long nowTime = System.currentTimeMillis();
		MyTime time1 = MyTime.newMyTime(timeStr, nowTime);
		System.out.println("expect:2017-9-26 00:00:00, actual:"+TimeUtil.formatTime(time1.getBeginTime()));
		System.out.println("expect:2017-9-28 10:00:00, actual:"+TimeUtil.formatTime(time1.getEndTime()));
	}
	
	public void testMytime2(){
		String timeStr = "2 2020-3-4 2020-10-6 1000-1200,1600-1800";
		System.out.println("========testTime2-1========="+timeStr);
		long nowTime = System.currentTimeMillis();
//		MyTime time1 = MyTime.newMyTime(timeStr, nowTime);
		
//		System.out.println("list长度：。。。。。"+time1.getList().size());
//		for(TimeInfo info:time1.getList()) {
//			System.out.println("expect:2017-9-26 00:00:00, actual:"+TimeUtil.formatTime(info.getStartTime()));
//			System.out.println("expect:2017-9-28 10:00:00, actual:"+TimeUtil.formatTime(info.getEndTime()));
//		}
		
		timeStr = "2 * * 0000-2400 ";
		System.out.println("========testTime2-2========="+timeStr);
		MyTime time2 = MyTime.newMyTime(timeStr, nowTime);
//		Assert.assertEquals(12*TimeUtil.HOUR_TIME, time1.getBeginTime());
//		Assert.assertEquals(Long.MAX_VALUE, time1.getEndTime());
//		Calendar cal = Calendar.getInstance();
//		cal.set(Calendar.HOUR_OF_DAY, 12);
//		cal.set(Calendar.MINUTE, 0);
//		time1.update(cal.getTimeInMillis());
//		Assert.assertTrue(time1.isActive());
		System.out.println("list长度：。。。。。"+time2.getList().size());

		for(TimeInfo info:time2.getList()) {
			System.out.println("时间段开始时间："+TimeUtil.formatTime(info.getStartTime()));
			System.out.println("时间段结束时间:"+TimeUtil.formatTime(info.getEndTime()));
		}
		System.out.println("表格配置开始时间:"+TimeUtil.formatTime(time2.getBeginTime()));
		System.out.println("表格配置结束时间:"+TimeUtil.formatTime(time2.getEndTime()));
	}
	
	public void testMytime3(){
		String timeStr = "3 2017-2018 10-11,12 13-14 1000-2000";
		System.out.println("========testTime3-1========="+timeStr);
		long nowTime = System.currentTimeMillis();
		MyTime time3 = MyTime.newMyTime(timeStr, nowTime);
		System.out.println("expect:2017-10-13 10:00:00, actual:"+TimeUtil.formatTime(time3.getBeginTime()));
		System.out.println("expect:2018-12-14 20:00:00, actual:"+TimeUtil.formatTime(time3.getEndTime()));
	}
	
	public void testMytime4(){
		String timeStr = "4 * * * 1,4,6 1000-2400";
		System.out.println("========testTime4-1========="+timeStr);
		long nowTime = System.currentTimeMillis();
		MyTime time3 = MyTime.newMyTime(timeStr, nowTime);
		System.out.println("expect:2017-10-13 10:00:00, actual:"+TimeUtil.formatTime(time3.getBeginTime()));
		System.out.println("expect:2018-12-14 20:00:00, actual:"+TimeUtil.formatTime(time3.getEndTime()));
		System.out.println("时间list的长度："+time3.getList().size());
		for(TimeInfo info:time3.getList()) {
			System.out.println("----------------------------------------------");
			System.out.println("时间段开始时间："+TimeUtil.formatTime(info.getStartTime()));
			System.out.println("时间段结束时间:"+TimeUtil.formatTime(info.getEndTime()));
		}
		TimeInfo timeinfo = time3.getNowTimeInfo();
		System.out.println(timeinfo.getStartTime());
		System.out.println(timeinfo.getEndTime());
		System.out.println("当前状态："+time3.getState().ordinal());
		
		
		
		timeStr = "4 2017 * * 2,4,6 1000-1200";
		System.out.println("========testTime4-2========="+timeStr);
		time3 = MyTime.newMyTime(timeStr, nowTime);
		System.out.println("expect:2017-01-03 10:00:00, actual:"+TimeUtil.formatTime(time3.getBeginTime()));
		System.out.println("expect:2017-12-31 11:59:59, actual:"+TimeUtil.formatTime(time3.getEndTime()));
		for(TimeInfo info:time3.getList()) {
			System.out.println("----------------------------------------------");
			System.out.println("时间段开始时间："+TimeUtil.formatTime(info.getStartTime()));
			System.out.println("时间段结束时间:"+TimeUtil.formatTime(info.getEndTime()));
		}
		
		System.out.println("当前状态："+time3.getState());
		
		
		timeStr = "4 2017-2020 * * 1,2,4 1100-2300";
		System.out.println("========testTime4-2========="+timeStr);
		time3 = MyTime.newMyTime(timeStr, nowTime);
		System.out.println("expect:2017-01-03 10:00:00, actual:"+TimeUtil.formatTime(time3.getBeginTime()));
		System.out.println("expect:2017-12-31 11:59:59, actual:"+TimeUtil.formatTime(time3.getEndTime()));
		for(TimeInfo info:time3.getList()) {
			System.out.println("----------------------------------------------");
			System.out.println("时间段开始时间："+TimeUtil.formatTime(info.getStartTime()));
			System.out.println("时间段结束时间:"+TimeUtil.formatTime(info.getEndTime()));
		}
		System.out.println("当前状态："+time3.getState());
		
		
	}
	
	public static void main(String[] args){
		MyTimeTest test = new MyTimeTest();
//		test.testMytime1();
		test.testMytime2();
//		test.testMytime3();
//		test.testMytime4();
//		Calendar cal = Calendar.getInstance();
//		System.out.println(TimeUtil.formatTime(cal.getTimeInMillis()));
//		cal.set(Calendar.MONTH, 1);
//		cal.set(Calendar.DAY_OF_MONTH, 1);
////		int day = cal.get(Calendar.YEAR);
//		int day = cal.get(Calendar.DAY_OF_MONTH);   
//		System.out.println(TimeUtil.formatTime(cal.getTimeInMillis()));
//		System.out.println(day);
////		int dayMax = cal.getMaximum(Calendar.DAY_OF_MONTH);   
//		int dayMax = cal.getActualMaximum(Calendar.DAY_OF_MONTH);   
//		System.out.println(dayMax);
	}
}
