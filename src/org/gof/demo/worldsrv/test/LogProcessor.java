package org.gof.demo.worldsrv.test;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class LogProcessor {

    // 内部类，用于存储每个玩家的数据
    private static class PlayerData {
        String humanId;
        int mailCount = 0;
        // 存储累加后的物品数据
        Map<String, Integer> aggregatedItems = new HashMap<>();

        public PlayerData(String humanId) {
            this.humanId = humanId;
        }

        // 增加邮件计数
        void incrementMailCount() {
            this.mailCount++;
        }

        // 获取邮件计数
        public int getMailCount() {
            return this.mailCount;
        }

        // 添加物品
        void addItems(Map<String, Integer> newItems) {
            newItems.forEach((key, value) ->
                    // merge方法会处理key已存在和不存在的情况，进行累加
                    this.aggregatedItems.merge(key, value, Integer::sum)
            );
        }

        @Override
        public String toString() {
            // 将物品Map转换为易读的JSON格式字符串
            // 如果map为空，则输出 {}
            String itemsJson = aggregatedItems.isEmpty() ? "{}" :
                    aggregatedItems.entrySet().stream()
                            .map(e -> "\"" + e.getKey() + "\":" + e.getValue())
                            .collect(Collectors.joining(",", "{", "}"));

            return String.format("玩家ID: %s, 邮件总数: %d, 额外物品: %s",
                    this.humanId, this.mailCount, itemsJson);
        }
    }

    public static void main(String[] args) {
        // 定义输入和输出文件路径
        String inputFile = "E:\\Downloads\\Logs-2025-09-22 10_24_34.txt";
        String outputFile = "E:\\Downloads\\mail_statistics_final.txt";

        Map<String, PlayerData> statistics = new HashMap<>();
        Pattern pattern = Pattern.compile("humanId=(\\d+), itemJSON=(\\{.*?\\})");

        // --- 1. 读取和处理日志文件 ---
        try (BufferedReader reader = new BufferedReader(new FileReader(inputFile))) {
            String line;
            int totalLine = 0;
            while ((line = reader.readLine()) != null) {
                Matcher matcher = pattern.matcher(line);
                if (matcher.find()) {
                    String humanId = matcher.group(1);
                    String itemJson = matcher.group(2);
                    Map<String, Integer> currentItems = parseItemJson(itemJson);

                    // 获取或创建该玩家的数据对象
                    PlayerData playerData = statistics.computeIfAbsent(humanId, k -> new PlayerData(k));

                    // 核心逻辑：只累加第二份及以后的邮件物品
                    // 当第一次遇到该玩家时，playerData.getMailCount()为0，此if不执行
                    // 当第二次及以后遇到时，mailCount > 0，执行物品累加
                    if (playerData.getMailCount() > 0) {
                        playerData.addItems(currentItems);
                    }

                    // 无论如何，邮件总数都要加1
                    playerData.incrementMailCount();
                    ++totalLine;
                }
            }
        } catch (IOException e) {
            System.err.println("读取日志文件时出错: " + e.getMessage());
            e.printStackTrace();
            return;
        }

        // --- 2. 转换Map为List，方便排序 ---
        List<PlayerData> playerList = new ArrayList<>(statistics.values());

        // --- 3. 按照邮件总数（条数）从大到小排序 ---
        playerList.sort(Comparator.comparingInt(PlayerData::getMailCount).reversed());

        //打印总共多少行

        // --- 4. 将最终结果写入输出文件 ---
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(outputFile))) {
            writer.write("--- 玩家邮件统计 (按邮件总数降序排列) ---\n");
            int totalNum = 0;
            for (PlayerData data : playerList) {
                totalNum += data.getMailCount();
                writer.write(data.toString() + "\n");
            }
            writer.write("总邮件数: " + totalNum + "\n");
            System.out.println("处理完成！结果已写入到: " + outputFile);
        } catch (IOException e) {
            System.err.println("写入统计文件时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 解析 itemJSON 字符串为 Map<String, Integer>
     */
    private static Map<String, Integer> parseItemJson(String jsonString) {
        Map<String, Integer> items = new HashMap<>();
        String content = jsonString.substring(1, jsonString.length() - 1);
        if (content.isEmpty()) {
            return items;
        }
        String[] pairs = content.split(",");
        for (String pair : pairs) {
            String[] keyValue = pair.split(":");
            String key = keyValue[0].replace("\"", "").trim();
            Integer value = Integer.parseInt(keyValue[1].trim());
            items.put(key, value);
        }
        return items;
    }
}