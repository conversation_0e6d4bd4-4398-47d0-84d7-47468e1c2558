package org.gof.demo.worldsrv.flyPet.hybrid;

import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.interfaces.ISerilizable;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.entity.FlyPet;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 上架飞宠信息
 */
public class FlyPetHybridInfo implements ISerilizable {
    public long id;// 飞宠id
    public long humanId;// 玩家id
    public int sn;// 飞宠sn
    public String name;// 飞宠名称
    public int level;// 飞宠等级
    public int advLev;// 飞宠进阶等级
    public int growth;// 飞宠成长系数
    public int culNum;// 飞宠培育次数
    public int gen;// 飞宠代数
    public List<Integer> entry = new ArrayList<>();// 飞宠天赋
    public long startTime;// 培养开始时间
    public long endTime;// 培养结束时间
    public long nextTime;// 下次可培养时间
    public int apply;// 培养是否需要申请
    public long useId;// 借用飞宠的玩家id
    public Set<Long> partnerIdSet = new HashSet<>();// 可以使用该飞宠的其他玩家
    public Set<Long> askSet = new HashSet<>();// 申请该飞宠的玩家
    public long effectEndTime;// 特权效果结束时间
    public int effectValue;// 冷却时间特权效果

    public FlyPetHybridInfo() {
    }

    public FlyPetHybridInfo(FlyPet pet, boolean isPartner, long effectEndTime, int effectValue) {
        id = pet.getId();
        humanId = pet.getHumanId();
        sn = pet.getSn();
        name = pet.getName();
        level = pet.getLevel();
        advLev = pet.getAdvanceLevel();
        growth = pet.getGrowth();
        gen = pet.getGeneration();
        entry = Utils.strToIntList(pet.getEntries());
        useId = pet.getBorrowHumanId();
        apply = pet.getIsApply();
        askSet = new HashSet<>(Utils.strToLongList(pet.getPermissionApply()));
        if (isPartner) {
            culNum = pet.getBorrowCultivateNum();
            startTime = pet.getBorrowCultivateStartTime();
            endTime = pet.getBorrowCultivateEndTime();
            nextTime = pet.getBorrowCultivateNextTime();
        } else {
            culNum = pet.getCultivateNum();
            startTime = pet.getCultivateStartTime();
            endTime = pet.getCultivateEndTime();
            nextTime = pet.getCultivateNextTime();
        }
        this.effectEndTime = effectEndTime;
        this.effectValue = effectValue;
    }

    @Override
    public void writeTo(OutputStream out) throws IOException {
        out.write(id);
        out.write(humanId);
        out.write(sn);
        out.write(name);
        out.write(level);
        out.write(advLev);
        out.write(growth);
        out.write(culNum);
        out.write(gen);
        out.write(entry);
        out.write(startTime);
        out.write(endTime);
        out.write(nextTime);
        out.write(useId);
        out.write(apply);
        out.write(partnerIdSet);
        out.write(askSet);
        out.write(effectEndTime);
        out.write(effectValue);
    }

    @Override
    public void readFrom(InputStream in) throws IOException {
        id = in.read();
        humanId = in.read();
        sn = in.read();
        name = in.read();
        level = in.read();
        advLev = in.read();
        growth = in.read();
        culNum = in.read();
        gen = in.read();
        entry = in.read();
        startTime = in.read();
        endTime = in.read();
        nextTime = in.read();
        useId = in.read();
        apply = in.read();
        partnerIdSet = in.read();
        askSet = in.read();
        effectEndTime = in.read();
        effectValue = in.read();
    }
}
