package org.gof.demo.worldsrv.flyPet;

import org.gof.core.support.observer.MsgReceiver;
import org.gof.demo.seam.msg.MsgParam;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.msg.MsgFly;

public class FlyPetMsgHandler {

    @MsgReceiver(MsgFly.fly_egg_info_c2s.class)
    public void fly_egg_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgFly.fly_egg_info_c2s msg = param.getMsg();
        FlyPetManager.inst().on_fly_egg_info_c2s(humanObj);
    }

    @MsgReceiver(MsgFly.fly_pet_info_c2s.class)
    public void fly_pet_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgFly.fly_pet_info_c2s msg = param.getMsg();
        FlyPetManager.inst().on_fly_pet_info_c2s(humanObj);
    }

    @MsgReceiver(MsgFly.fly_egg_incubate_c2s.class)
    public void fly_egg_incubate_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgFly.fly_egg_incubate_c2s msg = param.getMsg();
        FlyPetManager.inst().on_fly_egg_incubate_c2s(humanObj, msg.getIdList());
    }

    @MsgReceiver(MsgFly.fly_pet_level_up_c2s.class)
    public void fly_pet_level_up_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgFly.fly_pet_level_up_c2s msg = param.getMsg();
        FlyPetManager.inst().on_fly_pet_level_up_c2s(humanObj, msg.getId(), msg.getCostList());
    }

    @MsgReceiver(MsgFly.fly_pet_advance_c2s.class)
    public void fly_pet_advance_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgFly.fly_pet_advance_c2s msg = param.getMsg();
        FlyPetManager.inst().on_fly_pet_advance_c2s(humanObj, msg.getId());
    }

    @MsgReceiver(MsgFly.fly_pet_fight_c2s.class)
    public void fly_pet_fight_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgFly.fly_pet_fight_c2s msg = param.getMsg();
        FlyPetManager.inst().on_fly_pet_fight_c2s(humanObj, msg.getId());
    }

    @MsgReceiver(MsgFly.fly_pet_reset_c2s.class)
    public void fly_pet_reset_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgFly.fly_pet_reset_c2s msg = param.getMsg();
        FlyPetManager.inst().on_fly_pet_reset_c2s(humanObj, msg.getId(), msg.getGoodsId());
    }

    @MsgReceiver(MsgFly.fly_pet_resolve_c2s.class)
    public void fly_pet_resolve_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgFly.fly_pet_resolve_c2s msg = param.getMsg();
        FlyPetManager.inst().on_fly_pet_resolve_c2s(humanObj, msg.getIdList());
    }

    @MsgReceiver(MsgFly.fly_pet_rename_c2s.class)
    public void fly_pet_rename_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgFly.fly_pet_rename_c2s msg = param.getMsg();
        FlyPetManager.inst().on_fly_pet_rename_c2s(humanObj, msg.getId(), msg.getName());
    }

    @MsgReceiver(MsgFly.fly_hybrid_base_info_c2s.class)
    public void fly_hybrid_base_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgFly.fly_hybrid_base_info_c2s msg = param.getMsg();
        FlyPetManager.inst().on_fly_hybrid_base_info_c2s(humanObj);
    }

    @MsgReceiver(MsgFly.fly_hybrid_shelves_info_c2s.class)
    public void fly_hybrid_shelves_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgFly.fly_hybrid_shelves_info_c2s msg = param.getMsg();
        FlyPetManager.inst().on_fly_hybrid_shelves_info_c2s(humanObj);
    }

    @MsgReceiver(MsgFly.fly_hybrid_set_shelves_info_c2s.class)
    public void fly_hybrid_set_shelves_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgFly.fly_hybrid_set_shelves_info_c2s msg = param.getMsg();
        FlyPetManager.inst().on_fly_hybrid_set_shelves_info_c2s(humanObj, msg.getId(), msg.getType(), msg.getIsSave());
    }

    @MsgReceiver(MsgFly.fly_hybrid_partner_shelves_c2s.class)
    public void fly_hybrid_partner_shelves_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgFly.fly_hybrid_partner_shelves_c2s msg = param.getMsg();
        FlyPetManager.inst().on_fly_hybrid_partner_shelves_c2s(humanObj, msg.getRoleId());
    }

    @MsgReceiver(MsgFly.fly_hybrid_resp_c2s.class)
    public void fly_hybrid_resp_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgFly.fly_hybrid_resp_c2s msg = param.getMsg();
        FlyPetManager.inst().on_fly_hybrid_resp_c2s(humanObj, msg.getTargetId(), msg.getResp(), msg.getType(), msg.getPetId());
    }

    @MsgReceiver(MsgFly.fly_hybrid_kick_c2s.class)
    public void fly_hybrid_kick_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgFly.fly_hybrid_kick_c2s msg = param.getMsg();
        FlyPetManager.inst().on_fly_hybrid_kick_c2s(humanObj, msg.getTargetId());
    }

    @MsgReceiver(MsgFly.fly_hybrid_start_c2s.class)
    public void fly_hybrid_start_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgFly.fly_hybrid_start_c2s msg = param.getMsg();
        FlyPetManager.inst().on_fly_hybrid_start_c2s(humanObj, msg.getBaseId(), msg.getFlyAId(), msg.getFlyBId());
    }

    @MsgReceiver(MsgFly.fly_hybrid_get_c2s.class)
    public void fly_hybrid_get_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgFly.fly_hybrid_get_c2s msg = param.getMsg();
        FlyPetManager.inst().on_fly_hybrid_get_c2s(humanObj, msg.getId());
    }

    @MsgReceiver(MsgFly.fly_hybrid_change_base_name_c2s.class)
    public void fly_hybrid_change_base_name_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgFly.fly_hybrid_change_base_name_c2s msg = param.getMsg();
        FlyPetManager.inst().on_fly_hybrid_change_base_name_c2s(humanObj, msg.getId(), msg.getName());
    }

    @MsgReceiver(MsgFly.fly_hybrid_save_setting_c2s.class)
    public void fly_hybrid_save_setting_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgFly.fly_hybrid_save_setting_c2s msg = param.getMsg();
        FlyPetManager.inst().on_fly_hybrid_save_setting_c2s(humanObj, msg.getSetting());
    }

    @MsgReceiver(MsgFly.fly_hybrid_pet_info_c2s.class)
    public void fly_hybrid_pet_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgFly.fly_hybrid_pet_info_c2s msg = param.getMsg();
        FlyPetManager.inst().on_fly_hybrid_pet_info_c2s(humanObj, msg.getPetId(), msg.getRoleId());
    }

    @MsgReceiver(MsgFly.fly_pet_collection_c2s.class)
    public void fly_pet_collection_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgFly.fly_pet_collection_c2s msg = param.getMsg();
        FlyPetManager.inst().on_fly_pet_collection_c2s(humanObj);
    }

    @MsgReceiver(MsgFly.fly_pet_star_c2s.class)
    public void fly_pet_star_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgFly.fly_pet_star_c2s msg = param.getMsg();
        FlyPetManager.inst().on_fly_pet_star_c2s(humanObj, msg.getTargetId(), msg.getPetId(), msg.getOpt());
    }

    @MsgReceiver(MsgFly.fly_pet_resolve_reward_c2s.class)
    public void fly_pet_resolve_reward_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgFly.fly_pet_resolve_reward_c2s msg = param.getMsg();
        FlyPetManager.inst().on_fly_pet_resolve_reward_c2s(humanObj, msg.getIdList());
    }

    @MsgReceiver(MsgFly.fly_pet_reborn_c2s.class)
    public void fly_pet_reborn_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgFly.fly_pet_reborn_c2s msg = param.getMsg();
        FlyPetManager.inst().on_fly_pet_reborn_c2s(humanObj, msg.getId());
    }
}
