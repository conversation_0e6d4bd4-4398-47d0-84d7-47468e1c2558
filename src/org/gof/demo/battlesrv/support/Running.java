package org.gof.demo.battlesrv.support;

import org.gof.core.support.TickTimer;
import org.gof.core.support.Time;
import org.gof.demo.battlesrv.stageObj.UnitObject;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.support.Log;

import java.util.ArrayList;
import java.util.List;

/**
 * 地图单元移动类
 */
public class Running {
	public final static int INTERVAL = 125;						//两次改变移动最少间隔
	public final static int TIME_ERROR_MAX = 200;			//起点最大误差时间 
	
	private UnitObject obj;									//移动所属对象
	private boolean running;									//是否在移动中
	private long runTimeBegin;									//移动开始时间
	public long runTimePulse;									//最后修改位置的时间
	private final Vector3D runPosBegin = new Vector3D();			//移动开始坐标
	private final Vector3D runPosEnd = new Vector3D();				//移动结束坐标
	private final List<Vector3D> runPaths = new ArrayList<>();	//移动接下来目标
	
	private double speed;				//移动速度
	
	//为了便于运算的暂存值
	public double runTempSin;			//起始至目标的Sin值
	public double runTempCos;			//起始至目标的Cos值
	
	//本次移动开始时间
	private long thisRunTimeBegin = 0L;

	//计时器 每100毫秒计算一次
	private TickTimer timerUpdate = new TickTimer();



	/**
	 * 构造函数
	 * @param worldObject
	 */
	public Running(UnitObject worldObject) {
		this.obj = worldObject;
	}

	/**
	 * 移动
	 * 仅供WorldObj及子类的对应函数move调用
	 * 其余业务逻辑可使用xxObj.move()来改变移动状态
	 * 而不要直接调用本函数
	 * @param from
	 * @param to
	 */
	public void _move(Vector3D from, List<Vector3D> to, double speed) {
		//首个目标点
		Vector3D posEndFirst = to.remove(0);

		// 收到客户端移动请求并开始移动的时间，真正的移动在心跳中计算
//		Log.game.info("running move {}", obj.getTime());
		runTimeBegin = obj.getTime();
	
		runPaths.clear();
		runPaths.addAll(to);
		
		//设置为移动状态
		running = true;
		
		//记录速度
		this.speed = speed;

		//当前位置
		obj.setPosNow(from.toVector2D());

		//设置下一段路径
		setNextPath(from, posEndFirst);

		//为了便于运算的暂存值
		initTempValue();
		
		//重置移动的timer
		timerUpdate.start(obj.getTime(), Running.INTERVAL);
		onHumanStartRun();

	}

	private void onHumanStartRun() {
		if (!this.obj.castSkilling || !this.obj.canMove) {
			//疾跑计时器
			if(obj.isHumanObj() ) {
				HumanObject humanObj = obj.getHumanObj();
				if(thisRunTimeBegin == 0L) {
					thisRunTimeBegin = obj.getTime();
				}

			}
		}
	}

	/**
	 * 停止移动
	 * 仅供WorldObj及子类的对应函数stop调用
	 * 其余业务逻辑可使用xxObj.stop()来改变移动状态
	 * 而不要直接调用本函数
	 */
	public void _stop() {
		//速度置为起始速度
		thisRunTimeBegin = 0L;
		
		if(!running) return;
		
		running = false;
		changeRunPosEnd(runPosBegin);
		runPaths.clear();


//		obj.getUnit().setSpeed(GlobalConfVal.TROT_SPEED*100);
//		this.speed = GlobalConfVal.TROT_SPEED;
	}
	
	public void _pulse(long curr) {
		_pulse(curr, false);
	}
	
	private void changeRunPosEnd(Vector3D posEnd) {
		runPosEnd.setX(posEnd.x);
		runPosEnd.setY(posEnd.y);
	}

	private void initRunPosEnd() {
		if(runPosEnd.x <= 0)
			runPosEnd.x = 1;
		if(runPosEnd.x >= obj.stageObj.width)
			runPosEnd.x = obj.stageObj.width - 1;
		if(runPosEnd.y <= 0)
			runPosEnd.y = 1;
		if(runPosEnd.y >= obj.stageObj.height)
			runPosEnd.y = obj.stageObj.height - 1;
	}
	
	
	/**
	 * 暂时性修改措施
	 */
	public void onSpeedChanged(){
		this.speed = 1.0D * obj.getUnit().getSpeed() / 100;
	}
	
	public double getSpeed() {
		return speed;
	}

	/**
	 * 每心跳移动处理
	 */
	public void _pulse(long curr, boolean force) {
		if(!isRunning())
			return;

		//判断间隔时间
		if(!force && !timerUpdate.isPeriod(curr))
			return;


		//经过时间
		long timeDiff = curr - runTimeBegin;


		//如果从开始跑到当前时间
		/* 更新当前坐标 */
		//移动距离

		double posMoveMax = (timeDiff * speed) / Time.SEC;
		//移动距离对应的横纵偏移量

		double diffX = runTempCos * posMoveMax;
		double diffY = runTempSin * posMoveMax;

//		Log.game.info("runTimeBegin {}, timeDiff {} x {} y {} {} {}", runTimeBegin, timeDiff, runPosEnd.x, runPosEnd.y, diffX, diffY);

		//实际移动距离
		double trueX = runPosBegin.x + diffX;
		double trueY = runPosBegin.y + diffY;

		long overDiffTime = (int)(Vector2D.distance(trueX, trueY, runPosEnd.x, runPosEnd.y) * Time.SEC / speed);
		//计算拐点超时的时间
		if (overDiffTime < 0) {
			overDiffTime = 0;
		}

		//验证一下 不能超过目标点
		//理论上不会出现这种情况 检查一下放心
		if(runPosEnd.x >= runPosBegin.x && runPosEnd.x < trueX) {
			trueX = runPosEnd.x;
		} else if(runPosEnd.x < runPosBegin.x && runPosEnd.x > trueX) {
			trueX = runPosEnd.x;
		}
		if(runPosEnd.y >= runPosBegin.y && runPosEnd.y < trueY) {
			trueY = runPosEnd.y;
		} else if(runPosEnd.y < runPosBegin.y && runPosEnd.y > trueY) {
			trueY = runPosEnd.y;
		}

		//是否离开安全区通知
		if(obj.isHumanObj()){
			notifyAreaSwitch(trueX, trueY);
		}

		Vector3D trueVector = new Vector3D(trueX, trueY, runPosEnd.z);

		//设置对象的当前坐标
		obj.setPosNow(trueVector.toVector2D());
		runTimePulse = curr;

		if(!runPosEnd.equals(trueVector))
			return;
		
		//没有后续目标点
		if(runPaths.isEmpty()) {
			//停止移动
			running = false;

		} else{
			//设置下一段路径
			setNextPath(runPosEnd, runPaths.remove(0));
			runTimeBegin = curr - overDiffTime;
			
			//为了便于运算的暂存值
			initTempValue();
			
			//记录日志
			if(Log.stageMove.isInfoEnabled() && obj instanceof HumanObject) {
				Log.stageMove.info("角色{}到达当前目标{}，下次目标{}，接下来的目标为{}。", obj.name, runPosBegin.getPosStr(), runPosEnd.getPosStr(), runPaths);
			}
		}
	}

	private void notifyAreaSwitch(double trueX, double trueY) {
		Vector2D start = new Vector2D(runPosBegin.x, runPosBegin.y);
		Vector2D end = new Vector2D(trueX, trueY);
		HumanObject humanObject = obj.getHumanObj();
		boolean startInSafe = this.obj.stageObj.inSafeArea(start);
		boolean endInSafe = this.obj.stageObj.inSafeArea(end);
	}
	
	/**
	 * 获取当前的移动路径
	 * @return
	 */
	public List<Vector3D> getRunPath() {
		List<Vector3D> result = new ArrayList<>();
		result.add(runPosEnd);
		result.addAll(runPaths);
		return result;
	}
	
	/**
	 * 初始化暂存值 优化路径计算过程
	 */
	private void initTempValue() {
		//修正终点坐标
		initRunPosEnd();

		if(runPosEnd.x == runPosBegin.x && runPosEnd.y == runPosBegin.y) {
			_stop();
			return;
		}
		
		//起始至目标横纵偏移量
		double diffX = runPosEnd.x - runPosBegin.x;
		double diffY = runPosEnd.y - runPosBegin.y;
		
		//实际距离
		double distance = Math.sqrt(Math.pow(diffX, 2) + Math.pow(diffY, 2));
		if(distance == 0) {
			runTempSin = 0;
			runTempCos = 0;
			return;
		}

		//起始至目标的Sin,Cos值
		runTempSin = diffY / distance;
		runTempCos = diffX / distance;
	}
	
	/**
	 * 设置下一段路径，如果下一个目标点不能直接到达，那么取起点和终点直线上从起点能到达的最远的点
	 * @param posBegin
	 * @param posEnd
	 */
	public void setNextPath(Vector3D posBegin, Vector3D posEnd) {
//		Vector3D endReal = new Vector3D(posEnd.x, posEnd.y, posEnd.z);
//		if(obj instanceof HumanObject) {
//			if(PathFinding.isPosInBlock(obj.stageObj.sn, posBegin)) {
//				endReal = posBegin;
//			}
//
//			endReal = PathFinding.raycast(obj.stageObj.sn, posBegin, posEnd, obj.stageObj.pathFindingFlag);
//			if(!endReal.equals(posEnd)) {
//				Log.temp.debug("两个路点之间不可以直接到达，进行坐标修正！起点为{}，终点为{}，修正后终点为{}", posBegin, posEnd, endReal);
//			}
//		}
		
//		runPosBegin.set(posBegin);
//		changeRunPosEnd(endReal);
//		obj.setDirNow(posEnd.toVector2D());

	}

	public boolean isRunning() {
		return running;
	}

	public void setRunning(boolean isRunning) {
		running = isRunning;
	}

	public boolean isTimeExpired() {
		long curr = obj.getTime();
		//如果更改移动的时间过短（能容忍最低时间间隔为125ms）
//		Log.stageMove.info("curr - runTimePulse: {}", curr - runTimePulse);
		if(running && curr - runTimePulse <= INTERVAL) return false;
		
		return true;
	}
	
	/**
	 * 修正移动起始点
	 * @param from
	 * @return
	 */
	public Vector3D correctPosFrom(Vector3D from) {
		Vector3D result = new Vector3D();
		//如果起点在阻挡内，或者起点跟终点之间走不通，则将起点强制设置为当前点
//		StageObject stageObj = obj.stageObj;
//		Vector3D posNow3D = StageManager.getHeight(stageObj.sn, obj.getPosNow());
//		boolean isBlock = PathHelper.isPosInBlock(obj, from);
//		if(isBlock) {
//			result.set(posNow3D);
//			return result;
//		}
//		//没在阻挡中，误差在一定范围内，那修正到新给定的点
//		double distanceMax = distanceErrorMax();
//		if(from.distance(posNow3D) <= distanceMax) {
//			result.set(from);
//			return result;
//		}
//		//没在阻挡中，且误差超过范围，那就在允许范围内尽量靠近给定的起点
//		double diffX = from.x - obj.getPosNow().x;
//		double diffY = from.y - obj.getPosNow().y;
//
//		//实际距离
//		double diffTrue = Math.sqrt(Math.pow(diffX, 2) + Math.pow(diffY, 2));
//		if(diffTrue == 0) {
//			result.set(from);
//			return result;
//		}
//
//		double fixX = diffX * distanceMax / diffTrue;
//		double fixY = diffY * distanceMax / diffTrue;
//
//		//500ms给一次补偿
//		if(obj.getTime() - runTimePulse < 500) {
//			fixX = 0;
//			fixY = 0;
//		}
//
//		result.x = obj.getPosNow().x + fixX;
//		result.y = obj.getPosNow().y + fixY;
//		result.z = from.z;
//
//		boolean isBlock2 = PathHelper.isPosInBlock(obj, from);
//		if(isBlock2) {
//			result.x = obj.getPosNow().x;
//			result.y = obj.getPosNow().y;
//			result.z = from.z;
//		}
//
		return result;
	}
	
	public double distanceErrorMax() {
		double posMoveMax = ((TIME_ERROR_MAX / 2) * speed) / Time.SEC;
		return posMoveMax;

	}

	public long getThisRunTimeBegin() {
		return thisRunTimeBegin;
	}

	public void clearRunPath() {
		runPaths.clear();
	}
	
}
