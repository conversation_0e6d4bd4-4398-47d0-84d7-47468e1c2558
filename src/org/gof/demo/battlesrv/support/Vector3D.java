package org.gof.demo.battlesrv.support;

import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.interfaces.ISerilizable;

import java.io.IOException;

/**
 * 坐标
 */
public class Vector3D implements ISerilizable {
	public double x;			//X坐标
	public double y;			//Y坐标
	public double z;			//Z坐标
	
	public Vector3D() { }
	
	
	public void setX(double x) {
		this.x = x;
	}


	public void setY(double y) {
		this.y = y;
	}


	public void setZ(double z) {
		this.z = z;
	}


	/**
	 * 构造函数
	 * @param x
	 * @param y
	 */
	public Vector3D(double x, double y, double z) {
		this.x = x;
		this.y = y;
		this.z = z;
	}
	

	/**
	 * 转换为三维float型数组
	 */
	public float[] toFloat3() {
		return new float[]{(float)x, (float)y, (float)z};
	}
	
	public Vector2D toVector2D() {
		return new Vector2D(x, y);
	}
	
	/**
	 * 导航网格的
	 * xyz 转到 detour 的格式
	 * @return
	 */
	public float[] toDetourFloat3() {
//		return new float[]{(float)x, (float)z, (float)y};
		//原来的
		return new float[]{(float)y, (float)z, (float)x};
	}
	
	public float[] toDetourFloat4() {
		return new float[]{(float)x, (float)y, (float)z};
	}
	
	public Vector3D toServFromDetour() {
//		return new Vector3D(x,z,y);
		//原来的
		return new Vector3D(z, x, y);


	}
	/**
	 * 设置坐标值
	 * @param vector
	 */
	public void set(Vector3D vector) {
		this.x = vector.x;
		this.y = vector.y;
		this.z = vector.z;
	}
	
	public Vector3D sub(Vector3D vector) {
		this.x -= vector.x;
		this.y -= vector.y;
		this.z -= vector.z;
		return this;
	}
	
	public Vector3D sum(Vector3D vector) {
		this.x += vector.x;
		this.y += vector.y;
		this.z += vector.z;
		return this;
	}
	
	public Vector3D div(double a) {
		Vector3D result = new Vector3D();
		result.x = this.x / a;
		result.y = this.y / a;
		result.z = this.z / a;
		return result;
	}
	
	/**
	 * 获取易读的坐标字符串
	 * @return
	 */
	public String getPosStr() {
		return new StringBuilder("(").append(x).append(",").append(y).append(",").append(z).append(")").toString();
	}
	
	/**
	 * 两点之间的距离
	 * @param pos
	 * @return
	 */
	public double distance(Vector3D pos) {
		double t1x = this.x;
		double t1y = this.y;
		double t2x = pos.x;
		double t2y = pos.y;
		
		return Math.sqrt(Math.pow((t1x -t2x), 2) + Math.pow((t1y - t2y) , 2));
	}

	public double distanceSquared(Vector3D pos) {
		if(pos == null) return 0;

		double t1x = this.x;
		double t1y = this.y;

		double t2x = pos.x;
		double t2y = pos.y;

		return Math.pow((t1x - t2x), 2) + Math.pow((t1y - t2y) , 2);
	}
	
	/**
	 * 两点之间的距离参考值，不开根号
	 * @param pos
	 * @return
	 */
	public double distanceFar(Vector3D pos) {
		double t1x = this.x;
		double t1y = this.y;
		double t2x = pos.x;
		double t2y = pos.y;
		
		return Math.pow((t1x -t2x), 2) + Math.pow((t1y - t2y) , 2);
	}
	
	/**
	 * 两点之间的距离,带高度
	 * @param pos
	 * @return
	 */
	public double distance3D(Vector3D pos) {
		double t1x = this.x;
		double t1y = this.y;
		double t1z = this.z;
		double t2x = pos.x;
		double t2y = pos.y;
		double t2z = pos.z;
		
		return Math.sqrt(Math.pow((t1x -t2x), 2) + Math.pow((t1y - t2y) , 2) + Math.pow((t1z - t2z) , 2));
	}
	
	public double Dot(Vector3D vector) {
		return this.x * vector.x + this.y * vector.y + this.z * vector.z;
	}
	@Override
	public void writeTo(OutputStream out) throws IOException {
		out.write(x);
		out.write(y);
		out.write(z);
	}
	
	@Override
	public void readFrom(InputStream in) throws IOException {
		x = in.read();
		y = in.read();
		z = in.read();
	}
	
	@Override
	public boolean equals(Object other) {
		if (this == other) {
			return true;
		}
		
		if(!(other instanceof Vector3D)) {
			return false;
		}
		
		Vector3D castOther = (Vector3D) other;
		return new EqualsBuilder().append(this.x, castOther.x).append(this.y, castOther.y).append(this.z, castOther.z).isEquals();
	}
	
	@Override
	public int hashCode() {
		return new HashCodeBuilder().append(x).append(y).append(z).toHashCode();
	}

	@Override
	public String toString() {
		return new StringBuilder().append("[").append(x).append(",").append(y).append(",").append(z).append("]").toString();
	}

	public float getX() {
		return (float)x;
	}


	public float getY() {
		return (float)y;
	}

	public void setZ(Integer height) {
		this.z = height;
	}
	
	/**
	 * 是否零点
	 * 
	 * @return
	 */
	public boolean isZero() {
		boolean ret = false;
		if (getX() == 0 && getY() == 0 && getZ() == 0) {
			ret = true;
		}
		return ret;
	}
	
	public float getZ() {
		return (float)this.z;
	}

	public double flatDistanceSq(Vector3D other) {
		if (this.equals(other)) {
			return 0;
		}
		float t1x = getX();
		float t1z = getZ();
		float t2x = other.getX();
		float t2z = other.getZ();

		return Math.pow((t1x - t2x), 2) + Math.pow((t1z - t2z), 2);
	}

}
