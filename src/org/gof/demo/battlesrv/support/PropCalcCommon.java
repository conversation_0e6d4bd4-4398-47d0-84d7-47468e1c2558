package org.gof.demo.battlesrv.support;

import java.math.BigDecimal;


/**
 * 通用属性累加，不仅适用于prop，而且适用于propExt
 */
public class PropCalcCommon extends PropCalcBase<String, BigDecimal> {
	public PropCalcCommon() {
	}
	
	public PropCalcCommon(String json) {
		super(json);
	}

	@Override
	protected String toKey(String key) {
		return key;
	}

	@Override
	protected BigDecimal toValue(Object value) {
		return new BigDecimal(value.toString());
	}

	@Override
	protected boolean canDiscard(Object value) {
//		double temp = toValue(value);
//		if(temp < 1e-06 && temp > -1e-06) {
//			return true;
//		}
		return false;
	}


	
}
