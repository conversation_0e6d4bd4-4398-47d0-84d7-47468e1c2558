package org.gof.demo.battlesrv.stageObj;

import org.gof.demo.worldsrv.stage.StageCell;
import org.gof.demo.worldsrv.stage.StageObject;
import org.gof.demo.worldsrv.stage.StagePort;

import java.util.Comparator;

/**
 * 处理玩家 武将 怪物的公共属性 方法。 但是不包括战斗， 使得战斗可以独立
 * <AUTHOR>
 *
 */
public abstract class CharacterObject extends UnitObject  implements Comparator<CharacterObject> {
	public CharacterObject(StageObject stageObj) {
		super(stageObj);
	}
	
	public StagePort getPort() {
		return stageObj.getPort();
	}
	
	@Override
	public void pulseMove(long timeCurr) {
		super.pulseMove(timeCurr);
		
		StageCell cellBegin = stageCell;
		if(getPosNow() == null){
			return;
		}
		if(stageObj == null){
			return;
		}
		StageCell cellEnd = stageObj.getCell(getPosNow());
		if (cellBegin == null || cellEnd == null) {
			return;
		}

		stageCell = cellEnd;
		// 判断玩家有没有跨地图格了
		if (cellBegin != null && !cellEnd.equals(cellBegin)) { // 跨地图格了
//			StageManager.inst().cellChanged(cellBegin, cellEnd, this);
		}
	}
	
	@Override
	public int compare(CharacterObject u1, CharacterObject u2) {
		if(u2 == null || u1 == null)
			return 0;
		if(u1.profession < u2.profession)
			return -1;
		else if(u1.profession > u2.profession)
			return 1;
		else
			return u1.order - u2.order;
	}

	public int getRage() { return  ragePart.getRage(); }
}
