package org.gof.demo.battlesrv.stageObj.area;

import org.gof.demo.battlesrv.stageObj.UnitObject;
import org.gof.demo.battlesrv.support.Vector2D;
import org.gof.demo.worldsrv.stage.StageObject;

/**
 * 地图区域
 */
public abstract class AreaObject {
	protected StageObject stageObj; // 所属地图

	// 区域类型（目前暂时只处理安全区）
	protected int type;

	// 区域地形类型
	protected int shapeType;

	// 所属阵营(默认为全部,0)
	protected int camp;
	
	// 所属帮派(默认为全部,0)
	protected int faction;

	private int toMapSn;
	
	private String modelSn;
	
	/**
	 * 对应配置
	 */
//	private ConfSceneCharacter conf;
	
	/**
	 * 中心点
	 */
	protected Vector2D center;

	public AreaObject(){}
	public AreaObject(StageObject stageObj, int type, int shapeType, int camp,
			Vector2D center) {
		super();
		
		this.stageObj = stageObj;
		this.type = type;
		this.shapeType = shapeType;
		this.camp = camp;
		this.center = center;
	}

	public StageObject getStageObj() {
		return stageObj;
	}

	public int getType() {
		return type;
	}

	public int getShapeType() {
		return shapeType;
	}

	public Vector2D getCenter() {
		return center;
	}

	public int getCamp() {
		return camp;
	}

	public void setFaction(int faction) {
		this.faction = faction;
	}

	public int getFaction() {
		return faction;
	}

	public abstract boolean isInArea(UnitObject obj);
//
	public abstract boolean isVectInArea(Vector2D vector2D);

//	public ConfSceneCharacter getConf() {
//		return conf;
//	}
//
//	public void setConf(ConfSceneCharacter conf) {
//		this.conf = conf;
//	}

	public int getToMapSn() {
		return toMapSn;
	}

	public void setToMapSn(int toMapSn) {
		this.toMapSn = toMapSn;
	}

	public String getModelSn() {
		return modelSn;
	}

	public void setModelSn(String modelSn) {
		this.modelSn = modelSn;
	}

	
	
	
}
