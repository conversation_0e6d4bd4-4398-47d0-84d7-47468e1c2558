package org.gof.demo.battlesrv.stageObj;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.Port;
import org.gof.core.PortPulseQueue;
import org.gof.core.interfaces.ISerilizable;
import org.gof.core.support.TickTimer;
import org.gof.demo.battlesrv.support.Vector2D;
import org.gof.demo.support.statistics.StatisticsStage;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.stage.StageCell;
import org.gof.demo.worldsrv.stage.StageObject;
import org.gof.demo.worldsrv.support.Log;

import java.io.IOException;

/**
 * 地图单元基类
 */
public abstract class WorldObject implements ISerilizable {
	public StageObject stageObj;			//所属地图
	public StageCell stageCell;				//所属地图格
	
	public long id;							//ID
	public String name = "";				//name
	public String modelSn = "";				//模型Sn

	private boolean inWorld = false;				//是否在地图上显示 本属性不应该被Distr同步
	public boolean bridge = false;							//是否是跨服状态（跨服时，地图上隐藏，不接受其他广播消息）

	private Vector2D posNow = new Vector2D();	//坐标

	private Vector2D dirNow = new Vector2D();	//方向

	public WorldObject fireObj; //制造这个对象的源头
	private long timeCreate; 		//产生时候的时间
	private long timeCurr; 	//当前的时候  使用timeCrete +  pulse 的时间
	private int deltaTime; //变化的时间

	protected boolean initShow = true;
	
	protected TickTimer showTimer = null;
	protected TickTimer hiddenTimer = null; 
	
//	protected long[] rec = new long[10];
	
	public WorldObject(StageObject stageObj) {
		this.stageObj = stageObj;
	}
	
	public WorldObject() {
	}


	@Override
	public void writeTo(OutputStream out) throws IOException {
		out.write(id);
		out.write(name);
		out.write(modelSn);
		out.write(posNow);
	}

	@Override
	public void readFrom(InputStream in) throws IOException {
		id = in.read();
		name = in.read();
		modelSn = in.read();
		posNow = in.read();
	}

	@Override
	public String toString() {
		return new ToStringBuilder(this).append("id", id).append("name", name).toString();
	}

	public boolean isInWorld() {
		return inWorld;
	}

	public void setInWorld(boolean inWorld) {
		this.inWorld = inWorld;
	}

	public void pulse(int deltaTime) {
		timeCurr += deltaTime;
		this.deltaTime = deltaTime;
	}
	
	public long getTime() {
		return timeCurr;
	}

	public void startup() {
		this.timeCreate = Port.getTime();
		this.timeCurr = this.timeCreate;
	}

	public void stageRegister(StageObject stageObj) {
		
		//在地图中添加活动单元
		stageObj._addWorldObj(this);
		
		this.timeCreate = stageObj.getTime();
		this.timeCurr = this.timeCreate;

		onRegister();
		stageObj.onRegister(this);

		//记录地图上新增对象
		StatisticsStage.enter(stageObj.sn, this);
	}

	public void onRegister() {
	}


	/**
	 * 在地图显示
	 */
	public void stageShow() {
		//已在地图中的 忽略
		if(inWorld) {
			Log.stageCommon.error("使活动单元进入地图时发现inWorld状态为true：data={}", this);
			return;
		}
		
		//设置状态为在地图中
		inWorld = true;

		int type = 1;
		//通知其他玩家 有地图单元进入视野
	}
	
	/**
	 * 在地图显示： 复活
	 */
	public void stageShowRevive() {
		//已在地图中的 忽略
		if(inWorld) {
			Log.stageCommon.warn("使活动单元进入地图时发现inWorld状态为true：data={}", this);
			return;
		}
	}
	
	/**
	 * 进入地图
	 * @param stageObj
	 */
	public void stageEnter(StageObject stageObj) {
		stageObj.getPort().addQueue(new PortPulseQueue("stageObj", stageObj, "worldObj", this) {
			@Override
			public void execute(Port port) {
				WorldObject worldObj = param.get("worldObj");
				StageObject stageObj = param.get("stageObj");
				
				//加入地图并显示，人类不重复加入
				if(!(worldObj instanceof HumanObject))
					worldObj.stageRegister(stageObj);
				
				if(worldObj.initShow) //初始化显示
					worldObj.stageShow();
			}
		});
	}
	
	public final void stageLeave() {
		//设置状态
		inWorld = false;
		
		//将具体删除操作排入队列 在心跳的最后在进行删除
		//因为本心跳中可能还有后续操作需要本对象的实例
		if(stageObj == null) {
			return;
		}
		stageObj.getPort().addQueue(new PortPulseQueue("stageObj", stageObj, "worldObj", this) {
			public void execute(Port port) {
				StageObject stageObj = param.get("stageObj");
				WorldObject worldObj = param.get("worldObj");
				
				stageObj._delWorldObj(worldObj);

				if(worldObj instanceof HumanObject){
					// 玩家退出直接删除伙伴
				}
			}
		});
	}
	
	/**
	 * 从地图隐藏
	 */
	public void stageHide() {
		//设置状态
		inWorld = false;
		
		//通知其他玩家 有地图单元离开视野
	}
	

	protected TickTimer getNextHiddenTimer(){
		return null;
	}
	
	protected TickTimer getNextShowTimer(){
		return null;
	}

	public void setInitShow(boolean initShow) {
		this.initShow = initShow;
	}

	public void setHiddenTimer(long interval) {
		hiddenTimer = new TickTimer(interval);
	}

	public Vector2D getPosNow() {
		return posNow;
	}

	Vector2D lastPos = new Vector2D();

	public void setPosNow(Vector2D posNow) {
		this.posNow = posNow;
		lastPos = posNow;
	}
	
	public void setPosDir(Vector2D pos, Vector2D dir) {
		if (dir != null) {
			this.dirNow = dir;
		}
		this.posNow = pos;
		lastPos = pos;
	}

	public Vector2D getDirNow() {
		return dirNow;
	}

	public void setDirNow(Vector2D dir) {
		this.dirNow = dir;
	}

	public void finallyPulse(int deltaTime){
		timeCurr += deltaTime;
		this.deltaTime = deltaTime;
	}

	public Vector2D getLastPos(){
		return lastPos;
	}

}
