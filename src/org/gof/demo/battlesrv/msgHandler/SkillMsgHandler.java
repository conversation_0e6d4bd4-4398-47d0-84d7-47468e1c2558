package org.gof.demo.battlesrv.msgHandler;

import org.gof.core.support.observer.MsgReceiver;
import org.gof.demo.seam.msg.MsgParam;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.msg.MsgSkill;

public class SkillMsgHandler {

    @MsgReceiver(MsgSkill.skill_list_c2s.class)
    public void _msg_skill_list_c2s(MsgParam param){
        HumanObject humanObj = param.getHumanObject();
        SkillManager.inst()._msg_skill_list_c2s(humanObj);
    }

    @MsgReceiver(MsgSkill.skill_system_info_c2s.class)
    public void _msg_skill_system_info_c2s(MsgParam param){
        HumanObject humanObj = param.getHumanObject();
        SkillManager.inst()._msg_skill_system_info_c2s(humanObj);
    }

    @MsgReceiver(MsgSkill.skill_lv_c2s.class)
    public void _msg_skill_lv_c2s(MsgParam param){
        HumanObject humanObj = param.getHumanObject();
        MsgSkill.skill_lv_c2s msg = param.getMsg();
        SkillManager.inst()._msg_skill_lv_c2s(humanObj, msg.getSkillIdsList());
    }

    @MsgReceiver(MsgSkill.skill_equip_c2s.class)
    public void _msg_skill_equip_c2s(MsgParam param){
        HumanObject humanObj = param.getHumanObject();
        MsgSkill.skill_equip_c2s msg = param.getMsg();
        SkillManager.inst()._msg_skill_equip_c2s(humanObj, msg.getPosInfoList());
    }

    @MsgReceiver(MsgSkill.skill_tab_info_c2s.class)
    public void _msg_skill_tab_info_c2s(MsgParam param){
        HumanObject humanObj = param.getHumanObject();
        SkillManager.inst()._msg_skill_tab_info_c2s(humanObj);
    }

    @MsgReceiver(MsgSkill.skill_choose_tab_c2s.class)
    public void _msg_skill_choose_tab_c2s(MsgParam param){
        HumanObject humanObj = param.getHumanObject();
        MsgSkill.skill_choose_tab_c2s msg = param.getMsg();
        SkillManager.inst()._msg_skill_choose_tab_c2s(humanObj, msg.getTab());
    }

    @MsgReceiver(MsgSkill.skill_change_tab_name_c2s.class)
    public void _msg_skill_change_tab_name_c2s(MsgParam param){
        HumanObject humanObj = param.getHumanObject();
        MsgSkill.skill_change_tab_name_c2s msg = param.getMsg();
        SkillManager.inst()._msg_skill_change_tab_name_c2s(humanObj, msg.getTab(), msg.getName());
    }

    @MsgReceiver(MsgSkill.skill_set_delay_time_c2s.class)
    public void _msg_skill_set_delay_time_c2s(MsgParam param){
        HumanObject humanObj = param.getHumanObject();
        MsgSkill.skill_set_delay_time_c2s msg = param.getMsg();
        SkillManager.inst()._msg_skill_set_delay_time_c2s(humanObj, msg.getTab(), msg.getSetDelayTimeList());
    }
}
