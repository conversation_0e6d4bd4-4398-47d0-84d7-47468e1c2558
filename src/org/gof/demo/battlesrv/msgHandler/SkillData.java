package org.gof.demo.battlesrv.msgHandler;

import org.apache.commons.collections.map.HashedMap;
import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.interfaces.ISerilizable;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.ConfActivityControl;
import org.gof.demo.worldsrv.config.ConfIllustrated_0;
import org.gof.demo.worldsrv.entity.Human;
import org.gof.demo.worldsrv.entity.Human2;
import org.gof.demo.worldsrv.entity.Human3;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.support.Log;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SkillData implements ISerilizable {
    public final int delayPosMax = 6;
    //技能Sn, 等级
    public Map<Integer, Integer> skillSnLvMap = new HashedMap();
    //技能方案组, 技能集合
    public Map<Integer, List<Integer>> lineupMap = new HashedMap();
    public Map<Integer,Map<Integer, Long>> lineupDelayMap = new HashMap<>();


    //技能图鉴Sn, 图鉴等级
    public Map<Integer, Integer> skillIllustratedSnLvMap = new HashedMap();


    public SkillData() { }

    //登录的时候 解析数据
    public void initSkillData(Human2 human, Human3 human3){
        skillSnLvMap = Utils.jsonToMapIntInt(human.getSkillSnLvMap());
        skillIllustratedSnLvMap = Utils.jsonToMapIntInt(human3.getSkillIllustratedMap());
        for(int sn : GlobalConfVal.getInitSkillIllustratedMap().keySet()){
            if(!skillIllustratedSnLvMap.containsKey(sn)){
                skillIllustratedSnLvMap.put(sn, 0);
            } else{
                int lv = skillIllustratedSnLvMap.get(sn);
                ConfIllustrated_0 conf = ConfIllustrated_0.get(sn, lv);
                if(conf == null){
                    lv --;
                    conf = ConfIllustrated_0.get(sn, lv);
                    if(conf != null){
                        skillIllustratedSnLvMap.put(sn, lv);
                    }
                }
            }
        }

        initLineupDelayMap(human.getSkillDelayJSON());
        initSkillLineup(human.getSkillLineupJSON());
    }

    public void initSkillDataOffline(Human2 human){
        skillSnLvMap = Utils.jsonToMapIntInt(human.getSkillSnLvMap());
        initLineupDelayMap(human.getSkillDelayJSON());
        initSkillLineup(human.getSkillLineupJSON());
    }
    private void initLineupDelayMap(String json){
        lineupDelayMap = Utils.jsonToIntegerMapIntLong(json);
        for(int i = 1; i <= SkillManager.lineupMax; i++){
            if(lineupDelayMap.containsKey(i)){
                continue;
            }
            Map<Integer, Long> posTimeMap = new HashMap<>();
            for(int m = 1; m <= delayPosMax; m++){
                posTimeMap.put(m, 0L);
            }
            lineupDelayMap.put(i, posTimeMap);
        }
    }

    private void initSkillLineup(String json) {
        lineupMap = Utils.jsonToMapIntListInt(json);

        for(int i = 1; i <= SkillManager.lineupMax; i++){
            if(lineupMap.containsKey(i)){
                continue;
            }
            List<Integer> snList = new ArrayList<>();
            for(int m = 0; m < SkillManager.posMax; m++){
                snList.add(0);
            }
            lineupMap.put(i, snList);
        }
    }

    public void saveData(HumanObject humanObj){
        Human2 human = humanObj.getHuman2();
        Human3 human3 = humanObj.getHuman3();
        human.setSkillLineupJSON(Utils.mapIntListIntToJSON(lineupMap));
        human.setSkillSnLvMap(Utils.mapIntIntToJSON(skillSnLvMap));
        human3.setSkillIllustratedMap(Utils.mapIntIntToJSON(skillIllustratedSnLvMap));
        human.setSkillDelayJSON(Utils.mapIntMapIntLongToJSON(lineupDelayMap));
        human.update();
        human3.update();
    }




    @Override
    public void writeTo(OutputStream out) throws IOException {
        out.write(skillSnLvMap);
        out.write(lineupMap);
        out.write(skillIllustratedSnLvMap);
        out.write(lineupDelayMap);
    }

    @Override
    public void readFrom(InputStream in) throws IOException {
        skillSnLvMap.clear();
        skillSnLvMap = in.read();
        lineupMap.clear();
        lineupMap.putAll(in.<Map<Integer, List<Integer>>>read());
        skillIllustratedSnLvMap.clear();
        skillIllustratedSnLvMap = in.read();
        lineupDelayMap.clear();
        lineupDelayMap.putAll(in.<Map<Integer, Map<Integer, Long>>>read());
    }
}
