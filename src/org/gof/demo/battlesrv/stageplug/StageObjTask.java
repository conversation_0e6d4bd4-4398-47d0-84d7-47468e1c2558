package org.gof.demo.battlesrv.stageplug;

public class StageObjTask implements  IStageRunnable{

    private IStageRunnable target;
    private long runtime;
    public boolean executed;
    /**
     * 在一个指定的时间，执行这个任务
     * @param target
     * @param runtime
     */
    public StageObjTask(IStageRunnable target,long runtime){
        this.target = target;
        this.runtime = runtime;
        executed = false;
    }

    @Override
    public void run() {
        if(target != null){
            target.run();
            executed = true;
        }
    }

    /**
     * 时间是否到达
     * @param ts
     * @return
     */
    public boolean isPeriod(long ts){
        return ts >= runtime;
    }

    /**
     * 是否执行过
     * @return
     */
    public boolean isExecuted(){
        return executed;
    }

}
