package org.gof.demo.battlesrv.stageplug;


import java.util.HashMap;
import java.util.Map;

public class StageObjectBase {
    Map<String,StageObjTask> tasks = new HashMap<>();

    public void  pulse(long timestamp){
        for(StageObjTask task : tasks.values()){
            if(!task.isPeriod(timestamp) || task.isExecuted()){
               continue;
            }
            task.run();
        }
    }

    public void test2(){
    }

    private void test(long ts){
        StageObjTask task = new StageObjTask(new IStageRunnable() {
            @Override
            public void run() {

            }
        },ts );

        tasks.put("demo",task);
    }

}
