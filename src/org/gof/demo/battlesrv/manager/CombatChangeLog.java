package org.gof.demo.battlesrv.manager;

/**
 * @Author: yyc
 * @Description: 战力变化Log
 * @Date: 2022/2/9 10:46
 */
public enum CombatChangeLog {
    未设置(0),
    同伴(1),
    技能(2),
    装备(3),
    遗物(4),
    神器(6),
    坐骑(7),
    翅膀(8),
    武魂(9),
    雕像(11),
    科技(12),
    头衔任务(13),
    特权(14),
    停车改装升级(15),
    停车皮肤升级(16),
    职业觉醒(17),
    建筑(18),
    图鉴(19),
    飞宠(20),
    星将(21),
    翅膀天赋(22),
    美观值(23),
    钓鱼(24),
    钓鱼装备(25),
    天使(26),
    天使阵位(27),
    ;
    /** 日志类型值StringDataSn **/
    private int type;

    CombatChangeLog(int type) {
        this.type = type;
    }

    public int getType() {
        return type;
    }

}
