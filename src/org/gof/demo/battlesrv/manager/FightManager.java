package org.gof.demo.battlesrv.manager;

import org.gof.core.support.ManagerBase;


public class FightManager extends ManagerBase {
	/**
	 * 获取实例
	 * @return
	 */
	public static FightManager inst() {
		return inst(FightManager.class);
	}

	public static float PERINT = 10000F;

	/**计算伤害公式
	 * @param unitObjAtk
	 * @param unitObjDef
	 * @param atkType 攻击类型 1是魔法攻击
	 * @return
	 */
//	public int calcBaseHurt(UnitObject unitObjAtk, UnitObject unitObjDef, int atkType) {
//		Unit ua = unitObjAtk.getUnit();
//		Unit ud = unitObjDef.getUnit();
//
//		int result = 0;
//
//		float[] param = ConfGlobalUtils.getFloatArray(ConfGlobalKey.战斗公式系数);
//		if(param==null || param.length!=4){
//			param = new float[]{1.0f,1.0f,1.0f,1.0f};
//		}
//
//		ConfPropElement confPropElement = ConfPropElement.get(unitObjAtk.getProfession());
//		if(unitObjAtk.isMonsterObj()){
//			confPropElement = ConfPropElement.get(0);
//		}
//
//		if(confPropElement == null){
//			Log.temp.error("===找不到对应职业的表格数据，ConfPropElement not find sn={}", unitObjAtk.getProfession());
//			confPropElement = ConfPropElement.get(0);
//		}
//
//		//魔法
//		int dmgMag;
//
//		if(isPvp(unitObjAtk, unitObjDef)){
//			if(ua.getAtkMag() > 0.8 * ud.getDefMag()){
//				dmgMag = (int) (ua.getAtkMag() -  0.4 * ud.getDefMag());
//			}else{
//				long atkMag = (long) ua.getAtkMag() * (long) ua.getAtkMag();
//				dmgMag = (int)(atkMag / ud.getDefMag() * 0.625);
//			}
//			dmgMag *= 0.625;
//			dmgMag = Math.max(dmgMag, 0);
//		}else {
//			if(ua.getAtkMag() > 2 * ud.getDefMag()){
//				dmgMag = ua.getAtkMag() - ud.getDefMag();
//			}else{
//				long atkMag = (long) ua.getAtkMag() * (long) ua.getAtkMag();
//				dmgMag = (int)(atkMag / ud.getDefMag() * 0.25);
//			}
//			dmgMag = Math.max(dmgMag, 0);
//		}
//
//		//物理
//		int dmgPhy;
//
//		if(isPvp(unitObjAtk, unitObjDef)){
//			if(ua.getAtkPhy() > 0.8 * ud.getDefPhy()){
//				dmgPhy = (int) (ua.getAtkPhy() -  0.4 * ud.getDefPhy());
//			}else{
//				long atkPhy = (long) ua.getAtkPhy() * (long) ua.getAtkPhy();
//				dmgPhy = (int)(atkPhy / ud.getDefPhy() * 0.625);
//			}
//			dmgPhy *= 0.625;
//			dmgPhy = Math.max(dmgPhy, 0);
//		}else {
//			if(ua.getAtkPhy() > 2 * ud.getDefPhy()){
//				dmgPhy = ua.getAtkPhy() -  ud.getDefPhy();
//			}else{
//				long atkPhy = (long) ua.getAtkPhy() * (long) ua.getAtkPhy();
//				dmgPhy = (int)(atkPhy / ud.getDefPhy() * 0.25);
//			}
//
//			dmgPhy = Math.max(dmgPhy, 0);
//		}
//
//		//计算魔法攻击伤害
//		if(atkType == 0){
//			int dmgElm1 = ua.getAtkElm1() - ud.getDefElm1();
//			dmgElm1 = Math.max(dmgElm1, confPropElement.elm1Min);
//			dmgElm1 = Math.min(dmgElm1, confPropElement.elm1Max);
//
//			int dmgElm2 = ua.getAtkElm2() - ud.getDefElm2() ;
//			dmgElm2 = Math.max(dmgElm2, confPropElement.elm2Min);
//			dmgElm2 = Math.min(dmgElm2, confPropElement.elm2Max);
//
//			int dmgElm3 = ua.getAtkElm3() - ud.getDefElm3() ;
//			dmgElm3 = Math.max(dmgElm3, confPropElement.elm3Min);
//			dmgElm3 = Math.min(dmgElm3, confPropElement.elm3Max);
//
//			int dmgElm4 = ua.getAtkElm4() - ud.getDefElm4() ;
//			dmgElm4 = Math.max(dmgElm4, confPropElement.elm4Min);
//			dmgElm4 = Math.min(dmgElm4, confPropElement.elm4Max);
//
//			int dmgElm5 = ua.getAtkElm5() - ud.getDefElm5() ;
//			dmgElm5 = Math.max(dmgElm5, confPropElement.elm5Min);
//			dmgElm5 = Math.min(dmgElm5, confPropElement.elm5Max);
//
//			int dmgElm6 = ua.getAtkElm6() - ud.getDefElm6() ;
//			dmgElm6 = Math.max(dmgElm6, confPropElement.elm6Min);
//			dmgElm6 = Math.min(dmgElm6, confPropElement.elm6Max);
//
//			// 避免溢出
//			result = (int)((dmgPhy + dmgMag) * (long)(10000 + dmgElm1 + dmgElm2 + dmgElm3 + dmgElm4 + dmgElm5 + dmgElm6) / 10000);
//		}else if(atkType == 1) {
//			result = dmgMag;
//		} else if(atkType == 2){
//			result = dmgPhy;
//		}
//		if(result < 1)
//			return 1;
//		else
//			return result;
//	}
//
//	/**
//	 * 计算元素伤害
//	 * @param unitObjAtk
//	 * @param unitObjDef
//	 * @param element	类型 0 是普通 1,2,3,4 是分别的元素
//	 * @return
//	 */
//	public int calcElemHurt(UnitObject unitObjAtk, UnitObject unitObjDef, int element) {
//		int result = 0;
//		return result;
//	}
//
//	/**
//	 * 判断pvp
//	 * @param atk
//	 * @param def
//	 * @return
//	 */
//	public boolean isPvp(UnitObject atk, UnitObject def){
//		if(atk.isHumanObj() && def.isHumanObj()){
//			return true;
//		}
//		if(atk.isHumanObjMirror() && def.isHumanObjMirror()){
//			return true;
//		}
//		if(atk.isHumanObj() && def.isHumanObjMirror()){
//			return true;
//		}
//		if(atk.isHumanObjMirror() && def.isHumanObj()){
//			return true;
//		}
//
//		return false;
//	}
//
//	/**
//	 * 计算命中公式
//	 *
//	 * @param uoAtk
//	 * @param uoDef
//	 * @return
//	 */
//	public boolean isHit(UnitObject uoAtk, UnitObject uoDef) {
////
//		int hit = getCommanPropCal(uoAtk, uoDef, PropDamageKey.hit);
//
//		//如果：Random（1，10000）小于等于最终命中值，则为技能命中。
//		int hitRoll = RandomUtils.nextInt(10000);
//		if(hitRoll > hit)
//			return false;
//		else
//			return true;
//
//	}
//
//	/**
//	 * 是否是暴击
//	 * @param uoAtk
//	 * @param uoDef
//	 * @param atkType
//	 * @return
//	 */
//	public boolean isCrit(UnitObject uoAtk, UnitObject uoDef, int atkType) {
//		int crit = getCommanPropCal(uoAtk, uoDef,PropDamageKey.crit);
//
//		//如果：Random（1，10000）小于等于最终暴击值，则为技能暴击。
//		int critRoll = RandomUtils.nextInt(10000);
//		if(critRoll > crit)
//			return false;
//		else
//			return true;
//
//	}
//
//
//	/**
//	 * 是否是格挡
//	 * @param uoAtk
//	 * @param uoDef
//	 * @param atkType
//	 * @return
//	 */
//	public boolean isBlock(UnitObject uoAtk, UnitObject uoDef, int atkType) {
//
//		int blockFinal = getCommanPropCal(uoAtk, uoDef,PropDamageKey.block);
//
//		//如果：Random（1，10000）小于等于最终格挡值，则为技能格挡。
//		int blockRoll = RandomUtils.nextInt(10000);
//		if(blockRoll > blockFinal)
//			return false;
//		else
//			return true;
//
//	}
//
//	/**
//	 * 在攻击者和防守者中 依赖额定值和范围值，确定最终伤害
//	 * @return
//	 */
//	public int getCommanPropCal(UnitObject uoAtk, UnitObject uoDef,PropDamageKey key) {
//		Unit ua = uoAtk.getUnit();
//		Unit ud = uoDef.getUnit();
//
//		int uaSoul = 0;
//		int udSoul = 0;
//		if(uoAtk instanceof HumanObject){
//			uaSoul = uoAtk.getHumanObj().getHuman().getSoul();
//		}
//		if(uoDef instanceof HumanObject){
//			udSoul = uoDef.getHumanObj().getHuman().getSoul();
//		}
//
//		ConfPropSecond uaConfSecond = ConfPropSecond.get(uaSoul);
//		ConfPropSecond udConfSecond = ConfPropSecond.get(udSoul);
//
//		if(uaConfSecond == null || udConfSecond == null){
//			Log.game.info("找不到配置表 sn ConfPropSecond sn={}",key.name());
//			return 0;
//		}
//
//		if(key == PropDamageKey.hit){
//			key = PropDamageKey.dodge;
//		}
//		ConfPropDamage conf = ConfPropDamage.get(key.name());
//		if(conf == null) {
//			Log.game.info("找不到配置表 sn ConfPropDamage sn={}",key.name());
//			return 0;
//		}
//		double fixValue = conf.fixValue;
//		double minValue = conf.minValue;
//		double maxValue = conf.maxValue;
//
//		//需求：攻击方命中值 = 10000 - 防守方闪避值
//		if(key == PropDamageKey.hit){
//
//		}else if(key == PropDamageKey.crit){
//			minValue = uaConfSecond.critMin;
//			maxValue = uaConfSecond.critMax;
//		}else if(key == PropDamageKey.block){
//			minValue = udConfSecond.blockMin;
//			maxValue = udConfSecond.blockMax;
//		}else if(key == PropDamageKey.dodge){
//			minValue = udConfSecond.dodgeMin;
//			maxValue = udConfSecond.dodgeMax;
//		}
//
//		double atkValue = 0;
//		double defValue = 0;
//		switch (key) {
//		case hit:
//			atkValue = ua.getHit() * uaConfSecond.hitCoeff;
//			defValue = ud.getDodge() * udConfSecond.dodgeCoeff;
//			break;
//		case crit:
//			atkValue = ua.getCrit() * uaConfSecond.critCoeff;
//			defValue = ud.getTough() * udConfSecond.toughCoeff;
//			break;
//		case block:
//			atkValue = ud.getBlock() * udConfSecond.blockCoeff;
//			defValue = ua.getCrack() * uaConfSecond.crackCoeff;
//			break;
//		case critDmg:
//			atkValue = ua.getCritAdd();
//			defValue = ud.getCritDec();
//			break;
//		case blockDmg:
//			atkValue = ua.getBlockAdd();
//			defValue = ud.getBlockDec();
//			break;
//		case suck:
//			atkValue = ua.getSuck();
//			defValue = 0;
//			break;
//		case dodge:
//			atkValue = ud.getDodge() * udConfSecond.dodgeCoeff;
//			defValue = ua.getHit() * uaConfSecond.hitCoeff;
//			break;
//		default:
//			break;
//		}
//		//格挡值修正值=己方总格挡-目标总破击
//		double valueFix = atkValue - defValue;
//		//最终格挡值=额定格挡+格挡修正值
//		double finalValue = fixValue + valueFix;
//		//判断最终格挡值是否大于5：若是，最终格挡值=最终格挡值；否则，最终格挡值=额定格挡值。
//		double returnValue = 0D;
//
//		if(finalValue > maxValue) {
//			returnValue = maxValue;
//		}else if(finalValue < minValue) {
//			returnValue = minValue;
//		}else {
//			returnValue = finalValue;
//		}
//
//		//需求：攻击方命中值 = 10000 - 防守方闪避值
//		if(key == PropDamageKey.dodge){
//			returnValue = 10000 - returnValue;
//		}
//
//		if(uoAtk.isHumanObj()) {
////			Log.fight.error("getComman PropCal========start==================");
//
////			Log.fight.error("getCommanPropCal type={} return={}",key.name(),returnValue);
//
//
////			Log.fight.error("getCommanPropCal=========end=================");
//		}
//
//		return (int)returnValue;
//	}
//
//	/**
//	 * 判断是否吸血
//	 * @param uoAtk
//	 * @return
//	 */
//	public boolean isSuck(UnitObject uoAtk, UnitObject uoDef, int atkType) {
//		int suck = getCommanPropCal(uoAtk, uoDef,PropDamageKey.suck);
//
//		//如果：Random（1，10000）小于等于最终命中值，则为技能命中。
//		int hitRoll = RandomUtils.nextInt(10000);
//		if(hitRoll > suck)
//			return false;
//		else
//			return true;
//	}
//
//	/**
//	 * 计算吸血量
//	 * @param uoAtk
//	 * @param hoLost
//	 * @return
//	 */
//	public double bloodSuck(UnitObject uoAtk, int hoLost) {
//		Unit ua = uoAtk.getUnit();
//
//		double suckRatio = hoLost * ua.getSuckRatio() / 10000D;
//
//		return suckRatio;
//
//	}
//
//	/**
//	 * 获取免伤剩余比例，就是有效伤害比例
//	 * @param uoDef
//	 * @return
//	 */
//	public double avoidPer(UnitObject uoDef) {
////		Unit ud = uoDef.getUnit();
////
////		double result = 1.0;
////		result -= ud.getAvoidAtk() / 10000D;
////
////		return result;
//
//		return 1;
//	}
//
//	/**
//	 * 计算最终需要消耗的蓝
//	 * @param uoAtk
//	 * @param costMp
//	 */
//	public int calcCostMp(UnitObject uoAtk, int costMp) {
//		//如果消耗为0，直接返回
//		return 0;
//	}
//
//	/**
//	 * 属性覆盖
//	 * @param unitObj
//	 */
//	public void recoverProp(UnitObject unitObj) {
//
//	}
//
//	/**
//	 * 战斗剧情添加武将接口
//	 * @param humanObj
//	 * @param genSn
//	 */
//	public void onCSSkillAddGeneral(HumanObject humanObj, String genSn, Vector2D posBirth) {
//
//		int index = 0;
//		MonsterObject monsterObj = new MonsterObject(humanObj.stageObj, 0, genSn, true, index, 0);
//		monsterObj.posBegin = posBirth;
//		monsterObj.parentObjectId = humanObj.id;
//		monsterObj.startup();
//	}
//
//	/**战斗剧情删除武将
//	 * @param humanObj
//	 * @param id
//	 */
//	public void onCSSkillRemoveGeneral(HumanObject humanObj, long id) {
//		UnitObject unitObj = humanObj.stageObj.getUnitObj(id);
//		unitObj.stageLeave();
//	}
//
//	/**
//	 * 设置战术指令
//	 * @param humanObject
//	 * @param instruction
//	 */
//	public void setTacticsInstructions(HumanObject humanObject,long monsterId, int instruction){
//		if(!(humanObject.stageObj instanceof StageObjectInstance)){
//			return;
//		}
//		MsgFight.SCSetTacticsInstructions.Builder msg = MsgFight.SCSetTacticsInstructions.newBuilder();
//		msg.setMonsterId(monsterId);
//		msg.setInstruction(instruction);
//		StageManager.inst().sendMsgToStage(msg, humanObject.stageObj);
//	}
//
//
//	public void notifyPartnerSkillFailed(HumanObject humanObj, long casterId, int skillSn) {
//		MsgFight.SCFightAtkResult.Builder msg = MsgFight.SCFightAtkResult.newBuilder();
//		msg.setResultCode(-1);
//		msg.setSendId(casterId);
//		msg.setSkillSn(skillSn);
//		humanObj.sendMsg(msg);
//	}
//
//	private boolean canPartnerCastSkill(HumanObject humanObj, ConfSkill confSkill, PartnerObject partnerObj) {
//		int skillSn = confSkill.sn;
//		PartnerFighter fighter = (PartnerFighter)partnerObj.aiFighter;
//		if (!fighter.hasRageSkill(skillSn)) {
//			Log.game.debug("partner has no rage skill {}", skillSn);
//			return false;
//		}
//
//		int rage = partnerObj.getRage();
//		if (rage < confSkill.costRage) {
//			Log.game.debug("partner rage {} < {}", rage, confSkill.costRage);
//			return false;
//		}
//
//		SkillCommon skill = partnerObj.getSkillBook().getSkill(skillSn);
//		if (skill == null) {
//			Log.game.debug("partner {} no rage skill {}", partnerObj.id, skillSn);
//			return false;
//		}
//
//		if (fighter.getEnmityManager().isEmpty()) {
//			Log.game.debug("partner {} no in combat state, skill {}", partnerObj.id, skillSn);
//			return false;
//		}
//
//		ISpellCommandManager spellMgr = fighter.getSpellCommandManager();
//		ISpellCommand spellCmd = spellMgr.getCommand();
//		if (spellCmd == null) {
//			return false;
//		}
//
//		UnitObject target = spellCmd.getTarget();
//		if (target == null || target.isDie()) {
//			Log.game.debug("partner {} no target to cast", partnerObj.id);
//			return false;
//		}
//
//		double distance = partnerObj.getPosNow().distance(target.getPosNow());
//		double skillDistance = confSkill.range;
//		if (target.confModel != null && distance > target.confModel.collisionRadius) {
//			distance -= target.confModel.collisionRadius;
//		}
//		if (distance > skillDistance) {
//			Log.game.debug("too far to cast distance {} skill {} from partner {}", distance, skillDistance, partnerObj.id);
//			return false;
//		}
//
//		return true;
//
//	}
//	public void handlePartnerCastSkill(HumanObject humanObj, long partnerId, int skillSn) {
//		ConfSkill confSkill = ConfSkill.get(skillSn);
//		if (confSkill == null) {
//			return;
//		}
//
//		PartnerObject partnerObj = humanObj.partnerData.partnerObjMap.get(partnerId);
//		if (partnerObj == null) {
//			return;
//		}
//
//		SkillCommon skill = partnerObj.getSkillBook().getSkill(skillSn);
//		if (skill == null) {
//			Log.game.debug("partner {} no rage skill {}", partnerId, skillSn);
//			return;
//		}
//
//		if (partnerObj.aiFighter == null) {
//			return;
//		}
//
//		boolean canCast = canPartnerCastSkill(humanObj, confSkill, partnerObj);
//		if (!canCast) {
//			notifyPartnerSkillFailed(humanObj, partnerObj.id, skillSn);
//			return;
//		}
//
//		skill.isPartnerRageSkill = true;
//		partnerObj.getAiFighter().getSkillContainer().appendTriggerSkill(skillSn);
//	}
}