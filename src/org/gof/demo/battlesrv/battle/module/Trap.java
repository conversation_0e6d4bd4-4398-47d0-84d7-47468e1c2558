package org.gof.demo.battlesrv.battle.module;

import org.gof.core.support.Utils;
import org.gof.demo.battlesrv.battle.BattleMain;
import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.enumVo.AttribDefine;
import org.gof.demo.battlesrv.battle.module.buff.BuffTrap;
import org.gof.demo.battlesrv.battle.module.skill.SkillRunner;
import org.gof.demo.battlesrv.battle.enumVo.BattleFlag;
import org.gof.demo.battlesrv.battle.enumVo.TargetFilter;
import org.gof.demo.battlesrv.battle.enumVo.TargetSelectFilter;
import org.gof.demo.battlesrv.support.Vector2D;
import org.gof.demo.worldsrv.config.ConfEffect;
import org.gof.demo.worldsrv.config.ConfTrap;
import org.gof.demo.worldsrv.support.Log;

import java.util.ArrayList;
import java.util.List;

public class Trap {
    public static final int Type_One = 0;
    public static final int Type_RepeatOne = 1;
    public static final int Type_Repeat = 2;

    public SkillRunner runner;
    protected ConfTrap trapConfig;
    protected Vector2D pos;
    protected float curTime = 0;
    protected float curTotalTime = 0;
    protected int state = -1;
    protected float skillPar = 0;
    public boolean isStop;
    protected List<Integer> triggerList = new ArrayList<>();
    protected float maxTime = 0;

    public Trap() {

    }

    public static void trigger(Trap t, Vector2D i) {
//        Log.battle.info("Trap trigger {}", t.trapConfig.sn);
        SkillRunner s = t.runner;
        ConfTrap r = t.trapConfig;
        List<UnitHuman> a = s.getTargets(TargetFilter.fromValue(r.target[0]), r.range, i, r.target[1], TargetSelectFilter.fromValue(r.target[2]));
        if (!a.isEmpty()) {
            List<Integer> c = t.triggerList;
            List<Integer> o = new ArrayList<>();
            for (int j = 0; j < a.size(); j++){
                UnitHuman h = a.get(j);
                if (r.type == Type_RepeatOne) {
                    o.add(h.unitId);
                    if (c.contains(h.unitId))
                        continue;
                    c.add(h.unitId);
                }
                String[] p = r.buffId.split("\\|");
                for (String g : p) {
                    String[] d = g.split("\\,");
                    s.addBuff(h, Utils.intValue(d[0]), s.cast().data.getSkillFactAttrValue(Utils.floatValue(d[1]), s.useSkill().config.sn, AttribDefine.active_skillbuff_time.getValue()), t.skillPar);
                }
            }

            if (r.type == Type_RepeatOne) {
                List<Integer> removeList = new ArrayList<>();
                for (int T = 0; T < c.size(); T++) {
                    if (!o.contains(c.get(T))) {
                        removeList.add(c.get(T));
                    }
                }
                o.removeAll(removeList);
            }
            o.clear();
        }
    }

    public static void trigger(BuffTrap t, Vector2D i) {
//        Log.battle.info("BuffTrap trigger {}", t.trapConfig.sn);
        SkillRunner s = t.runner;
        ConfTrap r = t.trapConfig;
        List<UnitHuman> a = s.getTargets(TargetFilter.fromValue(r.target[0]), r.range, i, r.target[1], TargetSelectFilter.fromValue(r.target[2]));
        if (!a.isEmpty()) {
            List<Integer> c = t.triggerList;
            List<Integer> o = new ArrayList<>();
            for (UnitHuman h : a) {
                if (r.type == (int) Type_RepeatOne) {
                    o.add(h.unitId);
                    if (c.contains(h.unitId))
                        continue;
                    c.add(h.unitId);
                }
                String[] p = r.buffId.split("\\|");
                for (String g : p) {
                    String[] d = g.split("\\,");
                    s.addBuff(h, Utils.intValue(d[0]), s.cast().data.getSkillFactAttrValue(Utils.floatValue(d[1]), s.useSkill().config.sn, AttribDefine.active_skillbuff_time.getValue()), t.skillPar);
                }
            }

            if (r.type == (int) Type_RepeatOne) {
                List<Integer> removeList = new ArrayList<>();
                for (int T = 0; T < c.size(); T++) {
                    if (!o.contains(c.get(T))) {
                        removeList.add(c.get(T));
                    }
                }
                o.removeAll(removeList);
            }
            o.clear();
        }
    }

    private void _triggerAction() {
        ConfTrap e = this.trapConfig;
        BattleMain i = this.runner.battleMain;
        switch (e.type) {
            case Type_One:
                this.isStop = true;
                if ((i.battleFlag & (int) BattleFlag.OPEN_GRAPHIC.getValue()) == 0)
                    break;
                if ((i.battleFlag & (int) BattleFlag.UI_MASK.getValue()) > 0)
                    break;
                if (e.teffectId > 0) {
                    ConfEffect s = ConfEffect.get(e.teffectId);
                    if (s != null && s.is_ban > 0 && (i.battleFlag & (int) BattleFlag.SIMP_MODEL.getValue()) > 0)
                        break;
                }
                break;
            case Type_Repeat:
                this.state = 1;
                this.curTime = 0;
                this.maxTime = 0;
                if (0 == e.execute.length)
                    this.isStop = true;
                else
                    this.maxTime = this.runner.cast().data.getSkillFactAttrValue(e.execute[1], this.runner.useSkill().config.sn, AttribDefine.active_skillbuff_time.getValue());
                break;
        }
        trigger(this, this.pos);
    }

    public void onUpdate(float t) {
        if (this.isStop)
            return;
        if (-1 == this.state) {
            this.state = 0;
        }
        ConfTrap i = this.trapConfig;
        BattleMain s = this.runner.battleMain;
        if (0 == this.state) {
            if (!s.unitMgr.findTarget(this.runner.cast(), (TargetFilter.fromValue(i.triggerTarget)), i.triggerRange, this.pos, true).isEmpty())
                this._triggerAction();
            if (this.curTime >= i.duration)
                this.isStop = true;
        } else {
            if (this.curTime >= i.execute[0]) {
                trigger(this, this.pos);
                this.curTime = this.curTime - i.execute[0];
            }
            float r = 0 != this.maxTime ? this.maxTime : i.execute[1];
            if (this.curTotalTime >= r)
                this.isStop = true;
            this.curTotalTime = this.curTotalTime + t;
        }
        this.curTime = this.curTime + t;
    }

    public static Trap alloc(ConfTrap t, Vector2D e, float i) {
        Trap s = new Trap();
        s.trapConfig = t;
        s.pos = e;
        s.skillPar = i;
        return s;
    }
}
