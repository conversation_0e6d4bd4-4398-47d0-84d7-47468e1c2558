package org.gof.demo.battlesrv.battle.module;

import org.gof.demo.battlesrv.support.Vector2D;

/**
 * @program: game2
 * @description:
 * @author: Mr.wugz
 * @create: 2024-05-28 12:17
 **/
public class TargetPoint {
    public int type;
    public int priority;
    public Vector2D nextPos;

    // 常量定义，使用static final修饰
    public static final int T_Position = 1;
    public static final int TP_Move = 0;
    public static final int TP_Control = 1;
    public static final int TP_Forced = 1;

    // Getter方法（Java中没有直接的属性访问，需要使用Getter和Setter）
    public int getType() {
        return type;
    }

    public int getPriority() {
        return priority;
    }

    public Vector2D getNextPos() {
        return nextPos;
    }

    public TargetPoint() {

    }

    public static TargetPoint toPos(Vector2D pos, int priority) {//priority = 0
        return new TargetPoint(pos, priority, 0);
    }

    public TargetPoint(Vector2D pos, int priority, int type) {
        this.type = type;
        this.priority = priority;
        this.nextPos = pos;
    }

    public static void free(TargetPoint value) {
    }

}


