package org.gof.demo.battlesrv.battle.module;

import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.module.skill.Skill;

/**
 * @program: game2
 * @description:
 * @author: Mr.wugz
 * @create: 2024-05-28 12:17
 **/
public class TargetSkill {
    private int type;
    private int priority;
    private Skill skill;
    private UnitHuman target;

    public UnitHuman getTarget() {
        return target;
    }

    // 常量定义，使用static final修饰
    public static final int TP_AutoFind = 0;
    public static final int TP_ManualPoint = 1;
    public static final int TP_Forced = 2;

    // Getter方法（Java中没有直接的属性访问，需要使用Getter和Setter）
    public int getType() {
        return type;
    }

    public int getPriority() {
        return priority;
    }

    public Skill getSkill() {
        return skill;
    }

    public TargetSkill() {

    }

    public static TargetSkill toSkill(Skill skill, int priority) {// priority = TP_AutoFind
        return new TargetSkill(skill, priority, 0);
    }

    public TargetSkill(Skill skill, int priority, int type) {//priority = TP_AutoFind
        this.type = type;
        this.priority = priority;
        this.skill = skill;
    }

    public static void free(TargetSkill curSkill) {
    }

}


