package org.gof.demo.battlesrv.battle.module;

import org.gof.demo.battlesrv.battle.module.aI.*;
import org.gof.demo.battlesrv.battle.module.buff.*;
import org.gof.demo.battlesrv.battle.module.skill.SkillHandler.*;
import org.gof.demo.battlesrv.battle.module.skill.SkillRunner;
import org.gof.demo.worldsrv.config.ConfBuff;
import org.gof.demo.worldsrv.support.Log;

public class ClassFactory {

    public static IAIHandler aiMap(String aiConfig)
    {
        switch (aiConfig)
        {
            case "common":
                return new CommonAI();
            case "player":
                return new PlayerAI();
//            case "tfmonster":
//                return new TFMonsterAI();
//            case "tfplayer":
//                return new TFPlayerAI();
            case "t20player":
                return new T20PlayerAI();
            case "boss":
                return new BossAI();
            case "t20boss":
                return new T20BossAI();
            case "dlplayer":
                return new DLPlayerAI();
            case "shamplayer":
                return new ShamPlayerAI();
            case "flypet":
                return new FlyPetAI();
            case "dpvpplayer":
                return new DPVPPlayerAI();
        }
        Log.temp.error("====战斗出错，获取AIHandler失败 {}", aiConfig);
        return new CommonAI();
    }

    public static SkillHandleBase skillMap(String action, SkillRunner runner) {
        switch (action) {
            case "normal":
                return new SkillHandleNormal(runner);
            case "counter":
                return new SkillHandleCounter(runner);
            case "effect":
                return new SkillHandleEffect(runner);
            case "passive":
                return new SkillHandlePassiv(runner);
            case "boss_effect":
                return new SkillHandleBossEffect(runner);
            case "thief_normal":
                return new SkillHandleThiefNormal(runner);
            case "passive_artiact":
                return new SkillHandlePassive1(runner);
            case "passive_randomskill":
                return new SkillHandleRandomSkill(runner);
        }
        return new SkillHandleNormal(runner);
    }

    public static Buff buffMap(String action, ConfBuff conf) {
        switch (action) {
            case "dizz":
                return BuffDizz.alloc(conf);
            case "attrib":
                return BuffAttrib.alloc(conf);
            case "attrib_continue":
                return BuffAttribContinue.alloc(conf);
            case "addbuff_to_target":
                return BuffAddBuffToTarget.alloc(conf);
            case "state_triger":
                return BuffStateTrigger.alloc(conf);
            case "addbuff_trigger":
                return BuffAddBuffTrigger.alloc(conf);
            case "sharedamage":
                return BuffShareDamage.alloc(conf);
            case "triger_by_uType":
                return BuffTrigerByUType.alloc(conf);
            case "statetrigger":
                return BuffStateTrigger.alloc(conf);
            case "dotdamage":
                return BuffDotDamage.alloc(conf);
            case "delaydamage":
                return BuffDelayDamage.alloc(conf);
            case "ban_skill":
                return BuffBanSkill.alloc(conf);
            case "bound":
                return BuffBound.alloc(conf);
            case "invincible":
                return BuffInvincible.alloc(conf);
            case "throw_hit":
                return BuffThrowHit.alloc(conf);
            case "skill_effect":
                return BuffSkillEffect.alloc(conf);
            case "skill_value":
                return BuffSkillValue.alloc(conf);
            case "trap":
                return BuffTrap.alloc(conf);
            case "ban_act":
                return BuffBanAct.alloc(conf);
            case "clear":
                return BuffClear.alloc(conf);
            case "not_controll":
                return BuffNotControlled.alloc(conf);
            case "shield":
                return BuffShield.alloc(conf);
            case "attack_add":
                return BuffAttackAdd.alloc(conf);
            case "double_hit_num":
                return BuffSkillDoubleHitNum.alloc(conf);
            case "call_unit":
                return BuffCallUnit.alloc(conf);
            case "skill_damage_add":
                return BuffSkillDamageAdd.alloc(conf);
            case "attrib_condition":
                return BuffAttribCondition.alloc(conf);
            case "recover_power":
                return BuffRecoverPower.alloc(conf);
            case "direct_kill":
                return BuffDirectKill.alloc(conf);
            case "hpchange_trigger":
                return BuffHpChangeTrigger.alloc(conf);
            case "not_get_damage":
                return BuffNotGetDamage.alloc(conf);
            case "ignore_buff":
                return BuffIgnoreIds.alloc(conf);
            case "unit_call_damage_add":
                return BuffUnitCallDamageAdd.alloc(conf);
            case "break_shield":
                return BuffBreakShield.alloc(conf);
            case "double_trigger":
                return BuffDoubleTrigger.alloc(conf);
            case "reset_cd":
                return BuffResetCd.alloc(conf);
            case "pause_cd":
                return BuffPauseCd.alloc(conf);
            case "total_damage_trigger":
                return BuffTotalDamageTrigger.alloc(conf);
            case "fragile_effect":
                return BuffSkillFragileAdd.alloc(conf);
            case "attrib_convert":
                return BuffAttribConvert.alloc(conf);
            case "use_skill_add":
                return BuffUseSkillAdd.alloc(conf);
            case "hp_hurt":
                return BuffSkillHpHurt.alloc(conf);
            case "trigger_bullet":
                return BuffTriggerBullet.alloc(conf);
            case "skill_counter":
                return BuffSkillCounter.alloc(conf);
            case "immune_death":
                return BuffImmuneDeath.alloc(conf);
            case "random_buff":
                return BuffRandomBuff.alloc(conf);
            case "taunt":
                return BuffTaunt.alloc(conf);
            case "bleed":
                return BuffBleed.alloc(conf);
            case "block":
                return BuffBlock.alloc(conf);
            case "hp_trigger":
                return BuffHpTrigger.alloc(conf);
            case "check":
                return BuffCheck.alloc(conf);
            case "skill_real_damage":
                return BuffSkillRealDamage.alloc(conf);
            case "remake":
                return BuffRemake.alloc(conf);
            case "sound":
                return BuffSound.alloc(conf);
            case "skill_return":
                return BuffSkillReturn.alloc(conf);
            //case "double_skill":
            //    return BuffDoubleSkill.alloc(conf);
            case "frozen":
                return BuffFrozen.alloc(conf);
            //case "action":
            //    return BuffAction.alloc(conf);
            //case "current_hp":
            //    return BuffCurrentHp.alloc(conf);
            //case "skill_bufftime_add":
            //    return BuffSkillBuffTimeAdd.alloc(conf);
            //case "copy_unit":
            //    return BuffCopyUnit.alloc(conf);
            //case "ignore_copy":
            //    return BuffCopyIgnore.alloc(conf);
            case "vampire":
                return BuffVampire.alloc(conf);
            //case "giantslayer":
            //    return BuffGiantSlayer.alloc(conf);
            case "pet_convert":
                return BuffPetConvert.alloc(conf);
            case "speed_random_buff":
                return BuffSpeedRandom.alloc(conf);
            case "speed_trigger":
                return BuffSpeedTrigger.alloc(conf);
            case "defer_damage":
                return BuffDeferDamage.alloc(conf);
            case "hp_alter":
                return BuffHpAlter.alloc(conf);
        }
        return new Buff();
    }
}
