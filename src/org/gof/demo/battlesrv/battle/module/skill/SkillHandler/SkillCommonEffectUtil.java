package org.gof.demo.battlesrv.battle.module.skill.SkillHandler;

import org.gof.demo.battlesrv.battle.BattleParamKey;
import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.HurtUtil;
import org.gof.demo.battlesrv.battle.module.buff.Buff;
import org.gof.demo.battlesrv.battle.module.Bullet;
import org.gof.demo.battlesrv.battle.module.skill.SkillRunner;
import org.gof.demo.battlesrv.battle.enumVo.*;
import org.gof.demo.battlesrv.support.Vector2D;
import org.gof.demo.worldsrv.support.Log;

import java.util.Arrays;
import java.util.List;

/**
 * @program: game2
 * @description:
 * @author: Mr.wugz
 * @create: 2024-05-28 12:17
 **/
public class SkillCommonEffectUtil {

    private static Vector2D d;

    public static void addBullet(SkillRunner t, UnitHuman n, Vector2D from, int i, Object... r) {
        if (null == n) {
            int l = t.battleMain.random.Next(100, 300);
            double u = t.cast().Position.x + (t.cast().direction * l);
            d = new Vector2D(u, t.cast().Position.y);
            t.addBulletToPos(d, from, i);
        } else if (n.dead) {
            d = n.Position;
            t.addBulletToPos(d, from, i);
        } else {
            Bullet s = t.addBullet1(n, from, i);
            if (r != null){
                s.param.addAll(Arrays.asList(r));
            }
        }
    }


    public static UnitHuman getLockTarget(SkillRunner t, boolean n)// n = false
    {
        UnitHuman e = t.getLockTarget();
        if (null == e || e.dead)
            e = t.cast().hatredCtr.getMaxHatredUnit();
        return n && null == e ? t.getLockTarget() : e;
    }

    public static void checkTriggerBullet(SkillHandleBase t, SkillRunner n, UnitHuman i, Vector2D from, int l) {
        List<Buff> s = n.cast().buffCtr.getBuffByType(BuffGroupType.TRIGGER_BULLET.getValue());
        if (s != null && !s.isEmpty()) {
            int c = s.get(0).config().param1;
            int f = (int) s.get(0).config().param2 + 1;
            float d = s.get(0).config().param3;
            float g = s.get(0).skillPar;
            int p = n.battleMain.random.Next(c, f);

            t.addTimer(Math.round(d / 1000f), p, obj -> {
                if (l > 0)
                    addBullet(n, i, from, l, BattleParamKey.SkillEffectMark_Bullet, g);
                else if (i != null)
                    bulletHurt(n, i, g);
            });
        }
    }

    public static void bulletHurt(SkillRunner t, UnitHuman n, float a) {
        if (!t.cast().isDestroy() && null != n && !n.isDestroy()) {
            AttackType r = HurtUtil.checkHit(t.cast(), n, false);
            if (r != AttackType.Miss) {
                int f = 107;
                long d = HurtUtil.normalHurt(t.cast(), n, (int) r.getValue());
                HealthType g = r == AttackType.Normal ? HealthType.Hurt : HealthType.Hurt_Crit;
                float p = t.cast().data.getAttrib(AttribDefine.boss_dam);
                if (n.config().type == (int) UnitType.Boss.getValue() && p > 0)
                    d = Math.round(d * (1d + p));
                d = Math.round(d * (double)a);
                t.healthTarget(n, d, g, true);
            } else
                t.healthTarget(n, 0, HealthType.Miss, true);
        }
    }
}
