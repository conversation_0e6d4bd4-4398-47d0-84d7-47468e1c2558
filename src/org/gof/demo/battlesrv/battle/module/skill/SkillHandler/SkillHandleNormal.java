package org.gof.demo.battlesrv.battle.module.skill.SkillHandler;

import org.gof.core.Port;
import org.gof.core.scheduler.ScheduleTask;
import org.gof.core.support.Time;
import org.gof.core.support.Utils;
import org.gof.demo.battlesrv.battle.BattleParamKey;
import org.gof.demo.battlesrv.battle.bentity.ctroller.IAction;
import org.gof.demo.battlesrv.battle.bentity.ctroller.SkillCtr;
import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.HurtUtil;
import org.gof.demo.battlesrv.battle.module.buff.Buff;
import org.gof.demo.battlesrv.battle.module.buff.BuffAttackAdd;
import org.gof.demo.battlesrv.battle.module.Bullet;
import org.gof.demo.battlesrv.battle.module.skill.SkillRunner;
import org.gof.demo.battlesrv.battle.enumVo.*;
import org.gof.demo.battlesrv.support.Vector2D;
import org.gof.demo.worldsrv.config.ConfSkilleffcet;
import org.gof.demo.worldsrv.config.ConfUnitType;
import org.gof.demo.worldsrv.support.D;
import org.gof.demo.worldsrv.support.Log;

import java.util.List;

public class SkillHandleNormal extends SkillHandleBase {
    public SkillHandleNormal(SkillRunner runner) {
        super(runner);
    }

    @Override
    public void beginRun() {
        SkillHandleNormal t = this;
        SkillRunner e = this.runner;
        String a = BattleParamKey.AniNames_Skill_1;

        float r = e.getActSpeed(a, 1);

        e.changeModeAction(true, a, r);
        e.nextTriggerAction(k->{
            t.triggerAction(e, k, -1);
        });
    }


    protected void checkCounter(UnitHuman t, UnitHuman e) {
        if (1 != e.data.unitType().target && null != t.data.counterAttack && HurtUtil.checkCounterAct(t, e)) {
            t.addCounter(e);
        }
    }

    protected void att(UnitHuman t, int e) {
        SkillRunner r = this.runner;
        if (!r.cast().isDestroy() && null != t && !t.isDestroy()) {
            if (-1 == e)
                this.checkCounter(t, r.cast());
            if (r.cast().data.getBuffState(SpBuffState.StateTriger) > 0) {
                List<Buff> p = r.cast().buffCtr.getBuffByType(BuffGroupType.STATE_TRIGER.getValue());
                for (Buff s : p) {
                    s.onStateTrigger(StateTrigerType.Normal_Act, null);
                }
            }

            AttackType v = HurtUtil.checkHit(r.cast(), t, false);
            if (v != AttackType.Miss) {
                int H = 107;
                if (e > 0) {
                    long N = HurtUtil.normalDoubleHurt(r.cast(), t, v.getValue());
                    HealthType D = v == AttackType.Normal ? HealthType.Hurt_Double : HealthType.Hurt_Double_Crit;
                    float R = this.runner.cast().data.getAttrib(AttribDefine.boss_dam);
                    if (t.config().type ==  UnitType.Boss.getValue() && R > 0) N = Math.round((double)(N * (1 + R)));
                    List<Buff> S = r.cast().buffCtr.getBuffByType(BuffGroupType.DOUBLE_TRIGGER.getValue());
                    for (Buff U : S) {
                        U.onStateTrigger(StateTrigerType.Double_Act, t);
                    }
                    List<Buff> O = t.buffCtr.getBuffByType(BuffGroupType.FRAGILE_EFFECT.getValue());
                    for (Buff M : O) {
                        N += M.calDamage(N, r.cast(),0);
                    }
                    r.healthTarget(t, N, D, true);
                    List<SkillCtr.SkillEffect> W = r.cast().skillctr.skillEffects;
                    for (SkillCtr.SkillEffect K : W) {
                        if (K.triggerType != EffectTriggerType.DOUBLE_ATTACK.getValue()
                                && K.triggerType != EffectTriggerType.NORMAL_DOUBLE_ATTACK.getValue()
                                && K.triggerType != EffectTriggerType.ALL_ATTACK.getValue())
                            continue;
                        K.num++;
                        if (K.limit <= 0 || K.num % K.limit == 0) {
                            if (0 == K.useType)
                                this.addTask(K.id, t.Position, K.runner, null);
                            else if (1 == K.useType)
                                r.cast().skillctr.addSkill(K.id);
                        }
                    }
                    if (v == AttackType.Cirt)
                    {
                        for (int zz = 0; zz < W.size(); zz++)
                        {
                            SkillCtr.SkillEffect Z = W.get(zz);
                            if (Z.triggerType != EffectTriggerType.CRIT_ATTACK.getValue())
                                continue;
                            Z.num++;
                            if (Z.limit <= 0 || Z.num % Z.limit == 0)
                            {
                                UnitHuman j = 1 == ConfSkilleffcet.get(Z.id).targetType[1] ? r.cast() : t;
                                if (0 == Z.useType)
                                    this.addTask(Z.id, t.Position, Z.runner);
                                else if (1 == Z.useType)
                                    r.cast().skillctr.addSkill(Z.id);
                            }
                        }
                    }
                    List<Buff> z = t.buffCtr.getBuffByType(BuffGroupType.VAMPIRE.getValue());
                    for (Buff q : z)
                    {
                        if (q.runner.cast() != r.cast() && q.runner.cast() != r.cast().parent)
                            continue;
                        q.calDamage(N, t, 0);
                    }
                    List<SkillCtr.SkillEffect> Q = t.skillctr.skillEffects;
                    for (int zz = 0; zz < Q.size(); zz++)
                    {
                        SkillCtr.SkillEffect et = Q.get(zz);
                        if (et.triggerType != EffectTriggerType.HP_Hurt.getValue())
                            continue;
                        et.num++;
                        if (et.limit > 0 && et.num % et.limit != 0)
                            continue;
                        if (0 == et.useType)
                            this.addTask(et.id, t.Position, et.runner, t);
                        else if (1 == et.useType)
                            t.skillctr.addSkill(et.id);
                    }
                } else {
                    r.cast().normalActCount++;
                    long w = HurtUtil.normalHurt(r.cast(), t, (int) v.getValue());
                    HealthType Z = v == AttackType.Normal ? HealthType.Hurt : HealthType.Hurt_Crit;
                    List<Buff> P = r.cast().buffCtr.getBuffByType(BuffGroupType.NORMAL_ACT_NUM_TRIGGER.getValue());
                    for (Buff b : P) {
                        if (!(b instanceof BuffAttackAdd)) {
                            continue;
                        }
                        BuffAttackAdd x = (BuffAttackAdd) b;
                        if (x != null)
                            w += x.calValue(r.cast().normalActCount);
                    }
                    if (r.cast().isCallType && r.cast().parent != null) {
                        List<Buff> z = r.cast().parent.buffCtr.getBuffByType(BuffGroupType.UnitCallDamageAdd.getValue());
                        for (Buff j : z) {
                            w += j.calDamage(w, t,0);
                        }
                    }
                    float X = r.cast().data.getAttrib(AttribDefine.boss_dam);
                    if (t.config().type == (int) UnitType.Boss.getValue() && X > 0)
                        w = Math.round(w * (1D + X));
                    List<Buff> q = t.buffCtr.getBuffByType(BuffGroupType.FRAGILE_EFFECT.getValue());
                    for (Buff Y : q) {
                        w += Y.calDamage(w, r.cast(),0);
                    }
                    r.healthTarget(t, w, Z, true);

                    long h = HurtUtil.normailHpsteal(r.cast(), t, w);

                    if (h > 0) {
                        r.healthTarget(r.cast(), h, HealthType.Act_Hpsteal, false);
                        if (HurtUtil.checkNormailHpsteal1(this.runner.cast(), t)) {
                            long tt = HurtUtil.normailHpsteal1(r.cast(), null);
                            if (tt > 0)
                                r.healthTarget(r.cast(), tt, HealthType.Act_Hpsteal, false);
                        }
                    }
                    ConfUnitType et = ConfUnitType.get(r.cast().config().type);
                    if (HurtUtil.checkThrowHit(r.cast(), t)) {
                        r.throwHit(t, et.suspend_time[0], et.suspend_time[1], 0);
                    }
                    if (HurtUtil.checkDizz(r.cast(), t)) {
                        float at = r.cast().data.getAttrib(AttribDefine.vertigo_times) * (1 - t.data.getAttrib(AttribDefine.vertigo_res));
                        //(at = (at);
                        if (at > 0) {
                            r.addBuff(t, et.vertigo_time, at, 0);
                            List<SkillCtr.SkillEffect> it = r.cast().skillctr.skillEffects;
                            for (SkillCtr.SkillEffect lt : it) {
                                if (lt.triggerType == (int) EffectTriggerType.DIZZ.getValue()) {
                                    lt.num++;
                                    if (lt.limit <= 0 || lt.num % lt.limit == 0) {
                                        if (0 == lt.useType)
                                            this.addTask(lt.id, t.Position, lt.runner, t);
                                        else if (1 == lt.useType)
                                            r.cast().skillctr.addSkill(lt.id);
                                    }
                                }
                            }
                        }
                    }

                    List<SkillCtr.SkillEffect> ct = r.cast().skillctr.skillEffects;
                    for (SkillCtr.SkillEffect ut : ct) {
                        if (ut.triggerType != EffectTriggerType.NORMAL_ATTACK.getValue()
                                && ut.triggerType != EffectTriggerType.NORMAL_DOUBLE_ATTACK.getValue()
                                && ut.triggerType != EffectTriggerType.ALL_ATTACK.getValue())
                            continue;
                        ut.num++;
                        if (ut.limit <= 0 || ut.num % ut.limit == 0) {
                            if (0 == ut.useType)
                                this.addTask(ut.id, t.Position, ut.runner, t);
                            else if (1 == ut.useType)
                                r.cast().skillctr.addSkill(ut.id);
                        }
                    }

                    r.cast().buffCtr.removeBuff(BuffGroupType.DESTROY_WHEN_NORMAL_AFTER.getValue());
                    if (v == AttackType.Cirt) {
                        for (SkillCtr.SkillEffect dt : ct) {
                            if (dt.triggerType == EffectTriggerType.CRIT_ATTACK.getValue()){
                                dt.num++;
                                if (dt.limit <= 0 || dt.num % dt.limit == 0){
                                    UnitHuman Tt = 1 == ConfSkilleffcet.get(dt.id).targetType[1] ? r.cast() : t;
                                    if (0 == dt.useType)
                                        this.addTask(dt.id, Tt.Position, dt.runner, Tt);
                                    else if (1 == dt.useType)
                                        r.cast().skillctr.addSkill(dt.id);
                                }
                            }
                        }
                    }

                    List<Buff> Wt = t.buffCtr.getBuffByType(BuffGroupType.VAMPIRE.getValue());
                    for (Buff wt : Wt)
                    {
                        if (wt.runner.cast() != r.cast() && wt.runner.cast() != r.cast().parent)
                            continue;
                        wt.calDamage(w, t, 0);
                    }
                    List<SkillCtr.SkillEffect> Yt = t.skillctr.skillEffects;
                    for (int zz = 0; zz < Yt.size(); zz++)
                    {
                        SkillCtr.SkillEffect zt = Yt.get(zz);
                        if (zt.triggerType != EffectTriggerType.HP_Hurt.getValue())
                            continue;
                        zt.num++;
                        if (zt.limit > 0 && zt.num % zt.limit != 0)
                            continue;
                        if (0 == zt.useType)
                            this.addTask(zt.id, t.Position, zt.runner, t);
                        else if (1 == zt.useType)
                            t.skillctr.addSkill(zt.id);
                    }
                }
            } else
                r.healthTarget(t, 0, HealthType.Miss, true);
        }
    }

    private UnitHuman _addBullet(SkillRunner t, Vector2D from, int r, int i) {
        int n = (int) t.cast().data.getAttribByInt(AttribDefine.target_num);
        if (n > 1) {
            List<UnitHuman> c = t.getTargets(TargetFilter.Enemy, 0, t.cast().Position, n, TargetSelectFilter.NearTarget);
            for (UnitHuman u : c) {
                Bullet b = t.addBullet1(u, from, r);
                b.param.add(BattleParamKey.SkillEffectMark_Normal);
                b.param.add(i);
//                Log.battle.info ("_addBullet {} {}",r,b.param);
            }
            return c.size() > 0 ? c.get(0) : null;
        }
        UnitHuman f = SkillCommonEffectUtil.getLockTarget(t, true);
        SkillCommonEffectUtil.addBullet(t, f, from, r, BattleParamKey.SkillEffectMark_Normal, i);
//        Log.battle.info ("_addBullet {} {} {}",r,BattleParamKey.SkillEffectMark_Normal, i);
        return f;
    }

    private void _bulletNum(SkillRunner t, Vector2D from, int a, int r, UnitHuman i) {
        List<Buff> s = a > 0 ? t.cast().buffCtr.getBuffByType(BuffGroupType.BULLET_NUM.getValue())
                : t.cast().buffCtr.getBuffByType(BuffGroupType.NORMAL_BULLET_NUM.getValue());
        if (!s.isEmpty()) {
            int c = s.get(0).config().param1;
            float u = s.get(0).config().param2;
            this.addTimer(u, c - 1, obj -> {
                if (r > 0)
                    _addBullet(t, from, r, a);
                else if (i != null)
                    att(i, a);
            });
        }
    }

    private void triggerAction(SkillRunner t, int[] e, int r) {
        int n = t.cast().config().bullet;
        if (t.cast().data.skinConfig() != null && t.cast().data.skinConfig().bullet > 0)
            n = t.cast().data.skinConfig().bullet;
        if (null != t.configWeapon() && t.configWeapon().bullet > 0)
            n = t.configWeapon().bullet;
        t.cast().hurtNumCount++;

        UnitHuman c = null;
        if (0 == n) {
            if (t.cast().shamUnit)
                return;
            if ((t.battleMain.battleFlag & BattleFlag.NOT_HURT.getValue()) > 0)
                return;
            int o = (int) t.cast().data.getAttribByInt(AttribDefine.target_num);
            if (o > 1) {
                List<UnitHuman> f = t.getTargets(TargetFilter.Enemy, 0, t.cast().Position, o, TargetSelectFilter.NearTarget);
                for (UnitHuman T : f) {
                    this.att(T, r);
                }
                c = f.get(0);
            } else {
                c = SkillCommonEffectUtil.getLockTarget(t, false);
                this.att(c, r);
            }

            this._bulletNum(t, new Vector2D(0, 0), r, n, c);
            SkillCommonEffectUtil.checkTriggerBullet(this, t, c, new Vector2D(0, 0), n);
        } else {
            Vector2D y = t.cast().getBindPosByAction(e);
            if (t.cast().shamUnit || (t.battleMain.battleFlag & BattleFlag.NOT_HURT.getValue()) > 0) {
                this._addBullet(t, y, n, r);
                return;
            }
            Vector2D v = y;

            if (r > 0) {
                v.x = (y.x + (-30 * t.cast().direction));
                v.y = (y.y + 20);
            }
            this._bulletNum(t, v, r, n, c);
            c = this._addBullet(t, v, n, r);
            SkillCommonEffectUtil.checkTriggerBullet(this, t, c, v, n);
        }

        if (c == null || c.dead || -1 != r || t.cast().shamUnit)
            return;
        else if (HurtUtil.checkDoubleAct(t.cast(), c)) {
            t.healthTarget(t.cast(), 0, HealthType.Double_Act, false);
            this.triggerAction(t, e, 1);
        }
    }

    @Override
    public void onBulletAction(Bullet t, UnitHuman e, Vector2D a, List<Object> r) {
        if (null != e && !e.isDestroy()) {
            SkillRunner i = this.runner;
            if (i.cast().shamUnit || (i.battleMain.battleFlag & BattleFlag.NOT_HURT.getValue()) > 0)
                return;
            String s = String.valueOf(r.get(0));
            if (!BattleParamKey.SkillEffectMark_Bullet.equals(s)) {
                if (BattleParamKey.SkillEffectMark_Normal.equals(s))
                    this.att(e, (int) r.get(1));
                else{
                    SkillCommonEffectUtil.bulletHurt(i, e, (float) r.get(1));
                }
            }
        }
    }



}