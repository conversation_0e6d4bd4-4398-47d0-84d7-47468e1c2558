package org.gof.demo.battlesrv.battle.module.skill.SkillHandler;

import io.netty.util.internal.StringUtil;
import org.gof.demo.battlesrv.battle.BattleParamKey;
import org.gof.demo.battlesrv.battle.bentity.ctroller.IAction;
import org.gof.demo.battlesrv.battle.bentity.ctroller.SkillCtr;
import org.gof.demo.battlesrv.battle.enumVo.BuffGroupType;
import org.gof.demo.battlesrv.battle.module.skill.SkillRunner;
import org.gof.demo.battlesrv.battle.enumVo.EffectTriggerType;

import java.util.List;

public class SkillHandleBossEffect extends SkillHandleBase {
    public SkillHandleBossEffect(SkillRunner runner) {
        super(runner);
    }

    @Override
    public void beginRun() {
        SkillRunner i = this.runner;
        String t = BattleParamKey.AniNames_Skill_1;
        if (StringUtil.isNullOrEmpty(i.useSkill().config.anim))
            t = i.useSkill().config.anim;
        i.changeModeAction(true, t, 1);
        i.nextTriggerAction(this::triggerAction);
    }

    private void triggerAction(int[] i)
    {
        this.runner.cast().buffCtr.removeBuff(BuffGroupType.DESTROY_WHEN_SKILL_AFTER.getValue());
        this.execBeginAction();
        List<SkillCtr.SkillEffect> l = this.runner.cast().skillctr.skillEffects;
        int s = this.runner.useSkill().config.sn;
        for (SkillCtr.SkillEffect o : l) {
            if (o.triggerType == (int) EffectTriggerType.USE_SKILL.getValue() || o.triggerType == (int) EffectTriggerType.ALL_ATTACK.getValue()){
                o.num++;
                if (o.limit <= 0 || o.num % o.limit == 0){
                    String[] a =  o.param5;
                    if (a != null && a.length > 0) {
                        boolean f = false;
                        for (int u = 0; u < a.length; u++) {
                            if (a[u].equals(Integer.toString(s))) {
                                f = true;
                                break;
                            }
                        }
                        if (!f)
                            continue;
                    }
                    if (1 == o.useType)
                        this.addTask(o.id, this.runner.cast().Position, o.runner, null);
                    else if (1 == o.useType)
                        this.runner.cast().skillctr.addSkill(o.id);
                }
            }
        }
    }
}