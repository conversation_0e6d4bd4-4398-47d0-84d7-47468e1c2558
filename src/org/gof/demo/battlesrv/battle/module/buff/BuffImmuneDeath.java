package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.demo.battlesrv.battle.enumVo.AttribDefine;
import org.gof.demo.battlesrv.battle.enumVo.HealthType;
import org.gof.demo.worldsrv.config.ConfBuff;

public class BuffImmuneDeath extends Buff {
    public float _value;
    public float _cd;
    public float _triggerHp;
    public int _triggerNum;
    public boolean _immune;

    public static BuffImmuneDeath alloc(ConfBuff o) {
        BuffImmuneDeath t = new BuffImmuneDeath();
        t.setConfig(o);
        t._value = o.param1 / 10000f;
        t._triggerHp = o.param2 / 10000f;
        t._triggerNum = (int) o.param4;
        return t;
    }

    @Override
    protected void onDestroy() {
        this._cd = 0;
        this._immune = false;
        this.owner.data.immuneDeath = false;
    }

    @Override
    protected void loadEffect() {
        if (this._cd <= 0)
            return;
        super.loadEffect();
    }

    @Override
    protected void onUpdate(float t) {
        this.owner.data.immuneDeath = this._immune;
        if (this._immune) {
            this._cd = Math.max(this._cd - t, 0);
            this._immune = this._cd > 0;
            if (this._cd <= 0 && this._value > 0) {
                long e = Math.round(this.owner.data.getAttrib(AttribDefine.hp) * (double)this._value);
                this.runner.healthTarget(this.owner, e, HealthType.Treat, false);
            }
        }
    }

    @Override
    public boolean checkTarget(float t) {
        if (this._triggerNum <= 0)
            return false;
        if (this._cd > 0)
            return false;
        if (t / this.owner.data.getAttrib(AttribDefine.hp) <= this._triggerHp) {
            this._triggerNum--;
            this._cd = this.config().param3;
            this.owner.data.immuneDeath = true;
            this._immune = true;
        }
        return false;
    }
}
