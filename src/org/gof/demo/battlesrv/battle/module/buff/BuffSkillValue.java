package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.core.support.Utils;
import org.gof.demo.battlesrv.battle.FixRandom;
import org.gof.demo.battlesrv.battle.bentity.ctroller.SkillCtr;
import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.HurtUtil;
import org.gof.demo.battlesrv.battle.MathUtil;
import org.gof.demo.battlesrv.battle.enumVo.*;
import org.gof.demo.worldsrv.config.ConfBuff;

import java.util.List;

public class BuffSkillValue extends Buff {

    public static int HpType_Add = 1;
    public static int HpType_Sub = 2;
    public static int HpType_Attack = 4;
    public static int HpType_Target = 8;

    public int _hpType;
    public int _calType;
    public int _attribId;
    public int _ignoreFlag;
    public float[] _limit;

    public static BuffSkillValue alloc(ConfBuff o) {
        BuffSkillValue t = new BuffSkillValue();
        t.setConfig(o);
        t._hpType = o.param1;
        t._calType = (int) o.param2;
        t._attribId = (int) o.param3;
        t._ignoreFlag =(int) o.param4;
        t._limit = Utils.strToFloatArray(o.param5);//o.param5.ToFloats();
        return t;
    }

    @Override
    protected void onDestroy() {
    }

    @Override
    protected void onBegin() {
        UnitHuman t = this.owner;
        UnitHuman r = this.runner.cast();
        //if (!t.battleMain.battleFlag.AND(BattleFlag.NOT_HURT))
        {
            List<Buff> c = this.owner.buffCtr.getBuffByType(BuffGroupType.SKILL_RETURN.getValue());
            for(Buff ii : c)
            {
                if (ii.checkSkillReturn(r, this.runner))
                    return;
            }
            long e = this._calHurt(t, r);
            if (MathUtil.AND(this._hpType, HpType_Add)) {
                e = FixRandom.roundInt(e * this.skillPar);
                this.runner.healthTarget(t, e, HealthType.Treat, false);
                List<SkillCtr.SkillEffect> I = t.skillctr.skillEffects;
                for (SkillCtr.SkillEffect S : I) {
                    if (S.triggerType == EffectTriggerType.HP_Heal.getValue()) {
                        S.num++;
                        if (S.limit <= 0 || S.num % S.limit == 0) {
                            if (0 == S.useType)
                                this.runner.addTask(S.id, t.Position, S.runner, t);
                            else if (1 == S.useType)
                                t.skillctr.addSkill(S.id);
                        }
                    }
                }
            }
            else if (!this._calHpHurt(t, r, e)) {
                e = FixRandom.roundInt(e * r.data.getSkillFactAttrValue(this.skillPar, this.runner.useSkill().config.sn, AttribDefine.active_skilldamage_par.getValue()));
                long i = 0L;
                List<Buff> l = r.buffCtr.getBuffByType(BuffGroupType.SKILL_DAMAGE_ADD.getValue());
                for (Buff I : l) {
                    i = i + I.calDamage(e, t, this.runner.useSkill().config.sn);
                }
                l.clear();
                float m = MathUtil.AND(this._ignoreFlag, Flag.T1045.getValue()) ? 1 : r.data.getAttrib(AttribDefine.skill_dam_extra);
                long A = FixRandom.roundInt(e * m);
                HealthType S = HealthType.Hurt;
                if (!MathUtil.AND(this._ignoreFlag, Flag.SkillCrit.getValue()) && HurtUtil.checkSkillCirt(r)) {
                    float H = r.data.getAttrib(AttribDefine.skill_crit_dam);
                    A = FixRandom.roundInt(A * (1 + H));
                    A = FixRandom.roundInt((float) Math.pow(A, 0.98f));
                    S = HealthType.Hurt_Crit;
                }
                if (MathUtil.AND(this._ignoreFlag, Flag.UseCrit.getValue()) && HurtUtil.checkHit(r, t, true) == AttackType.Cirt) {
                    float H = r.data.getAttrib(AttribDefine.crit_dam);
                    float U = Math.max(0.5f, t.data.getAttrib(AttribDefine.crit_def));
                    A = FixRandom.roundInt(A * Math.max(1.5f, FixRandom.round(H / U)));
                    S = HealthType.Hurt_Crit;
                }
                float B = r.data.getAttrib(AttribDefine.boss_dam);
                if (this.owner.config().type == UnitType.Boss.getValue() && B > 0)
                    A =  FixRandom.roundInt(A * (1 + B));
                A = A + i;
                long E = r.skillctr.getRecordDamage(this.runner.useSkill().config.sn);
                A = FixRandom.roundInt(A * (1 + (E  / 10000f)));
                A = FixRandom.roundInt(A * this.runner.useSkill().counterDamage);
                float M = t.data.getAttrib(AttribDefine.skill_resist);
                A = HurtUtil.calHurt( FixRandom.roundInt(A * Math.max(0,  FixRandom.round(1 - M))),t, r);
                List<Buff> V = r.buffCtr.getBuffByType(BuffGroupType.GIANT_SLAYER.getValue());
                for(Buff j : V){
                    A = j.onCalHpDamage(t, A);
                }
                this.runner.healthTarget(t, A, S, false);
                if (S == HealthType.Hurt_Crit && !MathUtil.AND(this._ignoreFlag,Flag.UseCrit.getValue())) {
                    List<Buff> F = r.buffCtr.getBuffByType(BuffGroupType.STATE_TRIGER.getValue());
                    for (Buff C : F) {
                        C.onStateTrigger(StateTrigerType.Skill_Crit, null);
                    }
                    F.clear();
                    List<SkillCtr.SkillEffect> L = r.skillctr.skillEffects;
                    for (SkillCtr.SkillEffect w : L) {
                        if (w.triggerType == EffectTriggerType.SKILL_CRIT.getValue()) {
                            w.num++;
                            if (w.limit <= 0 || w.num % w.limit == 0){
                                if (0 == w.useType)
                                    this.runner.addTask(w.id, t.Position, w.runner, t);
                                else if (1 == w.useType)
                                    r.skillctr.addSkill(w.id);
                            }
                        }
                    }
                }
                long P = HurtUtil.skillHpsteal(r, t, A);
                if (P > 0){
                    this.runner.healthTarget(r, P, HealthType.Skill_Hpsteal, false);
                    List<SkillCtr.SkillEffect> L = r.skillctr.skillEffects;
                    for (SkillCtr.SkillEffect w : L) {
                        if (w.triggerType == EffectTriggerType.HP_Heal.getValue()) {
                            w.num++;
                            if (w.limit <= 0 || w.num % w.limit == 0){
                                if (0 == w.useType)
                                    this.runner.addTask(w.id, t.Position, w.runner, t);
                                else if (1 == w.useType)
                                    r.skillctr.addSkill(w.id);
                            }
                        }
                    }
                }
                List<Buff> z = r.buffCtr.getBuffByType(BuffGroupType.SKILL_REAL_DAMAGE.getValue());
                for (Buff J : z) {
                    long q = J.calDamage(A, t,0);
                    List<Buff> F = r.buffCtr.getBuffByType(BuffGroupType.GIANT_SLAYER.getValue());
                    for (Buff C : F) {
                        q = C.onCalHpDamage(t, q);
                    }
                    this.runner.healthTarget(t, q, HealthType.Real_Damage,false);
                    List<SkillCtr.SkillEffect> L = t.skillctr.skillEffects;
                    for (SkillCtr.SkillEffect w : L) {
                        if (w.triggerType == EffectTriggerType.HP_Hurt.getValue()) {
                            w.num++;
                            if (w.limit <= 0 || w.num % w.limit == 0){
                                if (0 == w.useType)
                                    this.runner.addTask(w.id, t.Position, w.runner, t);
                                else if (1 == w.useType)
                                    r.skillctr.addSkill(w.id);
                            }
                        }
                    }
                }

                List<Buff> gt = r.buffCtr.getBuffByType(BuffGroupType.GIANT_SLAYER.getValue());
                for (Buff ht : gt) {
                    ht.calDamage(A, t, 0);
                }
                List<SkillCtr.SkillEffect> L = t.skillctr.skillEffects;
                for (SkillCtr.SkillEffect w : L) {
                    if (w.triggerType == EffectTriggerType.HP_Hurt.getValue()) {
                        w.num++;
                        if (w.limit <= 0 || w.num % w.limit == 0){
                            if (0 == w.useType)
                                this.runner.addTask(w.id, t.Position, w.runner, t);
                            else if (1 == w.useType)
                                r.skillctr.addSkill(w.id);
                        }
                    }
                }
            }
        }
    }

    private long _calHurt(UnitHuman t, UnitHuman r) {
        long a = 0L;
        float e = r.data.getAttrib(AttribDefine.att);
        float i = t.data.getAttrib(AttribDefine.def);
        float u = t.data.getAttrib(AttribDefine.def_coe);
        switch (this._calType) {
            case 0:
                a = MathUtil.AND(this._hpType, HpType.Target.getValue()) ?
                         t.data.getAttribByInt(AttribDefine.fromValue(this._attribId))
                        : r.data.getAttribByInt(AttribDefine.fromValue(this._attribId));
                break;
            case 1:
                a = Math.max(FixRandom.roundInt(e - i* (1 + u)), 1L);
                break;
            case 2:
                float l = r.data.getAttrib(AttribDefine.hp);
                a =  FixRandom.roundInt(l - t.data.currenHp);
                break;
            case 3:
                a = t.data.currenHp;
                break;
            case 4:
                float d = r.data.getAttrib(AttribDefine.att_dam);
                a = FixRandom.roundInt(Math.max(FixRandom.roundInt(e - i* (1 + u)), 1) * d);
                break;
            case 5:
                float s = t.data.getAttrib(AttribDefine.att_dam);
                a = FixRandom.roundInt(Math.max( FixRandom.roundInt(e - i * (1 + u)), 1) * s);
                break;
            case 6:
                float c = r.data.getAttrib(AttribDefine.double_hit_dam);
                a = Math.max(FixRandom.roundInt( FixRandom.roundInt(e - i * (1 + u)) * c), 1);
                break;
            case 7:
                float f = r.data.getAttrib(AttribDefine.counter_dam);
                a = Math.max(FixRandom.roundInt( FixRandom.roundInt(e - i * (1 + u)) * f), 1);
                break;
            case 8:
                a = r.data.currenHp;
                break;
            case 9:
                a = r.data.getAttribByInt(AttribDefine.hp);
                break;
        }
        return a;
    }

    private boolean _calHpHurt(UnitHuman t, UnitHuman r, long a) {
        boolean e = 3 == this._calType || 2 == this._calType|| 8 == this._calType || 9 == this._calType;
        if (e || (0 == this._calType && this._attribId == (int) AttribDefine.hp.getValue())) {
            long i = FixRandom.roundInt(a * this.skillPar);
            i = FixRandom.roundInt(i * t.battleMain.injuryReduce);
            if (this._limit != null && this._limit.length > 0) {
                float c = r.data.getAttrib(AttribDefine.att);
                float g = t.data.getAttrib(AttribDefine.def);
                float h = t.data.getAttrib(AttribDefine.def_coe);
                float p = r.data.getAttrib(AttribDefine.att_dam);
                if (!r.isCallType && r.config().type == UnitType.Partner.getValue()) {
                    c = r.parent.data.getAttrib(AttribDefine.att);
                    float _ = r.data.getAttrib(AttribDefine.partner_dam);
                    float v = r.data.getAttrib(AttribDefine.partner_dam_extra);
                    p = FixRandom.round(p * _ * v);
                }
                long T = FixRandom.roundInt(Math.max(FixRandom.roundInt(c - g * (1 + h)), 1) * p);
                long m = FixRandom.roundInt(c * this._limit[0]);
                long y = FixRandom.roundInt(c * this._limit[1]);
                i = Math.max(i, m);
                i = Math.min(i, y);
            }
            this.runner.healthTarget(t, i, HealthType.Hurt, false);
            List<Buff> b = t.buffCtr.getBuffByType(BuffGroupType.VAMPIRE.getValue());
            for (Buff A : b) {
                A.calDamage(i, t, 0);
            }
            List<SkillCtr.SkillEffect> S = t.skillctr.skillEffects;
            for (SkillCtr.SkillEffect C:S) {
                if (C.triggerType == EffectTriggerType.HP_Hurt.getValue()) {
                    C.num++;
                    if (C.limit <= 0 || C.num % C.limit == 0) {
                        if (0 == C.useType)
                            this.runner.addTask(C.id, t.Position, C.runner, t);
                        else if (1 == C.useType)
                            t.skillctr.addSkill(C.id);
                    }
                }
            }
            return true;
        }
        return false;
    }
}
