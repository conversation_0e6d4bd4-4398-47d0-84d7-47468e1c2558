package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.core.support.Utils;
import org.gof.demo.battlesrv.battle.FixRandom;
import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.enumVo.AttribDefine;
import org.gof.demo.battlesrv.battle.enumVo.HealthType;
import org.gof.demo.worldsrv.config.ConfBuff;

public class BuffSkillHpHurt extends Buff {
    public float _resistPar;
    public float[] _limit;

    public static BuffSkillHpHurt alloc(ConfBuff o) {
        BuffSkillHpHurt t = new BuffSkillHpHurt();
        t.setConfig(o);
        t._resistPar = o.param1 / 10000f;
        t._limit = Utils.strToFloatArray(o.param5);
        return t;
    }

    @Override
    protected void onDestroy() {
    }

    @Override
    protected void onBegin() {
        UnitHuman t = this.owner;
        UnitHuman i = this.runner.cast();
        //if (!t.battleMain.battleFlag.AND(BattleFlag.NOT_HURT))
        {
            long n = t.data.getAttribByInt(AttribDefine.hp);
            float r = i.data.getAttrib(AttribDefine.resist);
            n = FixRandom.roundInt(n * this.skillPar) + FixRandom.roundInt(n * this._resistPar);
            n = FixRandom.roundInt(n * r);
            if (this._limit.length > 0) {
                float e = i.data.getAttrib(AttribDefine.att);
                float s = t.data.getAttrib(AttribDefine.def);
                float c = i.data.getAttrib(AttribDefine.att_dam);
                long f = FixRandom.roundInt(Math.max(FixRandom.roundInt(e - s), 1) * c);
                long h = FixRandom.roundInt(f * this._limit[0]);
                long p = FixRandom.roundInt(f * this._limit[1]);
                n = Math.max(n, h);
                n = Math.min(n, p);
            }
            this.runner.healthTarget(t, n, HealthType.Hurt, false);
        }
    }
}
