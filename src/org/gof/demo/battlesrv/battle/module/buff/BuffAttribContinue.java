package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.core.support.Utils;
import org.gof.demo.battlesrv.battle.module.MetaAttrib;
import org.gof.demo.battlesrv.battle.enumVo.AttribDefine;
import org.gof.demo.worldsrv.config.ConfBuff;
import org.gof.demo.worldsrv.support.Log;

public class BuffAttribContinue extends Buff {
    private AttribDefine _id;
    private float _value;
    private boolean _isMultiples;
    private float _lasttime;
    private float _freqtime;
    private float _CurTime1;
    private float _CurTime2;
    private float _totaladdvalue;

    public static BuffAttribContinue alloc(ConfBuff t) {
        BuffAttribContinue i = new BuffAttribContinue();
        i.setConfig(t);
        i._id = AttribDefine.fromValue(t.param1);
        i._isMultiples = 2 == t.param2;
        i._value = t.param3;
        i._freqtime = t.param4;
        i._lasttime = Utils.strToFloatArray(t.param5)[0];
        i._totaladdvalue = 0;
        i._CurTime1 = 0;
        i._CurTime2 = 0;
        return i;
    }

    public BuffAttribContinue() {

    }

    public BuffAttribContinue(ConfBuff t) {
        setConfig(t);
        _id = AttribDefine.fromValue(t.param1);
        _isMultiples = 2 == t.param2;
        _value = t.param3;
        _freqtime = t.param4;
        _lasttime = Utils.strToFloatArray(t.param5)[0];// t.param5.ToFloat();
        _totaladdvalue = 0;
        _CurTime1 = 0;
        _CurTime2 = 0;
    }

    @Override
    protected void onBegin() {
        this.AddValue();
    }

    private void AddValue() {
        MetaAttrib t = this.owner.data.getAttribMeta(this._id);
        if (null != t) {
            float i = this._value * this.skillPar;
            this._totaladdvalue = this._totaladdvalue + i;
            if (this._isMultiples)
                t.addMultiples(i);
            else
                t.addValue(i);
        } else
//                Console.WriteLine("警告 属性 " + this._id + " 不存在");
            Log.temp.error("警告 属性 {} 不存在", _id);
    }

    @Override
    protected void onUpdate(float t) {
        if (this._CurTime1 < this._lasttime) {
            if (this._CurTime2 >= this._freqtime) {
                this.AddValue();
                this._CurTime2 = 0;
            }
            this._CurTime2 = this._CurTime2 + t;
            this._CurTime1 = this._CurTime1 + t;
        }
    }

    @Override
    protected void onDestroy() {
        MetaAttrib t = this.owner.data.getAttribMeta(this._id);
        if (this._isMultiples)
            t.addMultiples(-this._totaladdvalue);
        else
            t.addValue(-this._totaladdvalue);
    }
}
