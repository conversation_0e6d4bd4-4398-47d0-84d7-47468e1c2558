package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.core.support.Utils;
import org.gof.demo.battlesrv.battle.FixRandom;
import org.gof.demo.battlesrv.battle.enumVo.AttribDefine;
import org.gof.demo.worldsrv.config.ConfBuff;

public class BuffHpTrigger extends Buff {
    private int _operator;
    private float triggervalue;
    private int _addBuffid;
    private int _addlasttime;

    public static BuffHpTrigger alloc(ConfBuff t) {
        BuffHpTrigger r = new BuffHpTrigger();
        r.setConfig(t);
        r._operator = t.param1;
        r.triggervalue = t.param2;
        r._addBuffid = (int)t.param3;
        r._addlasttime = t.param4;
        return r;
    }

    @Override
    protected void onBegin()
    {
        this._checkAddBuff();
    }

    private void _checkAddBuff()
    {
        long t = this.owner.data.currenHp;
        float r = FixRandom.round(t / this.owner.data.getAttrib(AttribDefine.hp));
        float e = FixRandom.round(this.triggervalue / 10000);
        boolean i = false;
        switch (this._operator)
        {
            case 0:
                i = r > e;
                break;
            case 1:
                i = r < e;
                break;
            case 2:
                i = r == e;
                break;
            case 3:
                i = r >= e;
                break;
            case 4:
                i = r <= e;
                break;
        }
        if (i){
            this.runner.addBuff(this.owner, this._addBuffid, this._addlasttime, this.skillPar);
        }
    }
}
