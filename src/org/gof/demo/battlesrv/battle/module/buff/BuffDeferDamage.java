package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.demo.battlesrv.battle.FixRandom;
import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.enumVo.HealthType;
import org.gof.demo.worldsrv.config.ConfBuff;

public class BuffDeferDamage extends Buff {

    private float getDamRatio;
    private float getDamTime;
    private float releaseDamRatio;
    private float releaseDamTime;
    private long totalDam;
    private float checkTime;
    private float _curtime;
    private float _freqtime = 1;

    public static BuffDeferDamage alloc(ConfBuff t) {
        BuffDeferDamage i = new BuffDeferDamage();
        i.setConfig(t);
        i.getDamRatio = t.param1;
        i.getDamTime = t.param2;
        i.releaseDamRatio = t.param3;
        i.releaseDamTime = t.param4;
        return i;
    }

    @Override
    protected void onBegin() {
        this.totalDam = 0;
        this.checkTime = 0;
        this._curtime = 0;
    }

    @Override
    protected void onDestroy() {
        this.totalDam = 0;
        this.checkTime = 0;
        this._curtime = 0;
    }

    @Override
    public long calDamage(long e, UnitHuman t, int n) {

        if (this.checkTime < this.getDamTime)
        {
            long i = FixRandom.roundInt(e * this.getDamRatio / 10000);
            this.totalDam += i;
            return e - i;
        }
        return e;
    }

    @Override
    protected void onUpdate(float e) {
        this.checkTime = this.checkTime + e;
        if (this.checkTime >= this.getDamTime) {
            if (this.checkTime <= this.getDamTime + this.releaseDamTime)
            {
                if (this._curtime >= this._freqtime)
                {
                    float t = this.totalDam * this.releaseDamRatio / 10000;
                    long i = FixRandom.roundInt(t);
                    if (0 != i) {
                        this.owner.hit(null, i, HealthType.Hurt);
                        this._curtime = 0;
                    }
                }
                this._curtime = FixRandom.round(this._curtime + e);
            }
            else{
                this.stop();
            }
        }
    }
}
