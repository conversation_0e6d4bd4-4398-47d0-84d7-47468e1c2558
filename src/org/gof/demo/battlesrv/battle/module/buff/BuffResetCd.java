package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.module.skill.Skill;
import org.gof.demo.worldsrv.config.ConfBuff;

import java.util.List;

public class BuffResetCd extends Buff {
    public int _num;
    public int _sortType;
    public int _resetType;

    public static BuffResetCd alloc(ConfBuff o) {
        BuffResetCd t = new BuffResetCd();
        t.setConfig(o);
        t._num = o.param1;
        t._sortType = (int) o.param2;
        t._resetType = (int) o.param3;
        return t;
    }

    @Override
    protected void onDestroy() {
    }

    @Override
    protected void onBegin() {
        UnitHuman t = this.owner;
        if (t.data.skillList != null && 0 != t.data.skillList.size()) {
            List<Skill> e = t.data.skillList;
            int r = Math.min(e.size(), this._num);
            for (int n = 0; n < r; n++) {
                Skill o = e.get(this.runner.battleMain.random.Next(0,e.size()));
                if (1 != o.state) {
                    o.currenPower = o.config.maxPower;
                    o.state = 0;
                } else
                    o.resetCd = true;
            }
        }
    }
}
