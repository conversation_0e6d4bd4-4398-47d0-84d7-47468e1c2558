package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.demo.worldsrv.config.ConfBuff;

public class BuffTaunt extends Buff {
    public long _value;

    public static BuffTaunt alloc(ConfBuff o) {
        BuffTaunt t = new BuffTaunt();
        t.setConfig(o);
        t._value = o.param1 + 999999999999999L;
        return t;
    }

    @Override
    protected void onDestroy() {
        this.owner.tauntValue -= this._value;
    }

    @Override
    protected void onBegin() {
        this.owner.tauntValue += this._value;
        super.onBegin();
    }
}
