package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.core.support.Utils;
import org.gof.demo.battlesrv.battle.enumVo.BuffGroupType;
import org.gof.demo.battlesrv.battle.module.MetaAttrib;
import org.gof.demo.battlesrv.battle.enumVo.AttribDefine;
import org.gof.demo.worldsrv.config.ConfBuff;

import java.util.List;

public class BuffAttribCondition extends Buff {
    private AttribDefine _id;
    private int _calType;
    private boolean _isMultiples;
    private float _value;
    private float[] _limit;
    private float lastValue;

    public static BuffAttribCondition alloc(ConfBuff t) {
        BuffAttribCondition i = new BuffAttribCondition();
        i.setConfig(t);
        i._id = AttribDefine.fromValue(t.param1);
        i._calType = (int) t.param2;
        i._isMultiples = 2 == t.param3;
        i._value = t.param4;
        i._limit = Utils.strToFloatArray(t.param5);
        i.lastValue = 0;
        return i;
    }

    public BuffAttribCondition() {

    }

    @Override
    public void updateAttrib() {
        MetaAttrib i = this.owner.data.getAttribMeta(this._id);
        if (this._isMultiples)
            i.addMultiples(-this.lastValue);
        else
            i.addValue(-this.lastValue);
        float t = 0;
        if (0 == this._calType) {
            float a = this.owner.data.getAttrib(AttribDefine.hp);
            long l = this.owner.data.currenHp;
            List<Buff> u = this.owner.buffCtr.getBuffByType(BuffGroupType.CURRENT_HP.getValue());
            if (u.isEmpty()){
                t = (a - l) / a;
            }
            else
            {
                for (Buff v : u)
                {
                    float f = v.getFixHp();
                    t = 1 - f;
                    break;
                }
            }
        }
        else if (1 == this._calType)
        {
            float c = this.owner.data.getAttrib(AttribDefine.speed);
            MetaAttrib p = this.owner.data.getAttribMeta(AttribDefine.speed);
            t = Math.max(0, p.getBaseValue() - c) / p.getBaseValue();
        }
        this.lastValue = Math.max(this._limit[0], Math.min(this._limit[1], t * this._value * this.skillPar));
        if (this._isMultiples)
            i.addMultiples(this.lastValue);
        else
            i.addValue(this.lastValue);
    }

    @Override
    protected void onDestroy() {
        MetaAttrib i = this.owner.data.getAttribMeta(this._id);
        if (this._isMultiples)
            i.addMultiples(-this.lastValue);
        else
            i.addValue(-this.lastValue);
    }
}