package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.module.skill.SkillRunner;
import org.gof.demo.battlesrv.battle.enumVo.SpBuffState;
import org.gof.demo.battlesrv.battle.enumVo.TargetFilter;
import org.gof.demo.battlesrv.battle.enumVo.TargetSelectFilter;
import org.gof.demo.worldsrv.config.ConfBuff;

import java.util.ArrayList;
import java.util.List;

public class BuffAddBuffToTarget extends Buff {
    private int _targettype;
    private int _buffid;
    private int _targetcount;
    private List<Buff> buffList = new ArrayList<>();

    public static BuffAddBuffToTarget alloc(ConfBuff o) {
        BuffAddBuffToTarget t = new BuffAddBuffToTarget();
        t.setConfig(o);
        t._targettype = o.param1;
        t._buffid = (int) o.param2;
        t._targetcount = (int) o.param3;
        return t;
    }

    @Override
    protected void onDestroy() {
        if (this._targettype == (int) TargetFilter.CastPartner.getValue())
            this.owner.data.AddBuffState(SpBuffState.PetAddBuff, -1);
        for (Buff f : buffList) {
            if (null != f && f.runner == this.runner)
                f.stop();
        }
        this.buffList.clear();
    }

    @Override
    protected void onBegin() {
        if (this._targettype == (int) TargetFilter.CastPartner.getValue())
            this.owner.data.AddBuffState(SpBuffState.PetAddBuff, 1);
        this._CheckTargets();
    }

    private void _CheckTargets() {
        List<UnitHuman> t = this.runner.getTargets((TargetFilter.fromValue(this._targettype)), 100, this.owner.Position, this._targetcount, TargetSelectFilter.NearTarget);
        SkillRunner e = this.runner;
        if (t.size() > 0) {
            this.buffList.clear();
            for (UnitHuman o : t) {
                Buff i = e.addBuff(o, this._buffid, -1, this.skillPar);
                if (null != i)
                    this.buffList.add(i);
            }
        }
    }

    @Override
    public boolean checkTarget(float t) {
        if (t == this._targettype)
            this._CheckTargets();
        return false;
    }

}
