package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.config.ConfBuff;

public class BuffRandomBuff extends Buff {
    public float _duration;
    public float _curTime;
    public float _buffTime;

    public static BuffRandomBuff alloc(ConfBuff o) {
        BuffRandomBuff t = new BuffRandomBuff();
        t.setConfig(o);
        t._duration = o.param1 / 1000f;
        t._buffTime = (int) o.param2 / 1000f;
        t._curTime = 0;
        return t;
    }

    @Override
    protected void onDestroy() {
    }

    @Override
    protected void onBegin() {
        this.addBuff();
    }

    @Override
    protected void onUpdate(float t) {
        if (this._curTime >= this._duration) {
            this.addBuff();
            this._curTime = 0;
        }
        this._curTime = this._curTime + t;
    }

    private void addBuff() {
        int[][] t = Utils.parseIntArray2(this.config().param5);//this.config().param5.ToJaggedInts();
        if (t.length > 0) {
            int[] n = t[this.runner.battleMain.random.Next(0,t.length)];
            for (int i = 0; i < n.length; i += 2) {
                int o = n[i];
                float f = n[i + 1] / 10000f * this.skillPar;
                this.runner.addBuff(this.owner, o, this._buffTime, f);
            }
        }
    }
}
