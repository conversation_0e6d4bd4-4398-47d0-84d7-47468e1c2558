package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.core.support.Utils;
import org.gof.demo.battlesrv.battle.FixRandom;
import org.gof.demo.battlesrv.battle.UnitMgr;
import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.enumVo.AttribDefine;
import org.gof.demo.battlesrv.battle.enumVo.TargetFilter;
import org.gof.demo.battlesrv.battle.enumVo.TargetSelectFilter;
import org.gof.demo.worldsrv.config.ConfBuff;

import java.util.ArrayList;
import java.util.List;

public class BuffSpeedTrigger extends Buff {

    private int _operator;
    private float triggervalue;
    private int[][] _addBuffList;
    private float _calType;

    public static BuffSpeedTrigger alloc(ConfBuff e) {
        BuffSpeedTrigger t = new BuffSpeedTrigger();
        t.setConfig(e);
        t._operator = e.param1;
        t.triggervalue = e.param2;
        t._calType = e.param3;
        t._addBuffList = Utils.parseIntArray2(e.param5);
        return t;
    }

    @Override
    protected void onBegin() {
        this._checkAddBuff();
    }

    private void _checkAddBuff()
    {
        float e = this.owner.data.getAttrib(AttribDefine.speed);
        float t = FixRandom.round(e / this.owner.data.getAttribMeta(AttribDefine.speed).getBaseValue());
        float a = FixRandom.round(this.triggervalue / 10000);
        boolean i = false;
        switch (this._operator)
        {
            case 0:
                i = t > a;
                break;
            case 1:
                i = t < a;
                break;
            case 2:
                i = t == a;
                break;
            case 3:
                i = t >= a;
                break;
            case 4:
                i = t <= a;
                break;
        }
        if (i)
        {
            List<UnitHuman> n = new ArrayList<>();
            n.add( this.owner);
            if (1 == this._calType)
            {
                UnitMgr c = this.runner.battleMain.unitMgr;
                List<UnitHuman> l = c.findTarget(this.owner, TargetFilter.CastPartner, 100, this.owner.Position);
                n = c.getTargetList(this.owner, l, 10, TargetSelectFilter.NearTarget);
            }
            for (int[] h : _addBuffList)
            {
                for (UnitHuman b : n)
                {
                    this.runner.addBuff(b, h[0], h[1], this.skillPar);
                }
            }
        }
    }
}
