package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.config.ConfBuff;

import java.util.List;

public class BuffUseSkillAdd extends Buff {
    public int _value;
    public int _limit;
    public List<Integer> skillList ;

    public static BuffUseSkillAdd alloc(ConfBuff o) {
        BuffUseSkillAdd t = new BuffUseSkillAdd();
        t.setConfig(o);
        t._value = o.param1;
        t._limit = (int) o.param2;
        t.skillList =  Utils.strToIntList(o.param5);
        return t;
    }

    @Override
    protected void onDestroy() {
    }
}
