package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.enumVo.AttribDefine;
import org.gof.demo.worldsrv.config.ConfBuff;

public class BuffSkillFragileAdd extends Buff {
    public int _calType;
    public int _attribId;

    public static BuffSkillFragileAdd alloc(ConfBuff o) {
        BuffSkillFragileAdd t = new BuffSkillFragileAdd();
        t.setConfig(o);
        t._calType = o.param1;
        t._attribId = (int) o.param2;
        return t;
    }

    @Override
    protected void onDestroy() {
    }

    @Override
    public long calDamage(long t, UnitHuman n,int e) {
        long i = 0L;
        if (0 == this._calType)
            i = Math.round(n.data.getAttrib(AttribDefine.fromValue(this._attribId)) * (double)this.skillPar);
        else if (1 == this._calType)
            i = Math.round(n.data.currenHp * (double)this.skillPar);
        return i;
    }
}
