package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.core.support.Utils;
import org.gof.demo.battlesrv.battle.FixRandom;
import org.gof.demo.battlesrv.battle.enumVo.AttribDefine;
import org.gof.demo.worldsrv.config.ConfBuff;

public class BuffRemake extends Buff {

    protected float _value;
    protected float _triggerHp;
    protected int _triggerNum;
    protected int[][] _addBuffList;
    protected float _effCD;
    protected boolean trigger;

    public static BuffRemake alloc(ConfBuff t) {
        BuffRemake e = new BuffRemake();
        e.setConfig(t);
        e._value = t.param1 / 10000f;
        e._triggerHp = t.param2 / 10000f;
        e._triggerNum = (int)t.param3;
        e._effCD = t.param4;
        e._addBuffList =Utils.parseIntArray2(t.param5);
        return e;
    }

    @Override
    protected void onBegin() {
        this._effCD = this.config().param4;
    }

    @Override
    protected void onUpdate(float t) {
        if (!this.trigger)
            return;
        this._effCD = Math.max(this._effCD - t, 0);
        if (this._effCD <= 0)
        {
            long e = Math.max(FixRandom.roundInt(this.owner.data.getAttribByInt(AttribDefine.hp) * this._value), this.owner.data.getAttribByInt(AttribDefine.hp));
            this.owner.data.currenHp = e;
            this.trigger = false;
            this.owner.data.remakeLock = false;
        }
    }

    @Override
    public boolean checkTarget(float t) {
        if (this._triggerNum <= 0)
            return false;
        if (FixRandom.round(t / this.owner.data.getAttrib(AttribDefine.hp)) <= this._triggerHp && this._value > 0)
        {
            this.trigger = true;
            this._triggerNum--;
            this.owner.data.remakeLock = true;
            for (int[] o : this._addBuffList)
            {
                this.runner.addBuff(this.owner, o[0], o[2], FixRandom.round(o[1] / 10000f));
            }
            return true;
        }
        return false;
    }

    @Override
    protected void onDestroy() {
        this.trigger = false;
        this._effCD = 0;
        this.owner.data.remakeLock = false;
    }
}
