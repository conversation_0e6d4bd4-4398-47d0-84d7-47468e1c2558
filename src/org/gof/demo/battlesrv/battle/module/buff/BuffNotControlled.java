package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.demo.worldsrv.config.ConfBuff;

public class BuffNotControlled extends Buff {
    public static BuffNotControlled alloc(ConfBuff o) {
        BuffNotControlled t = new BuffNotControlled();
        t.setConfig(o);
        return t;
    }

    @Override
    protected void onDestroy() {
        this.owner.statectr.notControlled--;
    }

    @Override
    protected void onBegin() {
        this.owner.statectr.notControlled++;
    }
}
