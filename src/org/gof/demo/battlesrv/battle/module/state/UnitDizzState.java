package org.gof.demo.battlesrv.battle.module.state;

import org.gof.demo.battlesrv.battle.BattleParamKey;
import org.gof.demo.battlesrv.battle.bentity.ctroller.StateCtr;
import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.module.buff.Buff;
import org.gof.demo.battlesrv.battle.enumVo.BuffGroupType;
import org.gof.demo.battlesrv.battle.enumVo.SpBuffState;
import org.gof.demo.battlesrv.battle.enumVo.StateTrigerType;
import org.gof.demo.battlesrv.battle.enumVo.StateType;

import java.util.List;

public class UnitDizzState extends State {

    public UnitDizzState(StateCtr ctr, int priority) {
        super(ctr, priority);
        type = StateType.Dizz;
    }

    @Override
    public void onEnter() {
        UnitHuman t = this._owner;
        this._ctr.lockCurrenState = true;
        this._ctr.beControlled++;
        t.movectr.stopMove();
        if (!t.animatorctr.hasState(BattleParamKey.UnitConfig_ANIMATOR_IDLE))
            t.animatorctr.changeState(BattleParamKey.UnitConfig_ANIMATOR_IDLE);
        if (t.data.getBuffState(SpBuffState.StateTriger) > 0) {
            List<Buff> r = t.buffCtr.getBuffByType(BuffGroupType.STATE_TRIGER.getValue());
            for (Buff e : r) {
                e.onStateTrigger(StateTrigerType.Dizz, null);
            }
        }
    }

    @Override
    public void onUpdate(float t) {
        if (this._owner.statectr.dizz <= 0)
            this.changeState();
    }

    @Override
    public void onExit() {
        UnitHuman t = this._owner;
        this._ctr.beControlled--;
        t.idle();
    }
}
