package org.gof.demo.battlesrv.battle.module.state;

import org.gof.demo.battlesrv.battle.BattleParamKey;
import org.gof.demo.battlesrv.battle.bentity.ctroller.StateCtr;
import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.enumVo.StateType;

public class UnitHitState extends State {
    public UnitHitState(StateCtr ctr, int priority) {
        super(ctr, priority);
        type = StateType.Hit;
    }

    @Override
    public void onEnter() {
        this._owner.animatorctr.changeState(BattleParamKey.UnitConfig_ANIMATOR_HIT);
    }

    @Override
    public void onUpdate(float t) {
        UnitHuman n = this._owner;
        if (n.animatorctr.curFrame() >= n.data.modelConfig().hitTime)
            this.changeState();
    }

    @Override
    public void onEnd() {
        this.changeState();
    }
}
