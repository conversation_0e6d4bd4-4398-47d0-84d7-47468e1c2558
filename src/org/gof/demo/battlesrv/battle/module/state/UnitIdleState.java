package org.gof.demo.battlesrv.battle.module.state;

import org.gof.demo.battlesrv.battle.bentity.ctroller.StateCtr;
import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.enumVo.StateType;

public class UnitIdleState extends State {
    public int freeNum;

    public UnitIdleState(StateCtr ctr, int priority) {
        super(ctr, priority);
        type = StateType.Idle;
        freeNum = 0;
    }

    @Override
    public void onEnter() {
        UnitHuman t = this._owner;
        t.idle();
        t.movectr.stopMove();
        this.freeNum = 0;
    }

    @Override
    public void onUpdate(float t) {
        UnitHuman e = this._owner;
        e.applyTarget(false);
        e.applySkill(false);
        if (null == e.curSkill() || null == e.curSkill().getTarget()) {
            if (null == e.checkTarget()) {
                if (null == e.curTarget())
                    this.freeNum++;
                else
                    this.changeState(StateType.Move);
            } else
                this.changeState(StateType.Skill);
        } else
            this.changeState(StateType.Skill);
    }

    @Override
    public void onExit() {
        this.freeNum = 0;
    }
}
