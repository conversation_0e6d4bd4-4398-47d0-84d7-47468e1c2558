package org.gof.demo.battlesrv.battle.module.aI;

import org.gof.demo.battlesrv.battle.BattleMain;
import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.enumVo.BattleFlag;
import org.gof.demo.battlesrv.battle.enumVo.TargetFilter;
import org.gof.demo.battlesrv.battle.module.skill.Skill;

import java.util.List;

public class T20PlayerAI extends IAIHandler {
    @Override
    protected  boolean startSkill()
    {
        BattleMain t = this.cast.battleMain();
        UnitHuman e = this.cast;
        if (e.battleMain().playerAuto && !((t.battleFlag & (int) BattleFlag.LIMIT_SKILL.getValue()) > 0 && t.mainCtr.player != e))
        {
            List<Skill> a = e.data.skillList;
            if (null == a || 0 == a.size())
                return false;
            for (Skill o : a)
            {
                if (e.aiTime >= o.useDelay && 0 == o.state)
                {
                    if (0 == e.battleMain().unitMgr.findTarget(e, TargetFilter.Enemy, 1000, e.Position, true).size())
                        continue;
                    e.addUnlimitSkill(o);
                }
            }
        }
        return false;
    }
}
