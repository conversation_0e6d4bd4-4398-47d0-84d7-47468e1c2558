package org.gof.demo.battlesrv.battle.module.aI;

import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.module.skill.Skill;
import org.gof.demo.battlesrv.battle.enumVo.TargetFilter;

import java.util.List;

public class ShamPlayerAI extends IAIHandler {

    @Override
    public void handleAction() {
    }

    @Override
    protected boolean startSkill() {
        return false;
    }
}

