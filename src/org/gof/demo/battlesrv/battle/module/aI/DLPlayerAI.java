package org.gof.demo.battlesrv.battle.module.aI;

import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.bentity.ctroller.HatredCtr;
import org.gof.demo.battlesrv.battle.module.skill.Skill;
import org.gof.demo.battlesrv.battle.enumVo.TargetFilter;

import java.util.List;

public class DLPlayerAI extends IAIHandler {

    @Override
    public void handleAction() {
        UnitHuman t = this.getTarget();
        if (null == t)
        {
            this.cast.wantTarget = null;
            this.cast.curTarget = null;
            this.startSkill();
            return;
        }
        this.startSkill();
        this.selectTarget(t);
    }

    @Override
    protected UnitHuman getTarget() {
        UnitHuman e = this.cast;
        List<HatredCtr.Hatred> n = e.hatredCtr.getHatredList();
        for (HatredCtr.Hatred a : n)
        {
            if (!a.unit.isDead() && a.unit.Position.y == e.Position.y)
                return a.unit;
        }
        for (HatredCtr.Hatred u : n)
        {
            if (!u.unit.isDead())
                return u.unit;
        }
        return null;
    }

    @Override
    protected boolean startSkill() {
        UnitHuman t = this.cast;
        if ( !t.battleMain.playerAuto)
        {
            if (t.battleMain.showMainCtr.player == t)
                return false;
        }
        else if (!t.battleMain.playerAuto)
            return false;
        List<Skill> e = t.data.skillList;
        if (null == e || e.isEmpty())
            return false;
        for (Skill s : e)
        {
            if (t.aiTime >= s.useDelay && 0 == s.state)
            {
                if (t.battleMain.unitMgr.findTarget(t, TargetFilter.Enemy, s.config.autoDis + t.skillAutoDis, t.Position).isEmpty())
                    continue;
                t.addUnlimitSkill(s);
            }
        }
        return false;
    }
}

