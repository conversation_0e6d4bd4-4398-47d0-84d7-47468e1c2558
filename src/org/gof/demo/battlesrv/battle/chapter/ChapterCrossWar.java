package org.gof.demo.battlesrv.battle.chapter;

import org.gof.demo.battlesrv.battle.BattleMain;
import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.worldsrv.config.ConfKfWarChapter;
import org.gof.demo.worldsrv.instance.ConfChapterVO;

import java.util.List;

public class ChapterCrossWar extends ChapterArena {
    public ChapterCrossWar(BattleMain battleMain) {
        super(battleMain);
    }

    @Override
    public ConfChapterVO getConfig(int id) {
        return new ConfChapterVO(ConfKfWarChapter.get(id));
    }

    @Override
    public void loadData(float t) {
        super.loadData(t);

        this.addBuff(this.battleMain.mainCtr.player);
        this.addBuff(this.arenaPlayerCtr.player);
    }

    private void addBuff(UnitHuman t) {
        List<Integer> e = t.data.buffs;
        if (null != e && 0 != e.size())
            for (int _e : e)
                t.skillctr.addChapterBuff(_e);
    }

}