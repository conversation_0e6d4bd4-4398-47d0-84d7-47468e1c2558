package org.gof.demo.battlesrv.battle.chapter;

import org.gof.demo.battlesrv.battle.BattleMain;
import org.gof.demo.battlesrv.battle.BattleMainServer;
import org.gof.demo.battlesrv.battle.enumVo.AttribDefine;

public class ChapterGvgServer extends ChapterGvg {
    public ChapterGvgServer(BattleMain battleMain) {
        super(battleMain);
    }

    @Override
    public boolean toResult(int t, boolean a)//a = false
    {
        BattleMainServer l = this.battleMain instanceof BattleMainServer ? (BattleMainServer) (this.battleMain) : null;
//            var l = this.battleMain as BattleMainServer;
        long e = this.battleMain.mainCtr != null && this.battleMain.mainCtr.player != null
                && this.battleMain.mainCtr.player.data != null ?
                Math.round(10000D * this.battleMain.mainCtr.player.data.currenHp
                        / this.battleMain.mainCtr.player.data.getAttribByInt(AttribDefine.hp)) : 0;
        long r = this.arenaPlayerCtr != null && this.arenaPlayerCtr.player != null
                && this.arenaPlayerCtr.player.data != null ? Math.round(10000D * this.arenaPlayerCtr.player.data.currenHp
                / this.arenaPlayerCtr.player.data.getAttribByInt(AttribDefine.hp)) : 0;
        l.toResult(e >= r ? 0 : 1, a);
        BattleMainServer s = l;
        //s.toResultExt && s.toResultExt({
        //    value1: e,
        //    value2: r
        //});
        return false;
    }
}