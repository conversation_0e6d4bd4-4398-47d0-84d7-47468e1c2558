package org.gof.demo.battlesrv.battle.chapter;

import org.gof.demo.battlesrv.battle.BattleMain;
import org.gof.demo.battlesrv.battle.BattleMainServer;

public class ChapterCrossParkPvpServer extends ChapterCrossParkPvp {
    public ChapterCrossParkPvpServer(BattleMain battleMain) {
        super(battleMain);
    }

    @Override
    public boolean toResult(int i, boolean b)//b = false
    {
        if (this.battleMain instanceof BattleMainServer)
            ((BattleMainServer) (this.battleMain)).toResult(i, b);
        return false;
    }
}