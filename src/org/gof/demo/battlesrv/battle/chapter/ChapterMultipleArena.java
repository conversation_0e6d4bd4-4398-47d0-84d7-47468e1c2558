package org.gof.demo.battlesrv.battle.chapter;

import org.gof.demo.battlesrv.battle.BattleMain;
import org.gof.demo.battlesrv.battle.PlayerData;
import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.enumVo.AttribDefine;
import org.gof.demo.battlesrv.battle.enumVo.UnitType;
import org.gof.demo.battlesrv.battle.module.UnitData;
import org.gof.demo.battlesrv.battle.module.UnitGroupCtr;
import org.gof.demo.worldsrv.config.ConfChapterType;
import org.gof.demo.worldsrv.config.ConfGlobal;
import org.gof.demo.worldsrv.config.ConfLevel;
import org.gof.demo.worldsrv.instance.InstanceConstants;
import org.gof.demo.worldsrv.support.enumKey.ConfGlobalKey;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class ChapterMultipleArena extends BaseChapter
{
    public List<UnitGroupCtr> arenaPlayerCtrs = new ArrayList<>();

    public ChapterMultipleArena(BattleMain battleMain) {
        super(battleMain);
    }

    public void destroy()
    {
    }

    public void init()
    {
    }

    public void start() {
        BattleMain t = this.battleMain;
        t.hitThrowDis = false;
        int r = 0;
        for (UnitGroupCtr s : t.playerCtrs)
        {
            r = Math.round(r + s.player.data.level);
        }
        int l = Math.round(r / 2.0f);
        ConfLevel u = ConfLevel.get(l);
        this.battleMain.injuryReduce = u.pvpInjuryReduce / 10000f;
        this.battleMain.shieldDecay = ConfGlobal.get(ConfGlobalKey.shield_correct.SN).value / 10000f;
        this.battleMain.treatDecay = ConfGlobal.get(ConfGlobalKey.hp_recovery_correct.SN).value / 10000f;
    }

    private UnitGroupCtr _addPlayer(float t, int e, PlayerData a, int teamId)
    {
        BattleMain n = this.battleMain;
        UnitGroupCtr i = new UnitGroupCtr();
        i.offsetY = 160;
        i.type = e;
        i.playerData = a;
        int[][] u = ConfChapterType.get(InstanceConstants.KUNGFURACE_CHAPTER_33).player_points;
        if (u.length >= a.Pos)
        {
            i.offsetY = u[a.Pos][1];
            i.offsetX = -u[a.Pos][0];
        }
        List<UnitData> c = a.units;
        for (int y = 0; y < c.size(); y++)
        {
            UnitHuman h;
            if (c.get(y).config().type == UnitType.Player.getValue())
            {
                h = n.unitMgr.addDPVPPlayer(c.get(y), teamId);
                i.player = h;
            }
            else
                h = n.unitMgr.addPlayer(c.get(y), teamId);
            h.direction = 1 == e ? -1 : 1;
            h.data.getAttribMeta(AttribDefine.att_range).setBaseValue(1000);
            h.data.getAttribMeta(AttribDefine.detection_range).setBaseValue(1000);
            h.skillAutoDis = 1000;
            if (h.config().type == UnitType.FlyPet.getValue()) i.player.flyPet = h;
            i.units.add(h);
        }
        i.positionSelected(t + e == 1 ? 720 : 0, true);
        return i;
    }

    public void loadData(float t)
    {
        BattleMain e = this.battleMain;
        Map<Long, PlayerData> a = e.data.playerList;
        for (Map.Entry<Long, PlayerData> r : a.entrySet())
        {
            PlayerData n = r.getValue();
            if (0 == n.team)
            {
                int i = e.data.playerImage ? 1 : 0;
                UnitGroupCtr o = this._addPlayer(t, i, n, 0);
                e.playerCtrs.add(o);
            }
            else
            {
                int s = e.data.playerImage ? 0 : 1;
                UnitGroupCtr l = this._addPlayer(t, s, n, 1);
                this.arenaPlayerCtrs.add(l);
            }
        }
        e.mainCtr = e.playerCtrs.get(0);
        e.showMainCtr = e.playerCtrs.get(0);
        if (e.data.playerImage)
            e.showMainCtr = this.arenaPlayerCtrs.get(0);
    }

    public void onUpdate(float t)
    {
        if (!this.over)
        {
            BattleMain r = this.battleMain;
            int n = 0;
            for (UnitGroupCtr e : r.playerCtrs)
            {
                if (e.player.isDead()) n++;
            }
            if (n >= r.playerCtrs.size())
            {
                this.over = true;
                this.toResult(1);
                return;
            }
            if (!this.updateTime(t))
            {
                n = 0;
                for (UnitGroupCtr o : this.arenaPlayerCtrs)
                {
                    if (o.player.isDead()) n++;
                }
                if (n >= this.arenaPlayerCtrs.size())
                {
                    this.over = true;
                    this.toResult(0);
                }
            }
        }
    }

    @Override
    public boolean updateTime(float t) {
        if (this.timeModel) {
            if (this.chapterTime <= 0) {
                this.over = true;
                BattleMain r = this.battleMain;
                int i = 0;
                int s = 0;
                for(UnitGroupCtr o : this.arenaPlayerCtrs) {
                    if (!o.player.isDead())
                        i = Math.round(i + o.player.data.currenHp);
                }
                for(UnitGroupCtr h : r.playerCtrs) {
                    if (!h.player.isDead())
                        s = Math.round(s + h.player.data.currenHp);
                }
                this.toResult(s >= i ? 0 : 1, true);
                return true;
            }
            this.chapterTime -= t /*Mathf.Round(this.chapterTime - t)*/;
            this.chapterTime = Math.max(this.chapterTime, 0);
        }
        return false;
    }

}
