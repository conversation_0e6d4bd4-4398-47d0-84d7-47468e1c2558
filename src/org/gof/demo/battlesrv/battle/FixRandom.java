package org.gof.demo.battlesrv.battle;

import org.gof.demo.worldsrv.support.Log;

public class FixRandom {

    protected long _seed;

    public FixRandom()
    {
        this._seed = 9;
    }

    public FixRandom(int seed)
    {
        this._seed = seed;
    }

    public int randomInt(int n , int t)
    {
        if (n == t)
            return n;
        int r = t - n;
        long o = roundInt(9301L * this._seed + 49297);
        o %= 233280;
        this._seed = o;
        long u = (long) Math.floor(round(round(o / 233280f) * r));
        u += n;
        u = roundInt(u);
        if (u >= t)
            u = t - 1;
        Log.battle.debug("randomInt seed {} value {}", this._seed, u);
        return (int)u;
    }

    public int Next(int n, int t)
    {
        int v = randomInt(n, t);
        return v;
    }

    public static float round(float t)
    {
        return (float) (t > 0 ? Math.floor(10000 * t + 0.5f) : Math.ceil(10000 * t - 0.5f)) / 10000f;
    }

    public static long roundInt(float t)
    {
        return (long)Math.floor(round(t));
    }
}