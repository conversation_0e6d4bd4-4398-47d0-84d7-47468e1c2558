package org.gof.demo.battlesrv.battle;

import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.enumVo.AttackType;
import org.gof.demo.battlesrv.battle.enumVo.AttribDefine;
import org.gof.demo.battlesrv.battle.enumVo.UnitType;
import org.gof.demo.worldsrv.config.ConfGlobal;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.enumKey.ConfGlobalKey;

public class HurtUtil {
    public static long calHurt(float t, UnitHuman r, UnitHuman a) {
        float i = r.data.getAttrib(AttribDefine.resist);
        float d = r.data.getAttrib(AttribDefine.pve_resist);
        float o = a.data.getAttrib(AttribDefine.pve_dam);
        t = FixRandom.roundInt(t * (1 + o));
        long u = FixRandom.roundInt((t * (1 - i)) * (1 - d));
        Log.battle.debug("calHurt {} -> {}", t, u);
        return Math.max(1, u);
    }

    public static long normalHurt(UnitHuman t, UnitHuman r, int a) {
        float d = t.data.getAttrib(AttribDefine.att);
        float u = r.data.getAttrib(AttribDefine.def);
        float e = r.data.getAttrib(AttribDefine.def_coe);
        float b = t.data.getAttrib(AttribDefine.crit_dam);
        float g = r.data.getAttrib(AttribDefine.crit_def);
        float l = r.data.getAttrib(AttribDefine.att_resist);
        float c = t.data.getAttrib(AttribDefine.att_dam);
        if (t.config().type == UnitType.Partner.getValue()) {
            d = t.parent.data.getAttrib(AttribDefine.att);
            l = r.data.getAttrib(AttribDefine.partner_resist);
            c = t.data.getAttrib(AttribDefine.partner_dam);
            float m = t.parent.data.getAttrib(AttribDefine.partner_dam_extra);
            c = c * m;
        } else if (t.config().type == UnitType.Gun.getValue()) {
            c = t.data.getAttrib(AttribDefine.partner_dam);
            l = t.data.getAttrib(AttribDefine.season_cannon_att_def);
        }
        long s = FixRandom.roundInt(Math.max(d - u * (1 + e), 1) * (c * (1 - l)));
        s = calHurt(s, r, t);
        if (1 != a)
            s = FixRandom.roundInt(s * Math.max(1.5f, (b / g)));
        return Math.max(1, s);
    }

    public static long normalDoubleHurt(UnitHuman t, UnitHuman r, int a) {
        float d = t.data.getAttrib(AttribDefine.att);
        float u = r.data.getAttrib(AttribDefine.def);
        float e = r.data.getAttrib(AttribDefine.def_coe);
        float b = t.data.getAttrib(AttribDefine.crit_dam);
        float g = r.data.getAttrib(AttribDefine.crit_def);
        float l = r.data.getAttrib(AttribDefine.double_hit_def);
        float c = t.data.getAttrib(AttribDefine.double_hit_dam);
        long m = 0L;
        if (t.config().type == UnitType.Partner.getValue()) {
            d = t.parent.data.getAttrib(AttribDefine.att);
            float s = t.data.getAttrib(AttribDefine.partner_dam);
            float A = t.parent.data.getAttrib(AttribDefine.partner_dam_extra);
            s *= A;
            float f = r.data.getAttrib(AttribDefine.partner_resist);
            float M = (Math.max(d - u * (1 + e), 1) * s) * (1 - f);
            m = FixRandom.roundInt(M * c);
        } else if (t.config().type == UnitType.Gun.getValue()) {
            float M = t.data.getAttrib(AttribDefine.partner_dam);
            float h = t.data.getAttrib(AttribDefine.season_cannon_att_def);
            float I = (Math.max(d - u * (1 + e), 1) * M) * (1 - h);
            m = FixRandom.roundInt((M * c) * (1 - l));
        }else
            m = FixRandom.roundInt((Math.max(d - u * (1 + e), 1) * c) * (1 - l));
        m = calHurt(m, r, t);
        if (1 != a)
            m = FixRandom.roundInt(m * Math.max(1.5f, b / g));
        return Math.max(1, m);
    }

    public static long normalCounterHurt(UnitHuman t, UnitHuman r, AttackType a) {
        float i = t.data.getAttrib(AttribDefine.att);
        float d = r.data.getAttrib(AttribDefine.def);
        float m = r.data.getAttrib(AttribDefine.def_coe);
        float u = t.data.getAttrib(AttribDefine.crit_dam);
        float b = r.data.getAttrib(AttribDefine.crit_def);
        float g = r.data.getAttrib(AttribDefine.counter_def);
        float l = t.data.getAttrib(AttribDefine.counter_dam);
        long c = FixRandom.roundInt(Math.max(i - d* (1 + m), 1) * l);
        c = FixRandom.roundInt(c * (1 - g));
        c = calHurt(c, r, t);
        if (1 != a.getValue())
            c = FixRandom.roundInt(c * Math.max(1.5f, (u / b)));
        return Math.max(1, c);
    }

    public static long normailHpsteal(UnitHuman t, UnitHuman r, float a) {
        float i = t.data.getAttrib(AttribDefine.att_hpsteal) - r.data.getAttrib(AttribDefine.att_hpsteal_def);
        i = Math.max(0, i);
        return FixRandom.roundInt(a * i);
    }

    public static long normailHpsteal1(UnitHuman t, UnitHuman r)// r = null
    {
        float a = t.data.getAttrib(AttribDefine.hpsteal_amount);
        float i = t.data.getAttrib(AttribDefine.ignore_hpsteal);
        float d = (a * Math.max(0, (1 - i)));
        float o = t.data.getAttrib(AttribDefine.hp);
        return FixRandom.roundInt(o * d);
    }

    public static long skillHpsteal(UnitHuman t, UnitHuman r, float a) {
        float i = t.data.getAttrib(AttribDefine.skill_hpsteal) - r.data.getAttrib(AttribDefine.skill_hpsteal_def);
        i = Math.max(0, i);
        return FixRandom.roundInt(a * i);
    }

    private static int[] u = new int[3];

    public static AttackType checkHit(UnitHuman t, UnitHuman r)
    {
         return checkHit( t,  r, false);
    }

    public static AttackType checkHit(UnitHuman t, UnitHuman r, boolean i)// i = false
    {
        float o = t.data.getAttrib(AttribDefine.hit);
        float b = i ? 0 : r.data.getAttrib(AttribDefine.miss);
        float g = t.data.getAttrib(AttribDefine.crit_rate);
        float l = Math.max((b - o), 0);
        int c = t.battleMain().random.Next(0, 10000);
        u[AttackType.Miss.getValue()] = (int)FixRandom.roundInt((float) Math.pow(100 * l, ConfGlobal.get(ConfGlobalKey.miss_correct.SN).value / 10000f) / 100f * 10000);
        u[AttackType.Normal.getValue()] = (int)FixRandom.roundInt(u[AttackType.Miss.getValue()] + FixRandom.roundInt((1 - l) * (1 - g) * 10000));
        u[AttackType.Cirt.getValue()] = (int)FixRandom.roundInt(u[AttackType.Normal.getValue()] + FixRandom.roundInt((1 - l) * g * 10000));
        AttackType m = AttackType.Cirt;
//        Log.temp.info("rand: {} cirt: {} miss: {}", c, u[AttackType.Cirt.ordinal()], u[AttackType.Miss.ordinal()]);
        for (int s = 0; s < 2; s++) {
            if (u[s] > 0 && c <= u[s]) {
                m = AttackType.fromValue(s);
                break;
            }
        }
        return m;
    }

    public static boolean checkDoubleAct(UnitHuman t, UnitHuman r) {
        float a = t.data.getAttrib(AttribDefine.double_hit);
        float i = 0;
        if (r != null)
            i = r.data.getAttrib(AttribDefine.ignore_double_hit);
        long d = FixRandom.roundInt(10000 * Math.max((a - i), 0));
        if (d <= 0)
            return false;
        int o = t.battleMain().random.Next(0,10000);
//        Log.battle.info("连击 rand: {} tem: {}", o, d);
        return o <= d;
    }

    public static boolean checkCounterAct(UnitHuman t, UnitHuman r) {
        float a = t.data.getAttrib(AttribDefine.counter);
        float i = r.data.getAttrib(AttribDefine.ignore_counter);
        float d = FixRandom.roundInt(10000 * Math.max((a - i), 0));
        if (d <= 0)
            return false;
        int o = t.battleMain().random.Next(0, 10000);
//        Log.battle.info("反击 rand: {} tem: {}", o, d);
        return o <= d;
    }

    public static boolean checkThrowHit(UnitHuman t, UnitHuman r) {
        float a = t.data.getAttrib(AttribDefine.suspend);
        float i = r.data.getAttrib(AttribDefine.suspend_def);
        float d = FixRandom.roundInt(10000 * (a - i));
        if (d <= 0)
            return false;
        int o = t.battleMain().random.Next(0, 10000);
//        Log.battle.info("击飞 rand: {} tem: {}", o, d);
        return o <= d;
    }

    public static boolean checkCounterThrowHit(UnitHuman t, UnitHuman r) {
        float a = t.data.getAttrib(AttribDefine.counter_suspend);
        float i = r.data.getAttrib(AttribDefine.suspend_def);
        float d = FixRandom.roundInt(10000 * (a - i));
        if (d <= 0)
            return false;
        int o = t.battleMain().random.Next(0, 10000);
//        Log.battle.info("反击击飞 rand: {} tem: {}", o, d);
        return o <= d;
    }

    public static boolean checkDizz(UnitHuman t, UnitHuman r) {
        float i = t.data.getAttrib(AttribDefine.vertigo);
        float d = r.data.getAttrib(AttribDefine.vertigo_def);
        float o = Math.max(0, (i - d));
        if (o <= 0)
            return false;
        float u = (float) Math.pow(100 * o, ConfGlobal.get(ConfGlobalKey.vertigo_correct.SN).value / 10000D) / 100;
        if ((u = FixRandom.roundInt(10000 * u)) <= 0)
            return false;
        int b = t.battleMain().random.Next(0, 10000);
//        Log.battle.info("击晕 rand: " + b + " tem: " + u);
        return b <= u;
    }

    public static boolean checkSkillCirt(UnitHuman t) {
        float r = t.data.getAttrib(AttribDefine.skill_crit_rate);
        float a = FixRandom.roundInt(10000 * r);
        if (a <= 0)
            return false;
        int i = t.battleMain().random.Next(0, 10000);
//        Log.battle.info("击晕 rand: " + i + " tem: " + a);
        return i < a;
    }

    public static boolean checkNormailHpsteal1(UnitHuman t, UnitHuman r) {
        float a = t.data.getAttrib(AttribDefine.hpsteal_rate) - r.data.getAttrib(AttribDefine.hpsteal_res);
        if ((a = (float) FixRandom.roundInt(Math.max(0, 10000 * (a)))) <= 0)
            return false;
        int i = t.battleMain().random.Next(0, 10000);
//        Log.battle.info("击晕 rand: " + i + " tem: " + a);
        return i <= a;
    }
}