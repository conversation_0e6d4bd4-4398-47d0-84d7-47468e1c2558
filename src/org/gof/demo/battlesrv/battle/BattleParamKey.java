package org.gof.demo.battlesrv.battle;

public class BattleParamKey {
    // BattleFlag
    public static int BattleFlag_NONE = 0;
    public static int BattleFlag_NOT_HURT = 1;
    public static int BattleFlag_LIMIT_SKILL = 2;
    public static int BattleFlag_LIMIT_ACT = 4;
    public static int BattleFlag_OPEN_GRAPHIC = 8;
    public static int BattleFlag_SIMP_MODEL = 16;
    public static int BattleFlag_UI_MASK = 32;

    // UnitConfig

    public static String UnitConfig_ANIMATOR_IDLE = "idle";
    public static String UnitConfig_ANIMATOR_MOVE = "run";
    public static String UnitConfig_ANIMATOR_HIT = "hit";
    public static String UnitConfig_ANIMATOR_BORN = "born";
    public static String UnitConfig_ANIMATOR_WIN = "win";
    public static String UnitConfig_ANIMATOR_STUN = "stun";
    public static String UnitConfig_ANIMATOR_DEAD = "dead";
    public static String UnitConfig_ANIMATOR_JIFEI = "knockback" /*"jifei"*/;
    public static String UnitConfig_ANIMATOR_CHUXIAN = "born"/*"chuxian"*/;
    public static int UnitConfig_HATE_WARRING_CAST = 1;
    public static int UnitConfig_HATE_WARRING_TARGET = 2;
    public static int UnitConfig_HATE_HURT_CAST = 4;
    public static int UnitConfig_HATE_HURT_TARGET = 8;
    public static int UnitConfig_HATE_SKILL_CAST = 16;
    public static int UnitConfig_HATE_SKILL_TARGET = 32;
    public static int UnitConfig_HATE_WARRING_EFFECT = 64;


    // AniNames
    public static String AniNames_Run = "run";
    public static String AniNames_Idle = "idle";
    public static String AniNames_Skill = "skill";
    public static String AniNames_Skill_1 = "skill1";

    public static String AniNames_MountTop_Idle = "idle"/*"idle_1"*/;
    public static String AniNames_MountTop_Run = "run"/*"run_1"*/;
    public static String AniNames_MountBottom_Idle = "idle"/*"idle_2"*/;
    public static String AniNames_MountBottom_Run = "run"/*"run_2"*/;

    public static String AniNames_Equip_Attack = "skill1"/*"bigskill"*/;
    public static String AniNames_Equip_Idle = "idle"/*"wuqi"*/;

    public static String AniNames_Wing_Idle = "idle";

    // SkillEffectMark
    public static String SkillEffectMark_Normal = "normal";
    public static String SkillEffectMark_Effect1 = "skillEffect1";
    public static String SkillEffectMark_Effect2 = "skillEffect2";
    public static String SkillEffectMark_Bullet = "trigger_bullet";
    public static String SkillEffectMark_Counter = "counter";
}