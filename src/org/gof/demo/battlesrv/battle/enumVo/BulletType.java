package org.gof.demo.battlesrv.battle.enumVo;

/**
 * 设置读缓存
 * 因为开放后的危险性，暂定屏蔽此功能。
 */
public enum BulletType {
    Liner(1),
    Bezier(2),
    ;
    private final int value;

    BulletType(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static BulletType fromValue(int value) {
        for (BulletType type : BulletType.values()) {
            if (type.value == value) {
                return type;
            }
        }
        return null;
    }
}
