package org.gof.demo.battlesrv.battle.enumVo;

/**
 * 设置读缓存
 * 因为开放后的危险性，暂定屏蔽此功能。
 */
public enum AttribDefine {
    min(1000),
    att(1001),
    hp(1002),
    att_speed(1003),
    crit_rate(1004),
    crit_dam(1005),
    crit_def(1006),
    hit(1007),
    miss(1008),
    speed(1009),
    detection_range(1010),
    att_range(1011),
    hp_recovery(1012),
    power_recovery(1013),
    att_hpsteal(1014),
    skill_hpsteal(1015),
    double_hit(1016),
    counter(1017),
    att_resist(1018),
    skill_resist(1019),
    partner_resist(1020),
    resist(1021),
    suspend(1022),
    vertigo(1023),
    def(1024),
    suspend_def(1025),
    vertigo_def(1026),
    att_hpsteal_def(1027),
    skill_hpsteal_def(1028),
    target_num(1029),
    vertigo_times(1030),
    vertigo_res(1031),
    double_hit_dam(1032),
    counter_dam(1033),
    double_hit_def(1034),
    counter_def(1035),
    counter_suspend(1036),
    skill_crit_rate(1037),
    skill_crit_dam(1038),
    att_dam(1039),
    partner_dam(1040),
    CONTROL_RES(1042),
    active_skillbuff_time(1041),
    active_skilldamage_par (1043) ,
    skill_dam_extra(1045),
    boss_dam(1046),
    partner_dam_extra(1047),
    ignore_double_hit(1048),
    ignore_counter(1049),
    shield_time_extra(1050),
    shield_hp_extra(1051),
    boss_def(1052),
    hpsteal_rate(1053),
    hpsteal_amount(1054),
    hpsteal_res(1055),
    ignore_hpsteal(1056),
    pve_dam(1057),
    pve_resist(1058),
    season_cannon_att_def(1059) ,
    def_coe(1060),
    skillbuff_time_all(1061),
    pve_extra_time(1062),
    power_recovery_buff(1064),
    max(1064),
    ;
    private final int value;

    AttribDefine(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static AttribDefine fromValue(int value) {
        for (AttribDefine type : AttribDefine.values()) {
            if (type.value == value) {
                return type;
            }
        }
        return null;
    }
}
