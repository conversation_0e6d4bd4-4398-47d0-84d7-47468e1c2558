package org.gof.demo.battlesrv.battle.enumVo;

/**
 * 设置读缓存
 * 因为开放后的危险性，暂定屏蔽此功能。
 */
public enum BindType {
    bp_lead(1),
    bp_bottom(2),
    bp_top(3),
    ;
    private final int value;

    BindType(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static BindType fromValue(int value) {
        for (BindType type : BindType.values()) {
            if (type.value == value) {
                return type;
            }
        }
        return null;
    }

}
