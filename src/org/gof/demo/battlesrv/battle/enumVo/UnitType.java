package org.gof.demo.battlesrv.battle.enumVo;

public enum UnitType {
    <PERSON>(1),
    <PERSON>(2),
    <PERSON>(3),
    <PERSON><PERSON><PERSON><PERSON>(4),
    <PERSON>(5),
    <PERSON><PERSON><PERSON><PERSON>(6),
    <PERSON><PERSON><PERSON><PERSON><PERSON>(7),
    <PERSON><PERSON><PERSON><PERSON>(8),
    <PERSON>(10),
    <PERSON>Pet(13),

    ;
    private final int value;

    UnitType(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static UnitType fromValue(int value) {
        for (UnitType type : UnitType.values()) {
            if (type.value == value) {
                return type;
            }
        }
        return null;
    }
}
