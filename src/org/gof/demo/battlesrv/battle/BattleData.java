package org.gof.demo.battlesrv.battle;

import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.interfaces.ISerilizable;
import org.gof.demo.battlesrv.battle.module.UnitData;
import org.gof.demo.worldsrv.msg.Define;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @program: game2
 * @description:
 * @author: Mr.wugz
 * @create: 2024-06-06 18:10
 **/
public class BattleData implements ISerilizable {
    public int serverId = 0;
    public int chapterType = 0; // InstanceConstants.FAMILYBRAWLCHAPTER_14;
    public int chapterId;
    public long seed;
    public int chapterMode = 0;
    public int recordType = 0;
    public String collects;
    public long recordWin;
    public int source = 0;
    public int battleCheckout;
    public int seasonPveDamAdd;
    // public Object key; //
    // public Object index; //

    public String key;
    public String index;

    public Map<Long, PlayerData> playerList = new HashMap<>();
    public boolean playerImage = false;
    public int passTime = 0;

    public List<Define.p_guild_boss_combat> combatList;
    public List<Define.p_battle_hp_state> hpSateList;

    public int openServerDay = 0;
    public List<Integer> buffs; // 使用Integer包装类来替代C#中的int

    public Define.p_battle_operator[] operators;
    public int manualOperator;

    public UnitData monster;
    public UnitData dummy;


    public BattleData() {

    }

    public BattleData(int chapterId) {
        this.chapterId = chapterId;
    }


    public BattleData(String json) {

    }

    @Override
    public void writeTo(OutputStream out) throws IOException {

    }

    @Override
    public void readFrom(InputStream in) throws IOException {

    }

}
