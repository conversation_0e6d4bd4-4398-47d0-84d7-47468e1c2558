package org.gof.demo.battlesrv.battle.bentity.ctroller;

import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.MathUtil;
import org.gof.demo.battlesrv.battle.module.TargetUnit;
import org.gof.demo.battlesrv.battle.enumVo.AttribDefine;
import org.gof.demo.battlesrv.battle.enumVo.PathState;
import org.gof.demo.battlesrv.support.Vector2D;

public class MovementCtr {
    public static class Rangle {
        public float x;
        public float y;

        public Rangle(float x, float y) {
            this.x = x;
            this.y = y;
        }
    }

    protected UnitHuman _owner;
    protected boolean _play;
    protected Vector2D _targetPosition = new Vector2D();
    protected Vector2D _temV2 = new Vector2D();
    protected PathState _state = PathState.Stop;
    protected float _speed = 0;
    public Rangle limitRangle;

    public PathState state(){
        return _state;
    }

    public void init(UnitHuman owner) {
        this._owner = owner;
        this._targetPosition = this._owner.Position;
    }

    public boolean startMove() {
        this._play = this.checkTargetPostion();
        return this._play;
    }

    public void stopPathFinding() {
        this._owner.animatorctr.speed = 1;
        this._targetPosition = this._owner.Position;
        this._state = PathState.Stop;
    }

    public void startPathFinding(Vector2D t) {
        UnitHuman i = this._owner;
        if (this.limitRangle != null) {
            double n = t.x > this.limitRangle.y ? this.limitRangle.y : t.x;
            this._targetPosition = new Vector2D(n, t.y);
        } else
            this._targetPosition = t;

        if (MathUtil.DistanceX(i.Position, this._targetPosition) > .1f)
            this._state = PathState.Seeking;
    }

    public boolean canMove() {
        if (this._owner.statectr.bound <= 0) {
            this._speed = this._owner.data.getAttrib(AttribDefine.speed);
            return this._speed >= .1f;
        }
        return false;
    }

    public void stopMove() {
        if (this._play) {
            this._play = false;
            this.stopPathFinding();
        }
    }

    public void onUpdate(float t) {
        if (!this._play)
            return;
        if (this.checkTargetPostion()) {
            UnitHuman i = this._owner;
            float n = MathUtil.DistanceX(this._targetPosition, i.Position);
            if (n <= .1f) {
                i.Position = this._targetPosition;
                this.stopMove();
            } else {
                float s = Math.min((this._speed * t), n);
                s = (i.direction * s);
                this._temV2 = new Vector2D(s, 0);
                i.Position = this._temV2.Sum(i.Position);
            }
        } else
            this.stopMove();
    }

    public void reset() {
        this.stopPathFinding();
    }

    public boolean checkFormationPostion(UnitHuman t, Vector2D i) {
        UnitHuman n = this._owner;
        if (null == t)
            return false;
        i = t.Position;
        int s = t.radiusSize + n.radiusSize;
        float o = s + n.attackDistance();
        float r = MathUtil.DistanceX(t.Position, n.Position);
        if (r <= o) {
            i = n.Position;
            return false;
        }

        int a = n.Position.x > t.Position.x ? 1 : -1;
        double h = ((r - (o - .1f)) * a);
        i = i.sum(new Vector2D(h, 0));
        return true;
    }

    public boolean checkTargetPostion() {
        UnitHuman t = this._owner;
        if (null == t.curTarget())
            return false;
        if (!this.canMove())
            return false;
        UnitHuman i = t.curTarget().getUnit();
        if (null != i && !i.destroy) t.curTarget().setPos(i.Position);
        Vector2D n = t.curTarget().getPos();
        if (t.curTarget().getType() == TargetUnit.T_UNIT) {
            if (i.dead) {
                t.setCurTarget(null);
                return false;
            }
            if (this.checkFormationPostion(i, n))
                this.startPathFinding(n);
            else
                this.stopPathFinding();
        } else
            this.startPathFinding(n);
        return this._state == PathState.Seeking;
    }
}