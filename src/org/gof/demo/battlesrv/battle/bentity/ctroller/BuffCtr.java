package org.gof.demo.battlesrv.battle.bentity.ctroller;

import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.module.buff.Buff;
import org.gof.demo.battlesrv.battle.enumVo.BuffGroupType;
import org.gof.demo.worldsrv.support.Log;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

public class BuffCtr {
    protected UnitHuman _owner;
    protected List<Buff> _list = new ArrayList<>();

    public void init(UnitHuman owner) {
        this._owner = owner;
    }

    public void reset() {
        // 销毁所有Buff，避免destroy方法中出现删除_list元素的操作导致ConcurrentModificationException
        List<Buff> t = new ArrayList<>(this._list);
//        for (int n = 0; n < t.size(); ++n) {
//            t.get(n).destroy();
//        }
        try {
            for (Buff b : t) {
                if(b != null){
                    b.destroy();
                }
            }
        } catch (Exception e) {
            // 处理单个Buff销毁时可能发生的异常
            Log.temp.error("", e);
        } finally {
            _list.clear();
        }
    }

    public void addBuff(Buff buff) {
        this._list.add(buff);
        buff.owner = this._owner;
        buff.start();
    }

    public Buff getBuff(int t) {
        List<Buff> n = this._list;
//        for (int f = 0; f < n.size(); ++f) {
//            if (n.get(f).config().sn == t)
//                return n.get(f);
//        }
        for(Buff b : n){
            if(b.config().sn == t){
                return b;
            }
        }
        return null;
    }

    public List<Buff> getBuffListById(int t) {
        List<Buff> n = this._list;
        List<Buff> f = new ArrayList<>();
//        for (int r = 0; r < n.size(); ++r) {
//            if (n.get(r).config().sn == t)
//                f.add(n.get(r));
//        }
        for(Buff b : n){
            if(b.config().sn == t){
                f.add(b);
            }
        }
        return f;
    }

    public void stopBuffById(int t) {
        List<Buff> n = this._list;
//        for (int f = 0; f < n.size(); ++f) {
//            if (n.get(f).config().sn == t)
//                n.get(f).stop();
//        }

        for(Buff b : n){
            if(b.config().sn == t){
                b.stop();
            }
        }
    }

    public boolean findBuffByType(int t) {
        List<Buff> n = this._list;
        for (int f = 0; f < n.size(); ++f) {
            if (n.get(f).buffType == t)
                return true;
        }
        return false;
    }

    public List<Buff> getBuffByType(int t) {
        List<Buff> n = this._list;
        List<Buff> f = new ArrayList<>();
        for (Buff b : n) {
            if (b.buffType == t) {
                f.add(b);
            }
        }
        return f;
    }

    public void removeBuff(int t) {
        List<Buff> n = this._list;
        for (Buff b : n) {
            if (b.buffType == t)
                b.stop();
        }
    }

    public void onUpdate(float t) {
        List<Buff> n = this._list;
        for (int f = 0; f < n.size(); ++f) {
            Buff i = n.get(f);
            if (!i.execBuff(t)) {
                i.destroy();
                n.remove(f);
                f--;
            }
        }
    }
}

