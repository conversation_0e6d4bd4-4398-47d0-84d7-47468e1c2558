package org.gof.demo.battlesrv.battle.bentity;
import org.gof.demo.battlesrv.battle.BattleMain;
import org.gof.demo.worldsrv.config.ConfUnit;

import java.util.ArrayList;
import java.util.List;

public class UnitBoss extends UnitHuman {
    public int hpNum;
    public List<ConfUnit> units = new ArrayList<>();
    public long hurtNum;
    protected int _index;

    public UnitBoss(BattleMain battleMain) {
        super(battleMain);
    }

    public int getIndex() {
        return _index;
    }
}
