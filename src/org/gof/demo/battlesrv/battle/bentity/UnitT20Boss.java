package org.gof.demo.battlesrv.battle.bentity;
import org.gof.core.support.Utils;
import org.gof.demo.battlesrv.battle.BattleMain;
import org.gof.demo.battlesrv.battle.enumVo.AttribDefine;
import org.gof.demo.battlesrv.battle.enumVo.DmgType;
import org.gof.demo.battlesrv.battle.module.UnitData;
import org.gof.demo.worldsrv.config.ConfAttribute;
import org.gof.demo.worldsrv.config.ConfUnit;
import org.gof.demo.worldsrv.global.GlobalConfVal;

import java.util.List;

public class UnitT20Boss extends UnitBoss {
    public UnitT20Boss(BattleMain battleMain) {
        super(battleMain);
    }

    @Override
    public void SetHpBar(UnitData _data) {
    }

    @Override
    public void onHpAction(long t, long i) {
        this.hurtNum += t > 0 ? t : 0;
        long n = this.hpNum;
        this._hpAction(t, i);
        //if (0 != i) battleEvent.emit(EventDefine.BossHpChange, this, i);
        //if (this.hpNum != n) battleEvent.emit(EventDefine.UnitHpNumChange, this.hpNum);
        //if (!GlobalDefine.CLIENT_TYPE) this.battleMain.curRecord.addHpState(this);
    }

    private void _hpAction(long t, long i) {
        if (t > 0 && 0 == this.data.currenHp) {
            this.hpNum++;
            this._index++;
            t += i;
            int e = this._index;
            if (e >= this.units.size()) {
                e = this.units.size() - 1;
            }
            ConfUnit s = this.units.get(e);
            for (Integer confAttrSn : GlobalConfVal.propNameSnModule1Map.values()) {
                ConfAttribute o = ConfAttribute.get(confAttrSn);
                this.data.attribs.get(AttribDefine.fromValue(o.sn)).setBaseValue(Utils.floatValue(s.getFieldValue(o.key)));
            }
            this.data.currenHp = this.data.getAttribByInt(AttribDefine.hp);
            if (this.data.currenHp <= t) {
                t -= this.data.currenHp;
                this.data.currenHp = 0;
                this._hpAction(t, 0);
                return;
            }
            this.data.currenHp -= t;
        }
    }

    @Override
    public void onAddDamage(HpAction t, DmgType i, long n) {
        //this.battleMain.curRecord.addDamage(t.Attacker_id, t.Defence_id, i, n, 0);
    }
}
