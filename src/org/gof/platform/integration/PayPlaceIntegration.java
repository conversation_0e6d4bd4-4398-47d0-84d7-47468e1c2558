package org.gof.platform.integration;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import org.gof.core.support.PackageClass;
import org.gof.core.support.Utils;
import org.gof.platform.http.Request;
import org.gof.platform.support.ReasonResult;

import com.alibaba.fastjson.JSONObject;

/**
 * 上层抽象，渠道集成对象，根据不同渠道来做不同处理
 * <AUTHOR>
 *
 */
public abstract class PayPlaceIntegration {
	//各个平台实现的实例
	private static final Map<PayPlaceKey, PayPlaceIntegration> instans = new HashMap<>();

	// 充值失败的提示
	public static final String STATUS_RECEIVE_100 = "100";	//订单重复
	public static final String STATUS_RECEIVE_101 = "101";	//充值礼包不存在
	public static final String STATUS_RECEIVE_102 = "102";	//金额不匹配
	public static final String STATUS_RECEIVE_103 = "103";	//超出充值次数限制

	static {
		init();
	}
	
	/**
	 * 检查登陆信息正确性
	 * @param json
	 * @return
	 */
	public abstract ReasonResult checkToken(JSONObject json);
	
	/**
	 * 检查支付信息正确性
	 * @param json
	 * @return
	 */
	public abstract ReasonResult payCheck(Request req);
	
	/**
	 * 支付返回值（返回给渠道商）
	 */
	public abstract String paySuccess(String reason);
	public abstract void paySuccess(Request req, String reason);
	public abstract String payFail(String reason);
	public abstract void payFail(Request req, String resaon, String deliverCode);
	
	/**
	 * 初始化
	 */
	private static void init() {
		// 获取源文件夹下的所有类
		Set<Class<?>> clazzes = PackageClass.find(true);
		
		// 遍历所有类，取出有注解的生成实体类
		for(Class<?> clazz : clazzes) {
			//非是Integration的子类则过滤
			if(!Utils.isInstanceof(clazz, PayPlaceIntegration.class)) continue;
			// 过滤没有PFConf注解的类
			if(!clazz.isAnnotationPresent(PayPlaceConf.class)) continue;

			// 获取@StageConfig上的stageSn
			PayPlaceConf conf = clazz.getAnnotation(PayPlaceConf.class);
			PayPlaceKey key = conf.value();
			
			instans.put(key, Utils.<PayPlaceIntegration>invokeConstructor(clazz));
		}
	}
	
	/**
	 * 获取实例
	 */
	public static PayPlaceIntegration getInstance(String pfKey) {
		return instans.get(PayPlaceKey.valueOf(pfKey));
	}

	public static PayPlaceIntegration getInstanceBuChannelId(String channelId) {
		return instans.get(PayPlaceKey.getByKey(channelId));
	}
}