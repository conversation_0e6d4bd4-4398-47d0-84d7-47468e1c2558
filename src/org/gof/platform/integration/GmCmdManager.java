package org.gof.platform.integration;

import java.util.List;
import java.util.Map;
import java.util.Random;

import org.apache.commons.lang3.StringUtils;
import org.gof.core.CallPoint;
import org.gof.core.Port;
import org.gof.core.support.*;
import org.gof.core.support.observer.Listener;
import org.gof.platform.ConstPf;
import org.gof.platform.LogPF;
import org.gof.platform.http.HttpAsyncSendServiceProxy;
import org.gof.platform.http.HttpServerHandler;
import org.gof.platform.http.Request;
import org.gof.platform.http.Response;
import org.gof.platform.observer.EventKeyPF;

import com.alibaba.fastjson.JSONObject;

/**
 * GM命令请求
 * <AUTHOR>
 *
 */
public class GmCmdManager extends ManagerBase {
	public static GmCmdManager inst() {
		return inst(GmCmdManager.class);
	}

	/**
	 * GM命令分发入口
	 * @param param
	 */
	@Listener(value=EventKeyPF.HTTP_RECEIVE, subStr=HttpServerHandler.GM_CMD)
	public void onGMCmd(Param param) {
		try {
			_gm(param);
		} catch (Exception e) {
			Request req = param.get("req");
			req.result(HttpServerHandler.RESULT_ERROR_EXE);

			LogPF.platform.error("执行【GM】请求时发生错误={}", param, e);
		}
	}

	@Listener(value=EventKeyPF.HTTP_RECEIVE, subStr=HttpServerHandler.GAME_SERVER)
	public void onGAME_SERVER(Param param) {
		try {
			Request req = param.get("req");
			LogPF.platform.error("收到 {}", req);

			req.params.put("httpIp", req.ip);
			//发送请求
			String json = Utils.toJSONString(req.params);
			LogPF.platform.info("<onGMCmd>: {}", json);

			Port port = Port.getCurrent();
			String nodeId = Distr.NODE_DEFAULT;
			if(S.isAdmin){
				nodeId = Distr.NODE_ADMIN_SERVER;
			}

			CallPoint toPoint = new CallPoint(nodeId, Distr.PORT_DEFAULT, ConstPf.SERV_WORLD_PF);
			port.call(toPoint, 9,"PF9_GAME_SERVER", new Object[]{ json });
			port.listenResult(this::_result_gm, "req", req);
		} catch (Exception e) {
			Request req = param.get("req");
			req.result(HttpServerHandler.RESULT_ERROR_EXE);

			LogPF.platform.error("执行【GAME_SERVER】请求时发生错误={}", param, e);
		}
	}

	private void _gm(Param param) {
		Request req = param.get("req");

		//发送请求
		String json = Utils.toJSONString(req.params);
		LogPF.platform.info("<onGMCmd>: {}", json);

		Port port = Port.getCurrent();
		String nodeId = Distr.NODE_DEFAULT;
		if(S.isAdmin){
			nodeId = Distr.NODE_ADMIN_SERVER;
		}
		CallPoint toPoint = new CallPoint(nodeId, Distr.PORT_DEFAULT, ConstPf.SERV_WORLD_PF);
		port.call(toPoint, 2,"_gm", new Object[]{ json });
		port.listenResult(this::_result_gm, "req", req);
	}
	public void _result_gm(Param results, Param context) {
		Request req = context.get("req");
		boolean success = results.get("success");
		String reason = results.get("reason");
		String para = results.getString("param");
		String server = results.getString("server");
		LogPF.platform.error("success={}, reason={}, params={}", success, reason, para);

		Response res = new Response(success, reason, para);
		res.setServer(StringUtils.isNotBlank(server) ? server : Config.GAME_SERVER_ID);
		req.result(Utils.toJSONString(res));
	}
	/**	查询角色
	 * @param param
	 */
	@Listener(value=EventKeyPF.HTTP_RECEIVE, subStr=HttpServerHandler.QUERY_ROLE)
	public void onQUERY_ROLE(Param param) {
		try {
			Request req = param.get("req");

			//发送请求
			String json = Utils.toJSONString(req.params);
			LogPF.platform.info("<onMonitor>: {}", json);

			//调用远程方法处理
			Port port = Port.getCurrent();
			CallPoint toPoint = new CallPoint(Distr.NODE_DEFAULT, Distr.PORT_DEFAULT, ConstPf.SERV_WORLD_PF);
			port.call(toPoint, 4,"onQUERY_ROLE", new Object[]{ json });
			port.listenResult(this::_result_onQUERY_ROLE, "req", req);
		} catch (Exception e) {
			Request req = param.get("req");
			JSONObject jo = new JSONObject();
			jo.put("errorCode", "00001");
			jo.put("errorDesc", "发生异常");
			req.result(jo.toJSONString());

			LogPF.platform.error("执行【查询角色】时发生错误={}", param, e);
		}
	}
	public void _result_onQUERY_ROLE(Param results, Param context) {
		Request req = context.get("req");

		JSONObject jo = new JSONObject();
		boolean success = results.getBoolean("success");
		List<Map<String, Object>> param = results.get("param");
		jo.put("errorDesc", results.getString("reason"));
		if(success){
			jo.put("errorCode", "00000");
		}else{
			jo.put("errorCode", "00001");
		}

		JSONObject data = new JSONObject();
		data.put("productUsers", param);
		jo.put("data", data);

		req.result(jo.toJSONString());
	}

	/**	查询在线人数
	 * @param param
	 */
	@Listener(value=EventKeyPF.HTTP_RECEIVE, subStr=HttpServerHandler.COUNT_ALL)
	public void onCOUNT_BY(Param param) {
		try {
			Request req = param.get("req");

			//发送请求
			String json = Utils.toJSONString(req.params);
			LogPF.platform.info("<onMonitor>: {}", json);

			Port port = Port.getCurrent();
			CallPoint toPoint = new CallPoint(Distr.NODE_DEFAULT, Distr.PORT_DEFAULT, ConstPf.SERV_WORLD_PF);
			port.call(toPoint, 5,"onCOUNT_BY", new Object[]{ json });
			port.listenResult(this::_result_onCOUNT_BY, "req", req);
		} catch (Exception e) {
			Request req = param.get("req");
			JSONObject jo = new JSONObject();
			jo.put("errorCode", "00001");
			jo.put("errorDesc", "发生异常");
			req.result(jo.toJSONString());

			LogPF.platform.error("执行【查询数量】时发生错误={}", param, e);
		}
	}
	public void _result_onCOUNT_BY(Param results, Param context) {
		Request req = context.get("req");
		int count = results.get("param");

		JSONObject jo = new JSONObject();
		jo.put("count", count);
		jo.put("errorDesc", results.getString("reason"));

		boolean success = results.getBoolean("success");
		if(success){
			jo.put("errorCode", "00000");
		}else{
			jo.put("errorCode", "00001");
		}
		req.result(jo.toJSONString());
	}

	@Listener(value=EventKeyPF.HTTP_RECEIVE, subStr=HttpServerHandler.GM_CMD_PARSE_TYPE2)
	public void onGMCmdBody(Param param) {
//		onGMCmd(param);
		Request req = param.get("req");
		//发送请求
		try {
			//发送请求
			String json = Utils.toJSONString(req.params);

			Port port = Port.getCurrent();
			String nodeId = Distr.NODE_DEFAULT;
			if(S.isAdmin){
				nodeId = Distr.NODE_ADMIN_SERVER;
			}
			CallPoint toPoint = new CallPoint(nodeId, Distr.PORT_DEFAULT, ConstPf.SERV_WORLD_PF);
			port.call(toPoint, 6, "onGMCmdBody", new Object[]{ json });
			port.listenResult(this::_result_gm, "req", req);
		} catch (Exception e) {
			req.result(HttpServerHandler.RESULT_ERROR_EXE);
			LogPF.platform.error("执行【GM】请求时发生错误={}", param, e);
		}
	}


	@Listener(value=EventKeyPF.HTTP_RECEIVE, subStr=HttpServerHandler.GM_CMD_NEED_RESULT)
	public void _GM_CMD_NEED_RESULT(Param param) {
		Request req = param.get("req");
		//发送请求
		try {
			//发送请求
			String json = Utils.toJSONString(req.params);

			Port port = Port.getCurrent();
			String nodeId = Distr.NODE_DEFAULT;
			if(S.isAdmin){
				nodeId = Distr.NODE_ADMIN_SERVER;
			}
			CallPoint toPoint = new CallPoint(nodeId, Distr.PORT_DEFAULT, ConstPf.SERV_WORLD_PF);
			port.call(toPoint, 7, "PF7_GMNeedResult", new Object[]{ json });
			port.listenResult(this::_result_gm, "req", req);
		} catch (Exception e) {
			req.result(HttpServerHandler.RESULT_ERROR_EXE);
			LogPF.platform.error("执行【GM】请求时发生错误={}", param, e);
		}
	}
}
