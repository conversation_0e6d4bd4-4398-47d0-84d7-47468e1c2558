package org.gof.platform.http;

import com.alibaba.fastjson.JSONObject;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.handler.codec.http.*;
import io.netty.handler.codec.http.multipart.*;
import io.netty.handler.codec.http.multipart.HttpPostRequestDecoder.EndOfDataDecoderException;
import io.netty.handler.codec.http.multipart.InterfaceHttpData.HttpDataType;
import org.gof.core.support.S;
import org.gof.core.support.SysException;
import org.gof.core.support.Utils;
import org.gof.platform.HttpPort;
import org.gof.platform.LogPF;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.atomic.AtomicInteger;

public class HttpServerHandler extends SimpleChannelInboundHandler<HttpObject> {

	//各个GM接口路径
	public static final String LOGIN_CHECK = "/loginCheck";		//登陆检查
	public static final String PAY_NOTIFY = "/payNotify";				//充值
	public static final String CSSC = "/CSSC";								//蓝版平台【GM平台转发】

	public static final String QUERY_ROLE = "/queryRole";			//玩家查询【GM平台接口】
	public static final String GM_CMD = "/GM";							//玩家查询【GM平台接口】
	public static final String GM_CMD_PARSE_TYPE2 = "/GMParseType2";	// GM指令，跟GM_CMD不同的是GM_CMD直接解析url上面的参数，不会解析http的body，这个从body解析参数
	public static final String GM_CMD_NEED_RESULT = "/GMNeedResult";	// 与GM_CMD_PARSE_TYPE2类似，但带返回，可能会超时，具体看处理事务
	public static final String COUNT_ALL = "countAll";					//查询在线人数【GM平台接口】

	public static final String CHECK_SERVER = "/checkServer";		//检查服务

	public static final String GAME_SERVER = "/gameServer";		//游戏服务注册

	//成功统一回复
	public static final String RESULT_OK = "{\"code\": 1, \"reason\": \"完成请求\"}";
	//错误请求地址提示
	public static final String RESULT_ERROR_PATH = "{\"errorCode\": 11111, \"errorDesc\": \"地址错误\"}";
	public static final String RESULT_ERROR_EXE = "{\"errorCode\": 00001, \"errorDesc\": \"处理请求发生错误\"}";

	//请求编号
	private static final AtomicInteger httpNo = new AtomicInteger();
	//数据工厂类
	private static final HttpDataFactory factory = new DefaultHttpDataFactory(DefaultHttpDataFactory.MINSIZE);

    @Override
	public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
		super.channelRead(ctx, msg);
	}

	@Override
    protected void channelRead0(ChannelHandlerContext ctx, HttpObject msg) throws InterruptedException, IOException {
		if (msg instanceof HttpRequest) {
			HttpRequest request = (HttpRequest) msg;
//		if (msg instanceof HttpRequest) {
//			HttpRequest request = (HttpRequest) msg;
			//TODO 由于项目部署在docker或者k8s上,项目与项目之间不会互相调用,故此备注掉IP过滤
			//IP过滤
			/*String filterResult = HttpIpFilter.chekIp(ctx, request);
			if(filterResult != null){
				Request req = new Request();
				req.channel = ctx.channel();
				req.result(RESULT_ERROR_PATH);
				return;
			}*/

			// 解析request请求参数
			RequestData data = new RequestData();
			data.uri = request.uri();
			String clientIP = request.headers().get("X-Forwarded-For");
			//获取远程IP得至
			if (clientIP == null) {
				InetSocketAddress insocket = (InetSocketAddress) ctx.channel().remoteAddress();
				clientIP = insocket.getAddress().getHostAddress();
			}
			data.ip = clientIP;
	        QueryStringDecoder query = new QueryStringDecoder(data.uri);

	        //打印收到的参数
	        if(LogPF.platform.isInfoEnabled()) {
	        	LogPF.platform.debug("[{}]收到HTTP请求:{}", ctx.channel().hashCode(), query.parameters());
	        }
			LogPF.platform.info("[{}]收到HTTP请求:{} {}", ctx.channel().hashCode(), query.parameters(), S.isAdmin);
			//请求地址
			data.action = query.path();

			//处理POST请求
			if(request.method().equals(HttpMethod.POST)) {


				// POST请求有数据，则继续处理
				if(msg instanceof HttpContent) {
					HttpContent chunk = (HttpContent) msg;
					RequestKey key = RequestKey.getKeyByAction(data.action);
					//解析类型，
					switch(key.getParseType()){
						case 1:		//1是requestParam，明码参数，带名字的
							// 解析POST的
							data.params = new HashMap<>();
							HttpPostRequestDecoder decoder = new HttpPostRequestDecoder(request);
							decoder.setDiscardThreshold(0);
							for (InterfaceHttpData dataItem : decoder.getBodyHttpDatas()) {
								if(dataItem.getHttpDataType()==HttpDataType.Attribute){
									Attribute attribute = (Attribute) dataItem;
									data.params.put(attribute.getName(), attribute.getValue());
								}
							}
//							parseParam(decoder, data, chunk);
							break;
						case 2:		//2是request Body 从流中读取json
								ByteBuf content = ((FullHttpRequest)request).content();
								String jsonStr = content.toString(StandardCharsets.UTF_8);
								LogPF.platform.info("received post msg:{}",jsonStr);
								JSONObject jo = Utils.toJSONObject(jsonStr);
								data.params = new HashMap<String, String>();
								for(Entry<String, Object> en : jo.entrySet()){
									data.params.put(en.getKey(), en.getValue().toString());
								}
//								parseBody(data, chunk);
							break;
					}
					LogPF.platform.info("POST请求：uri={}, param={}", data.action, data.params);
					//处理逻辑
					doRequest(ctx, data);
				}
			}
	    	//处理GET请求
	    	else {
	    		//请求参数
	    		data.params = new HashMap<>();

	            //解析参数
	            Map<String, List<String>> ps = query.parameters();
	            for (Entry<String, List<String>> p: ps.entrySet()) {
	                String key = p.getKey();
	                //如果有多个值 那么只有最后一个会生效
	                List<String> vals = p.getValue();
	                for (String val : vals) {
	                	data.params.put(key, val);
	                }
	            }
				LogPF.platform.info("GET请求：uri={}, param={}", request.getUri(), data.params);
	            doRequest(ctx, data);
	    	}
		}
    }

	/**
	 * 解析Http信息中的body为Json
	 * @param requestData
	 * @param chunk
	 */
	private void parseBody(RequestData requestData, HttpContent chunk) {
		ByteBuf buffer = chunk.content();
		byte[] dst = new byte[buffer.readableBytes()];
		//读出上传信息
		buffer.readBytes(dst);
		String param = new String(dst);
		JSONObject jo = Utils.toJSONObject(param);
		requestData.params = new HashMap<String, String>();
		for(Entry<String, Object> en : jo.entrySet()){
			requestData.params.put(en.getKey(), en.getValue().toString());
		}
	}

	/**
	 * 解析http中的request param的键值对参数
	 * @param requestData
	 * @param chunk
	 * @throws InterruptedException
	 */
	private void parseParam(HttpPostRequestDecoder decoder, RequestData requestData, HttpContent chunk) throws InterruptedException {
		decoder.offer(chunk);
		//请求参数
		requestData.params = new HashMap<>();

		//挨个处理参数
		try {
			for (InterfaceHttpData data : decoder.getBodyHttpDatas()) {
				if (data != null) {
					try {
						//处理参数
						if (data.getHttpDataType() == HttpDataType.Attribute) {
							Attribute attribute = (Attribute) data;
							requestData.params.put(attribute.getName(), attribute.getValue());
						}
					} catch(Exception e) {
						throw new SysException(e);
					} finally {
						data.release();
					}
				}
			}

		} catch (EndOfDataDecoderException e1) {
			//忽略这种异常
		}
	}

	/**
	 * 处理POST或者GET请求
	 * @param ctx
	 * @param data
	 * @throws InterruptedException
	 */
	public void doRequest(ChannelHandlerContext ctx, RequestData data) throws InterruptedException {
		//收到的请求信息
        Request req = new Request();
        req.id = httpNo.incrementAndGet();
        req.params = data.params;
    	req.handler = this;
    	req.key = RequestKey.getKeyByAction(data.action);
    	req.uri = data.uri;
		req.ip = data.ip;
    	req.channel = ctx.channel();

    	//如果错误的请求地址
        if(req.key == null) {
        	req.result(RESULT_ERROR_PATH);
        }

		//如果是充值，增加渠道标识
		if(data.action.contains(RequestKey.PAY_NOTIFY.getAction())) {
			//渠道KEY
			req.params.put("pfKey", data.payKey);
		}

		//发送请求对象
		HttpPort.addRequest(req);
	}

	@Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        LogPF.platform.error("", cause);
        ctx.close();
    }

    @Override
    public void channelReadComplete(ChannelHandlerContext ctx) throws Exception {
        ctx.flush();
    }
}
