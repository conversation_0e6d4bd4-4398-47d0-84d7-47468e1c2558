/**
 * 
 */
package org.gof.platform.sdk;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.gof.core.support.Config;
import org.gof.core.support.SysException;
import org.gof.core.support.Utils;
import org.gof.platform.LogPF;
import org.gof.platform.http.Request;
import org.gof.platform.integration.PayPlaceIntegration;
import org.gof.platform.integration.PayPlaceConf;
import org.gof.platform.integration.PayPlaceKey;
import org.gof.platform.support.ReasonResult;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.quartz.impl.jdbcjobstore.Util;

@PayPlaceConf(PayPlaceKey.zhangqu)
public class PlaceOurpalm extends PayPlaceIntegration{

	private static final String CHECK_TOKEN_URL = "http://auth.gamebean.net/ucenter/uMember/userLoginMgr.do";
	private static final String CHECK_QQ_TOKEN_URL = "http://auth03.gamebean.net/ucenter/uMember/userLoginMgr.do";
	private static final String IPID = "0002";
	
	public static final String APPID = "1001";//申请app时进行替换
	public static final String SECURITY_KEY = "d44aad69bb50d9bb321fa1298c1cdeed";//申请app时进行替换
	
	public static final int SUCCESS = 0;
	public static final int FAIL = 1;
	public static final int OTHER_ERROR = -99;
	
	public static final String STATUS_RECEIVE = "0001";	//收到计费服务器的请求
	public static final String STATUS_RECEIVE_DESC = "发货中";	//
	public static final String STATUS_SUCCESS = "0002";	//添加物品成功
	public static final String STATUS_SUCCESS_DESC = "发送成功";	//成功描述

	
	public static final String STATUS_FAIL = "1005";	//发货失败
	public static final String STATUS_FAIL_DESC = "充值失败";	//失败描述

	public static void main(String[] args) {
		checkToken("0", "902363686620150108184440790100006");
	}

	/**
	 * @param userIdentity
	 * @param token
	 * @param appId
	 * @return
	 * @throws IOException
	 * @throws ClientProtocolException
	 */
	public static int checkToken(String userIdentity, String token) {

		JSONObject jo = new JSONObject();
		jo.put("interfaceId", IPID);
		jo.put("tokenId", token);

		Map<String, String> params = new HashMap<String, String>();
		params.put("jsonStr", jo.toJSONString());

		String result = checkToken(params);

		System.out.println("Receive the remote Indentity:["+result+"].");
		return parseChargeResult(result, userIdentity, token);
	}

	/**
	 * @param result
	 * @return
	 */
	public static int parseChargeResult(String result, String id, String token) {
		Map<String, Object> resMap = JSON.parseObject(result);
		if(resMap != null){
			
			// 判断返回值状态
			Object code = resMap.get("status");
			int intCode = Integer.parseInt((String)code);

			//验证UserId
			boolean passed = false;
			Object userInfo = resMap.get("userInfo");
			Object tokenObj = resMap.get("tokenId");
			//验证token
			if(tokenObj != null && tokenObj.toString().equals(token) )
				passed = true;
			
			if(userInfo != null){
				Map<String, Object> userMap = JSON.parseObject(userInfo.toString());
				String skdId = userMap.get("id").toString();
				if(skdId.equals(id)){
				}
				if(!passed) {
					System.out.println("Account:[" + id + "];  Passed:[" + passed+"].");
					System.out.println("Receive the remote Indentity:["+result+"].");
				}
			}
			if(intCode == 0 && passed){
				return SUCCESS;
			} else {
				return FAIL;
			}
		}
		return OTHER_ERROR;
	}

	/**
	 * 验证登陆信息
	 * @return
	 */
	private static String checkToken(Map<String, String> params) {
		try {
			StringBuilder urlSB;
			//1 拼接地址
			if("qq".equals(Config.GAME_PLATFORM_NAME.toLowerCase())){
				urlSB = new StringBuilder(CHECK_QQ_TOKEN_URL); //CHECK_QQ_TOKEN_URL CHECK_TOKEN_URL
			} else {
				urlSB = new StringBuilder(CHECK_TOKEN_URL); //CHECK_QQ_TOKEN_URL CHECK_TOKEN_URL
			}
			
			//1.1 有需要拼接的参数
			if(!params.isEmpty()) {
				urlSB.append("?");
			}
			
			//1.2 拼接参数
			for(Entry<String, String> entry : params.entrySet()) {
				Object value = entry.getValue();
				String v = (value == null) ? "" : URLEncoder.encode(entry.getValue(), "UTF-8");
				
				urlSB.append(entry.getKey()).append("=").append(v).append("&");
			}
			
			//1.3 最终地址
			String urlStrFinal = urlSB.toString();
			
			//1.4 去除末尾的&
			if(urlStrFinal.endsWith("&")) {
				urlStrFinal = urlStrFinal.substring(0, urlStrFinal.length() - 1);
			}
			
			//请求地址
			HttpGet get = new HttpGet(urlStrFinal);
//			LogCore.core.info("urlStrFinal:[" + urlStrFinal + "].");
			//准备环境
			try(CloseableHttpClient http = HttpClients.createDefault();
				CloseableHttpResponse response = http.execute(get);) {

				//返回内容
			    HttpEntity entity = response.getEntity();
			    
			    //主体数据
			    InputStream in = entity.getContent();  
			    BufferedReader reader = new BufferedReader(new InputStreamReader(in, "UTF-8"));
			    //读取
			    StringBuilder sb = new StringBuilder();
			    String line = null;  
			    while ((line = reader.readLine()) != null) {  
			    	sb.append(line);
			    }
			    
			    reader.close();
			    reader = null;
			    
			    return sb.toString();
			}
		} catch (Exception e) {
			throw new SysException(e);
		}
	}

	@Override
	public ReasonResult checkToken(JSONObject json) {
		//验证信息
		String userIdentity = json.getString("userIdentity");
		String token = json.getString("token");
		int result = checkToken(userIdentity, token);
		//返回结果
		ReasonResult rt = new ReasonResult();
		rt.success = (result == 0);
		rt.code = result;
		rt.reason = "";
		
		return rt;
	}

	@Override
	public ReasonResult payCheck(Request req) {
		LogPF.platform.info("payCheck");
		ReasonResult result = new ReasonResult();
		result.success = true;
		
//		JSONObject jo = new JSONObject();
//		JSONObject status = new JSONObject();
//		status.put("deliverCode", STATUS_RECEIVE);
//		status.put("deliverDesc", "发货中");
//		jo.put("common", status);
//		//返回给计费服务器
//		req.result(Utils.toJSONString(jo));
		
		return result;
	}

	@Override
	public String paySuccess(String reason) {
		return null;
	}

	@Override
	public String payFail(String reason) {
		return null;
	}

	@Override
	public void paySuccess(Request req, String reason) {
		JSONObject result = new JSONObject();
		result.put("deliverCode", STATUS_SUCCESS);
		result.put("deliverDesc", STATUS_SUCCESS_DESC);
		result.put("orderId", req.params.get("orderId"));
		result.put("reason", reason);

		req.result(Utils.toJSONString(result));
		LogPF.platform.info("POST返回：result={}", result);
	}

	@Override
	public void payFail(Request req, String reason, String deliverCode) {
		JSONObject result = new JSONObject();
		result.put("deliverCode", StringUtils.isEmpty(deliverCode) ? STATUS_RECEIVE : deliverCode);
		result.put("deliverDesc", reason);
		result.put("orderId", req.params.get("orderId"));

		req.result(Utils.toJSONString(result));
		LogPF.platform.info("POST返回：result={}", result);
	}
}
