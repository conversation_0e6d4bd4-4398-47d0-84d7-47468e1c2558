package org.gof.platform.chat;

import java.util.List;
import java.util.Map;

import org.gof.core.Port;
import org.gof.core.Service;
import org.gof.core.gen.proxy.DistrClass;
import org.gof.core.gen.proxy.DistrMethod;
import org.gof.core.support.Config;
import org.gof.core.support.Utils;
import org.gof.platform.ConstPf;
import org.gof.platform.LogPF;
import org.gof.platform.http.HttpClient;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;


@DistrClass(servId = ConstPf.SERV_CHAT, importClass = { List.class })
public class ChatService extends Service {
	
	public ChatService(Port port) {
		super(port);
	}

	@Override
	public Object getId() {
		return ConstPf.SERV_CHAT;
	}
	
	@Override
	public void pulseOverride() {
		
	}
	
	/**
	 * 上传聊天记录
	 * @param loginJSON
	 */
	@DistrMethod
	public void uploadChat(List<Map<String, String>> chatList) {
		JSONObject upData = new JSONObject();
		
		JSONArray ja = new JSONArray();
		for(Map<String, String> m : chatList){
			ja.add(m);
		}
		
		upData.put("service", ConstPf.HTTP_CHATSERVER_SERVICE);
		upData.put("productId", 10000941);
		upData.put("serverId", Config.GAME_SERVER_ID);
		upData.put("data", ja);
		
		HttpClient clinet = new HttpClient(ConstPf.HTTP_CHATSERVER_IP);
		LogPF.platform.info("上传聊天记录{}条", chatList.size());
		LogPF.platform.info(Utils.createStr("返回参数：{}", clinet.doPost(upData.toJSONString())));
	}
	
}
