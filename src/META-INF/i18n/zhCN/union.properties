#\u5175\u56e2

union.error.commonFullLevel=\u5df2\u5230\u8fbe\u6700\u9ad8\u7b49\u7ea7
union.error.noUnion=\u672a\u627e\u5230\u5175\u56e2
union.error.noUnionName=\u8bf7\u8f93\u5165\u5175\u56e2\u540d\u79f0
union.error.repeatedUnionName=\u5175\u56e2\u540d\u79f0\u5df2\u88ab\u4f7f\u7528
union.error.expNotEnough=\u5175\u56e2\u7ecf\u9a8c\u4e0d\u8db3
union.error.coinNotEnough=\u5175\u56e2\u8d44\u91d1\u4e0d\u8db3
union.error.alreadyJoinCantCreate=\u4f60\u5df2\u5728\u4e00\u4e2a\u5175\u56e2\u4e2d\uff0c\u4e0d\u80fd\u521b\u5efa
union.error.alreadyJoin=\u5df2\u7ecf\u52a0\u5165\u4e86\u4e00\u4e2a\u5175\u56e2
union.error.levelNotEnough=\u7b49\u7ea7\u5230\u8fbe {} \u7ea7\u4ee5\u4e0a\u624d\u80fd\u521b\u5efa\u5175\u56e2
union.error.cantCreate=\u4e0d\u80fd\u521b\u5efa\u5175\u56e2
union.error.repeatedName=\u5175\u56e2\u540d\u79f0\u5df2\u7ecf\u5b58\u5728
union.error.notJoinAny=\u8fd8\u6ca1\u6709\u52a0\u5165\u5175\u56e2
union.error.alreadyApply=\u5df2\u53d1\u9001\u8fc7\u7533\u8bf7
union.error.exceedMaxApplyNum=\u540c\u65f6\u53ea\u80fd\u5411 {} \u4e2a\u5175\u56e2\u53d1\u51fa\u7533\u8bf7
union.error.decideFail=\u5bf9\u65b9\u5df2\u52a0\u5165\u4e00\u4e2a\u5175\u56e2
union.error.cantQuit=\u5f53\u524d\u804c\u4f4d\u4e0d\u80fd\u79bb\u5f00\u5175\u56e2
union.error.noApplyRecord=\u672a\u627e\u5230\u7533\u8bf7\u8bb0\u5f55
union.error.noHumanMember=\u672a\u627e\u5230\u5175\u56e2\u6210\u5458
union.error.isNotMember=\u8fd8\u672a\u52a0\u5165\u8be5\u5175\u56e2
union.error.unionNameLengthMax=\u5175\u56e2\u540d\u79f0\u6700\u591a {} \u4e2a\u5b57\u7b26
union.error.unionLevelMax=\u5175\u56e2\u7b49\u7ea7\u5df2\u5230\u6ee1\u7ea7
union.error.cantOperate=\u5f53\u524d\u804c\u4f4d\uff0c\u4e0d\u80fd\u8fd9\u6837\u64cd\u4f5c
union.error.humanNumMax=\u5175\u56e2\u4eba\u6570\u5df2\u6ee1
union.error.humanLevelNotEnough=\u7b49\u7ea7\u4e0d\u6ee1 {}\u7ea7 \u4e0d\u80fd\u4f7f\u7528\u5175\u56e2\u529f\u80fd
union.error.noBoss=\u6ca1\u6709\u53d1\u73b0\u5175\u56e2\u795e\u517d
union.error.moneyNotEnough=\u5175\u56e2\u8d44\u91d1\u4e0d\u8db3
union.error.expCantAddZero=\u589e\u52a0\u7684\u5175\u56e2\u7ecf\u9a8c\u4e0d\u80fd\u5c0f\u4e8e\u7b49\u4e8e0
union.error.noticeMax=\u516c\u544a\u5185\u5bb9\u8fc7\u957f
union.error.inviteOffLine=\u4e0d\u80fd\u9080\u8bf7\u79bb\u7ebf\u73a9\u5bb6
union.error.scienceActived=\u79d1\u6280\u5df2\u7ecf\u6fc0\u6d3b
union.error.scienceNotActive=\u79d1\u6280\u6ca1\u6709\u6fc0\u6d3b
union.error.scienceUnionLevelNotEnough=\u5175\u56e2\u7b49\u7ea7\u9700\u8fbe\u5230 {} \u7ea7
union.error.cantEntryUnionStage=\u5f53\u524d\u573a\u666f\u4e0d\u80fd\u8fdb\u5165\u5175\u56e2\u9a7b\u5730

union.tips.monsterAttack=\u602a\u7269\u6b63\u5728\u5165\u4fb5\u5175\u56e2\u9a7b\u5730\uff0c\u5c11\u5e74\u4eec\u901f\u901f\u652f\u63f4
union.tips.monsterAttackAgain=\u602a\u7269\u6765\u4e86\uff0c\u5c11\u5e74\u4eec\u53d8\u8eab\u5427!
union.tips.monsterAttackOver=\u602a\u7269\u5df2\u7ecf\u9000\u53bb\uff0c\u5b83\u4eec\u8fd8\u4f1a\u5377\u571f\u91cd\u6765\u5417\uff1f

union.log.funcUpgrade=\u5175\u56e2\u5347\u7ea7

union.log.coinAdd={} \u5175\u56e2\u8d44\u91d1\u589e\u957f\u4e86 {}
union.log.create={} \u521b\u5efa\u4e86\u5175\u56e2
union.log.join={} \u52a0\u5165\u4e86\u5175\u56e2
union.log.quit={} \u79bb\u5f00\u4e86\u5175\u56e2
union.log.jobChange={} \u5c06 {} \u7684\u804c\u4f4d\u53d8\u66f4\u4e3a {}
union.log.upgrade=\u5175\u56e2\u7b49\u7ea7\u5347\u5230 {} \u7ea7
union.log.moneyReduce={} \u6d88\u8017\u5175\u56e2\u8d44\u91d1 {}

#\u5175\u56e2\u6218
union.error.signupLeague.timeError=\u62a5\u540d\u65f6\u95f4\u5df2\u8fc7
union.error.signupLeague.already=\u5175\u56e2\u5df2\u7ecf\u62a5\u8fc7\u540d\u4e86
union.error.enterMatch.noMatch=\u4f60\u7684\u5175\u56e2\u73b0\u5728\u6ca1\u6709\u6b63\u5728\u8fdb\u884c\u7684\u6bd4\u8d5b
union.match.broadcast.noMatch=\u5175\u56e2\u6218\uff1a {} \u8f6e\u7a7a
union.match.broadcast.against=\u5175\u56e2\u6218\uff1a {} vs {}
union.match.broadcast.result=\u5175\u56e2\u6218\uff1a {} \u6218\u80dc {}
union.matchStage.broadcast.start=\u6218\u6597\u5f00\u59cb
union.matchStage.broadcast.end=\u6218\u6597\u7ed3\u675f