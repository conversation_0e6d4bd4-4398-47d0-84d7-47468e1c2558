# 合成大西瓜活动实现说明

## 已实现的功能

### 1. 数据结构
- **ControlFruitMergeData**: 合成大西瓜活动数据类
  - 体力管理器 (ControlStaminaManager)
  - 当前分数、最大分数、累计分数
  - 总消耗体力记录
  - 游戏状态 (p_fruit_merge_state)
  - 道具使用次数记录

### 2. 活动控制器
- **ActivityControlFruitMerge**: 合成大西瓜活动控制器
  - 活动信息获取 (on_act_fruit_merge_info_c2s)
  - 游戏结束处理 (on_act_fruit_merge_end_c2s)
  - 体力消耗处理 (on_act_fruit_merge_stamina_cost_c2s)
  - 道具使用处理 (on_act_fruit_merge_use_item_c2s)
  - 状态上报处理 (on_act_fruit_merge_state_c2s)
  - 体力刷新 (on_act_stamina_refresh_c2s)

### 3. 消息处理
- **ActivityMsgHandler**: 添加了所有合成大西瓜相关的消息接收器
- **ActivityManager**: 添加了对应的消息处理方法

### 4. 活动注册
- **ActivityControlTypeFactory**: 注册了活动类型5015到合成大西瓜控制器

## 核心功能实现

### 体力管理
- 使用 ControlStaminaManager 管理体力
- 支持体力消耗、恢复、道具补充
- 从 ActivityStamina_0 配置表读取体力相关配置

### 排行榜功能
- 支持分数排行榜
- 获取玩家排名
- 获取前100名分数列表

### 任务系统集成
- 合成水果时更新任务进度
- 使用 TaskConditionTypeKey.TASK_TYPE_2001 作为任务条件

### 数据持久化
- 使用 protobuf 序列化游戏数据
- 支持 LZ4 压缩存储

## 主要特性

### 1. 客户端驱动
- 游戏逻辑在客户端计算
- 服务器主要处理资源扣除和验证
- 分数由客户端上报

### 2. 体力系统
- 服务器对比客户端上报的总消耗体力
- 只扣除差值体力，避免重复扣除
- 支持体力道具使用

### 3. 道具管理
- 客户端请求使用道具
- 服务器验证并扣除道具
- 记录道具使用次数

### 4. 排行榜集成
- 游戏结束时更新排行榜
- 支持跨服排行榜
- 实时获取排名信息

## 配置依赖

### 必需的配置表
1. **ActivityStamina_0**: 活动体力配置
   - act_type: 5015
   - initial_stamina: 初始体力
   - max_stamina: 最大体力
   - stamina_recover: [恢复间隔秒, 每次恢复量]

2. **ConfActivityTerm**: 活动期数配置
   - rank_id: 排行榜ID

3. **ConfRanktype**: 排行榜类型配置

## 消息协议

### 客户端到服务器 (C2S)
- `act_fruit_merge_info_c2s`: 获取活动信息
- `act_fruit_merge_end_c2s`: 结束游戏
- `act_fruit_merge_stamina_cost_c2s`: 消耗体力
- `act_fruit_merge_use_item_c2s`: 使用道具
- `act_fruit_merge_state_c2s`: 上报状态

### 服务器到客户端 (S2C)
- `act_fruit_merge_info_s2c`: 活动信息响应
- `act_fruit_merge_end_s2c`: 游戏结束响应
- `act_stamina_refresh_s2c`: 体力刷新响应

## 使用方式

### 1. 配置活动
在 ActivityStamina_0 表中添加配置：
```
act_type: 5015
group_id: 1
initial_stamina: 10
max_stamina: 10
stamina_recover: [3600, 1]  // 1小时恢复1点
```

### 2. 客户端集成
- 实现游戏逻辑
- 定期上报游戏状态
- 游戏结束时上报最终分数
- 消耗体力时通知服务器

### 3. 任务配置
使用 TaskConditionTypeKey.TASK_TYPE_2001 配置合成相关任务

## 注意事项

1. **安全性**: 虽然逻辑在客户端，但服务器会验证体力消耗和道具使用
2. **性能**: 游戏状态上报不要过于频繁
3. **数据一致性**: 确保客户端和服务器的体力计算一致
4. **排行榜**: 分数更新会影响排行榜，注意性能影响

## 扩展建议

1. 添加反作弊机制
2. 增加游戏回放功能
3. 支持更多道具类型
4. 添加成就系统集成
