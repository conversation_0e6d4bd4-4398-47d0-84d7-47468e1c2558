new.db.schema=kumo_admin_redis
new.db.host=kumo-biz-mysql.pro.g123-cpp.com
new.db.port=3306
new.db.user=kumo_biz
new.db.pwd=4X8$QNhl44dO89#9
new.redis=redis://:<EMAIL>:6379
new.db.pool=40
new.redis.pool.size=20
new.redis.waiting.handlers=1000000

# 一个线程处理几张表
thread.processor.num=5
# 部分查询处理时是否每隔一段时间让线程暂停一会儿
thread.need.sleep=false
# 线程暂停间隔
thread.sleep.interval=500
# 部分批量查询一次查询的数量
query.once.count=500
# 是否包含更新数据(false代表只补插入数据库没有的数据，true代表还要全部更新数据)
update.all.data=false
# 更新或者插入200条数据，就让线程暂停thread.sleep.interval豪秒
db.write.interval.num=200