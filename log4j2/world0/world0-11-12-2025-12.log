2025-11-12 14:48:19,503 (WorldStartup.java:72) [INFO][GAME] 正在启动游戏服务器
2025-11-12 14:48:19,559 (WorldStartup.java:86) [INFO][GAME] 正在初始化事件容器
2025-11-12 14:48:20,092 (WorldStartup.java:89) [INFO][GAME] 正在初始化协议函数指针池
2025-11-12 14:48:24,220 (WorldStartup.java:97) [INFO][GAME] 加载策划数据
2025-11-12 14:48:25,298 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfBreakBigPrizePreview
2025-11-12 14:48:26,821 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfconfigAngel
2025-11-12 14:48:27,054 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfDoubleDraw
2025-11-12 14:48:27,478 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfFlyAdvance_0
2025-11-12 14:48:27,483 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfFlyCd_0
2025-11-12 14:48:27,502 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfFlyEntry_0
2025-11-12 14:48:27,505 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfFlyEvolutionPro_0
2025-11-12 14:48:27,520 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfFlyHybrid_0
2025-11-12 14:48:27,541 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfFlyLevel_0
2025-11-12 14:48:27,550 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfFly_achievement
2025-11-12 14:48:27,551 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfFly_advance_0
2025-11-12 14:48:29,245 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfMonopolyGridLevel
2025-11-12 14:48:29,898 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfSeasonEquipment
2025-11-12 14:48:29,935 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfSeasonShipAppearance
2025-11-12 14:48:30,044 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfSeverActivityGroup
2025-11-12 14:48:30,045 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfSeverGroup
2025-11-12 14:48:30,253 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfSlimeDungeon
2025-11-12 14:48:30,858 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfWuzhiLove
2025-11-12 14:48:30,858 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfWuzhiLoveLevel
2025-11-12 14:48:31,480 (GlobalConfVal.java:1318) [INFO][TEMP] ===加载副本表数量=70
2025-11-12 14:48:32,370 (GlobalConfVal.java:659) [INFO][TEMP] typeMallSnListMap={1=[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], 2=[246, 247, 248, 244, 245, 242, 243, 249, 238, 250, 239, 240, 241, 203, 208, 216, 224, 232, 202, 207, 215, 223, 231, 201, 206, 214, 222, 230, 204, 209, 217, 225, 233, 205, 210, 218, 226, 234, 2001, 211, 219, 227, 235, 212, 220, 228, 236, 213, 221, 229, 237], 4=[407, 408], 100=[1000001, 1000002, 1000003, 1000004, 1000005, 1000006, 1000007, 1000008, 1000009, 1000010, 1000011, 1000012, 1000013, 1000014, 1000015, 1000016, 1000017, 1000018, 1000019, 1000020, 1000021, 1000022, 1000023, 1000024], 5=[41000, 37000, 33000, 29001, 25001, 501, 58000, 58030, 13001, 58060, 58081, 54000, 50000, 496601, 1001, 1101, 38000, 34001, 34011, 34022, 34032, 34043, 34068, 1301, 34093, 30000, 1401, 26000, 501301, 59000, 100001, 14001, 100021, 100040, 55000, 59100, 1801, 59150, 10001, 51000, 1901, 47000, 477102, 43000, 2101, 2201, 35000, 477401, 477420, 620801, 477460, 620820, 620861, 620860, 620880, 620900, 620920, 27001, 60000, 60050, 56000, 60100, 60150, 52000, 48000, 511001, 437301, 40000, 437320, 40021, 437340, 486601, 32000, 28000, 24000, 61000, 57000, 61100, 61121, 61141, 12001, 61161, 438001, 61181, 438018, 53000, 61201, 53020, 53040, 53061, 53081, 45000, 41001, 37001, 33001, 29002, 25002, 502, 58001, 58031, 13002, 58061, 58082, 54001, 50001, 496602, 1002, 1102, 38001, 34002, 34012, 34023, 34033, 34044, 34045, 1302, 34094, 34095, 30001, 34110, 1402, 26001, 501302, 59001, 100002, 14002, 100022, 100041, 55001, 59101, 1802, 59151, 10002, 51001, 1902, 47001, 477103, 2003, 43001, 2102, 2202, 35001, 477402, 477421, 477434, 620802, 477461, 620822, 620862, 620881, 620901, 620921, 27002, 60001, 60051, 56001, 60101, 60151, 52001, 48001, 511002, 437302, 40001, 437321, 40022, 437341, 486602, 32001, 28001, 24001, 61001, 57001, 61101, 61122, 61142, 12002, 61162, 438002, 61182, 438019, 53001, 61202, 53021, 53041, 53062, 53082, 45001, 41002, 37002, 37011, 33002, 29003, 25003, 503, 58002, 58032, 13003, 58062, 58083, 54002, 50002, 496603, 1003, 1103, 38002, 34003, 34013, 34024, 34034, 34046, 34047, 34069, 1303, 34096, 34097, 30002, 1403, 26002, 501303, 59002, 100003, 14003, 100023, 100042, 55002, 59102, 1803, 59152, 10003, 51002, 1903, 47002, 477104, 2004, 43002, 2103, 39011, 2203, 35002, 477403, 477422, 620803, 477462, 620821, 620835, 620834, 620837, 620836, 620838, 620863, 620882, 620902, 620922, 27003, 60002, 60052, 56002, 60102, 60152, 52002, 48002, 511003, 437303, 40002, 40011, 437322, 40023, 437342, 40032, 36011, 486603, 32002, 28002, 24002, 61002, 57002, 61102, 61123, 61143, 12003, 61163, 438003, 61183, 438020, 53002, 61203, 53022, 53042, 53063, 53083, 45002, 37003, 33003, 29004, 25004, 504, 58011, 58041, 13004, 58071, 58092, 54011, 50030, 46011, 496612, 1004, 42011, 1104, 38003, 34004, 34014, 34025, 34035, 34048, 34070, 1304, 34098, 1404, 26003, 501304, 59041, 100004, 14004, 100024, 100043, 55021, 55024, 59141, 1804, 10004, 59180, 51030, 1904, 47011, 477105, 2005, 2104, 2204, 35003, 477404, 477423, 620804, 477463, 620823, 31011, 620864, 620883, 620903, 620923, 27004, 60041, 60080, 60140, 60180, 52011, 56110, 44011, 511004, 437304, 40003, 437323, 40024, 437343, 486612, 32003, 28003, 24003, 61011, 61111, 57021, 61132, 61152, 12004, 438004, 61184, 438021, 53011, 53031, 61223, 53051, 53072, 53092, 49011, 41003, 37004, 33004, 29005, 25005, 505, 58003, 58033, 13005, 58063, 58084, 54012, 50003, 496604, 1005, 1105, 38004, 34005, 34015, 34026, 34036, 34049, 34071, 1305, 30003, 34099, 1405, 26004, 501305, 59003, 100005, 14005, 100025, 100044, 55003, 59103, 1805, 59153, 10005, 51003, 1905, 47003, 477106, 2006, 43003, 2105, 2205, 35004, 477407, 477406, 477405, 477415, 477414, 477413, 477412, 477411, 477410, 477409, 477408, 477424, 620805, 620824, 477464, 620865, 620885, 620884, 620904, 620924, 27005, 60003, 60053, 56003, 60103, 60153, 52003, 48003, 511005, 437310, 437311, 437308, 437309, 437306, 437307, 437305, 40004, 437314, 437315, 437312, 437313, 437324, 40025, 437344, 486604, 32004, 28004, 24004, 61003, 57003, 61103, 61124, 61144, 12005, 61164, 438005, 438022, 61185, 53003, 61204, 53023, 53043, 53064, 53084, 45003, 41004, 37005, 33005, 29006, 25006, 506, 58004, 58034, 13006, 58064, 58085, 54003, 50004, 496605, 1006, 1106, 38005, 34006, 34016, 34027, 34037, 34050, 34072, 1306, 30004, 34100, 1406, 26005, 501306, 59004, 100006, 14006, 100026, 100045, 55004, 59104, 1806, 59154, 10006, 59161, 51004, 1906, 47004, 477107, 2007, 43004, 2106, 2206, 35005, 477416, 477425, 620806, 620825, 477465, 620866, 620886, 620912, 27006, 620931, 60004, 60054, 56004, 60104, 60154, 60161, 52004, 48004, 511006, 437316, 40005, 437325, 40026, 437345, 486605, 32005, 28005, 24005, 61004, 57004, 61104, 61125, 61133, 61145, 61153, 12006, 61165, 438006, 61173, 438023, 61193, 53004, 61205, 61212, 53024, 53044, 53065, 53085, 45004, 41005, 37006, 33006, 29007, 25007, 507, 58005, 58035, 58065, 58093, 54004, 50005, 496606, 1007, 1107, 38006, 34007, 34017, 34028, 34038, 34051, 34073, 1307, 30005, 34101, 1407, 26006, 501307, 59005, 100007, 14007, 100027, 100046, 55005, 59105, 1807, 59155, 10007, 51005, 1907, 47005, 477101, 2008, 43005, 2107, 2207, 35006, 477417, 477426, 620807, 620826, 477466, 620867, 620887, 620905, 620925, 27007, 60005, 60055, 56005, 60111, 60155, 52005, 48005, 511007, 437317, 40006, 437326, 40027, 437346, 486606, 32006, 28006, 24006, 61005, 57005, 61105, 61126, 61146, 12007, 61166, 438007, 61186, 53005, 438024, 61206, 53025, 53045, 53073, 53093, 45005, 41006, 37007, 33007, 29008, 25008, 508, 58006, 58036, 58066, 58086, 54005, 50006, 496607, 1008, 1108, 38007, 34008, 34018, 34029, 34039, 34052, 34074, 1308, 30006, 34102, 1408, 26007, 501308, 59006, 100008, 14008, 100028, 100047, 55006, 59106, 1808, 59156, 10008, 51006, 1908, 47006, 2009, 43006, 2108, 2208, 35007, 477427, 620808, 620827, 477467, 620888, 620906, 620926, 27008, 60006, 60056, 56006, 60105, 60156, 52006, 48006, 511008, 40007, 437327, 40028, 437347, 486607, 32007, 28007, 24007, 61006, 57006, 61106, 61127, 61147, 12008, 61167, 438008, 61187, 53006, 438025, 61207, 53026, 53046, 53066, 53086, 45006, 41007, 37008, 33008, 29009, 25009, 58007, 58037, 58067, 58087, 54006, 50007, 496608, 1009, 38008, 34009, 34019, 34030, 34040, 34053, 34075, 1309, 30007, 34103, 1409, 26008, 501309, 59007, 100009, 100029, 100048, 55007, 59107, 1809, 59157, 51007, 1909, 47007, 2010, 43007, 35008, 477428, 620809, 620828, 477468, 620889, 620907, 620927, 27009, 60007, 60057, 56007, 60106, 60157, 52007, 48007, 511009, 40008, 437328, 40029, 437348, 486608, 32008, 28008, 24008, 61007, 57007, 61107, 61128, 61148, 61168, 438009, 61188, 438026, 53007, 61208, 53027, 53047, 53067, 53087, 45007, 41008, 37009, 33009, 29010, 25010, 58008, 58038, 58068, 58088, 54007, 50008, 496609, 38009, 34010, 34020, 34031, 34041, 34054, 34076, 30008, 34104, 26009, 501310, 59008, 100010, 100030, 100049, 55008, 59108, 59158, 51008, 47008, 2012, 43008, 35009, 477429, 620810, 620829, 477469, 620890, 620908, 27010, 620928, 60008, 60058, 56008, 60107, 60158, 52008, 48008, 511010, 40009, 437329, 40030, 437349, 486609, 32009, 28009, 24009, 61008, 57008, 61108, 61129, 61149, 61169, 438010, 61189, 438027, 53008, 61209, 53028, 53048, 53068, 53088, 45008, 41009, 37010, 33010, 58009, 58039, 58069, 58089, 54008, 50009, 496610, 38010, 34021, 34042, 34055, 34077, 30009, 34105, 26010, 501311, 59009, 100011, 100031, 100050, 55009, 59109, 59159, 51009, 47009, 2013, 43009, 35010, 477430, 620811, 620830, 477470, 620868, 620891, 620909, 620929, 60009, 60059, 56009, 60108, 60159, 52009, 48009, 511011, 40010, 437330, 40031, 437350, 486610, 32010, 28010, 24010, 61009, 57009, 61109, 61130, 61150, 61170, 438011, 61190, 438028, 53009, 61210, 53029, 53049, 53069, 53089, 45009, 41010, 58010, 58040, 58070, 58090, 54009, 50010, 496611, 34056, 34078, 30010, 501312, 59010, 100012, 100032, 100051, 55010, 59110, 59160, 51010, 47010, 2014, 43010, 39001, 477431, 620812, 620831, 477471, 620869, 620892, 620910, 620932, 60010, 60060, 56010, 60109, 60160, 52010, 48010, 511012, 437331, 437351, 36001, 486611, 61010, 57010, 61110, 61131, 61151, 61171, 438012, 61191, 438029, 53010, 61211, 53030, 53050, 53070, 53090, 45010, 58091, 54010, 50020, 50031, 46001, 46012, 42001, 42012, 34057, 34079, 501313, 59031, 59042, 100013, 100033, 100052, 55011, 55022, 59131, 59142, 59170, 59181, 51020, 51031, 2017, 39002, 39012, 477432, 620813, 31001, 31012, 477472, 620832, 620870, 620893, 620911, 620930, 60031, 60042, 60070, 60081, 60110, 60141, 60170, 60181, 56100, 56111, 44001, 44012, 511013, 437332, 437352, 36002, 36012, 57011, 57022, 61172, 438013, 61192, 438030, 61213, 61224, 53071, 53091, 49001, 49012, 50021, 46002, 42002, 34058, 34080, 501314, 59032, 100014, 100034, 100053, 55012, 59132, 59171, 51021, 2018, 39003, 39013, 477433, 620814, 31002, 477473, 620833, 620871, 620894, 60032, 60071, 60130, 60171, 56101, 44002, 511014, 437333, 437353, 36003, 36013, 57012, 438014, 438031, 61214, 49002, 50022, 50032, 46003, 46013, 42003, 42013, 34059, 59033, 59043, 100015, 100035, 100037, 100036, 100054, 55013, 55023, 59133, 59143, 59172, 59182, 51022, 51032, 2019, 39004, 620815, 31003, 31013, 477474, 620872, 620895, 60033, 60043, 60072, 60082, 60131, 60142, 60172, 60182, 56102, 56112, 44003, 44013, 437354, 36004, 57013, 57023, 438015, 438016, 438017, 438034, 438032, 438033, 61215, 61225, 49003, 49013, 50023, 46004, 42004, 34060, 59034, 100055, 55014, 59134, 59173, 51023, 39005, 620816, 31004, 477475, 620873, 620896, 60034, 60073, 60132, 60173, 60183, 56103, 44004, 437355, 36005, 57014, 61216, 49004, 50024, 46005, 42005, 34061, 59035, 100056, 55015, 59135, 59174, 51024, 39006, 620817, 31005, 477476, 620874, 60035, 60074, 60133, 60174, 56104, 44005, 437356, 36006, 57015, 61217, 49005, 50025, 46006, 42006, 34062, 59036, 100057, 55016, 59136, 59175, 59183, 51025, 39007, 31006, 60036, 60075, 60134, 60175, 56105, 44006, 36007, 57016, 61218, 61226, 49006, 50026, 46007, 42007, 34063, 59037, 55017, 59137, 59176, 51026, 39008, 31007, 60037, 60076, 60135, 60176, 56106, 44007, 36008, 57017, 61219, 49007, 50027, 46008, 42008, 34064, 34111, 59038, 55018, 59138, 59177, 51027, 39009, 31008, 60038, 60077, 60136, 60177, 56107, 44008, 36009, 57018, 61220, 49008, 50028, 46009, 42009, 34065, 34081, 59039, 55019, 59139, 59178, 51028, 39010, 31009, 60039, 60078, 60137, 60178, 56108, 44009, 36010, 57019, 61221, 49009, 50029, 46010, 42010, 34066, 34082, 59040, 55020, 59140, 59179, 51029, 31010, 60040, 60079, 60138, 60179, 56109, 44010, 57020, 61222, 49010, 34067, 34083, 60139, 34084, 34085, 34086, 34087, 34088, 34089, 34090, 34091, 34092], 6=[630, 631, 650, 651, 652, 653, 654, 655, 656, 657, 616, 615, 614, 613, 610, 604, 603, 602, 601], 7=[709, 710, 711, 701, 702, 703, 704, 705, 706, 707, 716, 708, 717, 712, 713, 714], 8=[801, 802, 803, 804, 805, 806, 807, 808, 809], 9=[901, 902, 903, 904], 10=[1211, 1212, 1213, 1214, 1215, 1216, 1217, 1209, 1210, 1229, 1222, 1228, 1208, 1207, 1206, 1221, 1205, 1204, 1203, 1202, 1201], 11=[1501, 1602, 1731, 1603, 1732, 1601, 1701, 1604, 1702, 1703, 1704, 1705, 1706, 1707, 1708, 1709, 1710, 1711, 1712, 1713, 1714, 1715, 1716, 1717, 1718, 1720, 1721, 1722, 1723, 1724], 12=[2301, 2302, 2303, 2304, 2305, 2306, 2307, 2308], 13=[11001, 11101, 11002, 11102, 11003, 11103, 11004, 11104, 11005, 11105, 11006, 11106, 11007, 11107, 11008, 11108, 11009, 11109, 11010, 11110, 11011, 11111, 11012, 11112, 11013, 11113, 11014, 11114, 11015, 11115, 11016, 11116, 11017, 11117, 11018, 11118, 11019, 11119, 11020, 11120], 14=[15001, 15002, 15003, 15004, 15005, 15006, 15007, 15008, 15009, 15010, 15011, 15012, 15013, 15014, 15015, 15016, 15017, 15018, 15019, 15020, 15021, 15022, 15023, 15024], 18=[180001, 180002, 180003, 180005, 180006, 180007, 180008, 180009, 180010, 180011, 180012, 180013], 19=[407001, 407020, 407040, 407061, 407081, 407002, 407021, 407041, 407062, 407082, 407003, 407022, 407042, 407063, 407083, 407004, 407023, 407043, 407064, 407084, 407005, 407024, 407044, 407065, 407006, 407025, 407045, 407066, 407085, 407007, 407026, 407046, 407067, 407074, 407086, 407008, 407027, 407047, 407068, 407087, 407094, 407009, 407028, 407048, 407069, 407088, 407010, 407029, 407049, 407070, 407089, 407011, 407030, 407050, 407071, 407090, 407012, 407031, 407051, 407072, 407091, 407013, 407032, 407052, 407073, 407092, 407014, 407033, 407053, 407093], 20=[200010, 200011, 200012, 200000, 200001, 200002, 200003, 200004, 200006, 200005, 200008, 200009, 200007], 22=[911005, 911004, 911007, 911006, 911001, 911003, 911002, 911021, 911020, 911023, 911022, 911017, 911016, 911019, 911018, 911013, 911012, 911015, 911014, 911009, 911008, 911011, 911010, 911029, 911028, 911031, 911030, 911025, 911024, 911027, 911026]}
2025-11-12 14:48:32,430 (GlobalConfVal.java:509) [INFO][TEMP] ===crossGroupSnInfoMap=[4, 6, 7, 8, 9, 10, 11, 12, 13, 14]
2025-11-12 14:48:32,433 (GlobalConfVal.java:2675) [INFO][TEMP] crossWarAttrBonusMap={1={0={1002=2000}, 1={1024=2000, 1001=2000}}, 3={0={1002=2000}, 2={1024=2000, 1001=2000}, 3={1024=2000, 1001=2000}}}
2025-11-12 14:48:32,434 (GlobalConfVal.java:2689) [INFO][TEMP] crossWarPlayerScoreList=[[1, 100, 100], [101, 105, 200], [106, 110, 300], [111, 115, 400], [116, 120, 500], [121, 125, 600], [126, 130, 700], [131, 135, 800], [136, 140, 900], [141, 150, 1000]]
2025-11-12 14:48:32,435 (GlobalConfVal.java:2690) [INFO][TEMP] crossWarMonsterScoreMap={1200002=75, 1200003=100, 1200001=50, 1200006=175, 1200007=200, 1200004=125, 1200005=150, 1200010=275, 1200011=300, 1200008=225, 1200009=250, 1200012=325}
2025-11-12 14:48:32,436 (GlobalConfVal.java:2691) [INFO][TEMP] crossWarSonOfLightScoreInfo=[10000, 1, 5]
2025-11-12 14:48:32,501 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=1, startTime=2025-07-27 23:50:01, endTime=2025-08-04 23:50:50, server_range=15,16,mainServerId=15 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-07-28 00:00:00|2025-08-03 23:59:59
2025-11-12 14:48:32,511 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=2, startTime=2025-08-22 23:50:01, endTime=2025-09-04 23:50:50, server_range=17,18,mainServerId=17 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-08-25 00:00:00|2025-08-31 23:59:59
2025-11-12 14:48:32,513 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=3, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=1-5,mainServerId=1 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,514 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=4, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=19-25,mainServerId=19 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,515 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=5, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=26-30,mainServerId=26 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,516 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=6, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=31-35,mainServerId=31 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,517 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=7, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=36-40,mainServerId=36 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,518 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=8, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=41-45,mainServerId=41 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,518 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=9, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=46-50,mainServerId=46 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,519 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=10, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=51-55,mainServerId=51 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,520 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=11, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=56-60,mainServerId=56 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,521 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=12, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=61-64,mainServerId=61 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,522 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=13, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=65-70,mainServerId=65 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,523 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=14, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=71-75,mainServerId=71 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,525 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=15, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=76-80,mainServerId=76 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,526 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=16, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=81-85,mainServerId=81 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,527 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=17, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=86-90,mainServerId=86 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,528 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=18, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=91-95,mainServerId=91 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,529 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=19, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=96-100,mainServerId=96 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,529 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=20, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=101-105,mainServerId=101 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,530 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=21, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=106-110,mainServerId=106 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,531 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=22, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=111-115,mainServerId=111 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,532 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=23, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=116-120,mainServerId=116 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,533 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=24, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=121-125,mainServerId=121 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,533 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=25, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=126-130,mainServerId=126 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,534 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=26, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=131-135,mainServerId=131 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,535 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=27, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=136-140,mainServerId=136 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,535 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=28, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=141-145,mainServerId=141 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,536 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=29, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=146-150,mainServerId=146 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,537 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=30, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=151-155,mainServerId=151 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,538 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=31, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=156-160,mainServerId=156 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,539 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=32, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=161-165,mainServerId=161 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,540 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=33, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=166-170,mainServerId=166 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,540 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=34, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=171-175,mainServerId=171 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,541 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=35, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=176-180,mainServerId=176 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,542 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=36, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=181-185,mainServerId=181 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,543 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=37, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=186-190,mainServerId=186 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,544 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=38, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=191-195,mainServerId=191 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,544 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=39, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=196-200,mainServerId=196 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,545 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=40, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=201-205,mainServerId=201 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,545 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=41, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=206-210,mainServerId=206 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,546 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=42, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=211-215,mainServerId=211 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,547 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=43, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=216-220,mainServerId=216 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,547 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=44, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=221-225,mainServerId=221 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,548 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=45, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=226-230,mainServerId=226 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,549 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=46, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=231-235,mainServerId=231 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,549 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=47, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=236-240,mainServerId=236 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,550 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=48, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=241-245,mainServerId=241 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,550 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=49, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=246-250,mainServerId=246 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,551 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=50, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=251-255,mainServerId=251 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,552 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=51, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=256-260,mainServerId=256 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,552 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=52, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=261-265,mainServerId=261 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,553 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=53, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=266-270,mainServerId=266 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,553 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=54, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=271-275,mainServerId=271 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,554 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=55, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=276-280,mainServerId=276 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,555 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=56, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=281-285,mainServerId=281 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,555 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=57, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=286-290,mainServerId=286 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,556 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=58, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=291-295,mainServerId=291 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,556 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=59, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=296-300,mainServerId=296 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,557 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=60, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=301-305,mainServerId=301 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,557 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=61, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=306-310,mainServerId=306 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,558 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=62, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=311-315,mainServerId=311 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,558 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=63, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=316-320,mainServerId=316 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,558 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=64, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=321-325,mainServerId=321 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,559 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=65, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=326-330,mainServerId=326 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,559 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=66, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=331-335,mainServerId=331 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,560 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=67, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=336-340,mainServerId=336 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,561 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=68, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=341-345,mainServerId=341 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,561 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=69, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=346-350,mainServerId=346 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,562 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=70, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=351-355,mainServerId=351 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,562 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=71, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=356-360,mainServerId=356 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,563 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=72, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=361-365,mainServerId=361 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,563 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=73, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=366-370,mainServerId=366 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,564 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=74, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=371-375,mainServerId=371 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,564 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=75, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=376-380,mainServerId=376 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,565 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=76, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=381-385,mainServerId=381 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,565 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=77, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=386-390,mainServerId=386 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,566 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=78, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=391-395,mainServerId=391 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,566 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=79, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=396-400,mainServerId=396 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,566 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=80, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=401-405,mainServerId=401 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,567 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=81, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=406-410,mainServerId=406 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,567 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=82, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=411-415,mainServerId=411 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,568 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=83, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=416-420,mainServerId=416 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,569 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=84, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=421-425,mainServerId=421 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,569 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=85, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=426-430,mainServerId=426 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,569 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=86, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=431-435,mainServerId=431 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,570 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=87, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=436-440,mainServerId=436 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,570 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=88, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=6-10,mainServerId=6 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:32,571 (ServerMerge.java:16) [INFO][TEMP] ===加载合服数据sn=89, startTime=2025-09-04 23:50:01, endTime=2025-09-25 23:50:50, server_range=11-14,mainServerId=11 act_merge_id=[7, 8, 9, 10, 11, 12, 13, 14], act_merge_time=2025-09-08 00:00:00|2025-09-14 23:59:59
2025-11-12 14:48:34,223 (RedisTools.java:62) [INFO][org.gof.core.dbsrv.redis.RedisTools] initRedisClient redis constr:redis://127.0.0.1:6379
2025-11-12 14:48:34,638 (RedisTools.java:62) [INFO][org.gof.core.dbsrv.redis.RedisTools] initRedisClient redis constr:redis://127.0.0.1:6379/1
2025-11-12 14:48:34,647 (RedisTools.java:62) [INFO][org.gof.core.dbsrv.redis.RedisTools] initRedisClient redis constr:redis://127.0.0.1:6379/2
2025-11-12 14:48:34,938 (RedisTools.java:70) [INFO][org.gof.core.dbsrv.redis.RedisTools] ###################### redis connection success
2025-11-12 14:48:34,994 (EntityManager.java:58) [INFO][org.gof.core.dbsrv.redis.EntityManager] EntityManager init 
2025-11-12 14:48:34,997 (RedisTools.java:70) [INFO][org.gof.core.dbsrv.redis.RedisTools] ###################### redis connection success
2025-11-12 14:48:34,998 (EntityManager.java:64) [INFO][org.gof.core.dbsrv.redis.EntityManager] EntityManager init admin redis 
2025-11-12 14:48:34,999 (RedisTools.java:70) [INFO][org.gof.core.dbsrv.redis.RedisTools] ###################### redis connection success
2025-11-12 14:48:35,000 (EntityManager.java:69) [INFO][org.gof.core.dbsrv.redis.EntityManager] EntityManager init admin cross redis 
2025-11-12 14:48:35,002 (WorldStartup.java:132) [INFO][GAME] 前置准备完成，开始启动Node, nodeId=world0, nodeAddr=tcp://127.0.0.1:20108
2025-11-12 14:48:35,028 (WorldStartup.java:135) [INFO][GAME] server init : begin start node...
2025-11-12 14:48:35,832 (StdSchedulerFactory.java:1208) [INFO][org.quartz.impl.StdSchedulerFactory] Using default implementation for ThreadExecutor
2025-11-12 14:48:35,840 (SimpleThreadPool.java:268) [INFO][org.quartz.simpl.SimpleThreadPool] Job execution threads will use class loader of thread: main
2025-11-12 14:48:35,886 (SchedulerSignalerImpl.java:61) [INFO][org.quartz.core.SchedulerSignalerImpl] Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-11-12 14:48:35,893 (RAMJobStore.java:155) [INFO][org.quartz.simpl.RAMJobStore] RAMJobStore initialized.
2025-11-12 14:48:35,896 (StdSchedulerFactory.java:1362) [INFO][org.quartz.impl.StdSchedulerFactory] Quartz scheduler 'DefaultQuartzScheduler' initialized from default resource file in Quartz package: 'quartz.properties'
2025-11-12 14:48:35,896 (StdSchedulerFactory.java:1366) [INFO][org.quartz.impl.StdSchedulerFactory] Quartz scheduler version: 2.4.0-SNAPSHOT
2025-11-12 14:48:36,163 (WorldStartup.java:151) [INFO][GAME] server init : begin start platform...
2025-11-12 14:48:37,088 (WorldStartup.java:163) [INFO][GAME] server init : default port started...
2025-11-12 14:48:37,190 (HttpServer.java:46) [INFO][PLATFORM] PlatformHTTP服务启动成功 端口:8018
2025-11-12 14:48:37,195 (ServerList.java:63) [ERROR][TEMP] 模式 true true false
2025-11-12 14:48:37,207 (GameService.java:99) [INFO][TEMP] ===初始化游戏服务 gameValue
2025-11-12 14:48:37,322 (GameService.java:343) [INFO][GAME] 服务器列表serverIdListNow=[40001], serverList=[40001]
2025-11-12 14:48:37,420 (GameService.java:129) [INFO][GAME] [新闻播报]起服查询数据, serverId=40001, 数据量=31
2025-11-12 14:48:37,428 (GameService.java:104) [INFO][TEMP] ===删除玩家id数量总，0
2025-11-12 14:48:37,564 (ServerList.java:236) [INFO][TEMP] 获取服务器列表成功, servers=474 maxServerId=40002 isServerMerge=false
2025-11-12 14:48:37,565 (WorldStartup.java:199) [INFO][GAME] server nodeID=world0, defaultNodeId=world0
2025-11-12 14:48:37,632 (WorldStartup.java:221) [INFO][TEMP] ===服务器id=40001, 是否合服=false mergeId=40001
2025-11-12 14:48:37,633 (GameServiceManager.java:32) [INFO][GAME] 开始初始化service
2025-11-12 14:48:40,633 (PackageClass.java:228) [INFO][TEMP] ===findByFile=H:\Projects\sword\dev\server\lzWorldSrv\bin\org\gof\demo\worldsrv\msg continue!!!
2025-11-12 14:48:41,526 (PackageClass.java:228) [INFO][TEMP] ===findByFile=H:\Projects\sword\dev\server\lzWorldSrv\bin\org\gof\demo\worldsrv\msg continue!!!
2025-11-12 14:48:41,713 (PackageClass.java:228) [INFO][TEMP] ===findByFile=H:\Projects\sword\dev\server\lzWorldSrv\bin\org\gof\demo\worldsrv\msg continue!!!
2025-11-12 14:48:41,926 (PackageClass.java:228) [INFO][TEMP] ===findByFile=H:\Projects\sword\dev\server\lzWorldSrv\bin\org\gof\demo\worldsrv\msg continue!!!
2025-11-12 14:48:42,099 (PackageClass.java:228) [INFO][TEMP] ===findByFile=H:\Projects\sword\dev\server\lzWorldSrv\bin\org\gof\demo\worldsrv\msg continue!!!
2025-11-12 14:48:42,227 (ActivityControlService.java:142) [INFO][CROSS_WAR] init m_ttCrossWarReward={"running":true,"interval":604800000,"startTime":"2025-11-16 23:15:00","nextTime":"2025-11-16 23:15:00"}
2025-11-12 14:48:42,263 (ActivityControlLuckyLottery.java:47) [INFO][ACTIVITY] [福签抽奖]活动起服加载数据
2025-11-12 14:48:42,265 (ActivityControlBoss.java:70) [ERROR][ACTIVITY] 全民赶年兽全服活动数据开始重新加载, 活动类型=6202, round=62020001, json={}
2025-11-12 14:48:42,267 (ActivityServerData.java:222) [INFO][TEMP] ===检查活动循环初始化：serverId=40001 , roundSnList=[100001, 120001, 80001, 200001, 140001, 110001, 130001, 90001, 50001, 70002, 170001, 330001]
2025-11-12 14:48:42,268 (ActivityServerData.java:239) [INFO][TEMP] ===检查活动循环初始化：serverId=40001 , roundSnList=[100001, 120001, 80001, 200001, 140001, 110001, 130001, 90001, 50001, 70002, 170001, 330001]
2025-11-12 14:48:42,281 (ActivityControlService.java:672) [ERROR][ACTIVITY] [合服团购]起服不加载，活动已结束，actType=1206, round=12060001
2025-11-12 14:48:42,321 (PackageClass.java:228) [INFO][TEMP] ===findByFile=H:\Projects\sword\dev\server\lzWorldSrv\bin\org\gof\demo\worldsrv\msg continue!!!
2025-11-12 14:48:42,373 (ArenaRankedService.java:82) [INFO][TEMP] ===初始化跨服排位赛服务, serverId=40001
2025-11-12 14:48:42,399 (ArenaRankedService.java:1180) [ERROR][TEMP] ===arenaBridgeRankRoomKey:0数据为空。无需结算排位赛
2025-11-12 14:48:42,485 (CarParkService.java:93) [ERROR][CAR_PARK] 开始加载公共停车场数据：4000101,4000102,4000103,4000104,4000105
2025-11-12 14:48:42,526 (PackageClass.java:228) [INFO][TEMP] ===findByFile=H:\Projects\sword\dev\server\lzWorldSrv\bin\org\gof\demo\worldsrv\msg continue!!!
2025-11-12 14:48:42,605 (CarParkService.java:104) [ERROR][CAR_PARK] 加载公共停车场数据成功返回数量:5
2025-11-12 14:48:42,607 (CarParkService.java:115) [ERROR][CAR_PARK] 成功加载公共停车场数据: 4000101
2025-11-12 14:48:42,607 (CarParkService.java:115) [ERROR][CAR_PARK] 成功加载公共停车场数据: 4000102
2025-11-12 14:48:42,608 (CarParkService.java:115) [ERROR][CAR_PARK] 成功加载公共停车场数据: 4000103
2025-11-12 14:48:42,608 (CarParkService.java:115) [ERROR][CAR_PARK] 成功加载公共停车场数据: 4000104
2025-11-12 14:48:42,608 (CarParkService.java:115) [ERROR][CAR_PARK] 成功加载公共停车场数据: 4000105
2025-11-12 14:48:42,697 (PackageClass.java:228) [INFO][TEMP] ===findByFile=H:\Projects\sword\dev\server\lzWorldSrv\bin\org\gof\demo\worldsrv\msg continue!!!
2025-11-12 14:48:42,768 (GuildLeagueWarmUpService.java:863) [INFO][TEMP] ===serverId:40001, 乱斗是否跨服=true
2025-11-12 14:48:42,770 (CheckWorldService.java:101) [INFO][TEMP] ===代码未实现，检测的先不处理
2025-11-12 14:48:42,774 (ArenaService.java:92) [INFO][TEMP] ===初始化本服竞技场服务, 1737300900000
2025-11-12 14:48:42,775 (ArenaService.java:99) [INFO][TEMP] 本服竞技场赛季已经结束，不初始化， confSeason=3, timeNow=1762930122764, serverId 1 -> 60 timeNext=1736696100000, serverNoId=1
2025-11-12 14:48:42,871 (PackageClass.java:228) [INFO][TEMP] ===findByFile=H:\Projects\sword\dev\server\lzWorldSrv\bin\org\gof\demo\worldsrv\msg continue!!!
2025-11-12 14:48:42,945 (GuildService.java:178) [INFO][TEMP] =================init GuildService
2025-11-12 14:48:42,946 (GuildService.java:268) [INFO][TEMP] ===gve开始时间=1762946700000，2025-11-12 19:25:00
2025-11-12 14:48:42,947 (GuildService.java:273) [INFO][TEMP] ===gve结束时间=1762947300000，2025-11-12 19:35:00
2025-11-12 14:48:43,036 (WorldStartup.java:239) [INFO][GAME] server init : begin connect admin...
2025-11-12 14:48:43,041 (WorldStartup.java:244) [INFO][TEMP] ===连接中心管理服， nid=admin0, localBridgeId=world0， org.gof.core.RemoteNode@5cf072ea[remoteId=admin0,nodeType=ADMIN,remoteAddr=tcp://127.0.0.1:13000,localAlias=world0_40001,connected=false,main=true,createTime=2025-11-12 14:48:43,rogerTime=2025-11-12 14:48:43]
2025-11-12 14:48:43,042 (WorldStartup.java:251) [INFO][GAME] ====================
2025-11-12 14:48:43,042 (WorldStartup.java:252) [INFO][GAME] world0 started.
2025-11-12 14:48:43,043 (WorldStartup.java:253) [INFO][GAME] Listen:tcp://127.0.0.1:20108
2025-11-12 14:48:43,043 (WorldStartup.java:254) [INFO][GAME] ====================
2025-11-12 14:48:43,052 (HumanManager.java:5358) [INFO][TEMP] ===去后台请求屏蔽字库
2025-11-12 14:48:43,053 (HumanManager.java:5399) [INFO][TEMP] ===去后台请求跑马灯
2025-11-12 14:48:43,164 (WorldStartup.java:298) [INFO][GAME] 开启数据热更新扫描...
2025-11-12 14:48:43,232 (ClassScanProcess.java:58) [INFO][GAME] 开启类热更新扫描调度，每5分钟执行一次
2025-11-12 14:48:43,233 (WorldStartup.java:302) [INFO][GAME] 开启类热更新扫描...
2025-11-12 14:48:43,241 (WorldStartup.java:314) [ERROR][GAME] 开启连接服务
2025-11-12 14:48:43,242 (WorldStartup.java:316) [ERROR][GAME] 启动完成...
2025-11-12 14:48:43,250 (ServCheck.java:68) [ERROR][GAME] 
╔═══════════════════╤══════════════╗
║ service           │ costTime(ms) ║
╠═══════════════════╪══════════════╣
║ flyHybrid         │ 107          ║
╟───────────────────┼──────────────╢
║ worldBoss         │ 102          ║
╟───────────────────┼──────────────╢
║ guild             │ 75           ║
╟───────────────────┼──────────────╢
║ activity          │ 67           ║
╟───────────────────┼──────────────╢
║ name              │ 31           ║
╟───────────────────┼──────────────╢
║ carPark           │ 7            ║
╟───────────────────┼──────────────╢
║ arena             │ 3            ║
╟───────────────────┼──────────────╢
║ arenaRanked       │ 2            ║
╟───────────────────┼──────────────╢
║ farm              │ 1            ║
╟───────────────────┼──────────────╢
║                   │ 0            ║
╟───────────────────┼──────────────╢
║ checkWorld        │ 0            ║
╟───────────────────┼──────────────╢
║ mail              │ 0            ║
╟───────────────────┼──────────────╢
║ httpPush          │ 0            ║
╟───────────────────┼──────────────╢
║ pocketLine        │ 0            ║
╟───────────────────┼──────────────╢
║ serverSelect      │ 0            ║
╟───────────────────┼──────────────╢
║ humanGlobal       │ 0            ║
╟───────────────────┼──────────────╢
║ rank              │ 0            ║
╟───────────────────┼──────────────╢
║ dataReset         │ 0            ║
╟───────────────────┼──────────────╢
║ fillMail          │ 0            ║
╟───────────────────┼──────────────╢
║ gm                │ 0            ║
╟───────────────────┼──────────────╢
║ team              │ 0            ║
╟───────────────────┼──────────────╢
║ humanCreateApply  │ 0            ║
╟───────────────────┼──────────────╢
║ confirm           │ 0            ║
╟───────────────────┼──────────────╢
║ chat              │ 0            ║
╟───────────────────┼──────────────╢
║ guildLeagueWarmUp │ 0            ║
╚═══════════════════╧══════════════╝

2025-11-12 14:48:44,916 (GuildService.java:4579) [INFO][TEMP] 同步完成
2025-11-12 14:48:46,317 (WorldStartup.java:259) [INFO][GAME] 触发关闭服务器操作,开始踢人
2025-11-12 14:48:46,335 (HumanGlobalService.java:642) [INFO][GAME] ===已踢出所有玩家
2025-11-12 14:48:46,365 (CarParkService.java:199) [ERROR][GAME] 保存停车场数据完成
2025-11-12 14:48:53,236 (ClassScanProcess.java:135) [INFO][GAME] 开始一次扫描key=363957321744344066869,jarValue=363957321744344066869
2025-11-12 14:49:05,388 (WorldStartup.java:285) [INFO][GAME] 关闭服务器-检查db更新队列完成! 15s后服务器将关闭
2025-11-12 14:49:12,393 (CrossManager.java:104) [ERROR][GAME] 获取跨服分组超时！crossType=cross_activity,serverId=40001
2025-11-12 14:49:12,394 (ActivityControlService.java:1026) [ERROR][ACTIVITY] [福签抽奖]查询跨服节点报错, serverId=40001
io.vertx.core.impl.NoStackTraceThrowable: 获取跨服分组超时！
